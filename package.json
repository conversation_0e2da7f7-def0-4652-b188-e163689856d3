{"name": "xtaro-car-main", "version": "0.0.1", "description": "XTaro template demo", "scripts": {"//mini": "---------------------- 小程序相关 ----------------------", "build:mini": "xtaro build --platform mini --miniType weapp", "build:mini:accounts": "xtaro build --platform mini --miniType weapp --accounts", "dev:mini": "xtaro build --platform mini --miniType weapp --watch", "dev:alipay5288": "xtaro build --platform mini --miniType alipay --appId 5288 --watch", "build:alipay5288": "xtaro build --platform mini --miniType alipay --appId 5288", "build:weapp5227": "xtaro build --platform mini --miniType weapp --appId 5227", "dev:weapp5227": "xtaro build --platform mini --miniType weapp --appId 5227 --watch", "build:weapp5240": "xtaro build --platform mini --miniType weapp --appId 5240", "dev:weapp5240": "xtaro build --platform mini --miniType weapp --appId 5240 --watch", "//h5": "---------------------- h5相关 ----------------------", "build:h5": "xtaro build --platform h5", "build:trip-h5": "xtaro build --platform h5 --appId ********** -T", "dev:h5": "xtaro build --platform h5 --watch", "dev:trip-h5": "xtaro build --platform h5 --watch --appId ********** -T", "//crn": "---------------------- crn相关 ----------------------", "build:crn": "xtaro build --platform crn", "build:crn:harmony": "xtaro build --platform crn --rnversion=harmony", "install:ctrip": "npm install --registry=--registry=http://artifactory.release.ctripcorp.com/artifactory/api/npm/trip-npm-prod/ --legacy-peer-deps", "clean": "rm -rf package-lock.json && rm -rf node_modules && rm -rf dist && npm run install:ctrip", "dev:crn": "concurrently \"xtaro build --platform crn --watch\" \"tsc -w\"", "dev:crn:harmony": " xtaro build --platform crn --watch --rnversion=harmony", "start:crn:harmony": "xtaro build --platform crn --watch --blended --rnversion=harmony", "start:crn": "xtaro build --platform crn --watch --blended", "dev:crn37": "xtaro build --platform crn --appId 37 --watch", "lint-all": "eslint . --ignore-path .eslint<PERSON>ore --fix", "lint": "eslint --ignore-path .eslint<PERSON>ore --fix", "lint-trip": "vlint --ignore-path .eslint<PERSON>ore --fix", "test:all": "jest --silent --detectOpenHandles --runInBand --forceExit --json --outputFile='./coverage/case-result.json'", "test:ut": "jest --coverage --config jest.config.ut.js --detectOpenHandles  --forceExit", "test:new": "jest --coverage --changedSince=master-064 --coverageThreshold='{\"global\":{\"branches\":\"50\"}}' --detectOpenHandles  --forceExit", "test:watch": "jest --watch --debug --verbose --coverage=false", "test:fix": "npm run test -- -u", "test": "jest --coverage --detectOpenHandles --runInBand --forceExit", "build:crn37": "xtaro build --platform crn --appId 37", "removeAB": "node ./scripts/removeAB.js", "removeUL": "node ./scripts/removeUseLess.js", "build:version": "sh ./scripts/buildVersion.sh", "lint-staged": "lint-staged"}, "repository": {"type": "git", "url": "****************************:xtaro/xtaro-template.git"}, "lint-staged": {"src/**/*.{js,ts,tsx}": ["npm run lint-trip", "npx prettier --write"]}, "keywords": ["XTaro", "xtaro", "project", "xtaro-project", "xtaro-car-main"], "type": "commonjs", "author": "carFE", "license": "ISC", "dependencies": {"@babel/cli": "7.16.0", "@babel/core": "7.16.5", "@babel/preset-env": "7.16.5", "@babel/register": "7.16.5", "@babel/runtime": "7.16.5", "@ctrip/devhub-trace-log": "^0.2.3", "@ctrip/graphql-client": "^2.1.2", "@ctrip/xtaro": "2.1.0", "@ctrip/xtaro-component-calendar": "0.2.10", "@ctrip/xtaro-crn": "2.0.8-alpha.21", "@ctrip/xtaro-mock": "0.0.1-alpha.62", "@ctrip/xtaro-style": "2.0.12", "@ctrip/xtaro-types": "2.1.0", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^29.2.1", "@types/node": "13.13.12", "@types/node-fetch": "^2.6.2", "@types/promise-timeout": "^1.3.0", "@types/react": "16.9.36", "@types/react-native": "^0.60.31", "@types/react-test-renderer": "^16.9.2", "@types/uuid": "^3.4.9", "@types/yallist": "^3.0.1", "babel-cli": "6.26.0", "babel-plugin-transform-runtime": "6.23.0", "babel-preset-es2015": "6.24.1", "babel-preset-stage-2": "6.24.1", "bpk-tokens": "^29.6.1", "deprecated-react-native-prop-types": "^2.3.0", "esbuild-jest": "^0.5.0", "execa": "^5.1.1", "fs-extra": "^11.2.0", "immer": "9.0.6", "is-promise": "^4.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^7.0.0", "jest-react-native": "^18.0.0", "memoize-one": "^5.1.1", "node-fetch": "^2.6.9", "promise-timeout": "^1.3.0", "qs": "6.9.6", "react": "18.2.0", "react-addons-test-utils": "^15.6.2", "react-dom": "^18.3.1", "react-redux": "8.0.4", "react-test-renderer": "18.2.0", "react-timer-mixin": "0.13.4", "redux": "4.2.0", "redux-saga": "1.1.3", "redux-saga-test-plan": "^4.0.5", "reselect": "4.0.0", "theming": "3.3.0", "tsc-alias": "^1.7.1", "typescript": "5.4.3", "uuid": "^3.4.0", "yallist": "^4.0.0"}, "devDependencies": {"@commitlint/cli": "^8.3.6", "concurrently": "^7.5.0", "@commitlint/config-conventional": "^8.3.6", "@ctrip/eslint-plugin-car-linter": "1.2.0-beta", "@ctrip/node-vampire-lint": "2.0.27", "@ctrip/trip-common-eslint-rules": "^1.0.6", "@ctrip/xtaro-cli": "2.1.0", "@ctrip/bbk-trace": "1.1.3", "@testing-library/react": "^16.2.0", "@types/node-sass": "^4.11.8", "esbuild": "^0.18.16", "esbuild-jest": "^0.5.0", "eslint": "^8.57.0", "jest": "^29.2.1", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^7.0.0", "jest-preview": "^0.3.1", "ts-jest": "^24.3.0", "eslint-plugin-unused-imports": "4.1.4", "husky": "^4.2.5", "lint-staged": "^8.1.0", "prettier": "^3.3.3"}, "xtaro-crn-dist": "./dist/crn/xtaro-car-main", "xtaro-crn": true, "overrides": {"@jridgewell/gen-mapping": "0.3.5"}}