{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "4ee4da92-02d8-4cae-a32f-cebf7a6289d7", "cost": 1088}, "vehicleInfo": {"brandEName": "奥迪", "brandName": "奥迪", "name": "奥迪A4L", "zhName": "奥迪A4L", "vehicleCode": "33", "groupCode": "5", "groupSubClassCode": "", "groupName": "豪华轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "2.0T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置前驱/前置四驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6m12000c54zimgB978.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5t12000cojpolvE318.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4y12000cojpom99607.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2x12000cojplj0659A.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5212000cojpkreBAB2.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3y12000cojpymd2EB9.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6312000d5pb07yA868.png", "oilType": 3, "fuelType": "汽油", "luggageNum": "可放5个24寸行李箱", "guidSys": "部分车辆支持定速巡航/自适应巡航", "carPlay": "部分车辆支持CarPlay/AndroidAuto/CarLife", "chargeInterface": "部分车辆支持USB/AUX/SD/Type-C", "skylight": "支持", "charge": "", "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1024&app_ver=10.5", "vehiclesSetId": "97", "multimediaAlbums": [{"albumName": "官方相册", "note": "年款/颜色等以门店为准", "albumType": 1, "mediaGroup": [{"groupType": 2, "groupName": "视频", "groupSortNum": 2, "medias": [{"type": 2, "url": "https://video-preview.ctrip.com/videos/RV0icn162n5x7mgbk516D.mp4?auth=Nephele%20pz1vdj836nilu8gbdq16l18733mvnn1c%2F202304171712%2Fcar_standard_product%2FdG9rZW46YmQyOTAxNjJmZDI2MTIzMDdkMmEzMzA3N2ZiNTNhZDk2NjcwNmU1YTU0OTMxYmJiYjc3NGE5MjQ%3D&mark=yiche", "cover": "https://dimg04.c-ctrip.com/images/0RV6312000d5pb07yA868.png", "sortNum": 0}]}, {"groupType": 3, "groupName": "VR", "groupSortNum": 3, "medias": [{"type": 3, "url": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1024&app_ver=10.5", "cover": "https://dimg04.c-ctrip.com/images/0RV6m12000c54zimgB978.png?mark=yiche", "sortNum": 0}]}, {"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4l12000cojpz6h8F85.jpg?mark=yiche", "cover": "", "sortNum": 1}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6y12000cojpki9263C.jpg?mark=yiche", "cover": "", "sortNum": 2}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4d12000cojpxeh45EC.jpg?mark=yiche", "cover": "", "sortNum": 3}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5p12000cojpym0F953.jpg?mark=yiche", "cover": "", "sortNum": 4}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2z12000cojpjofC8C4.jpg?mark=yiche", "cover": "", "sortNum": 5}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5t12000cojpolvE318.jpg?mark=yiche", "cover": "", "sortNum": 1}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2x12000cojplj0659A.jpg?mark=yiche", "cover": "", "sortNum": 2}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3y12000cojpymd2EB9.jpg?mark=yiche", "cover": "", "sortNum": 3}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5o12000cojpjp0AA77.jpg?mark=yiche", "cover": "", "sortNum": 4}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0u12000cojpmbs4F8F.jpg?mark=yiche", "cover": "", "sortNum": 5}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4y12000cojpom99607.jpg?mark=yiche", "cover": "", "sortNum": 1}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5212000cojpkreBAB2.jpg?mark=yiche", "cover": "", "sortNum": 2}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2n12000cojpxf64F96.jpg?mark=yiche", "cover": "", "sortNum": 3}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6e12000cojpz73EFA4.jpg?mark=yiche", "cover": "", "sortNum": 4}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6q12000cojq7u05DED.jpg?mark=yiche", "cover": "", "sortNum": 5}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}]}], "mediaTypes": [3, 2], "vehicleKey": "0_33_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "snowTyre": {"type": 0, "typeDesc": "不支持"}, "reverseImage": {"type": 1, "typeDesc": "支持"}, "reverseSensor": {"type": 1, "typeDesc": "支持"}, "tachograph": {"type": 2, "typeDesc": "部分车辆支持"}, "shortEndurance": "km"}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "uniqSign": "320011148103061887983w31tY5X9708VM5rl91e", "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "isFromSearch": true, "promptInfos": [], "promotMap": {}, "extras": {"goodsShelvesTwoABVersion": "C", "goodsShelves2EhaiLogic": "1", "packageLevelAB": "B", "selfServiceSwitch": "1", "isLicensePlateHideShow": "0", "packageLevelSwitch": "1", "commodityClass2Version": "1", "goodsShelvesTwoSwitch": "1", "abVersion": "241008_DSJT_ykjpx|B,230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,220624_DSJT_spfj1|B,250325_DSJT_huojia20|C", "isNewLicensePlate": "0", "serverRequestId": "W25273qM2K11f8343032", "prepProductGroupTopSwitch": "0"}, "imStatus": 0, "recommendProducts": [{"vehicleCode": "166", "sortNum": 2, "lowestPrice": 0, "highestPrice": 1169, "maximumRating": 5, "maximumCommentCount": 19288, "lowestDistance": 0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD5131", "vendorCode": "83646", "pStoreCode": "115460", "rStoreCode": "115460", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "comPriceCode": "[c]MTYzMjMxNTI4NDAxNDQ3MzAwfDIxfDAuMDUtMjAyNS06MDA6NiAwMDQzJiYwMCYxbHNlJjEmZmEkfDEwMTQzJiYxNDMwMSYxJDEwMCYxNDMyMC4wMyYxJi4wMCQwJjIwJjEmNDEwMDImNDAuMC4wMDIwMjUwMCR8MjYgMS0wNS06MDAmODozMC0wNS0yMDI1ODozMDI3IDEyMDI1OjAwfDIyIDEtMDUtOjA0ADM6NTc=", "priceVersion": "SH-PRICEVERSION_MTE1NDYwXzE2Nl8xXzE0M18xNDNfMTQzXzIwMy4wMF8wXzYwLjBfMF8wXzAuMF8wLjBfNDAuMDBfMjAuMDBfMC4wMF8wLjAwXzgxMjIzNzU5", "vendorVehicleCode": "136664_56613_pupai", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 81223759, "pLev": 1989079, "rLev": 1989079, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "11037", "name": "优惠券", "amount": 128, "amountDesc": "¥128"}, {"code": "4530", "name": "火车票用户专享", "amount": 15, "amountDesc": "¥15"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 60, "amountStr": "¥60", "subAmount": 203, "subAmountStr": "¥203", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3917", "vehicleCode": "0", "packageType": 1, "skuId": 48113129}}, {"reference": {"bizVendorCode": "SD8450", "vehicleCode": "0", "packageType": 1, "skuId": 57940450}}, {"reference": {"bizVendorCode": "SD3008", "vehicleCode": "0", "packageType": 1, "skuId": 229405}}, {"reference": {"bizVendorCode": "SD4484", "vehicleCode": "0", "packageType": 1, "skuId": 29459186}}, {"reference": {"bizVendorCode": "SD4825", "vehicleCode": "0", "packageType": 1, "skuId": 1917488}}, {"reference": {"bizVendorCode": "SD3980", "vehicleCode": "0", "packageType": 1, "skuId": 1855175}}, {"reference": {"bizVendorCode": "SD15565", "vehicleCode": "0", "packageType": 1, "skuId": 79202236}}, {"reference": {"bizVendorCode": "SD6823", "vehicleCode": "0", "packageType": 1, "skuId": 78536711}}, {"reference": {"bizVendorCode": "SD4293", "vehicleCode": "0", "packageType": 1, "skuId": 30538817}}, {"reference": {"bizVendorCode": "SD7460", "vehicleCode": "0", "packageType": 1, "skuId": 75412058}}, {"reference": {"bizVendorCode": "SD15416", "vehicleCode": "0", "packageType": 1, "skuId": 86213681}}, {"reference": {"bizVendorCode": "SD3494", "vehicleCode": "0", "packageType": 1, "skuId": 3018335}}, {"reference": {"bizVendorCode": "SD6915", "vehicleCode": "0", "packageType": 0, "skuId": 55247459}}, {"reference": {"bizVendorCode": "SD7587", "vehicleCode": "0", "packageType": 0, "skuId": 77468987}}, {"reference": {"bizVendorCode": "SD8059", "vehicleCode": "0", "packageType": 1, "skuId": 4456241}}, {"reference": {"bizVendorCode": "SD5301", "vehicleCode": "0", "packageType": 1, "skuId": 1924454}}, {"reference": {"bizVendorCode": "SD5302", "vehicleCode": "0", "packageType": 0, "skuId": 1908158}}, {"reference": {"bizVendorCode": "SD5953", "vehicleCode": "0", "packageType": 1, "skuId": 1966073}}, {"reference": {"bizVendorCode": "SD4178", "vehicleCode": "0", "packageType": 1, "skuId": 4014454}}, {"reference": {"bizVendorCode": "SD14790", "vehicleCode": "0", "packageType": 1, "skuId": 62965659}}, {"reference": {"bizVendorCode": "SD5299", "vehicleCode": "0", "packageType": 0, "skuId": 84842927}}, {"reference": {"bizVendorCode": "SD5535", "vehicleCode": "0", "packageType": 1, "skuId": 1968509}}, {"reference": {"bizVendorCode": "SD3237", "vehicleCode": "0", "packageType": 1, "skuId": 2710305}}, {"reference": {"bizVendorCode": "SD3191", "vehicleCode": "0", "packageType": 1, "skuId": 4914919}}, {"reference": {"bizVendorCode": "SD6305", "vehicleCode": "0", "packageType": 1, "skuId": 59116989}}, {"reference": {"bizVendorCode": "SD3226", "vehicleCode": "0", "packageType": 1, "skuId": 1816926}}, {"reference": {"bizVendorCode": "SD6300", "vehicleCode": "0", "packageType": 1, "skuId": 78234346}}, {"reference": {"bizVendorCode": "SD6992", "vehicleCode": "0", "packageType": 1, "skuId": 2428627}}, {"reference": {"bizVendorCode": "SD6298", "vehicleCode": "0", "packageType": 1, "skuId": 23778168}}, {"reference": {"bizVendorCode": "SD14981", "vehicleCode": "0", "packageType": 1, "skuId": 1912557}}], "reactId": "1357053750", "group": 0, "groupSort": 0, "hot": 0, "hotType": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD5131_0_115460_115460"]}, "minTPrice": 60, "minDPrice": 0, "modifySameVehicle": false, "minDOrinPrice": 143, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减143", "groupId": 1, "mergeId": 0}, "priceSize": 31, "isEasy": true, "isCredit": true, "pWay": "可选：免费送车上门", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_166_"}, {"vehicleCode": "2385", "sortNum": 82, "lowestPrice": 0, "highestPrice": 0, "maximumRating": 4.9, "maximumCommentCount": 11755, "lowestDistance": 0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3917", "vendorCode": "33731", "pStoreCode": "107597", "rStoreCode": "107597", "vehicleCode": "0", "packageId": "", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3NTk3XzIzODVfMV8xNDNfMTQzXzE0M18yMDMuMDBfMF82MC4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzAuMDBfMC4wMF8zMzY1OTAx", "vendorVehicleCode": "3365901", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 3365901, "pLev": 23458, "rLev": 23458, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "11037", "name": "优惠券", "amount": 128, "amountDesc": "¥128"}, {"code": "4530", "name": "火车票用户专享", "amount": 15, "amountDesc": "¥15"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 60, "amountStr": "¥60", "subAmount": 203, "subAmountStr": "¥203", "currencyCode": "¥"}]}], "reactId": "1357053753", "group": 0, "groupSort": 0, "hot": 0, "hotType": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3917_0_107597_107597"]}, "minTPrice": 60, "minDPrice": 0, "modifySameVehicle": false, "minDOrinPrice": 143, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减143", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isEasy": true, "isCredit": true, "pWay": "可选：免费送车上门", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_2385_"}, {"vehicleCode": "452", "sortNum": 109, "lowestPrice": 0, "highestPrice": 1938, "maximumRating": 5, "maximumCommentCount": 3880, "lowestDistance": 0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD8450", "vendorCode": "85787", "pStoreCode": "133801", "rStoreCode": "133801", "vehicleCode": "0", "packageType": 0, "comPriceCode": "[c]MzkzNjMzMjE1NjM1OTE1MDAyNS01fHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE3OCYmMTc4JmxzZSYwMSYxJHwxMCYxNzgmMTc4MyYxJiQxMDAwJjMwMzAuMDEwMDIuMDAkMC4wMCYxJjQwMCR8JjQwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTMzODAxXzQ3MzRfMV8xNzhfMTc4XzE3OF8yNDguMDBfMF83MC4wXzBfMF8wLjBfMC4wXzQwLjAwXzMwLjAwXzAuMDBfMC4wMF84ODU1OTIxMw==", "vendorVehicleCode": "88559213", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 88559213, "pLev": 1506366, "rLev": 1506366, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 178, "detail": [{"code": "1001", "name": "租车费", "amount": 178, "amountDesc": "¥178"}, {"code": "11037", "name": "优惠券", "amount": 160, "amountDesc": "¥160"}, {"code": "4530", "name": "火车票用户专享", "amount": 18, "amountDesc": "¥18"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "name": "车行手续费", "amount": 30, "amountDesc": "¥30", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 70, "amountStr": "¥70", "subAmount": 248, "subAmountStr": "¥248", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD7314", "vehicleCode": "0", "packageType": 0, "skuId": 3742373}}, {"reference": {"bizVendorCode": "SD7460", "vehicleCode": "0", "packageType": 1, "skuId": 68999813}}, {"reference": {"bizVendorCode": "SD9254", "vehicleCode": "0", "packageType": 0, "skuId": 73253741}}, {"reference": {"bizVendorCode": "SD4293", "vehicleCode": "0", "packageType": 1, "skuId": 6842877}}, {"reference": {"bizVendorCode": "SD3897", "vehicleCode": "0", "packageType": 1, "skuId": 1858057}}, {"reference": {"bizVendorCode": "SD3980", "vehicleCode": "0", "packageType": 1, "skuId": 1861862}}, {"reference": {"bizVendorCode": "SD4825", "vehicleCode": "0", "packageType": 1, "skuId": 1917433}}, {"reference": {"bizVendorCode": "SD4598", "vehicleCode": "0", "packageType": 1, "skuId": 1924627}}, {"reference": {"bizVendorCode": "SD5377", "vehicleCode": "0", "packageType": 1, "skuId": 1978557}}, {"reference": {"bizVendorCode": "SD13224", "vehicleCode": "0", "packageType": 0, "skuId": 62097344}}, {"reference": {"bizVendorCode": "SD15833", "vehicleCode": "0", "packageType": 0, "skuId": 87745624}}, {"reference": {"bizVendorCode": "SD5154", "vehicleCode": "0", "packageType": 1, "skuId": 62524981}}, {"reference": {"bizVendorCode": "SD8059", "vehicleCode": "0", "packageType": 1, "skuId": 4456065}}, {"reference": {"bizVendorCode": "SD14790", "vehicleCode": "0", "packageType": 1, "skuId": 63015674}}, {"reference": {"bizVendorCode": "SD7620", "vehicleCode": "0", "packageType": 0, "skuId": 5243493}}, {"reference": {"bizVendorCode": "SD7620", "vehicleCode": "0", "packageType": 0, "skuId": 5243493}}, {"reference": {"bizVendorCode": "SD7421", "vehicleCode": "0", "packageType": 1, "skuId": 89995396}}, {"reference": {"bizVendorCode": "SD3111", "vehicleCode": "0", "packageType": 1, "skuId": 4496798}}, {"reference": {"bizVendorCode": "SD5044", "vehicleCode": "0", "packageType": 1, "skuId": 6750753}}, {"reference": {"bizVendorCode": "SD5953", "vehicleCode": "0", "packageType": 1, "skuId": 6808126}}, {"reference": {"bizVendorCode": "SD8398", "vehicleCode": "0", "packageType": 0, "skuId": 79756146}}, {"reference": {"bizVendorCode": "SD4215", "vehicleCode": "0", "packageType": 1, "skuId": 1913049}}, {"reference": {"bizVendorCode": "SD3191", "vehicleCode": "0", "packageType": 1, "skuId": 1798271}}, {"reference": {"bizVendorCode": "SD3226", "vehicleCode": "0", "packageType": 1, "skuId": 5109671}}, {"reference": {"bizVendorCode": "SD6300", "vehicleCode": "0", "packageType": 1, "skuId": 57112462}}, {"reference": {"bizVendorCode": "SD4583", "vehicleCode": "0", "packageType": 1, "skuId": 1916804}}, {"reference": {"bizVendorCode": "SD3752", "vehicleCode": "0", "packageType": 0, "skuId": 6876781}}, {"reference": {"bizVendorCode": "SD4487", "vehicleCode": "0", "packageType": 1, "skuId": 1917186}}], "reactId": "1357053754", "group": 0, "groupSort": 0, "hot": 0, "hotType": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD8450_0_133801_133801"]}, "minTPrice": 70, "minDPrice": 0, "modifySameVehicle": false, "minDOrinPrice": 178, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减178", "groupId": 1, "mergeId": 0}, "priceSize": 29, "isEasy": true, "isCredit": true, "pWay": "可选：免费送车上门", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_452_"}], "recommendVehicleList": [{"brandEName": "宝马", "brandName": "宝马", "name": "宝马3系（23款及以前）", "zhName": "宝马3系（23款及以前）", "vehicleCode": "166", "groupCode": "5", "groupSubClassCode": "", "groupName": "豪华轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "2.0T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置四驱/前置后驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5712000krsbpjuD61D.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6c12000krstnvx6E36.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6112000krstlye3EA4.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4b12000krsth1p147D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3q12000krstnwn569F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4012000krstjmsCEE8.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2e12000b5jo8l95870.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "部分车辆支持定速巡航/自适应巡航/全速自适应巡航", "carPlay": "部分车辆支持CarPlay/CarLife", "chargeInterface": "部分车辆支持USB/Type-C/AUX", "skylight": "支持", "charge": "", "carPhone": true, "autoStart": true, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1365&app_ver=10.5", "vehiclesSetId": "97", "mediaTypes": [3, 2], "vehicleKey": "0_166_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "snowTyre": {"description": "81223759:0;48113129:0;57940450:0;229405:0;29459186:0;1917488:0;1855175:0;79202236:0;78536711:0;30538817:0;75412058:0;86213681:0;3018335:0;55247459:0;77468987:0;4456241:0;1924454:0;1908158:0;1966073:0;4014454:0;62965659:0;84842927:0;1968509:0;2710305:0;4914919:0;59116989:0;1816926:0;78234346:0;2428627:0;23778168:0;1912557:0;57940450:0;", "type": 0, "typeDesc": "不支持"}, "reverseImage": {"description": "81223759:1;48113129:1;57940450:1;229405:1;29459186:1;1917488:1;1855175:1;79202236:1;78536711:1;30538817:1;75412058:1;86213681:1;3018335:1;55247459:1;77468987:1;4456241:1;1924454:1;1908158:1;1966073:1;4014454:1;62965659:1;84842927:1;1968509:1;2710305:1;4914919:1;59116989:1;1816926:1;78234346:1;2428627:1;23778168:1;1912557:1;57940450:1;", "type": 1, "typeDesc": "支持"}, "reverseSensor": {"description": "81223759:1;48113129:1;57940450:1;229405:1;29459186:1;1917488:1;1855175:1;79202236:1;78536711:1;30538817:1;75412058:1;86213681:1;3018335:1;55247459:1;77468987:1;4456241:1;1924454:1;1908158:1;1966073:1;4014454:1;62965659:1;84842927:1;1968509:1;2710305:1;4914919:1;59116989:1;1816926:1;78234346:1;2428627:1;23778168:1;1912557:1;57940450:1;", "type": 1, "typeDesc": "支持"}, "shortEndurance": "km"}, {"brandEName": "宝马", "brandName": "宝马", "name": "宝马3系（23款及以前）", "zhName": "宝马3系（23款及以前）", "vehicleCode": "166", "groupCode": "5", "groupSubClassCode": "", "groupName": "豪华轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "2.0T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置四驱/前置后驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5712000krsbpjuD61D.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6c12000krstnvx6E36.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6112000krstlye3EA4.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4b12000krsth1p147D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3q12000krstnwn569F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4012000krstjmsCEE8.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2e12000b5jo8l95870.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "部分车辆支持定速巡航/自适应巡航/全速自适应巡航", "carPlay": "部分车辆支持CarPlay/CarLife", "chargeInterface": "部分车辆支持USB/Type-C/AUX", "skylight": "支持", "charge": "", "carPhone": true, "autoStart": true, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1365&app_ver=10.5", "vehiclesSetId": "97", "mediaTypes": [3, 2], "vehicleKey": "2_166_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "snowTyre": {"description": "81223759:0;48113129:0;57940450:0;229405:0;29459186:0;1917488:0;1855175:0;79202236:0;78536711:0;30538817:0;75412058:0;86213681:0;3018335:0;55247459:0;77468987:0;4456241:0;1924454:0;1908158:0;1966073:0;4014454:0;62965659:0;84842927:0;1968509:0;2710305:0;4914919:0;59116989:0;1816926:0;78234346:0;2428627:0;23778168:0;1912557:0;57940450:0;", "type": 0, "typeDesc": "不支持"}, "reverseImage": {"description": "81223759:1;48113129:1;57940450:1;229405:1;29459186:1;1917488:1;1855175:1;79202236:1;78536711:1;30538817:1;75412058:1;86213681:1;3018335:1;55247459:1;77468987:1;4456241:1;1924454:1;1908158:1;1966073:1;4014454:1;62965659:1;84842927:1;1968509:1;2710305:1;4914919:1;59116989:1;1816926:1;78234346:1;2428627:1;23778168:1;1912557:1;57940450:1;", "type": 1, "typeDesc": "支持"}, "reverseSensor": {"description": "81223759:1;48113129:1;57940450:1;229405:1;29459186:1;1917488:1;1855175:1;79202236:1;78536711:1;30538817:1;75412058:1;86213681:1;3018335:1;55247459:1;77468987:1;4456241:1;1924454:1;1908158:1;1966073:1;4014454:1;62965659:1;84842927:1;1968509:1;2710305:1;4914919:1;59116989:1;1816926:1;78234346:1;2428627:1;23778168:1;1912557:1;57940450:1;", "type": 1, "typeDesc": "支持"}, "shortEndurance": "km"}, {"brandEName": "凯迪拉克", "brandName": "凯迪拉克", "name": "凯迪拉克XTS", "zhName": "凯迪拉克XTS", "vehicleCode": "2385", "groupCode": "5", "groupSubClassCode": "", "groupName": "豪华轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "2.0T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV2u12000c8khi7eEE86.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6t12000cgm6m28D8EF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3u12000cgm6kt68757.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2912000cgm6p0p1D6C.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6j12000cgm6l5g68CA.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5912000cgm6ri1AB4B.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1f12000cgm6qht959C.jpg?mark=yiche", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "支持定速巡航/自适应巡航", "carPlay": "部分车辆支持CarPlay", "chargeInterface": "部分车辆支持USB/AUX/SD/Type-C", "skylight": "支持", "charge": "", "carPhone": true, "autoStart": true, "autoBackUp": false, "vehiclesSetId": "97", "mediaTypes": [], "vehicleKey": "0_2385_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "snowTyre": {"description": "3365901:0;", "type": 0, "typeDesc": "不支持"}, "reverseImage": {"description": "3365901:1;", "type": 1, "typeDesc": "支持"}, "reverseSensor": {"description": "3365901:1;", "type": 1, "typeDesc": "支持"}, "shortEndurance": "km"}, {"brandEName": "奔驰", "brandName": "奔驰", "name": "奔驰C级", "zhName": "奔驰C级", "vehicleCode": "452", "groupCode": "5", "groupSubClassCode": "", "groupName": "豪华轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.5T-1.6T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置后驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0e12000etff2hzBDBC.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5812000cftzt044374.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6512000cftzt0l0B1B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2n12000cftzs2y016D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3y12000cftzuc925CB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6212000cftzrhbD267.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2z12000ap2wurk3093.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "支持定速巡航", "carPlay": "部分车辆支持CarPlay/CarLife", "chargeInterface": "部分车辆支持USB/SD/AUX", "skylight": "支持", "charge": "", "carPhone": true, "vehiclesSetId": "97", "mediaTypes": [2], "vehicleKey": "0_452_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "snowTyre": {"description": "88559213:0;3742373:0;68999813:0;73253741:0;6842877:0;1858057:0;1861862:0;1917433:0;1924627:0;1978557:0;62097344:0;87745624:0;62524981:0;4456065:0;63015674:0;5243493:0;5243493:0;89995396:0;4496798:0;6750753:0;6808126:0;79756146:0;1913049:0;1798271:0;5109671:0;57112462:0;1916804:0;6876781:0;1917186:0;", "type": 0, "typeDesc": "不支持"}, "reverseImage": {"description": "88559213:1;3742373:1;68999813:1;73253741:1;6842877:1;1858057:1;1861862:1;1917433:1;1924627:1;1978557:1;62097344:1;87745624:0;62524981:1;4456065:1;63015674:1;5243493:1;5243493:1;89995396:1;4496798:1;6750753:1;6808126:1;79756146:1;1913049:1;1798271:1;5109671:1;57112462:1;1916804:1;6876781:1;1917186:1;", "type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"description": "88559213:1;3742373:1;68999813:1;73253741:1;6842877:1;1858057:1;1861862:1;1917433:1;1924627:1;1978557:1;62097344:1;87745624:0;62524981:1;4456065:1;63015674:1;5243493:1;5243493:1;89995396:1;4496798:1;6750753:1;6808126:1;79756146:1;1913049:0;1798271:1;5109671:1;57112462:1;1916804:1;6876781:1;1917186:1;", "type": 2, "typeDesc": "部分车辆支持"}, "shortEndurance": "km"}], "productGroupCodeUesd": "prep", "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "shareVehicleInfo": {"vehicleName": "奥迪A4L", "groupName": "奥迪A4L", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "displacement": "2.0T", "style": "", "vehicleImage": "https://dimg04.c-ctrip.com/images/0RV6m12000c54zimgB978.png?mark=yiche", "license": "", "minCurrentDailyPrice": 0}, "floor": [{"floorId": "0_1_1", "floorName": ["1年内车龄"], "isSelect": true, "pickWayInfo": "免费送车上门", "alltags": [{"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 48, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 0, "currentOriginalDailyPrice": 188, "curOriginDPrice": 188, "oTPrice": 248, "currentTotalPrice": 60, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 188, "payofftype": 2}], "gapPrice": 0}, "reference": {"bizVendorCode": "SD5051", "vendorCode": "82747", "pStoreCode": "115276", "rStoreCode": "115276", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD5051_0_115276_115276", "comPriceCode": "[c]NzYzMzMxMzQ4MDIxMTE3NDAwfDI1fDAuMDUtMjAyNS06MDA6NiAwMDg4JiYwMCYxbHNlJjEmZmEkfDEwMTg4JiYxODgwMSYxJDEwMCYxODgyMC4wMyYxJi4wMCQwJjIwJjEmNDEwMDImNDAuMC4wMDIwMjUwMCR8MjYgMS0wNS06MDAmODozMC0wNS0yMDI1ODozMDI3IDEyMDI1OjAwfDIyIDEtMDUtOjA0ADM6NTc=", "priceVersion": "SH-PRICEVERSION_MTE1Mjc2XzMzXzFfMTg4XzE4OF8xODhfMjQ4LjAwXzBfNjAuMF8wXzBfMC4wXzAuMF80MC4wMF8yMC4wMF8wLjAwXzAuMDBfNTAzMjk4NV8zNTEwfDM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNTA0XzEwMDE6MTg4fDEwMDM6MjAuMDB8MTAwMjo0MC4wMF8wLjVfMzU2Mw==", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "5032985", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减188", "groupId": 1, "mergeId": 0}, {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减188", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 248, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "5032985", "storeId": "115276"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "精选好货", "pRc": 0, "rRc": 0, "skuId": 5032985, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 2127889, "rLevel": 2127889, "promtId": 841370114, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "34", "v": "88409"}, "newEnergy": 0, "platform": 10, "kPSId": 115276, "kRSId": 115276, "kVId": 82747, "pLev": 2127889, "rLev": 2127889, "klbVersion": 1, "kVehicleId": 33, "stockLevel": "A", "adjustRuleId": "", "packageLevel": "BAS", "productId": "31347633117480215", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减188", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 188, "amountDesc": "¥188"}, {"code": "11037", "name": "优惠券", "amount": 169, "amountDesc": "¥169"}, {"code": "4530", "name": "火车票用户专享", "amount": 19, "amountDesc": "¥19"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 60, "amountStr": "¥60", "subAmount": 248, "subAmountStr": "¥248", "currencyCode": "¥"}], "packageLevel": "BAS"}, {"code": "2001", "name": "优享保障", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 0, "currentOriginalDailyPrice": 188, "curOriginDPrice": 188, "oTPrice": 288, "currentTotalPrice": 100, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 188, "payofftype": 2}], "gapPrice": 40}, "reference": {"bizVendorCode": "SD5051", "vendorCode": "82747", "pStoreCode": "115276", "rStoreCode": "115276", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD5051_0_115276_115276", "comPriceCode": "[c]NzYzMzMxMzQ4MDIxMTE3NDAwfDI1fDAuMDUtMjAyNS06MDA6NiAwMDg4JiYwMCYxbHNlJjEmZmEkfDEwMTg4JiYxODgwMSYxJDEwMCYxODgyMC4wMyYxJi4wMCQwJjIwJjEmNDEwMDImNDAuMC4wMDIwMjUwMCR8MjYgMS0wNS06MDAmODozMC0wNS0yMDI1ODozMDI3IDEyMDI1OjAwfDIyIDEtMDUtOjA0ADM6NTc=", "priceVersion": "SH-PRICEVERSION_MTE1Mjc2XzMzXzFfMTg4XzE4OF8xODhfMjQ4LjAwXzBfNjAuMF8wXzBfMC4wXzAuMF80MC4wMF8yMC4wMF8wLjAwXzAuMDBfNTAzMjk4NV8zNTEwfDM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNTA0XzEwMDE6MTg4fDEwMDM6MjAuMDB8MTAwMjo0MC4wMF8wLjVfMzU2Mw==", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "5032985", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减188", "groupId": 1, "mergeId": 0}, {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减188", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 248, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "5032985", "storeId": "115276"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "精选好货", "pRc": 0, "rRc": 0, "skuId": 5032985, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 2127889, "rLevel": 2127889, "promtId": 841370114, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "34", "v": "88409"}, "newEnergy": 0, "platform": 10, "kPSId": 115276, "kRSId": 115276, "kVId": 82747, "pLev": 2127889, "rLev": 2127889, "klbVersion": 1, "kVehicleId": 33, "stockLevel": "A", "adjustRuleId": "", "packageLevel": "ADV", "productId": "31347633117480215", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减188", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 188, "amountDesc": "¥188"}, {"code": "11037", "name": "优惠券", "amount": 169, "amountDesc": "¥169"}, {"code": "4530", "name": "火车票用户专享", "amount": 19, "amountDesc": "¥19"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}, {"code": "2001", "name": "优享服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 100, "amountStr": "¥100", "subAmount": 288, "subAmountStr": "¥288", "currencyCode": "¥"}], "packageLevel": "ADV"}, {"code": "2011", "name": "尊享保障", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"currentDailyPrice": 0, "currentOriginalDailyPrice": 188, "curOriginDPrice": 188, "oTPrice": 348, "currentTotalPrice": 160, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 188, "payofftype": 2}], "gapPrice": 100}, "reference": {"bizVendorCode": "SD5051", "vendorCode": "82747", "pStoreCode": "115276", "rStoreCode": "115276", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD5051_0_115276_115276", "comPriceCode": "[c]NzYzMzMxMzQ4MDIxMTE3NDAwfDI1fDAuMDUtMjAyNS06MDA6NiAwMDg4JiYwMCYxbHNlJjEmZmEkfDEwMTg4JiYxODgwMSYxJDEwMCYxODgyMC4wMyYxJi4wMCQwJjIwJjEmNDEwMDImNDAuMC4wMDIwMjUwMCR8MjYgMS0wNS06MDAmODozMC0wNS0yMDI1ODozMDI3IDEyMDI1OjAwfDIyIDEtMDUtOjA0ADM6NTc=", "priceVersion": "SH-PRICEVERSION_MTE1Mjc2XzMzXzFfMTg4XzE4OF8xODhfMjQ4LjAwXzBfNjAuMF8wXzBfMC4wXzAuMF80MC4wMF8yMC4wMF8wLjAwXzAuMDBfNTAzMjk4NV8zNTEwfDM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNTA0XzEwMDE6MTg4fDEwMDM6MjAuMDB8MTAwMjo0MC4wMF8wLjVfMzU2Mw==", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "5032985", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减188", "groupId": 1, "mergeId": 0}, {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减188", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 248, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "5032985", "storeId": "115276"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "精选好货", "pRc": 0, "rRc": 0, "skuId": 5032985, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 2127889, "rLevel": 2127889, "promtId": 841370114, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "34", "v": "88409"}, "newEnergy": 0, "platform": 10, "kPSId": 115276, "kRSId": 115276, "kVId": 82747, "pLev": 2127889, "rLev": 2127889, "klbVersion": 1, "kVehicleId": 33, "stockLevel": "A", "adjustRuleId": "", "packageLevel": "PRE", "productId": "31347633117480215", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减188", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 188, "amountDesc": "¥188"}, {"code": "11037", "name": "优惠券", "amount": 169, "amountDesc": "¥169"}, {"code": "4530", "name": "火车票用户专享", "amount": 19, "amountDesc": "¥19"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}, {"code": "2011", "name": "尊享服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 160, "amountStr": "¥160", "subAmount": 348, "subAmountStr": "¥348", "currencyCode": "¥"}], "packageLevel": "PRE"}], "lowestPrice": 1, "vendorName": "佳翊租车", "recommendationDesc": "本车型1年内车龄最低价"}, {"floorId": "0_3_1", "floorName": ["2年以上车龄"], "isSelect": true, "pickWayInfo": "免费送车上门", "alltags": [{"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 48, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 0, "currentOriginalDailyPrice": 180, "curOriginDPrice": 180, "oTPrice": 240, "currentTotalPrice": 60, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 180, "payofftype": 2}], "gapPrice": 0}, "reference": {"bizVendorCode": "SD4085", "vendorCode": "80333", "pStoreCode": "107175", "rStoreCode": "107175", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4085_0_107175_107175", "comPriceCode": "[c]NjI0MDMxMzM0NTY0MzM2OTAwfDI5fDAuMDUtMjAyNS06MDA6NiAwMDgwJiYwMCYxbHNlJjEmZmEkfDEwMTgwJiYxODAwMSYxJDEwMCYxODAyMC4wMyYxJi4wMCQwJjIwJjEmNDEwMDImNDAuMC4wMDIwMjUwMCR8MjYgMS0wNS06MDAmODozMC0wNS0yMDI1ODozMDI3IDEyMDI1OjAwfDIyIDEtMDUtOjA0ADM6NTc=", "priceVersion": "SH-PRICEVERSION_MTA3MTc1XzMzXzFfMTgwXzE4MF8xODBfMjQwLjAwXzBfNjAuMF8wXzBfMC4wXzAuMF80MC4wMF8yMC4wMF8wLjAwXzAuMDBfNDQwMDQyMl8zODEwfDM1NjN8Mzc0NnwzNzg4fDQyMjl8MzUwNF8xMDAxOjE4MHwxMDAzOjIwLjAwfDEwMDI6NDAuMDBfM18zNTYz", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "4400422", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减180", "groupId": 1, "mergeId": 0}, {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减180", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 240, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4400422", "storeId": "107175"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "低价省钱", "pRc": 0, "rRc": 0, "skuId": 4400422, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 127454, "rLevel": 127454, "promtId": 841370114, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "34", "v": "83463"}, "newEnergy": 0, "platform": 10, "kPSId": 107175, "kRSId": 107175, "kVId": 80333, "pLev": 127454, "rLev": 127454, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "BAS", "productId": "31336240336945649", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减180", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 180, "detail": [{"code": "1001", "name": "租车费", "amount": 180, "amountDesc": "¥180"}, {"code": "11037", "name": "优惠券", "amount": 162, "amountDesc": "¥162"}, {"code": "4530", "name": "火车票用户专享", "amount": 18, "amountDesc": "¥18"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 60, "amountStr": "¥60", "subAmount": 240, "subAmountStr": "¥240", "currencyCode": "¥"}], "packageLevel": "BAS"}, {"code": "2001", "name": "优享保障", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 0, "currentOriginalDailyPrice": 180, "curOriginDPrice": 180, "oTPrice": 280, "currentTotalPrice": 100, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 180, "payofftype": 2}], "gapPrice": 40}, "reference": {"bizVendorCode": "SD4085", "vendorCode": "80333", "pStoreCode": "107175", "rStoreCode": "107175", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4085_0_107175_107175", "comPriceCode": "[c]NjI0MDMxMzM0NTY0MzM2OTAwfDI5fDAuMDUtMjAyNS06MDA6NiAwMDgwJiYwMCYxbHNlJjEmZmEkfDEwMTgwJiYxODAwMSYxJDEwMCYxODAyMC4wMyYxJi4wMCQwJjIwJjEmNDEwMDImNDAuMC4wMDIwMjUwMCR8MjYgMS0wNS06MDAmODozMC0wNS0yMDI1ODozMDI3IDEyMDI1OjAwfDIyIDEtMDUtOjA0ADM6NTc=", "priceVersion": "SH-PRICEVERSION_MTA3MTc1XzMzXzFfMTgwXzE4MF8xODBfMjQwLjAwXzBfNjAuMF8wXzBfMC4wXzAuMF80MC4wMF8yMC4wMF8wLjAwXzAuMDBfNDQwMDQyMl8zODEwfDM1NjN8Mzc0NnwzNzg4fDQyMjl8MzUwNF8xMDAxOjE4MHwxMDAzOjIwLjAwfDEwMDI6NDAuMDBfM18zNTYz", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "4400422", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减180", "groupId": 1, "mergeId": 0}, {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减180", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 240, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4400422", "storeId": "107175"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "低价省钱", "pRc": 0, "rRc": 0, "skuId": 4400422, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 127454, "rLevel": 127454, "promtId": 841370114, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "34", "v": "83463"}, "newEnergy": 0, "platform": 10, "kPSId": 107175, "kRSId": 107175, "kVId": 80333, "pLev": 127454, "rLev": 127454, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "ADV", "productId": "31336240336945649", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减180", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 180, "detail": [{"code": "1001", "name": "租车费", "amount": 180, "amountDesc": "¥180"}, {"code": "11037", "name": "优惠券", "amount": 162, "amountDesc": "¥162"}, {"code": "4530", "name": "火车票用户专享", "amount": 18, "amountDesc": "¥18"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}, {"code": "2001", "name": "优享服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 100, "amountStr": "¥100", "subAmount": 280, "subAmountStr": "¥280", "currencyCode": "¥"}], "packageLevel": "ADV"}, {"code": "2011", "name": "尊享保障", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"currentDailyPrice": 0, "currentOriginalDailyPrice": 180, "curOriginDPrice": 180, "oTPrice": 340, "currentTotalPrice": 160, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 180, "payofftype": 2}], "gapPrice": 100}, "reference": {"bizVendorCode": "SD4085", "vendorCode": "80333", "pStoreCode": "107175", "rStoreCode": "107175", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4085_0_107175_107175", "comPriceCode": "[c]NjI0MDMxMzM0NTY0MzM2OTAwfDI5fDAuMDUtMjAyNS06MDA6NiAwMDgwJiYwMCYxbHNlJjEmZmEkfDEwMTgwJiYxODAwMSYxJDEwMCYxODAyMC4wMyYxJi4wMCQwJjIwJjEmNDEwMDImNDAuMC4wMDIwMjUwMCR8MjYgMS0wNS06MDAmODozMC0wNS0yMDI1ODozMDI3IDEyMDI1OjAwfDIyIDEtMDUtOjA0ADM6NTc=", "priceVersion": "SH-PRICEVERSION_MTA3MTc1XzMzXzFfMTgwXzE4MF8xODBfMjQwLjAwXzBfNjAuMF8wXzBfMC4wXzAuMF80MC4wMF8yMC4wMF8wLjAwXzAuMDBfNDQwMDQyMl8zODEwfDM1NjN8Mzc0NnwzNzg4fDQyMjl8MzUwNF8xMDAxOjE4MHwxMDAzOjIwLjAwfDEwMDI6NDAuMDBfM18zNTYz", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "4400422", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减180", "groupId": 1, "mergeId": 0}, {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减180", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 240, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4400422", "storeId": "107175"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "低价省钱", "pRc": 0, "rRc": 0, "skuId": 4400422, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 127454, "rLevel": 127454, "promtId": 841370114, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "34", "v": "83463"}, "newEnergy": 0, "platform": 10, "kPSId": 107175, "kRSId": 107175, "kVId": 80333, "pLev": 127454, "rLev": 127454, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "PRE", "productId": "31336240336945649", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减180", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 180, "detail": [{"code": "1001", "name": "租车费", "amount": 180, "amountDesc": "¥180"}, {"code": "11037", "name": "优惠券", "amount": 162, "amountDesc": "¥162"}, {"code": "4530", "name": "火车票用户专享", "amount": 18, "amountDesc": "¥18"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}, {"code": "2011", "name": "尊享服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 160, "amountStr": "¥160", "subAmount": 340, "subAmountStr": "¥340", "currencyCode": "¥"}], "packageLevel": "PRE"}], "vendorName": "百铭租车"}, {"floorId": "0_3_3", "floorName": ["2年以上车龄"], "isSelect": false, "pickWayInfo": "门店取车，距所选地址直线1.9公里", "alltags": [{"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "20万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 232, "currentOriginalDailyPrice": 258, "curOriginDPrice": 258, "oTPrice": 368, "currentTotalPrice": 342, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 26, "payofftype": 2}], "gapPrice": 0}, "reference": {"bizVendorCode": "SD6991", "vendorCode": "13088", "pStoreCode": "140092", "rStoreCode": "140092", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD6991_0_140092_140092", "priceVersion": "SH-PRICEVERSION_MTQwMDkyXzMzXzFfMjU4LjBfMjU4LjBfMC4wXzM2OC4wXzIzMi4wXzM0Mi4wXzBfMF8wLjBfMC4wXzkwLjBfMjAuMF8wXzBfNDA1NDQ2ODBfMzgxMHwzNTYzfDM3NDZ8Mzc1N18xMDAxOjI1OC4wfDEwMDM6MjAuMHwxMDAyOjkwLjBfOTlfMzU2Mw==", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "1254", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "20", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "已减26", "groupId": 1, "mergeId": 0}], "pStoreNav": "门店取车，距所选地址直线1.9公里", "rStoreNav": "门店还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 368, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 232, "totalDailyPrice": 232, "vdegree": "20", "grantedcode": "", "mergeInfo": [{"vehicleId": "1254", "storeId": "140092"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 40544680, "klbPId": 13158, "klb": 1, "pCType": -1, "rCType": -1, "pLevel": -1, "rLevel": -1, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "0", "s": "4.53", "c": "34"}, "newEnergy": 0, "platform": 0, "kPSId": 140092, "kRSId": 140092, "kVId": 13088, "pLev": -1, "rLev": -1, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "BAS", "platformCal": false, "originPsType": 0}, "marketingTags": [{"title": "火车票用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "已减26", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 232, "amountStr": "¥232", "subAmount": 232, "subAmountStr": "日均¥232", "originalDailyPrice": 258, "detail": [{"code": "1001", "name": "租车费", "amount": 258, "amountDesc": "¥258"}, {"code": "4530", "name": "火车票用户专享", "amount": 26, "amountDesc": "¥26"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "name": "基础服务费", "amount": 90, "amountDesc": "¥90", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 342, "amountStr": "¥342", "subAmount": 368, "subAmountStr": "¥368", "currencyCode": "¥"}], "packageLevel": "BAS"}], "vendorName": "一嗨租车"}, {"floorId": "0_2_1", "floorName": ["2年内车龄"], "isSelect": true, "pickWayInfo": "免费送车上门", "alltags": [{"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 0, "currentOriginalDailyPrice": 158, "curOriginDPrice": 158, "oTPrice": 238, "currentTotalPrice": 80, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 158, "payofftype": 2}], "gapPrice": 0}, "reference": {"bizVendorCode": "SD3008", "vendorCode": "13033", "pStoreCode": "44844", "rStoreCode": "44844", "vehicleCode": "0", "packageId": "Enjoyment", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3008_0_44844_44844", "comPriceCode": "7244477c5a2cddf7bbf34b275212ca4e-638835228000000000", "priceVersion": "SH-PRICEVERSION_NDQ4NDRfMzNfMV8xNTguMF8xNTguMF8wLjBfMjM4LjAwXzBfODAuMF8wXzBfMC4wXzAuMF82MC4wXzIwLjBfMC4wMF8wLjAwXzIzMjI5MV8zNTQ3fDM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjE1OC4wfDEwMDM6MjAuMHwxMDAyOjYwLjBfMl8zNTYz", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "ADJC6653", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减158", "groupId": 1, "mergeId": 0}, {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减158", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 238, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "ADJC6653", "storeId": "44844"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 232291, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 1411019, "rLevel": 1411019, "promtId": 841370114, "rCoup": 0, "sortInfo": {"p": "1", "s": "59.96", "c": "34"}, "newEnergy": 0, "platform": 0, "kPSId": 44844, "kRSId": 44844, "kVId": 13033, "pLev": 1411019, "rLev": 1411019, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "BAS", "productId": "31642660743922119", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减158", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 158, "detail": [{"code": "1001", "name": "租车费", "amount": 158, "amountDesc": "¥158"}, {"code": "11037", "name": "优惠券", "amount": 142, "amountDesc": "¥142"}, {"code": "4530", "name": "火车票用户专享", "amount": 16, "amountDesc": "¥16"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 80, "amountStr": "¥80", "subAmount": 238, "subAmountStr": "¥238", "currencyCode": "¥"}], "packageLevel": "BAS"}, {"code": "2001", "name": "优享保障", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 0, "currentOriginalDailyPrice": 158, "curOriginDPrice": 158, "oTPrice": 278, "currentTotalPrice": 120, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 158, "payofftype": 2}], "gapPrice": 40}, "reference": {"bizVendorCode": "SD3008", "vendorCode": "13033", "pStoreCode": "44844", "rStoreCode": "44844", "vehicleCode": "0", "packageId": "Enjoyment", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3008_0_44844_44844", "comPriceCode": "7244477c5a2cddf7bbf34b275212ca4e-638835228000000000", "priceVersion": "SH-PRICEVERSION_NDQ4NDRfMzNfMV8xNTguMF8xNTguMF8wLjBfMjM4LjAwXzBfODAuMF8wXzBfMC4wXzAuMF82MC4wXzIwLjBfMC4wMF8wLjAwXzIzMjI5MV8zNTQ3fDM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjE1OC4wfDEwMDM6MjAuMHwxMDAyOjYwLjBfMl8zNTYz", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "ADJC6653", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减158", "groupId": 1, "mergeId": 0}, {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减158", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 238, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "ADJC6653", "storeId": "44844"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 232291, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 1411019, "rLevel": 1411019, "promtId": 841370114, "rCoup": 0, "sortInfo": {"p": "1", "s": "59.96", "c": "34"}, "newEnergy": 0, "platform": 0, "kPSId": 44844, "kRSId": 44844, "kVId": 13033, "pLev": 1411019, "rLev": 1411019, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "ADV", "productId": "31642660743922119", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减158", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 158, "detail": [{"code": "1001", "name": "租车费", "amount": 158, "amountDesc": "¥158"}, {"code": "11037", "name": "优惠券", "amount": 142, "amountDesc": "¥142"}, {"code": "4530", "name": "火车票用户专享", "amount": 16, "amountDesc": "¥16"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}, {"code": "2001", "name": "优享服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 120, "amountStr": "¥120", "subAmount": 278, "subAmountStr": "¥278", "currencyCode": "¥"}], "packageLevel": "ADV"}, {"code": "2011", "name": "尊享保障", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"currentDailyPrice": 0, "currentOriginalDailyPrice": 158, "curOriginDPrice": 158, "oTPrice": 338, "currentTotalPrice": 180, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 158, "payofftype": 2}], "gapPrice": 100}, "reference": {"bizVendorCode": "SD3008", "vendorCode": "13033", "pStoreCode": "44844", "rStoreCode": "44844", "vehicleCode": "0", "packageId": "Enjoyment", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3008_0_44844_44844", "comPriceCode": "7244477c5a2cddf7bbf34b275212ca4e-638835228000000000", "priceVersion": "SH-PRICEVERSION_NDQ4NDRfMzNfMV8xNTguMF8xNTguMF8wLjBfMjM4LjAwXzBfODAuMF8wXzBfMC4wXzAuMF82MC4wXzIwLjBfMC4wMF8wLjAwXzIzMjI5MV8zNTQ3fDM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjE1OC4wfDEwMDM6MjAuMHwxMDAyOjYwLjBfMl8zNTYz", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "ADJC6653", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减158", "groupId": 1, "mergeId": 0}, {"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减158", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 238, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "ADJC6653", "storeId": "44844"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 232291, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 1411019, "rLevel": 1411019, "promtId": 841370114, "rCoup": 0, "sortInfo": {"p": "1", "s": "59.96", "c": "34"}, "newEnergy": 0, "platform": 0, "kPSId": 44844, "kRSId": 44844, "kVId": 13033, "pLev": 1411019, "rLev": 1411019, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "PRE", "productId": "31642660743922119", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "火车票用户专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "共减158", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 158, "detail": [{"code": "1001", "name": "租车费", "amount": 158, "amountDesc": "¥158"}, {"code": "11037", "name": "优惠券", "amount": 142, "amountDesc": "¥142"}, {"code": "4530", "name": "火车票用户专享", "amount": 16, "amountDesc": "¥16"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 160, "amountStr": "¥160", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}, {"code": "2011", "name": "尊享服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 180, "amountStr": "¥180", "subAmount": 338, "subAmountStr": "¥338", "currencyCode": "¥"}], "packageLevel": "PRE"}], "vendorName": "枫叶租车"}, {"floorId": "1_3_1", "floorName": ["2年以上车龄"], "isSelect": false, "pickWayInfo": "免费送车上门", "alltags": [{"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 133, "currentOriginalDailyPrice": 143, "curOriginDPrice": 143, "oTPrice": 213, "currentTotalPrice": 203, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10, "payofftype": 2}], "gapPrice": 0}, "reference": {"bizVendorCode": "SD6248", "vendorCode": "30305", "pStoreCode": "115960", "rStoreCode": "115960", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD6248_0_115960_115960", "comPriceCode": "[c]NzczNTMwNTM4MTI1OTQwMTAyNS0zfHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE0MyYmMTQzJmxzZSYwMSYxJHwxMCYxNDMmMTQzMyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjUwMCR8JjUwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTE1OTYwXzMzXzFfMTQzXzE0M18xNDNfMjEzLjAwXzEzMy4wXzIwMy4wXzBfMF8wLjBfMC4wXzUwLjAwXzIwLjAwXzAuMDBfMC4wMF80NDg5ODUyXzM4MTB8MzY3OXwzNzQ2XzEwMDE6MTQzfDEwMDM6MjAuMDB8MTAwMjo1MC4wMF8zXzM2Nzk=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "4489852", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 213, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 133, "totalDailyPrice": 133, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4489852", "storeId": "115960"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 4489852, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 55129, "rLevel": 55129, "promtId": 640365579, "rCoup": 0, "sortInfo": {"p": "1", "s": "11.45", "c": "34", "v": "16197"}, "newEnergy": 0, "platform": 10, "kPSId": 115960, "kRSId": 115960, "kVId": 30305, "pLev": 55129, "rLev": 55129, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "BAS", "productId": "30537735940181253", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 133, "amountStr": "¥133", "subAmount": 133, "subAmountStr": "日均¥133", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "name": "基础服务费", "amount": 50, "amountDesc": "¥50", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 203, "amountStr": "¥203", "subAmount": 213, "subAmountStr": "¥213", "currencyCode": "¥"}], "packageLevel": "BAS"}, {"code": "2001", "name": "优享保障", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 133, "currentOriginalDailyPrice": 143, "curOriginDPrice": 143, "oTPrice": 253, "currentTotalPrice": 243, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10, "payofftype": 2}], "gapPrice": 40}, "reference": {"bizVendorCode": "SD6248", "vendorCode": "30305", "pStoreCode": "115960", "rStoreCode": "115960", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD6248_0_115960_115960", "comPriceCode": "[c]NzczNTMwNTM4MTI1OTQwMTAyNS0zfHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE0MyYmMTQzJmxzZSYwMSYxJHwxMCYxNDMmMTQzMyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjUwMCR8JjUwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTE1OTYwXzMzXzFfMTQzXzE0M18xNDNfMjEzLjAwXzEzMy4wXzIwMy4wXzBfMF8wLjBfMC4wXzUwLjAwXzIwLjAwXzAuMDBfMC4wMF80NDg5ODUyXzM4MTB8MzY3OXwzNzQ2XzEwMDE6MTQzfDEwMDM6MjAuMDB8MTAwMjo1MC4wMF8zXzM2Nzk=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "4489852", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 213, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 133, "totalDailyPrice": 133, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4489852", "storeId": "115960"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 4489852, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 55129, "rLevel": 55129, "promtId": 640365579, "rCoup": 0, "sortInfo": {"p": "1", "s": "11.45", "c": "34", "v": "16197"}, "newEnergy": 0, "platform": 10, "kPSId": 115960, "kRSId": 115960, "kVId": 30305, "pLev": 55129, "rLev": 55129, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "ADV", "productId": "30537735940181253", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 133, "amountStr": "¥133", "subAmount": 133, "subAmountStr": "日均¥133", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "name": "基础服务费", "amount": 50, "amountDesc": "¥50", "showFree": false}, {"code": "2001", "name": "优享服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 243, "amountStr": "¥243", "subAmount": 253, "subAmountStr": "¥253", "currencyCode": "¥"}], "packageLevel": "ADV"}, {"code": "2011", "name": "尊享保障", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"currentDailyPrice": 133, "currentOriginalDailyPrice": 143, "curOriginDPrice": 143, "oTPrice": 313, "currentTotalPrice": 303, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10, "payofftype": 2}], "gapPrice": 100}, "reference": {"bizVendorCode": "SD6248", "vendorCode": "30305", "pStoreCode": "115960", "rStoreCode": "115960", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD6248_0_115960_115960", "comPriceCode": "[c]NzczNTMwNTM4MTI1OTQwMTAyNS0zfHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE0MyYmMTQzJmxzZSYwMSYxJHwxMCYxNDMmMTQzMyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjUwMCR8JjUwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTE1OTYwXzMzXzFfMTQzXzE0M18xNDNfMjEzLjAwXzEzMy4wXzIwMy4wXzBfMF8wLjBfMC4wXzUwLjAwXzIwLjAwXzAuMDBfMC4wMF80NDg5ODUyXzM4MTB8MzY3OXwzNzQ2XzEwMDE6MTQzfDEwMDM6MjAuMDB8MTAwMjo1MC4wMF8zXzM2Nzk=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "4489852", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 213, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 133, "totalDailyPrice": 133, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4489852", "storeId": "115960"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 4489852, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 55129, "rLevel": 55129, "promtId": 640365579, "rCoup": 0, "sortInfo": {"p": "1", "s": "11.45", "c": "34", "v": "16197"}, "newEnergy": 0, "platform": 10, "kPSId": 115960, "kRSId": 115960, "kVId": 30305, "pLev": 55129, "rLev": 55129, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "PRE", "productId": "30537735940181253", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 133, "amountStr": "¥133", "subAmount": 133, "subAmountStr": "日均¥133", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 150, "amountStr": "¥150", "detail": [{"code": "1002", "name": "基础服务费", "amount": 50, "amountDesc": "¥50", "showFree": false}, {"code": "2011", "name": "尊享服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 303, "amountStr": "¥303", "subAmount": 313, "subAmountStr": "¥313", "currencyCode": "¥"}], "packageLevel": "PRE"}], "vendorName": "展旺租车"}, {"floorId": "0_3_3", "floorName": ["2年以上车龄"], "isSelect": false, "pickWayInfo": "门店取车，距所选地址直线3.0公里", "alltags": [{"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 133, "currentOriginalDailyPrice": 143, "curOriginDPrice": 143, "oTPrice": 213, "currentTotalPrice": 203, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10, "payofftype": 2}], "gapPrice": 0}, "reference": {"bizVendorCode": "SD6248", "vendorCode": "30305", "pStoreCode": "115960", "rStoreCode": "115960", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD6248_0_115960_115960", "comPriceCode": "[c]NzczNTMwNTM4MTI1OTQwMTAyNS0zfHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE0MyYmMTQzJmxzZSYwMSYxJHwxMCYxNDMmMTQzMyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjUwMCR8JjUwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTE1OTYwXzMzXzFfMTQzXzE0M18xNDNfMjEzLjAwXzEzMy4wXzIwMy4wXzBfMF8wLjBfMC4wXzUwLjAwXzIwLjAwXzBfMF80NDg5ODUyXzM4MTB8MzU2M3wzNzQ2XzEwMDE6MTQzfDEwMDM6MjAuMDB8MTAwMjo1MC4wMF8zXzM1NjM=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "4489852", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "门店取车，距所选地址直线3.0公里", "rStoreNav": "门店还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 213, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 133, "totalDailyPrice": 133, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4489852", "storeId": "115960"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 4489852, "klbPId": 0, "klb": 1, "pCType": -1, "rCType": -1, "pLevel": 55129, "rLevel": 55129, "promtId": 640365579, "rCoup": 0, "sortInfo": {"p": "0", "s": "11.45", "c": "34", "v": "16197"}, "newEnergy": 0, "platform": 10, "kPSId": 115960, "kRSId": 115960, "kVId": 30305, "pLev": -1, "rLev": -1, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "BAS", "productId": "30537735940181253", "platformCal": true, "originPsType": 0}, "marketingTags": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 133, "amountStr": "¥133", "subAmount": 133, "subAmountStr": "日均¥133", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "name": "基础服务费", "amount": 50, "amountDesc": "¥50", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 203, "amountStr": "¥203", "subAmount": 213, "subAmountStr": "¥213", "currencyCode": "¥"}], "packageLevel": "BAS"}, {"code": "2001", "name": "优享保障", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 133, "currentOriginalDailyPrice": 143, "curOriginDPrice": 143, "oTPrice": 253, "currentTotalPrice": 243, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10, "payofftype": 2}], "gapPrice": 40}, "reference": {"bizVendorCode": "SD6248", "vendorCode": "30305", "pStoreCode": "115960", "rStoreCode": "115960", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD6248_0_115960_115960", "comPriceCode": "[c]NzczNTMwNTM4MTI1OTQwMTAyNS0zfHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE0MyYmMTQzJmxzZSYwMSYxJHwxMCYxNDMmMTQzMyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjUwMCR8JjUwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTE1OTYwXzMzXzFfMTQzXzE0M18xNDNfMjEzLjAwXzEzMy4wXzIwMy4wXzBfMF8wLjBfMC4wXzUwLjAwXzIwLjAwXzBfMF80NDg5ODUyXzM4MTB8MzU2M3wzNzQ2XzEwMDE6MTQzfDEwMDM6MjAuMDB8MTAwMjo1MC4wMF8zXzM1NjM=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "4489852", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "门店取车，距所选地址直线3.0公里", "rStoreNav": "门店还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 213, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 133, "totalDailyPrice": 133, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4489852", "storeId": "115960"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 4489852, "klbPId": 0, "klb": 1, "pCType": -1, "rCType": -1, "pLevel": 55129, "rLevel": 55129, "promtId": 640365579, "rCoup": 0, "sortInfo": {"p": "0", "s": "11.45", "c": "34", "v": "16197"}, "newEnergy": 0, "platform": 10, "kPSId": 115960, "kRSId": 115960, "kVId": 30305, "pLev": -1, "rLev": -1, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "ADV", "productId": "30537735940181253", "platformCal": true, "originPsType": 0}, "marketingTags": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 133, "amountStr": "¥133", "subAmount": 133, "subAmountStr": "日均¥133", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "name": "基础服务费", "amount": 50, "amountDesc": "¥50", "showFree": false}, {"code": "2001", "name": "优享服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 243, "amountStr": "¥243", "subAmount": 253, "subAmountStr": "¥253", "currencyCode": "¥"}], "packageLevel": "ADV"}, {"code": "2011", "name": "尊享保障", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"currentDailyPrice": 133, "currentOriginalDailyPrice": 143, "curOriginDPrice": 143, "oTPrice": 313, "currentTotalPrice": 303, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10, "payofftype": 2}], "gapPrice": 100}, "reference": {"bizVendorCode": "SD6248", "vendorCode": "30305", "pStoreCode": "115960", "rStoreCode": "115960", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD6248_0_115960_115960", "comPriceCode": "[c]NzczNTMwNTM4MTI1OTQwMTAyNS0zfHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE0MyYmMTQzJmxzZSYwMSYxJHwxMCYxNDMmMTQzMyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjUwMCR8JjUwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTE1OTYwXzMzXzFfMTQzXzE0M18xNDNfMjEzLjAwXzEzMy4wXzIwMy4wXzBfMF8wLjBfMC4wXzUwLjAwXzIwLjAwXzBfMF80NDg5ODUyXzM4MTB8MzU2M3wzNzQ2XzEwMDE6MTQzfDEwMDM6MjAuMDB8MTAwMjo1MC4wMF8zXzM1NjM=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "4489852", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "门店取车，距所选地址直线3.0公里", "rStoreNav": "门店还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 213, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 133, "totalDailyPrice": 133, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4489852", "storeId": "115960"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 4489852, "klbPId": 0, "klb": 1, "pCType": -1, "rCType": -1, "pLevel": 55129, "rLevel": 55129, "promtId": 640365579, "rCoup": 0, "sortInfo": {"p": "0", "s": "11.45", "c": "34", "v": "16197"}, "newEnergy": 0, "platform": 10, "kPSId": 115960, "kRSId": 115960, "kVId": 30305, "pLev": -1, "rLev": -1, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "PRE", "productId": "30537735940181253", "platformCal": true, "originPsType": 0}, "marketingTags": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 133, "amountStr": "¥133", "subAmount": 133, "subAmountStr": "日均¥133", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 150, "amountStr": "¥150", "detail": [{"code": "1002", "name": "基础服务费", "amount": 50, "amountDesc": "¥50", "showFree": false}, {"code": "2011", "name": "尊享服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 303, "amountStr": "¥303", "subAmount": 313, "subAmountStr": "¥313", "currencyCode": "¥"}], "packageLevel": "PRE"}], "vendorName": "展旺租车"}, {"floorId": "1_2_1", "floorName": ["2年内车龄"], "isSelect": false, "pickWayInfo": "送车上门", "alltags": [{"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 128, "currentOriginalDailyPrice": 143, "curOriginDPrice": 143, "oTPrice": 243, "currentTotalPrice": 228, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 15, "payofftype": 2}], "gapPrice": 0}, "reference": {"bizVendorCode": "SD16131", "vendorCode": "15008582", "pStoreCode": "2717009", "rStoreCode": "2717009", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD16131_0_2717009_2717009", "comPriceCode": "[c]OTE5ODMzNzkxODg2Mzc5OTAyNS0yfHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE0MyYmMTQzJmxzZSYwMSYxJHwxMCYxNDMmMTQzMyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjQwMCR8JjQwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MjcxNzAwOV8zM18xXzE0M18xNDNfMTQzXzI0My4wMF8xMjguMF8yMjguMF8xXzFfMC4wXzAuMF80MC4wMF8yMC4wMF8yMC4wMF8yMC4wMF84ODU5Njg4MF8zNTQ3fDM4MTB8MzY3OXwzNzQ2XzEwMDE6MTQzfDEwMDM6MjAuMDB8MTEwMjY6MjAuMDB8MTEwMjc6MjAuMDB8MTAwMjo0MC4wMF8yXzM2Nzk=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "88596880", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "已减15", "groupId": 1, "mergeId": 0}], "pStoreNav": "送车上门", "rStoreNav": "上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 2, "returnWayInfo": 2, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 243, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 128, "totalDailyPrice": 128, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "88596880", "storeId": "2717009"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 88596880, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 2221927, "rLevel": 2221927, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "2", "s": "9.31", "c": "34", "v": "2717009"}, "newEnergy": 0, "platform": 10, "kPSId": 2717009, "kRSId": 2717009, "kVId": 15008582, "pLev": 2221927, "rLev": 2221927, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "BAS", "productId": "33799198379918862", "platformCal": true, "originPsType": 2}, "marketingTags": [{"title": "火车票用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "已减15", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 128, "amountStr": "¥128", "subAmount": 128, "subAmountStr": "日均¥128", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "4530", "name": "火车票用户专享", "amount": 15, "amountDesc": "¥15"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}, {"code": "11026", "name": "送车上门服务费", "amount": 20, "amountDesc": "¥20", "showFree": false}, {"code": "11027", "name": "上门取车服务费", "amount": 20, "amountDesc": "¥20", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 228, "amountStr": "¥228", "subAmount": 243, "subAmountStr": "¥243", "currencyCode": "¥"}], "packageLevel": "BAS"}, {"code": "2001", "name": "优享保障", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 128, "currentOriginalDailyPrice": 143, "curOriginDPrice": 143, "oTPrice": 283, "currentTotalPrice": 268, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 15, "payofftype": 2}], "gapPrice": 40}, "reference": {"bizVendorCode": "SD16131", "vendorCode": "15008582", "pStoreCode": "2717009", "rStoreCode": "2717009", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD16131_0_2717009_2717009", "comPriceCode": "[c]OTE5ODMzNzkxODg2Mzc5OTAyNS0yfHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE0MyYmMTQzJmxzZSYwMSYxJHwxMCYxNDMmMTQzMyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjQwMCR8JjQwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MjcxNzAwOV8zM18xXzE0M18xNDNfMTQzXzI0My4wMF8xMjguMF8yMjguMF8xXzFfMC4wXzAuMF80MC4wMF8yMC4wMF8yMC4wMF8yMC4wMF84ODU5Njg4MF8zNTQ3fDM4MTB8MzY3OXwzNzQ2XzEwMDE6MTQzfDEwMDM6MjAuMDB8MTEwMjY6MjAuMDB8MTEwMjc6MjAuMDB8MTAwMjo0MC4wMF8yXzM2Nzk=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "88596880", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "已减15", "groupId": 1, "mergeId": 0}], "pStoreNav": "送车上门", "rStoreNav": "上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 2, "returnWayInfo": 2, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 243, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 128, "totalDailyPrice": 128, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "88596880", "storeId": "2717009"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 88596880, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 2221927, "rLevel": 2221927, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "2", "s": "9.31", "c": "34", "v": "2717009"}, "newEnergy": 0, "platform": 10, "kPSId": 2717009, "kRSId": 2717009, "kVId": 15008582, "pLev": 2221927, "rLev": 2221927, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "ADV", "productId": "33799198379918862", "platformCal": true, "originPsType": 2}, "marketingTags": [{"title": "火车票用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "已减15", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 128, "amountStr": "¥128", "subAmount": 128, "subAmountStr": "日均¥128", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "4530", "name": "火车票用户专享", "amount": 15, "amountDesc": "¥15"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}, {"code": "11026", "name": "送车上门服务费", "amount": 20, "amountDesc": "¥20", "showFree": false}, {"code": "11027", "name": "上门取车服务费", "amount": 20, "amountDesc": "¥20", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}, {"code": "2001", "name": "优享服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 268, "amountStr": "¥268", "subAmount": 283, "subAmountStr": "¥283", "currencyCode": "¥"}], "packageLevel": "ADV"}, {"code": "2011", "name": "尊享保障", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"currentDailyPrice": 128, "currentOriginalDailyPrice": 143, "curOriginDPrice": 143, "oTPrice": 343, "currentTotalPrice": 328, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 15, "payofftype": 2}], "gapPrice": 100}, "reference": {"bizVendorCode": "SD16131", "vendorCode": "15008582", "pStoreCode": "2717009", "rStoreCode": "2717009", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD16131_0_2717009_2717009", "comPriceCode": "[c]OTE5ODMzNzkxODg2Mzc5OTAyNS0yfHwyNiAwMDA1LTIwMCYxOjAwOjEmZmE0MyYmMTQzJmxzZSYwMSYxJHwxMCYxNDMmMTQzMyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjQwMCR8JjQwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MjcxNzAwOV8zM18xXzE0M18xNDNfMTQzXzI0My4wMF8xMjguMF8yMjguMF8xXzFfMC4wXzAuMF80MC4wMF8yMC4wMF8yMC4wMF8yMC4wMF84ODU5Njg4MF8zNTQ3fDM4MTB8MzY3OXwzNzQ2XzEwMDE6MTQzfDEwMDM6MjAuMDB8MTEwMjY6MjAuMDB8MTEwMjc6MjAuMDB8MTAwMjo0MC4wMF8yXzM2Nzk=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "88596880", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "火车票用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "已减15", "groupId": 1, "mergeId": 0}], "pStoreNav": "送车上门", "rStoreNav": "上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 2, "returnWayInfo": 2, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 243, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 128, "totalDailyPrice": 128, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "88596880", "storeId": "2717009"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 88596880, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 2221927, "rLevel": 2221927, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "2", "s": "9.31", "c": "34", "v": "2717009"}, "newEnergy": 0, "platform": 10, "kPSId": 2717009, "kRSId": 2717009, "kVId": 15008582, "pLev": 2221927, "rLev": 2221927, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "PRE", "productId": "33799198379918862", "platformCal": true, "originPsType": 2}, "marketingTags": [{"title": "火车票用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "4530", "groupCode": "MarketGroup503", "amountTitle": "已减15", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 128, "amountStr": "¥128", "subAmount": 128, "subAmountStr": "日均¥128", "originalDailyPrice": 143, "detail": [{"code": "1001", "name": "租车费", "amount": 143, "amountDesc": "¥143"}, {"code": "4530", "name": "火车票用户专享", "amount": 15, "amountDesc": "¥15"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}, {"code": "11026", "name": "送车上门服务费", "amount": 20, "amountDesc": "¥20", "showFree": false}, {"code": "11027", "name": "上门取车服务费", "amount": 20, "amountDesc": "¥20", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}, {"code": "2011", "name": "尊享服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 328, "amountStr": "¥328", "subAmount": 343, "subAmountStr": "¥343", "currencyCode": "¥"}], "packageLevel": "PRE"}], "vendorName": "路尚畅行租车"}, {"floorId": "0_3_3", "floorName": ["1年内车龄"], "isSelect": false, "pickWayInfo": "免费送车上门", "alltags": [{"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 289, "currentOriginalDailyPrice": 299, "curOriginDPrice": 299, "oTPrice": 359, "currentTotalPrice": 349, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10, "payofftype": 2}], "gapPrice": 0}, "reference": {"bizVendorCode": "SD7314", "vendorCode": "15000571", "pStoreCode": "153263", "rStoreCode": "153263", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD7314_0_153263_153263", "comPriceCode": "[c]NDE5NTMxMzY2MzE5NjIyODAyNS02fHwyNiAwMDA1LTIwMCYyOjAwOjEmZmE5OSYmMjk5JmxzZSYwMSYxJHwxMCYyOTkmMjk5MyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjQwMCR8JjQwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTUzMjYzXzMzXzFfMjk5XzI5OV8yOTlfMzU5LjAwXzI4OS4wXzM0OS4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzAuMDBfMC4wMF80OTg1NDg5N18zNTEwfDM4MTB8MzY3OXwzNzQ2XzEwMDE6Mjk5fDEwMDM6MjAuMDB8MTAwMjo0MC4wMF8xXzM2Nzk=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "49854897", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 359, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 289, "totalDailyPrice": 289, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "49854897", "storeId": "153263"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 49854897, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 96490, "rLevel": 96490, "promtId": 640365579, "rCoup": 0, "sortInfo": {"p": "1", "s": "4.37", "c": "34", "v": "153263"}, "newEnergy": 0, "platform": 10, "kPSId": 153263, "kRSId": 153263, "kVId": 15000571, "pLev": 96490, "rLev": 96490, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "BAS", "productId": "31364195622863196", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 289, "amountStr": "¥289", "subAmount": 289, "subAmountStr": "日均¥289", "originalDailyPrice": 299, "detail": [{"code": "1001", "name": "租车费", "amount": 299, "amountDesc": "¥299"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 349, "amountStr": "¥349", "subAmount": 359, "subAmountStr": "¥359", "currencyCode": "¥"}], "packageLevel": "BAS"}, {"code": "2001", "name": "优享保障", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"currentDailyPrice": 289, "currentOriginalDailyPrice": 299, "curOriginDPrice": 299, "oTPrice": 399, "currentTotalPrice": 389, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10, "payofftype": 2}], "gapPrice": 40}, "reference": {"bizVendorCode": "SD7314", "vendorCode": "15000571", "pStoreCode": "153263", "rStoreCode": "153263", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD7314_0_153263_153263", "comPriceCode": "[c]NDE5NTMxMzY2MzE5NjIyODAyNS02fHwyNiAwMDA1LTIwMCYyOjAwOjEmZmE5OSYmMjk5JmxzZSYwMSYxJHwxMCYyOTkmMjk5MyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjQwMCR8JjQwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTUzMjYzXzMzXzFfMjk5XzI5OV8yOTlfMzU5LjAwXzI4OS4wXzM0OS4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzAuMDBfMC4wMF80OTg1NDg5N18zNTEwfDM4MTB8MzY3OXwzNzQ2XzEwMDE6Mjk5fDEwMDM6MjAuMDB8MTAwMjo0MC4wMF8xXzM2Nzk=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "49854897", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 359, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 289, "totalDailyPrice": 289, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "49854897", "storeId": "153263"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 49854897, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 96490, "rLevel": 96490, "promtId": 640365579, "rCoup": 0, "sortInfo": {"p": "1", "s": "4.37", "c": "34", "v": "153263"}, "newEnergy": 0, "platform": 10, "kPSId": 153263, "kRSId": 153263, "kVId": 15000571, "pLev": 96490, "rLev": 96490, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "ADV", "productId": "31364195622863196", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 289, "amountStr": "¥289", "subAmount": 289, "subAmountStr": "日均¥289", "originalDailyPrice": 299, "detail": [{"code": "1001", "name": "租车费", "amount": 299, "amountDesc": "¥299"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}, {"code": "2001", "name": "优享服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 389, "amountStr": "¥389", "subAmount": 399, "subAmountStr": "¥399", "currencyCode": "¥"}], "packageLevel": "ADV"}, {"code": "2011", "name": "尊享保障", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"currentDailyPrice": 289, "currentOriginalDailyPrice": 299, "curOriginDPrice": 299, "oTPrice": 459, "currentTotalPrice": 449, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10, "payofftype": 2}], "gapPrice": 100}, "reference": {"bizVendorCode": "SD7314", "vendorCode": "15000571", "pStoreCode": "153263", "rStoreCode": "153263", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD7314_0_153263_153263", "comPriceCode": "[c]NDE5NTMxMzY2MzE5NjIyODAyNS02fHwyNiAwMDA1LTIwMCYyOjAwOjEmZmE5OSYmMjk5JmxzZSYwMSYxJHwxMCYyOTkmMjk5MyYxJiQxMDAwJjIwMjAuMDEwMDIuMDAkMC4wMCYxJjQwMCR8JjQwLi0wNS0yMDI1ODozMDI2IDEyMDI1OjAwJjI3IDEtMDUtOjAwfDg6MzAtMDUtMjAyNTM6NTcyMiAxAAAAADowNAA=", "priceVersion": "SH-PRICEVERSION_MTUzMjYzXzMzXzFfMjk5XzI5OV8yOTlfMzU5LjAwXzI4OS4wXzM0OS4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzAuMDBfMC4wMF80OTg1NDg5N18zNTEwfDM4MTB8MzY3OXwzNzQ2XzEwMDE6Mjk5fDEwMDM6MjAuMDB8MTAwMjo0MC4wMF8xXzM2Nzk=", "pCityId": 34, "rCityId": 34, "vendorVehicleCode": "49854897", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车上门", "rStoreNav": "免费上门取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 359, "isrec": false, "recommendOrder": 0, "mergeId": 1320, "rectype": 1, "cvid": 33, "rentalamount": 289, "totalDailyPrice": 289, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "49854897", "storeId": "153263"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 49854897, "klbPId": 0, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 96490, "rLevel": 96490, "promtId": 640365579, "rCoup": 0, "sortInfo": {"p": "1", "s": "4.37", "c": "34", "v": "153263"}, "newEnergy": 0, "platform": 10, "kPSId": 153263, "kRSId": 153263, "kVId": 15000571, "pLev": 96490, "rLev": 96490, "klbVersion": 1, "kVehicleId": 33, "adjustRuleId": "", "packageLevel": "PRE", "productId": "31364195622863196", "platformCal": true, "originPsType": 1}, "marketingTags": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 289, "amountStr": "¥289", "subAmount": 289, "subAmountStr": "日均¥289", "originalDailyPrice": 299, "detail": [{"code": "1001", "name": "租车费", "amount": 299, "amountDesc": "¥299"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "name": "基础服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}, {"code": "2011", "name": "尊享服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 449, "amountStr": "¥449", "subAmount": 459, "subAmountStr": "¥459", "currencyCode": "¥"}], "packageLevel": "PRE"}], "vendorName": "昆明纵游租车"}]}