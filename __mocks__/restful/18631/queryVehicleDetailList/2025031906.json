{"promotMap": {}, "extras": {"packageLevelAB": "", "goodsShelvesTwoSwitch": "1", "isLicensePlateHideShow": "0", "goodsShelvesTwoABVersion": "C", "packageLevelSwitch": "0", "abVersion": "230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,250325_DSJT_huojia20|C", "serverRequestId": "h5743A5538TpybnaUy74", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "shareVehicleInfo": {"doorNo": 5, "displacement": "2.0L", "style": "", "license": "", "minCurrentDailyPrice": 0, "vehicleImage": "https://dimg04.c-ctrip.com/images/0RV3j12000c56ai0v751E.png?mark=yiche", "groupName": "丰田锋兰达", "transmissionType": 1, "passengerNo": 5, "vehicleName": "丰田锋兰达", "transmissionName": "自动挡"}, "baseResponse": {"code": "200", "requestId": "a363234a-f48f-40df-9dc7-68249a7581e1", "cost": 61, "isSuccess": true, "returnMsg": "OK"}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "productGroupCodeUesd": "6", "resBodySize": 54554, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "recommendVehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_5068_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife/原厂互联/映射", "displacement": "1.4T", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "3921940:0;6913747:0;77088796:0;69176339:0;44239364:0;30672993:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV2612000c566bju4BD9.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "捷达VS5", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=2612&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "3921940:1;6913747:1;77088796:1;69176339:0;44239364:1;30672993:1;"}, "carPhone": true, "shortEndurance": "km", "vehicleCode": "5068", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "捷达VS5", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2612000c566bju4BD9.png?mark=yiche", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "3921940:1;6913747:1;77088796:0;69176339:0;44239364:1;30672993:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0n12000c5qlxglBCED.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6y12000c5qm4dp5DE8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1j12000c5qlxgn5B53.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3m12000c5qm56t739A.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4712000c5qm4c41DF5.jpg?mark=yiche"], "transmissionType": 1, "brandName": "捷达", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "捷达", "licenseStyle": "2", "vehiclesSetId": "10", "guidSys": "支持自适应巡航/定速巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_88194_", "luggageNo": 2, "carPlay": "支持原厂互联/映射/HUAWEIHiCar/CarPlay", "displacement": "1.5T", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "60947125:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV6z12000f1mdmvx1142.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "哈弗H6", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "60947125:1;"}, "shortEndurance": "km", "carPhone": true, "vehicleCode": "88194", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "哈弗H6", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1z12000esaq9itF0AD.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "60947125:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV2s12000esaq33rD0CA.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4a12000esaq3o5C1B2.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0r12000esapwv74267.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3412000esapvp0752E.png?mark=yiche"], "transmissionType": 1, "brandName": "哈弗", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "哈弗", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "支持全速自适应巡航/定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_89132_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife/HUAWEIHiCar", "displacement": "1.5L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "62430131:0;62934502:0;62829313:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV4c12000fq38j400A14.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "大众途岳", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "62430131:1;62934502:1;62829313:1;"}, "shortEndurance": "km", "carPhone": true, "vehicleCode": "89132", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "大众途岳", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2m12000fq3675813C6.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "62430131:0;62934502:1;62829313:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6m12000fq39o59C246.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4412000fq39cqnF6EB.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0h12000fq384flE108.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1o12000fq35ir8A244.png?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "支持全速自适应巡航/定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5698_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/HUAWEIHiCar", "displacement": "2.0L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "4953760:0;6913655:0;30683703:0;5020285:0;5033551:0;7659891:0;5031182:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV4y12000c7kn1r6D5E1.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "丰田卡罗拉锐放", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6549&app_ver=10.5", "reverseImage": {"type": 1, "typeDesc": "支持", "description": "4953760:1;6913655:1;30683703:1;5020285:1;5033551:1;7659891:1;5031182:1;"}, "carPhone": true, "shortEndurance": "km", "vehicleCode": "5698", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田卡罗拉锐放", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2a12000ap2ohun5416.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "4953760:1;6913655:0;30683703:0;5020285:1;5033551:1;7659891:0;5031182:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6w12000cdv04ui3F96.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0x12000cdv07avF35D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0o12000cduzxfgD5C1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3s12000cdv02utCA52.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4112000cdv07ycC23C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}], "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "vehicleInfo": {"skylight": "部分车辆支持", "vehicleKey": "0_5696_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/HUAWEIHiCar", "displacement": "2.0L", "autoPark": false, "charge": "", "fuelType": "汽油", "snowTyre": {"type": 0, "typeDesc": "不支持"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV3j12000c56ai0v751E.png?mark=yiche"], "license": "", "isSpecialized": true, "multimediaAlbums": [{"albumName": "官方相册", "albumType": 1, "mediaGroup": [{"groupType": 3, "groupName": "VR", "groupSortNum": 3, "medias": [{"sortNum": 0, "type": 3, "url": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6581&app_ver=10.5", "cover": "https://dimg04.c-ctrip.com/images/0RV3j12000c56ai0v751E.png?mark=yiche"}]}, {"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3t12000cf26cyg4561.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6j12000cf26dgqF872.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4912000cf26dz405C9.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4z12000cf267zf547C.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6w12000cf26iq2B83A.jpg?mark=yiche", "cover": ""}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1s12000cf2620aB699.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1r12000cf26gn52E4E.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4f12000cf26ce32835.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3912000cf267jt9692.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3k12000cf2620e4A36.jpg?mark=yiche", "cover": ""}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3112000cf26dheA673.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0612000cf26kqy9CDC.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0e12000cf266zmF508.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2c12000cf26brr6BE2.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6812000cf26cem332B.jpg?mark=yiche", "cover": ""}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}], "note": "年款/颜色等以门店为准"}], "groupCode": "6", "zhName": "丰田锋兰达", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6581&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhone": true, "shortEndurance": "km", "vehicleCode": "5696", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田锋兰达", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3j12000c56ai0v751E.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1s12000cf2620aB699.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3112000cf26dheA673.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1r12000cf26gn52E4E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0612000cf26kqy9CDC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4f12000cf26ce32835.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "tachograph": {"type": 2, "typeDesc": "部分车辆支持"}, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "部分车辆支持全速自适应巡航", "transmissionName": "自动挡"}, "isFromSearch": false, "timeInterval": 115.************, "recommendProducts": [{"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 296, "amountDesc": "¥296", "name": "租车费"}, {"code": "11037", "amount": 296, "amountDesc": "¥296", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 148, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 80, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥376", "subAmount": 376, "name": "总价", "amountStr": "¥80"}], "reference": {"vehicleCode": "0", "rStoreCode": "136210", "packageId": "", "pLev": 77082, "comPriceCode": "[c]Mzk0MjMxMzAwMTA2MjI3MTAwfDI2fDAuMDUtMjAyNS06MDA6OSAwMDgmJjEwMCY2ZSY2NSZ0cnUyNS0wJiQyMCAwMDo1LTMwMCYyMjAwOjAmZmFsOCYmMTI4JiRzZSYyMSYyJnwxMDAyOTYkMTQ4JiYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYyJjMwMCR8MjYwLjAwNS0yMDI1LTozMDo5IDA5MDI1LTAwJjIxIDA5MDUtMzAwfDI6MzA6MDQtMTAyNS06MzM6NyAxNgAAAAAxNQAA", "bizVendorCode": "SD3917", "pStoreCode": "136210", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTM2MjEwXzUwNjhfMV8xNDhfMjk2XzY4XzM3Ni4wMF8wXzgwLjBfMF8wXzAuMF8wLjBfNjAuMDBfMjAuMDBfMC4wMF8wLjAwXzM5MjE5NDA=", "sendTypeForPickUpCar": 0, "skuId": 3921940, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 77082, "vendorCode": "33731", "vendorVehicleCode": "3921940"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6913747, "bizVendorCode": "SD4178"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 77088796, "bizVendorCode": "SD14518"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 69176339, "bizVendorCode": "SD13611"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44239364, "bizVendorCode": "SD3194"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 30672993, "bizVendorCode": "SD3102"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减296", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5068", "highestPrice": 388, "pWay": "可选：免费送车上门", "minDPrice": 0, "vehicleKey": "0_5068_", "hot": 0, "minTPrice": 80, "lowestDistance": 0.1, "group": 0, "type": 0, "sortNum": 2, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3917_0_136210_136210"], "introduce": "当前车型最低价"}, "minDOrinPrice": 148, "isEasy": true, "isCredit": true, "maximumCommentCount": 2543, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "priceSize": 6}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 316, "amountDesc": "¥316", "name": "租车费"}, {"code": "11037", "amount": 316, "amountDesc": "¥316", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 158, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 25, "amountStr": "¥25", "detail": [{"code": "1003", "amount": 25, "amountDesc": "¥25", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 85, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥401", "subAmount": 401, "name": "总价", "amountStr": "¥85"}], "reference": {"vehicleCode": "0", "rStoreCode": "250479", "packageId": "", "pLev": 260980, "bizVendorCode": "SD4178", "pStoreCode": "250479", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjUwNDc5Xzg4MTk0XzFfMTU4XzMxNl8xNThfNDAxLjAwXzBfODUuMF8wXzBfMC4wXzAuMF82MC4wMF8yNS4wMF8wLjAwXzAuMDBfNjA5NDcxMjU=", "sendTypeForPickUpCar": 0, "skuId": 60947125, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 260980, "vendorCode": "15000220", "vendorVehicleCode": "60947125"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减316", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "88194", "highestPrice": 0, "pWay": "可选：免费送车上门", "minDPrice": 0, "vehicleKey": "0_88194_", "hot": 0, "minTPrice": 85, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 3, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4178_0_250479_250479"], "introduce": "当前车型最低价"}, "minDOrinPrice": 158, "isEasy": true, "isCredit": true, "maximumCommentCount": 1348, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "priceSize": 1}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 336, "amountDesc": "¥336", "name": "租车费"}, {"code": "11037", "amount": 336, "amountDesc": "¥336", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 168, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 90, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥426", "subAmount": 426, "name": "总价", "amountStr": "¥90"}], "reference": {"vehicleCode": "0", "rStoreCode": "422682", "packageId": "", "pLev": 1757561, "bizVendorCode": "SD14518", "pStoreCode": "422682", "packageType": 1, "priceVersion": "SH-PRICEVERSION_NDIyNjgyXzg5MTMyXzFfMTY4XzMzNl8xNjhfNDI2LjAwXzBfOTAuMF8wXzBfMC4wXzAuMF82MC4wMF8zMC4wMF8wLjAwXzAuMDBfNjI0MzAxMzE=", "sendTypeForPickUpCar": 0, "skuId": 62430131, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1757561, "vendorCode": "85551", "vendorVehicleCode": "62430131"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 62934502, "bizVendorCode": "SD4178"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 62829313, "bizVendorCode": "SD3102"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减336", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "89132", "highestPrice": 78, "pWay": "可选：免费送车上门", "minDPrice": 0, "vehicleKey": "0_89132_", "hot": 0, "minTPrice": 90, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 4, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD14518_0_422682_422682"], "introduce": "当前车型最低价"}, "minDOrinPrice": 168, "isEasy": true, "isCredit": true, "maximumCommentCount": 1348, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "priceSize": 3}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 356, "amountDesc": "¥356", "name": "租车费"}, {"code": "11037", "amount": 356, "amountDesc": "¥356", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 178, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 80, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥436", "subAmount": 436, "name": "总价", "amountStr": "¥80"}], "reference": {"vehicleCode": "0", "rStoreCode": "136210", "packageId": "", "pLev": 77082, "comPriceCode": "[c]NDAwNjMxMzAxMTUwMzcwNDAwfDI0fDAuMDUtMjAyNS06MDA6OSAwMDA4JiYwMCYxdWUmOTEmdHIwMjUtNyYkMjAgMDAwNS0zMDAmMjowMDoxJmZhNDgmJjI0OCZsc2UmMDEmMiR8MTAmMzU2JjE3ODMmMSYkMTAwMCYyMDIwLjAxMDAyLjAwJDAuMDAmMiYzMDAkfCY2MC4tMDUtMjAyNTk6MzAyOSAwMjAyNTowMCYzMSAwLTA1LTowMHw5OjMwLTA0LTIwMjU2OjMzMTcgMQAAAAA6MTUA", "bizVendorCode": "SD3917", "pStoreCode": "136210", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTM2MjEwXzU2OThfMV8xNzhfMzU2XzEwOF80MzYuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF80OTUzNzYw", "sendTypeForPickUpCar": 0, "skuId": 4953760, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 77082, "vendorCode": "33731", "vendorVehicleCode": "4953760"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6913655, "bizVendorCode": "SD4178"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 30683703, "bizVendorCode": "SD14518"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5020285, "bizVendorCode": "SD3587"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5033551, "bizVendorCode": "SD3104"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 7659891, "bizVendorCode": "SD10708"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5031182, "bizVendorCode": "SD3102"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减356", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5698", "highestPrice": 188, "pWay": "可选：免费送车上门", "minDPrice": 0, "vehicleKey": "0_5698_", "hot": 0, "minTPrice": 80, "lowestDistance": 100.3829, "group": 0, "type": 0, "sortNum": 14, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3917_0_136210_136210"], "introduce": "当前车型最低价"}, "minDOrinPrice": 178, "isEasy": true, "isCredit": true, "maximumCommentCount": 2543, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "priceSize": 7}], "ResponseStatus": {"Extension": [{"Value": "3459783598428553592", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a2d7f5d-484688-1479051", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1744878797141+0800)/"}, "imStatus": 0, "floor": [{"pickWayInfo": "免费送车上门", "floorName": ["2年内车龄"], "vendorName": "云游旅程租车", "packageList": [{"reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "30705235", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 3997, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 476, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD14518", "elct": 0, "pLevel": 1757561, "gsDesc": "低价省钱", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_NDIyNjgyXzU2OTZfMV8xOThfMzk2XzE5OF80NzYuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF8zMDcwNTIzNV8zNTQ3fDM1MDF8MzU2M3wzNzQ2fDM3ODhfMTAwMTozOTZ8MTAwMzoyMC4wMHwxMDAyOjYwLjAw", "alipay": false, "vendorCode": "85551", "productCode": "SD14518_0_422682_422682", "pLev": 1757561, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 422682, "age": 30, "rCoup": 0, "kRSId": 422682, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1757561, "pStoreCode": "422682", "pickUpOnDoor": true, "aType": 0, "productId": "31355931413692555", "kVId": 85551, "sortInfo": {"p": "1", "s": "100.0", "c": "3997", "v": "422682"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减396", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 841370114, "rStoreCode": "422682", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 30705235, "rLevel": 1757561, "newEnergy": 0}, "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 396, "amountDesc": "¥396", "name": "租车费"}, {"code": "11037", "amount": 396, "amountDesc": "¥396", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 198, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 80, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥476", "subAmount": 476, "name": "总价", "amountStr": "¥80"}], "code": "1002", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 198, "currentTotalPrice": 80, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 0, "marginPrice": 0, "oTPrice": 476, "deductInfos": [{"totalAmount": 396, "payofftype": 2}], "currentOriginalDailyPrice": 198, "currentDailyPrice": 0, "naked": true}, "packageLevel": "BAS", "name": "基础保障", "marketingTags": [{"category": 3, "sortNum": 10000, "amountTitle": "已减396", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}]}, {"reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "30705235", "pickWayInfo": 1, "packageLevel": "ADV", "pCityId": 3997, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 476, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD14518", "elct": 0, "pLevel": 1757561, "gsDesc": "低价省钱", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_NDIyNjgyXzU2OTZfMV8xOThfMzk2XzE5OF80NzYuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF8zMDcwNTIzNV8zNTQ3fDM1MDF8MzU2M3wzNzQ2fDM3ODhfMTAwMTozOTZ8MTAwMzoyMC4wMHwxMDAyOjYwLjAw", "alipay": false, "vendorCode": "85551", "productCode": "SD14518_0_422682_422682", "pLev": 1757561, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 422682, "age": 30, "rCoup": 0, "kRSId": 422682, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1757561, "pStoreCode": "422682", "pickUpOnDoor": true, "aType": 0, "productId": "31355931413692555", "kVId": 85551, "sortInfo": {"p": "1", "s": "100.0", "c": "3997", "v": "422682"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减396", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 841370114, "rStoreCode": "422682", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 30705235, "rLevel": 1757561, "newEnergy": 0}, "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 396, "amountDesc": "¥396", "name": "租车费"}, {"code": "11037", "amount": 396, "amountDesc": "¥396", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 198, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2001", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "优享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 160, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥556", "subAmount": 556, "name": "总价", "amountStr": "¥160"}], "code": "2001", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 198, "currentTotalPrice": 160, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 40, "marginPrice": 0, "oTPrice": 556, "deductInfos": [{"totalAmount": 396, "payofftype": 2}], "currentOriginalDailyPrice": 198, "currentDailyPrice": 0, "naked": true}, "packageLevel": "ADV", "name": "优享保障", "marketingTags": [{"category": 3, "sortNum": 10000, "amountTitle": "已减396", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}]}, {"reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "30705235", "pickWayInfo": 1, "packageLevel": "PRE", "pCityId": 3997, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 476, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD14518", "elct": 0, "pLevel": 1757561, "gsDesc": "低价省钱", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_NDIyNjgyXzU2OTZfMV8xOThfMzk2XzE5OF80NzYuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF8zMDcwNTIzNV8zNTQ3fDM1MDF8MzU2M3wzNzQ2fDM3ODhfMTAwMTozOTZ8MTAwMzoyMC4wMHwxMDAyOjYwLjAw", "alipay": false, "vendorCode": "85551", "productCode": "SD14518_0_422682_422682", "pLev": 1757561, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 422682, "age": 30, "rCoup": 0, "kRSId": 422682, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1757561, "pStoreCode": "422682", "pickUpOnDoor": true, "aType": 0, "productId": "31355931413692555", "kVId": 85551, "sortInfo": {"p": "1", "s": "100.0", "c": "3997", "v": "422682"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减396", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 841370114, "rStoreCode": "422682", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 30705235, "rLevel": 1757561, "newEnergy": 0}, "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 396, "amountDesc": "¥396", "name": "租车费"}, {"code": "11037", "amount": 396, "amountDesc": "¥396", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 198, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 220, "amountStr": "¥220", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2011", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "尊享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 240, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥636", "subAmount": 636, "name": "总价", "amountStr": "¥240"}], "code": "2011", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"curOriginDPrice": 198, "currentTotalPrice": 240, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 80, "marginPrice": 0, "oTPrice": 636, "deductInfos": [{"totalAmount": 396, "payofftype": 2}], "currentOriginalDailyPrice": 198, "currentDailyPrice": 0, "naked": true}, "packageLevel": "PRE", "name": "尊享保障", "marketingTags": [{"category": 3, "sortNum": 10000, "amountTitle": "已减396", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}]}], "lowestPrice": 1, "isSelect": true, "floorId": "0_2_1", "alltags": [{"category": 1, "sortNum": 25, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}]}, {"pickWayInfo": "免费送车上门", "floorName": ["新款", "1年内车龄"], "vendorName": "鑫豪出行", "packageList": [{"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "58525545", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 3997, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 271, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 88, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 176}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD13611", "elct": 0, "pLevel": 1443681, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_ODMzMjg4XzU2OTZfMV84OF8xNzZfODhfMjcxLjAwXzg4XzI3MS4wXzBfMF8wLjBfMC4wXzYwLjAwXzM1LjAwXzAuMDBfMC4wMF81ODUyNTU0NV8zNzg5fDM1MTB8MzY3OXwzNzQ2XzEwMDE6MTc2fDEwMDM6MzUuMDB8MTAwMjo2MC4wMA==", "alipay": false, "vendorCode": "15006385", "productCode": "SD13611_0_833288_833288", "pLev": 1443681, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 833288, "age": 30, "rCoup": 0, "kRSId": 833288, "platformCal": true, "freeIllegalDeposit": false, "rLev": 1443681, "pStoreCode": "833288", "pickUpOnDoor": true, "aType": 0, "productId": "31506819257306370", "kVId": 15006385, "sortInfo": {"p": "1", "s": "9.81", "c": "3997", "v": "833288"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "833288", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 58525545, "rLevel": 1443681, "newEnergy": 0}, "fees": [{"amount": 176, "detail": [{"code": "1001", "amount": 176, "amountDesc": "¥176", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥88", "subAmount": 88, "name": "车辆租金", "amountStr": "¥176"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 271, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥271", "subAmount": 271, "name": "总价", "amountStr": "¥271"}], "code": "1002", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 88, "currentTotalPrice": 271, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 0, "marginPrice": 0, "oTPrice": 271, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 88, "naked": true}, "packageLevel": "BAS", "name": "基础保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "58525545", "pickWayInfo": 1, "packageLevel": "ADV", "pCityId": 3997, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 271, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 88, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 176}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD13611", "elct": 0, "pLevel": 1443681, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_ODMzMjg4XzU2OTZfMV84OF8xNzZfODhfMjcxLjAwXzg4XzI3MS4wXzBfMF8wLjBfMC4wXzYwLjAwXzM1LjAwXzAuMDBfMC4wMF81ODUyNTU0NV8zNzg5fDM1MTB8MzY3OXwzNzQ2XzEwMDE6MTc2fDEwMDM6MzUuMDB8MTAwMjo2MC4wMA==", "alipay": false, "vendorCode": "15006385", "productCode": "SD13611_0_833288_833288", "pLev": 1443681, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 833288, "age": 30, "rCoup": 0, "kRSId": 833288, "platformCal": true, "freeIllegalDeposit": false, "rLev": 1443681, "pStoreCode": "833288", "pickUpOnDoor": true, "aType": 0, "productId": "31506819257306370", "kVId": 15006385, "sortInfo": {"p": "1", "s": "9.81", "c": "3997", "v": "833288"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "833288", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 58525545, "rLevel": 1443681, "newEnergy": 0}, "fees": [{"amount": 176, "detail": [{"code": "1001", "amount": 176, "amountDesc": "¥176", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥88", "subAmount": 88, "name": "车辆租金", "amountStr": "¥176"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2001", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "优享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 351, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥351", "subAmount": 351, "name": "总价", "amountStr": "¥351"}], "code": "2001", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 88, "currentTotalPrice": 351, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 40, "marginPrice": 0, "oTPrice": 351, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 88, "naked": true}, "packageLevel": "ADV", "name": "优享保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "58525545", "pickWayInfo": 1, "packageLevel": "PRE", "pCityId": 3997, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 271, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 88, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 176}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD13611", "elct": 0, "pLevel": 1443681, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_ODMzMjg4XzU2OTZfMV84OF8xNzZfODhfMjcxLjAwXzg4XzI3MS4wXzBfMF8wLjBfMC4wXzYwLjAwXzM1LjAwXzAuMDBfMC4wMF81ODUyNTU0NV8zNzg5fDM1MTB8MzY3OXwzNzQ2XzEwMDE6MTc2fDEwMDM6MzUuMDB8MTAwMjo2MC4wMA==", "alipay": false, "vendorCode": "15006385", "productCode": "SD13611_0_833288_833288", "pLev": 1443681, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 833288, "age": 30, "rCoup": 0, "kRSId": 833288, "platformCal": true, "freeIllegalDeposit": false, "rLev": 1443681, "pStoreCode": "833288", "pickUpOnDoor": true, "aType": 0, "productId": "31506819257306370", "kVId": 15006385, "sortInfo": {"p": "1", "s": "9.81", "c": "3997", "v": "833288"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "833288", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 58525545, "rLevel": 1443681, "newEnergy": 0}, "fees": [{"amount": 176, "detail": [{"code": "1001", "amount": 176, "amountDesc": "¥176", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥88", "subAmount": 88, "name": "车辆租金", "amountStr": "¥176"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 220, "amountStr": "¥220", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2011", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "尊享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 431, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥431", "subAmount": 431, "name": "总价", "amountStr": "¥431"}], "code": "2011", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"curOriginDPrice": 88, "currentTotalPrice": 431, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 80, "marginPrice": 0, "oTPrice": 431, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 88, "naked": true}, "packageLevel": "PRE", "name": "尊享保障", "marketingTags": []}], "isSelect": false, "floorId": "1_1_1", "alltags": [{"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}]}, {"pickWayInfo": "免费送车上门", "floorName": ["新款", "1年内车龄"], "vendorName": "云南龙麟租车", "packageList": [{"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "77416739", "pickWayInfo": 1, "stockLevel": "A", "pCityId": 3997, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 626, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 273, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 546}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3102", "elct": 0, "pLevel": 1661094, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_MTgzNjAxMF81Njk2XzFfMjczXzU0Nl84OF82MjYuMDBfMjczXzYyNi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF83NzQxNjczOV8zNzg5fDM1MTB8MzUwMXwzNTYzfDM3NDZ8Mzc4OF8xMDAxOjU0NnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "15000063", "productCode": "SD3102_0_1836010_1836010", "pLev": 1661094, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1836010, "age": 30, "rCoup": 0, "kRSId": 1836010, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1661094, "pStoreCode": "1836010", "pickUpOnDoor": true, "aType": 0, "productId": "31357016206576081", "kVId": 15000063, "sortInfo": {"p": "1", "s": "1.99", "c": "3997", "v": "1836010"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1836010", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 77416739, "rLevel": 1661094, "newEnergy": 0}, "fees": [{"amount": 546, "detail": [{"code": "1001", "amount": 546, "amountDesc": "¥546", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥273", "subAmount": 273, "name": "车辆租金", "amountStr": "¥546"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 626, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥626", "subAmount": 626, "name": "总价", "amountStr": "¥626"}], "code": "1002", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 273, "currentTotalPrice": 626, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 0, "marginPrice": 0, "oTPrice": 626, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 273, "naked": true}, "packageLevel": "BAS", "name": "基础保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "77416739", "pickWayInfo": 1, "stockLevel": "A", "pCityId": 3997, "packageLevel": "ADV", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 626, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 273, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 546}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3102", "elct": 0, "pLevel": 1661094, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_MTgzNjAxMF81Njk2XzFfMjczXzU0Nl84OF82MjYuMDBfMjczXzYyNi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF83NzQxNjczOV8zNzg5fDM1MTB8MzUwMXwzNTYzfDM3NDZ8Mzc4OF8xMDAxOjU0NnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "15000063", "productCode": "SD3102_0_1836010_1836010", "pLev": 1661094, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1836010, "age": 30, "rCoup": 0, "kRSId": 1836010, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1661094, "pStoreCode": "1836010", "pickUpOnDoor": true, "aType": 0, "productId": "31357016206576081", "kVId": 15000063, "sortInfo": {"p": "1", "s": "1.99", "c": "3997", "v": "1836010"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1836010", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 77416739, "rLevel": 1661094, "newEnergy": 0}, "fees": [{"amount": 546, "detail": [{"code": "1001", "amount": 546, "amountDesc": "¥546", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥273", "subAmount": 273, "name": "车辆租金", "amountStr": "¥546"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2001", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "优享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 706, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥706", "subAmount": 706, "name": "总价", "amountStr": "¥706"}], "code": "2001", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 273, "currentTotalPrice": 706, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 40, "marginPrice": 0, "oTPrice": 706, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 273, "naked": true}, "packageLevel": "ADV", "name": "优享保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "77416739", "pickWayInfo": 1, "stockLevel": "A", "pCityId": 3997, "packageLevel": "PRE", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 626, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 273, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 546}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3102", "elct": 0, "pLevel": 1661094, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_MTgzNjAxMF81Njk2XzFfMjczXzU0Nl84OF82MjYuMDBfMjczXzYyNi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF83NzQxNjczOV8zNzg5fDM1MTB8MzUwMXwzNTYzfDM3NDZ8Mzc4OF8xMDAxOjU0NnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "15000063", "productCode": "SD3102_0_1836010_1836010", "pLev": 1661094, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1836010, "age": 30, "rCoup": 0, "kRSId": 1836010, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1661094, "pStoreCode": "1836010", "pickUpOnDoor": true, "aType": 0, "productId": "31357016206576081", "kVId": 15000063, "sortInfo": {"p": "1", "s": "1.99", "c": "3997", "v": "1836010"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1836010", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 77416739, "rLevel": 1661094, "newEnergy": 0}, "fees": [{"amount": 546, "detail": [{"code": "1001", "amount": 546, "amountDesc": "¥546", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥273", "subAmount": 273, "name": "车辆租金", "amountStr": "¥546"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 220, "amountStr": "¥220", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2011", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "尊享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 786, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥786", "subAmount": 786, "name": "总价", "amountStr": "¥786"}], "code": "2011", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"curOriginDPrice": 273, "currentTotalPrice": 786, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 80, "marginPrice": 0, "oTPrice": 786, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 273, "naked": true}, "packageLevel": "PRE", "name": "尊享保障", "marketingTags": []}], "isSelect": false, "floorId": "0_1_1", "alltags": [{"category": 1, "sortNum": 25, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}]}, {"pickWayInfo": "免费送车上门", "floorName": ["新款", "1年内车龄"], "vendorName": "云南龙麟租车", "packageList": [{"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "77416739", "pickWayInfo": 1, "stockLevel": "A", "pCityId": 3997, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 626, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 273, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 546}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3102", "elct": 0, "pLevel": 1661094, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_MTgzNjAxMF81Njk2XzFfMjczXzU0Nl84OF82MjYuMDBfMjczXzYyNi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF83NzQxNjczOV8zNzg5fDM1MTB8MzUwMXwzNTYzfDM3NDZ8Mzc4OF8xMDAxOjU0NnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "15000063", "productCode": "SD3102_0_1836010_1836010", "pLev": 1661094, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1836010, "age": 30, "rCoup": 0, "kRSId": 1836010, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1661094, "pStoreCode": "1836010", "pickUpOnDoor": true, "aType": 0, "productId": "31357016206576081", "kVId": 15000063, "sortInfo": {"p": "1", "s": "1.99", "c": "3997", "v": "1836010"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1836010", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 77416739, "rLevel": 1661094, "newEnergy": 0}, "fees": [{"amount": 546, "detail": [{"code": "1001", "amount": 546, "amountDesc": "¥546", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥273", "subAmount": 273, "name": "车辆租金", "amountStr": "¥546"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 626, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥626", "subAmount": 626, "name": "总价", "amountStr": "¥626"}], "code": "1002", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 273, "currentTotalPrice": 626, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 0, "marginPrice": 0, "oTPrice": 626, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 273, "naked": true}, "packageLevel": "BAS", "name": "基础保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "77416739", "pickWayInfo": 1, "stockLevel": "A", "pCityId": 3997, "packageLevel": "ADV", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 626, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 273, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 546}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3102", "elct": 0, "pLevel": 1661094, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_MTgzNjAxMF81Njk2XzFfMjczXzU0Nl84OF82MjYuMDBfMjczXzYyNi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF83NzQxNjczOV8zNzg5fDM1MTB8MzUwMXwzNTYzfDM3NDZ8Mzc4OF8xMDAxOjU0NnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "15000063", "productCode": "SD3102_0_1836010_1836010", "pLev": 1661094, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1836010, "age": 30, "rCoup": 0, "kRSId": 1836010, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1661094, "pStoreCode": "1836010", "pickUpOnDoor": true, "aType": 0, "productId": "31357016206576081", "kVId": 15000063, "sortInfo": {"p": "1", "s": "1.99", "c": "3997", "v": "1836010"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1836010", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 77416739, "rLevel": 1661094, "newEnergy": 0}, "fees": [{"amount": 546, "detail": [{"code": "1001", "amount": 546, "amountDesc": "¥546", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥273", "subAmount": 273, "name": "车辆租金", "amountStr": "¥546"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2001", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "优享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 706, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥706", "subAmount": 706, "name": "总价", "amountStr": "¥706"}], "code": "2001", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 273, "currentTotalPrice": 706, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 40, "marginPrice": 0, "oTPrice": 706, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 273, "naked": true}, "packageLevel": "ADV", "name": "优享保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "77416739", "pickWayInfo": 1, "stockLevel": "A", "pCityId": 3997, "packageLevel": "PRE", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 626, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 273, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 546}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3102", "elct": 0, "pLevel": 1661094, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_MTgzNjAxMF81Njk2XzFfMjczXzU0Nl84OF82MjYuMDBfMjczXzYyNi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF83NzQxNjczOV8zNzg5fDM1MTB8MzUwMXwzNTYzfDM3NDZ8Mzc4OF8xMDAxOjU0NnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "15000063", "productCode": "SD3102_0_1836010_1836010", "pLev": 1661094, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1836010, "age": 30, "rCoup": 0, "kRSId": 1836010, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1661094, "pStoreCode": "1836010", "pickUpOnDoor": true, "aType": 0, "productId": "31357016206576081", "kVId": 15000063, "sortInfo": {"p": "1", "s": "1.99", "c": "3997", "v": "1836010"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1836010", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 77416739, "rLevel": 1661094, "newEnergy": 0}, "fees": [{"amount": 546, "detail": [{"code": "1001", "amount": 546, "amountDesc": "¥546", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥273", "subAmount": 273, "name": "车辆租金", "amountStr": "¥546"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 220, "amountStr": "¥220", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2011", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "尊享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 786, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥786", "subAmount": 786, "name": "总价", "amountStr": "¥786"}], "code": "2011", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"curOriginDPrice": 273, "currentTotalPrice": 786, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 80, "marginPrice": 0, "oTPrice": 786, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 273, "naked": true}, "packageLevel": "PRE", "name": "尊享保障", "marketingTags": []}], "isSelect": false, "floorId": "3_1_1", "alltags": [{"category": 1, "sortNum": 25, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}]}, {"pickWayInfo": "免费送车上门", "floorName": ["新款", "1年内车龄"], "vendorName": "云南龙麟租车", "packageList": [{"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "77416739", "pickWayInfo": 1, "stockLevel": "A", "pCityId": 3997, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 626, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 273, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 546}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3102", "elct": 0, "pLevel": 1661094, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_MTgzNjAxMF81Njk2XzFfMjczXzU0Nl84OF82MjYuMDBfMjczXzYyNi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF83NzQxNjczOV8zNzg5fDM1MTB8MzUwMXwzNTYzfDM3NDZ8Mzc4OF8xMDAxOjU0NnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "15000063", "productCode": "SD3102_0_1836010_1836010", "pLev": 1661094, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1836010, "age": 30, "rCoup": 0, "kRSId": 1836010, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1661094, "pStoreCode": "1836010", "pickUpOnDoor": true, "aType": 0, "productId": "31357016206576081", "kVId": 15000063, "sortInfo": {"p": "1", "s": "1.99", "c": "3997", "v": "1836010"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1836010", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 77416739, "rLevel": 1661094, "newEnergy": 0}, "fees": [{"amount": 546, "detail": [{"code": "1001", "amount": 546, "amountDesc": "¥546", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥273", "subAmount": 273, "name": "车辆租金", "amountStr": "¥546"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 626, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥626", "subAmount": 626, "name": "总价", "amountStr": "¥626"}], "code": "1002", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 273, "currentTotalPrice": 626, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 0, "marginPrice": 0, "oTPrice": 626, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 273, "naked": true}, "packageLevel": "BAS", "name": "基础保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "77416739", "pickWayInfo": 1, "stockLevel": "A", "pCityId": 3997, "packageLevel": "ADV", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 626, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 273, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 546}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3102", "elct": 0, "pLevel": 1661094, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_MTgzNjAxMF81Njk2XzFfMjczXzU0Nl84OF82MjYuMDBfMjczXzYyNi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF83NzQxNjczOV8zNzg5fDM1MTB8MzUwMXwzNTYzfDM3NDZ8Mzc4OF8xMDAxOjU0NnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "15000063", "productCode": "SD3102_0_1836010_1836010", "pLev": 1661094, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1836010, "age": 30, "rCoup": 0, "kRSId": 1836010, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1661094, "pStoreCode": "1836010", "pickUpOnDoor": true, "aType": 0, "productId": "31357016206576081", "kVId": 15000063, "sortInfo": {"p": "1", "s": "1.99", "c": "3997", "v": "1836010"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1836010", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 77416739, "rLevel": 1661094, "newEnergy": 0}, "fees": [{"amount": 546, "detail": [{"code": "1001", "amount": 546, "amountDesc": "¥546", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥273", "subAmount": 273, "name": "车辆租金", "amountStr": "¥546"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2001", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "优享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 706, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥706", "subAmount": 706, "name": "总价", "amountStr": "¥706"}], "code": "2001", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 273, "currentTotalPrice": 706, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 40, "marginPrice": 0, "oTPrice": 706, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 273, "naked": true}, "packageLevel": "ADV", "name": "优享保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 3997, "rCType": 2, "vendorVehicleCode": "77416739", "pickWayInfo": 1, "stockLevel": "A", "pCityId": 3997, "packageLevel": "PRE", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车上门", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 626, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 273, "grantedcode": "", "isrec": false, "cvid": 5696, "rentalamount": 546}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3102", "elct": 0, "pLevel": 1661094, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "免费上门取车", "priceVersion": "SH-PRICEVERSION_MTgzNjAxMF81Njk2XzFfMjczXzU0Nl84OF82MjYuMDBfMjczXzYyNi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF83NzQxNjczOV8zNzg5fDM1MTB8MzUwMXwzNTYzfDM3NDZ8Mzc4OF8xMDAxOjU0NnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "15000063", "productCode": "SD3102_0_1836010_1836010", "pLev": 1661094, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1836010, "age": 30, "rCoup": 0, "kRSId": 1836010, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1661094, "pStoreCode": "1836010", "pickUpOnDoor": true, "aType": 0, "productId": "31357016206576081", "kVId": 15000063, "sortInfo": {"p": "1", "s": "1.99", "c": "3997", "v": "1836010"}, "kVehicleId": 5696, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1836010", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 77416739, "rLevel": 1661094, "newEnergy": 0}, "fees": [{"amount": 546, "detail": [{"code": "1001", "amount": 546, "amountDesc": "¥546", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥273", "subAmount": 273, "name": "车辆租金", "amountStr": "¥546"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 220, "amountStr": "¥220", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2011", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "尊享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 786, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥786", "subAmount": 786, "name": "总价", "amountStr": "¥786"}], "code": "2011", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"curOriginDPrice": 273, "currentTotalPrice": 786, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 80, "marginPrice": 0, "oTPrice": 786, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 273, "naked": true}, "packageLevel": "PRE", "name": "尊享保障", "marketingTags": []}], "isSelect": false, "floorId": "4_1_1", "alltags": [{"category": 1, "sortNum": 25, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}]}], "checkResponseTime": 1744878796970.596, "checkRequestTime": 1744878796855.229, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 315, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 315, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1744878796854, "afterFetch": 1744878797169}}