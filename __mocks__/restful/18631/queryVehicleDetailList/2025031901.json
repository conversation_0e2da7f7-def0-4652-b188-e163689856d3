{"includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "productGroupCodeUesd": "2", "shareVehicleInfo": {"doorNo": 4, "displacement": "1.4T-1.6L", "style": "", "license": "", "minCurrentDailyPrice": 60, "vehicleImage": "https://dimg04.c-ctrip.com/images/0RV0a12000c7kmcp84670.png?mark=yiche", "groupName": "大众朗逸", "transmissionType": 1, "passengerNo": 5, "vehicleName": "大众朗逸", "transmissionName": "自动挡"}, "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "recommendVehicleList": [], "resBodySize": 377743, "promotMap": {}, "vehicleInfo": {"skylight": "部分车辆支持", "vehicleKey": "0_1069_", "luggageNo": 5, "carPlay": "不支持", "displacement": "1.4T-1.6L", "autoPark": false, "charge": "", "fuelType": "汽油", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0a12000c7kmcp84670.png?mark=yiche"], "license": "", "isSpecialized": true, "multimediaAlbums": [{"albumName": "官方相册", "albumType": 1, "mediaGroup": [{"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0m12000chlv9o2C40D.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4l12000chlv9fb01BB.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3m12000chlvgqu7170.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6f12000chlv46p6A8E.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5n12000chlvgjfD1CA.jpg?mark=yiche", "cover": ""}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0312000chlvgr2CE91.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1212000chlv9fkFF40.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2612000chlvh5q2C37.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3212000chlvh5s16E8.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1t12000chlvgy9C2EF.jpg?mark=yiche", "cover": ""}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1u12000chlvgyp97DC.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5i12000chlv9ft6840.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1n12000chlvg3h6E9F.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6y12000chlvia8818B.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0r12000chlvg3f3916.jpg?mark=yiche", "cover": ""}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}], "note": "年款/颜色等以门店为准"}], "groupCode": "2", "zhName": "大众朗逸", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD", "mediaTypes": [], "vehicleCode": "1069", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众朗逸", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0m12000chlv9o2C40D.jpg?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0312000chlvgr2CE91.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1u12000chlvgyp97DC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000chlv9fkFF40.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000chlv9ft6840.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2612000chlvh5q2C37.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "71", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, "floor": [{"floorId": "f1111", "lowestPrice": 0, "floorName": ["1年内车龄"], "isSelect": false, "pickWayInfo": "机场内 携程租车中心取车", "returnWayInfo": "", "alltags": [{"category": 1, "sortNum": 25, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "收折旧停运费"], "priceInfo": {"curOriginDPrice": 458, "currentTotalPrice": 60, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceVersion": "SH-PRICEVERSION_MTA3MzY5XzQ5NDBfMV80NThfNDU4XzQ1OF81MTguMDBfMF82MC4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzBfMF8xODU3Nzg3", "priceType": 1, "marginPrice": 0, "oTPrice": 518, "deductInfos": [{"totalAmount": 458, "payofftype": 2}], "currentOriginalDailyPrice": 458, "currentDailyPrice": 0, "naked": true}, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1857787", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至车站内", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 518, "recommendOrder": 0, "mergeId": 663, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "107369", "vehicleId": "1857787"}], "grantedcode": "", "isrec": false, "cvid": 4940, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD4030", "elct": 0, "pLevel": 25639, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 2714, "rStoreNav": "车站内还车", "priceVersion": "SH-PRICEVERSION_MTA3MzY5XzQ5NDBfMV80NThfNDU4XzQ1OF81MTguMDBfMF82MC4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzBfMF8xODU3Nzg3XzM1NDh8MzgxMHwzNTA0fDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTo0NTh8MTAwMzoyMC4wMHwxMDAyOjQwLjAw", "alipay": false, "vendorCode": "70697", "productCode": "SD4030_0_107369_107369", "pLev": 25639, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 107369, "age": 30, "rCoup": 0, "kRSId": 107369, "freeIllegalDeposit": false, "rLev": 25639, "pStoreCode": "107369", "pickUpOnDoor": true, "aType": 0, "kVId": 70697, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "71503"}, "kVehicleId": 4940, "comPriceCode": "[c]", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减458", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 902694658, "rStoreCode": "107369", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1854602, "rLevel": 25639, "newEnergy": 0}, "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}], "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 458, "amountDesc": "¥458", "name": "租车费"}, {"code": "11037", "amount": 458, "amountDesc": "¥458", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 458, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 60, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥518", "subAmount": 518, "name": "总价", "amountStr": "¥60"}], "priceDailys": [{"date": "string", "oDprice": "string", "priceStr": "string", "showType": 0}], "dPriceDesc": "string"}, {"code": "2001", "name": "优享保障", "insuranceDesc": [], "priceInfo": {"curOriginDPrice": 564, "currentTotalPrice": 60, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzQ5NDBfMV80NjRfNDY0XzQ2NF81MjQuMDBfMF82MC4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzBfMF8xODU0NjAy", "priceType": 1, "marginPrice": 0, "oTPrice": 524, "deductInfos": [{"totalAmount": 464, "payofftype": 2}], "currentOriginalDailyPrice": 464, "currentDailyPrice": 0, "naked": true}, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": -1, "vendorVehicleCode": "1854602", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "门店取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 524, "recommendOrder": 0, "mergeId": 663, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "106878", "vehicleId": "1854602"}], "grantedcode": "", "isrec": false, "cvid": 4940, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3866", "elct": 0, "pLevel": -1, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": -1, "priceType": 1, "isEasyLife": true, "klbPId": 309093, "rStoreNav": "门店还车", "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzQ5NDBfMV80NjRfNDY0XzQ2NF81MjQuMDBfMF82MC4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzBfMF8xODU0NjAyXzM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjQ2NHwxMDAzOjIwLjAwfDEwMDI6NDAuMDA=", "alipay": false, "vendorCode": "30147", "productCode": "SD3866_0_106878_106878", "pLev": -1, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 106878, "age": 30, "rCoup": 0, "kRSId": 106878, "freeIllegalDeposit": false, "rLev": -1, "pStoreCode": "106878", "pickUpOnDoor": false, "aType": 0, "kVId": 30147, "sortInfo": {"p": "16", "s": "100.0", "c": "43", "v": "1937"}, "kVehicleId": 4940, "comPriceCode": "[c]", "dropOffOnDoor": false, "returnWayInfo": 16, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减464", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 902694658, "rStoreCode": "106878", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1854602, "rLevel": -1, "newEnergy": 0}, "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}], "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 464, "amountDesc": "¥464", "name": "租车费"}, {"code": "11037", "amount": 464, "amountDesc": "¥464", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 464, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 60, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥524", "subAmount": 524, "name": "总价", "amountStr": "¥60"}], "priceDailys": [{"date": "string", "oDprice": "string", "priceStr": "string", "showType": 0}], "dPriceDesc": "string"}, {"code": "prep", "name": "无忧保障", "insuranceDesc": ["车损全额免赔", "200万三者保障", "不收折旧停运费", "200万三者保障", "不收折旧停运费"], "priceInfo": {"curOriginDPrice": 1288, "currentTotalPrice": 648, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceVersion": "SH-PRICEVERSION_MTA2MTkwXzQ5NDBfMV8xMjg4XzEyODhfMTI4OF8xMzQ4LjAwXzI4OC4wXzM0OC4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzBfMF8xODE3MzA1", "priceType": 1, "marginPrice": 0, "oTPrice": 1348, "deductInfos": [{"totalAmount": 1000, "payofftype": 2}], "currentOriginalDailyPrice": 1288, "currentDailyPrice": 288, "naked": true}, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1817305", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至车站内", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 1348, "recommendOrder": 0, "mergeId": 663, "vdegree": "0", "rectype": 1, "totalDailyPrice": 288, "mergeInfo": [{"storeId": "106190", "vehicleId": "1817305"}], "grantedcode": "", "isrec": false, "cvid": 4940, "rentalamount": 288}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3225", "elct": 0, "pLevel": 18287, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 472, "rStoreNav": "车站内还车", "priceVersion": "SH-PRICEVERSION_MTA2MTkwXzQ5NDBfMV8xMjg4XzEyODhfMTI4OF8xMzQ4LjAwXzI4OC4wXzM0OC4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzBfMF8xODE3MzA1XzM4MTB8MzUwNHwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MTI4OHwxMDAzOjIwLjAwfDEwMDI6NDAuMDA=", "alipay": false, "vendorCode": "64662", "productCode": "SD3225_0_106190_106190", "pLev": 17724, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 106190, "age": 30, "rCoup": 0, "kRSId": 106190, "freeIllegalDeposit": false, "rLev": 17724, "pStoreCode": "106190", "pickUpOnDoor": false, "aType": 0, "kVId": 64662, "sortInfo": {"p": "1", "s": "4.39", "c": "43", "v": "64549"}, "kVehicleId": 4940, "comPriceCode": "[c]", "dropOffOnDoor": false, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减1000", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 902694658, "rStoreCode": "106190", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1854602, "rLevel": 18287, "newEnergy": 0}, "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}], "fees": [{"amount": 288, "detail": [{"code": "1001", "amount": 1288, "amountDesc": "¥1288", "name": "租车费"}, {"code": "11037", "amount": 1000, "amountDesc": "¥1000", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥288", "originalDailyPrice": 1288, "subAmount": 288, "name": "车辆租金", "amountStr": "¥288"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 348, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥1348", "subAmount": 1348, "name": "总价", "amountStr": "¥348"}], "priceDailys": [{"date": "string", "oDprice": "string", "priceStr": "string", "showType": 0}], "dPriceDesc": "string"}]}, {"floorId": "f2222", "lowestPrice": 1, "floorName": ["新款", "6个月内车龄"], "isSelect": true, "pickWayInfo": "机场内 汇合点取车", "returnWayInfo": "", "alltags": [{"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "行车记录仪", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "收折旧停运费"], "priceInfo": {"curOriginDPrice": 882, "oTPrice": 336, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 78, "currentTotalPrice": 336, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1495_55056_pupai", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "携程租车中心取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 476, "recommendOrder": 0, "mergeId": 627, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "106896", "vehicleId": "1495_55056_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4039, "rentalamount": 0}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3926", "elct": 0, "pLevel": 23665, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 3568, "rStoreNav": "携程租车中心还车", "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzQwMzlfMV8xODhfMzc2XzE4OF80NzYuMDBfMF8xMDAuMF8wXzBfMC4wXzAuMF84MC4wMF8yMC4wMF8wXzBfNjg4NDU5NzRfMzY3OXwzNzQ2fDM3NzlfMTAwMTozNzZ8MTAwMzoyMC4wMHwxMDAyOjgwLjAw", "alipay": false, "vendorCode": "37573", "productCode": "SD3926_0_106896_106896", "pLev": 100619, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 2, "packageType": 0, "kPSId": 106896, "age": 30, "rCoup": 0, "kRSId": 106896, "freeIllegalDeposit": false, "rLev": 100619, "pStoreCode": "106896", "pickUpOnDoor": true, "aType": 0, "kVId": 37573, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "kVehicleId": 4039, "comPriceCode": "[c]NTk3NDY4ODQyNS0wfHwyMCAwMDo0LTAyMCYxODAwOjAmZmFsOCYmMTg4JiRzZSYxLTA0LTIwMjUwOjAwMDMgMDE4OCY6MDAmYWxzZSYxJmYmJHwxJjE4ODImMTgwMDEmNiQxMDgmMzcmMjAuMDMmMTAuMDAwMCYyMiYyJiQxMDAwJjgwNDAuMHwyMDIuMDAkLTAyIDUtMDQwOjAwMTU6MzUtMDQmMjAyMTU6My0wNCB8MjAyMDowMC0yNSA1LTAzMjowMzEzOjQ=", "dropOffOnDoor": true, "returnWayInfo": 16, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减376", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 2, "klb": 1, "promtId": 902694658, "rStoreCode": "106896", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 68845974, "rLevel": 23665, "newEnergy": 0}, "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}], "fees": [{"amount": 118, "detail": [{"code": "1001", "amount": 135, "amountDesc": "¥135", "name": "租车费"}, {"code": "3743", "amount": 17, "amountDesc": "¥17", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥60", "originalDailyPrice": 68, "subAmount": 60, "name": "车辆租金", "amountStr": "¥118"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 198, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥215", "subAmount": 215, "name": "总价", "amountStr": "¥198"}], "priceDailys": [{"date": "string", "oDprice": "string", "priceStr": "string", "showType": 0}], "dPriceDesc": "string"}, {"code": "2001", "name": "优享保障", "insuranceDesc": ["车损全额免赔", "100万三者保障", "收折旧停运费"], "priceInfo": {"curOriginDPrice": 88, "currentTotalPrice": 55, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzYzMzNfMV84OF84OF84OF8xNDMuMDBfMF81NS4wXzBfMF8wLjBfMC4wXzM1LjAwXzIwLjAwXzBfMF82MzUwNTcyMw==", "priceType": 1, "marginPrice": 0, "oTPrice": 143, "deductInfos": [{"totalAmount": 88, "payofftype": 2}], "currentOriginalDailyPrice": 88, "currentDailyPrice": 0, "naked": true}, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1438_48997_pupai", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "携程租车中心取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 143, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "grantedcode": "", "isrec": false, "cvid": 6333, "rentalamount": 0}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3926", "elct": 0, "pLevel": 23665, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 3568, "rStoreNav": "携程租车中心还车", "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzYzMzNfMV84OF84OF84OF8xNDMuMDBfMF81NS4wXzBfMF8wLjBfMC4wXzM1LjAwXzIwLjAwXzBfMF82MzUwNTcyM18zNTQ4fDM2Nzl8Mzc0NnwzNzc5XzEwMDE6ODh8MTAwMzoyMC4wMHwxMDAyOjM1LjAw", "alipay": false, "vendorCode": "37573", "productCode": "SD3926_0_106896_106896", "pLev": 100619, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 2, "packageType": 0, "kPSId": 106896, "age": 30, "rCoup": 0, "kRSId": 106896, "freeIllegalDeposit": false, "rLev": 100619, "pStoreCode": "106896", "pickUpOnDoor": true, "aType": 0, "kVId": 37573, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "kVehicleId": 6333, "comPriceCode": "[c]", "dropOffOnDoor": true, "returnWayInfo": 16, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减88", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 2, "klb": 1, "promtId": 902694658, "rStoreCode": "106896", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 63505723, "rLevel": 23665, "newEnergy": 1}, "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}], "fees": [{"amount": 118, "detail": [{"code": "1001", "amount": 135, "amountDesc": "¥135", "name": "租车费"}, {"code": "3743", "amount": 17, "amountDesc": "¥17", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥60", "originalDailyPrice": 68, "subAmount": 60, "name": "车辆租金", "amountStr": "¥118"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 198, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥215", "subAmount": 215, "name": "总价", "amountStr": "¥198"}], "priceDailys": [{"date": "string", "oDprice": "string", "priceStr": "string", "showType": 0}], "dPriceDesc": "string"}, {"code": "prep", "name": "无忧保障", "insuranceDesc": ["车损全额免赔", "200万三者保障", "不收折旧停运费"], "priceInfo": {"curOriginDPrice": 78, "oTPrice": 336, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 262, "currentTotalPrice": 336, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "reference": {"bizVendorCode": "string", "vendorCode": "string", "pStoreCode": "string", "rStoreCode": "string", "vehicleCode": "string", "packageId": "string", "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "string", "responseReturnLocationId": "string", "vendorVehicleId": "string", "vendorRateReference": "string"}, "decoratorVendorType": 0, "easyLifeUpgradePackageId": 0, "isEasyLife": false, "withPrice": false, "packageId4CutPrice": 0, "payMode": 0, "bomCode": "string", "productCode": "string", "rateCode": "string", "subType": 0, "comPriceCode": "string", "priceVersion": "string", "priceVersionOfLowestPrice": "string", "pCityId": 0, "rCityId": 0, "vendorVehicleCode": "string", "age": 0, "alipay": false, "aType": 0, "vendorSupportZhima": false, "hotType": 0, "priceType": 0, "vehicleDegree": "string", "idType": 0, "fType": 0, "rentCenterId": 0, "isSelect": false, "unionCardFilter": {"itemCode": "string", "name": "string", "code": "string", "groupCode": "string", "bitwiseType": 0, "binaryDigit": 0, "sortNum": 0, "isQuickItem": false, "quickSortNum": 0, "icon": "string", "promotion": {"type": 0, "title": "string", "description": "string", "longTag": "string", "longDesc": "string", "couponDesc": "string", "deductionPercent": 0, "deductionAmount": 0, "dayDeductionAmount": 0, "payofftype": 0, "payoffName": "string", "code": "string", "isFromCtrip": false, "islimitedTimeOfferType": false, "sortNum": 0, "strategySource": "string", "earningsCost": "string", "businessCost": "string", "resourceCost": "string", "configVersion": "string", "isEnabled": false, "actionedDate": "string", "expiredDate": "string", "extDesc": "string", "selected": false, "startAmount": 0, "isOverlay": false, "promotionId": 0, "overlayDesc": "string", "couponName": "string", "unitName": "string", "popAmountTile": "string", "deductionType": 0, "discountType": 0, "adjustPriceCode": "string", "labelCode": "string", "unitCurrency": "string"}, "mark": "string", "positionCode": "string", "selectedIcon": "string", "itemType": 0, "step": 0}, "noDepositFilter": {"itemCode": "string", "name": "string", "code": "string", "groupCode": "string", "bitwiseType": 0, "binaryDigit": 0, "sortNum": 0, "isQuickItem": false, "quickSortNum": 0, "icon": "string", "promotion": {"type": 0, "title": "string", "description": "string", "longTag": "string", "longDesc": "string", "couponDesc": "string", "deductionPercent": 0, "deductionAmount": 0, "dayDeductionAmount": 0, "payofftype": 0, "payoffName": "string", "code": "string", "isFromCtrip": false, "islimitedTimeOfferType": false, "sortNum": 0, "strategySource": "string", "earningsCost": "string", "businessCost": "string", "resourceCost": "string", "configVersion": "string", "isEnabled": false, "actionedDate": "string", "expiredDate": "string", "extDesc": "string", "selected": false, "startAmount": 0, "isOverlay": false, "promotionId": 0, "overlayDesc": "string", "couponName": "string", "unitName": "string", "popAmountTile": "string", "deductionType": 0, "discountType": 0, "adjustPriceCode": "string", "labelCode": "string", "unitCurrency": "string"}, "mark": "string", "positionCode": "string", "selectedIcon": "string", "itemType": 0, "step": 0}, "labels": [{"title": "string", "titleExtra": "string", "category": 0, "type": 0, "code": "string", "typeDesc": "string", "description": "string", "sortNum": 0, "subTitle": "string", "icon": "string", "showLayer": 0, "colorCode": "string", "subList": [{"title": "string", "titleExtra": "string", "category": 0, "type": 0, "code": "string", "typeDesc": "string", "description": "string", "sortNum": 0, "subTitle": "string", "icon": "string", "showLayer": 0, "colorCode": "string", "subList": [null], "labelCode": "string", "positionCode": "string", "groupCode": "string", "tagGroups": 0, "tagSortNum": 0, "amountTitle": "string", "groupId": 0, "mergeId": 0, "prefix": "string", "prefixTypeId": 0, "mergeTitle": "string", "descriptionObject": [{"contentStyle": "string", "stringObjs": [{"content": "string", "style": "string", "url": "string"}]}]}], "labelCode": "string", "positionCode": "string", "groupCode": "string", "tagGroups": 0, "tagSortNum": 0, "amountTitle": "string", "groupId": 0, "mergeId": 0, "prefix": "string", "prefixTypeId": 0, "mergeTitle": "string", "descriptionObject": [{"contentStyle": "string", "stringObjs": [{"content": "string", "style": "string", "url": "string"}]}]}], "pStoreNav": "string", "rStoreNav": "string", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "recsort": 0, "rectype": 0, "cvid": 0, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "string", "grantedcode": "string", "mergeInfo": [{"vehicleId": "string", "storeId": "string"}]}, "adjustVersion": "string", "gsId": 0, "noLp": 0, "elct": 0, "gsDesc": "string", "pRc": 0, "rRc": 0, "skuId": 0, "klbPId": 0, "klb": 0, "pCType": 0, "rCType": 0, "pLevel": 0, "rLevel": 0, "promtId": 0, "rCoup": 0, "sippCode": "string", "sortInfo": {}, "vehicleLevelInfo": {"vLevel": "string", "apiCode": "string", "labelCode": "string", "carAge": "string"}, "newEnergy": 0, "platform": 0, "kPSId": 0, "kRSId": 0, "kVId": 0, "pLev": 0, "rLev": 0, "klbVersion": 0, "kVehicleId": 0, "packagePriceInfos": [{"packageType": "string", "priceCode": "string"}], "diffStoreCode": "string", "productType": 0, "secretBoxParam": {"groupNameScope": [{"vehicleCode": "string", "vehicleName": "string", "vehicleImageUrl": "string"}], "deposit": 0, "peccancyDeposit": 0, "activityType": 0, "ctripVehicleId": 0, "storeId": 0, "timespan": 0, "sign": "string", "totalFeeOfSecretBox": 0, "minTotalFeeOfCurrentGroup": 0, "vehicleGroup": 0}, "stockLevel": "string", "pkgRuleId": 0, "adjustRuleId": "string", "vehicleKey": "string", "isVehicle2": false, "packageLevel": "string", "largeRadiusVersion": "string", "listRequestId": "string", "pickupLocationId": "string", "returnLocationId": "string", "isGroupNew": false, "limitPkt": false, "platformCal": false, "productId": "string"}, "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}], "fees": [{"amount": 118, "detail": [{"code": "1001", "amount": 135, "amountDesc": "¥135", "name": "租车费"}, {"code": "3743", "amount": 17, "amountDesc": "¥17", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥60", "originalDailyPrice": 68, "subAmount": 60, "name": "车辆租金", "amountStr": "¥118"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 198, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥215", "subAmount": 215, "name": "总价", "amountStr": "¥198"}], "priceDailys": [{"date": "string", "oDprice": "string", "priceStr": "string", "showType": 0}], "dPriceDesc": "string"}]}, {"floorId": "f3333", "lowestPrice": 0, "floorName": ["新款", "6个月内车龄"], "isSelect": true, "pickWayInfo": "机场内 汇合点取车", "returnWayInfo": "", "alltags": [{"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "延误免费留车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1233", "code": "6", "title": "ETC", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3594"}], "packageList": [{"code": "1002", "name": "基础保障", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "收折旧停运费"], "priceInfo": {"curOriginDPrice": 78, "oTPrice": 336, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 78, "currentTotalPrice": 336, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "reference": {"bizVendorCode": "string", "vendorCode": "string", "pStoreCode": "string", "rStoreCode": "string", "vehicleCode": "string", "packageId": "string", "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "string", "responseReturnLocationId": "string", "vendorVehicleId": "string", "vendorRateReference": "string"}, "decoratorVendorType": 0, "easyLifeUpgradePackageId": 0, "isEasyLife": false, "withPrice": false, "packageId4CutPrice": 0, "payMode": 0, "bomCode": "string", "productCode": "string", "rateCode": "string", "subType": 0, "comPriceCode": "string", "priceVersion": "string", "priceVersionOfLowestPrice": "string", "pCityId": 0, "rCityId": 0, "vendorVehicleCode": "string", "age": 0, "alipay": false, "aType": 0, "vendorSupportZhima": false, "hotType": 0, "priceType": 0, "vehicleDegree": "string", "idType": 0, "fType": 0, "rentCenterId": 0, "isSelect": false, "unionCardFilter": {"itemCode": "string", "name": "string", "code": "string", "groupCode": "string", "bitwiseType": 0, "binaryDigit": 0, "sortNum": 0, "isQuickItem": false, "quickSortNum": 0, "icon": "string", "promotion": {"type": 0, "title": "string", "description": "string", "longTag": "string", "longDesc": "string", "couponDesc": "string", "deductionPercent": 0, "deductionAmount": 0, "dayDeductionAmount": 0, "payofftype": 0, "payoffName": "string", "code": "string", "isFromCtrip": false, "islimitedTimeOfferType": false, "sortNum": 0, "strategySource": "string", "earningsCost": "string", "businessCost": "string", "resourceCost": "string", "configVersion": "string", "isEnabled": false, "actionedDate": "string", "expiredDate": "string", "extDesc": "string", "selected": false, "startAmount": 0, "isOverlay": false, "promotionId": 0, "overlayDesc": "string", "couponName": "string", "unitName": "string", "popAmountTile": "string", "deductionType": 0, "discountType": 0, "adjustPriceCode": "string", "labelCode": "string", "unitCurrency": "string"}, "mark": "string", "positionCode": "string", "selectedIcon": "string", "itemType": 0, "step": 0}, "noDepositFilter": {"itemCode": "string", "name": "string", "code": "string", "groupCode": "string", "bitwiseType": 0, "binaryDigit": 0, "sortNum": 0, "isQuickItem": false, "quickSortNum": 0, "icon": "string", "promotion": {"type": 0, "title": "string", "description": "string", "longTag": "string", "longDesc": "string", "couponDesc": "string", "deductionPercent": 0, "deductionAmount": 0, "dayDeductionAmount": 0, "payofftype": 0, "payoffName": "string", "code": "string", "isFromCtrip": false, "islimitedTimeOfferType": false, "sortNum": 0, "strategySource": "string", "earningsCost": "string", "businessCost": "string", "resourceCost": "string", "configVersion": "string", "isEnabled": false, "actionedDate": "string", "expiredDate": "string", "extDesc": "string", "selected": false, "startAmount": 0, "isOverlay": false, "promotionId": 0, "overlayDesc": "string", "couponName": "string", "unitName": "string", "popAmountTile": "string", "deductionType": 0, "discountType": 0, "adjustPriceCode": "string", "labelCode": "string", "unitCurrency": "string"}, "mark": "string", "positionCode": "string", "selectedIcon": "string", "itemType": 0, "step": 0}, "labels": [{"title": "string", "titleExtra": "string", "category": 0, "type": 0, "code": "string", "typeDesc": "string", "description": "string", "sortNum": 0, "subTitle": "string", "icon": "string", "showLayer": 0, "colorCode": "string", "subList": [{"title": "string", "titleExtra": "string", "category": 0, "type": 0, "code": "string", "typeDesc": "string", "description": "string", "sortNum": 0, "subTitle": "string", "icon": "string", "showLayer": 0, "colorCode": "string", "subList": [null], "labelCode": "string", "positionCode": "string", "groupCode": "string", "tagGroups": 0, "tagSortNum": 0, "amountTitle": "string", "groupId": 0, "mergeId": 0, "prefix": "string", "prefixTypeId": 0, "mergeTitle": "string", "descriptionObject": [{"contentStyle": "string", "stringObjs": [{"content": "string", "style": "string", "url": "string"}]}]}], "labelCode": "string", "positionCode": "string", "groupCode": "string", "tagGroups": 0, "tagSortNum": 0, "amountTitle": "string", "groupId": 0, "mergeId": 0, "prefix": "string", "prefixTypeId": 0, "mergeTitle": "string", "descriptionObject": [{"contentStyle": "string", "stringObjs": [{"content": "string", "style": "string", "url": "string"}]}]}], "pStoreNav": "string", "rStoreNav": "string", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "recsort": 0, "rectype": 0, "cvid": 0, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "string", "grantedcode": "string", "mergeInfo": [{"vehicleId": "string", "storeId": "string"}]}, "adjustVersion": "string", "gsId": 0, "noLp": 0, "elct": 0, "gsDesc": "string", "pRc": 0, "rRc": 0, "skuId": 0, "klbPId": 0, "klb": 0, "pCType": 0, "rCType": 0, "pLevel": 0, "rLevel": 0, "promtId": 0, "rCoup": 0, "sippCode": "string", "sortInfo": {}, "vehicleLevelInfo": {"vLevel": "string", "apiCode": "string", "labelCode": "string", "carAge": "string"}, "newEnergy": 0, "platform": 0, "kPSId": 0, "kRSId": 0, "kVId": 0, "pLev": 0, "rLev": 0, "klbVersion": 0, "kVehicleId": 0, "packagePriceInfos": [{"packageType": "string", "priceCode": "string"}], "diffStoreCode": "string", "productType": 0, "secretBoxParam": {"groupNameScope": [{"vehicleCode": "string", "vehicleName": "string", "vehicleImageUrl": "string"}], "deposit": 0, "peccancyDeposit": 0, "activityType": 0, "ctripVehicleId": 0, "storeId": 0, "timespan": 0, "sign": "string", "totalFeeOfSecretBox": 0, "minTotalFeeOfCurrentGroup": 0, "vehicleGroup": 0}, "stockLevel": "string", "pkgRuleId": 0, "adjustRuleId": "string", "vehicleKey": "string", "isVehicle2": false, "packageLevel": "string", "largeRadiusVersion": "string", "listRequestId": "string", "pickupLocationId": "string", "returnLocationId": "string", "isGroupNew": false, "limitPkt": false, "platformCal": false, "productId": "string"}, "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}], "fees": [{"amount": 118, "detail": [{"code": "1001", "amount": 135, "amountDesc": "¥135", "name": "租车费"}, {"code": "3743", "amount": 17, "amountDesc": "¥17", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥60", "originalDailyPrice": 68, "subAmount": 60, "name": "车辆租金", "amountStr": "¥118"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 198, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥215", "subAmount": 215, "name": "总价", "amountStr": "¥198"}], "priceDailys": [{"date": "string", "oDprice": "string", "priceStr": "string", "showType": 0}], "dPriceDesc": "string"}, {"code": "2001", "name": "优享保障", "insuranceDesc": ["车损全额免赔", "100万三者保障", "收折旧停运费"], "priceInfo": {"curOriginDPrice": 78, "oTPrice": 336, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 137, "currentTotalPrice": 336, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "reference": {"bizVendorCode": "string", "vendorCode": "string", "pStoreCode": "string", "rStoreCode": "string", "vehicleCode": "string", "packageId": "string", "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "string", "responseReturnLocationId": "string", "vendorVehicleId": "string", "vendorRateReference": "string"}, "decoratorVendorType": 0, "easyLifeUpgradePackageId": 0, "isEasyLife": false, "withPrice": false, "packageId4CutPrice": 0, "payMode": 0, "bomCode": "string", "productCode": "string", "rateCode": "string", "subType": 0, "comPriceCode": "string", "priceVersion": "string", "priceVersionOfLowestPrice": "string", "pCityId": 0, "rCityId": 0, "vendorVehicleCode": "string", "age": 0, "alipay": false, "aType": 0, "vendorSupportZhima": false, "hotType": 0, "priceType": 0, "vehicleDegree": "string", "idType": 0, "fType": 0, "rentCenterId": 0, "isSelect": false, "unionCardFilter": {"itemCode": "string", "name": "string", "code": "string", "groupCode": "string", "bitwiseType": 0, "binaryDigit": 0, "sortNum": 0, "isQuickItem": false, "quickSortNum": 0, "icon": "string", "promotion": {"type": 0, "title": "string", "description": "string", "longTag": "string", "longDesc": "string", "couponDesc": "string", "deductionPercent": 0, "deductionAmount": 0, "dayDeductionAmount": 0, "payofftype": 0, "payoffName": "string", "code": "string", "isFromCtrip": false, "islimitedTimeOfferType": false, "sortNum": 0, "strategySource": "string", "earningsCost": "string", "businessCost": "string", "resourceCost": "string", "configVersion": "string", "isEnabled": false, "actionedDate": "string", "expiredDate": "string", "extDesc": "string", "selected": false, "startAmount": 0, "isOverlay": false, "promotionId": 0, "overlayDesc": "string", "couponName": "string", "unitName": "string", "popAmountTile": "string", "deductionType": 0, "discountType": 0, "adjustPriceCode": "string", "labelCode": "string", "unitCurrency": "string"}, "mark": "string", "positionCode": "string", "selectedIcon": "string", "itemType": 0, "step": 0}, "noDepositFilter": {"itemCode": "string", "name": "string", "code": "string", "groupCode": "string", "bitwiseType": 0, "binaryDigit": 0, "sortNum": 0, "isQuickItem": false, "quickSortNum": 0, "icon": "string", "promotion": {"type": 0, "title": "string", "description": "string", "longTag": "string", "longDesc": "string", "couponDesc": "string", "deductionPercent": 0, "deductionAmount": 0, "dayDeductionAmount": 0, "payofftype": 0, "payoffName": "string", "code": "string", "isFromCtrip": false, "islimitedTimeOfferType": false, "sortNum": 0, "strategySource": "string", "earningsCost": "string", "businessCost": "string", "resourceCost": "string", "configVersion": "string", "isEnabled": false, "actionedDate": "string", "expiredDate": "string", "extDesc": "string", "selected": false, "startAmount": 0, "isOverlay": false, "promotionId": 0, "overlayDesc": "string", "couponName": "string", "unitName": "string", "popAmountTile": "string", "deductionType": 0, "discountType": 0, "adjustPriceCode": "string", "labelCode": "string", "unitCurrency": "string"}, "mark": "string", "positionCode": "string", "selectedIcon": "string", "itemType": 0, "step": 0}, "labels": [{"title": "string", "titleExtra": "string", "category": 0, "type": 0, "code": "string", "typeDesc": "string", "description": "string", "sortNum": 0, "subTitle": "string", "icon": "string", "showLayer": 0, "colorCode": "string", "subList": [{"title": "string", "titleExtra": "string", "category": 0, "type": 0, "code": "string", "typeDesc": "string", "description": "string", "sortNum": 0, "subTitle": "string", "icon": "string", "showLayer": 0, "colorCode": "string", "subList": [null], "labelCode": "string", "positionCode": "string", "groupCode": "string", "tagGroups": 0, "tagSortNum": 0, "amountTitle": "string", "groupId": 0, "mergeId": 0, "prefix": "string", "prefixTypeId": 0, "mergeTitle": "string", "descriptionObject": [{"contentStyle": "string", "stringObjs": [{"content": "string", "style": "string", "url": "string"}]}]}], "labelCode": "string", "positionCode": "string", "groupCode": "string", "tagGroups": 0, "tagSortNum": 0, "amountTitle": "string", "groupId": 0, "mergeId": 0, "prefix": "string", "prefixTypeId": 0, "mergeTitle": "string", "descriptionObject": [{"contentStyle": "string", "stringObjs": [{"content": "string", "style": "string", "url": "string"}]}]}], "pStoreNav": "string", "rStoreNav": "string", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "recsort": 0, "rectype": 0, "cvid": 0, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "string", "grantedcode": "string", "mergeInfo": [{"vehicleId": "string", "storeId": "string"}]}, "adjustVersion": "string", "gsId": 0, "noLp": 0, "elct": 0, "gsDesc": "string", "pRc": 0, "rRc": 0, "skuId": 0, "klbPId": 0, "klb": 0, "pCType": 0, "rCType": 0, "pLevel": 0, "rLevel": 0, "promtId": 0, "rCoup": 0, "sippCode": "string", "sortInfo": {}, "vehicleLevelInfo": {"vLevel": "string", "apiCode": "string", "labelCode": "string", "carAge": "string"}, "newEnergy": 0, "platform": 0, "kPSId": 0, "kRSId": 0, "kVId": 0, "pLev": 0, "rLev": 0, "klbVersion": 0, "kVehicleId": 0, "packagePriceInfos": [{"packageType": "string", "priceCode": "string"}], "diffStoreCode": "string", "productType": 0, "secretBoxParam": {"groupNameScope": [{"vehicleCode": "string", "vehicleName": "string", "vehicleImageUrl": "string"}], "deposit": 0, "peccancyDeposit": 0, "activityType": 0, "ctripVehicleId": 0, "storeId": 0, "timespan": 0, "sign": "string", "totalFeeOfSecretBox": 0, "minTotalFeeOfCurrentGroup": 0, "vehicleGroup": 0}, "stockLevel": "string", "pkgRuleId": 0, "adjustRuleId": "string", "vehicleKey": "string", "isVehicle2": false, "packageLevel": "string", "largeRadiusVersion": "string", "listRequestId": "string", "pickupLocationId": "string", "returnLocationId": "string", "isGroupNew": false, "limitPkt": false, "platformCal": false, "productId": "string"}, "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}], "fees": [{"amount": 118, "detail": [{"code": "1001", "amount": 135, "amountDesc": "¥135", "name": "租车费"}, {"code": "3743", "amount": 17, "amountDesc": "¥17", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥60", "originalDailyPrice": 68, "subAmount": 60, "name": "车辆租金", "amountStr": "¥118"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 198, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥215", "subAmount": 215, "name": "总价", "amountStr": "¥198"}], "priceDailys": [{"date": "string", "oDprice": "string", "priceStr": "string", "showType": 0}], "dPriceDesc": "string"}, {"code": "prep", "name": "无忧保障", "insuranceDesc": ["车损全额免赔", "200万三者保障", "不收折旧停运费"], "priceInfo": {"curOriginDPrice": 78, "oTPrice": 336, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 262, "currentTotalPrice": 336, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "reference": {"bizVendorCode": "string", "vendorCode": "string", "pStoreCode": "string", "rStoreCode": "string", "vehicleCode": "string", "packageId": "string", "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "string", "responseReturnLocationId": "string", "vendorVehicleId": "string", "vendorRateReference": "string"}, "decoratorVendorType": 0, "easyLifeUpgradePackageId": 0, "isEasyLife": false, "withPrice": false, "packageId4CutPrice": 0, "payMode": 0, "bomCode": "string", "productCode": "string", "rateCode": "string", "subType": 0, "comPriceCode": "string", "priceVersion": "string", "priceVersionOfLowestPrice": "string", "pCityId": 0, "rCityId": 0, "vendorVehicleCode": "string", "age": 0, "alipay": false, "aType": 0, "vendorSupportZhima": false, "hotType": 0, "priceType": 0, "vehicleDegree": "string", "idType": 0, "fType": 0, "rentCenterId": 0, "isSelect": false, "unionCardFilter": {"itemCode": "string", "name": "string", "code": "string", "groupCode": "string", "bitwiseType": 0, "binaryDigit": 0, "sortNum": 0, "isQuickItem": false, "quickSortNum": 0, "icon": "string", "promotion": {"type": 0, "title": "string", "description": "string", "longTag": "string", "longDesc": "string", "couponDesc": "string", "deductionPercent": 0, "deductionAmount": 0, "dayDeductionAmount": 0, "payofftype": 0, "payoffName": "string", "code": "string", "isFromCtrip": false, "islimitedTimeOfferType": false, "sortNum": 0, "strategySource": "string", "earningsCost": "string", "businessCost": "string", "resourceCost": "string", "configVersion": "string", "isEnabled": false, "actionedDate": "string", "expiredDate": "string", "extDesc": "string", "selected": false, "startAmount": 0, "isOverlay": false, "promotionId": 0, "overlayDesc": "string", "couponName": "string", "unitName": "string", "popAmountTile": "string", "deductionType": 0, "discountType": 0, "adjustPriceCode": "string", "labelCode": "string", "unitCurrency": "string"}, "mark": "string", "positionCode": "string", "selectedIcon": "string", "itemType": 0, "step": 0}, "noDepositFilter": {"itemCode": "string", "name": "string", "code": "string", "groupCode": "string", "bitwiseType": 0, "binaryDigit": 0, "sortNum": 0, "isQuickItem": false, "quickSortNum": 0, "icon": "string", "promotion": {"type": 0, "title": "string", "description": "string", "longTag": "string", "longDesc": "string", "couponDesc": "string", "deductionPercent": 0, "deductionAmount": 0, "dayDeductionAmount": 0, "payofftype": 0, "payoffName": "string", "code": "string", "isFromCtrip": false, "islimitedTimeOfferType": false, "sortNum": 0, "strategySource": "string", "earningsCost": "string", "businessCost": "string", "resourceCost": "string", "configVersion": "string", "isEnabled": false, "actionedDate": "string", "expiredDate": "string", "extDesc": "string", "selected": false, "startAmount": 0, "isOverlay": false, "promotionId": 0, "overlayDesc": "string", "couponName": "string", "unitName": "string", "popAmountTile": "string", "deductionType": 0, "discountType": 0, "adjustPriceCode": "string", "labelCode": "string", "unitCurrency": "string"}, "mark": "string", "positionCode": "string", "selectedIcon": "string", "itemType": 0, "step": 0}, "labels": [{"title": "string", "titleExtra": "string", "category": 0, "type": 0, "code": "string", "typeDesc": "string", "description": "string", "sortNum": 0, "subTitle": "string", "icon": "string", "showLayer": 0, "colorCode": "string", "subList": [{"title": "string", "titleExtra": "string", "category": 0, "type": 0, "code": "string", "typeDesc": "string", "description": "string", "sortNum": 0, "subTitle": "string", "icon": "string", "showLayer": 0, "colorCode": "string", "subList": [null], "labelCode": "string", "positionCode": "string", "groupCode": "string", "tagGroups": 0, "tagSortNum": 0, "amountTitle": "string", "groupId": 0, "mergeId": 0, "prefix": "string", "prefixTypeId": 0, "mergeTitle": "string", "descriptionObject": [{"contentStyle": "string", "stringObjs": [{"content": "string", "style": "string", "url": "string"}]}]}], "labelCode": "string", "positionCode": "string", "groupCode": "string", "tagGroups": 0, "tagSortNum": 0, "amountTitle": "string", "groupId": 0, "mergeId": 0, "prefix": "string", "prefixTypeId": 0, "mergeTitle": "string", "descriptionObject": [{"contentStyle": "string", "stringObjs": [{"content": "string", "style": "string", "url": "string"}]}]}], "pStoreNav": "string", "rStoreNav": "string", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "recsort": 0, "rectype": 0, "cvid": 0, "rentalamount": 0, "totalDailyPrice": 0, "vdegree": "string", "grantedcode": "string", "mergeInfo": [{"vehicleId": "string", "storeId": "string"}]}, "adjustVersion": "string", "gsId": 0, "noLp": 0, "elct": 0, "gsDesc": "string", "pRc": 0, "rRc": 0, "skuId": 0, "klbPId": 0, "klb": 0, "pCType": 0, "rCType": 0, "pLevel": 0, "rLevel": 0, "promtId": 0, "rCoup": 0, "sippCode": "string", "sortInfo": {}, "vehicleLevelInfo": {"vLevel": "string", "apiCode": "string", "labelCode": "string", "carAge": "string"}, "newEnergy": 0, "platform": 0, "kPSId": 0, "kRSId": 0, "kVId": 0, "pLev": 0, "rLev": 0, "klbVersion": 0, "kVehicleId": 0, "packagePriceInfos": [{"packageType": "string", "priceCode": "string"}], "diffStoreCode": "string", "productType": 0, "secretBoxParam": {"groupNameScope": [{"vehicleCode": "string", "vehicleName": "string", "vehicleImageUrl": "string"}], "deposit": 0, "peccancyDeposit": 0, "activityType": 0, "ctripVehicleId": 0, "storeId": 0, "timespan": 0, "sign": "string", "totalFeeOfSecretBox": 0, "minTotalFeeOfCurrentGroup": 0, "vehicleGroup": 0}, "stockLevel": "string", "pkgRuleId": 0, "adjustRuleId": "string", "vehicleKey": "string", "isVehicle2": false, "packageLevel": "string", "largeRadiusVersion": "string", "listRequestId": "string", "pickupLocationId": "string", "returnLocationId": "string", "isGroupNew": false, "limitPkt": false, "platformCal": false, "productId": "string"}, "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}], "fees": [{"amount": 118, "detail": [{"code": "1001", "amount": 135, "amountDesc": "¥135", "name": "租车费"}, {"code": "3743", "amount": 17, "amountDesc": "¥17", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥60", "originalDailyPrice": 68, "subAmount": 60, "name": "车辆租金", "amountStr": "¥118"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 198, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥215", "subAmount": 215, "name": "总价", "amountStr": "¥198"}], "priceDailys": [{"date": "string", "oDprice": "string", "priceStr": "string", "showType": 0}], "dPriceDesc": "string"}]}], "baseResponse": {"code": "200", "requestId": "b192d542-92dd-4810-8109-8a0b27c3d692", "cost": 144, "isSuccess": true, "returnMsg": "OK"}, "imStatus": 1, "isFromSearch": false, "ResponseStatus": {"Extension": [{"Value": "8007929469320734054", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a2c9f38-482481-754000", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1736932612269+0800)/"}, "extras": {"packageLevelAB": "", "abVersion": "230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "isLicensePlateHideShow": "0", "serverRequestId": "s6E32j9LO0668UxToE9p", "packageLevelSwitch": "0", "rSelect": "1", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "recommendProducts": [{"groupSort": 0, "lowestPrice": 69, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 133, "detail": [{"code": "1001", "amount": 152, "amountDesc": "¥152", "name": "租车费"}, {"code": "3743", "amount": 19, "amountDesc": "¥19", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥69", "originalDailyPrice": 78, "subAmount": 69, "name": "车辆租金", "amountStr": "¥133"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 59, "amountStr": "¥59", "detail": [{"code": "1002", "amount": 59, "amountDesc": "¥59", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 212, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥231", "subAmount": 231, "name": "总价", "amountStr": "¥212"}], "reference": {"vehicleCode": "0", "rStoreCode": "797693", "packageId": "sec", "pLev": 1469735, "comPriceCode": "[c]MTYyODUyMzcwfDIwfDAuMDEtMTYyNS0wMDA6MCAwMDomJjEmMCY3OGUmNzhmYWxzMjUtMCYkMjAgMDA6MS0xNzAmNzgwMDowMjIuMCY3NCZmYWxzMDAwJiY3NCRlJjc4MSYyJnwxMDA1MiQxNzgmMTEmMjAwMDMmMjAuMC4wMCYwMiYyMCQxMDAwJjUmMzAuJHwyMDkuMDAxLTE2MjUtMDAwOjAgMTI6MjUtMDAmMjAgMTA6MS0xODB8MjAwMDowMS0xNTI1LTAxNjo0IDE3OgAAAAA5AAAA", "bizVendorCode": "SD12615", "pStoreCode": "797693", "packageType": 1, "priceVersion": "SH-PRICEVERSION_Nzk3NjkzXzU0MDdfMV83OF8xNTJfNzhfMjMxLjAwXzY5LjBfMjEyLjBfMF8wXzAuMF8wLjBfNTkuMDBfMjAuMDBfMC4wMF8wLjAwXzUyMzcxNjI4", "sendTypeForPickUpCar": 0, "skuId": 52371628, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1469735, "vendorCode": "15005425", "vendorVehicleCode": "8929_34344_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6937402, "bizVendorCode": "SD3198"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 780394, "bizVendorCode": "SD14450"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 73447214, "bizVendorCode": "SD7763"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 27861121, "bizVendorCode": "SD3012"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6934135, "bizVendorCode": "SD4162"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6892630, "bizVendorCode": "SD4161"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4516648, "bizVendorCode": "SD4490"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6837524, "bizVendorCode": "SD9566"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4644248, "bizVendorCode": "SD5722"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2441408, "bizVendorCode": "SD3969"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860717, "bizVendorCode": "SD3914"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4695759, "bizVendorCode": "SD6363"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5585972, "bizVendorCode": "SD9928"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 29780318, "bizVendorCode": "SD6416"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 3209135, "bizVendorCode": "SD4856"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1913024, "bizVendorCode": "SD4982"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2710916, "bizVendorCode": "SD6343"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1922622, "bizVendorCode": "SD4924"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 43940156, "bizVendorCode": "SD12980"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 3237950, "bizVendorCode": "SD3488"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1976757, "bizVendorCode": "SD6282"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1908057, "bizVendorCode": "SD4515"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 4955709, "bizVendorCode": "SD8495"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1838146, "bizVendorCode": "SD3331"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 77222531, "bizVendorCode": "SD11466"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861235, "bizVendorCode": "SD3905"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1907479, "bizVendorCode": "SD4711"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1960762, "bizVendorCode": "SD6471"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2678200, "bizVendorCode": "SD4741"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1913202, "bizVendorCode": "SD4827"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 60106908, "bizVendorCode": "SD7616"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 66199505, "bizVendorCode": "SD13526"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5025476, "bizVendorCode": "SD4634"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 68974039, "bizVendorCode": "SD5426"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1958397, "bizVendorCode": "SD5527"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 6934404, "bizVendorCode": "SD10873"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5020314, "bizVendorCode": "SD8628"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4459685, "bizVendorCode": "SD4955"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1920777, "bizVendorCode": "SD4899"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1859445, "bizVendorCode": "SD4116"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 4517232, "bizVendorCode": "SD6769"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1915888, "bizVendorCode": "SD4725"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5611415, "bizVendorCode": "SD9900"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 62813374, "bizVendorCode": "SD14296"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1836577, "bizVendorCode": "SD3499"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2777420, "bizVendorCode": "SD5469"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 3341905, "bizVendorCode": "SD3323"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5031161, "bizVendorCode": "SD8669"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1916128, "bizVendorCode": "SD4474"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 23968155, "bizVendorCode": "SD10283"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1980192, "bizVendorCode": "SD6105"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1915234, "bizVendorCode": "SD4592"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4553701, "bizVendorCode": "SD5111"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5012152, "bizVendorCode": "SD8633"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 3964596, "bizVendorCode": "SD7492"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1970205, "bizVendorCode": "SD6395"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5194784, "bizVendorCode": "SD9098"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1973742, "bizVendorCode": "SD5490"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 45675575, "bizVendorCode": "SD7463"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5135981, "bizVendorCode": "SD8995"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1910645, "bizVendorCode": "SD4471"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1908339, "bizVendorCode": "SD4969"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1837661, "bizVendorCode": "SD3384"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1858731, "bizVendorCode": "SD3634"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1923384, "bizVendorCode": "SD4753"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减19", "groupId": 1}, "vehicleCode": "5407", "highestPrice": 666, "pWay": "可选：店员免费上门送取车", "minDPrice": 69, "vehicleKey": "0_5407_", "hot": 0, "minTPrice": 212, "lowestDistance": 28.1892, "group": 0, "type": 0, "sortNum": 6, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD12615_0_797693_797693"], "introduce": "当前车型最低价"}, "minDOrinPrice": 78, "isEasy": true, "isCredit": true, "maximumCommentCount": 15749, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 66}, {"groupSort": 0, "lowestPrice": 69, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 133, "detail": [{"code": "1001", "amount": 152, "amountDesc": "¥152", "name": "租车费"}, {"code": "3743", "amount": 19, "amountDesc": "¥19", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥69", "originalDailyPrice": 78, "subAmount": 69, "name": "车辆租金", "amountStr": "¥133"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 59, "amountStr": "¥59", "detail": [{"code": "1002", "amount": 59, "amountDesc": "¥59", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 212, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥231", "subAmount": 231, "name": "总价", "amountStr": "¥212"}], "reference": {"vehicleCode": "0", "rStoreCode": "797693", "packageId": "sec", "pLev": 1469735, "comPriceCode": "[c]MTU3OTUyMzcwfDIwfDAuMDEtMTYyNS0wMDA6MCAwMDomJjEmMCY3OGUmNzhmYWxzMjUtMCYkMjAgMDA6MS0xNzAmNzgwMDowMjIuMCY3NCZmYWxzMDAwJiY3NCRlJjc4MSYyJnwxMDA1MiQxNzgmMTEmMjAwMDMmMjAuMC4wMCYwMiYyMCQxMDAwJjUmMzAuJHwyMDkuMDAxLTE2MjUtMDAwOjAgMTI6MjUtMDAmMjAgMTA6MS0xODB8MjAwMDowMS0xNTI1LTAxNjo0IDE3OgAAAAA5AAAA", "bizVendorCode": "SD12615", "pStoreCode": "797693", "packageType": 1, "priceVersion": "SH-PRICEVERSION_Nzk3NjkzXzQwNjdfMV83OF8xNTJfNzhfMjMxLjAwXzY5LjBfMjEyLjBfMF8wXzAuMF8wLjBfNTkuMDBfMjAuMDBfMC4wMF8wLjAwXzUyMzcxNTc5", "sendTypeForPickUpCar": 0, "skuId": 52371579, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1469735, "vendorCode": "15005425", "vendorVehicleCode": "8928_38476_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6937398, "bizVendorCode": "SD3198"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6934128, "bizVendorCode": "SD4162"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1863478, "bizVendorCode": "SD3969"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 23272810, "bizVendorCode": "SD3871"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 43903077, "bizVendorCode": "SD3130"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5724795, "bizVendorCode": "SD6363"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24004863, "bizVendorCode": "SD7426"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5039965, "bizVendorCode": "SD8838"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5301313, "bizVendorCode": "SD3331"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4390877, "bizVendorCode": "SD8010"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 68977189, "bizVendorCode": "SD5426"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1862748, "bizVendorCode": "SD4116"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 41215805, "bizVendorCode": "SD10193"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2711474, "bizVendorCode": "SD4924"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 3957569, "bizVendorCode": "SD6681"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减19", "groupId": 1}, "vehicleCode": "5348", "highestPrice": 503, "pWay": "可选：店员免费上门送取车", "minDPrice": 69, "vehicleKey": "0_5348_", "hot": 0, "minTPrice": 212, "lowestDistance": 23.398, "group": 0, "type": 0, "sortNum": 12, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD12615_0_797693_797693"], "introduce": "当前车型最低价"}, "minDOrinPrice": 78, "isEasy": true, "isCredit": true, "maximumCommentCount": 15749, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 16}, {"groupSort": 0, "lowestPrice": 60, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 119, "detail": [{"code": "1001", "amount": 136, "amountDesc": "¥136", "name": "租车费"}, {"code": "3743", "amount": 17, "amountDesc": "¥17", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥60", "originalDailyPrice": 68, "subAmount": 60, "name": "车辆租金", "amountStr": "¥119"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 199, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥216", "subAmount": 216, "name": "总价", "amountStr": "¥199"}], "reference": {"vehicleCode": "0", "rStoreCode": "2285368", "packageId": "sec", "pLev": 1876330, "bizVendorCode": "SD7763", "pStoreCode": "2285368", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjI4NTM2OF81MzQ5XzFfNjguMF8xMzYuMF8wLjBfMjE2LjBfNjAuMF8xOTkuMF8wXzBfMC4wXzAuMF82MC4wXzIwLjBfMF8wXzcyOTI2MDcx", "sendTypeForPickUpCar": 0, "skuId": 72926071, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1876330, "vendorCode": "15006533", "vendorVehicleCode": "90676_68796_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 25715852, "bizVendorCode": "SD11455"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 25648589, "bizVendorCode": "SD8303"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 65930124, "bizVendorCode": "SD4685"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4157351, "bizVendorCode": "SD3446"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1924934, "bizVendorCode": "SD4592"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5012269, "bizVendorCode": "SD8633"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1922165, "bizVendorCode": "SD4969"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5043741, "bizVendorCode": "SD8762"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减17", "groupId": 1}, "vehicleCode": "5349", "highestPrice": 351, "pWay": "可选：店员免费上门送取车", "minDPrice": 60, "vehicleKey": "0_5349_", "hot": 0, "minTPrice": 199, "lowestDistance": 17.3719, "group": 0, "type": 0, "sortNum": 24, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD7763_0_2285368_2285368"], "introduce": "当前车型最低价"}, "minDOrinPrice": 68, "isEasy": true, "isCredit": true, "maximumCommentCount": 558, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 9}, {"groupSort": 0, "lowestPrice": 60, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 116, "detail": [{"code": "1001", "amount": 132, "amountDesc": "¥132", "name": "租车费"}, {"code": "3743", "amount": 16, "amountDesc": "¥16", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥60", "originalDailyPrice": 68, "subAmount": 60, "name": "车辆租金", "amountStr": "¥116"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 59, "amountStr": "¥59", "detail": [{"code": "1002", "amount": 59, "amountDesc": "¥59", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 195, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥211", "subAmount": 211, "name": "总价", "amountStr": "¥195"}], "reference": {"vehicleCode": "0", "rStoreCode": "797693", "packageId": "sec", "pLev": 1469735, "comPriceCode": "[c]MTYxNDUyMzcwfDIwfDAuMDEtMTYyNS0wMDA6MCAwMDomJjEmMCY2OGUmNjhmYWxzMjUtMCYkMjAgMDA6MS0xNzAmNjgwMDowMjIuMCY2NCZmYWxzMDAwJiY2NCRlJjY4MSYyJnwxMDAzMiQxNjgmMTEmMjAwMDMmMjAuMC4wMCYwMiYyMCQxMDAwJjUmMzAuJHwyMDkuMDAxLTE2MjUtMDAwOjAgMTI6MjUtMDAmMjAgMTA6MS0xODB8MjAwMDowMS0xNTI1LTAxNjo0IDE3OgAAAAA5AAAA", "bizVendorCode": "SD12615", "pStoreCode": "797693", "packageType": 1, "priceVersion": "SH-PRICEVERSION_Nzk3NjkzXzMxNTlfMV82OF8xMzJfNjhfMjExLjAwXzYwLjBfMTk1LjBfMF8wXzAuMF8wLjBfNTkuMDBfMjAuMDBfMC4wMF8wLjAwXzUyMzcxNjE0", "sendTypeForPickUpCar": 0, "skuId": 52371614, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1469735, "vendorCode": "15005425", "vendorVehicleCode": "49529_51156_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6899907, "bizVendorCode": "SD3866"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 35878, "bizVendorCode": "SD14450"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 27861107, "bizVendorCode": "SD3012"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 25655743, "bizVendorCode": "SD11455"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 8156051, "bizVendorCode": "SD10483"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 61610333, "bizVendorCode": "SD12823"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 76255229, "bizVendorCode": "SD11599"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 44325065, "bizVendorCode": "SD9900"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 4518046, "bizVendorCode": "SD8103"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减16", "groupId": 1}, "vehicleCode": "3159", "highestPrice": 78, "pWay": "可选：店员免费上门送取车", "minDPrice": 60, "vehicleKey": "0_3159_", "hot": 0, "minTPrice": 195, "lowestDistance": 23.8315, "group": 0, "type": 0, "sortNum": 30, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD12615_0_797693_797693"], "introduce": "当前车型最低价"}, "minDOrinPrice": 68, "isEasy": true, "isCredit": true, "maximumCommentCount": 4482, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 10}], "checkResponseTime": 1736932566783.66, "checkRequestTime": 1736932566466.143, "timeInterval": 317.516845703125, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 342, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 342, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1736932566465, "afterFetch": 1736932566807}}