{"promotMap": {}, "extras": {"packageLevelAB": "", "goodsShelvesTwoSwitch": "1", "isLicensePlateHideShow": "0", "goodsShelvesTwoABVersion": "C", "packageLevelSwitch": "0", "abVersion": "230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,220624_DSJT_spfj1|B,250325_DSJT_huojia20|C", "serverRequestId": "8n42wjFV7drVC44Q49Q1", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "shareVehicleInfo": {"doorNo": 4, "displacement": "1.5L", "style": "", "license": "", "minCurrentDailyPrice": 0, "vehicleImage": "https://dimg04.c-ctrip.com/images/0RV2h12000danff763DD3.png?mark=yiche", "groupName": "比亚迪秦PLUS", "transmissionType": 1, "passengerNo": 5, "vehicleName": "比亚迪秦PLUS", "transmissionName": "自动挡"}, "baseResponse": {"code": "200", "requestId": "96a06a93-af10-45f7-8a6b-cf35eb29a60f", "cost": 55, "isSuccess": true, "returnMsg": "OK"}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "vehicleInfo": {"skylight": "部分车辆支持", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "不支持", "displacement": "1.5L", "autoPark": false, "endurance": "工信部续航46km-101km", "fuelType": "插电式", "charge": "", "vehicleKey": "0_51276_", "imageList": ["https://dimg04.c-ctrip.com/images/0RV2h12000danff763DD3.png?mark=yiche"], "license": "", "isSpecialized": true, "multimediaAlbums": [{"albumName": "官方相册", "albumType": 1, "mediaGroup": [{"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0d12000danf8z67655.png?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2912000danfa6c0B71.png?mark=yiche", "cover": ""}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4p12000danfvfb4819.png?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0912000dang7l5EC33.png?mark=yiche", "cover": ""}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6s12000danftdy405F.png?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1l12000danfty4A54A.png?mark=yiche", "cover": ""}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}], "note": "年款/颜色等以门店为准"}], "snowTyre": {"type": 0, "typeDesc": "不支持"}, "groupCode": "2", "zhName": "比亚迪秦PLUS", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持"}, "shortEndurance": "46-101km", "carPhone": true, "vehicleCode": "51276", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "比亚迪秦PLUS", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0d12000danf8z67655.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV4p12000danfvfb4819.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6s12000danftdy405F.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0912000dang7l5EC33.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1l12000danfty4A54A.png?mark=yiche"], "transmissionType": 1, "brandName": "比亚迪", "oilType": 4, "tachograph": {"type": 2, "typeDesc": "部分车辆支持"}, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "比亚迪", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "72", "guidSys": "支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}, "isFromSearch": false, "timeInterval": 148.************, "resBodySize": 25787, "ResponseStatus": {"Extension": [{"Value": "7171417761366154127", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a28f13a-484692-806336", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1744892129227+0800)/"}, "imStatus": 0, "floor": [{"pickWayInfo": "免费送车至车站内", "floorName": ["1年内车龄"], "vendorName": "琼驰租车", "packageList": [{"reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 55, "rCType": 2, "vendorVehicleCode": "65937775", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 55, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至车站内", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 196, "recommendOrder": 0, "mergeId": 1324, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "1443037", "vehicleId": "65937775"}], "grantedcode": "", "isrec": false, "cvid": 51276, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD5809", "elct": 0, "pLevel": 1479528, "gsDesc": "精选好货", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "车站内还车", "priceVersion": "SH-PRICEVERSION_MTQ0MzAzN181MTI3Nl8xXzU4XzExNl81OF8xOTYuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTkzNzc3NV8zNTEwfDM4MTB8MzU2M3wzNzQ2fDM3ODh8MzUwNF8xMDAxOjExNnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "62267", "productCode": "SD5809_0_1443037_1443037", "pLev": 1479528, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1443037, "age": 30, "rCoup": 0, "kRSId": 1443037, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1479528, "pStoreCode": "1443037", "pickUpOnDoor": true, "aType": 0, "productId": "31308446150823529", "kVId": 62267, "sortInfo": {"p": "1", "s": "100.0", "c": "55", "v": "1443037"}, "kVehicleId": 51276, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减116", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 841370114, "rStoreCode": "1443037", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 65937775, "rLevel": 1479528, "newEnergy": 1}, "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "11037", "amount": 116, "amountDesc": "¥116", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 58, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 80, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥196", "subAmount": 196, "name": "总价", "amountStr": "¥80"}], "code": "1002", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 58, "currentTotalPrice": 80, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 0, "marginPrice": 0, "oTPrice": 196, "deductInfos": [{"totalAmount": 116, "payofftype": 2}], "currentOriginalDailyPrice": 58, "currentDailyPrice": 0, "naked": true}, "packageLevel": "BAS", "name": "基础保障", "marketingTags": [{"category": 3, "sortNum": 10000, "amountTitle": "已减116", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}]}, {"reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 55, "rCType": 2, "vendorVehicleCode": "65937775", "pickWayInfo": 1, "packageLevel": "ADV", "pCityId": 55, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至车站内", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 196, "recommendOrder": 0, "mergeId": 1324, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "1443037", "vehicleId": "65937775"}], "grantedcode": "", "isrec": false, "cvid": 51276, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD5809", "elct": 0, "pLevel": 1479528, "gsDesc": "精选好货", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "车站内还车", "priceVersion": "SH-PRICEVERSION_MTQ0MzAzN181MTI3Nl8xXzU4XzExNl81OF8xOTYuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTkzNzc3NV8zNTEwfDM4MTB8MzU2M3wzNzQ2fDM3ODh8MzUwNF8xMDAxOjExNnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "62267", "productCode": "SD5809_0_1443037_1443037", "pLev": 1479528, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1443037, "age": 30, "rCoup": 0, "kRSId": 1443037, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1479528, "pStoreCode": "1443037", "pickUpOnDoor": true, "aType": 0, "productId": "31308446150823529", "kVId": 62267, "sortInfo": {"p": "1", "s": "100.0", "c": "55", "v": "1443037"}, "kVehicleId": 51276, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减116", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 841370114, "rStoreCode": "1443037", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 65937775, "rLevel": 1479528, "newEnergy": 1}, "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "11037", "amount": 116, "amountDesc": "¥116", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 58, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2001", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "优享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 160, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥276", "subAmount": 276, "name": "总价", "amountStr": "¥160"}], "code": "2001", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 58, "currentTotalPrice": 160, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 40, "marginPrice": 0, "oTPrice": 276, "deductInfos": [{"totalAmount": 116, "payofftype": 2}], "currentOriginalDailyPrice": 58, "currentDailyPrice": 0, "naked": true}, "packageLevel": "ADV", "name": "优享保障", "marketingTags": [{"category": 3, "sortNum": 10000, "amountTitle": "已减116", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}]}, {"reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 55, "rCType": 2, "vendorVehicleCode": "65937775", "pickWayInfo": 1, "packageLevel": "PRE", "pCityId": 55, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至车站内", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 196, "recommendOrder": 0, "mergeId": 1324, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "1443037", "vehicleId": "65937775"}], "grantedcode": "", "isrec": false, "cvid": 51276, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD5809", "elct": 0, "pLevel": 1479528, "gsDesc": "精选好货", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "车站内还车", "priceVersion": "SH-PRICEVERSION_MTQ0MzAzN181MTI3Nl8xXzU4XzExNl81OF8xOTYuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTkzNzc3NV8zNTEwfDM4MTB8MzU2M3wzNzQ2fDM3ODh8MzUwNF8xMDAxOjExNnwxMDAzOjIwLjAwfDEwMDI6NjAuMDA=", "alipay": false, "vendorCode": "62267", "productCode": "SD5809_0_1443037_1443037", "pLev": 1479528, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1443037, "age": 30, "rCoup": 0, "kRSId": 1443037, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 1479528, "pStoreCode": "1443037", "pickUpOnDoor": true, "aType": 0, "productId": "31308446150823529", "kVId": 62267, "sortInfo": {"p": "1", "s": "100.0", "c": "55", "v": "1443037"}, "kVehicleId": 51276, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减116", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 841370114, "rStoreCode": "1443037", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 65937775, "rLevel": 1479528, "newEnergy": 1}, "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "11037", "amount": 116, "amountDesc": "¥116", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 58, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 220, "amountStr": "¥220", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}, {"code": "2011", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "尊享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 240, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥356", "subAmount": 356, "name": "总价", "amountStr": "¥240"}], "code": "2011", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"curOriginDPrice": 58, "currentTotalPrice": 240, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 80, "marginPrice": 0, "oTPrice": 356, "deductInfos": [{"totalAmount": 116, "payofftype": 2}], "currentOriginalDailyPrice": 58, "currentDailyPrice": 0, "naked": true}, "packageLevel": "PRE", "name": "尊享保障", "marketingTags": [{"category": 3, "sortNum": 10000, "amountTitle": "已减116", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}]}], "lowestPrice": 1, "isSelect": true, "floorId": "0_1_1", "alltags": [{"category": 1, "sortNum": 25, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 48, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}]}, {"pickWayInfo": "免费送车至车站内", "floorName": ["2年内车龄"], "vendorName": "圆茄子出行", "packageList": [{"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 55, "rCType": 2, "vendorVehicleCode": "24940560", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 55, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至车站内", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 276, "recommendOrder": 0, "mergeId": 1324, "vdegree": "0", "rectype": 1, "totalDailyPrice": 68, "mergeInfo": [{"storeId": "320902", "vehicleId": "24940560"}], "grantedcode": "", "isrec": false, "cvid": 51276, "rentalamount": 136}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD11688", "elct": 0, "pLevel": 776049, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "车站内还车", "priceVersion": "SH-PRICEVERSION_MzIwOTAyXzU0MThfMV82OF8xMzZfNjhfMjc2LjAwXzY4XzI3Ni4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfMjQ5NDA1NjBfMzU0N3wzODEwfDM1NjN8Mzc0NnwzNzg4fDM1MDRfMTAwMToxMzZ8MTAwMzoyMC4wMHwxMDAyOjEyMC4wMA==", "alipay": false, "vendorCode": "15004560", "productCode": "SD11688_0_320902_320902", "pLev": 776049, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 320902, "age": 30, "rCoup": 0, "kRSId": 320902, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 776049, "pStoreCode": "320902", "pickUpOnDoor": true, "aType": 0, "productId": "31482546634163719", "kVId": 15004560, "sortInfo": {"p": "1", "s": "9.47", "c": "55", "v": "320902"}, "kVehicleId": 5418, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "320902", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 24940560, "rLevel": 776049, "newEnergy": 1}, "fees": [{"amount": 136, "detail": [{"code": "1001", "amount": 136, "amountDesc": "¥136", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥68", "subAmount": 68, "name": "车辆租金", "amountStr": "¥136"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 276, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥276", "subAmount": 276, "name": "总价", "amountStr": "¥276"}], "code": "1002", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 68, "currentTotalPrice": 276, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 0, "marginPrice": 0, "oTPrice": 276, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 68, "naked": true}, "packageLevel": "BAS", "name": "基础保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 55, "rCType": 2, "vendorVehicleCode": "24940560", "pickWayInfo": 1, "packageLevel": "ADV", "pCityId": 55, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至车站内", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 276, "recommendOrder": 0, "mergeId": 1324, "vdegree": "0", "rectype": 1, "totalDailyPrice": 68, "mergeInfo": [{"storeId": "320902", "vehicleId": "24940560"}], "grantedcode": "", "isrec": false, "cvid": 51276, "rentalamount": 136}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD11688", "elct": 0, "pLevel": 776049, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "车站内还车", "priceVersion": "SH-PRICEVERSION_MzIwOTAyXzU0MThfMV82OF8xMzZfNjhfMjc2LjAwXzY4XzI3Ni4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfMjQ5NDA1NjBfMzU0N3wzODEwfDM1NjN8Mzc0NnwzNzg4fDM1MDRfMTAwMToxMzZ8MTAwMzoyMC4wMHwxMDAyOjEyMC4wMA==", "alipay": false, "vendorCode": "15004560", "productCode": "SD11688_0_320902_320902", "pLev": 776049, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 320902, "age": 30, "rCoup": 0, "kRSId": 320902, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 776049, "pStoreCode": "320902", "pickUpOnDoor": true, "aType": 0, "productId": "31482546634163719", "kVId": 15004560, "sortInfo": {"p": "1", "s": "9.47", "c": "55", "v": "320902"}, "kVehicleId": 5418, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "320902", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 24940560, "rLevel": 776049, "newEnergy": 1}, "fees": [{"amount": 136, "detail": [{"code": "1001", "amount": 136, "amountDesc": "¥136", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥68", "subAmount": 68, "name": "车辆租金", "amountStr": "¥136"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}, {"code": "2001", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "优享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 356, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥356", "subAmount": 356, "name": "总价", "amountStr": "¥356"}], "code": "2001", "insuranceDesc": ["车损全额免赔", "100万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 68, "currentTotalPrice": 356, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 40, "marginPrice": 0, "oTPrice": 356, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 68, "naked": true}, "packageLevel": "ADV", "name": "优享保障", "marketingTags": []}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 55, "rCType": 2, "vendorVehicleCode": "24940560", "pickWayInfo": 1, "packageLevel": "PRE", "pCityId": 55, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至车站内", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 276, "recommendOrder": 0, "mergeId": 1324, "vdegree": "0", "rectype": 1, "totalDailyPrice": 68, "mergeInfo": [{"storeId": "320902", "vehicleId": "24940560"}], "grantedcode": "", "isrec": false, "cvid": 51276, "rentalamount": 136}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD11688", "elct": 0, "pLevel": 776049, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 0, "rStoreNav": "车站内还车", "priceVersion": "SH-PRICEVERSION_MzIwOTAyXzU0MThfMV82OF8xMzZfNjhfMjc2LjAwXzY4XzI3Ni4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfMjQ5NDA1NjBfMzU0N3wzODEwfDM1NjN8Mzc0NnwzNzg4fDM1MDRfMTAwMToxMzZ8MTAwMzoyMC4wMHwxMDAyOjEyMC4wMA==", "alipay": false, "vendorCode": "15004560", "productCode": "SD11688_0_320902_320902", "pLev": 776049, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 320902, "age": 30, "rCoup": 0, "kRSId": 320902, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 776049, "pStoreCode": "320902", "pickUpOnDoor": true, "aType": 0, "productId": "31482546634163719", "kVId": 15004560, "sortInfo": {"p": "1", "s": "9.47", "c": "55", "v": "320902"}, "kVehicleId": 5418, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "320902", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 24940560, "rLevel": 776049, "newEnergy": 1}, "fees": [{"amount": 136, "detail": [{"code": "1001", "amount": 136, "amountDesc": "¥136", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥68", "subAmount": 68, "name": "车辆租金", "amountStr": "¥136"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 280, "amountStr": "¥280", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}, {"code": "2011", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "尊享服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 436, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥436", "subAmount": 436, "name": "总价", "amountStr": "¥436"}], "code": "2011", "insuranceDesc": ["车损全额免赔", "150万三者保障", "1万停运费保障"], "priceInfo": {"curOriginDPrice": 68, "currentTotalPrice": 436, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceType": 1, "gapPrice": 80, "marginPrice": 0, "oTPrice": 436, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 68, "naked": true}, "packageLevel": "PRE", "name": "尊享保障", "marketingTags": []}], "isSelect": false, "floorId": "0_2_1", "alltags": [{"category": 1, "sortNum": 25, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 48, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}]}], "recommendVehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_5068_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife/原厂互联/映射", "displacement": "1.4T", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "3921940:0;6913747:0;77088796:0;69176339:0;44239364:0;30672993:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV2612000c566bju4BD9.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "捷达VS5", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=2612&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "3921940:1;6913747:1;77088796:1;69176339:0;44239364:1;30672993:1;"}, "carPhone": true, "shortEndurance": "km", "vehicleCode": "5068", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "捷达VS5", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2612000c566bju4BD9.png?mark=yiche", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "3921940:1;6913747:1;77088796:0;69176339:0;44239364:1;30672993:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0n12000c5qlxglBCED.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6y12000c5qm4dp5DE8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1j12000c5qlxgn5B53.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3m12000c5qm56t739A.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4712000c5qm4c41DF5.jpg?mark=yiche"], "transmissionType": 1, "brandName": "捷达", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "捷达", "licenseStyle": "2", "vehiclesSetId": "10", "guidSys": "支持自适应巡航/定速巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_88194_", "luggageNo": 2, "carPlay": "支持原厂互联/映射/HUAWEIHiCar/CarPlay", "displacement": "1.5T", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "60947125:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV6z12000f1mdmvx1142.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "哈弗H6", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "60947125:1;"}, "shortEndurance": "km", "carPhone": true, "vehicleCode": "88194", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "哈弗H6", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1z12000esaq9itF0AD.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "60947125:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV2s12000esaq33rD0CA.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4a12000esaq3o5C1B2.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0r12000esapwv74267.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3412000esapvp0752E.png?mark=yiche"], "transmissionType": 1, "brandName": "哈弗", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "哈弗", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "支持全速自适应巡航/定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_89132_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife/HUAWEIHiCar", "displacement": "1.5L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "62430131:0;62934502:0;62829313:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV4c12000fq38j400A14.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "大众途岳", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "62430131:1;62934502:1;62829313:1;"}, "shortEndurance": "km", "carPhone": true, "vehicleCode": "89132", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "大众途岳", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2m12000fq3675813C6.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "62430131:0;62934502:1;62829313:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6m12000fq39o59C246.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4412000fq39cqnF6EB.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0h12000fq384flE108.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1o12000fq35ir8A244.png?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "支持全速自适应巡航/定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5698_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/HUAWEIHiCar", "displacement": "2.0L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "4953760:0;6913655:0;30683703:0;5020285:0;5033551:0;7659891:0;5031182:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV4y12000c7kn1r6D5E1.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "丰田卡罗拉锐放", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6549&app_ver=10.5", "reverseImage": {"type": 1, "typeDesc": "支持", "description": "4953760:1;6913655:1;30683703:1;5020285:1;5033551:1;7659891:1;5031182:1;"}, "carPhone": true, "shortEndurance": "km", "vehicleCode": "5698", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田卡罗拉锐放", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2a12000ap2ohun5416.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "4953760:1;6913655:0;30683703:0;5020285:1;5033551:1;7659891:0;5031182:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6w12000cdv04ui3F96.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0x12000cdv07avF35D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0o12000cduzxfgD5C1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3s12000cdv02utCA52.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4112000cdv07ycC23C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}], "checkResponseTime": 1744892129136.967, "checkRequestTime": 1744892128988.543, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 278, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 278, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1744892128987, "afterFetch": 1744892129265}}