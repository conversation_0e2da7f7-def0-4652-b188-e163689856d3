{"promotMap": {}, "extras": {"packageLevelAB": "B", "goodsShelvesTwoSwitch": "1", "isLicensePlateHideShow": "0", "goodsShelvesTwoABVersion": "C", "packageLevelSwitch": "1", "abVersion": "241008_DSJT_ykjpx|A,240419_DSJT_wyz24|B,230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,231218_DSJT_zzqh|B,231218_DSJT_zzqh|B,250325_DSJT_huojia20|C", "serverRequestId": "xDTCin647H7X0Fy4Dwm5", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "shareVehicleInfo": {"doorNo": 4, "style": "", "license": "", "minCurrentDailyPrice": 78, "vehicleImage": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "groupName": "比亚迪秦PLUS", "transmissionType": 1, "passengerNo": 5, "vehicleName": "比亚迪秦PLUS", "transmissionName": "自动挡"}, "baseResponse": {"code": "200", "requestId": "b647ea78-f246-46d2-89c9-a499bab98132", "cost": 44, "isSuccess": true, "returnMsg": "OK"}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "checkRequestTime": 1744788590067.712, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "vehicleInfo": {"skylight": "不支持", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "不支持", "vehicleKey": "0_5410_", "autoPark": false, "endurance": "工信部续航400km-600km", "fuelType": "纯电动", "charge": "快充0.5小时", "snowTyre": {"type": 0, "typeDesc": "不支持"}, "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "multimediaAlbums": [{"albumName": "官方相册", "albumType": 1, "mediaGroup": [{"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"sortNum": 0, "type": 1, "url": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "cover": ""}]}, {"groupType": 8, "groupName": "细节", "groupSortNum": 8, "medias": [{"sortNum": 0, "type": 1, "url": "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "cover": ""}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}], "note": "年款/颜色等以门店为准"}], "groupCode": "2", "zhName": "比亚迪秦PLUS", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "不支持", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持"}, "shortEndurance": "400-600km", "carPhone": true, "vehicleCode": "5410", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "比亚迪秦PLUS", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "luggageNum": "可放2个24寸行李箱", "passengerNo": 5, "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "reverseSensor": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "transmissionType": 1, "brandName": "比亚迪", "oilType": 5, "tachograph": {"type": 0, "typeDesc": "不支持"}, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "比亚迪", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "67", "guidSys": "不支持", "transmissionName": "自动挡"}, "isFromSearch": false, "timeInterval": 102.287109375, "resBodySize": 10818, "ResponseStatus": {"Extension": [{"Value": "8431874059713565122", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a76674b-484663-248351", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1744788590264+0800)/"}, "imStatus": 0, "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "floor": [{"pickWayInfo": "免费送车至机场内", "floorName": ["2年以上车龄"], "vendorName": "美点租车", "packageList": [{"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "2511418", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至机场内", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 436, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 78, "grantedcode": "", "isrec": false, "cvid": 5410, "rentalamount": 156}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD5982", "elct": 0, "pLevel": 60298, "gsDesc": "", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 10078, "rStoreNav": "机场内还车", "priceVersion": "SH-PRICEVERSION_MTE2OTU0XzU0MTBfMV85OF8xOTZfOThfNDM2LjAwXzc4LjBfMzk2LjBfMF8wXzAuMF8wLjBfMTYwLjAwXzM1LjAwXzAuMDBfMC4wMF8yNTExNDE4XzM4MTB8MzU2M3wzNzQ2XzEwMDE6MTk2fDEwMDM6MzUuMDB8MTEwMDY6NDUuMDB8MTAwMjoxNjAuMDA=", "alipay": false, "vendorCode": "77147", "productCode": "SD5982_0_116954_116954", "pLev": 60298, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 116954, "age": 30, "rCoup": 0, "kRSId": 116954, "platformCal": true, "freeIllegalDeposit": false, "rLev": 60298, "pStoreCode": "116954", "pickUpOnDoor": true, "aType": 0, "kVId": 77147, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "79099"}, "kVehicleId": 5410, "comPriceCode": "[c]", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减40", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "116954", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 2511418, "rLevel": 60298, "newEnergy": 1}, "fees": [{"amount": 156, "detail": [{"code": "1001", "amount": 196, "amountDesc": "¥196", "name": "租车费"}, {"code": "3743", "amount": 40, "amountDesc": "¥40", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥78", "originalDailyPrice": 98, "subAmount": 78, "name": "车辆租金", "amountStr": "¥156"}, {"code": "CAR_SERVICE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 45, "amountDesc": "¥45", "showFree": false, "name": "夜间取车费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 160, "amountStr": "¥160", "detail": [{"code": "1002", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 396, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥436", "subAmount": 436, "name": "总价", "amountStr": "¥396"}], "code": "1002", "insuranceDesc": ["1500元内车损自付", "50万三者保障", "不免停运费"], "priceInfo": {"curOriginDPrice": 98, "currentTotalPrice": 396, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceVersion": "SH-PRICEVERSION_MTE2OTU0XzU0MTBfMV85OF8xOTZfOThfNDM2LjAwXzc4LjBfMzk2LjBfMF8wXzAuMF8wLjBfMTYwLjAwXzM1LjAwXzAuMDBfMC4wMF8yNTExNDE4", "priceType": 1, "marginPrice": 0, "oTPrice": 436, "deductInfos": [{"totalAmount": 40, "payofftype": 2}], "currentOriginalDailyPrice": 98, "currentDailyPrice": 78, "naked": true}, "packageLevel": "BAS", "name": "基础保障", "marketingTags": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减40", "groupId": 1}]}, {"reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "2511418", "pickWayInfo": 1, "packageLevel": "PREP", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费送车至机场内", "originPsType": 1, "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 800, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 400, "grantedcode": "", "isrec": false, "cvid": 5410, "rentalamount": 800}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD5982", "elct": 0, "pLevel": 60298, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 201258, "rStoreNav": "机场内还车", "priceVersion": "SH-PRICEVERSION_MTE2OTU0XzU0MTBfMV80MDBfODAwXzQwMF84MDBfNDAwXzgwMC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzI1MTE0MTg=", "alipay": false, "vendorCode": "77147", "productCode": "SD5982_0_116954_116954", "pLev": 60298, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "noLp": 1, "pRc": 0, "packageType": 1, "kPSId": 116954, "age": 30, "rCoup": 0, "kRSId": 116954, "packageId": "", "platformCal": true, "freeIllegalDeposit": false, "rLev": 60298, "pStoreCode": "116954", "pickUpOnDoor": true, "aType": 0, "kVId": 77147, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "79099"}, "kVehicleId": 5410, "comPriceCode": "[c]", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "116954", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 2511418, "rLevel": 60298, "newEnergy": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "amount": 800, "amountStr": "¥800", "detail": [{"code": "20000001", "amount": 800, "amountDesc": "¥800", "name": "无忧租+车损全免保障"}], "name": "车辆租赁费"}, {"amount": 800, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥800", "subAmount": 800, "name": "一口价", "amountStr": "¥800"}], "code": "prep", "insuranceDesc": ["车损全额免赔", "150万三者保障", "免停运费"], "priceInfo": {"curOriginDPrice": 400, "currentTotalPrice": 800, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceVersion": "SH-PRICEVERSION_MTE2OTU0XzU0MTBfMV80MDBfODAwXzQwMF84MDBfNDAwXzgwMC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzI1MTE0MTg=", "priceType": 1, "marginPrice": 0, "oTPrice": 800, "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 400, "naked": true}, "packageLevel": "PREP", "name": "无忧保障", "marketingTags": []}], "lowestPrice": 1, "isSelect": false, "floorId": "0_3_1", "alltags": [{"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}]}], "checkResponseTime": 1744788590169.999, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 186, "environmentCost": 1, "cacheFetchCost": 0, "fetchCost": 186, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1744788590066, "afterFetch": 1744788590252}}