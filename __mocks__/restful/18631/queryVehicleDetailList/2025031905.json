{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "c35f2d7f-2ee6-4dd9-91a9-18428926536f", "cost": 618}, "ResponseStatus": {"Timestamp": "/Date(1744786029673 0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "5714551005147668687"}, {"Id": "RootMessageId", "Value": "921822-0a182bb1-484662-3636951"}]}, "vehicleInfo": {"brandEName": "丰田", "brandName": "丰田", "name": "丰田卡罗拉（23款及以前）", "zhName": "丰田卡罗拉（23款及以前）", "vehicleCode": "5407", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.2T-1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "oilType": 3, "fuelType": "汽油", "luggageNum": "可放2个24寸行李箱", "guidSys": "支持全速自适应巡航", "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "chargeInterface": "支持USB/Type-C", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "carPhone": true, "autoBackUp": true, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "vehiclesSetId": "70", "multimediaAlbums": [{"albumName": "官方相册", "note": "年款/颜色等以门店为准", "albumType": 1, "mediaGroup": [{"groupType": 3, "groupName": "VR", "groupSortNum": 3, "medias": [{"type": 3, "url": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "cover": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "sortNum": 0}]}, {"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0v12000c5s8x28CE97.jpg?mark=yiche", "cover": "", "sortNum": 1}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1f12000c5s8zer6086.jpg?mark=yiche", "cover": "", "sortNum": 2}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4y12000c5s8zej70B6.jpg?mark=yiche", "cover": "", "sortNum": 3}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV7412000c5s92lx251B.jpg?mark=yiche", "cover": "", "sortNum": 4}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4g12000c5s8x2w3A34.jpg?mark=yiche", "cover": "", "sortNum": 5}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "cover": "", "sortNum": 1}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "cover": "", "sortNum": 2}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche", "cover": "", "sortNum": 3}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0d12000c5s95c634CA.jpg?mark=yiche", "cover": "", "sortNum": 4}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2k12000c5s98e21861.jpg?mark=yiche", "cover": "", "sortNum": 5}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "cover": "", "sortNum": 1}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "cover": "", "sortNum": 2}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1612000c5s9g6kD17B.jpg?mark=yiche", "cover": "", "sortNum": 3}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3e12000c5s9cx6B645.jpg?mark=yiche", "cover": "", "sortNum": 4}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1212000c5s9etaA0E9.jpg?mark=yiche", "cover": "", "sortNum": 5}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}]}], "mediaTypes": [3], "vehicleKey": "0_5407_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "snowTyre": {"type": 0, "typeDesc": "不支持"}, "reverseImage": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持"}, "tachograph": {"type": 2, "typeDesc": "部分车辆支持"}, "shortEndurance": "km"}, "specificProductGroups": {"vendorPriceList": [{"vendorName": "路路发租车", "isMinTPriceVendor": true, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "路路发租车（安顺店）", "commentCount": 312, "qCommentCount": 312, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "commentLabel": "办理手续便捷", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 56, "currentOriginalDailyPrice": 75, "curOriginDPrice": 75, "oTPrice": 230, "currentTotalPrice": 192, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 38, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3921", "vendorCode": "35441", "pStoreCode": "263096", "rStoreCode": "263096", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3921_0_263096_263096", "comPriceCode": "[c]NDE2fDgxNzh8MjAyMC4wMC0xOCA1LTA0MDowMDAwOjAmMSZmJjc1JiY3NSZhbHNlNS0wNCQyMDIwMDowLTE5ICY3NSYwOjAwYWxzZSYxJmYkfDEwJjc1JiY3NSYwMSYyMTAwMzE1MCQwLjAwJjEmMjAwJDEmMjAuMiYzMDAwMiY2MC4wLjAwJjAyNS0wJHwyOCAxMDA0LTEwMCYyOjAwOjA0LTIwMjUtOjAwOjAgMTAwMjUtMDB8MjYgMTQwNC0xMDkAADo0Nzo=", "priceVersion": "SH-PRICEVERSION_MjYzMDk2XzU1OTdfMV83NV8xNTBfNzVfMjMwLjAwXzU2LjBfMTkyLjBfMF8wXzAuMF8wLjBfNjAuMDBfMjAuMDBfMC4wMF8wLjAwXzgxNzg0MTZfMzU0N3wzNTAxfDM1NjN8Mzc0NnwzNzg4XzEwMDE6MTUwfDEwMDM6MjAuMDB8MTAwMjo2MC4wMA==", "pCityId": 179, "rCityId": 179, "vendorVehicleCode": "8178416", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "周三福利日 券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "共减38", "groupId": 1, "mergeId": 0}, {"title": "周三福利日 券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减38", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车至机场内", "rStoreNav": "机场内还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 230, "isrec": false, "recommendOrder": 0, "mergeId": 182, "rectype": 1, "cvid": 5407, "rentalamount": 112, "totalDailyPrice": 56, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "8178416", "storeId": "263096"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "精选好货", "pRc": 0, "rRc": 0, "skuId": 8178416, "klbPId": 1532, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 429892, "rLevel": 429892, "promtId": 194874922, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "179", "v": "263096"}, "newEnergy": 0, "platform": 10, "kPSId": 263096, "kRSId": 263096, "kVId": 35441, "pLev": 429892, "rLev": 429892, "klbVersion": 1, "kVehicleId": 5597, "adjustRuleId": "", "packageLevel": "BAS", "platformCal": false, "originPsType": 1}, "sortNum": 0, "pStoreRouteDesc": "免费送车至机场内", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "周三福利日 券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "共减38", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_f0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 64, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 48, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 144, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0, "storeScore": 83.75, "isSelect": true, "distance": 0, "rDistance": 6.0156, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3743", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "路路发租车", "card": 0, "ctripVehicleCode": "5407", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 112, "amountStr": "¥112", "subAmount": 56, "subAmountStr": "日均¥56", "originalDailyPrice": 75, "detail": [{"code": "1001", "name": "租车费", "amount": 150, "amountDesc": "¥150"}, {"code": "11037", "name": "优惠券", "amount": 20, "amountDesc": "¥20"}, {"code": "3743", "name": "周三福利日", "amount": 18, "amountDesc": "¥18"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 192, "amountStr": "¥192", "subAmount": 230, "subAmountStr": "¥230", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bb134c69e657458e9757fb3ee3d820ec", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_5407_", "addProducts": [], "stationType": 1, "originPsType": 1, "originRsType": 1, "lowestPrice": 1}, {"vendorName": "环球驰骋租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "满意", "vendorDesc": "环球驰骋租车", "commentCount": 89, "qCommentCount": 89, "qExposed": "4.6", "overallRating": "4.6", "maximumRating": 5, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77, "currentOriginalDailyPrice": 87, "curOriginDPrice": 87, "oTPrice": 274, "currentTotalPrice": 254, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 20, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD6280", "vendorCode": "32302", "pStoreCode": "115993", "rStoreCode": "115993", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD6280_0_115993_115993", "comPriceCode": "[c]Njk2fDUzNTl8MjAyMC4xNS0xOCA1LTA0MDowMDAwOjAmMSZmJjg3JiY4NyZhbHNlNS0wNCQyMDIwMDowLTE5ICY4NyYwOjAwYWxzZSYxJmYkfDEwJjg3JiY4NyYwMSYyMTAwMzE3NCQwLjAwJjEmMjAwJDEmMjAuMiY0MDAwMiY4MC4wLjAwJjAyNS0wJHwyOCAxMDA0LTEwMCYyOjAwOjA0LTIwMjUtOjAwOjAgMTAwMjUtMDB8MjYgMTQwNC0xMDkAADo0Nzo=", "priceVersion": "SH-PRICEVERSION_MTE1OTkzXzU1OTdfMV84N18xNzRfODdfMjc0LjAwXzc3LjBfMjU0LjBfMF8wXzAuMF8wLjBfODAuMDBfMjAuMDBfMC4wMF8wLjAwXzUzNTk2OTZfMzU0N3wzODEwfDM1NjN8Mzc0NnwzNzg4fDM1MDRfMTAwMToxNzR8MTAwMzoyMC4wMHwxMDAyOjgwLjAw", "pCityId": 179, "rCityId": 179, "vendorVehicleCode": "5359696", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减20", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车至机场内", "rStoreNav": "机场内还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 274, "isrec": false, "recommendOrder": 0, "mergeId": 182, "rectype": 1, "cvid": 5407, "rentalamount": 154, "totalDailyPrice": 77, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "5359696", "storeId": "115993"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 5359696, "klbPId": 7755, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 55934, "rLevel": 55934, "promtId": 194874922, "rCoup": 0, "sortInfo": {"p": "1", "s": "52.79", "c": "179", "v": "17782"}, "newEnergy": 0, "platform": 10, "kPSId": 115993, "kRSId": 115993, "kVId": 32302, "pLev": 55934, "rLev": 55934, "klbVersion": 1, "kVehicleId": 5597, "adjustRuleId": "", "packageLevel": "BAS", "platformCal": false, "originPsType": 1}, "sortNum": 1, "pStoreRouteDesc": "免费送车至机场内", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 48, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减20", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_f0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 32, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 48, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1, "storeScore": 57.01, "isSelect": false, "distance": 0, "rDistance": 5.4738, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "环球驰骋租车", "card": 0, "ctripVehicleCode": "5407", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 154, "amountStr": "¥154", "subAmount": 77, "subAmountStr": "日均¥77", "originalDailyPrice": 87, "detail": [{"code": "1001", "name": "租车费", "amount": 174, "amountDesc": "¥174"}, {"code": "11037", "name": "优惠券", "amount": 20, "amountDesc": "¥20"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 254, "amountStr": "¥254", "subAmount": 274, "subAmountStr": "¥274", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "ba54837031974e00a4508b078fda59bd", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_5407_", "addProducts": [], "stationType": 3, "originPsType": 1, "originRsType": 1}, {"vendorName": "安顺国信租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "西秀区店", "commentCount": 310, "qCommentCount": 310, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77, "currentOriginalDailyPrice": 87, "curOriginDPrice": 87, "oTPrice": 274, "currentTotalPrice": 254, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 20, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD6162", "vendorCode": "82523", "pStoreCode": "117378", "rStoreCode": "117378", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD6162_0_117378_117378", "comPriceCode": "[c]MDgzfDMyNzN8MjAyMC4xNS0xOCA1LTA0MDowMDAwOjAmMSZmJjg3JiY4NyZhbHNlNS0wNCQyMDIwMDowLTE5ICY4NyYwOjAwYWxzZSYxJmYkfDEwJjg3JiY4NyYwMSYyMTAwMzE3NCQwLjAwJjEmMjAwJDEmMjAuMiY0MDAwMiY4MC4wLjAwJjAyNS0wJHwyOCAxMDA0LTEwMCYyOjAwOjA0LTIwMjUtOjAwOjAgMTAwMjUtMDB8MjYgMTQwNC0xMDkAADo0Nzo=", "priceVersion": "SH-PRICEVERSION_MTE3Mzc4XzU1OTdfMV84N18xNzRfODdfMjc0LjAwXzc3LjBfMjU0LjBfMF8wXzAuMF8wLjBfODAuMDBfMjAuMDBfMC4wMF8wLjAwXzMyNzMwODNfMzUxMHwzNTAxfDM1NjN8Mzc0NnwzNzg4fDM1MDRfMTAwMToxNzR8MTAwMzoyMC4wMHwxMDAyOjgwLjAw", "pCityId": 179, "rCityId": 179, "vendorVehicleCode": "3273083", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减20", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费送车至机场内", "rStoreNav": "机场内还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 274, "isrec": false, "recommendOrder": 0, "mergeId": 182, "rectype": 1, "cvid": 5407, "rentalamount": 154, "totalDailyPrice": 77, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "3273083", "storeId": "117378"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 3273083, "klbPId": 11087, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 89363, "rLevel": 89363, "promtId": 194874922, "rCoup": 0, "sortInfo": {"p": "1", "s": "52.79", "c": "179", "v": "88011"}, "newEnergy": 0, "platform": 10, "kPSId": 117378, "kRSId": 117378, "kVId": 82523, "pLev": 89363, "rLev": 89363, "klbVersion": 1, "kVehicleId": 5597, "stockLevel": "B", "adjustRuleId": "", "packageLevel": "BAS", "platformCal": false, "originPsType": 1}, "sortNum": 2, "pStoreRouteDesc": "免费送车至机场内", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "1年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3510", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 48, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减20", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_f0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 48, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 3024, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2, "storeScore": 44.75, "isSelect": false, "distance": 0, "rDistance": 5.5829, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "安顺国信租车", "card": 0, "ctripVehicleCode": "5407", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 154, "amountStr": "¥154", "subAmount": 77, "subAmountStr": "日均¥77", "originalDailyPrice": 87, "detail": [{"code": "1001", "name": "租车费", "amount": 174, "amountDesc": "¥174"}, {"code": "11037", "name": "优惠券", "amount": 20, "amountDesc": "¥20"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 254, "amountStr": "¥254", "subAmount": 274, "subAmountStr": "¥274", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "b432735981154097897decab4796f684", "licenseTag": "", "newCar": true, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_5407_", "addProducts": [], "stationType": 3, "originPsType": 1, "originRsType": 1}, {"vendorName": "一嗨租车", "isMinTPriceVendor": false, "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc4s12000cewdel8F3E3.jpg", "commentInfo": {"level": "超棒", "vendorDesc": "黄果树机场送车点", "commentCount": 165, "qCommentCount": 165, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 114, "currentOriginalDailyPrice": 129, "curOriginDPrice": 129, "oTPrice": 378, "currentTotalPrice": 347, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 31, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD6991", "vendorCode": "13088", "pStoreCode": "147222", "rStoreCode": "147222", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD6991_0_147222_147222", "priceVersion": "SH-PRICEVERSION_MTQ3MjIyXzU1OTdfMV8xMjkuMF8yNTguMF8wLjBfMzc4LjBfMTE0LjBfMzQ3LjBfMF8wXzAuMF8wLjBfMTAwLjBfMjAuMF8wXzBfODcxNjM4ODlfMzU2M3wzNzQ2fDM3NTdfMTAwMToyNTguMHwxMDAzOjIwLjB8MTAwMjoxMDAuMA==", "pCityId": 179, "rCityId": 179, "vendorVehicleCode": "4869", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减31", "groupId": 1, "mergeId": 0}], "pStoreNav": "机场内 门店取车", "rStoreNav": "机场内 门店还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 378, "isrec": false, "recommendOrder": 0, "mergeId": 182, "rectype": 1, "cvid": 5407, "rentalamount": 227, "totalDailyPrice": 114, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "4869", "storeId": "147222"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 87163889, "klbPId": 13158, "klb": 1, "pCType": -1, "rCType": -1, "pLevel": -1, "rLevel": -1, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "0", "s": "25.89", "c": "179"}, "newEnergy": 0, "platform": 0, "kPSId": 147222, "kRSId": 147222, "kVId": 13088, "pLev": -1, "rLev": -1, "klbVersion": 1, "kVehicleId": 5597, "adjustRuleId": "", "packageLevel": "BAS", "platformCal": false, "originPsType": 0}, "sortNum": 3, "pStoreRouteDesc": "机场内 门店取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "全国连锁", "category": 9, "type": 1, "code": "20", "sortNum": 10000, "colorCode": "1", "labelCode": "3757", "groupCode": "MarketGroup1335", "groupId": 2, "mergeId": 0}, {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减31", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_f0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 48, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 3, "storeScore": 58.63, "isSelect": false, "stock": 99999, "distance": 0.0008, "rDistance": 0.0008, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3743", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "一嗨租车", "card": 0, "ctripVehicleCode": "5407", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 227, "amountStr": "¥227", "subAmount": 114, "subAmountStr": "日均¥114", "originalDailyPrice": 129, "detail": [{"code": "1001", "name": "租车费", "amount": 258, "amountDesc": "¥258"}, {"code": "3743", "name": "周三福利日", "amount": 31, "amountDesc": "¥31"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "name": "基础服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 347, "amountStr": "¥347", "subAmount": 378, "subAmountStr": "¥378", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "3c475f1f45d2447f8b1633b54fbc7356", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_5407_", "addProducts": [], "stationType": 1, "originPsType": 0, "originRsType": 0}]}, "filteredProductGroups": {"vendorPriceList": []}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "uniqSign": "32001014890339030951Hd8gzm6h079737vaiY0R", "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "isFromSearch": true, "promptInfos": [], "promotMap": {}, "extras": {"goodsShelvesTwoABVersion": "", "packageLevelAB": "", "selfServiceSwitch": "1", "rSelect": "1", "isLicensePlateHideShow": "0", "packageLevelSwitch": "0", "commodityClass2Version": "1", "goodsShelvesTwoSwitch": "0", "abVersion": "230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B", "isNewLicensePlate": "0", "serverRequestId": "6zz475z6XhlpiC49Z1kZ", "prepProductGroupTopSwitch": "0"}, "imStatus": 1, "recommendProducts": [{"vehicleCode": "4139", "sortNum": 0, "lowestPrice": 52, "highestPrice": 62, "maximumRating": 4.9, "maximumCommentCount": 312, "lowestDistance": 0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD12742", "vendorCode": "15005550", "pStoreCode": "541724", "rStoreCode": "541724", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NTE0OTQzOTcwfDIwfDAuMDQtMTgyNS0wMDA6MCAwMDomJjEmMCY2MiY1MiZ0cnVlNS0wNCQyMDIwMDowLTE5ICY2MiYwOjAwcnVlJiYxJnR8MTAwNTImJDYyJjExJjImMDAzJjI0JDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjIwLjAwMDAmNjI1LTAkfDIwIDEwOjQtMTgwJjIwMDA6MDQtMjAyNS0wMDA6MCAxMDoyNS0wMHwyMCAxNDo0LTE2OQAAADQ3OjA=", "priceVersion": "SH-PRICEVERSION_NTQxNzI0XzQxMzlfMV82Ml8xMjRfNjJfMjA0LjAwXzUyLjBfMTg0LjBfMF8wXzAuMF8wLjBfNjAuMDBfMjAuMDBfMC4wMF8wLjAwXzQzOTc1MTQ5", "vendorVehicleCode": "43975149", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 43975149, "pLev": 1284067, "rLev": 1284067, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 104, "amountStr": "¥104", "subAmount": 52, "subAmountStr": "日均¥52", "originalDailyPrice": 62, "detail": [{"code": "1001", "name": "租车费", "amount": 124, "amountDesc": "¥124"}, {"code": "11037", "name": "优惠券", "amount": 5, "amountDesc": "¥5"}, {"code": "3743", "name": "周三福利日", "amount": 15, "amountDesc": "¥15"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 184, "amountStr": "¥184", "subAmount": 204, "subAmountStr": "¥204", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3921", "vehicleCode": "0", "packageType": 1, "skuId": 8462791}}, {"reference": {"bizVendorCode": "SD8120", "vehicleCode": "0", "packageType": 1, "skuId": 4689860}}, {"reference": {"bizVendorCode": "SD6162", "vehicleCode": "0", "packageType": 1, "skuId": 2851501}}, {"reference": {"bizVendorCode": "SD6280", "vehicleCode": "0", "packageType": 1, "skuId": 1978565}}], "reactId": "1447095880", "group": 0, "groupSort": 0, "hot": 0, "hotType": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD12742_0_541724_541724"]}, "minTPrice": 184, "minDPrice": 52, "modifySameVehicle": false, "minDOrinPrice": 62, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周三福利日 券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "共减20", "groupId": 1, "mergeId": 0}, "priceSize": 5, "isEasy": true, "isCredit": true, "pWay": "可选：免费送车至机场内", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_4139_"}, {"vehicleCode": "5348", "sortNum": 1, "lowestPrice": 56, "highestPrice": 122, "maximumRating": 4.9, "maximumCommentCount": 312, "lowestDistance": 0.0008, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3921", "vendorCode": "35441", "pStoreCode": "263096", "rStoreCode": "263096", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NjY3fDgxNzd8MjAyMC4wMC0xOCA1LTA0MDowMDAwOjAmMSZmJjc1JiY3NSZhbHNlNS0wNCQyMDIwMDowLTE5ICY3NSYwOjAwYWxzZSYxJmYkfDEwJjc1JiY3NSYwMSYyMTAwMzE1MCQwLjAwJjEmMjAwJDEmMjAuMiYzMDAwMiY2MC4wLjAwJjAyNS0wJHwyOCAxMDA0LTEwMCYyOjAwOjA0LTIwMjUtOjAwOjAgMTAwMjUtMDB8MjYgMTQwNC0xMDkAADo0Nzo=", "priceVersion": "SH-PRICEVERSION_MjYzMDk2XzQwNjdfMV83NV8xNTBfNzVfMjMwLjAwXzU2LjBfMTkyLjBfMF8wXzAuMF8wLjBfNjAuMDBfMjAuMDBfMC4wMF8wLjAwXzgxNzc2Njc=", "vendorVehicleCode": "8177667", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 8177667, "pLev": 429892, "rLev": 429892, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 112, "amountStr": "¥112", "subAmount": 56, "subAmountStr": "日均¥56", "originalDailyPrice": 75, "detail": [{"code": "1001", "name": "租车费", "amount": 150, "amountDesc": "¥150"}, {"code": "11037", "name": "优惠券", "amount": 20, "amountDesc": "¥20"}, {"code": "3743", "name": "周三福利日", "amount": 18, "amountDesc": "¥18"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 192, "amountStr": "¥192", "subAmount": 230, "subAmountStr": "¥230", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD8120", "vehicleCode": "0", "packageType": 1, "skuId": 4689833}}, {"reference": {"bizVendorCode": "SD12742", "vehicleCode": "0", "packageType": 1, "skuId": 43938112}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 28854589}}], "reactId": "1447095881", "group": 0, "groupSort": 0, "hot": 0, "hotType": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3921_0_263096_263096"]}, "minTPrice": 192, "minDPrice": 56, "modifySameVehicle": false, "minDOrinPrice": 75, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周三福利日 券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "共减38", "groupId": 1, "mergeId": 0}, "priceSize": 4, "isEasy": true, "isCredit": true, "pWay": "可选：免费送车至机场内", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_5348_"}, {"vehicleCode": "5349", "sortNum": 6, "lowestPrice": 55, "highestPrice": 60, "maximumRating": 5, "maximumCommentCount": 312, "lowestDistance": 96.3986, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3921", "vendorCode": "35441", "pStoreCode": "263096", "rStoreCode": "263096", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]OTA1fDgxNzd8MjAyMC4wMC0xOCA1LTA0MDowMDAwOjAmMSZmJjc1JiY3NSZhbHNlNS0wNCQyMDIwMDowLTE5ICY3NSYwOjAwYWxzZSYxJmYkfDEwJjc1JiY3NSYwMSYyMTAwMzE1MCQwLjAwJjEmMjAwJDEmMjAuMiYzMDAwMiY2MC4wLjAwJjAyNS0wJHwyOCAxMDA0LTEwMCYyOjAwOjA0LTIwMjUtOjAwOjAgMTAwMjUtMDB8MjYgMTQwNC0xMDkAADo0Nzo=", "priceVersion": "SH-PRICEVERSION_MjYzMDk2XzUzNDlfMV83NV8xNTBfNzVfMjMwLjAwXzU2LjBfMTkyLjBfMF8wXzAuMF8wLjBfNjAuMDBfMjAuMDBfMC4wMF8wLjAwXzgxNzc5MDU=", "vendorVehicleCode": "8177905", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 8177905, "pLev": 429892, "rLev": 429892, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 112, "amountStr": "¥112", "subAmount": 56, "subAmountStr": "日均¥56", "originalDailyPrice": 75, "detail": [{"code": "1001", "name": "租车费", "amount": 150, "amountDesc": "¥150"}, {"code": "11037", "name": "优惠券", "amount": 20, "amountDesc": "¥20"}, {"code": "3743", "name": "周三福利日", "amount": 18, "amountDesc": "¥18"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 192, "amountStr": "¥192", "subAmount": 230, "subAmountStr": "¥230", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD8120", "vehicleCode": "0", "packageType": 1, "skuId": 4496691}}, {"reference": {"bizVendorCode": "SD12495", "vehicleCode": "0", "packageType": 1, "skuId": 49249257}}], "reactId": "1447095883", "group": 0, "groupSort": 0, "hot": 0, "hotType": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD12495_0_540520_540520"]}, "minTPrice": 192, "minDPrice": 56, "modifySameVehicle": false, "minDOrinPrice": 75, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周三福利日 券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "共减38", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "pWay": "可选：免费送车至机场内", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_5349_"}, {"vehicleCode": "3159", "sortNum": 7, "lowestPrice": 56, "highestPrice": 114, "maximumRating": 4.9, "maximumCommentCount": 312, "lowestDistance": 0.0008, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3921", "vendorCode": "35441", "pStoreCode": "263096", "rStoreCode": "263096", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NzQ5fDg0NjJ8MjAyMC4wMC0xOCA1LTA0MDowMDAwOjAmMSZmJjc1JiY3NSZhbHNlNS0wNCQyMDIwMDowLTE5ICY3NSYwOjAwYWxzZSYxJmYkfDEwJjc1JiY3NSYwMSYyMTAwMzE1MCQwLjAwJjEmMjAwJDEmMjAuMiYzMDAwMiY2MC4wLjAwJjAyNS0wJHwyOCAxMDA0LTEwMCYyOjAwOjA0LTIwMjUtOjAwOjAgMTAwMjUtMDB8MjYgMTQwNC0xMDkAADo0Nzo=", "priceVersion": "SH-PRICEVERSION_MjYzMDk2XzMxNTlfMV83NV8xNTBfNzVfMjMwLjAwXzU2LjBfMTkyLjBfMF8wXzAuMF8wLjBfNjAuMDBfMjAuMDBfMC4wMF8wLjAwXzg0NjI3NDk=", "vendorVehicleCode": "8462749", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 8462749, "pLev": 429892, "rLev": 429892, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 112, "amountStr": "¥112", "subAmount": 56, "subAmountStr": "日均¥56", "originalDailyPrice": 75, "detail": [{"code": "1001", "name": "租车费", "amount": 150, "amountDesc": "¥150"}, {"code": "11037", "name": "优惠券", "amount": 20, "amountDesc": "¥20"}, {"code": "3743", "name": "周三福利日", "amount": 18, "amountDesc": "¥18"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 192, "amountStr": "¥192", "subAmount": 230, "subAmountStr": "¥230", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD6162", "vehicleCode": "0", "packageType": 1, "skuId": 1963517}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 43964943}}], "reactId": "1447095884", "group": 0, "groupSort": 0, "hot": 0, "hotType": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3921_0_263096_263096"]}, "minTPrice": 192, "minDPrice": 56, "modifySameVehicle": false, "minDOrinPrice": 75, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "自助取还", "category": 2, "type": 1, "code": "6", "sortNum": 1, "showLayer": 1, "colorCode": "1", "labelCode": "3866", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周三福利日 券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "共减38", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "pWay": "可选：免费送车至机场内", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_3159_"}], "recommendVehicleList": [{"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "4139", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.0T", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5212000c7mwubp7B39.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0l12000c7mwy55EE30.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000c7mwvvc7936.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000c7mx6u22402.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3012000c7mwv0sCA00.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0612000d5pibur948E.png", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "不支持", "carPlay": "部分车辆支持原厂互联/映射/CarLife/CarPlay", "chargeInterface": "部分车辆支持USB/AUX/Type-C/SD", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "vehiclesSetId": "70", "mediaTypes": [3, 2], "vehicleKey": "0_4139_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "snowTyre": {"description": "43975149:0;8462791:0;4689860:0;2851501:0;1978565:0;", "type": 0, "typeDesc": "不支持"}, "reverseImage": {"description": "43975149:1;8462791:0;4689860:1;2851501:1;1978565:1;", "type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"description": "43975149:1;8462791:0;4689860:1;2851501:1;1978565:1;", "type": 2, "typeDesc": "部分车辆支持"}, "shortEndurance": "km"}, {"brandEName": "大众", "brandName": "大众", "name": "大众宝来", "zhName": "大众宝来", "vehicleCode": "5348", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.2T-1.5L", "struct": "三厢车", "fuel": "95号或92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV1312000c55kc5rB85A.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5u12000ceg05xiDC3D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5b12000ceg059v0F7F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5712000ceg030e0DAB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4u12000cefzzmd2949.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000cefzw8jFD51.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1312000c55kc5rB85A.png?mark=yiche", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "guidSys": "部分车辆支持定速巡航/全速自适应巡航", "carPlay": "部分车辆支持CarPlay/CarLife/原厂互联/映射", "chargeInterface": "部分车辆支持USB/Type-C", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7742&app_ver=10.5", "vehiclesSetId": "70", "mediaTypes": [3], "vehicleKey": "0_5348_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "snowTyre": {"description": "8177667:0;4689833:0;43938112:0;28854589:0;", "type": 0, "typeDesc": "不支持"}, "reverseImage": {"description": "8177667:1;4689833:1;43938112:1;28854589:1;", "type": 1, "typeDesc": "支持"}, "reverseSensor": {"description": "8177667:1;4689833:1;43938112:1;28854589:1;", "type": 1, "typeDesc": "支持"}, "shortEndurance": "km"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田雷凌", "zhName": "丰田雷凌", "vehicleCode": "5349", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.2T-1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5p12000c55ltwq3DF6.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1p12000c6dj1p6A208.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4m12000c6dj1ih509C.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4m12000c6dj3cz87FB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0j12000c6dj7yg6A16.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5o12000c6diyzeA24C.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV7312000b5v2nt01A20.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "部分车辆支持全速自适应巡航", "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "chargeInterface": "支持USB/Type-C", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "carPhone": true, "autoBackUp": true, "vehiclesSetId": "70", "mediaTypes": [2], "vehicleKey": "0_5349_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "snowTyre": {"description": "8177905:0;4496691:0;49249257:0;", "type": 0, "typeDesc": "不支持"}, "reverseImage": {"description": "8177905:1;4496691:1;49249257:1;", "type": 1, "typeDesc": "支持"}, "reverseSensor": {"description": "8177905:0;4496691:0;49249257:1;", "type": 2, "typeDesc": "部分车辆支持"}, "shortEndurance": "km"}, {"brandEName": "日产", "brandName": "日产", "name": "日产轩逸(23及以前款)", "zhName": "日产轩逸(23及以前款)", "vehicleCode": "3159", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.6L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6c12000eodv4th22F2.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV7112000c5j6fs7D2EA.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3m12000c5j6qwk4321.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1r12000c5j6lb87547.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2p12000c5j714mD617.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0u12000c5j6fs96046.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2l12000befacgb63B5.jpg", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "guidSys": "部分车辆支持全速自适应巡航", "carPlay": "不支持", "chargeInterface": "部分车辆支持USB/AUX/Type-C", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=912&app_ver=10.5", "vehiclesSetId": "70", "mediaTypes": [3, 2], "vehicleKey": "0_3159_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "snowTyre": {"description": "8462749:0;1963517:0;43964943:0;", "type": 0, "typeDesc": "不支持"}, "reverseImage": {"description": "8462749:1;1963517:1;43964943:1;", "type": 1, "typeDesc": "支持"}, "reverseSensor": {"description": "8462749:0;1963517:1;43964943:0;", "type": 2, "typeDesc": "部分车辆支持"}, "shortEndurance": "km"}], "productGroupCodeUesd": "2", "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "shareVehicleInfo": {"vehicleName": "丰田卡罗拉（23款及以前）", "groupName": "丰田卡罗拉（23款及以前）", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "displacement": "1.2T-1.5L", "style": "", "vehicleImage": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "license": "", "minCurrentDailyPrice": 56}, "timeInterval": "711", "checkRequestTime": "1744786028471", "checkResponseTime": "1744786029182", "resBodySize": 49944, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 1315, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 1315, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1744786028470, "afterFetch": 1744786029785}}