{"baseResponse": {"extMap": {}, "cost": 365, "hasResult": true, "extraIndexTags": {"pcName": "中国", "rcId": "1", "rcName": "中国", "rCityName": "三亚", "rCityId": "43", "pCityId": "43", "pcId": "1", "pCityName": "三亚"}, "code": "200", "errorCode": "0", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "c34ca821-f0d6-40a6-b65e-404d6b10b86c", "isSuccess": true}, "rHub": 1, "isKlbData": true, "allVendorPriceCount": 4, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": false, "extras": {"packageLevelAB": "B", "goodsShelvesTwoSwitch": "1", "isLicensePlateHideShow": "0", "abVersion": "241008_DSJT_ykjpx|A,240419_DSJT_wyz24|B,230104_DSJT_rc101|B,230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,231218_DSJT_zzqh|B,231218_DSJT_zzqh|B,250325_DSJT_huojia20|C", "packageLevelSwitch": "1", "hasListSign": "5WsN0G61", "serverRequestId": "Ty6FF98P15x7fYXTVO2c", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 3, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "6", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "6", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}, {"sortNum": 9, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Childseat", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 1, "name": "儿童座椅"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝马", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_现代", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "现代", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "50-100", "sortNum": 2, "name": "¥100以下", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "200-400", "sortNum": 4, "name": "¥200-400", "groupCode": "Price", "itemCode": "Price_200-400"}, {"code": "400-99999", "sortNum": 5, "name": "¥400以上", "groupCode": "Price", "itemCode": "Price_400-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "PickReturn", "itemCode": "PickReturn_AirportPR", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "机场内门店取车"}, {"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 1, "positionCode": "1", "itemCode": "PickReturn_PickupInAirport", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "送车至机场"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "SelfService", "itemCode": "SelfService_UnSupport", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "非自助取还"}], "groupCode": "SelfService", "shortName": "自助取还", "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "mark": "hot", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "positionCode": "18", "name": "携程优选", "icon": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30147", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "骑仕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_37573", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "凯美租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_77147", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "美点租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_81889", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "八骏马租车"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "Sxw0548356Hyu9NTu2tt", "timeInterval": 541.326171875, "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [], "groupCode": "all", "hasResult": true}], "resBodySize": 17911, "labelCodes": ["4243", "4222", "4201", "3495", "3494", "3504", "3548", "3827", "3696", "3731", "3788", "3810", "4229", "3679", "3746"], "quickFilter": [{"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 1, "positionCode": "1", "itemCode": "PickReturn_PickupInAirport", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "送车至机场"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "mark": "hot", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "positionCode": "18", "name": "携程优选", "icon": ""}], "promotMap": {}, "requestInfo": {"rLongitude": 109.408508, "rDate": "20250418163000", "age": 30, "pCityId": 43, "returnDate": "/Date(1744965000000+0800)/", "sourceCountryId": 1, "pLatitude": 18.307269, "rLatitude": 18.307269, "pLongitude": 109.408508, "pDate": "20250416173000", "rCityId": 43, "pickupLocationName": "凤凰国际机场-国际航站楼", "returnLocationName": "凤凰国际机场-国际航站楼", "pickupDate": "/Date(1744795800000+0800)/"}, "allVehicleCount": 4, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"filterCode": "Vendor_0"}, "checkResponseTime": 1743414512902.121, "vehicleList": [{"skylight": "不支持", "vehicleKey": "2_4940_", "luggageNo": 2, "carPlay": "不支持", "displacement": "2.0T", "autoPark": true, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "1854602:0;"}, "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0r1200000mggmx198C.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "9", "zhName": "宝马Z4", "doorNo": 2, "autoParkDesc": {"type": 1, "typeDesc": "支持"}, "driveMode": "前置后驱", "chargeInterface": "不支持", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "1854602:1;"}, "shortEndurance": "km", "carPhone": true, "vehicleCode": "4940", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "宝马Z4", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "fuel": "95号", "passengerNo": 2, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "1854602:1;"}, "vehicleAccessoryImages": [], "transmissionType": 1, "brandName": "宝马", "oilType": 3, "struct": "软顶敞篷车", "groupName": "跑车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "宝马", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "109", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "不支持", "vehicleKey": "2_4020_", "luggageNo": 5, "carPlay": "不支持", "displacement": "1.4L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "10924733:0;"}, "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2q1200000mgg5qE85B.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "现代悦纳", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "不支持", "mediaTypes": [], "reverseImage": {"type": 0, "typeDesc": "不支持", "description": "10924733:0;"}, "shortEndurance": "km", "vehicleCode": "4020", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "现代悦纳", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 0, "typeDesc": "不支持", "description": "10924733:0;"}, "isHot": false, "vehicleAccessoryImages": [], "transmissionType": 1, "brandName": "现代", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "现代", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "64", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "不支持", "vehicleKey": "2_40_", "luggageNo": 5, "carPlay": "不支持", "displacement": "2.0T-2.8L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "1854627:0;"}, "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV611200000mktxiC6A7.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "5", "zhName": "奥迪A6L", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置四驱/前置前驱", "chargeInterface": "不支持", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "1854627:1;"}, "shortEndurance": "km", "vehicleCode": "40", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "奥迪A6L", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6e12000chdycozC2C3.jpg?mark=yiche", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "1854627:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV4612000chdy3qjACED.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5t12000chdyb8b37F3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4x12000chdxxndF18D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000chdy5r47540.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1312000chdycp46724.jpg?mark=yiche"], "transmissionType": 1, "brandName": "奥迪", "oilType": 6, "struct": "三厢车", "groupName": "豪华轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "奥迪", "licenseStyle": "2", "vehiclesSetId": "92", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "不支持", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "不支持", "vehicleKey": "2_5410_", "autoPark": false, "endurance": "工信部续航400km-600km", "fuelType": "纯电动", "charge": "快充0.5小时", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "2511418:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "比亚迪秦PLUS", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "不支持", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "2511418:1;"}, "shortEndurance": "400-600km", "carPhone": true, "vehicleCode": "5410", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "比亚迪秦PLUS", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "fuel": "", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "2511418:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "transmissionType": 1, "brandName": "比亚迪", "oilType": 5, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "比亚迪", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "67", "guidSys": "不支持", "transmissionName": "自动挡"}], "storeList": [{"pickOffLevel": 23665, "storeCode": "106896", "pickUpLevel": 23665}, {"pickOffLevel": -1, "storeCode": "106878", "pickUpLevel": -1}, {"pickOffLevel": 60298, "storeCode": "116954", "pickUpLevel": 60298}, {"pickOffLevel": 27021, "storeCode": "107104", "pickUpLevel": 27021}], "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": true, "isLastPage": true, "uniqSign": "12001105110000201356d9Xe7rQ643cK76A6lu96", "pHub": 1, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": []}, "checkRequestTime": 1743414512360.795, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "2977021820563739652", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a795a52-484281-143691", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1743414512932+0800)/"}, "isRecommend": true, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&listExtraMaps[packageLevelVersion]=1&listExtraMaps[goodsShelvesAB]=C&listExtraMaps[nonJumpFlow]=1&modify=&orderId=&pickupPointInfo=2025-04-16 17:30:00|凤凰国际机场-国际航站楼|43|18.307269|109.408508|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=&productGroupCodeFirst=&queryListCacheId=fd82521c-997d-409e-99f7-bc84cabfda3d&returnPointInfo=2025-04-18 16:30:00|凤凰国际机场-国际航站楼|43|18.307269|109.408508|||&sortType=1&uid=M2258803416@@PAGENUM@@1", "groupId": "18631/queryProducts?batch=", "networkCost": 545, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 545, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1743414512359, "afterFetch": 1743414512904, "hasRetry": false}}