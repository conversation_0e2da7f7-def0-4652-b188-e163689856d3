{"freePreAuthCode": "", "timeInterval": 737.766845703125, "hasResultWithoutFilter": true, "licenseInfo": {"pickupCountryName": "泰国", "returnCountryName": "泰国", "returnSupportCDLType": 2, "pickupSupportCDLType": 2, "pickupLicenseDesc": "泰国部分租车公司支持中国大陆驾照租车", "noticeMsg": "", "returnLicenseDesc": "泰国部分租车公司支持中国大陆驾照租车"}, "allVendorPriceCount": 30, "needRetry": false, "vehicleList": [{"transmissionName": "自动挡", "imageList": [], "vehicleKey": "531861_1001_0", "vehicleCode": "531861_1001_0", "name": "丰田 ", "zhName": "丰田 ", "passengerNo": 5, "brandId": 120, "hasConditioner": false, "fuelMode": "", "doorNo": 4, "isSpecialized": false, "groupCode": "1", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Toyota", "groupSubClassCode": "1001", "groupName": "经济轿车", "transmissionType": 1, "groupSubName": "小型轿车", "driveType": "0"}, {"transmissionName": "手动挡", "imageList": [], "vehicleKey": "570564_16_0", "vehicleCode": "570564_16_0", "name": "斯柯达 ", "zhName": "斯柯达 ", "passengerNo": 5, "brandId": 9, "hasConditioner": false, "fuelMode": "", "doorNo": 5, "isSpecialized": false, "groupCode": "4", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Skoda", "groupSubClassCode": "16", "groupName": "SUV", "transmissionType": 2, "groupSubName": "小型SUV", "driveType": "0"}, {"transmissionName": "手动挡", "imageList": [], "vehicleKey": "569535_39_0", "vehicleCode": "569535_39_0", "name": "大众 ", "zhName": "大众 ", "passengerNo": 5, "brandId": 98, "hasConditioner": false, "fuelMode": "", "doorNo": 5, "isSpecialized": false, "groupCode": "1", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "VW", "groupSubClassCode": "39", "groupName": "经济轿车", "transmissionType": 2, "groupSubName": "中小型轿车", "driveType": "0"}, {"transmissionName": "手动挡", "imageList": [], "vehicleKey": "553155_29_0", "vehicleCode": "553155_29_0", "name": "雷诺 ", "zhName": "雷诺 ", "passengerNo": 5, "brandId": 234, "hasConditioner": false, "fuelMode": "", "doorNo": 5, "isSpecialized": false, "groupCode": "2", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Renault", "groupSubClassCode": "29", "groupName": "舒适轿车", "transmissionType": 2, "groupSubName": "旅行车", "driveType": "0"}, {"transmissionName": "手动挡", "imageList": [], "vehicleKey": "531462_24_0", "vehicleCode": "531462_24_0", "name": "丰田 ", "zhName": "丰田 ", "passengerNo": 5, "brandId": 120, "hasConditioner": false, "fuelMode": "", "doorNo": 4, "isSpecialized": false, "groupCode": "8", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Toyota", "groupSubClassCode": "24", "groupName": "皮卡", "transmissionType": 2, "groupSubName": "皮卡", "driveType": "0"}, {"transmissionName": "自动挡", "imageList": [], "vehicleKey": "557019_13_0", "vehicleCode": "557019_13_0", "fuel": "电动", "name": "宝马 ", "zhName": "宝马 ", "passengerNo": 5, "brandId": 43, "hasConditioner": false, "fuelMode": "5", "doorNo": 4, "isSpecialized": false, "groupCode": "2", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "BMW", "groupSubClassCode": "13", "groupName": "舒适轿车", "transmissionType": 1, "groupSubName": "中型轿车", "driveType": "0"}, {"transmissionName": "自动挡", "imageList": [], "vehicleKey": "565601_12_0", "vehicleCode": "565601_12_0", "name": "铃木 ", "zhName": "铃木 ", "passengerNo": 4, "brandId": 97, "hasConditioner": false, "fuelMode": "", "doorNo": 4, "isSpecialized": false, "groupCode": "1", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Suzuki", "groupSubClassCode": "12", "groupName": "经济轿车", "transmissionType": 1, "groupSubName": "紧凑型轿车", "driveType": "0"}, {"transmissionName": "自动挡", "imageList": [], "vehicleKey": "531098_12_0", "vehicleCode": "531098_12_0", "name": "丰田 ", "zhName": "丰田 ", "passengerNo": 5, "brandId": 120, "hasConditioner": false, "fuelMode": "", "doorNo": 4, "isSpecialized": false, "groupCode": "1", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Toyota", "groupSubClassCode": "12", "groupName": "经济轿车", "transmissionType": 1, "groupSubName": "紧凑型轿车", "driveType": "0"}, {"transmissionName": "自动挡", "imageList": [], "vehicleKey": "531812_1001_0", "vehicleCode": "531812_1001_0", "name": "丰田 ", "zhName": "丰田 ", "passengerNo": 5, "brandId": 120, "hasConditioner": false, "fuelMode": "", "doorNo": 4, "isSpecialized": false, "groupCode": "1", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Toyota", "groupSubClassCode": "1001", "groupName": "经济轿车", "transmissionType": 1, "groupSubName": "小型轿车", "driveType": "0"}, {"transmissionName": "自动挡", "imageList": [], "vehicleKey": "545574_12_0", "vehicleCode": "545574_12_0", "name": "本田思域", "zhName": "本田思域", "passengerNo": 5, "brandId": 193, "hasConditioner": false, "fuelMode": "", "doorNo": 4, "isSpecialized": false, "groupCode": "1", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Honda", "groupSubClassCode": "12", "groupName": "经济轿车", "transmissionType": 1, "groupSubName": "紧凑型轿车", "driveType": "0"}, {"transmissionName": "手动挡", "imageList": [], "vehicleKey": "530496_25_0", "vehicleCode": "530496_25_0", "name": "丰田 ", "zhName": "丰田 ", "passengerNo": 12, "brandId": 120, "hasConditioner": false, "fuelMode": "", "doorNo": 4, "isSpecialized": false, "groupCode": "10", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Toyota", "groupSubClassCode": "25", "groupName": "其他车型", "transmissionType": 2, "groupSubName": "厢式货车", "driveType": "0"}, {"transmissionName": "手动挡", "imageList": [], "vehicleKey": "541864_31_0", "vehicleCode": "541864_31_0", "name": "欧宝 ", "zhName": "欧宝 ", "passengerNo": 5, "brandId": 165, "hasConditioner": false, "fuelMode": "", "doorNo": 5, "isSpecialized": false, "groupCode": "4", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Opel", "groupSubClassCode": "31", "groupName": "SUV", "transmissionType": 2, "groupSubName": "豪华SUV", "driveType": "0"}, {"transmissionName": "手动挡", "imageList": [], "vehicleKey": "570788_13_0", "vehicleCode": "570788_13_0", "name": "斯柯达 ", "zhName": "斯柯达 ", "passengerNo": 5, "brandId": 9, "hasConditioner": false, "fuelMode": "", "doorNo": 5, "isSpecialized": false, "groupCode": "2", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Skoda", "groupSubClassCode": "13", "groupName": "舒适轿车", "transmissionType": 2, "groupSubName": "中型轿车", "driveType": "0"}, {"transmissionName": "自动挡", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "vehicleKey": "531049_13_0", "vehicleCode": "531049_13_0", "name": "丰田凯美瑞", "zhName": "丰田凯美瑞", "imageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "brandId": 120, "passengerNo": 5, "hasConditioner": false, "doorNo": 4, "isSpecialized": false, "fuelMode": "", "groupCode": "2", "userRealImageCount": 0, "luggageNo": 3, "brandEName": "Toyota", "groupSubClassCode": "13", "groupName": "舒适轿车", "transmissionType": 1, "groupSubName": "中型轿车", "driveType": "0"}, {"transmissionName": "自动挡", "imageList": [], "vehicleKey": "532036_18_0", "vehicleCode": "532036_18_0", "name": "丰田 ", "zhName": "丰田 ", "passengerNo": 7, "brandId": 120, "hasConditioner": false, "fuelMode": "", "doorNo": 4, "isSpecialized": false, "groupCode": "4", "userRealImageCount": 0, "luggageNo": 0, "brandEName": "Toyota", "groupSubClassCode": "18", "groupName": "SUV", "transmissionType": 1, "groupSubName": "中型SUV", "driveType": "0"}], "commNotices": [], "isAll": false, "isMoreAge": false, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"sortNum": 1, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_5", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "7座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 2, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 100000, "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动档"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "FuelAndDriveGroupCode", "itemCode": "FuelAndDriveGroupCode_elec", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}], "groupCode": "FuelAndDriveGroupCode", "shortName": "能源驱动", "bitwiseType": 2, "name": "能源驱动"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 0, "groupCode": "Brand_0", "itemCode": "Brand_120", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 1, "groupCode": "Brand_0", "itemCode": "Brand_9", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "斯柯达", "icon": "//pages.c-ctrip.com/carisd/brandlogo/sikeda.png"}, {"sortNum": 2, "groupCode": "Brand_0", "itemCode": "Brand_98", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 3, "groupCode": "Brand_0", "itemCode": "Brand_234", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "雷诺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/leinuo.png"}, {"sortNum": 4, "groupCode": "Brand_0", "itemCode": "Brand_43", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 5, "groupCode": "Brand_0", "itemCode": "Brand_97", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "铃木", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingmu.png"}, {"sortNum": 6, "groupCode": "Brand_0", "itemCode": "Brand_193", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 7, "groupCode": "Brand_0", "itemCode": "Brand_165", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "欧宝", "icon": "//pages.c-ctrip.com/carisd/brandlogo/oubao.png"}], "groupCode": "Brand_0", "shortName": "车辆品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-100", "sortNum": 0, "name": "¥100以下", "groupCode": "Price", "itemCode": "Price_0-100"}, {"code": "100-200", "sortNum": 0, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-300", "sortNum": 0, "name": "¥200-300", "groupCode": "Price", "itemCode": "Price_200-300"}, {"code": "300-400", "sortNum": 0, "name": "¥300-400", "groupCode": "Price", "itemCode": "Price_300-400"}, {"code": "400-99999", "sortNum": 0, "name": "¥400以上", "groupCode": "Price", "itemCode": "Price_400-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "StoreWay", "itemCode": "StoreWay_Contact", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "抵达后联系店员"}, {"sortNum": 2, "groupCode": "StoreWay", "quickSortNum": 30000, "itemCode": "StoreWay_PickupOnDoor", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 2, "groupCode": "StoreWay", "itemCode": "StoreWay_Warking", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "步行前往"}], "groupCode": "StoreWay", "shortName": "取车方式", "bitwiseType": 2, "name": "取车方式"}, {"shortName": "门店位置", "sortNum": 3, "bitwiseType": 2, "name": "门店位置", "groupCode": "Location", "filterItems": [{"sortNum": 1, "groupCode": "Location", "quickSortNum": 80000, "itemCode": "Location_OnAirport", "binaryDigit": 3, "isQuickItem": true, "bitwiseType": 2, "name": "机场店"}, {"sortNum": 3, "groupCode": "Location", "itemCode": "Distance_M500", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "直线500米内"}, {"sortNum": 4, "groupCode": "Location", "itemCode": "Distance_K1", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "直线1公里内"}, {"sortNum": 5, "groupCode": "Location", "itemCode": "Distance_K2", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "直线2公里内"}, {"sortNum": 6, "groupCode": "Location", "itemCode": "Distance_K5", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "直线5公里内"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "StoreService", "quickSortNum": 120000, "itemCode": "StoreService_3564", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "立即确认"}, {"sortNum": 2, "groupCode": "StoreService", "quickSortNum": 90000, "itemCode": "StoreService_3563", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "免费取消"}, {"sortNum": 3, "groupCode": "StoreService", "itemCode": "StoreService_3562", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_3599", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 1, "name": "自助取车"}, {"sortNum": 6, "groupCode": "StoreService", "itemCode": "StoreService_3554", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "含第三者保障"}, {"sortNum": 7, "groupCode": "StoreService", "itemCode": "StoreService_3553", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 1, "name": "0起赔额"}, {"sortNum": 9, "groupCode": "StoreService", "itemCode": "StoreService_3977", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "满油/电取还"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"shortName": "支付方式", "sortNum": 5, "bitwiseType": 2, "name": "支付方式", "groupCode": "PayMode", "filterItems": [{"sortNum": 2, "groupCode": "PayMode", "itemCode": "PayMode_Prepaid", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "在线支付"}]}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 99, "groupCode": "Activity", "itemCode": "Activity_3834", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "四月特惠"}], "groupCode": "Activity", "shortName": "优惠活动", "bitwiseType": 2, "name": "优惠活动"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "CreditCard", "quickSortNum": 10000, "itemCode": "CreditCard_DepositFree", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "芝麻免押"}, {"sortNum": 4, "groupCode": "CreditCard", "itemCode": "CreditCard_SupportUnionLogoNew", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "支持银联"}, {"sortNum": 5, "groupCode": "CreditCard", "itemCode": "CreditCard_SupportNoChip", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "支持非芯片信用卡"}, {"sortNum": 5, "groupCode": "CreditCard", "itemCode": "CreditCard_SupportNoEmbossed", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "支持非凸字信用卡"}], "groupCode": "CreditCard", "shortName": "押金方式", "bitwiseType": 2, "name": "押金方式"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 0, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1001", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "仅需中国驾照原件"}, {"sortNum": 1, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1002", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "支持中国驾照原件+驾照国际翻译认证件"}, {"sortNum": 2, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1003", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "支持中国驾照原件+车行翻译件"}, {"sortNum": 3, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1004", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "支持中国驾照原件+当地语言公证件"}, {"sortNum": 4, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1014", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "支持国际驾照IDP+签发国当地驾照"}, {"sortNum": 5, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1006", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "支持香港驾照"}, {"sortNum": 6, "groupCode": "DriverLience", "itemCode": "DriverLience_47", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "仅需当地驾照"}], "groupCode": "DriverLience", "shortName": "驾照要求", "bitwiseType": 2, "name": "驾照要求"}, {"shortName": "点评", "sortNum": 9, "bitwiseType": 2, "name": "点评", "groupCode": "Comment", "filterItems": [{"sortNum": 4, "groupCode": "Comment", "itemCode": "Comment_3.5", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "3.5分以上"}, {"sortNum": 4, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}, {"sortNum": 4, "groupCode": "Comment", "itemCode": "Comment_4.5", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.5分以上"}]}, {"sortNum": 10, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "itemCode": "Vendor_SD0012", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "THAI"}, {"sortNum": 7, "promotion": {"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, "groupCode": "Vendor_0", "itemCode": "Vendor_SD1576", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "Budget Thai"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "resBodySize": 187521, "uniqSign": "5bfdaf74-1756-4709-a062-62d07ee6286c", "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "价格 低→高", "type": 2}], "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}}, "supportUnionLogoCode": "", "baseResponse": {"extMap": {"allCost": "616.0", "start": "2025-04-24 16:46:53", "end": "2025-04-24 16:46:53"}, "cost": 627, "hasResult": true, "extraIndexTags": {"pcName": "泰国", "rcId": "4", "bizVendorCode": "14088277,14088020", "rcName": "泰国", "normalCount": "2", "rCityId": "359", "rCityName": "曼谷", "c_blank": "0", "pcId": "4", "pCityId": "359", "pCityName": "曼谷"}, "code": "201", "apiResCodes": [], "returnMsg": "SUCCESS", "requestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "isSuccess": true}, "filterItemExts": [], "promptInfos": [], "ResponseStatus": {"Extension": [{"Value": "4567286738242181691", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a78c6ef-484856-396194", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1745484413975+0800)/"}, "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "同组车型是指按照车型功能、空间大小、座位数和车门数划分的紧凑型轿车、中型轿车、小型SUV等等这些分类。您在网上预订时选择的车并不能保证到店就一定取到一模一样的，可能会得到同组车型中的其他车。"}, "cover": "https://ak-d.tripcdn.com/images/1tg1u12000dwdf1uw1DCD.png", "carProtection": {"title": "取车保障", "description": "在库存充足的情况下，车行会尽量提供您在预订时所选的车型\n如果这款车暂时没有库存，车行便会为您提供同车型组内的其他车型\n若因门店原因需要调整车型，不额外收取车型变更费用"}, "vedio": "https://ak-v.tripcdn.com/videos/9D0g2r000001j0rrk1E0A.mp4", "cases": [{"vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含", "vehicleGroupCode": "default"}, {"vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特", "vehicleGroupCode": "D"}, {"vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特", "vehicleGroupCode": "S"}, {"vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者", "vehicleGroupCode": "R"}]}, "requestInfo": {"rLongitude": 100.750112, "rDate": "20250510120000", "age": 30, "pCityId": 359, "returnDate": "/Date(1746849600000+0800)/", "sourceCountryId": 1, "pLatitude": 13.689999, "rLatitude": 13.689999, "pLongitude": 100.750112, "pDate": "20250508120000", "rCityId": 359, "pickupLocationName": "素万那普国际机场", "returnLocationName": "素万那普国际机场", "pickupDate": "/Date(1746676800000+0800)/", "rentalDay": 2}, "isFromSearch": true, "productGroupsHashCode": "cd191a83e76a7d738f17c0997b931c171c74995cf6b464ae7e0b7f40e54543b118b9e7b0", "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "labelCodes": ["3562", "3561", "3560", "3592", "3581", "3556", "3599", "3554", "3553", "3850", "3689", "3942", "3834"], "quickFilter": [{"sortNum": 4, "groupCode": "CreditCard", "quickSortNum": 10000, "itemCode": "CreditCard_DepositFree", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "芝麻免押"}, {"sortNum": 2, "groupCode": "StoreWay", "quickSortNum": 30000, "itemCode": "StoreWay_PickupOnDoor", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 1, "groupCode": "Location", "quickSortNum": 80000, "itemCode": "Location_OnAirport", "binaryDigit": 3, "isQuickItem": true, "bitwiseType": 2, "name": "机场店"}, {"sortNum": 2, "groupCode": "StoreService", "quickSortNum": 90000, "itemCode": "StoreService_3563", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "免费取消"}, {"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 100000, "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动档"}, {"sortNum": 1, "groupCode": "StoreService", "quickSortNum": 120000, "itemCode": "StoreService_3564", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "立即确认"}], "allVehicleCount": 15, "extras": {"oriRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "isNewSort": "1", "abVersion": "240401_DSJT_cjlby|B,240802_DSJT_hdyh|A", "oriRequestTime": "2025-04-24 16:46:53", "isVehicle2": "1"}, "checkResponseTime": 1745484413822.5598, "checkRequestTime": 1745484413084.793, "productGroups": [{"sortNum": 1, "productList": [{"vehicleCode": "531861_1001_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "531861_1001_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 36022, "pickWayInfo": 0, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkANgT4oR6lEEkm689yFmMc0MQ+XjphhR1yHgKP8fZTMdEkQFNVEQAkBi50a39KPwxDt09qG3Omvjill66ftGCfJvva0gAdyQvqUcvayu7L3/Cq3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795854", "unionCardFilter": {}, "kVehicleId": 531861, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531861", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_-1_228795_-1_531861_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 0, "age": 30, "skuId": 68795854, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "到达大厅，步行可达", "ctripVehicleCode": "531861", "platformCode": "", "reactId": "1646539174", "priceInfo": {"currentTotalPrice": 346, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 173, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "531861_1001_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": 619522, "pCityId": 36022, "pickWayInfo": 1, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkANgT4oR6lEEkm689yFmMc0MQ+XjphhR1yHgKP8fZTMdEkQFNVEQAkBi50a39KPwxDt09qG3Omvjj+AMfVoSy2vfva0gAdyQvqUcvayu7L3/Cq3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795854", "unionCardFilter": {}, "kVehicleId": 531861, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531861", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": 619522, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_619522_228795_619522_531861_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 1, "age": 30, "skuId": 68795854, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "531861", "platformCode": "", "reactId": "1646539132", "priceInfo": {"currentTotalPrice": 346, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 173, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["68795854"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1646539640", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "similarVehicleInfos": [{"vehicleCode": "531861", "vehicleName": "丰田 Yaris <PERSON>"}], "vendorName": "Budget Thai", "bizVendorCode": "14088277"}], "sortNum": 0, "lowestDistance": 0.9677, "minTPrice": 346, "vehicleKey": "531861_1001_0", "lowestPrice": 173, "highestPrice": 173}, {"vehicleCode": "569535_39_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "569535_39_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 359, "pickWayInfo": 0, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awS7jGEdi1ZdoiK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SHSa92Jxhsuk71OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534824", "unionCardFilter": {}, "kVehicleId": 569535, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "569535", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_-1_1234122_-1_569535_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 0, "age": 30, "skuId": 110534824, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "机场店，抵达后联系店员", "ctripVehicleCode": "569535", "platformCode": "", "reactId": "16465394018", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "569535_39_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": 737542, "pCityId": 359, "pickWayInfo": 1, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awS7jGEdi1ZdoiK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SHSa92Jxhsuk71OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534824", "unionCardFilter": {}, "kVehicleId": 569535, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "569535", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": 737542, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_737542_1234122_737542_569535_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 1, "age": 30, "skuId": 110534824, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "569535", "platformCode": "", "reactId": "16465394521", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["110534824"], "introduce": "同组车型最低价"}, "maximumCommentCount": 20, "reactId": "1646539643", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "similarVehicleInfos": [{"vehicleCode": "569535", "vehicleName": "大众 Voyage"}], "vendorName": "THAI", "bizVendorCode": "14088020"}], "sortNum": 2, "lowestDistance": 0.2652, "minTPrice": 458, "vehicleKey": "569535_39_0", "lowestPrice": 229, "highestPrice": 229}, {"vehicleCode": "565601_12_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "565601_12_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 359, "pickWayInfo": 0, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awSzTsXA4m/IGyK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SN+hF/2POecK71OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534817", "unionCardFilter": {}, "kVehicleId": 565601, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "565601", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_-1_1234122_-1_565601_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 0, "age": 30, "skuId": 110534817, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "机场店，抵达后联系店员", "ctripVehicleCode": "565601", "platformCode": "", "reactId": "16465395124", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "565601_12_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": 737542, "pCityId": 359, "pickWayInfo": 1, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awSzTsXA4m/IGyK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SN+hF/2POecK71OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534817", "unionCardFilter": {}, "kVehicleId": 565601, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "565601", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": 737542, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_737542_1234122_737542_565601_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 1, "age": 30, "skuId": 110534817, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "565601", "platformCode": "", "reactId": "16465395426", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["110534817"], "introduce": "同组车型最低价"}, "maximumCommentCount": 20, "reactId": "1646539644", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "similarVehicleInfos": [{"vehicleCode": "565601", "vehicleName": "铃木 Celiro"}], "vendorName": "THAI", "bizVendorCode": "14088020"}], "sortNum": 6, "lowestDistance": 0.2652, "minTPrice": 458, "vehicleKey": "565601_12_0", "lowestPrice": 229, "highestPrice": 229}, {"vehicleCode": "531098_12_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "531098_12_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 36022, "pickWayInfo": 0, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkADVX7YI7MpaTYDUrP9BG5Y8Q+XjphhR1yHgKP8fZTMdGxY5gNX/5Q+S50a39KPwxDt09qG3Omvjill66ftGCfJk9ohfY0g7o/dPou+e/hxL6q3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795826", "unionCardFilter": {}, "kVehicleId": 531098, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531098", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_-1_228795_-1_531098_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 0, "age": 30, "skuId": 68795826, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "到达大厅，步行可达", "ctripVehicleCode": "531098", "platformCode": "", "reactId": "16465394923", "priceInfo": {"currentTotalPrice": 426, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 213, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "531098_12_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": 619522, "pCityId": 36022, "pickWayInfo": 1, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkADVX7YI7MpaTYDUrP9BG5Y8Q+XjphhR1yHgKP8fZTMdGxY5gNX/5Q+S50a39KPwxDt09qG3Omvjj+AMfVoSy2vU9ohfY0g7o/dPou+e/hxL6q3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795826", "unionCardFilter": {}, "kVehicleId": 531098, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531098", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": 619522, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_619522_228795_619522_531098_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 1, "age": 30, "skuId": 68795826, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "531098", "platformCode": "", "reactId": "16465395225", "priceInfo": {"currentTotalPrice": 426, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 213, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["68795826"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1646539642", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "similarVehicleInfos": [{"vehicleCode": "531098", "vehicleName": "丰田 Co<PERSON>a <PERSON>is"}], "vendorName": "Budget Thai", "bizVendorCode": "14088277"}], "sortNum": 7, "lowestDistance": 0.9677, "minTPrice": 426, "vehicleKey": "531098_12_0", "lowestPrice": 213, "highestPrice": 213}, {"vehicleCode": "531812_1001_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "531812_1001_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 36022, "pickWayInfo": 0, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkAiV5wI9HR6NYQuJuMDRPIhcQ+XjphhR1yHgKP8fZTMdHMVXIMg2ZT/y50a39KPwxDt09qG3Omvjj+AMfVoSy2vdgCWRiWjjzurYVFh/Uo3Laq3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795819", "unionCardFilter": {}, "kVehicleId": 531812, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531812", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_-1_228795_-1_531812_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 0, "age": 30, "skuId": 68795819, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "到达大厅，步行可达", "ctripVehicleCode": "531812", "platformCode": "", "reactId": "16465394722", "priceInfo": {"currentTotalPrice": 376, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 188, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "531812_1001_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": 619522, "pCityId": 36022, "pickWayInfo": 1, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkAiV5wI9HR6NYQuJuMDRPIhcQ+XjphhR1yHgKP8fZTMdHMVXIMg2ZT/y50a39KPwxDt09qG3Omvjill66ftGCfJtgCWRiWjjzurYVFh/Uo3Laq3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795819", "unionCardFilter": {}, "kVehicleId": 531812, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531812", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": 619522, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_619522_228795_619522_531812_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 1, "age": 30, "skuId": 68795819, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "531812", "platformCode": "", "reactId": "16465395929", "priceInfo": {"currentTotalPrice": 376, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 188, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["68795819"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1646539641", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "similarVehicleInfos": [{"vehicleCode": "531812", "vehicleName": "丰田 Vios E"}], "vendorName": "Budget Thai", "bizVendorCode": "14088277"}], "sortNum": 8, "lowestDistance": 0.9677, "minTPrice": 376, "vehicleKey": "531812_1001_0", "lowestPrice": 188, "highestPrice": 188}, {"vehicleCode": "545574_12_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "545574_12_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 36022, "pickWayInfo": 0, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkARp3W5vVKHj4A4nQIJl2g08Q+XjphhR1yHgKP8fZTMdFLTztjWXl72C50a39KPwxDt09qG3Omvjill66ftGCfJvLQvpa39Cout02nMhqEq/6q3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795833", "unionCardFilter": {}, "kVehicleId": 545574, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "545574", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_-1_228795_-1_545574_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 0, "age": 30, "skuId": 68795833, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 64, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "到达大厅，步行可达", "ctripVehicleCode": "545574", "platformCode": "", "reactId": "1646539227", "priceInfo": {"currentTotalPrice": 596, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 298, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "545574_12_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": 619522, "pCityId": 36022, "pickWayInfo": 1, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkARp3W5vVKHj4A4nQIJl2g08Q+XjphhR1yHgKP8fZTMdFLTztjWXl72C50a39KPwxDt09qG3Omvjj+AMfVoSy2vfLQvpa39Cout02nMhqEq/6q3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795833", "unionCardFilter": {}, "kVehicleId": 545574, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "545574", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": 619522, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_619522_228795_619522_545574_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 1, "age": 30, "skuId": 68795833, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 64, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "545574", "platformCode": "", "reactId": "1646539206", "priceInfo": {"currentTotalPrice": 596, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 298, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["68795833"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1646539645", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "similarVehicleInfos": [{"vehicleCode": "545574", "vehicleName": "本田 Civic"}], "vendorName": "Budget Thai", "bizVendorCode": "14088277"}], "sortNum": 9, "lowestDistance": 0.9677, "minTPrice": 596, "vehicleKey": "545574_12_0", "lowestPrice": 298, "highestPrice": 298}], "groupCode": "1", "groupName": "经济轿车", "dailyPrice": 173, "allowMerge": true, "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/economy.png"}, {"sortNum": 2, "productList": [{"vehicleCode": "553155_29_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "553155_29_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 359, "pickWayInfo": 0, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWcAbj6b7Ly9awQfyqcXI4CVYCK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4bbmJmDGVY3xYde3d73aff2VNn4/mAjle4yYGkuO+eXSRpfVy+O0wEHBEgtXUptnJQ=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "72891757", "unionCardFilter": {}, "kVehicleId": 553155, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "553155", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_-1_1234122_-1_553155_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 0, "age": 30, "skuId": 72891757, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "机场店，抵达后联系店员", "ctripVehicleCode": "553155", "platformCode": "", "reactId": "16465393213", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "553155_29_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": 737542, "pCityId": 359, "pickWayInfo": 1, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWcAbj6b7Ly9awQfyqcXI4CVYCK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4bbmJmDGVY3xYde3d73aff2VNn4/mAjle4yYGkuO+eXSRpfVy+O0wEHBEgtXUptnJQ=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "72891757", "unionCardFilter": {}, "kVehicleId": 553155, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "553155", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": 737542, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_737542_1234122_737542_553155_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 1, "age": 30, "skuId": 72891757, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "553155", "platformCode": "", "reactId": "16465393515", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["72891757"], "introduce": "同组车型最低价"}, "maximumCommentCount": 20, "reactId": "1646539640", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "similarVehicleInfos": [{"vehicleCode": "553155", "vehicleName": "雷诺 Clio Grandtour"}], "vendorName": "THAI", "bizVendorCode": "14088020"}], "sortNum": 3, "lowestDistance": 0.2652, "minTPrice": 458, "vehicleKey": "553155_29_0", "lowestPrice": 229, "highestPrice": 229}, {"vehicleCode": "557019_13_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "557019_13_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 359, "pickWayInfo": 0, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWcAbj6b7Ly9awR3zsQJvlfd9yK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4bbmJmDGVY3xfQR4z8tlXG3VNn4/mAjle4yYGkuO+eXSRpfVy+O0wEHBEgtXUptnJQ=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "72891736", "unionCardFilter": {}, "kVehicleId": 557019, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "557019", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_-1_1234122_-1_557019_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 0, "age": 30, "skuId": 72891736, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "FuelAndDriveGroupCode", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "机场店，抵达后联系店员", "ctripVehicleCode": "557019", "platformCode": "", "reactId": "1646539100", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "557019_13_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": 737542, "pCityId": 359, "pickWayInfo": 1, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWcAbj6b7Ly9awR3zsQJvlfd9yK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4bbmJmDGVY3xfQR4z8tlXG3VNn4/mAjle4yYGkuO+eXSRpfVy+O0wEHBEgtXUptnJQ=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "72891736", "unionCardFilter": {}, "kVehicleId": 557019, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "557019", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": 737542, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_737542_1234122_737542_557019_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 1, "age": 30, "skuId": 72891736, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "FuelAndDriveGroupCode", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "557019", "platformCode": "", "reactId": "1646539111", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["72891736"], "introduce": "同组车型最低价"}, "maximumCommentCount": 20, "reactId": "1646539641", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "similarVehicleInfos": [{"vehicleCode": "557019", "vehicleName": "宝马 I3"}], "vendorName": "THAI", "bizVendorCode": "14088020"}], "sortNum": 5, "lowestDistance": 0.2652, "minTPrice": 458, "vehicleKey": "557019_13_0", "lowestPrice": 229, "highestPrice": 229}, {"vehicleCode": "570788_13_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "570788_13_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 359, "pickWayInfo": 0, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awRQsZJFjwWTHCK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SN85wxmUoeEY71OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534831", "unionCardFilter": {}, "kVehicleId": 570788, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "570788", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_-1_1234122_-1_570788_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 0, "age": 30, "skuId": 110534831, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "机场店，抵达后联系店员", "ctripVehicleCode": "570788", "platformCode": "", "reactId": "16465393012", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "570788_13_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": 737542, "pCityId": 359, "pickWayInfo": 1, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awRQsZJFjwWTHCK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SN85wxmUoeEY71OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534831", "unionCardFilter": {}, "kVehicleId": 570788, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "570788", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": 737542, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_737542_1234122_737542_570788_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 1, "age": 30, "skuId": 110534831, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "570788", "platformCode": "", "reactId": "16465393414", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["110534831"], "introduce": "同组车型最低价"}, "maximumCommentCount": 20, "reactId": "1646539642", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "similarVehicleInfos": [{"vehicleCode": "570788", "vehicleName": "斯柯达 Rapid"}], "vendorName": "THAI", "bizVendorCode": "14088020"}], "sortNum": 12, "lowestDistance": 0.2652, "minTPrice": 458, "vehicleKey": "570788_13_0", "lowestPrice": 229, "highestPrice": 229}, {"vehicleCode": "531049_13_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "531049_13_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 36022, "pickWayInfo": 0, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkA8hm0DpAhmrvxHN3L8jPVpMQ+XjphhR1yHgKP8fZTMdE1+6oqyi0I0y50a39KPwxDt09qG3Omvjill66ftGCfJpxi4w4xc6ainI9QlzdcjtKq3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795840", "unionCardFilter": {}, "kVehicleId": 531049, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531049", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_-1_228795_-1_531049_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 0, "age": 30, "skuId": 68795840, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "到达大厅，步行可达", "ctripVehicleCode": "531049", "platformCode": "", "reactId": "1646539238", "priceInfo": {"currentTotalPrice": 852, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 426, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "531049_13_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": 619522, "pCityId": 36022, "pickWayInfo": 1, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkA8hm0DpAhmrvxHN3L8jPVpMQ+XjphhR1yHgKP8fZTMdE1+6oqyi0I0y50a39KPwxDt09qG3Omvjj+AMfVoSy2vZxi4w4xc6ainI9QlzdcjtKq3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795840", "unionCardFilter": {}, "kVehicleId": 531049, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531049", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": 619522, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_619522_228795_619522_531049_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 1, "age": 30, "skuId": 68795840, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "531049", "platformCode": "", "reactId": "1646539259", "priceInfo": {"currentTotalPrice": 852, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 426, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["68795840"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1646539643", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "similarVehicleInfos": [{"vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "vehicleCode": "531049", "vehicleName": "丰田 Camry"}], "vendorName": "Budget Thai", "bizVendorCode": "14088277"}], "sortNum": 13, "lowestDistance": 0.9677, "minTPrice": 852, "vehicleKey": "531049_13_0", "lowestPrice": 426, "highestPrice": 426}], "groupCode": "2", "groupName": "舒适轿车", "dailyPrice": 229, "allowMerge": true, "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/standard.png"}, {"sortNum": 4, "productList": [{"vehicleCode": "570564_16_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "570564_16_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 359, "pickWayInfo": 0, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWcAbj6b7Ly9awS0CqA3pbSDUyK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4bbmJmDGVY3xTuX5sPMlEOXVNn4/mAjle4yYGkuO+eXSRpfVy+O0wEHBEgtXUptnJQ=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "72891750", "unionCardFilter": {}, "kVehicleId": 570564, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "570564", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_-1_1234122_-1_570564_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 0, "age": 30, "skuId": 72891750, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "机场店，抵达后联系店员", "ctripVehicleCode": "570564", "platformCode": "", "reactId": "16465395728", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "570564_16_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": 737542, "pCityId": 359, "pickWayInfo": 1, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWcAbj6b7Ly9awS0CqA3pbSDUyK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4bbmJmDGVY3xTuX5sPMlEOXVNn4/mAjle4yYGkuO+eXSRpfVy+O0wEHBEgtXUptnJQ=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "72891750", "unionCardFilter": {}, "kVehicleId": 570564, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "570564", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": 737542, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_737542_1234122_737542_570564_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 1, "age": 30, "skuId": 72891750, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "570564", "platformCode": "", "reactId": "16465395627", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["72891750"], "introduce": "同组车型最低价"}, "maximumCommentCount": 20, "reactId": "1646539640", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "similarVehicleInfos": [{"vehicleCode": "570564", "vehicleName": "斯柯达 Kodiaq"}], "vendorName": "THAI", "bizVendorCode": "14088020"}], "sortNum": 1, "lowestDistance": 0.2652, "minTPrice": 458, "vehicleKey": "570564_16_0", "lowestPrice": 229, "highestPrice": 229}, {"vehicleCode": "541864_31_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "541864_31_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 359, "pickWayInfo": 0, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awTe0VQLuCbV9SK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SIj6NSf15uT171OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534845", "unionCardFilter": {}, "kVehicleId": 541864, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "541864", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_-1_1234122_-1_541864_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 0, "age": 30, "skuId": 110534845, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "机场店，抵达后联系店员", "ctripVehicleCode": "541864", "platformCode": "", "reactId": "1646539185", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "541864_31_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": 737542, "pCityId": 359, "pickWayInfo": 1, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awTe0VQLuCbV9SK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SIj6NSf15uT171OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534845", "unionCardFilter": {}, "kVehicleId": 541864, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "541864", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": 737542, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_737542_1234122_737542_541864_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 1, "age": 30, "skuId": 110534845, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Brand_0", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "541864", "platformCode": "", "reactId": "1646539153", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["110534845"], "introduce": "同组车型最低价"}, "maximumCommentCount": 20, "reactId": "1646539641", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "similarVehicleInfos": [{"vehicleCode": "541864", "vehicleName": "欧宝 Mokka"}], "vendorName": "THAI", "bizVendorCode": "14088020"}], "sortNum": 11, "lowestDistance": 0.2652, "minTPrice": 458, "vehicleKey": "541864_31_0", "lowestPrice": 229, "highestPrice": 229}, {"vehicleCode": "532036_18_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "532036_18_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 36022, "pickWayInfo": 0, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkAsJmkWLTb02RWJeckttFA8sQ+XjphhR1yHgKP8fZTMdH9VQyRtmaT6y50a39KPwxDt09qG3Omvjill66ftGCfJpxi4w4xc6aihHbDXZMSnPaq3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795847", "unionCardFilter": {}, "kVehicleId": 532036, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "532036", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_-1_228795_-1_532036_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 0, "age": 30, "skuId": 68795847, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "到达大厅，步行可达", "ctripVehicleCode": "532036", "platformCode": "", "reactId": "16465394420", "priceInfo": {"currentTotalPrice": 1002, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 501, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "Budget Thai", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "reference": {"vehicleKey": "532036_18_0", "kPSId": 228795, "largeRadiusVersion": "0", "pLev": 619522, "pCityId": 36022, "pickWayInfo": 1, "kRSId": 228795, "isEasyLife": false, "payMode": 2, "priceVersion": "AWkAsJmkWLTb02RWJeckttFA8sQ+XjphhR1yHgKP8fZTMdH9VQyRtmaT6y50a39KPwxDt09qG3Omvjj+AMfVoSy2vZxi4w4xc6aihHbDXZMSnPaq3+kGNH0Uo3dBZ1jEEeUKiQ6K1WL2JleHfPRimHdgqw==", "packageId": "393422", "rStoreCode": "228795", "labels": [], "vendorCode": "SD1576", "productCode": "68795847", "unionCardFilter": {}, "kVehicleId": 532036, "rCityId": 36022, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "532036", "bizVendorCode": "14088277", "vendorVehicleCode": "", "kVId": 14088277, "rLev": 619522, "noDepositFilter": {}, "pStoreCode": "228795", "bomCode": "228795_619522_228795_619522_532036_CDW_FRFB_TPL_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 393422, "returnWayInfo": 1, "age": 30, "skuId": 68795847, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": false, "maxAge": 65, "extMap": {"confirmRightNow": "false", "distance": "0.9677", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": false, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "7小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要7小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 20, "groupCode": "MarketGroup1327", "code": "2", "title": "含1名额外驾驶员", "colorCode": "2", "type": 1, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "labelCode": "3556"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 65, "groupCode": "MarketGroup1327", "code": "2", "title": "含第三者保障", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含第三者保障", "labelCode": "3554"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 120, "groupCode": "MarketGroup1325", "code": "4", "title": "不支持中国大陆驾照", "colorCode": "4", "type": 2, "description": "供应商不支持使用中国大陆驾照租车。", "labelCode": "3561"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 52, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 59, "checkType": 0}], "distance": 0.9677, "minAge": 25, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "532036", "platformCode": "", "reactId": "16465393917", "priceInfo": {"currentTotalPrice": 1002, "localCurrencyCode": "THB", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 501, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["68795847"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1646539642", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "similarVehicleInfos": [{"vehicleCode": "532036", "vehicleName": "丰田 Fortuner"}], "vendorName": "Budget Thai", "bizVendorCode": "14088277"}], "sortNum": 14, "lowestDistance": 0.9677, "minTPrice": 1002, "vehicleKey": "532036_18_0", "lowestPrice": 501, "highestPrice": 501}], "groupCode": "4", "groupName": "SUV", "dailyPrice": 229, "allowMerge": true, "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/intermediate_suv.png"}, {"sortNum": 8, "productList": [{"vehicleCode": "531462_24_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "531462_24_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 359, "pickWayInfo": 0, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awQhpNJPtjUb3SK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SKBua64r9CGz71OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534838", "unionCardFilter": {}, "kVehicleId": 531462, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531462", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_-1_1234122_-1_531462_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 0, "age": 30, "skuId": 110534838, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "机场店，抵达后联系店员", "ctripVehicleCode": "531462", "platformCode": "", "reactId": "16465394219", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "531462_24_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": 737542, "pCityId": 359, "pickWayInfo": 1, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWgAbj6b7Ly9awQhpNJPtjUb3SK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4YwWxtX65n+SKBua64r9CGz71OyHp1IWClnWnCVEMj0IcLQPLcOAVC6frcnyWoDSFo=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "110534838", "unionCardFilter": {}, "kVehicleId": 531462, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "531462", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": 737542, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_737542_1234122_737542_531462_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 1, "age": 30, "skuId": 110534838, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "531462", "platformCode": "", "reactId": "16465393716", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["110534838"], "introduce": "同组车型最低价"}, "maximumCommentCount": 20, "reactId": "1646539640", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "similarVehicleInfos": [{"vehicleCode": "531462", "vehicleName": "丰田 Hilux Double Cab"}], "vendorName": "THAI", "bizVendorCode": "14088020"}], "sortNum": 4, "lowestDistance": 0.2652, "minTPrice": 458, "vehicleKey": "531462_24_0", "lowestPrice": 229, "highestPrice": 229}], "groupCode": "8", "groupName": "皮卡", "dailyPrice": 229, "allowMerge": true, "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/pick_up.png"}, {"sortNum": 10, "productList": [{"vehicleCode": "530496_25_0", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "530496_25_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": -1, "pCityId": 359, "pickWayInfo": 0, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWcAbj6b7Ly9awTCinlDt1TLQCK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4bbmJmDGVY3xaKbLvhYpu/nVNn4/mAjle4yYGkuO+eXSRpfVy+O0wEHBEgtXUptnJQ=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "72891743", "unionCardFilter": {}, "kVehicleId": 530496, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "530496", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": -1, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_-1_1234122_-1_530496_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 0, "age": 30, "skuId": 72891743, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 32, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "机场店，抵达后联系店员", "ctripVehicleCode": "530496", "platformCode": "", "reactId": "16465392811", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}, {"vendorName": "THAI", "vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "reference": {"vehicleKey": "530496_25_0", "kPSId": 1234122, "largeRadiusVersion": "0", "pLev": 737542, "pCityId": 359, "pickWayInfo": 1, "kRSId": 1234122, "isEasyLife": false, "payMode": 2, "priceVersion": "AWcAbj6b7Ly9awTCinlDt1TLQCK+jstbkAjjdswZRqMCd5k5t85PBgL2SxbttPVFtIPU6P9YVGC6H4bbmJmDGVY3xaKbLvhYpu/nVNn4/mAjle4yYGkuO+eXSRpfVy+O0wEHBEgtXUptnJQ=", "packageId": "376384", "rStoreCode": "1234122", "labels": [{"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}], "vendorCode": "SD0012", "productCode": "72891743", "unionCardFilter": {}, "kVehicleId": 530496, "rCityId": 359, "listRequestId": "5bfdaf74-1756-4709-a062-62d07ee6286c", "vehicleCode": "530496", "bizVendorCode": "14088020", "vendorVehicleCode": "", "kVId": 14088020, "rLev": 737542, "noDepositFilter": {}, "pStoreCode": "1234122", "bomCode": "1234122_737542_1234122_737542_530496_FPO_TP_ULM_0_0", "vcExtendRequest": {}, "isVehicle2": true, "isGroupNew": false, "decoratorVendorType": 0, "klbVersion": 1, "pkgRuleId": 376384, "returnWayInfo": 1, "age": 30, "skuId": 72891743, "packageType": 0}, "evaluation": {"title": "", "type": 1}, "isRStoreSupportCdl": true, "maxAge": 70, "extMap": {"confirmRightNow": "false", "distance": "0.2652", "freeCancel": "false", "isVendorActive": "0", "isAsiaPickup": "true", "isConfirmTimeGth12": "false", "isThirdInsurance": "false", "isULM": "true", "isNoOnewayFee": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "storeScore": 0, "isPStoreSupportCdl": true, "sortNum": 1, "allTags": [{"category": 1, "sortNum": 10, "groupCode": "MarketGroup1336", "title": "1小时确认", "colorCode": "1", "type": 1, "description": "根据该车行近期订单表现，预订此产品后平均需要1小时确认订单是否预订成功。", "labelCode": "3942"}, {"category": 1, "sortNum": 15, "groupCode": "MarketGroup1336", "code": "2", "title": "限时免费取消", "colorCode": "1", "type": 1, "description": "", "labelCode": "3581"}, {"category": 2, "sortNum": 5, "groupCode": "MarketGroup1334", "code": "2", "title": "芝麻免押", "colorCode": "2", "type": 1, "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "labelCode": "3850"}, {"category": 2, "sortNum": 45, "groupCode": "MarketGroup1325", "code": "2", "title": "仅需中国大陆驾照", "colorCode": "2", "type": 1, "description": "只需中国大陆驾照即可取车。", "labelCode": "3560"}, {"category": 2, "sortNum": 56, "groupCode": "MarketGroup1325", "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 3, "sortNum": 10000, "groupCode": "MarketGroup1312", "title": "四月特惠", "colorCode": "3", "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "labelCode": "3834"}, {"category": 4, "sortNum": 131, "groupCode": "MarketGroup1325", "code": "4", "title": "低龄驾驶费", "colorCode": "4", "type": 2, "description": "已选的驾驶员年龄在该门店需要收取低龄驾驶费。", "labelCode": "3592"}], "promotions": [{"sortNum": 1, "longTag": "0起赔额", "longDesc": "保险起赔额为0，意味着该保险承保的责任范围内，您无需支付任何费用。\n", "title": "0起赔额", "deductionPercent": 0, "selected": false, "type": 1, "description": "0起赔额", "isFromCtrip": false, "isEnabled": false}, {"sortNum": 1, "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "title": "四月特惠", "deductionPercent": 0, "selected": false, "type": 1, "description": "四月特惠", "isFromCtrip": false, "isEnabled": false}], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 20, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 261, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Activity", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 32, "checkType": 0}, {"groupCode": "StoreWay", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 44, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CreditCard", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 63, "checkType": 0}], "distance": 0.2652, "minAge": 21, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "530496", "platformCode": "", "reactId": "16465392710", "priceInfo": {"currentTotalPrice": 458, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 229, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {}, "orignalPriceStyle": "WithStrikethrough"}], "vehicleRecommendProduct": {"productCodes": ["72891743"], "introduce": "同组车型最低价"}, "maximumCommentCount": 20, "reactId": "1646539650", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5512000ejfpr8d86BB.png", "similarVehicleInfos": [{"vehicleCode": "530496", "vehicleName": "丰田 Hiace"}], "vendorName": "THAI", "bizVendorCode": "14088020"}], "sortNum": 10, "lowestDistance": 0.2652, "minTPrice": 458, "vehicleKey": "530496_25_0", "lowestPrice": 229, "highestPrice": 229}], "groupCode": "10", "groupName": "其他车型", "dailyPrice": 229, "allowMerge": true, "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/else.png"}], "appResponseMap": {"isFromCache": false, "isCacheValid": false, "cacheKey": "27140%2FqueryProducts%3Fbatch%3D0_M2258803416_%7B%22age%22%3A30%2C%22adultNumbers%22%3A2%2C%22childrenNumbers%22%3A0%2C%22pickupPointInfo%22%3A%7B%22cityId%22%3A359%2C%22date%22%3A%222025-05-08%2012%3A00%3A00%22%2C%22locationCode%22%3A%22BKK%22%2C%22locationName%22%3A%22%E7%B4%A0%E4%B8%87%E9%82%A3%E6%99%AE%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BA%22%2C%22locationType%22%3A1%2C%22poi%22%3A%7B%22latitude%22%3A13.689999%2C%22longitude%22%3A100.750112%2C%22radius%22%3A0%7D%2C%22pickupOnDoor%22%3A0%2C%22dropOffOnDoor%22%3A0%7D%2C%22returnPointInfo%22%3A%7B%22cityId%22%3A359%2C%22date%22%3A%222025-05-10%2012%3A00%3A00%22%2C%22locationCode%22%3A%22BKK%22%2C%22locationName%22%3A%22%E7%B4%A0%E4%B8%87%E9%82%A3%E6%99%AE%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BA%22%2C%22locationType%22%3A1%2C%22poi%22%3A%7B%22latitude%22%3A13.689999%2C%22longitude%22%3A100.750112%2C%22radius%22%3A0%7D%2C%22pickupOnDoor%22%3A0%2C%22dropOffOnDoor%22%3A0%7D%2C%22searchType%22%3A1%2C%22modify%22%3A%7B%7D%2C%22queryListCacheId%22%3A%22%22%2C%22vendorGroup%22%3A0%2C%22listAbVersion%22%3A%22240716_DSJT_Cref%7CB%22%2C%22extraMaps%22%3A%7B%22klbVersion%22%3Anull%2C%22orignScenes%22%3Anull%2C%22isMarketing%22%3Anull%7D%7D", "groupId": "27140/queryProducts?batch=0", "networkCost": 757, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 757, "setCacheCost": 0, "cacheFrom": "", "uniqRequestKey": "OSD_C_APP_4e8f4f61-574f-4289-8e14-b4ac7fdd016a/true", "beforeFetch": 1745484413083, "afterFetch": 1745484413840, "hasRetry": false, "isSuccess": true, "hasResult": true}}