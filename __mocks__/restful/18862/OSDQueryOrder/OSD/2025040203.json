{"modifyInfoDto": {}, "returnStore": {"location": {"city": {"id": 359, "name": "曼谷"}, "country": {"id": 4, "name": "泰国"}, "locationType": 1, "locationName": "廊曼国际机场", "continent": {"id": 4, "name": "泰国"}, "poiInfo": {"type": 1, "longitude": 100.604199, "latitude": 13.91326}, "locationCode": "DMK", "province": {"id": 0}}, "storeAddress": "222 <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> 10210, Thailand", "countryName": "泰国", "storeCode": "DMK", "storeLocation": "与取车门店相同, 门店到达大厅", "localDateTime": "2025-04-16 10:00:00", "storeName": "Bangkok Donmuang Airport ", "contactWayList": [{"contactWayValue": "3333333333日反反复复方法", "contactWayType": "1", "contactWayName": "微信", "isCanModify": true}, {"contactWayValue": "人人人人人人人人人人人人人人", "contactWayType": "4", "contactWayName": "WhatsApp", "isCanModify": true}], "latitude": 13.919905, "storeOpenTimeDesc": "{\"\":\"05:00 - 22:00\"}", "storeGuide": "如需归还车辆，请在抵达前联系我们的柜台，并将车辆停放在国内客运航站楼前的预算指定停车区。我们友好的工作人员将在此等候，为您办理登记手续。记得随身携带您的个人物品。\n", "countryId": 4, "storeTel": "************/+************", "localDateTimeDTO": {"date": "2025年4月16日", "time": "10:00"}, "businessTimeDesc": "{\"正常营业时间：\":\"05:00 - 22:00\"}", "longitude": 100.60193, "businessTimePolicy": {"content": [""]}, "serviceType": "0", "userSearchLocation": "廊曼国际机场", "storeID": 228770, "disclaimer": "The above information is provided by the branch", "cityName": "曼谷"}, "orderPriceInfo": {"currentTotalPrice": 2209, "localCurrencyCode": "THB", "currentCurrencyCode": "CNY", "packageType": 0, "payMode": 2, "localTotalPrice": 10384.5, "payModeDesc": "在线支付", "localPrice": {"title": "", "totalPrice": 0, "currencyCode": "THB"}, "payAmount": 2209, "prepayPriceDetails": [{"title": "shark|common_carHireFee", "totalPrice": 2209, "currencyCode": "CNY"}], "coupons": [], "priceDetails": [{"title": "shark|common_carHireFee", "totalPrice": 2209, "currencyCode": "CNY"}], "prepayPrice": {"title": "", "totalPrice": 2209, "currencyCode": "CNY"}, "localPriceDetails": [], "couponAmount": 0}, "productDetails": [{"productInfoList": [{"minPackageItmes": [{"code": "ULM", "title": "不限里程", "type": 2, "description": "租期内没有公里数限制。\n"}, {"code": "ADD1", "title": "1名额外驾驶员", "type": 3, "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"}, {"code": "FRFB", "title": "满油取还", "type": 4, "description": "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"}, {"code": "FRFB", "title": "满电取还", "type": 4, "description": "取车时电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去充电的工时费，具体金额以取车时门店确认为准。建议保留最后一次充电时的单据以及取还车时显示的车辆电量表的照片以备用。"}]}], "claimsProcess": [], "briefInsuranceItems": [{"insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "起赔额THB 15000", "currencyCode": "THB", "packageId": 393415, "maxExcess": 15000, "excessShortDesc": "起赔额THB 15000", "minExcess": 15000, "coverageWithPlatformInsurance": "起赔额THB 15000"}], "code": "CDW", "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "excessTable": {"title": "起赔额THB 15000", "subObject": [{"title": "车行承担", "content": ["THB 15000以上部分"]}, {"title": "客户或承租方承担", "content": ["THB 15000及以下部分（据实承担）"]}]}, "isInclude": true, "description": "保障车辆碰撞损失", "type": 7, "name": "车辆碰撞保障", "isFromCtrip": false}, {"insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "起赔额THB 15000", "currencyCode": "THB", "packageId": 393415, "maxExcess": 15000, "excessShortDesc": "起赔额THB 15000", "minExcess": 15000, "coverageWithPlatformInsurance": "起赔额THB 15000"}], "code": "TPL", "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "excessTable": {"title": "起赔额THB 15000", "subObject": [{"title": "车行承担", "content": ["THB 15000以上部分"]}, {"title": "客户或承租方承担", "content": ["THB 15000及以下部分（据实承担）"]}]}, "isInclude": true, "description": "保障第三方车辆或人员伤害损失", "type": 7, "name": "第三者责任保障", "isFromCtrip": false}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比", "", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比"], "insPackageId": 5, "insuranceItems": [{"productId": 393415, "description": "保障车辆碰撞损失", "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后"]}, "excessTable": {"title": "起赔额THB 15000", "subObject": [{"title": "车行承担", "content": ["THB 15000以上部分"]}, {"title": "客户或承租方承担", "content": ["THB 15000及以下部分（据实承担）"]}]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "type": 7, "title": "车辆碰撞保障", "code": "CDW", "insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "起赔额THB 15000", "currencyCode": "THB", "packageId": 393415, "maxExcess": 15000, "excessShortDesc": "起赔额THB 15000", "minExcess": 15000, "coverageWithPlatformInsurance": "起赔额THB 15000"}], "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "isInclude": true, "name": "车辆碰撞保障", "isFromCtrip": false, "labels": null, "statusBlocks": null, "subBtns": null, "isTitleRight": true}, {"productId": 393415, "description": "保障第三方车辆或人员伤害损失", "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准"]}, "excessTable": {"title": "起赔额THB 15000", "subObject": [{"title": "车行承担", "content": ["THB 15000以上部分"]}, {"title": "客户或承租方承担", "content": ["THB 15000及以下部分（据实承担）"]}]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "type": 7, "title": "第三者责任保障", "code": "TPL", "insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "起赔额THB 15000", "currencyCode": "THB", "packageId": 393415, "maxExcess": 15000, "excessShortDesc": "起赔额THB 15000", "minExcess": 15000, "coverageWithPlatformInsurance": "起赔额THB 15000"}], "isInclude": true, "name": "第三者责任保障", "isFromCtrip": false, "labels": null, "statusBlocks": null, "subBtns": null, "isTitleRight": true}]}], "vendorInfo": {"confirmDate": 1743695142000, "platformName": "", "bizVendorCode": "14088277", "platformCode": "", "vendorName": "Budget Thai", "broker": false, "vendorImageUrl": "https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png", "confirmDateStr": "2025-04-03 23:45", "commentInfo": {"commentCount": 0, "topScore": 5, "commentLabel": "", "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=228770&calabiVehicleId=545574&vehicleName=null&productCategoryId=35&isHideNavBar=YES", "vendorGoodType": 1, "hasComment": 0, "exposedScore": 0}}, "addPayments": [], "useCalabiId": true, "orderBaseInfo": {"orderDate": 1743585342000, "uId": "M2258803416", "channelType": "17671", "payMode": 2, "extOperation": [], "orderLocale": "zh-CN", "orderStatusName": "已取消", "orderStatusCtrip": "CAR_CANCELLED", "successSafeRentAuth": false, "orderId": 1128168945010416, "orderStatus": 4, "allStatuses": [], "allOperations": [{"operationId": 4, "buttonName": "再次预订", "enable": true}, {"operationId": 3, "enable": false, "code": 4}, {"code": 0, "operationId": 12, "enable": false, "display": "none", "buttonName": "取消修改"}], "orderTip": {"tipContentArray": ["您的订单已取消，感谢您使用携程租车。"], "tipType": 5, "warnType": 0, "tipContent": "您的订单已取消，感谢您使用携程租车。"}, "orderStatusDesc": "已取消"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1743594823384+0800)/", "extension": [{"id": "CLOGGING_TRACE_ID", "value": "2200634437622784858"}, {"id": "RootMessageId", "value": "921822-0a78e91c-484331-164264"}]}, "resBodySize": 24605, "refundProgressList": [], "driverInfo": {"flightNo": "", "decryptTelphone": "13917623823", "lastName": "LI", "age": "34", "firstName": "LIU", "isChangeContact": false, "decryptMail": "<EMAIL>", "contactWayList": [], "optionalContactWayList": [{"contactWayName": "微信", "isCanModify": true, "contactWayType": "1"}, {"contactWayName": "WhatsApp", "isCanModify": true, "contactWayType": "4"}, {"contactWayName": "LINE", "isCanModify": true, "contactWayType": "2"}, {"contactWayName": "KakaoTalk", "isCanModify": true, "contactWayType": "3"}, {"contactWayName": "当地电话", "isCanModify": true, "contactWayType": "0"}], "telphone": "139****3823", "email": "d**<EMAIL>", "name": "LI/LIU", "areaCode": "86"}, "packageInfos": [{"insPackageId": 5, "naked": false, "defaultBomCode": "", "currencyCode": "THB", "defaultPackageId": 393415, "gapPrice": 0, "isDefault": true, "stepPrice": 0, "lowestDailyPrice": 0, "packageName": "基础套餐", "guaranteeDegree": 0}], "vehicleInfo": {"doorNum": 4, "fuelType": "3", "fourDrive": false, "transmission": "AT", "vehicleGroupName": "紧凑型轿车", "fuelNote": "具体燃油类型与加油型号以门店告知为准", "displacement": "", "imageUrl": "", "special": false, "luggageNum": 0, "hasAC": false, "vehicleDisplacement": "", "vehicleGroupId": 12, "fuelNoteTitle": "Gasoline", "vehicleName": "本田思域", "passengerNum": 5, "vendorVehicleCode": ""}, "pickupStore": {"location": {"city": {"id": 359, "name": "曼谷"}, "country": {"id": 4, "name": "泰国"}, "locationType": 1, "locationName": "素万那普国际机场", "continent": {"id": 4, "name": "泰国"}, "poiInfo": {"type": 1, "longitude": 100.750112, "latitude": 13.689999}, "locationCode": "BKK", "province": {"id": 0}}, "storeAddress": "222 <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> 10210, Thailand", "countryName": "泰国", "storeCode": "DMK", "storeLocation": "门店位于廊曼国际机场到达大厅，距素万那普国际机场驾车50公里", "localDateTime": "2025-04-07 10:00:00", "storeName": "Bangkok Donmuang Airport ", "contactWayList": [{"contactWayValue": "3333333333日反反复复方法", "contactWayType": "1", "contactWayName": "微信", "isCanModify": true}, {"contactWayValue": "人人人人人人人人人人人人人人", "contactWayType": "4", "contactWayName": "WhatsApp", "isCanModify": true}], "latitude": 13.919905, "storeOpenTimeDesc": "{\"\":\"05:00 - 22:00\"}", "storeGuide": "如需取车，请前往位于曼谷廊曼机场国内到达大厅客运大楼 11 号门的 Budget 柜台。在这里，我们友好的工作人员将现场完成您的租车协议和取车流程。                      \n", "countryId": 4, "storeTel": "************/+************", "localDateTimeDTO": {"date": "2025年4月9日", "time": "10:00"}, "businessTimeDesc": "{\"正常营业时间：\":\"05:00 - 22:00\"}", "longitude": 100.60193, "businessTimePolicy": {"content": [""]}, "waitTimeDesc": "", "serviceType": "0", "userSearchLocation": "素万那普国际机场", "storeID": 228770, "disclaimer": "The above information is provided by the branch", "cityName": "曼谷"}, "baseResponse": {"code": "200", "requestId": "0506d30f-08f6-4592-8cbb-34250120170f", "cost": 2392, "isSuccess": true, "returnMsg": "success"}, "localesInfo": [{"locale": "zh-CN", "ctripInsDeclaration": {"purchasedDesc": "该客人的保险套餐已满足当地法律的最低要求，请避免强迫客人添加任何保险，否则可能导致我处对相关当局的投诉。", "requirement": "如果认为某项保险是必须的，请写明'xx保险是必须的'并附上签名和日期。", "partner": "亲爱的供应商伙伴：", "statement": "携程声明"}, "localeType": 1, "localeName": "简体中文"}, {"locale": "th-TH", "ctripInsDeclaration": {"purchasedDesc": "แพ็คเกจประกันภัยของลูกค้าครอบคลุมตามข้อกำหนดขั้นต่ำของกฎหมายท้องถิ่นแล้ว โปรดหลีกเลี่ยงการกดดันให้ลูกค้าซื้อประกันภัยเพิ่มเติม การกระทำดังกล่าวอาจทำให้เกิดข้อร้องเรียนได้", "requirement": "หากคุณเชื่อว่าต้องมีประกันภัยบางประเภท โปรดระบุเป็นลายลักษณ์อักษรว่า \"ต้องมีประกันภัย xx\" พร้อมลงลายมือชื่อของคุณและวันที่", "partner": "เรียน ซัพพลายเออร์พาร์ทเนอร์:", "statement": "ใบแจ้งยอดของ Trip.com"}, "localeType": 2, "localeName": "泰语"}, {"locale": "en-US", "ctripInsDeclaration": {"purchasedDesc": "The customer's insurance package already meets minimum local law requirements. Please avoid pressuring the customer to add any insurance. Doing so may result in complaints being raised.", "requirement": "If you believe a certain type of insurance is required, please indicate in writing that \"xx insurance is required\" and include your signature and date.", "partner": "Dear supplier partner:", "statement": "Trip.com Statement"}, "localeType": 3, "localeName": "英语"}], "checkResponseTime": 1743594823294.8232, "checkRequestTime": 1743594820852.762, "insuranceAndXProductDesc": [{"type": 1, "desc": ["取车时，店员可能会向您推销额外保险或收费项目，请根据实际需要选购。在签署合同时，请仔细核对是否有额外收费。如被强制要求购买，请您不要签字，当场联系携程客服协助解决。"]}], "continuePayInfo": {"needContinuePay": false}, "extraInfos": [], "extendedInfo": {"attr": {"osdConfirmNew": "true", "voucherVersionV2": "B", "isContractTemplates": "true", "fulfillmentVersion": "D", "isVehicle2": "1", "voucherVersion": "B", "osdModifyOrderVersion": "B", "ctripInsuranceVersion": "B", "osdDetailVersion": "A"}, "osdModifyOrderVersion": "B", "osdModifyNewOrder": false, "carAssistantSummary": [{"content": "取车材料"}, {"content": "办理驾照翻译件"}], "crossLocationsPolicy": {"title": "旅行限制", "crossLocationsInfos": [{"crossType": 3, "crossTypeName": "跨境政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。"]}]}, "ctripInsuranceVersion": "B", "pickUpMaterials": [{"summaryTitle": "驾驶员需持有本人名下4项材料取车：", "title": "取车要求", "content": ["驾驶员年龄25-65周岁，且持有驾照至少满12个月"], "summaryContent": ["18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "25-65周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}]}, {"subObject": [{"code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "其他地区护照", "subTitle": "其他地区护照原件"}], "title": "护照原件", "subTitle": "", "type": 0}, {"subObject": [{"type": 5, "subObject": [{"code": "OH", "subObject": [{"code": "ODL,IDP", "sortNum": 1, "title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "status": "0"}], "title": "门店支持以下驾照组合", "type": 5}]}], "title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合原件，电子版、照片或非正式驾照（临时驾照、学习驾照、区域限制驾照等）将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1}, {"subObject": [{"title": "信用卡要求", "content": ["您无需支付押金，但仍需携带信用卡以作担保", "卡片所示姓名（需为全拼）与主驾驶员护照姓名一致。", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）", "可能需要两张信用卡，详情请咨询门店"], "type": 6, "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "0"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}], "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png"]}, {"title": "押金说明", "content": ["该门店不收取押金"], "type": 8, "table": [{"title": "押金", "showFree": false, "description": "该门店不收取押金"}]}], "title": "国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店"], "summaryContent": ["该门店不收取押金"], "type": 2, "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，平台为您提供"], "type": 3}], "klbVersion": 1, "osdDetailVersion": "B", "carAssistantSummaryV2": [{"title": "取车材料&租车指南", "type": 1, "url": "https://www.fat10668.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168945010416&h5View=1&locale=zh_cn&orderStatus=CAR_CANCELLED&vendorId=14088277&countryId=4&isHideNavBar=YES&subEnv=fat11183&transparentbar=1&newOsdVoucherV2=1&orderLocale=zh-CN,th-TH,en-US"}, {"title": "期待您的下一次预订", "type": 6}], "flightDelayRule": {"rules": [{"title": "如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。"}, {"title": "建议您填写航班号，如遇延误请携带机票前往取车"}], "title": "航班延误保留政策", "description": "航班延误保留政策", "delayStatus": 0}, "promptInfos": [{"title": "押金汇率说明", "type": 12, "contents": [{"stringObjs": [{"content": "押金所需人民币按照授权免押时的汇率计算，押金实际所需人民币以授权免押时的金额为准"}]}]}], "sign": "CHRcpFz30Vq21z1FxF1hlhJ5GkY="}, "cancelRuleInfo": {"cancelReasons": ["行程变更/取消", "修改订单", "重复下单", "车不能跨境跨岛", "信用卡问题", "驾照证件问题", "其他网站更便宜", "其他"], "osdCancelRuleInfo": {"code": "FreeCancel", "title": "取消政策", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "当地时间2025-04-08 10:00前可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "当地时间2025-04-08 10:00前可免费取消", "style": "6"}]}, "showFree": true, "type": 300, "description": "注意：均为当地时间", "subTitle": "当地时间2025-04-08 10:00前可免费取消", "items": [{"showFree": true, "lossFee": 0, "title": "2025-04-08 10:00前", "subTitle": "取车前24小时前", "description": "可免费取消", "key": "743478"}, {"showFree": false, "lossFee": 0, "title": "2025-04-09 02:00后", "subTitle": "取车前8小时后", "description": "取消将收取全部租金作为违约金，最多收取¥10,000", "key": "743485"}, {"showFree": false, "lossFee": 0, "title": "2025-04-08 10:00至04-09 02:00", "subTitle": "取车前24小时至取车前8小时", "description": "取消将收取租金50%作为违约金，最多收取¥1,000", "key": "743492"}]}, "cancelTip": "Booking canceled", "cancelTipColor": 0, "ruleList": ["- You pick up the car late or drop it off early", "- You do not pick up the car within the given timeframe", "- You do not provide the required documents when picking up the car", "- The main driver's credit card does not have sufficient funds"], "customerCurrency": "CNY", "longTitle": "In the following circumstances, you will not receive a refund:"}, "authType": 0, "timeInterval": 2442.061279296875, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 2809, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 2809, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1743594820852, "afterFetch": 1743594823661}}