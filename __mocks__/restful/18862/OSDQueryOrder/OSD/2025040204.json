{"ResponseStatus": {"Timestamp": "2025-04-07 17:25:38", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "8662984764902862453"}, {"Id": "RootMessageId", "Value": "921822-0a8dbdf2-484449-1126389"}]}, "orderBaseInfo": {"orderId": "1128137460298506", "uId": "M180970538", "channelType": "17671", "orderDate": 1743071516000, "orderLocale": "zh-CN", "orderStatus": 4, "orderStatusDesc": "已取消", "orderStatusName": "已取消", "orderStatusCtrip": "CAR_CANCELLED", "allStatuses": [], "allOperations": [{"operationId": 4, "buttonName": "再次预订", "enable": true}, {"operationId": 3, "enable": false, "code": 4}, {"operationId": 12, "buttonName": "取消修改", "enable": false, "display": "none", "code": 0}], "orderTip": {"tipType": 5, "tipContent": "您的订单已取消，感谢您使用携程租车。", "tipContentArray": ["您的订单已取消，感谢您使用携程租车。"], "warnType": 0}, "payMode": 2, "successSafeRentAuth": false, "extOperation": []}, "continuePayInfo": {"needContinuePay": false}, "orderPriceInfo": {"packageType": 0, "currentTotalPrice": 1512, "currentCurrencyCode": "CNY", "localTotalPrice": 263.22, "localCurrencyCode": "USD", "payMode": 2, "payModeDesc": "在线支付", "prepayPrice": {"title": "", "totalPrice": 1512, "currencyCode": "CNY"}, "localPrice": {"title": "", "totalPrice": 0, "currencyCode": "USD"}, "prepayPriceDetails": [{"title": "shark|common_carHireFee", "totalPrice": 1512, "currencyCode": "CNY"}], "localPriceDetails": [], "priceDetails": [{"title": "shark|common_carHireFee", "totalPrice": 1912, "currencyCode": "CNY"}], "payAmount": 1512, "couponAmount": 400, "coupons": [{"couponCode": "lbctmzcrit", "promotionId": 841370114, "deductionAmount": 400, "displayName": "立减券"}]}, "vehicleInfo": {"vehicleName": "起亚Soul", "special": false, "passengerNum": 5, "luggageNum": 2, "hasAC": false, "transmission": "AT", "vehicleGroupName": "紧凑型轿车", "vendorVehicleCode": "CCAR_KiaSoul_5e56379f2c07a28b49d6", "imageUrl": "https://dimg04.c-ctrip.com/images/0yc0u12000egsmxjw4A8B.png", "vehicleDisplacement": "", "displacement": "", "doorNum": 4, "fuelType": "2416", "vehicleGroupId": 12, "fourDrive": false, "fuelNote": "门店会根据库存情况为您提供汽油车或者柴油车，具体燃油类型与加油型号以门店告知为准。", "fuelNoteTitle": "汽油或柴油"}, "vendorInfo": {"vendorName": "<PERSON><PERSON>", "vendorImageUrl": "https://dimg04.c-ctrip.com/images/20p0v12000ejfq7y8C78B.png", "confirmDate": 1743019316000, "confirmDateStr": "2025-03-27 04:01", "platformCode": "", "platformName": "Avis Budget EMEA Limited", "bizVendorCode": "14000", "commentInfo": {"vendorGoodType": 1, "exposedScore": 3.8, "topScore": 5, "level": "", "commentCount": 110, "hasComment": 1, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=67764&calabiVehicleId=73648&vehicleName=null&productCategoryId=35&isHideNavBar=YES"}, "broker": false}, "pickupStore": {"localDateTime": "2025-04-03 10:00:00", "storeName": "Los Angeles Intl Airport", "storeCode": "LAXT01", "storeAddress": "5251 W 98th St,90045", "longitude": -118.374623, "latitude": 33.950017, "storeGuide": "从每个航站楼的行李领取区出发，按照“Ground Transportation”标识前往位于二层室外的巴士等候区。\n出航站楼后沿着指示标志一直走，在紫色的“Rental Car Shuttle”区域搭乘Avis的班车前往柜台办理手续。\n在此等候Avis的接驳巴士将搭载您前往柜台办理手续。", "storeLocation": "洛杉矶国际机场店", "storeWay": "可搭乘巴士到达", "storeTel": "*************", "storeOpenTimeDesc": "{\"\":\"24小时营业\"}", "cityName": "洛杉矶", "provinceName": "加利福尼亚州", "countryName": "美国", "userSearchLocation": "洛杉矶国际机场", "serviceType": "0", "storeID": 67764, "location": {"locationType": 1, "locationName": "洛杉矶国际机场", "locationCode": "LAX", "continent": {"id": 66, "name": "美国"}, "country": {"id": 66, "name": "美国"}, "province": {"id": 10125, "name": "加利福尼亚州"}, "city": {"id": 347, "name": "洛杉矶"}, "poiInfo": {"latitude": 33.941589, "longitude": -118.40853, "type": 1}}, "disclaimer": "The above information is provided by the branch", "localDateTimeDTO": {"date": "2025年4月3日", "time": "10:00"}, "countryId": 66, "businessTimeDesc": "{\"正常营业时间：\":\"24小时营业\"}", "businessTimePolicy": {"content": [""]}, "waitTimeDesc": ""}, "returnStore": {"localDateTime": "2025-04-10 10:00:00", "storeName": "Los Angeles Intl Airport", "storeCode": "LAXT01", "storeAddress": "5251 W 98th St,90045", "longitude": -118.374623, "latitude": 33.950017, "storeGuide": "请按照标记的指示牌前往 Consolidated Rent-A-Car 设施的 Avis 租车归还区，至于确切的换车地点请提前联系工作人员进行确认。", "storeLocation": "洛杉矶国际机场店", "storeWay": "可搭乘巴士到达", "storeTel": "*************", "storeOpenTimeDesc": "{\"\":\"24小时营业\"}", "cityName": "洛杉矶", "provinceName": "加利福尼亚州", "countryName": "美国", "userSearchLocation": "洛杉矶国际机场", "serviceType": "0", "storeID": 67764, "location": {"locationType": 1, "locationName": "洛杉矶国际机场", "locationCode": "LAX", "continent": {"id": 66, "name": "美国"}, "country": {"id": 66, "name": "美国"}, "province": {"id": 10125, "name": "加利福尼亚州"}, "city": {"id": 347, "name": "洛杉矶"}, "poiInfo": {"latitude": 33.941589, "longitude": -118.40853, "type": 1}}, "disclaimer": "The above information is provided by the branch", "localDateTimeDTO": {"date": "2025年4月10日", "time": "10:00"}, "countryId": 66, "businessTimeDesc": "{\"正常营业时间：\":\"24小时营业\"}", "businessTimePolicy": {"content": [""]}}, "driverInfo": {"name": "c6c141a7b3c0b9982aab6f85714e6dceeedb49f2fc886b9fc7b271e04996dee4", "age": "69", "email": "99ef3bd8fc5d84443c2078e102e12a192e1e71b70162a17c654ece5886ba81b1", "telphone": "986be18d4a82dc16448d6301d508ef5e2186288267345fcd7ca9b7923f608078", "areaCode": "86", "flightNo": "", "decryptTelphone": "***********", "decryptMail": "<EMAIL>", "lastName": "61c16a3c84576fa7c3585f5de6bc02913e99ec4a6fb1108a2ec6b41909514548", "firstName": "cd5c6c7cd35830d0a25daec995861313bc16ed7dca16c483dd7141ee8cb2113f", "contactWayList": [], "isChangeContact": false, "optionalContactWayList": [{"contactWayType": "1", "contactWayName": "微信", "isCanModify": true}, {"contactWayType": "4", "contactWayName": "WhatsApp", "isCanModify": true}, {"contactWayType": "5", "contactWayName": "QQ", "isCanModify": true}, {"contactWayType": "0", "contactWayName": "当地电话", "isCanModify": true}]}, "extraInfos": [], "cancelRuleInfo": {"cancelTip": "Booking canceled", "longTitle": "In the following circumstances, you will not receive a refund:", "ruleList": ["- You pick up the car late or drop it off early", "- You do not pick up the car within the given timeframe", "- You do not provide the required documents when picking up the car", "- The main driver's credit card does not have sufficient funds"], "cancelReasons": ["行程变更/取消", "修改订单", "重复下单", "车不能跨境跨岛", "信用卡问题", "驾照证件问题", "其他网站更便宜", "其他"], "cancelTipColor": 0, "osdCancelRuleInfo": {"title": "取消政策", "subTitle": "当地时间4月3日10:00前可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "当地时间4月3日10:00前可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "当地时间4月3日10:00前可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "items": [{"title": "2025-04-03 10:00前", "subTitle": "取车时间前", "description": "可免费取消", "showFree": true, "key": "220408385", "lossFee": 0}, {"title": "2025-04-03 10:00后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金", "showFree": false, "key": "220408392", "lossFee": 0}], "description": "注意：均为当地时间", "showFree": true}, "customerCurrency": "CNY"}, "refundProgressList": [], "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "ebe7588c-f288-4935-b19f-9caad70ec0aa", "cost": 658}, "packageInfos": [{"insPackageId": 192, "isDefault": true, "packageName": "高级套餐", "currencyCode": "USD", "defaultBomCode": "", "defaultPackageId": 258812, "guaranteeDegree": 0, "naked": false, "lowestDailyPrice": 0, "gapPrice": 0, "stepPrice": 0}], "productDetails": [{"insPackageId": 192, "insuranceItems": [{"productId": 258812, "title": "超级车辆碰撞保障", "description": "保障因意外事故对租赁车辆的车身造成的损伤", "code": "SCDW", "type": 7, "name": "超级车辆碰撞保障", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 258812, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0}], "converageExplain": {"title": "承保范围", "content": ["在租车期间内，因碰撞或剐蹭导致的租赁车辆车身损伤。起赔额通常较低或为零。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["1）对第三方的车辆或财产造成的损失；\n2）车辆涉水造成的车辆损坏；\n3）因加错燃油类型导致车辆损坏；\n4）不爱惜车辆，在非铺装路面行驶；\n5）在正常道路行驶期间，对于公共设施的损坏；\n6）对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n7）由未登记“额外驾驶员”的其他人驾驶车辆；\n8）驾驶车辆跨境未告知工作人员，出境后发生车损；\n9）未经租车公司授权的修车，拖车产生的费用或损失；\n10）超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n11）车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n12）货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n13）由于自然灾害，或不可抗力因素导致的损失。\n*实际不承保范围以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价。"]}]}, "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}]}}, {"productId": 258812, "title": "超级车辆盗抢保障", "description": "保障车辆被盗或被抢劫造成的车辆损伤", "code": "STP", "type": 7, "name": "超级车辆盗抢保障", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 258812, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0}], "converageExplain": {"title": "承保范围", "content": ["保障租车期间因车辆被盗或被抢劫造成的车辆损伤。起赔额通常较低或为零。*实际赔付范围与标准以门店合同为准。"]}, "unConverageExplain": {"title": "不承保范围", "content": ["1）钥匙遗落车内导致被盗； 钥匙丢失，损坏或被盗；\n2）车门忘关导致被盗；\n3）车内额外设备如GPS，WIFI，儿童座椅等；\n4）在未经授权的区域行驶；\n5）将车辆长时间停在不当的地方导致车辆被盗；\n6）因车辆被盗被抢导致的车辆停运费或维修手续费；\n7）违反合同或当地法律的情况下导致的损失；\n*实际不承保范围以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价。"]}]}, "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}]}}], "insuranceDesc": ["租车公司将根据产生车损的次数扣除对应次数的起赔额费用。若最终定损后单次车损的维修费用低于起赔额，会退回该次车损起赔额多收的部分。"], "productInfoList": [{"minPackageItmes": [{"title": "不限里程", "type": 2, "code": "ULM", "description": "租期内没有公里数限制。\n"}, {"title": "满油取还", "type": 4, "code": "FRFB", "description": "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"}]}], "claimsProcess": [{"subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。切勿自行安排维修事宜，或者与第三方私下处理。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}], "extraDesc": [], "briefInsuranceItems": [{"name": "超级碰撞盗抢保障", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"minExcess": 0, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 0"}]}], "insuranceNotice": "租车公司将根据产生车损的次数扣除对应次数的起赔额费用。若最终定损后单次车损的维修费用低于起赔额，会退回该次车损起赔额多收的部分。"}], "addPayments": [], "extendedInfo": {"flightDelayRule": {"title": "航班延误保留政策", "description": "若航班延误，门店将不保留车辆，请留取充足的时间取车", "rules": [{"title": "若航班延误，门店将不保留车辆，请留取充足的时间取车"}, {"title": "建议您填写航班号，如遇延误请携带机票前往取车"}], "delayStatus": 0}, "klbVersion": 1, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄21-80周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对21-25周岁将收取“青年驾驶费”；参考价格：USD27（约¥197）/天，包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "21-80周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}], "summaryTitle": "驾驶员需持有本人名下5项材料取车："}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合原件，电子版、照片或非正式驾照（临时驾照、学习驾照、区域限制驾照等）将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，驾驶员驾照有效期内可多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可至携程APP境外租车首页办理。"], "type": 22, "code": "CDL,IDL", "url": "https://m.ctrip.com/webapp/carhire/xsd/xsdnewinterlicensePage?isHideNavBar=YES&channelid=235936", "sortNum": 2, "optimalType": "1", "status": "2"}, {"title": "中国大陆驾照原件 + 车行翻译件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 22, "code": "CDL,DLT", "url": "https://m.ctrip.com/webapp/carhire/xsd/driverlicenseTranslatePage?isHideNavBar=YES", "sortNum": 3, "status": "2"}, {"title": "中国大陆驾照原件 + 英文公证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 22, "code": "CDL,OET", "url": "https://m.ctrip.com/webapp/carhire/xsd/languagelicensePage?isHideNavBar=YES", "sortNum": 4, "status": "2"}]}]}, {"type": 10, "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "若您只能使用美加驾照租车，请前往Trip.com下单。"}]}]}]}, {"title": "国际信用卡", "content": ["带芯片"], "summaryContent": ["押金约租车费用+US$ 250（约¥1,829），取车时刷取信用卡预授权，还车后30-60天内退还"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名（需为全拼）与主驾驶员护照姓名一致。", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "额外驾驶员也需要出示另一张符合供应商要求的信用卡"}]}], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "0"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}, {"contentStyle": "SupportMagneticStripe", "stringObjs": [{"content": "0"}]}, {"contentStyle": "UnionPayCurrencyType", "stringObjs": [{"content": "2"}]}]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 250（约¥1,829），取车时刷取信用卡预授权，还车后30-60天内退还"], "type": 8, "table": [{"title": "押金", "description": "押金约租车费用+US$ 250（约¥1,829），取车时刷取信用卡预授权，还车后30-60天内退还", "showFree": false}]}], "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，平台为您提供"], "type": 3}, {"title": "其他材料", "content": ["按门店要求，取车时需提供往返机票。 ", "如果租车者的居住地不在美国或加拿大，则需要出示往返机票和护照。"], "summaryContent": ["按门店要求，取车时需提供往返机票。 如果租车者的居住地不在美国或加拿大，则需要出示往返机票和护照。"], "type": 14}], "ctripInsuranceVersion": "B", "carAssistantSummary": [{"content": "取车材料"}, {"content": "办理驾照翻译件"}], "osdDetailVersion": "B", "crossLocationsPolicy": {"crossLocationsInfos": [{"crossType": 3, "locations": [{"name": "安提瓜和巴布达", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "regionId": "201", "isSelected": false}, {"name": "安圭拉", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "regionId": "275", "isSelected": false}, {"name": "阿鲁巴", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "regionId": "171", "isSelected": false}, {"name": "巴巴多斯", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "202", "isSelected": false}, {"name": "百慕大", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "207", "isSelected": false}, {"name": "波多黎各", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "208", "isSelected": false}, {"name": "伯利兹", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "210", "isSelected": false}, {"name": "巴拿马", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "18", "isSelected": false}, {"name": "巴哈马", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "185", "isSelected": false}, {"name": "多米尼加共和国", "status": 3, "statusName": "不允许跨境", "firstChar": "D", "regionId": "276", "isSelected": false}, {"name": "多米尼克", "status": 3, "statusName": "不允许跨境", "firstChar": "D", "regionId": "217", "isSelected": false}, {"name": "法属圣马丁", "status": 3, "statusName": "不允许跨境", "firstChar": "F", "regionId": "291", "isSelected": false}, {"name": "瓜德罗普岛", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "261", "isSelected": false}, {"name": "格林纳达", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "220", "isSelected": false}, {"name": "哥斯达黎加", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "38", "isSelected": false}, {"name": "古巴", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "39", "isSelected": false}, {"name": "荷兰加勒比区", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "259", "isSelected": false}, {"name": "荷属圣马丁", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "295", "isSelected": false}, {"name": "海地", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "41", "isSelected": false}, {"name": "洪都拉斯", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "44", "isSelected": false}, {"name": "加拿大", "status": 2, "statusName": "条件跨境", "firstChar": "J", "policy": "需提前告知门店，并从门店处获得加拿大非居民保险卡。在宾夕法尼亚州和华盛顿州亚基马（Yakima）出发的车辆禁止跨境前往加拿大。在新泽西州部分地区租的车仅可前往安大略省和魁北克省，详情请咨询门店。", "regionId": "47", "isSelected": false}, {"name": "开曼群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "K", "regionId": "223", "isSelected": false}, {"name": "库拉索岛", "status": 3, "statusName": "不允许跨境", "firstChar": "K", "regionId": "294", "isSelected": false}, {"name": "墨西哥", "status": 3, "statusName": "不允许跨境", "firstChar": "M", "regionId": "72", "isSelected": false}, {"name": "马提尼克", "status": 3, "statusName": "不允许跨境", "firstChar": "M", "regionId": "279", "isSelected": false}, {"name": "美属维尔京群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "M", "regionId": "280", "isSelected": false}, {"name": "尼加拉瓜", "status": 3, "statusName": "不允许跨境", "firstChar": "N", "regionId": "198", "isSelected": false}, {"name": "圣皮埃尔和密克隆岛", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "289", "isSelected": false}, {"name": "圣巴泰勒米", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "297", "isSelected": false}, {"name": "萨尔瓦多", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "238", "isSelected": false}, {"name": "圣基茨和尼维斯", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "243", "isSelected": false}, {"name": "圣卢西亚", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "244", "isSelected": false}, {"name": "圣文森特和格林纳丁斯", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "246", "isSelected": false}, {"name": "特克斯和凯科斯群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "T", "regionId": "265", "isSelected": false}, {"name": "特立尼达和多巴哥", "status": 3, "statusName": "不允许跨境", "firstChar": "T", "regionId": "252", "isSelected": false}, {"name": "危地马拉", "status": 3, "statusName": "不允许跨境", "firstChar": "W", "regionId": "90", "isSelected": false}, {"name": "英属维尔京群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "Y", "regionId": "282", "isSelected": false}, {"name": "牙买加", "status": 3, "statusName": "不允许跨境", "firstChar": "Y", "regionId": "101", "isSelected": false}, {"name": "英属蒙塞拉特岛", "status": 3, "statusName": "不允许跨境", "firstChar": "Y", "regionId": "299", "isSelected": false}], "crossTypeName": "跨境政策", "summaryPolicies": ["若您的行程中涉及跨境，请提前选择"], "title": "选择计划前往的国家", "subTitle": "门店支持在以下区域跨境使用车辆："}, {"crossType": 2, "crossTypeName": "跨州政策", "summaryPolicies": ["可以在美国境内跨州驾驶，在内华达州、新泽西州、宾夕法尼亚州、犹他州及华盛顿州的部分城市租车可能有额外跨州要求，详情请提前咨询门店。"], "summaryTitle": "条件跨州"}], "notes": ["若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。"], "title": "旅行限制"}, "sign": "CHRcpFz30Vq21z1FxF1hlhJ5GkY=", "promptInfos": [{"title": "押金汇率说明", "type": 12, "contents": [{"stringObjs": [{"content": "押金所需人民币按照授权免押时的汇率计算，押金实际所需人民币以授权免押时的金额为准"}]}]}], "carAssistantSummaryV2": [{"title": "取车材料&租车指南", "type": 1, "url": "https://m.ctrip.com/carhire/materialsMustRead?orderId=1128137460298506&h5View=1&locale=zh_cn&orderStatus=CAR_CANCELLED&vendorId=14000&countryId=66&isHideNavBar=YES&transparentbar=1&newOsdVoucherV2=1&orderLocale=zh-CN,en-US", "note": "免费办理驾照翻译件"}, {"title": "期待您的下一次预订", "type": 6}], "attr": {"voucherVersionV2": "B", "ctripInsuranceVersion": "B", "isVehicle2": "1", "osdConfirmNew": "true", "isContractTemplates": "true", "fulfillmentVersion": "D", "osdModifyOrderVersion": "B", "osdDetailVersion": "A", "voucherVersion": "B"}, "osdModifyOrderVersion": "B", "osdModifyNewOrder": false}, "modifyInfoDto": {}, "insuranceAndXProductDesc": [{"type": 1, "desc": ["取车时，店员可能会向您推销额外保险或收费项目，请根据实际需要选购。在签署合同时，请仔细核对是否有额外收费。如被强制要求购买，请您不要签字，当场联系携程客服协助解决。"]}], "authType": 0, "useCalabiId": true, "localesInfo": [{"localeType": 1, "locale": "zh-CN", "localeName": "简体中文", "ctripInsDeclaration": {"statement": "携程声明", "partner": "亲爱的供应商伙伴：", "purchasedDesc": "该客人的保险套餐已满足当地法律的最低要求，请避免强迫客人添加任何保险，否则可能导致我处对相关当局的投诉。", "requirement": "如果认为某项保险是必须的，请写明'xx保险是必须的'并附上签名和日期。"}}, {"localeType": 2, "locale": "en-US", "localeName": "英语", "ctripInsDeclaration": {"statement": "Trip.com Statement", "partner": "Dear supplier partner:", "purchasedDesc": "The customer's insurance package already meets minimum local law requirements. Please avoid pressuring the customer to add any insurance. Doing so may result in complaints being raised.", "requirement": "If you believe a certain type of insurance is required, please indicate in writing that \"xx insurance is required\" and include your signature and date."}}]}