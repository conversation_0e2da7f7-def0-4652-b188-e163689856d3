/**
 * icon family
 */
export const iconFamily = 'crn_font_car_ctrip_v1';
// @ts-ignore
export const getIconFamily = () => global.__carIconFamily || iconFamily;

/**
 * arrow
 */
export const arrowLeftCtrip = '&#xe943;';
export const arrowLeft = '&#xe943;';
export const arrowRight = '&#xe944;';
export const arrowUp = '&#xe0b3;';
export const arrowDown = '&#xe0b2;';
export const arrowLeftLine = '&#xee52;';
export const arrowUpFilled = '&#xee57;';
export const arrowDownFilled = '&#xee58;';
export const arrowFilterUpFilled = '&#xe948;';
export const arrowFilterDownFilled = '&#xe949;';
export const arrowRadiusRight = '&#xf0764;';
/**
 * circle
 */
export const circleLine = '&#xee35;';
export const circleLineThin = '&#xee35;';
export const circleTick = '&#xf2bf;';
export const circleTickThin = '&#xf2bf;';
export const circleTickFilled = '&#xed1e;';
export const circleTickFilledThin = '&#xed1e;';
export const circleQuestion = '&#xe931;';
export const circleDot = '&#xf13b;';
export const circleWithI = '&#xee6e;';
export const circleWithSigh = '&#xee73;';
export const circleWithSighFilled = '&#xee74;';
export const circleAdd = '&#xe93e;';
export const circleAddFilled = '&#xed1c;';
export const circleCrossFilled = '&#xee77;';
export const circleSubtract = '&#xed0a;';
export const circleSubtractFilled = '&#xed1b;';
export const circleClose = '&#xed1f;';
export const circleA = '&#xee7d;';
export const circleAFilled = '&#xee7e;';
export const circleM = '&#xee83;';
export const circleMFilled = '&#xee84;';
export const circleI = '&#xef89;';
export const circleIFilled = '&#xef62;';
export const circleCross = '&#xef70;';
export const circleGPS = '&#xf2c4;';
export const circleArrowDown = '&#xf2c7;';
export const circleArrowUp = '&#xf2c2;';
export const circleTickOut = '&#xf4f1;';
export const circleWithDot = '&#xf8a2;';
/**
 * Square
 */
export const square = '&#xee5f;';
export const squareTickFilled = '&#xee5e;';
export const newSquare = '&#xf0811;';
export const newSquareTickFilled = '&#xf0812;';
/**
 * phone
 */
export const phone = '&#xe952;';
export const phoneWifi = '&#xee6d;';
export const phoneFilled = '&#xee70;';
/**
 * other
 */
export const write = '&#xee4e';
export const edit = '&#xe93b;';
export const edit2 = '&#xf0df;';
export const recommend = '&#xf62f;';
export const recommend2 = '&#xf49b;';
export const discount = '&#xf056;';
export const discountFilled = '&#xee4c;';
export const oneWay = '&#xee59;';
export const lightning = '&#xee5b';
export const lightningFilled = '&#xf2c0;';
export const zhima = '&#xe52e;';
export const ctripCredit = '&#xf0116;';

export const contacts = '&#xee60;';
export const contactsLight = '&#xf097d;';

export const booking = '&#xee61;';
export const booking2 = '&#xf2bb;';
export const book = '&#xf2c5;';
export const clock = '&#xee62;';
export const currency = '&#xee63;';
export const filter = '&#xee64;';
export const sort = '&#xee69;';
export const tick = '&#xee65;';
export const tickStrong = '&#xf2be;';
export const tickStrongShaped = '&#xf075d;';
export const heartShaped = '&#xe957;';
export const startShaped = '&#xee6a;';
export const review = '&#xee68;';
export const countDown = '&#xe930;';
export const countDownFilled = '&#xee6c;';
export const present = '&#xee71;';
export const customerService = '&#xf00d6;';
export const customerServiceFilled = '&#xee72;';
export const customerChat = '&#xf1aa6;';
export const snow = '&#xee7c;';
export const shield = '&#xee85;';
export const shieldLightning = '&#xf434;';
export const user = '&#xee87;';
export const userAdd = '&#xf2c9;';
export const license = '&#xf2c8;';
export const wallet = '&#xee88;';
export const creditCard = '&#xef1f;';
export const shop = '&#xef8a;';
export const warn = '&#xf411;';
export const warnShape = '&#xef39;';
export const clockShape = '&#xee32;';

/**
 * car facilities
 */
export const car = '&#xee8b;';
export const carLine = '&#xf2bd;';
export const carLineCross = '&#xf2c6;';
export const notice = '&#xf0709;';
export const door = '&#xee7f;';
export const luggage = '&#xee82;';
export const seat = '&#xee86;';
export const gasoline = '&#xee81;';
export const gasolineFilled = '&#xee80;';
export const gasoline2 = '&#xf2c3;';
export const gasoline3 = '&#xf74e;';
export const odometer = '&#xef60;';
export const struct = '&#xe81a;';
export const fuel = '&#xe81c;';
export const gearbox = '&#xe819;';
export const driveMode = '&#xe81d;';
export const electricCar = '&#xf74d;';
export const fuelType = '&#xf411;';
export const v2limit = '&#xf0988;';
/**
 * address
 */
export const address = '&#xee78;';
export const addressLight = '&#xe933;';
export const addressFilled = '&#xee78;';
export const addressLocating = '&#xee67;';
export const addressMap = '&#xf2cf;';
export const addressStore = '&#xf00e5;';
export const addressGuide = '&#xe936;';
export const addressArrival = '&#xf00e6;';
export const addressWay = '&#xf0166;';
/**
 * cross
 */
export const cross = '&#xe940;';

/**
 * marketing
 */
export const hot = '&#xf40f;';
export const hot2 = '&#xf4f3;';
export const hot3 = '&#xf1051;';
export const bell = '&#xf4f2;';

/**
 * map
 */
export const local = '&#xe011;';
/**
 * rentCenter
 */
export const rentCenter = '&#xeb42;';
/**
 * driver
 */
export const addDriver = '&#xe93e;';

/**
 * car must read
 */
export const ic_cancel = '&#xf4d8;';
export const ic_cost = '&#xf4d9;';
export const ic_damage = '&#xf4da;';
export const ic_deposit = '&#xf4db;';
export const ic_driver = '&#xf4dc;';
export const ic_fuel = '&#xf4dd;';
export const ic_fuelelr = '&#xf0815;';
export const ic_Inter = '&#xf4de;';
export const ic_mileage = '&#xf4df;';
export const ic_pickup = '&#xf4e0;';
export const ic_policy = '&#xf4e1;';
export const ic_restricted = '&#xf4e2;';
export const ic_sales = '&#xf4e3;';
export const ic_tips = '&#xf4e4;';
export const ic_age = '&#xf4d7;';

export const ic_home = '&#xf479;';
export const ic_users_2 = '&#xf5fa;';

export const history_delete = '&#xe92d';
export const ic_delete = '&#xe92d;';

/**
 * location
 */
export const location = '&#xee79;';
export const loading = '&#xee34;';
export const refresh = '&#xe937;';
/**
 * search Type
 */
export const intlCity = '&#xf721;';
export const intlPOIAirport = '&#xee8c;';
export const store = '&#xf725;';
export const railwayStation = '&#xee8f;';
export const markLand = '&#xf724;';
export const metroStation = '&#xf726;';
export const zone = '&#xf723;';
export const hotel = '&#xf722;';
export const secNode = '&#xf727;';
export const search = '&#xe92a;';

/**
 * list fold
 */
export const ic_top = '&#xf72e;';

export const bellFill = '&#xf504;';

/**
 * share
 */
export const share = '&#xe939;';
export const shareAndroid = '&#xf22a;';
/**
 * home
 */
export const home = '&#xe928;';
export const homeSel = '&#xf8ea;';
export const service = '&#xf1dbc;';
export const redPocket = '&#xe9db;';
export const homeBooking = '&#xe94b;';
export const fortune = '&#xe4d0;';
export const back = '&#xe943;';
export const homeBookingSel = '&#xf4ec;';
export const serviceSel = '&#xf8e9;';
export const redPocketSel = '&#xf8e8;';
export const help = '&#xe931;';
export const arrowForward = '&#xe008;';
export const warning = '&#xe92f;';
export const privilege = '&#xf085c;';

/**
 * easylife
 */
export const easyLife = '&#xf891;';
export const wycircleTickFilled = '&#xee4d;';
export const wycircleTick = '&#xee5c;';

export const like = '&#xe956;';

export const huLicense = '&#xf0080;';
export const waiLicense = '&#xf0081;';

export const tripBell = '&#xf00fb;';

export const miniSearchEdit = '&#xe93c;';
export const invoice = '&#xf0119;';

export const tickStronger = '&#xf011a;';
export const pickupWay = '&#xf0166;';

export const sendCarUser = '&#xf032d;';

// 信用租
export const xyzText = '&#xf0382;';
export const xyzFree = '&#xf03f8;';

// 区域页
export const tick_area = '&#xe411;';
export const location_area = '&#xed05;';

// 证件认证
export const eyeOff = '&#xf0463;';
export const eyeOn = '&#xf0464;';
export const scanLoading = '&#xf0465;';

// 产品详情页激励icon
export const encourageIcon = '&#xea01;';
export const camera = '&#xf0a40;';

// 区域页分类图标
export const areaMetro = '&#xe3f7;';
export const areaRegion = '&#xe418;';
export const areaPlane = '&#xe95b;';
export const areaTrain = '&#xe95d;';
export const areaBus = '&#xe965;';
export const shoppingPoint = '&#xe689;';

export const copy = '&#xf01d6;';

export const ic_qunar_user = '&#xf06d2;';

// 免异地还车费
export const diffFree = '&#xf051a;';

// 机票闭环添加驾驶员
export const driverMore = '&#xf0742;';
export const driverEdit = '&#xf0740;';
export const driverList = '&#xf0741;';
export const panelClose = '&#xed1f;';

// 优惠
export const ic_check = '&#xf0868;';

// 租车中心
export const carRentalCenter = '&#xf08cf;';
export const ic_check_2 = '&#xf08d0;';
export const right_arrow = '&#xf08d1;';

// 权益解锁数量字体
export const memberLockedOne = '&#xf09ef;';
export const memberLockedTwo = '&#xf09f0;';
export const memberLockedThree = '&#xf09f1;';
export const memberLockedFour = '&#xf09f2;';
export const memberLockedFive = '&#xf09f3;';
export const memberModalClose = '&#xe93f;';
// 首页重构
export const num1 = '&#xf09ef;';
export const num2 = '&#xf09f0;';
export const num3 = '&#xf09f1;';
export const num4 = '&#xf09f2;';
export const num5 = '&#xf09f3;';

// 新填写页
export const deposit = '&#xf09b8;';
export const mileage = '&#xf09b9;';
export const miniImage = '&#xf09ba;';
export const carConfig = '&#xf09bb;';
export const circleTickRound = '&#xf09bc;';
export const arrowRightStrong = '&#xf09bd;';
export const confirm = '&#xf09be;';
export const cancellation = '&#xf09bf;';
export const pickUpWays = '&#xf09c0;';
export const carConfig2 = '&#xf09c2;';
export const arrowRightFilled = '&#xf09c3;';
export const signBtnArrow = '&#xee90;';
export const serviceInfo = '&#xef8d;';
export const serviceAdd = '&#xf0b73;';
export const serviceClose = '&#xf0b74;';
export const serviceRight = '&#xf0c39;';
export const serviceContaint = '&#xf0b75;';

export const hi = '&#xf0d0c;';
export const ic_checkBox = '&#xf0ce3;';
export const icon_close = '&#xf0d56;';

export const cancelPenaltyAgree = '&#xf0dd9';
export const cancelPenaltyRefuse = '&#xf0dda';
export const backToTop = '&#xe1c7;';

// 填写页优化
export const calendar = '&#xe94c';

// 海外无结果详情页
export const remind = '&#xe010;';
// 自助取还
export const complete = '&#xf14e6;';
export const vehicleControl = '&#xf1616;';
export const lock = '&#xf1617;';
export const unlock = '&#xf1618;';
export const selfService = '&#xf1619;';

// 海外车型重构二期
export const electric = '&#xf15fe;';
export const hybrid = '&#xf1600;';
export const dieseloil = '&#xf15ff;';
export const quattro = '&#xf15fd;';

// Loading加载
export const rotateLoading = '&#xeb44;';

export const squareQuestion = '&#xf17c5;';

export const arrowRightList = '&#xf1883;';

export const faceAuthIcon = '&#xf18e9;';
export const copyIcon = '&#xf17d2;';

// 搜筛中插
export const bulbIcon = '&#xf20d7;';
export const account = '&#xf0a3f;';

export const userName = '&#xf2150;';
export const mobile = '&#xf214f;';
