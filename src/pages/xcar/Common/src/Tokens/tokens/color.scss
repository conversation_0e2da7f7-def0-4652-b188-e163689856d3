$R_0_0_0_0_0: rgba(0, 0, 0, 0);
$R_0_0_0_0_7: rgba(0, 0, 0, 0.7);
$R_0_0_0_0_4: rgba(0, 0, 0, 0.4);
$R_0_0_0_0_3: rgba(0, 0, 0, 0.3);
$R_0_160_233_0_5: rgba(0, 160, 233, 0.5);
$R_0_0_0_0_14: rgba(0, 0, 0, 0.14);
$R_218_240_255: rgb(218, 240, 255);
$R_239_250_255: rgb(239, 250, 255);
$R_11_166_110_0_08: rgba(11, 166, 110, 0.08);
$R_11_166_110_0_04: rgba(11, 166, 110, 0.04);
$R_234_212_155_0_8: rgba(234, 212, 155, 0.8);
$R_159_117_49_0_3: rgba(159, 117, 49, 0.3);
$R_46_182_170_0_1: rgba(46, 182, 170, 0.1);
$R_0_0_0_0_65: rgba(0, 0, 0, 0.65);
$R_34_34_34_0_7: rgba(34, 34, 34, 0.7);
$R_3_133_246_0_1: rgba(3, 133, 246, 0.1);
$R_51_51_51_0_06: rgba(51, 51, 51, 0.06);
$R_46_49_65: rgb(46, 49, 65);
$R_174_191_212: rgb(174, 191, 212);
$R_15_41_77_0_08: rgba(15, 41, 77, 0.08);
$R_225_33_255_0_4: rgba(225, 33, 255, 0.4);
$R_154_204_249: rgb(154, 204, 249);
$R_0_0_0_0_6: rgba(0, 0, 0, 0.6);
$R_0_0_0_0_5: rgba(0, 0, 0, 0.5);
$R_255_216_213_0_7: rgba(255, 216, 213, 0.7);
$R_255_255_255_0_4: rgba(255, 255, 255, 0.4);
$R_255_255_255_0: rgba(255, 255, 255, 0);
$R_255_255_255_0_5: rgba(255, 255, 255, 0.5);
$R_242_112_0_0_2: rgba(242, 112, 0, 0.2);
$R_0_0_0_0_05: rgba(0, 0, 0, 0.05);
$R_254_189_147_1: rgba(254, 189, 147, 1);
$R_0_174_115_0_1: rgba(0, 174, 115, 0.1);
$R_255_255_255_0_13: rgba(255, 255, 255, 0.13);
$R_255_255_255_0_6: rgba(255, 255, 255, 0.6);
$R_255_199_194_0_7: rgba(255, 199, 194, 0.7);
$R_255_160_160: rgb(255, 160, 160);
$R_255_255_255_0_21: rgba(255, 255, 255, 0.21);
$R_0_32_59_0_02: rgba(0, 32, 59, 0.02);
$R_233_241_255_0: rgba(233, 241, 255, 0);
$R_244_250_255_0: rgba(244, 250, 255, 0);
$R_0_134_246_0_08: rgba(0, 134, 246, 0.08);
$R_0_141_155_0_5: rgba(0, 141, 155, 0.5);
$R_201_224_254_0_55: rgba(201, 224, 254, 0.55);
$R_239_244_248_0: rgba(239, 244, 248, 0);
$R_255_119_0: rgb(255, 119, 0);
$R_255_157_10: rgb(255, 157, 10);
$R_255_198_198: rgb(255, 198, 198);
$R_161_221_255_0_22: rgba(161, 221, 255, 0.22);
$R_104_185_255: rgb(104, 185, 255);
$R_17_223_119: rgb(17, 223, 119);
$R_0_222_174: rgb(0, 222, 174);
$R_255_150_48: rgb(255, 150, 48);
$R_255_32_75: rgb(255, 32, 75);
$R_255_255_255_0_1: rgba(255, 255, 255, 0.1);
$R_0_0_0_0_08: rgba(0, 0, 0, 0.08);
$R_223_248_240_0_7: rgba(223, 248, 240, 0.7);
$R_70_115_190_0_25: rgba(70, 115, 190, 0.25);
$R_17_17_17_0_10: rgba(17, 17, 17, 0.1);
$R_0_0_0_004: rgba(0, 0, 0, 0.04);
$C_transparent: transparent;
$C_007fe8: #007fe8;
$C_0072d1: #0072d1;
$C_0086f6: #0086f6;
$C_2598f8: #2598f8;
$C_4caaf8: #4caaf8;
$C_72bbf9: #72bbf9;
$C_99CEFB: #99cefb;
$C_bfe0fc: #bfe0fc;
$C_E6F3FE: #e6f3fe;
$C_f3f8fe: #f3f8fe;
$C_0186F6: #0186f6;
$C_2698f7: #2698f7;
$C_00A0E9: #00a0e9;
$C_5678A8: #5678a8;
$C_4eaaf8: #4eaaf8;
$C_397FF6: #397ff6;
$C_FFF1EB: #fff1eb;
$C_FFE9E7: #ffe9e7;
$C_FFF1F1: #fff1f1;
$C_0385F6: #0385f6;
$C_2F69E2: #2f69e2;
$C_113DA3: #113da3;
$C_007AF5: #007af5;
$C_5578A8: #5578a8;
$C_1677ff: #1677ff;
$C_0083FF: #0083ff;
$C_6FB8F6: #6fb8f6;
$C_fafcff: #fafcff;
$C_f2f9ff: #f2f9ff;
$C_4c97ff: #4c97ff;
$C_f4faff: #f4faff;
$C_e9f1ff: #e9f1ff;
$C_4673be: #4673be;
$C_006edc: #006edc;
$C_0176e6: #0176e6;
$C_9DADC7: #9dadc7;
$C_B4CBED: #b4cbed;
$C_8dc8ff: #8dc8ff;
$C_edf7ff: #edf7ff;
$C_0f4999: #0f4999;
$C_062A50: #062a50;
$C_0040AC: #0040ac;
$C_38A2FF: #38a2ff;
$C_EFF4F8: #eff4f8;
$C_FF0000: #ff0000;
$C_FFF4F1: #fff4f1;
$C_FF0404: #ff0404;
$C_FF0B0B: #ff0b0b;
$C_F9FBFC: #f9fbfc;
$C_1A41FF: #1a41ff;
$C_E4EEFF: #e4eeff;
$C_CCECFF: #ccecff;
$C_EBF6FF: #ebf6ff;
$C_e3f2ff: #e3f2ff;
$C_C9E0FE: #c9e0fe;
$C_add9ff: #add9ff;
$C_006ff6: #006ff6;
$C_F5F9FF: #f5f9ff;
$C_009c66: #009c66;
$C_00ae73: #00ae73;
$C_00b87a: #00b87a;
$C_24c28d: #24c28d;
$C_4bcea2: #4bcea2;
$C_74d8b6: #74d8b6;
$C_99e1c8: #99e1c8;
$C_bfeddd: #bfeddd;
$C_e6f8f1: #e6f8f1;
$C_f2fbf7: #f2fbf7;
$C_45A83A: #45a83a;
$C_19A094: #19a094;
$C_00AE73: #00ae73;
$C_F2FBF8: #f2fbf8;
$C_01AE73: #01ae73;
$C_26C28D: #26c28d;
$C_d96500: #d96500;
$C_f27000: #f27000;
$C_ff7700: #ff7700;
$C_ff8b26: #ff8b26;
$C_ffa04c: #ffa04c;
$C_ffb473: #ffb473;
$C_fdc899: #fdc899;
$C_ffdcbf: #ffdcbf;
$C_fff1e5: #fff1e5;
$C_fff8f2: #fff8f2;
$C_ff6600: #ff6600;
$C_FF732F: #ff732f;
$C_FF4E15: #ff4e15;
$C_FF3E00: #ff3e00;
$C_462003: #462003;
$C_FFF3CA: #fff3ca;
$C_FF5722: #ff5722;
$C_FFFAF3: #fffaf3;
$C_FF6F00: #ff6f00;
$C_FF9500: #ff9500;
$C_FFA04D: #ffa04d;
$C_FF9913: #ff9913;
$C_FEF1E6: #fef1e6;
$C_FF6501: #ff6501;
$C_FE6600: #fe6600;
$C_93582f: #93582f;
$C_d06117: #d06117;
$C_cc8d61: #cc8d61;
$C_FFF6F0: #fff6f0;
$C_FFF3F0: #fff3f0;
$C_FFEAE5: #ffeae5;
$C_ff3500: #ff3500;
$C_FEB64E: #feb64e;
$C_FF5A06: #ff5a06;
$C_d01307: #d01307;
$C_e9170a: #e9170a;
$C_f63a2d: #f63a2d;
$C_f85d53: #f85d53;
$C_f97f78: #f97f78;
$C_fba49d: #fba49d;
$C_fcc5c2: #fcc5c2;
$C_fee8e5: #fee8e5;
$C_fef2f1: #fef2f1;
$C_F85E53: #f85e53;
$C_F5190A: #f5190a;
$C_808A92: #808a92;
$C_E96457: #e96457;
$C_EE3B28: #ee3b28;
$C_FEF3F2: #fef3f2;
$C_FF2F1E: #ff2f1e;
$C_FFEEE7: #ffeee7;
$C_FF481D: #ff481d;
$C_FEE8E6: #fee8e6;
$C_F5180B: #f5180b;
$C_F51A0B: #f51a0b;
$C_F8665B: #f8665b;
$C_F03831: #f03831;
$C_E1C285: #e1c285;
$C_C9963E: #c9963e;
$C_654C0A: #654c0a;
$C_9F7531: #9f7531;
$C_FFFDF6: #fffdf6;
$C_EAD6A1: #ead6a1;
$C_F7F7F7: #f7f7f7;
$C_FFFBE0: #fffbe0;
$C_DE5B10: #de5b10;
$C_FEECCF: #feeccf;
$C_AE8032: #ae8032;
$C_FFF3DF: #fff3df;
$C_F8E4C3: #f8e4c3;
$C_FFDFC6: #ffdfc6;
$C_DFEBF8: #dfebf8;
$C_753400: #753400;
$C_333333: #333333;
$C_666: #666;
$C_999: #999;
$C_aaa: #aaa;
$C_bbb: #bbb;
$C_ccc: #ccc;
$C_ddd: #ddd;
$C_eee: #eee;
$C_f4f4f4: #f4f4f4;
$C_f8f8f8: #f8f8f8;
$C_455873: #455873;
$C_0F294D: #0f294d;
$C_030303: #030303;
$C_8592A6: #8592a6;
$C_DADFE6: #dadfe6;
$C_e5f2fe: #e5f2fe;
$C_222: #222;
$C_F5F5F5: #f5f5f5;
$C_E0E0E0: #e0e0e0;
$C_EFECEA: #efecea;
$C_EFEADA: #efeada;
$C_BBC9DC: #bbc9dc;
$C_E5EAF2: #e5eaf2;
$C_E3E3E3: #e3e3e3;
$C_D4D4D4: #d4d4d4;
$C_EFF1F6: #eff1f6;
$C_FAFAFA: #fafafa;
$C_D8D8D8: #d8d8d8;
$C_EBEBEB: #ebebeb;
$C_AABBD3: #aabbd3;
$C_5C7080: #5c7080;
$C_E6E6E6: #e6e6e6;
$C_F7F8F9: #f7f8f9;
$C_F6F6F6: #f6f6f6;
$C_eaeaea: #eaeaea;
$C_F1F5F8: #f1f5f8;
$C_FEFEFF: #fefeff;
$C_111111: #111111;
$C_222630: #222630;
$C_ebeff5: #ebeff5;
$C_888888: #888888;
$C_C5C5C5: #c5c5c5;
$C_ced2d9: #ced2d9;
$C_F2F6F9: #f2f6f9;
$C_3A4A99: #3a4a99;
$C_eff2f5: #eff2f5;
$C_e7e7e7: #e7e7e7;
$C_E8EBF2: #e8ebf2;
$C_5578a7: #5578a7;
$C_89a0c2: #89a0c2;
$C_99aecb: #99aecb;
$C_abbbd4: #abbbd4;
$C_bac9db: #bac9db;
$C_cdd6e5: #cdd6e5;
$C_dee4ee: #dee4ee;
$C_eef1f6: #eef1f6;
$C_f6f8fa: #f6f8fa;
$C_f9fafe: #f9fafe;
$C_DCE0E5: #dce0e5;
$C_f8fafd: #f8fafd;
$C_F2F8FE: #f2f8fe;
$C_99aeca: #99aeca;
$C_f5f6fa: #f5f6fa;
$C_E9F2FF: #e9f2ff;
$C_EEEEEE: #eeeeee;
$C_FFF: #fff;
$C_F9DF02: #f9df02;
$C_000: #000;
$C_007FE9: #007fe9;
$C_C4C4C4: #c4c4c4;
$C_00a7fa: #00a7fa;
$C_0076f5: #0076f5;
$C_ffa50a: #ffa50a;
$C_ff9d0a: #ff9d0a;
$C_19A0F0: #19a0f0;
$C_0B67D1: #0b67d1;
$C_EDFAFF: #edfaff;
$C_E7F3FF: #e7f3ff;
$C_DFF3FF: #dff3ff;
$C_007FEA: #007fea;
$C_4DAEFF: #4daeff;
$C_218CFF: #218cff;
$C_FF8739: #ff8739;
$C_FF664B: #ff664b;
$C_94D1FF: #94d1ff;
$C_D1D7DC: #d1d7dc;
$C_E2F1FF: #e2f1ff;
$C_F1F8FF: #f1f8ff;
$C_F3F9FF: #f3f9ff;
$C_EAF5FF: #eaf5ff;
$C_F9FCFF: #f9fcff;
$C_71BDFF: #71bdff;
$C_8ACAFF: #8acaff;
$C_fb5857: #fb5857;
$C_ff9365: #ff9365;
$C_8E5E11: #8e5e11;
$C_FFF8E9: #fff8e9;
$C_FFFCF8: #fffcf8;
$C_DDE4ED: #dde4ed;
$C_F5F5F9: #f5f5f9;
$C_09BB07: #09bb07;
$C_F76260: #f76260;
$C_2eb6aa: #2eb6aa;
$C_BABABA: #bababa;
$C_FF6B61: #ff6b61;
$C_F64D41: #f64d41;
$C_FFF1E6: #fff1e6;
$C_FFFAF2: #fffaf2;
$C_F2C100: #f2c100;
$C_35BC74: #35bc74;
$C_E5F8F1: #e5f8f1;
$C_F0F2F5: #f0f2f5;
$C_366AB3: #366ab3;
$C_FFEAC1: #ffeac1;
$C_FFEBC9: #ffebc9;
$C_F63B2E: #f63b2e;
$C_BFCDE0: #bfcde0;
$C_02A971: #02a971;
$C_b7e4ff: #b7e4ff;
$C_ffffdd: #ffffdd;
$C_c16ff8: #c16ff8;
$C_F0FAFF: #f0faff;
$C_1CC677: #1cc677;
$C_B809D3: #b809d3;
$C_FCDEFF: #fcdeff;
$C_01B87A: #01b87a;
$C_5378A6: #5378a6;
$C_474747: #474747;
$C_E5470F: #e5470f;
$C_fd8f3a: #fd8f3a;
$C_34C6B6: #34c6b6;
$C_3E89FA: #3e89fa;
$C_5F84FE: #5f84fe;
$C_787FFE: #787ffe;
$C_eb7449: #eb7449;
$C_D01508: #d01508;
$C_7B3E29: #7b3e29;
$C_FFF7F1: #fff7f1;
$C_FFEFE5: #ffefe5;
$C_FBD9C3: #fbd9c3;
$C_F1C09F: #f1c09f;
$C_D2D2D2: #d2d2d2;
$C_D0D0D0: #d0d0d0;
$C_F9F9F9: #f9f9f9;
$C_F98078: #f98078;
$C_FF7B5B: #ff7b5b;
$C_F94E3A: #f94e3a;
$C_FF8700: #ff8700;
$C_FF9908: #ff9908;
$C_FF714E: #ff714e;
$C_F53B2E: #f53b2e;
$C_FFAFAF: #ffafaf;
$C_FF6D4C: #ff6d4c;
$C_FF4C1D: #ff4c1d;
$C_FFFAF9: #fffaf9;
$C_FEC899: #fec899;
$C_AB5647: #ab5647;
$C_3167B8: #3167b8;
$C_C58A7F: #c58a7f;
$C_0080FF: #0080ff;
$C_93300D: #93300d;
$C_FCF2F1: #fcf2f1;
$C_20324B: #20324b;
$C_3B5272: #3b5272;
$C_7292C0: #7292c0;
$C_4A6C9B: #4a6c9b;
$C_61B7FF: #61b7ff;
$C_73BCFA: #73bcfa;
$C_00203B: #00203b;
$C_034db5: #034db5;
$C_E7F1FF: #e7f1ff;
$C_DFE4EA: #dfe4ea;
$C_EDF2F8: #edf2f8;
$C_F46518: #f46518;
$C_FF7529: #ff7529;
$C_72BCFA: #72bcfa;
$C_0172D0: #0172d0;
$C_FFDDDD: #ffdddd;
$C_F5F8FA: #f5f8fa;
$C_0086F8: #0086f8;
$C_B4E6FF: #b4e6ff;
$C_FFF2BF: #fff2bf;
$C_CCD6E5: #ccd6e5;
$C_F7FAFD: #f7fafd;
$C_05B87A: #05b87a;
$C_EFFAFF: #effaff;
$C_CEE8FF: #cee8ff;
$C_979797: #979797;
$C_FDEBCE: #fdebce;
$C_784323: #784323;
$C_8D472E: #8d472e;
$C_4E251C: #4e251c;
$C_fae2b9: #fae2b9;
$C_ffeed5: #ffeed5;
$C_FFF8EC: #fff8ec;
$C_CCA76B: #cca76b;
$C_555555: #555555;
$C_FFF4E2: #fff4e2;
$C_FFF9EF: #fff9ef;
$C_B47B43: #b47b43;
$C_3E3E3E: #3e3e3e;
$C_86898A: #86898a;
$C_494949: #494949;
$C_787878: #787878;
$C_384A8C: #384a8c;
$C_192039: #192039;
$C_AFB7D1: #afb7d1;
$C_4B5F9D: #4b5f9d;
$C_27396F: #27396f;
$C_6376B7: #6376b7;
$C_3A4C8B: #3a4c8b;
$C_6275b6: #6275b6;
$C_47559F: #47559f;
$C_383C5F: #383c5f;
$C_B5BBD9: #b5bbd9;
$C_626B9D: #626b9d;
$C_465290: #465290;
$C_7C8ADC: #7c8adc;
$C_49579E: #49579e;
$C_7b89db: #7b89db;
$C_595cb3: #595cb3;
$C_e3edfe: #e3edfe;
$C_7EA8B5: #7ea8b5;
$C_36647F: #36647f;
$C_D3F5FF: #d3f5ff;
$C_E8F9FE: #e8f9fe;
$C_5E9BAE: #5e9bae;
$C_CBDCE1: #cbdce1;
$C_6FABBF: #6fabbf;
$C_6298AA: #6298aa;
$C_8ABCCC: #8abccc;
$C_55859A: #55859a;
$C_6293a7: #6293a7;
$C_218db1: #218db1;
$C_e3f6fc: #e3f6fc;
$C_DDB57E: #ddb57e;
$C_9D763F: #9d763f;
$C_F1E1CB: #f1e1cb;
$C_DBB981: #dbb981;
$C_D0A357: #d0a357;
$C_E6C489: #e6c489;
$C_C8A16A: #c8a16a;
$C_debb81: #debb81;
$C_7AA2DD: #7aa2dd;
$C_416FB2: #416fb2;
$C_CEE2FF: #cee2ff;
$C_F0F6FF: #f0f6ff;
$C_CADAF1: #cadaf1;
$C_6090D4: #6090d4;
$C_91B7EE: #91b7ee;
$C_4C7EC6: #4c7ec6;
$C_78a0db: #78a0db;
$C_4d86d8: #4d86d8;
$C_D7F1FF: #d7f1ff;
$C_EFF9FF: #eff9ff;
$C_99DCFD: #99dcfd;
$C_46B3FF: #46b3ff;
$C_0092F8: #0092f8;
$C_0FBFFF: #0fbfff;
$C_008AF5: #008af5;
$C_2c9af7: #2c9af7;
$C_F20C00: #f20c00;
$C_dce4ed: #dce4ed;
$C_55B7FF: #55b7ff;
$C_49A0FF: #49a0ff;
$C_FF8678: #ff8678;
$C_FFEFEF: #ffefef;
$C_F8FBFF: #f8fbff;
$C_0286f6: #0286f6;
$C_5B5B5B: #5b5b5b;
$C_FF502C: #ff502c;
$C_F6F9FD: #f6f9fd;
$C_FF480E: #ff480e;
$C_912F00: #912f00;
$C_d7231d: #d7231d;
$C_FF2323: #ff2323;
$C_284B7D: #284b7d;
$C_d6d6d6: #d6d6d6;
$C_F51909: #f51909;
$C_5c5c5c: #5c5c5c;
$C_ca741c: #ca741c;
$C_FFF0DB: #fff0db;
$C_0ba66e: #0ba66e;
$C_FFEBDB: #ffebdb;
$C_F2200C: #f2200c;
$C_fff6f5: #fff6f5;
$C_3568FF: #3568ff;
$C_3B98FE: #3b98fe;
$C_FFC25F: #ffc25f;
$C_E98811: #e98811;
$C_AE6214: #ae6214;
$C_D5E8FF: #d5e8ff;
$C_F1F7FF: #f1f7ff;
$C_FF5500: #ff5500;
$C_888: #888;
$C_E6FAF3: #e6faf3;
$C_24B381: #24b381;
$C_F0F0F0: #f0f0f0;
$C_9f5c5c: #9f5c5c;
$C_ebf3ff: #ebf3ff;
$C_2ABA87: #2aba87;
$C_F5F7FA: #f5f7fa;
$C_f97b20: #f97b20;
$C_FF9900: #ff9900;
$C_F8FAFB: #f8fafb;
$C_fff8ed: #fff8ed;
$C_dbdcde: #dbdcde;
$C_1856AE: #1856ae;
$C_E0F0FD: #e0f0fd;
$C_FFF4E1: #fff4e1;
$C_00D19F: #00d19f;
$C_E3F9EF: #e3f9ef;
$C_008FFF: #008fff;
$C_FCFDFF: #fcfdff;
$C_D9EEFE: #d9eefe;
$C_E5F7FE: #e5f7fe;
$C_E1F2FF: #e1f2ff;
$C_FF8800: #ff8800;
$C_CBCBCB: #cbcbcb;
$C_5D6F96: #5d6f96;
$C_D5DAE1: #d5dae1;
$C_657c96: #657c96;
$C_318BF7: #318bf7;
$C_2aa1ff: #2aa1ff;
$C_F5F8FB: #f5f8fb;
$C_3A3A3A: #3a3a3a;
$C_E5E5E5: #e5e5e5;
$C_EBF4FF: #ebf4ff;
$C_272727: #272727;
$C_F0F7FF: #f0f7ff;
$C_FF6D2C: #ff6d2c;
$C_A95B28: #a95b28;
$C_DB712A: #db712a;
$C_008CFF: #008cff;
$C_0070FD: #0070fd;
$C_FD7300: #fd7300;
$C_fd7300: #fd7300;
$C_ff822d: #ff822d;
$C_e9edf1: #e9edf1;
$C_fff0dc: #fff0dc;
$C_602a12: #602a12;
$C_E2EFFF: #e2efff;
$C_FFF4F5: #fff4f5;
$C_FFF6EA: #fff6ea;
$C_F24C3D: #f24c3d;
$white: #fff;
$litleWhite: #f9df02;
$textWhite: #fff;
$black: #000;
$transparent: transparent;
$transparentBase: transparent;
$blackTransparent: rgba(0, 0, 0, 0.7);
$modalShadow: rgba(0, 0, 0, 0.7);
$cardShadow: rgba(0, 0, 0, 0.14);
$orderCardStatusBlueShadow: #007fe9;
$blueBtnShadow: #6fb8f6;
$bottomBarShadow: #c4c4c4;
$blueBase: #0086f6;
$greenBase: #00b87a;
$orangeBase: #ff7700;
$redBase: #f5190a;
$grayBase: #999;
$blueGrayBase: #111111;
$lightRedBase: #ff481d;
$blueDeepBase: #006ff6;
$blueIcon: #2598f8;
$greenIcon: #24c28d;
$orangeIcon: #ff8b26;
$redIcon: #f63a2d;
$blueGrayIcon: #999;
$blueBorder: #bfe0fc;
$greenBorder: #bfeddd;
$orangeBorder: #ffdcbf;
$redBorder: #fcc5c2;
$grayBorder: #eee;
$grayDescLine: #bbb;
$darkGrayBorder: #ccc;
$lightRedBorder: #ff2f1e;
$blueBg: #f3f8fe;
$blueBgSecondary: #e6f3fe;
$blueBg47: #f5f9ff;
$greenBg: #f2fbf7;
$orangeBg: #fff8f2;
$orangeBgLight: #fff8f2;
$orangeBgLight2: #fff3ca;
$redBg: #fef2f1;
$grayBg: #f4f4f4;
$grayBgSecondary: #f8f8f8;
$blueGrayBg: #eef1f6;
$tableBg: #f2f8fe;
$grayPlaceholder: #eeeeee;
$tableGrayBg: #f5f6fa;
$blackBase: #111111;
$ipxGragBg: #f4f4f4;
$lightRedBg: #ffeee7;
$redLightBg: #fee8e6;
$blueClick: #007fe8;
$orangePrice: #ff6600;
$orangePrice2: #ff9500;
$priceColor: #ff6600;
$fontPrimary: #111111;
$fontSecondary: #666;
$fontSubDark: #999;
$fontSubLight: #ddd;
$fontGrayBlue: #aaa;
$fontOrangeDark: #462003;
$fontBlueDark: #99aeca;
$fontGrayDark: #222;
$linearGradientBlueLight: #00a7fa;
$linearGradientBlueDark: #0076f5;
$linearGradientOrangeLight: #ffa50a;
$linearGradientOrangeDark: #ff7700;
$linearGradientOrangeOrderLight: #ff9d0a;
$linearGradientOrangeOrderDark: #ff7700;
$linearGradientLightBlue: #19a0f0;
$linearGradientDarkBlue: #0b67d1;
$linearGradientBlueBgLight: #edfaff;
$linearGradientBlueBgDark: #e7f3ff;
$linearGradientXyzBgBlue: #dff3ff;
$linearGradientGuideBlueLight: #007fea;
$linearGradientGuideBlueDark: #4daeff;
$linearGradientOrderCardBlueLight: #00a7fa;
$linearGradientOrderCardBlueDark: #218cff;
$linearGradientOrderCardOrangeLight: #ff8739;
$linearGradientOrderCardOrangeDark: #ff664b;
$linearGradientPlateDark: #94d1ff;
$linearGradientPlateOtherDark: #d1d7dc;
$linearGradientItineraryCardStop1: #e2f1ff;
$linearGradientItineraryCardStop2: #f1f8ff;
$linearGradientItineraryCardStop3: #f3f9ff;
$linearGradientCarCenterStop1: rgba(218, 240, 255);
$linearGradientCarCenterStop2: rgba(239, 250, 255);
$linearGradientCancelTipDark: #eaf5ff;
$linearGradientCancelTipLight: #f9fcff;
$linearGradientRecommendLabelStop1: rgba(11, 166, 110, 0.8);
$linearGradientRecommendLabelStop2: rgba(11, 166, 110, 0.4);
$linearGradientBlueBottom: #71bdff;
$linearGradientBluePercentBg: #8acaff;
$linearGradientRushGrabDark: #fb5857;
$linearGradientRushGrabLight: #ff9365;
$newLinearGradientRushGrabDark: rgba(255, 119, 0);
$newLinearGradientRushGrabLight: rgba(255, 157, 10);
$greenInsurance: #45a83a;
$osdGreenInsurance: #00b87a;
$radioBorderColor: #dce0e5;
$radioDisableBackGroundColor: #cdd6e5;
$horizontalSelectedColor: #0186f6;
$horizontalColor: #666;
$mapTabSelectdColor: #0086f6;
$mapTabColor: #666;
$mapLocalColor: #666;
$btnBorder: #2698f7;
$guideStepSequenceColor: #ccc;
$hasEndColor: #999;
$expreienceBtnBorderColor: #0086f6;
$expreienceBtnTextColor: #0086f6;
$btnDarkBlueBorder: #0385f6;
$tableBorderColor: #dee4ee;
$tableBackgroundColor: #f8fafd;
$easylifeBg: #e1c285;
$easylifeText: #c9963e;
$easylifeTextDark: #654c0a;
$easylifeTag: #9f7531;
$easylifeTagBg: #fffdf6;
$easylifeShadowColor: rgba(234, 212, 155, 0.8);
$easylifeBorderColor: #ead6a1;
$easylifeText2: #8e5e11;
$easylifeBg2: #fff8e9;
$easylifeBg3: #fffcf8;
$easylifeBorderColor2: rgba(159, 117, 49, 0.3);
$easylifeGray1: #dde4ed;
$easylifeGray2: #f5f5f9;
$easylifeBlue: #006ff6;
$easylifeTextBlack: #111111;
$essylifeTextGray: #888888;
$sesameBase: #19a094;
$sesamePrimary: #00a0e9;
$sesameFontPrimary: #222;
$sesameSuccess: #09bb07;
$sesameFail: #f76260;
$sesameLabel: #2eb6aa;
$sesameLabelBg: rgba(46, 182, 170, 0.1);
$sesameClose: #bababa;
$likeGradient1: #ff6b61;
$likeGradient2: #f64d41;
$labelGreenBg: #e6f8f1;
$labelGreenText: #00b87a;
$labelOrangeBg: #fff1e6;
$labelOrangeText: #ff7700;
$labelBlueBg: #e6f3fe;
$labelBlueText: #0086f6;
$bookbarTipsGreenBg: #e6f8f1;
$bookbarTipsGreenText: #00b87a;
$bookbarTipsOrangeBg: #fffaf2;
$deleteBg: #f63a2d;
$certificateTypeInvaildTip: #f2c100;
$certificateTypeInvaildColor: #ccc;
$certificateActive: #f2f8fe;
$certificateActiveBorder: #4eaaf8;
$blueReview: #5678a8;
$blueNps: #397ff6;
$blueLightNps: #e6f3fe;
$blueVehicleHdBg: #e9f2ff;
$orangePayCountDown: #ff5722;
$orangeTipBg: #fffaf3;
$labelBlueBorder: #4eaaf8;
$selfHelpBg: #f6f8fa;
$extrasColor: #0f294d;
$extrasTextColor: #8592a6;
$dayText: #dadfe6;
$vehicleSimilar: #e5f2fe;
$verifyTitle: #ff6600;
$replenishSubTitle: #fffbe0;
$replenishSubTitleColor: #de5b10;
$iconCountDownFilled: #ffa04d;
$insLableBg: #eee;
$insStatusRed: #e96457;
$bbkNpsBgColor: #fff;
$bbkNpsTitleColor: #111111;
$bbkNpsWillStatusColor: #111111;
$bbkNpsPointNumColor: #0086f6;
$bbkNpsPointNumBgColor: #0086f6;
$bbkNpsSelectColor: #0086f6;
$bbkNpsInputBg: #f6f8fa;
$successGreen: #00ae73;
$blueShadow: #99cefb;
$diffBg: #fef3f2;
$underlineColor: #e3e3e3;
$tipModalBlackTransparent: rgba(0, 0, 0, 0.65);
$hasPayStatusColor: #35bc74;
$freeDepositLabelColor: #e5f8f1;
$lightPinkBorder: #f0f2f5;
$discountRed: #f85e53;
$fontBlackDark: #455873;
$fontBlackLight: #0f294d;
$orangePayLabel: #ff6f00;
$dialogTitle: #030303;
$driverError: #f5190a;
$driverRadioBorder: #aaa;
$dirverRadioVaildBorder: #eee;
$driverCertificateTip: #ff9913;
$licenseLabel: #366ab3;
$foreignLicenseLabel: rgba(34, 34, 34, 0.7);
$licenseLabelNew: rgba(3, 133, 246, 0.1);
$foreignLicenseLabelNew: rgba(51, 51, 51, 0.6);
$zhimaBorder: rgba(0, 160, 233, 0.5);
$defaultLabel: #5678a8;
$secLabel: #89a0c2;
$lineColor: #eeeeee;
$cityLabel: rgba(46, 49, 65);
$cityLabelText: #ffeac1;
$cityLabelText2: #ffebc9;
$splitGray: #ccc;
$shadowGray: rgba(174, 191, 212);
$bookbarBgColor: #fff;
$filterButtonColor: #f8f8f8;
$tipsRed: #f63b2e;
$addedServiceOrangeTag: #ff6600;
$addedServiceRedTag: #ee3b28;
$shadowColor: rgba(15, 41, 77, 0.8);
$switchBgInactive: #ccc;
$warning: #ff9913;
$marketGray: #bfcde0;
$soldOutLabelBgColor: #efecea;
$soldOutEasyLifeLabelBgColor: #efeada;
$numberBackgroundColor: #bbc9dc;
$numberLineBackgroundColor: #e5eaf2;
$duanwuColor: #02a971;
$festivalColor: #b7e4ff;
$festivalSelectedColor: #ffffdd;
$nationalDayColor: #fff;
$nationalDaySelectedColor: #c16ff8;
$insuranceLabelBg: #00ae73;
$priceFallBg: #f2fbf8;
$priceDiffTableBorder: #eff1f6;
$zhimaLabelBg: #f0faff;
$preAuthLabelBg: #1cc677;
$elevenFestivalLabelBorderColor: rgba(225, 33, 255, 0.4);
$elevenFestivalLabelColor: #b809d3;
$elevenFooterColor: #fcdeff;
$priceDescIconColor: #01b87a;
$dotBackgroundColor: rgba(154, 204, 249);
$priceDescColor: #0385f6;
$nationalChainTagColor: #5378a6;
$homeSloganTitColor: #474747;
$christmasColor: #e5470f;
$toastBgColor: rgba(0, 0, 0, 0.7);
$loadFailColor: #f5190a;
$areaMetro: #fd8f3a;
$areaRegion: #34c6b6;
$areaPlane: #3e89fa;
$areaTrain: #5f84fe;
$areaBus: #787ffe;
$shoppingPoint: #eb7449;
$redClose: #d01508;
$brown: #7b3e29;
$superLinearStart: #fff7f1;
$superLinearEnd: #ffefe5;
$superCouponLinearStart: #fbd9c3;
$superCouponLinearEnd: #f1c09f;
$superMissCouponLinearStart: #d2d2d2;
$superMissCouponLinearEnd: #d0d0d0;
$centerText: #2f69e2;
$couponItemBg: #f9f9f9;
$newCouponItemBg: #f5f5f5;
$couponRed: #f98078;
$couponLinearStart: #ff7b5b;
$couponLinearEnd: #f94e3a;
$cornerLinearStart: #ff8700;
$cornerLinearEnd: #ff9908;
$listCouponLinearStart: #ff714e;
$listCouponLinearEnd: #f53b2e;
$homeNewCouponLinearStart: rgba(255, 216, 213, 0.7);
$homeNewCouponLinearEnd: #ffafaf;
$homeHasPurchaseCouponLinearEnd: rgba(255, 198, 198);
$bookingCouponLabelBg: #ff6d4c;
$newCoupon: #ff4c1d;
$listCouponEntryBg: #fffaf9;
$noCarTipBackgroundColor: rgba(255, 255, 255, 0.5);
$noCarTipBorderColor: #fec899;
$noCarTipShadowColor: rgba(242, 112, 0, 0.2);
$privilegeBtnColor: #ab5647;
$privilegeBgColor: #3167b8;
$privilegeDescColor: #c58a7f;
$modifyOrderInsurancesColor: #01ae73;
$modifyOrderPayTimeColor: #f5180b;
$modifyOrderPayTimeShadowColor: rgba(0, 0, 0, 0.5);
$labelSoldBg: #fafafa;
$labelPostfixBg: #fef1e6;
$labelMarketBorder: rgba(254, 189, 147, 1);
$easyLifeBgColor: #113da3;
$nationalChainTagTextColor: #93300d;
$nationalChainTagBackgroundColor: #fcf2f1;
$carRentalCenterStoreAddrColor: #20324b;
$carRentalCenterRightArrowColor: #3b5272;
$carRentalCenterTagIconColor: #7292c0;
$carRentalCenterTagTextColor: #4a6c9b;
$carRentalCenterBottomLineColor: #61b7ff;
$refundTotalAmount: #ff6501;
$refundDot: #d8d8d8;
$refundDotLine: #ebebeb;
$refundSuccess: #01ae73;
$refundFaile: #f5180b;
$refundProcessed: #007af5;
$ItineraryCardTitle: #5578a8;
$ItineraryCardSelectedBorder: #73bcfa;
$cardShadowColor: #00203b;
$vocBorderColor: #ffb473;
$vocSelectedTextColor: #fe6600;
$signTimeLabelBg: rgba(0, 174, 115, 0.1);
$signBtnBg: #1677ff;
$signBtnTran: #034db5;
$cansignBtn: #e7f1ff;
$consultBgColor: rgba(255, 255, 255, 0.13);
$consultShadow: #0083ff;
$consultTip: rgba(255, 255, 255, 0.6);
$refundPenaltyHelp: #b4cbed;
$refundPenaltyRadioSelected: #1677ff;
$refundPenaltyError: #f51a0b;
$refundPenaltyIcon: #5c7080;
$refundPenaltyGreenDesc: #01ae73;
$refundPenaltyRedDesc: #f51a0b;
$refundPenaltyStepShadow: #8dc8ff;
$newBookingLinear1: #0080ff;
$newBookingLinear2: #dfe4ea;
$newBookingGrayBg: #edf2f8;
$couponOrangeText: #f46518;
$descItemDot: #ff7529;
$sectionTitleGradientBg: #72bcfa;
$toPayDot: #ff6501;
$instructionShadowColor: #0172d0;
$homeCouponBg1: rgba(255, 199, 194, 0.7);
$homeCouponBg2: rgba(255, 160, 160);
$homeCouponBgShadow: #ffdddd;
$reviewGrayBg: #f5f8fa;
$reviewBlueBg: #0086f8;
$reviewBlueBg2: #b4e6ff;
$reviewYellowBg: #fff2bf;
$newBookingTableLine: #ccd6e5;
$newBookingTableTitleBg: #f7fafd;
$depositPayed: #05b87a;
$depositUnPayed: #ff6501;
$depositBg: #f5f8fa;
$disableDepositBg: #effaff;
$itineraryCardRadioBoxBorder: #bbc9dc;
$itineraryCardRadioBoxTick: #dde4ed;
$itineraryCardRadioShadow: #cee8ff;
$itineraryCardGapLine: #979797;
$emptylinearGradientStart: rgba(255, 255, 255, 0.21);
$defaultLine: #e6e6e6;
$memberText: #fdebce;
$memberLabel: #784323;
$linearGradientMemberSuperStart: #8d472e;
$linearGradientMemberSuperEnd: #4e251c;
$memberBg: #f7f8f9;
$memberMenuShadow: rgba(0, 32, 59, 0.2);
$memberBorder: #f6f6f6;
$memberDetailTxt: #fae2b9;
$memberGoRent: #ffeed5;
$memberModalLineStart: #fff8ec;
$memberModalLineEnd: #cca76b;
$linearGradientBlackDiamondStart: #555555;
$linearGradientBlackDiamondEnd: #111111;
$blackDiamondTitleBg: #fff4e2;
$blackDiamondTipBg: #fff9ef;
$blackDiamondTipText: #b47b43;
$blackDiamondLine: #bbb;
$blackDimamndText: #feeccf;
$linearGradientButtonBlackDiamondStart: #3e3e3e;
$linearGradientButtonBlackDiamondEnd: #111111;
$linearGradientMenuBlackDiamondStart: #86898a;
$linearGradientMenuBlackDiamondEnd: #494949;
$blackDiamondNumber: #787878;
$blackDiamondBookingTxt: #ae8032;
$blackDiamondCouponBg: #fff3df;
$blackDiamondBookingTip: #f8e4c3;
$linearGradientGoldDiamondStart: #384a8c;
$linearGradientGoldDiamondEnd: #192039;
$goldDiamondTitleBg: #fff4e2;
$goldDiamondTipBg: #fff9ef;
$goldDiamondTipText: #b47b43;
$goldDiamondLine: #afb7d1;
$goldDiamondText: #feeccf;
$linearGradientButtonGoldDiamondStart: #4b5f9d;
$linearGradientButtonGoldDiamondEnd: #27396f;
$linearGradientMenuGoldDiamondStart: #6376b7;
$linearGradientMenuGoldDiamondEnd: #3a4c8b;
$goldDiamondNumber: #6275b6;
$goldDiamondBookingTxt: #ae8032;
$goldDiamondCouponBg: #fff3df;
$goldDiamondBookingTip: #f8e4c3;
$linearGradientDiamondStart: #47559f;
$linearGradientDiamondEnd: #383c5f;
$diamondTitleBg: #fff4e2;
$diamondTipBg: #fff9ef;
$diamondTipText: #b47b43;
$diamondLine: #b5bbd9;
$diamondText: #feeccf;
$linearGradientButtonDiamondStart: #626b9d;
$linearGradientButtonDiamondEnd: #465290;
$linearGradientMenuDiamondStart: #7c8adc;
$linearGradientMenuDiamondEnd: #49579e;
$diamondNumber: #7b89db;
$diamondBookingTxt: #595cb3;
$diamondCouponBg: #e3edfe;
$diamondBookingTip: #595cb3;
$linearGradientPlatinumStart: #7ea8b5;
$linearGradientPlatinumEnd: #36647f;
$platinumTitleBg: #d3f5ff;
$platinumTipBg: #e8f9fe;
$platinumTipText: #5e9bae;
$platinumLine: #cbdce1;
$platinumText: #fff;
$linearGradientButtonPlatinumStart: #6fabbf;
$linearGradientButtonPlatinumEnd: #6298aa;
$linearGradientMenuPlatinumStart: #8abccc;
$linearGradientMenuPlatinumEnd: #55859a;
$platinumNumber: #6293a7;
$platinumBookingTxt: #218db1;
$platinumCouponBg: #e3f6fc;
$platinumBookingTip: #218db1;
$linearGradientGoldStart: #ddb57e;
$linearGradientGoldEnd: #9d763f;
$goldTitleBg: #fff4e2;
$goldTipBg: #fff9ef;
$goldTipText: #b47b43;
$goldLine: #f1e1cb;
$goldText: #fff;
$linearGradientButtonGoldStart: #dbb981;
$linearGradientButtonGoldEnd: #d0a357;
$linearGradientMenuGoldStart: #e6c489;
$linearGradientMenuGoldEnd: #c8a16a;
$goldNumber: #debb81;
$goldBookingTxt: #ae8032;
$goldCouponBg: #fff3df;
$goldBookingTip: #ae8032;
$linearGradientSilverStart: #7aa2dd;
$linearGradientSilverEnd: #416fb2;
$silverTitleBg: #cee2ff;
$silverTipBg: #f0f6ff;
$silverTipText: #7aa2dd;
$silverLine: #cadaf1;
$silverText: #fff;
$linearGradientButtonSilverStart: #7aa2dd;
$linearGradientButtonSilverEnd: #6090d4;
$linearGradientMenuSilverStart: #91b7ee;
$linearGradientMenuSilverEnd: #4c7ec6;
$silverNumber: #78a0db;
$silverHomeBlockTitleColor: #4d86d8;
$linearGradientNormalStart: #00a7fa;
$linearGradientNormalEnd: #0076f5;
$normalTitleBg: #d7f1ff;
$normalTipBg: #eff9ff;
$normalTipText: #00a7fa;
$normalLine: #99dcfd;
$normalText: #fff;
$linearGradientButtonNormalStart: #46b3ff;
$linearGradientButtonNormalEnd: #0092f8;
$linearGradientMenuNormalStart: #0fbfff;
$linearGradientMenuNormalEnd: #008af5;
$normalNumber: #46b3ff;
$normalHomeBlockTitleColor: #2c9af7;
$toUseButton: #f8665b;
$newAddBg: #f53b2e;
$newAddShadow: #f20c00;
$damageFeeDetailHeaderBorder: #dce4ed;
$depositGreenDot: #05b87a;
$feeProgressGradient1: #55b7ff;
$feeProgressGradient2: #49a0ff;
$linearGradientCouponBookEnd: #f5180b;
$activityLinearBg1: #ff8678;
$activityLinearBg2: #ffefef;
$tableHeadBg: #fafcff;
$tableSelectedBg: #f2f9ff;
$selectedShadowBg: #4c97ff;
$serviceUpgradeBg: #f8fbff;
$serviceUpgradeBorder: #0286f6;
$serviceTipStart: #e9f1ff;
$serviceTipEnd: rgba(233, 241, 255, 0);
$orangeServiceTipStart: #fff1e6;
$orangeServiceTipEnd: rgba(244, 250, 255, 0);
$feeDetailCouponColor: #ff6501;
$optimizeStoreCloseColor: #93582f;
$optimizeStoreShadowColor: #5b5b5b;
$optimizeStoreSpliteColor: #eaeaea;
$optimizeStoreDescIconColor: #d06117;
$optimizeLabelBorderColor: #cc8d61;
$yunnanCouponColor: #ff502c;
$renewCardDayBg: #f6f9fd;
$cashBackColor: #ff480e;
$cashBackTitleColor: #912f00;
$getCouponToRent: #d7231d;
$getCouponPrice: #ff2323;
$cancelTipTitle: #284b7d;
$cancelTipBtnBorder: #d6d6d6;
$cashFailHelp: #f51909;
$cashSuccess: #01ae73;
$cancelSecondLevelReason: #0072d1;
$restAssuredTagColor: #4673be;
$easylifeTagTextColor: #006edc;
$easylifeTagBorderColor: #0176e6;
$marketLinearStartColor: #fff6f0;
$marketLineaEndColor: #ffefe5;
$marketLinearCouponStartColor: #fff3f0;
$marketLineaCouponEndColor: #ffeae5;
$marketCouponPriceColor: #ff3500;
$upgradeArrow: #9dadc7;
$depositLinearBlue: rgba(0, 134, 246, 0.8);
$optimizationStrengthenDot: #5c5c5c;
$optimizationStrengthenSubTitleText: #ca741c;
$optimizationStrengthenGradientStart: #fff0db;
$limitObjectTitle: #222630;
$limitSimmarize: #c9e0fe;
$limitMapBorder: #add9ff;
$recommendLabel: #0ba66e;
$recommendLabelDivider: rgba(0, 141, 155, 0.5);
$hotCityTag: #ffebdb;
$fuelTypeModalBg: #e3f2ff;
$modifyOrderExplain: #f2200c;
$modifyOrderExplainBg: #fff6f5;
$noResultBg: #f1f5f8;
$recommendVehicleTipStart: #edf7ff;
$recommendSearchTipText: #0f4999;
$recommendSearchTipBgStart: #ccecff;
$recommendSearchTipBgEnd: #ebf6ff;
$recommendProposeBg: #111111;
$virtualNumberIconGradient1: #3568ff;
$virtualNumberIconGradient2: #3b98fe;
$virtualNumberOptimizeGradient1: #ffc25f;
$virtualNumberOptimizeGradient2: #e98811;
$virtualNumberOptimizeGradient3: #ae6214;
$virutualNumberBgGradient1: #d5e8ff;
$virutualNumberBgGradient2: #f1f7ff;
$recommendBg: #ff5500;
$virtualNumberSplitLine: #888;
$virtualNumberTitleBg: rgba(201, 224, 254, 0.55);
$lessBg: #e6faf3;
$lessMain: #24b381;
$cancelPolicyExpire: #c5c5c5;
$venderListHeaderGradientBlue: rgba(239, 244, 248, 0);
$dateEditBg: #f0f0f0;
$nationalChainTagBorder: #9f5c5c;
$rentalDateDurationBg: #ebeff5;
$bookingOptimizationBlue: #006ff6;
$driverItemSelectedBg: #ebf3ff;
$addMoreDriver: #888888;
$insuranceTipBg: #f5f7fa;
$discountBorder: #f97b20;
$bookingOptimizationGrayBg: #ebeff5;
$payButtonGradientOrange: #ff9900;
$couponSplit: #ebeff5;
$disableCoupon: #c5c5c5;
$couponMergeBg: #f8fafb;
$orderCardUpgradeBg: #fff8ed;
$storeDetailNewBorder: #ebeff5;
$storeDetailBlue: #006ff6;
$storeDetailReviewBg: #f2f8fe;
$searchTip: #ff8b26;
$searchGapSplit: #dbdcde;
$ageModifyBlue: #006ff6;
$browsingBlue: #1856ae;
$browsingLinear: #e0f0fd;
$listSecretBoxBg: #fff4e1;
$depositFreeTableHeaderBg: #00d19f;
$depositFreeTableGradientBg: #e3f9ef;
$depositRadioChecked: #008fff;
$depositLinearGradientStart: #fcfdff;
$depositLinearGradientEnd: #d9eefe;
$depositFreeAuthGradientStart: #e5f7fe;
$depositFreeAuthGradientEnd: #e1f2ff;
$claimDashColor: #ced2d9;
$claimNumBgColor: #aabbd3;
$insuranceTipIconColor: #26c28d;
$insuranceTipLinearStart: #feb64e;
$insuranceTipLinearEnd: #ff5a06;
$orangeFF8800: #ff8800;
$homeBottomBg: #f2f6f9;
$specialRentalShadowBg: #3a4a99;
$specialRentalBorderBg: #eff2f5;
$travelLimitBorderColor: #e7e7e7;
$solidTextColor: #f03831;
$grayBorder2: #d4d4d4;
$loadingTextGray: #cbcbcb;
$osdItineraryCardBg: #f5f8fc;
$osdItineraryCardBoder: #eeeff3;
$osdItineraryGapLine: #d5d5d5;
$osdlinearGradientItinerary1: #e6effb;
$osdlinearGradientItinerary2: #e6ecf4;
$osdProductAdditionalTitle: #111111;
$osdProductAdditionalDescBg: #888888;
$C_F2F7FE: #f2f7fe;
$C_FFD7CB: #ffd7cb;
$C_FFEACC: #ffeacc;
$C_36190A: #36190a;
$C_646464: #646464;
$C_CBDFFC: #cbdffc;
$C_CACACA: #cacaca;
$C_00A66F: #00a66f;
$C_F54336: #f54336;
$C_FFE4CC: #ffe4cc;
$insuranceSellingText: #657c96;
$C_4D6F94: #4d6f94;
$C_14C800: #14c800;
$C_D0D8E6: #d0d8e6;
$C_00B988: #00b988;
$C_006FF6: #006ff6;
$C_DBE7F0: #dbe7f0;
$C_FE5500: #fe5500;
$C_001A65: #001a65;
$C_000A43: #000a43;
$C_0066F6: #0066f6;
$productRecommendTip: #385b8c;
$productRecommendTipBg: #c8e1ff;
$C_DFE9F4: #dfe9f4;
$C_E3EAF1: #e3eaf1;
$C_F1F5FC: #f1f5fc;
$C_FFF8F8: #fff8f8;
$C_F4A29D: #f4a29d;
$C_69C6A5: #69c6a5;
$C_EFF8F3: #eff8f3;
$C_EDF5FF: #edf5ff;
$C_0088F6: #0088f6;
$C_F1F3FA: #f1f3fa;
$C_E3E3E4: #e3e3e4;
$C_E8ECF1: #e8ecf1;
$C_E8E8E8: #e8e8e8;
$C_B8C6D9: #b8c6d9;
$C_08A66F: #08a66f;
$C_D9E0EE: #d9e0ee;
$C_DEDEDE: #dedede;
$C_FA6400: #fa6400;
$C_EFEFEF: #efefef;
$C_777777: #777777;
$C_E8F5FF: #e8f5ff;
$C_71ACFF: #71acff;
$C_FFEDE8: #ffede8;
$C_FFF8F5: #fff8f5;
$C_FFEFEA: #ffefea;
$C_DEE9FF: #dee9ff;
$C_DBF3FF: #dbf3ff;
$C_D4EEFF: #d4eeff;
$C_F9A278: #f9a278;
$C_619EFF: #619eff;
$C_83C0FF: #83c0ff;
$C_DBDBDB: #dbdbdb;
$C_4673BE: #4673be;
$C_4673B2: #4673b2;
$C_2375FF: #2375ff;
$C_00CB86: #00cb86;
$C_41A5F9: #41a5f9;
$deepBlueBase: #006ff6;
$C_E4F9F2: #e4f9f2;
$C_EEF4FF: #eef4ff;
$C_DCE1E8: #dce1e8;
$C_A8A8A8: #a8a8a8;
$C_C4DDFF: #c4ddff;
$C_F8FAFD: #f8fafd;
$C_00A4F8: #00a4f8;
$C_0079ED: #0079ed;
$C_009FFB: #009ffb;
$C_B8BDC8: #b8bdc8;
$C_8B91A0: #8b91a0;
$C_0073F5: #0073f5;
$C_0C5FE7: #0c5fe7;
$C_D3E7FF: #d3e7ff;
$C_066FD6: #066fd6;
$C_156BD5: #156bd5;
$C_49B9FF: #49b9ff;
$C_71BBF9: #71bbf9;
$C_34475E: #34475e;
$C_308EFF: #308eff;
$C_84BCFF: #84bcff;
$C_8DD9BE: #8dd9be;
$C_E3FFF5: #e3fff5;
$C_A6B7CE: #a6b7ce;
$R_6_111_214_0_6: rgba(6, 111, 214, 0.6);
$R_244_247_249_0_6: rgba(244, 247, 249, 0.6);
$R_76_81_100_0_0_5: rgba(76, 81, 100, 0.05);
$C_DCF0FF: #dcf0ff;
$C_EDF6FF: #edf6ff;
$C_E6F0FE: #e6f0fe;
$C_F6F8FB: #f6f8fb;
$C_D6E6FD: #d6e6fd;
$C_1658DC: #1658dc;
$C_F2FCF9: #f2fcf9;
$C_e7f8ff: #e7f8ff;
$C_89cafa: #89cafa;
$C_DBEAF6: #dbeaf6;
$C_FFC899: #ffc899;

// 无结果卡片背景色
$noResultContainerBg: #e8eeff;
$noResultBtn: #3264ff;
$noResultTipBg: #fff4eb;
$noResultTipText: $C_FF5500;
$noResultTipIcon: $C_ff6600;
$noResultTipBorder: $C_F1F5F8;
$noResultBtnBorder: #3a91fb;
$noResultRecommendTip: #3263a6;

$C_051A37: #051a37;
$C_D5E9FE: #d5e9fe;
$C_F2FAFD: #f2fafd;
$C_FFF0DC: #fff0dc;
$C_FFF6EA: #fff6ea;
$C_602A12: #602a12;
$C_909daf: #909daf;
$C_E7EAF2: #e7eaf2;
$C_DAE6f9: #dae6f9;
$C_C4D4E6: #c4d4e6;
$C_e7eaf4: #e7eaf4;
$C_DAE6f9: #dae6f9;
$C_C4D4E6: #c4d4e6;
$C_FAFCFE: #fafcfe;
$C_F4F7F9: #f4f7f9;
$C_0019C3: #0019c3;
$C_00A7FA: #00a7fa;
$C_843A00: #843a00;
$R_245_247_250_0: rgba(245, 247, 250, 0);
$C_FFE3B1: #FFE3B1;
$C_F7F8FA: #F7F8FA;
$C_E35600: #E35600;
$C_FFE3B1: #ffe3b1;
$C_3565B9: #3565b9;
$C_F3F5FA: #f3f5fa;
$C_894918: #894918;
$R_239_244_248_0_6: rgba(239, 244, 248, 0.6);
$C_FFE3B1: #ffe3b1;
$C_F7F8FA: #f7f8fa;
$C_F1F1F1: #f1f1f1;
$C_EBF5FF: #ebf5ff;
$R_151_151_151_0_0_7: rgba(151, 151, 151, 0.07);
$R_137_73_24_0_0_5: rgba(137, 73, 24, 0.05);
