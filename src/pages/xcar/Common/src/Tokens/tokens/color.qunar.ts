import BbkChannel from '../../Utils';

/**
 * http://cds.ued.ctripcorp.com/?cat=144
 */
const blue1 = '#007fe8';
const blue2 = '#0072d1';
const blue3 = '#00CAD8';
const blue4 = '#00CAD8';
const blue5 = '#4caaf8';
const blue6 = '#72bbf9';
const blue7 = '#99CEFB';
const blue8 = '#B4EDDA';
const blue9 = '#E5F9FB';
const blue10 = '#f3f8fe';
const blue11 = '#00CAD8';
const blue12 = '#00CAD8';
const bule13 = '#2698f7';
const blue14 = '#00CAD8';
const blue15 = '#0F9DA7';
const blue16 = '#00CAD8';
const blue17 = '#397FF6';
const blue18 = 'rgba(0, 160, 233, 0.5)';
const blue19 = '#00CAD8';
const blue20 = '#2F69E2';
const blue21 = '#113DA3';
const blue22 = '#007AF5';
const blue24 = '#1677ff';
const blue25 = '#0083FF';
const blue27 = '#fafcff';
const blue28 = '#f2f9ff';
const blue29 = '#4c97ff';
const blue30 = '#f4faff';
const blue31 = '#e9f1ff';
const blue32 = '#4673be';
const blue33 = '#006edc';
const blue34 = '#0176e6';
const blue35 = '#9DADC7';
const blue36 = '#B4CBED';
const blue37 = '#8dc8ff';
const blue38 = '#edf7ff';
const blue39 = '#0f4999';
const blue40 = '#CCECFF';
const blue41 = '#EBF6FF';
const blue42 = '#e3f2ff';
const blue43 = '#C9E0FE';
const blue44 = '#add9ff';
const blue45 = '#006ff6';
const blue46 = '#f2f8fe';

const green1 = '#009c66';
const green2 = '#E5F9FB';
const green3 = '#00b87a';
const green4 = '#24c28d';
const green5 = '#4bcea2';
const green6 = '#74d8b6';
const green7 = '#99e1c8';
const green8 = '#bfeddd';
const green9 = '#e6f8f1';
const green10 = '#f2fbf7';
const green11 = '#45A83A';
const green12 = '#19A094'; // Dark Green
const green13 = '#E5F9FB';
const green14 = '#F2FBF8';
const green15 = '#01AE73';
const green16 = '#01ae73';
const green17 = '#26C28D';

const orange1 = '#d96500';
const orange2 = '#f27000';
const orange3 = '#00CAD8';
const orange4 = '#ff8b26';
const orange5 = '#ffa04c';
const orange6 = '#ffb473';
const orange7 = '#fdc899';
const orange8 = '#ffdcbf';
const orange9 = '#fff1e5';
const orange10 = '#fff8f2';
const orange11 = '#ff6600';
const orange12 = '#462003'; // Reviews Satisfaction Text
const orange13 = '#FFF3CA'; // Reviews Satisfaction bg
const orange14 = '#FF5722';
const orange15 = '#FFFAF3';
const orange16 = '#FF6F00';
const orange17 = '#FF9500';
const orange18 = '#FFA04D';
const orange19 = '#FF9913';
const orange20 = '#FEF1E6';
const orange21 = '#FF6501';
const orange23 = '#93582f';
const orange24 = '#d06117';
const orange25 = '#cc8d61';
const orange26 = '#FFF6F0';
const orange27 = '#FFF3F0';
const orange28 = '#FFEAE5';
const orange29 = '#ff3500';

const red1 = '#d01307';
const red2 = '#e9170a';
const red3 = '#f5190a';
const red4 = '#f63a2d';
const red5 = '#f85d53';
const red6 = '#f97f78';
const red7 = '#fba49d';
const red8 = '#fcc5c2';
const red9 = '#fee8e5';
const red10 = '#fef2f1';
const red11 = '#F85E53';
const red12 = '#F5190A';
const red13 = '#E96457';
const red14 = '#EE3B28';
const red15 = '#FEF3F2';
const red16 = '#FF2F1E';
const red17 = '#FFEEE7';
const red18 = '#FF481D';
const red19 = '#FEE8E6';
const red20 = '#F5180B';
const red21 = '#F51A0B';
const red22 = '#F8665B';
const red23 = '#F51A0B';

const yellow1 = '#E1C285'; // Easylife bg
const yellow2 = '#C9963E'; // Easylife Text
const yellow3 = '#654C0A'; // Easylife Text dark
const yellow4 = '#9F7531'; // Easylife Tag
const yellow5 = '#FFFDF6'; // Easylife Tag Bg
const yellow6 = '#EAD6A1'; // Easylife Border Color
const yellow7 = '#FFFBE0';
const yellow8 = '#DE5B10';
const yellow9 = '#FEECCF';
const yellow10 = '#AE8032';
const yellow11 = '#FFF3DF';
const yellow12 = '#F8E4C3';

const gray1 = '#333333';
const gray2 = '#666';
const gray3 = '#999';
const gray4 = '#aaa';
const gray5 = '#bbb';
const gray6 = '#ccc';
const gray7 = '#ddd';
const gray8 = '#eee';
const gray9 = '#f4f4f4';
const gray10 = '#f8f8f8';
const gray11 = '#455873';
const gray12 = '#0F294D';
const gray13 = '#030303';
const gray14 = '#8592A6';
const gray15 = '#DADFE6';
const gray16 = '#e5f2fe';
const gray17 = '#222';
const gray18 = '#F5F5F5';
const gray19 = '#E0E0E0';
const gray20 = '#EFECEA';
const gray21 = '#EFEADA';
const gray22 = '#BBC9DC';
const gray23 = '#E5EAF2';
const gray24 = '#E3E3E3';
const gray25 = '#D4D4D4';
const gray26 = '#EFF1F6';
const gray27 = '#FAFAFA';
const gray28 = '#D8D8D8';
const gray29 = '#EBEBEB';
const gray30 = '#AABBD3';
const gray31 = '#5C7080';
const gray32 = '#E6E6E6';
const gray33 = '#F7F8F9';
const gray34 = '#F6F6F6';
const gray35 = '#eaeaea';
const gray36 = '#F1F5F8';
const gray37 = '#111111';
const gray38 = '#222630';
const gray39 = '#ebeff5';
const gray40 = '#888888';
const gray42 = '#ced2d9';

const blueGray1 = '#5578a7';
const blueGray2 = '#9CD4D8';
const blueGray3 = '#99aecb';
const blueGray4 = '#abbbd4';
const blueGray5 = '#bac9db';
const blueGray6 = '#cdd6e5';
const blueGray7 = '#dee4ee';
const blueGray8 = '#eef1f6';
const blueGray9 = '#f6f8fa';
const blueGray10 = '#f9fafe';
const blueGray11 = '#DCE0E5';
const blueGray12 = '#E5F9FB';
const blueGray13 = '#F2F8FE';
const blueGray14 = '#99aeca';
const blueGray15 = '#f5f6fa';
const blueLight16 = '#E9F2FF';

const blurGray11 = '#EEEEEE';

export const white = '#fff';
export const litleWhite = '#F9DF02';
export const textWhite = '#fff';
export const black = '#000';
export const transparent = 'transparent';
export const transparentBase = transparent;
export const blackTransparent = 'rgba(0, 0, 0, 0.7)';
export const modalShadow = 'rgba(0, 0, 0, 0.7)';
export const cardShadow = 'rgba(0, 0, 0, 0.14)';
export const orderCardStatusBlueShadow = '#007FE9';

// 品牌色、标准色
export const blueBase = blue3;
export const greenBase = green3;
export const orangeBase = orange3;
export const redBase = red3;
export const grayBase = gray3;
export const blueGrayBase = gray1;
export const lightRedBase = red18;
// 单色图标
export const blueIcon = blue4;
export const greenIcon = green4;
export const orangeIcon = orange4;
export const redIcon = red4;
export const blueGrayIcon = gray3;
// 边框色
export const blueBorder = blue8;
export const greenBorder = green8;
export const orangeBorder = orange8;
export const redBorder = red8;
export const grayBorder = gray8;
export const grayDescLine = gray5;
export const darkGrayBorder = gray6;
export const lightRedBorder = red16;
// 浅色衬底
export const blueBg = blue10;
export const blueBgSecondary = blue9;
export const greenBg = green10;
export const orangeBg = orange10;
export const orangeBgLight = orange10;
export const orangeBgLight2 = orange13;
export const redBg = red10;
export const grayBg = gray9;
export const grayBgSecondary = gray10;
export const blueGrayBg = blueGray8;
export const tableBg = blueGray13;
export const grayPlaceholder = blurGray11;
export const tableGrayBg = blueGray15;
export const blackBase = gray1;
export const ipxGragBg = gray9;
export const lightRedBg = red17;
export const redLightBg = red19;

// 点击色
export const blueClick = blue1;

// 价格
export const orangePrice = orange11;
export const orangePrice2 = orange17;
export const priceColor = orange11;

// 字体
export const fontPrimary = gray1;
export const fontSecondary = gray2;
export const fontSubDark = gray3;
export const fontSubLight = gray7;
export const fontGrayBlue = gray4;
export const fontOrangeDark = orange12;
export const fontBlueDark = blueGray14;
export const fontGrayDark = gray17;

// 渐变
export const linearGradientBlueLight = '#00CAD8';
export const linearGradientBlueDark = '#00CAD8';
export const linearGradientOrangeLight = '#00CAD8';
export const linearGradientOrangeDark = orange3;
export const linearGradientOrangeOrderLight = '#ff9d0a';
export const linearGradientOrangeOrderDark = orange3;
export const linearGradientLightBlue = '#00CAD8';
export const linearGradientDarkBlue = '#00CAD8';
export const linearGradientBlueBgLight = '#EDFAFF';
export const linearGradientBlueBgDark = '#E7F3FF';
export const linearGradientXyzBgBlue = '#DFF3FF';
export const linearGradientOrderCardBlueLight = '#00A7FA';
export const linearGradientOrderCardBlueDark = '#218CFF';
export const linearGradientOrderCardOrangeLight = '#FF8739';
export const linearGradientOrderCardOrangeDark = '#FF664B';

export const linearGradientCancelTipDark = '#EAF5FF';
export const linearGradientCancelTipLight = '#F9FCFF';

export const linearGradientRecommendLabelStop1 = 'rgba(11, 166, 110, 0.08)';
export const linearGradientRecommendLabelStop2 = 'rgba(11, 166, 110, 0.04)';

export const linearGradientBlueBottom = '#71BDFF';

export const linearGradientBluePercentBg = '#8ACAFF';

// 保险绿
export const greenInsurance = green11;
export const osdGreenInsurance = green3;

export const radioBorderColor = blueGray11;
export const radioDisableBackGroundColor = blueGray6;

// horizontal selected of blue color
export const horizontalSelectedColor = blue11;
export const horizontalColor = gray2;

// pickdrop of guide for map component
export const mapTabSelectdColor = blue12;
export const mapTabColor = gray2;
export const mapLocalColor = gray2;
export const btnBorder = blue14;
export const guideStepSequenceColor = gray6;
export const hasEndColor = gray3;
export const expreienceBtnBorderColor = blue12;
export const expreienceBtnTextColor = blue12;
export const btnDarkBlueBorder = blue19;

// 表格
export const tableBorderColor = blueGray7;
export const tableBackgroundColor = blueGray12;
export const qunarTableBackgroundColor = '#F8FAFD';

// 无忧租
export const easylifeBg = yellow1;
export const easylifeText = yellow2;
export const easylifeTextDark = yellow3;
export const easylifeTag = yellow4;
export const easylifeTagBg = yellow5;
export const easylifeShadowColor = 'rgba(234,212,155,0.80)';
export const easylifeBorderColor = yellow6;
export const easylifeText2 = '#8E5E11';
export const easylifeBg2 = '#FFF8E9';
export const easylifeBg3 = '#FFFCF8';
export const easylifeBorderColor2 = 'rgba(159,117,49,0.30)';
export const easylifeGray1 = '#DDE4ED';
export const easylifeGray2 = '#F5F5F9';
export const easylifeBlue = blue45;
export const easylifeTextBlack = gray37;
export const essylifeTextGray = gray40;

// 芝麻免押 blue14
export const sesameBase = green12;
export const sesamePrimary = blue14;
export const sesameFontPrimary = gray17;
export const sesameSuccess = '#09BB07';
export const sesameFail = '#F76260';
export const sesameLabel = '#00CAD8';
export const sesameLabelBg = 'rgba(46, 182, 170, 0.1)';
export const sesameClose = '#BABABA';

// 猜你喜欢
export const likeGradient1 = '#FF6B61';
export const likeGradient2 = '#F64D41';

export const labelGreenBg = green9;
export const labelGreenText = green3;
export const labelOrangeBg = '#FFF1E6';
export const labelOrangeText = orange3;
export const labelBlueBg = blue9;
export const labelBlueText = blue3;
export const bookbarTipsGreenBg = green9;
export const bookbarTipsGreenText = green3;
export const bookbarTipsOrangeBg = '#FFFAF2';
export const deleteBg = red4;
export const certificateTypeInvaildTip = '#F2C100';
export const certificateTypeInvaildColor = gray6;
export const certificateActive = '#F2F8FE';
export const certificateActiveBorder = blue16;
// 点评
export const blueReview = blue15;

// 订单详情页
export const blueNps = blue17;
export const blueLightNps = blue9;
export const blueVehicleHdBg = blueLight16;
export const orangePayCountDown = orange14;
export const orangeTipBg = orange15;
export const labelBlueBorder = blue16;
export const selfHelpBg = blueGray9;
export const extrasColor = gray12;
export const extrasTextColor = gray14;
export const dayText = gray15;
export const vehicleSimilar = gray16;
export const verifyTitle = orange11;
export const replenishSubTitle = yellow7;
export const replenishSubTitleColor = yellow8;
export const iconCountDownFilled = orange18;
export const insLableBg = gray8;
export const insStatusRed = red13;
export const bbkNpsBgColor = white;
export const bbkNpsTitleColor = fontPrimary;
export const bbkNpsWillStatusColor = blueGrayBase;
export const bbkNpsPointNumColor = mapTabSelectdColor;
export const bbkNpsPointNumBgColor = blueBgSecondary;
export const bbkNpsSelectColor = blueBase;
export const bbkNpsInputBg = blueGray9;
export const successGreen = green3;
export const blueShadow = blue7;
export const diffBg = red15;
export const underlineColor = gray24;
export const tipModalBlackTransparent = 'rgba(0, 0, 0, 0.65)';
export const hasPayStatusColor = '#35BC74';
export const freeDepositLabelColor = '#E5F8F1';
export const lightPinkBorder = '#F0F2F5';

// 优惠
export const discountRed = red11;

// 支付方式label
export const fontBlackDark = gray11;
export const fontBlackLight = gray12;
export const orangePayLabel = orange16;

// dialog
export const dialogTitle = gray13;

// driver
export const driverError = red12;
export const driverRadioBorder = gray4;
export const dirverRadioVaildBorder = gray8;
export const driverCertificateTip = orange19;

// 沪牌
export const licenseLabel = '#366AB3';
export const foreignLicenseLabel = 'rgba(34,34,34,0.7)';
// 新版沪牌
export const licenseLabelNew = 'rgba(3,133,246,0.1)';
export const foreignLicenseLabelNew = 'rgba(51,51,51,0.06)';
// 芝麻label border
export const zhimaBorder = blue18;

// 城市区域
export const defaultLabel = blue15;
export const secLabel = blueGray2;

// 平铺列表
export const lineColor = blurGray11;

export const cityLabel = 'rgb(46, 49, 65)';
export const cityLabelText = '#FFEAC1';
export const cityLabelText2 = '#FFEBC9';

// 分割线
export const splitGray = gray6;

// Bookbar
export const shadowGray = 'rgb(174,191,212)';
export const bookbarBgColor = white;
// filterbutton
export const filterButtonColor = gray10;
export const tipsRed = '#F63B2E';

// AddedService
export const addedServiceOrangeTag = orange11;
export const addedServiceRedTag = red14;

export const shadowColor = 'rgba(15,41,77,0.08)';
// switch
export const switchBgInactive = gray6;

// warning
export const warning = orange19;

// marketfooter
export const marketGray = '#BFCDE0';

export const soldOutLabelBgColor = gray20;
export const soldOutEasyLifeLabelBgColor = gray21;

export const numberBackgroundColor = gray22;
export const numberLineBackgroundColor = gray23;

// 端午节颜色
export const duanwuColor = '#02A971';

// 88节颜色
export const festivalColor = '#b7e4ff';
export const festivalSelectedColor = '#ffffdd';

// 国庆节颜色
export const nationalDayColor = white;
export const nationalDaySelectedColor = '#c16ff8';

// 保险标签背景色
export const insuranceLabelBg = green13;

// 变价弹层
export const priceFallBg = green14;
export const priceDiffTableBorder = gray26;

// 信用租
export const zhimaLabelBg = '#E5F9FB';
export const preAuthLabelBg = '#1CC677';

// 双11节颜色
export const elevenFestivalLabelBorderColor = 'rgba(225,33,255,0.40)';
export const elevenFestivalLabelColor = '#B809D3';
export const elevenFooterColor = '#FCDEFF';

// 列表页总价说明
export const priceDescIconColor = blue3;
export const dotBackgroundColor = 'rgba(0,202,216,0.30)';
export const priceDescColor = blue3;

// 一嗨全国连锁标签
export const nationalChainTagColor = '#5378A6';

// 新版首页底部slogan的title颜色
export const homeSloganTitColor = '#474747';

// 双旦主题字体颜色
export const christmasColor = '#E5470F';

// 自定义toast背景色
export const toastBgColor = 'rgba(0,0,0,0.60)';

// 增量加载失败按钮颜色
export const loadFailColor = '#F5190A';

// 区域页分类图标色值
export const areaMetro = '#fd8f3a';
export const areaRegion = '#34C6B6';
export const areaPlane = '#3E89FA';
export const areaTrain = '#5F84FE';
export const areaBus = '#787FFE';
export const shoppingPoint = '#eb7449';

export const redClose = '#D01508';

export const brown = '#7B3E29';
export const superLinearStart = '#FFF7F1';
export const superLinearEnd = '#FFEFE5';
export const superCouponLinearStart = '#FBD9C3';
export const superCouponLinearEnd = '#F1C09F';
export const superMissCouponLinearStart = '#D2D2D2';
export const superMissCouponLinearEnd = '#D0D0D0';

export const orderStatusColor = '#ff7700';

// 租车中心文案颜色
export const centerText = blue20;

// 优惠券
export const couponItemBg = '#F9F9F9';
export const newCouponItemBg = gray18;
export const couponRed = '#F98078';
export const couponLinearStart = '#FF7B5B';
export const couponLinearEnd = '#F94E3A';
export const cornerLinearStart = '#FF8700';
export const cornerLinearEnd = '#FF9908';
export const listCouponLinearStart = '#FF714E';
export const listCouponLinearEnd = '#F53B2E';
export const homeNewCouponLinearStart = 'rgba(255,216,213,0.70)';
export const homeNewCouponLinearEnd = '#FFAFAF';
export const bookingCouponLabelBg = '#FF6D4C';
export const newCoupon = '#FF4C1D';
export const listCouponEntryBg = '#FFFAF9';

// 取消订单到店无车颜色
export const noCarTipBackgroundColor = 'rgba(255,255,255,0.50)';
export const noCarTipBorderColor = '#FEC899';
export const noCarTipShadowColor = 'rgba(242,112,0,0.20)';

// 特权弹窗
export const privilegeBtnColor = '#AB5647';
export const privilegeBgColor = '#3167B8';
export const privilegeDescColor = '#C58A7F';
// 修改订单补款确认页-保险加购状态颜色
export const modifyOrderInsurancesColor = '#01AE73';
export const modifyOrderPayTimeColor = '#F5180B';
export const modifyOrderPayTimeShadowColor = 'rgba(0,0,0,0.05)';
// 标签样式
export const labelSoldBg = gray27;
export const labelPostfixBg = orange20;
export const labelMarketBorder = 'rgba(254,189,147, 1)';
export const easyLifeBgColor = blue21;
// 全国连锁
export const nationalChainTagTextColor = '#93300D';
export const nationalChainTagBackgroundColor = '#FCF2F1';
// 租车中心
export const carRentalCenterStoreAddrColor = '#20324B';
export const carRentalCenterRightArrowColor = '#3B5272';
export const carRentalCenterTagIconColor = '#7292C0';
export const carRentalCenterTagTextColor = '#4A6C9B';
export const carRentalCenterBottomLineColor = '#61B7FF';
// 退款进度
export const refundTotalAmount = orange21;
export const refundDot = gray28;
export const refundDotLine = gray29;
export const refundSuccess = green15;
export const refundFaile = red20;
export const refundProcessed = blue22;
// 我的凭证 绿色标签
export const signTimeLabelBg = 'rgba(0,174,115,0.1)';
export const signBtnBg = '#1677ff';
export const signBtnTran = '#034db5';
// 取消订单
export const refundPenaltyHelp = blue36;
export const refundPenaltyRadioSelected = blue24;
export const refundPenaltyError = red21;
export const refundPenaltyIcon = gray31;
export const refundPenaltyGreenDesc = green16;
export const refundPenaltyRedDesc = red23;
export const refundPenaltyStepShadow = blue37;
// 新填写页
export const newBookingLinear1 = '#0080FF';
export const newBookingLinear2 = '#DFE4EA';
export const newBookingGrayBg = '#EDF2F8';
export const couponOrangeText = '#F46518';
export const reviewGrayBg = '#F5F8FA';
export const reviewBlueBg = '#0086F8';
export const reviewBlueBg2 = '#B4E6FF';
export const reviewYellowBg = '#FFF2BF';
export const newBookingTableLine = '#CCD6E5';
export const newBookingTableTitleBg = '#F7FAFD';
export const cardShadowColor = '#00203B';
// 催事件进度
export const consultBgColor = 'rgba(255,255,255, 0.13)';
export const consultShadow = blue25;
export const consultTip = 'rgba(255,255,255, 0.6)';
// 订详押金
export const depositPayed = '#05B87A';
export const depositUnPayed = '#FF6501';
export const depositBg = '#F5F8FA';
export const disableDepositBg = '#EFFAFF';
// 订详整体优化
export const descItemDot = '#FF7529';
export const sectionTitleGradientBg = '#72BCFA';
export const toPayDot = '#FF6501';
export const instructionShadowColor = '#0172D0';
// 权益页面渐变色
export const emptylinearGradientStart = 'rgba(255,255,255, 0.21)';
export const defaultLine = gray32;
export const memberText = '#FDEBCE';
export const memberLabel = '#784323';
export const linearGradientMemberSuperStart = '#8D472E';
export const linearGradientMemberSuperEnd = '#4E251C';
export const memberBg = gray33;
export const memberMenuShadow = 'rgba(0, 32, 59, 0.02)';
export const memberBorder = gray34;
export const memberDetailTxt = '#fae2b9';
export const memberGoRent = '#ffeed5';
export const memberModalLineStart = '#FFF8EC';
export const memberModalLineEnd = '#CCA76B';
// 黑钻
export const linearGradientBlackDiamondStart = '#555555';
export const linearGradientBlackDiamondEnd = black;
export const blackDiamondTitleBg = '#FFF4E2';
export const blackDiamondTipBg = '#FFF9EF';
export const blackDiamondTipText = '#B47B43';
export const blackDiamondLine = grayDescLine;
export const blackDimamndText = yellow9;
export const linearGradientButtonBlackDiamondStart = '#3E3E3E';
export const linearGradientButtonBlackDiamondEnd = black;
export const linearGradientMenuBlackDiamondStart = '#86898A';
export const linearGradientMenuBlackDiamondEnd = '#494949';
export const blackDiamondNumber = '#787878';
export const blackDiamondBookingTxt = yellow10;
export const blackDiamondCouponBg = yellow11;
export const blackDiamondBookingTip = yellow12;
// 金钻
export const linearGradientGoldDiamondStart = '#384A8C';
export const linearGradientGoldDiamondEnd = '#192039';
export const goldDiamondTitleBg = '#FFF4E2';
export const goldDiamondTipBg = '#FFF9EF';
export const goldDiamondTipText = '#B47B43';
export const goldDiamondLine = '#AFB7D1';
export const goldDiamondText = yellow9;
export const linearGradientButtonGoldDiamondStart = '#4B5F9D';
export const linearGradientButtonGoldDiamondEnd = '#27396F';
export const linearGradientMenuGoldDiamondStart = '#6376B7';
export const linearGradientMenuGoldDiamondEnd = '#3A4C8B';
export const goldDiamondNumber = '#6275b6';
export const goldDiamondBookingTxt = yellow10;
export const goldDiamondCouponBg = yellow11;
export const goldDiamondBookingTip = yellow12;
// 钻石
export const linearGradientDiamondStart = '#47559F';
export const linearGradientDiamondEnd = '#383C5F';
export const diamondTitleBg = '#FFF4E2';
export const diamondTipBg = '#FFF9EF';
export const diamondTipText = '#B47B43';
export const diamondLine = '#B5BBD9';
export const diamondText = yellow9;
export const linearGradientButtonDiamondStart = '#626B9D';
export const linearGradientButtonDiamondEnd = '#465290';
export const linearGradientMenuDiamondStart = '#7C8ADC';
export const linearGradientMenuDiamondEnd = '#49579E';
export const diamondNumber = '#7b89db';
export const diamondBookingTxt = '#595cb3';
export const diamondCouponBg = '#e3edfe';
export const diamondBookingTip = diamondBookingTxt;
// 铂金
export const linearGradientPlatinumStart = '#7EA8B5';
export const linearGradientPlatinumEnd = '#36647F';
export const platinumTitleBg = '#D3F5FF';
export const platinumTipBg = '#E8F9FE';
export const platinumTipText = '#5E9BAE';
export const platinumLine = '#CBDCE1';
export const platinumText = white;
export const linearGradientButtonPlatinumStart = '#6FABBF';
export const linearGradientButtonPlatinumEnd = '#6298AA';
export const linearGradientMenuPlatinumStart = '#8ABCCC';
export const linearGradientMenuPlatinumEnd = '#55859A';
export const platinumNumber = '#6293a7';
export const platinumBookingTxt = '#218db1';
export const platinumCouponBg = '#e3f6fc';
export const platinumBookingTip = platinumBookingTxt;
// 黄金
export const linearGradientGoldStart = '#DDB57E';
export const linearGradientGoldEnd = '#9D763F';
export const goldTitleBg = '#FFF4E2';
export const goldTipBg = '#FFF9EF';
export const goldTipText = '#B47B43';
export const goldLine = '#F1E1CB';
export const goldText = white;
export const linearGradientButtonGoldStart = '#DBB981';
export const linearGradientButtonGoldEnd = '#D0A357';
export const linearGradientMenuGoldStart = '#E6C489';
export const linearGradientMenuGoldEnd = '#C8A16A';
export const goldNumber = '#debb81';
export const goldBookingTxt = yellow10;
export const goldCouponBg = yellow11;
export const goldBookingTip = goldBookingTxt;
// 白银
export const linearGradientSilverStart = '#7AA2DD';
export const linearGradientSilverEnd = '#416FB2';
export const silverTitleBg = '#CEE2FF';
export const silverTipBg = '#F0F6FF';
export const silverTipText = '#7AA2DD';
export const silverLine = '#CADAF1';
export const silverText = white;
export const linearGradientButtonSilverStart = linearGradientSilverStart;
export const linearGradientButtonSilverEnd = '#6090D4';
export const linearGradientMenuSilverStart = '#91B7EE';
export const linearGradientMenuSilverEnd = '#4C7EC6';
export const silverNumber = '#78a0db';
export const silverHomeBlockTitleColor = '#4d86d8';
// 普通
export const linearGradientNormalStart = linearGradientOrderCardBlueLight;
export const linearGradientNormalEnd = '#0076F5';
export const normalTitleBg = '#D7F1FF';
export const normalTipBg = '#EFF9FF';
export const normalTipText = '#00A7FA';
export const normalLine = '#99DCFD';
export const normalText = white;
export const linearGradientButtonNormalStart = '#46B3FF';
export const linearGradientButtonNormalEnd = '#0092F8';
export const linearGradientMenuNormalStart = '#0FBFFF';
export const linearGradientMenuNormalEnd = '#008AF5';
export const normalNumber = linearGradientButtonNormalStart;
export const normalHomeBlockTitleColor = '#2c9af7';
// 生日福利页面
export const toUseButton = red22;
// 车损
export const newAddBg = '#F53B2E';
export const newAddShadow = '#F20C00';
export const damageFeeDetailHeaderBorder = '#dce4ed';
export const depositGreenDot = '#05B87A';
export const feeProgressGradient1 = '#55B7FF';
export const feeProgressGradient2 = '#49A0FF';
// 领取订按钮
export const linearGradientCouponBookEnd = red20;

export const activityLinearBg1 = '#FF8678';
export const activityLinearBg2 = '#FFEFEF';

// 车型重构
export const tableHeadBg = blue27;
export const tableSelectedBg = blue28;
export const selectedShadowBg = blue29;
export const serviceUpgradeBg = '#F8FBFF';
export const serviceUpgradeBorder = '#0286f6';
export const serviceTipStart = blue31;
export const serviceTipEnd = 'rgba(233, 241, 255, 0)';
export const orangeServiceTipStart = labelOrangeBg;
export const orangeServiceTipEnd = 'rgba(244, 250, 255, 0)';
// 费用明细优惠卷颜色
export const feeDetailCouponColor = orange21;
// 优选好店弹窗按钮颜色
export const optimizeStoreCloseColor = orange23;
export const optimizeStoreShadowColor = '#5B5B5B';
export const optimizeStoreSpliteColor = gray35;
export const optimizeStoreDescIconColor = orange24;
export const optimizeLabelBorderColor = orange25;
// 云南云旅价格颜色
export const yunnanCouponColor = '#FF502C';
export const cashBackColor = '#FF480E';
export const cashBackTitleColor = '#912F00';

export const getCouponToRent = '#d7231d';
export const getCouponPrice = '#FF2323';
export const cancelTipTitle = '#284B7D';
export const cancelTipBtnBorder = '#d6d6d6';
export const cashFailHelp = '#F51909';
export const cashSuccess = '#ffae73';
export const cancelSecondLevelReason = blue2;
// 标签升级
export const restAssuredTagColor = blue32;
export const easylifeTagTextColor = blue33;
export const easylifeTagBorderColor = blue34;
export const marketLinearStartColor = orange26;
export const marketLineaEndColor = superLinearEnd;
export const marketLinearCouponStartColor = orange27;
export const marketLineaCouponEndColor = orange28;
export const marketCouponPriceColor = orange29;
export const upgradeArrow = blue35;

export const depositLinearBlue = 'rgba(0, 134, 246, 0.08)';

// 优选强化
export const optimizationStrengthenDot = '#5c5c5c';
export const optimizationStrengthenSubTitleText = '#ca741c';
export const optimizationStrengthenGradientStart = '#FFF0DB';
// 新版限行提示
export const limitObjectTitle = gray38;
export const limitSimmarize = blue43;
export const limitMapBorder = blue44;

// 车型推荐标签
export const recommendLabel = '#0ba66e';
export const recommendLabelDivider = 'rgba(0,141,155,0.5)';

export const hotCityTag = '#FFEBDB';
// 能源说明弹窗
export const fuelTypeModalBg = blue42;

// 修改订单修改说明提醒
export const modifyOrderExplain = '#F2200C';
export const modifyOrderExplainBg = '#fff6f5';

// 无少结果推荐
export const noResultBg = gray36;
export const recommendVehicleTipStart = blue38;
export const recommendSearchTipText = blue39;
export const recommendSearchTipBgStart = blue40;
export const recommendSearchTipBgEnd = blue41;
export const recommendProposeBg = gray37;

// 固话小号
export const virtualNumberIconGradient1 = '#3568FF';
export const virtualNumberIconGradient2 = '#3B98FE';

export const virtualNumberOptimizeGradient1 = '#FFC25F';
export const virtualNumberOptimizeGradient2 = '#E98811';
export const virtualNumberOptimizeGradient3 = '#AE6214';

export const virutualNumberBgGradient1 = '#D5E8FF';
export const virutualNumberBgGradient2 = '#F1F7FF';

export const recommendBg = '#FF5500';

export const virtualNumberSplitLine = '#888';
export const virtualNumberTitleBg = 'rgba(201,224,254,0.55)';

// LESS可持续旅行项目
export const lessBg = '#E6FAF3';
export const lessMain = '#24B381';

export const venderListHeaderGradientBlue = 'rgba(239, 244, 248,0)';
export const dateEditBg = '#F0F0F0';
// 填写页优化
export const nationalChainTagBorder = '#9f5c5c';
export const rentalDateDurationBg = '#ebeff5';
export const bookingOptimizationBlue = '#006ff6';
export const driverItemSelectedBg = '#ebf3ff';
export const addMoreDriver = '#888888';
export const insuranceTipBg = '#F5F7FA';
export const discountBorder = '#f97b20';
export const bookingOptimizationGrayBg = '#EBEFF5';
export const payButtonGradientOrange = '#FF9900';
// 填写页优惠押金合并
export const couponSplit = '#EBEFF5';
export const disableCoupon = '#C5C5C5';
export const couponMergeBg = '#F8FAFB';

// 订单卡片
export const orderCardUpgradeBg = '#fff8ed';
export const storeDetailNewBorder = gray39;
export const storeDetailBlue = blue45;
export const storeDetailReviewBg = blue46;

// 出境免押
export const depositFreeTableHeaderBg = '#00D19F';
export const depositFreeTableGradientBg = '#E3F9EF';
export const depositRadioChecked = '#008FFF';
export const depositLinearGradientStart = '#FCFDFF';
export const depositLinearGradientEnd = '#D9EEFE';
export const depositFreeAuthGradientStart = '#E5F7FE';
export const depositFreeAuthGradientEnd = '#E1F2FF';
// 新版海外保险
export const claimDashColor = gray42;
export const claimNumBgColor = gray30;
export const insuranceTipIconColor = green17;

// 旅行限制
export const travelLimitBorderColor = '#e7e7e7';
export const insuranceSellingText = '#657c96';
