import setOpacity from './src/setOpacity';
import button from './tokens/button';
import * as color from './tokens/color.ctrip';
import * as font from './tokens/font.ctrip';
import * as fontCommon from './tokens/font.common';
import * as label from './tokens/label';
import * as radius from './tokens/radius';
import * as space from './tokens/space';
import * as border from './tokens/border';
import * as icon from './tokens/icon.ctrip';
import * as tokenType from './tokens/tokenType';
import * as druation from './tokens/druation';
import * as layout from './tokens/layout';
import * as zIndex from './tokens/zIndex';
import * as styleSheet from './tokens/styleSheet.ctrip';

/**
 * 均以750设计稿为准
 */
export {
  radius,
  icon,
  font,
  fontCommon,
  layout,
  setOpacity,
  space,
  border,
  button,
  label,
  druation,
  tokenType,
  color,
  zIndex,
  styleSheet,
};
