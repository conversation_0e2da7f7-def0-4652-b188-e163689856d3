export interface ExtMapType {
  key?: string;
}

export interface BaseResponseType {
  isSuccess?: boolean;
  code?: string;
  returnMsg?: string;
  requestId?: string;
  cost?: number;
  showMessage?: string;
  extMap?: ExtMapType;
  apiResCodes?: Array<string>;
  hasResult?: boolean;
}

export interface ErrorFieldsType {
  FieldName?: string;
  ErrorCode?: string;
  Message?: string;
}

export interface ErrorsType {
  Message?: string;
  ErrorCode?: string;
  StackTrace?: string;
  SeverityCode?: any;
  ErrorFields?: Array<ErrorFieldsType>;
  ErrorClassification?: any;
}

export interface ExtensionType {
  Id?: string;
  Version?: string;
  ContentType?: string;
  Value?: string;
}

export interface ResponseStatusType {
  Timestamp?: Date;
  Ack?: string;
  Errors?: Array<ErrorsType>;
  Build?: string;
  Version?: string;
  Extension?: Array<ExtensionType>;
}

export interface RequestInfoType {
  pickupDate?: Date;
  pickupLocationName?: string;
  returnDate?: Date;
  returnLocationName?: string;
  rentalDay?: number;
  sourceCountryId?: number;
  age?: number;
  language?: string;
  currencyCode?: string;
}

export interface PromotionType {
  type?: number;
  title?: string;
  description?: string;
  longTag?: string;
  longDesc?: string;
  couponDesc?: string;
  deductionPercent?: number;
  deductionAmount?: number;
  dayDeductionAmount?: number;
  payofftype?: number;
  payoffName?: string;
  code?: string;
  isFromCtrip?: boolean;
  islimitedTimeOfferType?: boolean;
  sortNum?: number;
  strategySource?: string;
  earningsCost?: string;
  businessCost?: string;
  resourceCost?: string;
  configVersion?: string;
  isEnabled?: boolean;
  actionedDate?: string;
  expiredDate?: string;
  extDesc?: string;
  selected?: boolean;
  startAmount?: number;
  isOverlay?: boolean;
  promotionId?: number;
  overlayDesc?: string;
  couponName?: string;
  unitName?: string;
  popAmountTile?: string;
  deductionType?: number;
}

export interface FilterItemsType {
  itemCode?: string;
  name?: string;
  code?: string;
  groupCode?: string;
  bitwiseType?: number;
  binaryDigit?: number;
  sortNum?: number;
  isQuickItem?: boolean;
  quickSortNum?: number;
  icon?: string;
  promotion?: PromotionType;
}

export interface FilterGroupsType {
  name?: string;
  sortNum?: number;
  groupCode?: string;
  bitwiseType?: number;
  filterItems?: Array<FilterItemsType>;
  shortName?: string;
  icon?: string;
}

export interface FilterMenuItemsType {
  name?: string;
  code?: string;
  sortNum?: number;
  hierarchy?: number;
  filterGroups?: Array<FilterGroupsType>;
}

export interface FilterItemExtsType {
  name?: string;
  groupCode?: string;
  itemCodes?: Array<string>;
  sortNum?: number;
}

export interface SimilarVehicleInfosType {
  vehicleCode?: string;
  vehicleName?: string;
  vehicleImageUrl?: string;
}

export interface VendorSimilarVehicleInfosType {
  bizVendorCode?: string;
  vendorName?: string;
  vendorLogo?: string;
  similarVehicleInfos?: Array<SimilarVehicleInfosType>;
}

export interface VehicleListType {
  brandId?: number;
  brandEName?: string;
  brandName?: string;
  name?: string;
  zhName?: string;
  vehicleCode?: string;
  imageUrl?: string;
  groupCode?: string;
  groupSubClassCode?: string;
  groupName?: string;
  transmissionType?: number;
  transmissionName?: string;
  passengerNo?: number;
  doorNo?: number;
  luggageNo?: number;
  displacement?: string;
  struct?: string;
  fuel?: string;
  gearbox?: string;
  driveMode?: string;
  driveType?: string;
  style?: string;
  imageList?: Array<string>;
  userRealImageList?: Array<string>;
  storeRealImageList?: Array<string>;
  similarImageList?: Array<string>;
  specializedImages?: Array<string>;
  isSpecialized?: boolean;
  isHot?: boolean;
  recommendDesc?: string;
  hasConditioner?: boolean;
  conditionerDesc?: string;
  spaceDesc?: string;
  similarCommentDesc?: string;
  vendorSimilarVehicleInfos?: Array<VendorSimilarVehicleInfosType>;
  vehicleAccessoryImages?: Array<string>;
  license?: string;
  realityImageUrl?: string;
  vedio?: string;
}

export interface CommentInfoType {
  level?: string;
  vendorDesc?: string;
  commentCount?: number;
  qCommentCount?: number;
  qExposed?: string;
  overallRating?: number;
  maximumRating?: number;
}

export interface PriceInfoType {
  currentDailyPrice?: number;
  currentOriginalDailyPrice?: number;
  currentTotalPrice?: number;
  currentCurrencyCode?: string;
  localCurrencyCode?: string;
  marginPrice?: number;
  naked?: boolean;
  priceVersion?: string;
  priceType?: number;
}

export interface VcExtendRequestType {
  responsePickUpLocationId?: string;
  responseReturnLocationId?: string;
  vendorVehicleId?: string;
}

export interface ReferenceType {
  bizVendorCode?: string;
  vendorCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
  vehicleCode?: string;
  packageId?: number;
  packageType?: number;
  vcExtendRequest?: VcExtendRequestType;
  decoratorVendorType?: number;
  easyLifeUpgradePackageId?: number;
  isEasyLife?: boolean;
  withPrice?: boolean;
  packageId4CutPrice?: number;
  payMode?: number;
  bomCode?: string;
  productCode?: string;
  rateCode?: string;
  subType?: number;
  comPriceCode?: string;
  priceVersion?: string;
  priceVersionOfLowestPrice?: string;
  pCityId?: number;
  rCityId?: number;
  vendorVehicleCode?: string;
  age?: number;
  alipay?: boolean;
  aType?: number;
  hotType?: number;
  priceType?: number;
  vehicleDegree?: string;
  idType?: number;
  fType?: number;
  unionCardFilter?: FilterItemsType;
  noDepositFilter?: FilterItemsType;
  pickUpOnDoor?: boolean;
  dropOffOnDoor?: boolean;
  klbVersion?: number;
  priceTip?: string;
}

export interface SubListType {
  title?: string;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: number;
}

export interface SubListType2 {
  title?: string;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: number;
  subList?: Array<SubListType>;
}

export interface VendorTagType {
  title?: string;
  type?: number;
  code?: string;
  titleExtra?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  subList?: Array<SubListType2>;
  category?: number;
  colorCode?: string;
  prefix?: string;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
  labelCode?: string;
}

export interface CommentInfoType2 {
  overallRating?: number;
  commentCount?: number;
  level?: string;
  newVendorDescDescription?: string;
}

export interface EasyLifeVsNormalInfoListType {
  title?: string;
  easyLifeDesc?: string;
  normalDesc?: string;
}

export interface EasyLifeInsuranceIncludesType {
  itemName?: string;
  coverage?: string;
}

export interface ItemListType {
  sortNum?: number;
  name?: string;
}

export interface ItemListType2 {
  sortNum?: number;
  name?: string;
  itemList?: Array<ItemListType>;
}

export interface ExtDescriptionListType {
  sortNum?: number;
  name?: string;
  itemList?: Array<ItemListType2>;
}

export interface EasyLifeInsuranceDetailType {
  bizVendorCode?: string;
  easyLifeInsuranceDesc?: Array<string>;
  easyLifeInsuranceAdditionalDesc?: Array<string>;
  easyLifeInsuranceUncludes?: Array<string>;
  easyLifeInsuranceIncludes?: Array<EasyLifeInsuranceIncludesType>;
  vendorName?: string;
  extDescriptionList?: Array<ExtDescriptionListType>;
  clauseUrl?: string;
}

export interface EasyLifeCtripInsuranceConfigType {
  withCtripInsurance?: boolean;
  productIds?: Array<number>;
}

export interface EasyLifeInfoType {
  isEasyLife?: boolean;
  pickupTips?: string;
  returnTips?: string;
  pickupLongTips?: string;
  returnLongTips?: string;
  tagList?: Array<VendorTagType>;
  commentInfo?: CommentInfoType2;
  easyLifeVsNormalInfoList?: Array<EasyLifeVsNormalInfoListType>;
  easyLifeInsuranceDetail?: EasyLifeInsuranceDetailType;
  countryId?: number;
  cityList?: Array<number>;
  bizVendorCode?: string;
  freePreAuthorization?: boolean;
  originFreePreAuth?: boolean;
  needWechat?: boolean;
  easyLifeCtripInsuranceConfig?: EasyLifeCtripInsuranceConfigType;
  onewayfee?: number;
  freePreAuthDesc?: string;
  guidImages?: string;
}

export interface FilterAggregationsType {
  name?: string;
  groupCode?: string;
  binaryDigit?: number;
  checkType?: number;
}

export interface AdjustPriceInfoType {
  bAdjustDailyPrice?: number;
  bAdjustTotalPrice?: number;
  adjustStrategy?: string;
  cashbackStrategy?: string;
}

export interface VendorPriceListType {
  vendorName?: string;
  vendorLogo?: string;
  commentInfo?: CommentInfoType;
  priceInfo?: PriceInfoType;
  reference?: ReferenceType;
  promotions?: Array<PromotionType>;
  isSpecialized?: boolean;
  sortNum?: number;
  newVendorDesc?: string;
  vendorTag?: VendorTagType;
  pStoreRouteDesc?: string;
  rStoreRouteDesc?: string;
  easyLifeInfo?: EasyLifeInfoType;
  showIZuCheLogo?: boolean;
  isBroker?: boolean;
  platformName?: string;
  platformCode?: string;
  allTags?: Array<VendorTagType>;
  evaluation?: VendorTagType;
  filterAggregations?: Array<FilterAggregationsType>;
  extTitle?: string;
  reactId?: string;
  isPStoreSupportCdl?: boolean;
  isRStoreSupportCdl?: boolean;
  privilegesPromotions?: Array<PromotionType>;
  qualityScore?: number;
  sortScore?: number;
  storeScore?: number;
  isSelect?: boolean;
  orignalPriceStyle?: any;
  stock?: number;
  stockDesc?: string;
  distance?: number;
  rDistance?: number;
  adverts?: number;
  extMap?: ExtMapType;
  payModes?: Array<number>;
  freeDeposit?: number;
  urge?: string;
  actId?: string;
  couId?: string;
  joinerType?: string;
  joinerStoreId?: string;
  adjustPriceInfo?: AdjustPriceInfoType;
  pickUpFee?: number;
  pickOffFee?: number;
  showVendor?: boolean;
  isOrderVehicle?: boolean;
  cyVendorName?: string;
  card?: number;
  ctripVehicleCode?: string;
}

export interface VehicleRecommendProductType {
  introduce?: string;
  productCodes?: Array<string>;
}

export interface ProductListType {
  vehicleCode?: string;
  sortNum?: number;
  filterCodeStr?: string;
  lowestPrice?: number;
  highestPrice?: number;
  minTPrice?: number;
  minDPrice?: number;
  maximumRating?: number;
  maximumCommentCount?: number;
  lowestDistance?: number;
  isSpecialized?: boolean;
  vendorPriceList?: Array<VendorPriceListType>;
  reactId?: string;
  group?: number;
  isPush?: number;
  scoreSort?: number;
  hotSort?: number;
  hot?: number;
  hotType?: number;
  hotStyle?: string;
  hotScore?: number;
  vendorSimilarVehicleInfos?: Array<VendorSimilarVehicleInfosType>;
  vehicleRecommendProduct?: VehicleRecommendProductType;
  isEasy?: boolean;
  isOptim?: boolean;
  isSelect?: boolean;
  carAge?: string;
  isCredit?: boolean;
  priceSize?: number;
  pTag?: VendorTagType;
  outTags?: Array<VendorTagType>;
  rCoup?: number;
  productRef?: {
    license: string;
    licenseStyle: string;
    licenseTag?: string;
  };
  productTopInfo?: any;
}

export interface ProductGroupsType {
  groupCode?: string;
  groupName?: string;
  sortNum?: number;
  productList?: Array<ProductListType>;
  groupImg?: string;
  allowMerge?: boolean;
  dailyPrice?: number;
}

export interface WorkTimeType {
  openTimeDesc?: string;
  openTime?: string;
  closeTime?: string;
  description?: string;
}

export interface StoreServiceListType {
  title?: string;
  description?: string;
  typeCode?: string;
}

export interface ScoreInfoType {
  store?: number;
  car?: number;
  suport?: number;
  clean?: number;
  exposed?: number;
}

export interface StoreListType {
  storeCode?: string;
  bizVendorCode?: string;
  telephone?: string;
  storeName?: string;
  address?: string;
  longitude?: number;
  latitude?: number;
  mapUrl?: string;
  storeGuild?: string;
  storeLocation?: string;
  storeWay?: string;
  workTime?: WorkTimeType;
  storeServiceList?: Array<StoreServiceListType>;
  countryId?: number;
  countryName?: string;
  provinceId?: number;
  provinceName?: string;
  cityId?: number;
  cityName?: string;
  isAirportStore?: boolean;
  distance?: string;
  productCount?: number;
  lowestPrice?: number;
  commentInfo?: CommentInfoType;
  type?: number;
  isrentcent?: number;
  pickUpLevel?: number;
  pickOffLevel?: number;
  sendTypeForPickUpCar?: number;
  sendTypeForPickOffCar?: number;
  psend?: number;
  rsend?: number;
  scoreInfo?: ScoreInfoType;
}

export interface VendorListType {
  bizVendorCode?: string;
  vendorName?: string;
  vendorImageUrl?: string;
  vendorCode?: string;
  isBroker?: boolean;
  platformCode?: string;
  platformName?: string;
  haveCoupon?: boolean;
  vendorTag?: VendorTagType;
}

export interface AllResponseMapType {
  key?: boolean;
}

export interface RecommendInfoType {
  promptTitle?: string;
  promptSubTitle?: string;
  buttonTitle?: string;
  errorCode?: string;
  type?: number;
  recTime?: number;
  recDistance?: number;
  pTime?: string;
  rTime?: string;
  recMsg?: string;
  recommendProducts?: Array<ProductListType>;
}

export interface StringObjsType {
  content?: string;
  style?: string;
  url?: string;
}

export interface ContentsType {
  contentStyle?: string;
  stringObjs?: Array<StringObjsType>;
}

export interface LocationsType {
  groupCode?: string;
  index?: number;
}

export interface ButtonType {
  title?: string;
  type?: number;
  icon?: string;
}

export interface CommNoticesType {
  title?: string;
  subTitle?: string;
  contents?: Array<ContentsType>;
  type?: number;
  icon?: string;
  note?: string;
  locations?: Array<LocationsType>;
  button?: ButtonType;
  filterItem?: FilterItemsType;
}

export interface LicenseInfoType {
  pickupLicenseDesc?: string;
  noticeMsg?: string;
  pickupSupportCDLType?: number;
  pickupCountryName?: string;
  returnCountryName?: string;
  returnSupportCDLType?: number;
  returnLicenseDesc?: string;
  tipsGroups?: Array<VendorTagType>;
  hasFlight?: boolean;
}

export interface CampaignVendorsType {
  vendorCode?: string;
  vendorName?: string;
}

export interface CampaignInfoType {
  hotCity?: boolean;
  vendorDeduction?: number;
  campaignVendors?: Array<CampaignVendorsType>;
  promotionToAll?: boolean;
  promotionDeduction?: number;
  promotionVendors?: Array<CampaignVendorsType>;
  overallDeduction?: number;
  maxDeductionProducts?: Array<string>;
  deductionProducts?: Array<string>;
}

export interface RentCenterType {
  id?: number;
  name?: string;
  icon?: string;
  backImage?: string;
  images?: Array<string>;
  index?: number;
}

export interface FavoriteInfoType {
  locations?: Array<LocationsType>;
  title?: string;
  desc?: string;
  filterItems?: Array<FilterItemsType>;
}

export interface EasyLifeInfoType2 {
  isEasyLife?: boolean;
  tagList?: Array<VendorTagType>;
}

export interface BasicDataType {
  sortItems?: Array<VendorTagType>;
  priceItems?: Array<VendorTagType>;
  indexItems?: Array<VendorTagType>;
  trunkInfo?: StringObjsType;
}

export interface CasesType {
  vehicleGroupCode?: string;
  vehicleGroupName?: string;
  representativeVehicleName?: string;
  vehicleGroupItems?: string;
}

export interface SimilarVehicleIntroduceType {
  introduce?: VendorTagType;
  carProtection?: VendorTagType;
  cases?: Array<CasesType>;
}

export interface ListDtoType {
  baseResponse?: BaseResponseType;
  ResponseStatus?: ResponseStatusType;
  currencyDecimal?: number;
  requestInfo?: RequestInfoType;
  allVehicleCount?: number;
  allVendorPriceCount?: number;
  filterMenuItems?: Array<FilterMenuItemsType>;
  quickFilter?: Array<FilterItemsType>;
  filterItemExts?: Array<FilterItemExtsType>;
  vehicleList?: Array<VehicleListType>;
  productGroups?: Array<ProductGroupsType>;
  productGroupsHashCode?: string;
  storeList?: Array<StoreListType>;
  vendorList?: Array<VendorListType>;
  allResponseMap?: AllResponseMapType;
  needRetry?: boolean;
  freePreAuthCode?: string;
  supportUnionLogoCode?: string;
  recommendInfo?: RecommendInfoType;
  commNotices?: Array<CommNoticesType>;
  licenseInfo?: LicenseInfoType;
  campaignInfo?: CampaignInfoType;
  rentCenter?: RentCenterType;
  promptInfos?: Array<CommNoticesType>;
  favoriteInfo?: FavoriteInfoType;
  easyLifeInfo?: EasyLifeInfoType2;
  basicData?: BasicDataType;
  similarVehicleIntroduce?: SimilarVehicleIntroduceType;
  designatedVehicleIntroduce?: VendorTagType;
}
