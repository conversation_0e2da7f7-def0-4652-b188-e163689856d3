import { get as lodashGet, groupBy as lodashGroupBy } from 'lodash-es';

import memoize from 'memoize-one';
import { BbkUtils } from '../../../Utils';
import {
  ProductGroupsType,
  FilterMenuItemsType,
  ProductListType,
} from './Types/ListDtoType';
import { FilterType } from './Types/ListType';
import { Filter, getPriceCounts, getSortValue } from './Filter';
import { SortPrice } from './Sort';
import { isFilterParametersEqual, isParametersEqual } from './IsEqual';

const getAllowedSummaryVehicles = (productGroups: ProductGroupsType[]) =>
  productGroups.filter(f => f.allowMerge);

const getSummaryVehicles = (
  productGroups: ProductGroupsType[],
): ProductListType[] => {
  let productList: ProductListType[] = [];
  for (let i = 0; i < productGroups.length; i++) {
    productList = productList.concat(productGroups[i].productList);
  }
  return productList;
};

const getSummaryGroup = (
  summaryVehicles: ProductGroupsType[],
  group: ProductGroupsType,
): ProductGroupsType => ({
  groupCode: group.groupCode,
  groupName: group.groupName,
  dailyPrice: group.dailyPrice,
  productList: summaryVehicles,
});

const combineProductGroups = (
  productGroups: ProductGroupsType[],
  summaryGroup?: ProductGroupsType,
) => {
  const waitMergeVehicles = getAllowedSummaryVehicles(productGroups);
  const summaryVehicles = getSummaryVehicles(waitMergeVehicles);
  const group = getSummaryGroup(summaryVehicles, summaryGroup);
  return [group, ...productGroups];
};

// const memoFilter = memoize(Filter, isFilterParametersEqual);

const cloneProductGroups = productGroups => {
  if (!productGroups) {
    return [];
  }
  const temp = [];
  productGroups.forEach(element => {
    const newProductList = [];
    if (lodashGet(element, 'productList.length') > 0) {
      element.productList.forEach(productItem => {
        if (productItem?.vendorPriceList?.length > 0) {
          newProductList.push({
            ...productItem,
            vendorPriceList: [...productItem.vendorPriceList],
          });
        } else {
          newProductList.push({ ...productItem });
        }
      });
    }
    temp.push({ ...element, productList: newProductList });
  });
  return temp;
};

const getProductGroups = (
  productGroups: ProductGroupsType[],
  productGroupsHashCode?: string,
  summaryGroup?: ProductGroupsType,
  filterItems?: FilterMenuItemsType[],
  activeFilters?: FilterType,
) => {
  let products: ProductGroupsType[] = cloneProductGroups(productGroups);
  const filters = activeFilters || {};

  // time monitor
  // @ts-ignore
  // console.time('bbk-logic-getProductGroups');

  // filter bits and price
  if (
    (filters.bitsFilter && filters.bitsFilter.length > 0) ||
    (filters.priceFilter && filters.priceFilter.length > 0) ||
    (filters.commentFilter && filters.commentFilter.length > 0)
  ) {
    products = Filter(
      products,
      filterItems,
      activeFilters,
      productGroupsHashCode,
    );
    // @ts-ignore
    // console.timeLog('bbk-logic-getProductGroups');
  }

  // add summary group
  if (products.length > 0 && summaryGroup) {
    products = combineProductGroups(products, summaryGroup);
    // @ts-ignore
    // console.timeLog('bbk-logic-getProductGroups');
  }

  // sort
  if (products.length > 0 && filters.sortFilter) {
    products = SortPrice(products, activeFilters, summaryGroup);
    // @ts-ignore
    // console.timeLog('bbk-logic-getProductGroups');
  }
  // @ts-ignore
  // console.timeEnd('bbk-logic-getProductGroups');

  return products;
};

const getMinDailyPrice = productList => {
  const copyProductList = BbkUtils.cloneDeep(productList);
  let minDailyPrice = 0;
  const vehiclePriceList = [];
  copyProductList.forEach(pItem => {
    const vendorPriceList = lodashGet(pItem, 'vendorPriceList');
    if (vehiclePriceList && vendorPriceList.length > 0) {
      vendorPriceList.sort(
        (a, b) =>
          lodashGet(a, 'priceInfo.currentDailyPrice') -
          lodashGet(b, 'priceInfo.currentDailyPrice'),
      );
      vehiclePriceList.push(
        lodashGet(vendorPriceList[0], 'priceInfo.currentDailyPrice'),
      );
    }
  });
  if (vehiclePriceList.length > 0) {
    vehiclePriceList.sort((a, b) => a - b);
    minDailyPrice = vehiclePriceList[0];
  }
  return minDailyPrice;
};

const getVehiclePriceGroup = products => {
  return products.map(prod => {
    let isPriceGroup = false;
    let filterDailyPrice;
    prod.productList.map((m, index) => {
      m.logicIndex = index;
      m.isGroup = m.group > 0; // 确保只有两个值 true false
      filterDailyPrice = getSortValue(filterDailyPrice, m.lowestPrice, true);
      if (!isPriceGroup && m.isGroup) isPriceGroup = true;
      return m;
    });

    prod.filterDailyPrice = filterDailyPrice;

    if (!isPriceGroup) return prod;

    const group = lodashGroupBy(prod.productList, 'isGroup');
    const vehiclePriceList = group.false || [];

    const vehicles = lodashGroupBy(group.true, 'group');
    Object.keys(vehicles).map(key => {
      // 备份排序的logicIndex
      const originLogicIndex = vehicles[key][0].logicIndex;

      // 找到报价最低价的位置，强制放在第一位
      const copyList = BbkUtils.cloneDeep(vehicles[key]);
      copyList.sort((a, b) => {
        if (a.minTPrice >= 0 && b.minTPrice >= 0) {
          return a.minTPrice - b.minTPrice;
        }
        return a.lowestPrice - b.lowestPrice;
      });
      const lowerLogicIndex = copyList[0].logicIndex;
      const lowerIndex = vehicles[key].findIndex(
        f => f.logicIndex === lowerLogicIndex,
      );
      if (lowerIndex > 0) {
        vehicles[key].splice(lowerIndex, 1);
        vehicles[key].unshift(copyList[0]);
      }

      const item = BbkUtils.cloneDeep(vehicles[key][0]);
      item.logicIndex = originLogicIndex;
      item.priceGroup = vehicles[key];
      item.priceGroup.map((priceItem, index) => {
        priceItem.logicIndex = index;
        return priceItem;
      });
      vehiclePriceList.push(item);
    });
    vehiclePriceList.sort((a, b) => a.logicIndex - b.logicIndex);
    prod.productList = vehiclePriceList.map((item, index) => {
      item.logicIndex = index;
      return item;
    });
    return prod;
  });
};

const getProductsAndCount = (
  productGroups: ProductGroupsType[],
  productGroupsHashCode?: string,
  summaryGroup?: ProductGroupsType,
  filterItems?: FilterMenuItemsType[],
  activeFilters?: FilterType,
  type?: string,
) => {
  const products = getProductGroups(
    productGroups,
    productGroupsHashCode,
    summaryGroup,
    filterItems,
    activeFilters,
  );
  const counts = getPriceCounts(products, summaryGroup);
  const priceGroup = getVehiclePriceGroup(products);
  return {
    productGroups: priceGroup,
    ...counts,
  };
};

const memoGetProductsAndCount = memoize(getProductsAndCount, isParametersEqual);

const getProductGroupsAndCount = (
  productGroups: ProductGroupsType[],
  productGroupsHashCode?: string,
  summaryGroup?: ProductGroupsType,
  filterItems?: FilterMenuItemsType[],
  activeFilters?: FilterType,
  type?: string,
) => {
  const newSummaryGroup = BbkUtils.cloneDeep(summaryGroup);
  const newActiveFilters = BbkUtils.cloneDeep(activeFilters);
  return memoGetProductsAndCount(
    productGroups,
    productGroupsHashCode,
    newSummaryGroup,
    filterItems,
    newActiveFilters,
    type,
  );
};

export {
  combineProductGroups,
  getSummaryVehicles,
  getSummaryGroup,
  getAllowedSummaryVehicles,
  // main filter funciton
  getProductGroupsAndCount,
};

export default getProductGroups;
