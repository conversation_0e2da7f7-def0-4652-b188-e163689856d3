/**
 * Copyright (c) 2015, Facebook, Inc.  All rights reserved.
 *
 * Facebook, Inc. ("Facebook") owns all right, title and interest, including
 * all intellectual property and other proprietary rights, in and to the React
 * Native CustomComponents software (the "Software").  Subject to your
 * compliance with these terms, you are hereby granted a non-exclusive,
 * worldwide, royalty-free copyright license to (1) use and copy the Software;
 * and (2) reproduce and distribute the Software as part of your own software
 * ("Your Software").  Facebook reserves all rights not expressly granted to
 * you in this license agreement.
 *
 * THE SOFTWARE AND DOCUMENTATION, IF ANY, ARE PROVIDED "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES (INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE) ARE DISCLAIMED.
 * IN NO EVENT SHALL FACEBOOK OR ITS AFFILIATES, OFFICERS, DIRECTORS OR
 * EMPLOYEES BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THE SOFTWARE, EVEN IF
 * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/**
 * Cannot "use strict" because we must use eval in this file.
 */
/* eslint-disable global-strict */

const keyOf = require('fbjs/lib/keyOf');

const X_DIM = keyOf({ x: null });
const Y_DIM = keyOf({ y: null });
const Z_DIM = keyOf({ z: null });
const W_DIM = keyOf({ w: null });

const TRANSFORM_ROTATE_NAME = keyOf({ transformRotateRadians: null });

const ShouldAllocateReusableOperationVars = {
  transformRotateRadians: true,
  transformScale: true,
  transformTranslate: true,
};

const InitialOperationField = {
  transformRotateRadians: [0, 0, 0, 1],
  transformTranslate: [0, 0, 0],
  transformScale: [1, 1, 1],
};

/**
 * Creates a highly specialized animation function that may be evaluated every
 * frame. For example:
 *
 *    var ToTheLeft = {
 *      opacity: {
 *        from: 1,
 *        to: 0.7,
 *        min: 0,
 *        max: 1,
 *        type: 'linear',
 *        extrapolate: false,
 *        round: 100,
 *      },
 *      left: {
 *        from: 0,
 *        to: -SCREEN_WIDTH * 0.3,
 *        min: 0,
 *        max: 1,
 *        type: 'linear',
 *        extrapolate: true,
 *        round: PixelRatio.get(),
 *      },
 *    };
 *
 *    var toTheLeft = buildStyleInterpolator(ToTheLeft);
 *
 *  Would returns a specialized function of the form:
 *
 *    function(result, value) {
 *      var didChange = false;
 *      var nextScalarVal;
 *      var ratio;
 *      ratio = (value - 0) / 1;
 *      ratio = ratio > 1 ? 1 : (ratio < 0 ? 0 : ratio);
 *      nextScalarVal = Math.round(100 * (1 * (1 - ratio) + 0.7 * ratio)) / 100;
 *      if (!didChange) {
 *        var prevVal = result.opacity;
 *        result.opacity = nextScalarVal;
 *        didChange = didChange  || (nextScalarVal !== prevVal);
 *      } else {
 *        result.opacity = nextScalarVal;
 *      }
 *      ratio = (value - 0) / 1;
 *      nextScalarVal = Math.round(2 * (0 * (1 - ratio) + -30 * ratio)) / 2;
 *      if (!didChange) {
 *        var prevVal = result.left;
 *        result.left = nextScalarVal;
 *        didChange = didChange  || (nextScalarVal !== prevVal);
 *      } else {
 *        result.left = nextScalarVal;
 *      }
 *      return didChange;
 *    }
 */

const ARGUMENT_NAMES_RE = /([^\s,]+)/g;
/**
 * This is obviously a huge hack. Proper tooling would allow actual inlining.
 * This only works in a few limited cases (where there is no function return
 * value, and the function operates mutatively on parameters).
 *
 * Example:
 *
 *
 *   var inlineMe(a, b) {
 *      a = b + b;
 *  };
 *
 *  inline(inlineMe, ['hi', 'bye']);  // "hi = bye + bye;"
 *
 * @param {string} fnStr Source of any simple function who's arguments can be
 * replaced via a regex.
 * @param {array<string>} replaceWithArgs Corresponding names of variables
 * within an environment, to replace `func` args with.
 * @return {string} Resulting function body string.
 */
const inline = function (fnStr, replaceWithArgs) {
  const parameterNames =
    fnStr
      .slice(fnStr.indexOf('(') + 1, fnStr.indexOf(')'))
      .match(ARGUMENT_NAMES_RE) || [];
  const replaceRegexStr = parameterNames
    .map(function (paramName) {
      return `\\b${paramName}\\b`;
    })
    .join('|');
  const replaceRegex = new RegExp(replaceRegexStr, 'g');
  const fnBody = fnStr.substring(
    fnStr.indexOf('{') + 1,
    fnStr.lastIndexOf('}'),
  );
  const newFnBody = fnBody.replace(replaceRegex, function (parameterName) {
    const indexInParameterNames = parameterNames.indexOf(parameterName);
    const replacementName = replaceWithArgs[indexInParameterNames];
    return replacementName;
  });
  return newFnBody.split('\n');
};

/**
 * Simply a convenient way to inline functions using the inline function.
 */
const MatrixOps = {
  unroll: `function(matVar, m0, m1, m2, m3, m4, m5, m6, m7, m8, m9, m10, m11, m12, m13, m14, m15) {
    m0 = matVar[0];
    m1 = matVar[1];
    m2 = matVar[2];
    m3 = matVar[3];
    m4 = matVar[4];
    m5 = matVar[5];
    m6 = matVar[6];
    m7 = matVar[7];
    m8 = matVar[8];
    m9 = matVar[9];
    m10 = matVar[10];
    m11 = matVar[11];
    m12 = matVar[12];
    m13 = matVar[13];
    m14 = matVar[14];
    m15 = matVar[15];
  }`,

  matrixDiffers: `function(retVar, matVar, m0, m1, m2, m3, m4, m5, m6, m7, m8, m9, m10, m11, m12, m13, m14, m15) {
    retVar = retVar ||
    m0 !== matVar[0] ||
    m1 !== matVar[1] ||
    m2 !== matVar[2] ||
    m3 !== matVar[3] ||
    m4 !== matVar[4] ||
    m5 !== matVar[5] ||
    m6 !== matVar[6] ||
    m7 !== matVar[7] ||
    m8 !== matVar[8] ||
    m9 !== matVar[9] ||
    m10 !== matVar[10] ||
    m11 !== matVar[11] ||
    m12 !== matVar[12] ||
    m13 !== matVar[13] ||
    m14 !== matVar[14] ||
    m15 !== matVar[15];
  }`,

  transformScale: `function(matVar, opVar) {
    // Scaling matVar by opVar
    var x = opVar[0];
    var y = opVar[1];
    var z = opVar[2];
    matVar[0] = matVar[0] * x;
    matVar[1] = matVar[1] * x;
    matVar[2] = matVar[2] * x;
    matVar[3] = matVar[3] * x;
    matVar[4] = matVar[4] * y;
    matVar[5] = matVar[5] * y;
    matVar[6] = matVar[6] * y;
    matVar[7] = matVar[7] * y;
    matVar[8] = matVar[8] * z;
    matVar[9] = matVar[9] * z;
    matVar[10] = matVar[10] * z;
    matVar[11] = matVar[11] * z;
    matVar[12] = matVar[12];
    matVar[13] = matVar[13];
    matVar[14] = matVar[14];
    matVar[15] = matVar[15];
  }`,

  /**
   * All of these matrix transforms are not general purpose utilities, and are
   * only suitable for being inlined for the use of building up interpolators.
   */
  transformTranslate: `function(matVar, opVar) {
    // Translating matVar by opVar
    var x = opVar[0];
    var y = opVar[1];
    var z = opVar[2];
    matVar[12] = matVar[0] * x + matVar[4] * y + matVar[8] * z + matVar[12];
    matVar[13] = matVar[1] * x + matVar[5] * y + matVar[9] * z + matVar[13];
    matVar[14] = matVar[2] * x + matVar[6] * y + matVar[10] * z + matVar[14];
    matVar[15] = matVar[3] * x + matVar[7] * y + matVar[11] * z + matVar[15];
  }`,

  /**
   * @param {array} matVar Both the input, and the output matrix.
   * @param {quaternion specification} q Four element array describing rotation.
   */
  transformRotateRadians: `function(matVar, q) {
    // Rotating matVar by q
    var xQuat = q[0], yQuat = q[1], zQuat = q[2], wQuat = q[3];
    var x2Quat = xQuat + xQuat;
    var y2Quat = yQuat + yQuat;
    var z2Quat = zQuat + zQuat;
    var xxQuat = xQuat * x2Quat;
    var xyQuat = xQuat * y2Quat;
    var xzQuat = xQuat * z2Quat;
    var yyQuat = yQuat * y2Quat;
    var yzQuat = yQuat * z2Quat;
    var zzQuat = zQuat * z2Quat;
    var wxQuat = wQuat * x2Quat;
    var wyQuat = wQuat * y2Quat;
    var wzQuat = wQuat * z2Quat;
    // Step 1: Inlines the construction of a quaternion matrix ('quatMat')
    var quatMat0 = 1 - (yyQuat + zzQuat);
    var quatMat1 = xyQuat + wzQuat;
    var quatMat2 = xzQuat - wyQuat;
    var quatMat4 = xyQuat - wzQuat;
    var quatMat5 = 1 - (xxQuat + zzQuat);
    var quatMat6 = yzQuat + wxQuat;
    var quatMat8 = xzQuat + wyQuat;
    var quatMat9 = yzQuat - wxQuat;
    var quatMat10 = 1 - (xxQuat + yyQuat);
    // quatMat3/7/11/12/13/14 = 0, quatMat15 = 1

    // Step 2: Inlines multiplication, takes advantage of constant quatMat cells
    var a00 = matVar[0];
    var a01 = matVar[1];
    var a02 = matVar[2];
    var a03 = matVar[3];
    var a10 = matVar[4];
    var a11 = matVar[5];
    var a12 = matVar[6];
    var a13 = matVar[7];
    var a20 = matVar[8];
    var a21 = matVar[9];
    var a22 = matVar[10];
    var a23 = matVar[11];

    var b0  = quatMat0, b1 = quatMat1, b2 = quatMat2;
    matVar[0] = b0 * a00 + b1 * a10 + b2 * a20;
    matVar[1] = b0 * a01 + b1 * a11 + b2 * a21;
    matVar[2] = b0 * a02 + b1 * a12 + b2 * a22;
    matVar[3] = b0 * a03 + b1 * a13 + b2 * a23;
    b0 = quatMat4; b1 = quatMat5; b2 = quatMat6;
    matVar[4] = b0 * a00 + b1 * a10 + b2 * a20;
    matVar[5] = b0 * a01 + b1 * a11 + b2 * a21;
    matVar[6] = b0 * a02 + b1 * a12 + b2 * a22;
    matVar[7] = b0 * a03 + b1 * a13 + b2 * a23;
    b0 = quatMat8; b1 = quatMat9; b2 = quatMat10;
    matVar[8] = b0 * a00 + b1 * a10 + b2 * a20;
    matVar[9] = b0 * a01 + b1 * a11 + b2 * a21;
    matVar[10] = b0 * a02 + b1 * a12 + b2 * a22;
    matVar[11] = b0 * a03 + b1 * a13 + b2 * a23;
  }`,
};

// Optimized version of general operation applications that can be used when
// the target matrix is known to be the identity matrix.
const MatrixOpsInitial = {
  transformScale: `function(matVar, opVar) {
    // Scaling matVar known to be identity by opVar
    matVar[0] = opVar[0];
    matVar[1] = 0;
    matVar[2] = 0;
    matVar[3] = 0;
    matVar[4] = 0;
    matVar[5] = opVar[1];
    matVar[6] = 0;
    matVar[7] = 0;
    matVar[8] = 0;
    matVar[9] = 0;
    matVar[10] = opVar[2];
    matVar[11] = 0;
    matVar[12] = 0;
    matVar[13] = 0;
    matVar[14] = 0;
    matVar[15] = 1;
  }`,

  transformTranslate: `function(matVar, opVar) {
    // Translating matVar known to be identity by opVar;
    matVar[0] = 1;
    matVar[1] = 0;
    matVar[2] = 0;
    matVar[3] = 0;
    matVar[4] = 0;
    matVar[5] = 1;
    matVar[6] = 0;
    matVar[7] = 0;
    matVar[8] = 0;
    matVar[9] = 0;
    matVar[10] = 1;
    matVar[11] = 0;
    matVar[12] = opVar[0];
    matVar[13] = opVar[1];
    matVar[14] = opVar[2];
    matVar[15] = 1;
  }`,

  /**
   * @param {array} matVar Both the input, and the output matrix - assumed to be
   * identity.
   * @param {quaternion specification} q Four element array describing rotation.
   */
  transformRotateRadians: `function(matVar, q) {

    // Rotating matVar which is known to be identity by q
    var xQuat = q[0], yQuat = q[1], zQuat = q[2], wQuat = q[3];
    var x2Quat = xQuat + xQuat;
    var y2Quat = yQuat + yQuat;
    var z2Quat = zQuat + zQuat;
    var xxQuat = xQuat * x2Quat;
    var xyQuat = xQuat * y2Quat;
    var xzQuat = xQuat * z2Quat;
    var yyQuat = yQuat * y2Quat;
    var yzQuat = yQuat * z2Quat;
    var zzQuat = zQuat * z2Quat;
    var wxQuat = wQuat * x2Quat;
    var wyQuat = wQuat * y2Quat;
    var wzQuat = wQuat * z2Quat;
    // Step 1: Inlines the construction of a quaternion matrix ('quatMat')
    var quatMat0 = 1 - (yyQuat + zzQuat);
    var quatMat1 = xyQuat + wzQuat;
    var quatMat2 = xzQuat - wyQuat;
    var quatMat4 = xyQuat - wzQuat;
    var quatMat5 = 1 - (xxQuat + zzQuat);
    var quatMat6 = yzQuat + wxQuat;
    var quatMat8 = xzQuat + wyQuat;
    var quatMat9 = yzQuat - wxQuat;
    var quatMat10 = 1 - (xxQuat + yyQuat);
    // quatMat3/7/11/12/13/14 = 0, quatMat15 = 1

    // Step 2: Inlines the multiplication with identity matrix.
    var b0  = quatMat0, b1 = quatMat1, b2 = quatMat2;
    matVar[0] = b0;
    matVar[1] = b1;
    matVar[2] = b2;
    matVar[3] = 0;
    b0 = quatMat4; b1 = quatMat5; b2 = quatMat6;
    matVar[4] = b0;
    matVar[5] = b1;
    matVar[6] = b2;
    matVar[7] = 0;
    b0 = quatMat8; b1 = quatMat9; b2 = quatMat10;
    matVar[8] = b0;
    matVar[9] = b1;
    matVar[10] = b2;
    matVar[11] = 0;
    matVar[12] = 0;
    matVar[13] = 0;
    matVar[14] = 0;
    matVar[15] = 1;
  }`,
};

const setNextValAndDetectChange = function (name, tmpVarName) {
  return (
    `  if (!didChange) {\n` +
    `    var prevVal = result.${name};\n` +
    `    result.${name} = ${tmpVarName};\n` +
    `    didChange = didChange  || (${tmpVarName} !== prevVal);\n` +
    `  } else {\n` +
    `    result.${name} = ${tmpVarName};\n` +
    `  }\n`
  );
};

const computeNextValLinear = function (anim, from, to, tmpVarName) {
  const hasRoundRatio = 'round' in anim;
  const roundRatio = anim.round;
  let fn = `  ratio = (value - ${anim.min}) / ${anim.max - anim.min};\n`;
  if (!anim.extrapolate) {
    fn += '  ratio = ratio > 1 ? 1 : (ratio < 0 ? 0 : ratio);\n';
  }

  const roundOpen = hasRoundRatio ? `Math.round(${roundRatio} * ` : '';
  const roundClose = hasRoundRatio ? `) / ${roundRatio}` : '';
  fn += `  ${tmpVarName} = ${roundOpen}(${from} * (1 - ratio) + ${to} * ratio)${
    roundClose
  };\n`;
  return fn;
};

const computeNextValLinearScalar = function (anim) {
  return computeNextValLinear(anim, anim.from, anim.to, 'nextScalarVal');
};

const computeNextValConstant = function (anim) {
  const constantExpression = JSON.stringify(anim.value);
  return `  nextScalarVal = ${constantExpression};\n`;
};

const computeNextValStep = function (anim) {
  return `  nextScalarVal = value >= ${anim.threshold} ? ${anim.to} : ${
    anim.from
  };\n`;
};

const computeNextValIdentity = function (anim) {
  return '  nextScalarVal = value;\n';
};

const operationVar = function (name) {
  return `${name}ReuseOp`;
};

const createReusableOperationVars = function (anims) {
  let ret = '';
  for (const name in anims) {
    if (ShouldAllocateReusableOperationVars[name]) {
      ret += `var ${operationVar(name)} = [];\n`;
    }
  }
  return ret;
};

const newlines = function (statements) {
  return `\n${statements.join('\n')}\n`;
};

/**
 * @param {Animation} anim Configuration entry.
 * @param {key} dimension Key to examine in `from`/`to`.
 * @param {number} index Field in operationVar to set.
 * @return {string} Code that sets the operation variable's field.
 */
const computeNextMatrixOperationField = function (
  anim,
  name,
  dimension,
  index,
) {
  const fieldAccess = `${operationVar(name)}[${index}]`;
  if (anim.from[dimension] !== undefined && anim.to[dimension] !== undefined) {
    return `  ${anim.from[dimension]}` !== anim.to[dimension]
      ? computeNextValLinear(
          anim,
          anim.from[dimension],
          anim.to[dimension],
          fieldAccess,
        )
      : `${fieldAccess} = ${anim.from[dimension]};`;
  }
  return `  ${fieldAccess} = ${InitialOperationField[name][index]};`;
};

const unrolledVars = [];
for (let varIndex = 0; varIndex < 16; varIndex++) {
  unrolledVars.push(`m${varIndex}`);
}
const setNextMatrixAndDetectChange = function (orderedMatrixOperations) {
  const fn = [
    '  var transform = result.transform !== undefined ? ' +
      'result.transform : (result.transform = [{ matrix: [] }]);' +
      '  var transformMatrix = transform[0].matrix;',
  ];
  fn.push.apply(
    fn,
    inline(MatrixOps.unroll, ['transformMatrix'].concat(unrolledVars)),
  );
  for (let i = 0; i < orderedMatrixOperations.length; i++) {
    const opName = orderedMatrixOperations[i];
    if (i === 0) {
      fn.push.apply(
        fn,
        inline(MatrixOpsInitial[opName], [
          'transformMatrix',
          operationVar(opName),
        ]),
      );
    } else {
      fn.push.apply(
        fn,
        inline(MatrixOps[opName], ['transformMatrix', operationVar(opName)]),
      );
    }
  }
  fn.push.apply(
    fn,
    inline(
      MatrixOps.matrixDiffers,
      ['didChange', 'transformMatrix'].concat(unrolledVars),
    ),
  );
  return fn;
};

const InterpolateMatrix = {
  transformTranslate: true,
  transformRotateRadians: true,
  transformScale: true,
};

const createFunctionString = function (anims) {
  // We must track the order they appear in so transforms are applied in the
  // correct order.
  const orderedMatrixOperations = [];

  // Wrapping function allows the final function to contain state (for
  // caching).
  let fn = 'return (function() {\n';
  fn += createReusableOperationVars(anims);
  fn += 'return function(result, value) {\n';
  fn += '  var didChange = false;\n';
  fn += '  var nextScalarVal;\n';
  fn += '  var ratio;\n';

  for (const name in anims) {
    const anim = anims[name];
    if (anim.type === 'linear') {
      if (InterpolateMatrix[name]) {
        orderedMatrixOperations.push(name);
        const setOperations = [
          computeNextMatrixOperationField(anim, name, X_DIM, 0),
          computeNextMatrixOperationField(anim, name, Y_DIM, 1),
          computeNextMatrixOperationField(anim, name, Z_DIM, 2),
        ];
        if (name === TRANSFORM_ROTATE_NAME) {
          setOperations.push(
            computeNextMatrixOperationField(anim, name, W_DIM, 3),
          );
        }
        fn += newlines(setOperations);
      } else {
        fn += computeNextValLinearScalar(anim, 'nextScalarVal');
        fn += setNextValAndDetectChange(name, 'nextScalarVal');
      }
    } else if (anim.type === 'constant') {
      fn += computeNextValConstant(anim);
      fn += setNextValAndDetectChange(name, 'nextScalarVal');
    } else if (anim.type === 'step') {
      fn += computeNextValStep(anim);
      fn += setNextValAndDetectChange(name, 'nextScalarVal');
    } else if (anim.type === 'identity') {
      fn += computeNextValIdentity(anim);
      fn += setNextValAndDetectChange(name, 'nextScalarVal');
    }
  }
  if (orderedMatrixOperations.length) {
    fn += newlines(setNextMatrixAndDetectChange(orderedMatrixOperations));
  }
  fn += '  return didChange;\n';
  fn += '};\n';
  fn += '})()';
  return fn;
};

/**
 * @param {object} anims Animation configuration by style property name.
 * @return {function} Function accepting style object, that mutates that style
 * object and returns a boolean describing if any update was actually applied.
 */
const buildStyleInterpolator = function (anims) {
  // Defer compiling this method until we really need it.
  let interpolator = null;
  function lazyStyleInterpolator(result, value) {
    if (interpolator === null) {
      interpolator = Function(createFunctionString(anims))();
    }
    return interpolator(result, value);
  }
  return lazyStyleInterpolator;
};

module.exports = buildStyleInterpolator;
