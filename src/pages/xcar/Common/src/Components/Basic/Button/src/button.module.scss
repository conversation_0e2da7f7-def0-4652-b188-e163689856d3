@import '../../../../Tokens/tokens/color.scss';

.labelStyle {
  position: absolute;
  top: -14px;
  justify-content: center;
  align-items: center;
  background-color: $orangeBase;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 12px;
  padding-left: 10px;
  padding-right: 10px;
  min-width: 74px;
  height: 24px;
}
.labelTextStyle {
  font-size: 20px;
  line-height: 26px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $white;
}
.rightTopHookStyle {
  width: 24px;
  height: 24px;
  position: absolute;
  right: 0px;
  top: 0px;
}
.rightBottomHookSprayStyle {
  width: 96px;
  height: 88px;
  position: absolute;
  right: 0px;
  bottom: 0px;
  z-index: 10;
}
.rightBottomHookStyle {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 0px;
  bottom: 0px;
  z-index: 10;
}
.loadingWrap {
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.loading {
  width: 36px;
  height: 36px;
}
.iconView {
  flex-direction: row;
}
