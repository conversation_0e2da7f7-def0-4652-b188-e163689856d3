import Image from '@c2x/components/Image';
import {
  xMergeStyles,
  XView as View,
  XImageProps as ImageProps,
  XLinearGradient as LinearGradient,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';
import c2xStyles from './button.module.scss';
// @ts-ignore
import { withTheme } from '../../../../Theming'; // @ts-ignore
import { color, button, tokenType } from '../../../../Tokens';
import Text from '../../Text'; // @ts-ignore
import BbkTouchable, { Props as TouchableProps } from '../../Touchable/src';
import { Props as LabelProps } from '../../Label';
import { buttonTheme } from './Theme';
export const DIMG04_PATH = 'https://dimg04.c-ctrip.com/images/';
const sprayCorner = `${DIMG04_PATH}1tg4k12000cxv1rii421D.png`;

const filterCorner = `https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/filterSelected.png`;

interface Props
  extends tokenType.ColorProps,
    tokenType.ButtonProps,
    TouchableProps {
  /**
   * button text
   */
  text?: string;
  /**
   * theme from withTheme wrap
   */
  theme?: buttonTheme;
  /**
   * button style
   * these styles will be covered by IBUButton, so these are invalid
   *    {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingLeft: padding,
          paddingRight: padding,
          backgroundColor: wrapperViewColor,
          borderRadius: 2,
        }
   */
  buttonStyle?: CSSProperties;
  /**
   * button text style
   */
  textStyle?: CSSProperties;
  /**
   * button with icon content
   * such as &#xee4f;
   */
  iconCode?: string;
  /**
   * icon style
   */
  iconStyle?: CSSProperties;

  onPress?: () => void;

  textLines?: number;

  imageUrl?: string;

  imageProps?: ImageProps;

  labelProps?: LabelProps;

  showRightTopHook?: boolean;

  showRightBottomHook?: boolean;

  rightBottomHookUrl?: string;

  gradientColorArr?: Array<string>;

  iconShowRight?: boolean;

  isLoading?: boolean;
}

const getThemeColor = (theme, colorType) => {
  theme = theme || {};
  return theme[colorType] || theme[tokenType.ColorType.Blue] || {};
};

const getButtonStyle = (
  buttonType,
  { theme, buttonSize, colorType }: Props,
) => {
  const isGradient = buttonType === tokenType.ButtonType.Gradient;
  const isSpray = buttonType === tokenType.ButtonType.Spray;
  const colorTheme = getThemeColor(theme, colorType);
  const { buttonGradientColor = {} as any } = colorTheme;
  const buttonSizeStyle =
    button[`button${buttonSize}Style`] || button.buttonMStyle;
  const buttonTextSizeStyle =
    button[`button${buttonSize}TextStyle`] || button.buttonMTextStyle;
  const buttonColor = color[`${colorType}Base`] || color.blueBase;
  const buttonStyleBgColor = colorTheme.buttonBgColor || buttonColor;
  const sprayBtn = isSpray ? button.buttonSprayStyle : null;
  return {
    $buttonStyle: xMergeStyles([
      buttonSizeStyle,
      {
        backgroundColor: isSpray ? color.deepBlueBase : buttonStyleBgColor,
        ...sprayBtn,
      },
    ]),

    $buttonTextStyle: xMergeStyles([
      buttonTextSizeStyle,
      {
        color:
          (isGradient
            ? buttonGradientColor.buttonGradientTextColor
            : colorTheme.buttonTextColor) || color.white,
      },
    ]),

    $gradientColorArr: [
      buttonGradientColor.from || color.linearGradientOrangeLight,
      buttonGradientColor.to || color.linearGradientOrangeDark,
    ],
  };
};

const IbuMap = {
  buttonSize: {
    [tokenType.ButtonSize.S]: 'sm',
    [tokenType.ButtonSize.M]: 'md',
    [tokenType.ButtonSize.L]: 'lg',
  },
  colorStyle: {
    [tokenType.ColorType.Blue]: 'blue',
    [tokenType.ColorType.Orange]: 'orange',
    [tokenType.ColorType.Transparent]: 'transparent',
  },
};

function BbkButton(props: Props) {
  const {
    text,
    colorType = tokenType.ColorType.Blue,
    theme,
    buttonStyle,
    textStyle,
    buttonSize,
    buttonType,
    iconCode,
    iconStyle,
    textLines,
    imageUrl,
    imageProps,
    labelProps,
    showRightTopHook = false,
    showRightBottomHook = false,
    rightBottomHookUrl,
    gradientColorArr,
    iconShowRight,
    isLoading,
    ...passThroughProps
  } = props;

  const isGradient = buttonType === tokenType.ButtonType.Gradient;
  const isSpray = buttonType === tokenType.ButtonType.Spray;
  const { $buttonStyle, $buttonTextStyle, $gradientColorArr } = getButtonStyle(
    buttonType,
    props,
  );
  const textDom = (
    <Text
      numberOfLines={textLines}
      style={xMergeStyles([$buttonTextStyle, textStyle])}
    >
      {text}
    </Text>
  );

  const iconDom =
    iconCode &&
    (iconCode.indexOf('http') > -1 ? (
      <Image src={iconCode} style={iconStyle} />
    ) : (
      <Text type="icon" style={xMergeStyles([$buttonTextStyle, iconStyle])}>
        {iconCode}
      </Text>
    ));

  const imageDom = imageUrl && <Image {...imageProps} src={imageUrl} />;

  const innerDom = imageUrl ? imageDom : textDom;
  const labelDom = labelProps && (
    <View
      className={c2xStyles.labelStyle}
      style={xMergeStyles([
        labelProps.labelStyle,
        (!labelProps.labelStyle ||
          !labelProps.labelStyle.hasOwnProperty('left')) &&
          styles.$labelStyleRight,
      ])}
    >
      <Text className={c2xStyles.labelTextStyle} style={labelProps.textStyle}>
        {labelProps.text}
      </Text>
    </View>
  );

  const rightTopHook = showRightTopHook && (
    <Image
      src={
        'https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/filterSelect.png'
      }
      className={c2xStyles.rightTopHookStyle}
    />
  );

  const rightBottomHook =
    showRightBottomHook &&
    (isSpray ? (
      <Image
        src={rightBottomHookUrl || sprayCorner}
        className={c2xStyles.rightBottomHookSprayStyle}
      />
    ) : (
      <Image
        src={rightBottomHookUrl || filterCorner}
        className={c2xStyles.rightBottomHookStyle}
      />
    ));

  const children = iconShowRight ? (
    <>
      {innerDom}
      {iconDom}
    </>
  ) : (
    <>
      {iconDom}
      {innerDom}
    </>
  );

  if (isLoading) {
    return (
      <BbkTouchable
        style={!isGradient && xMergeStyles([$buttonStyle, buttonStyle])}
        debounce={true}
        {...passThroughProps}
        disabled={true}
      >
        <View className={c2xStyles.loadingWrap}>
          <Image
            src={`${DIMG04_PATH}1tg0i12000cw6mi0fC09A.gif`}
            className={c2xStyles.loading}
          />
        </View>
      </BbkTouchable>
    );
  }

  return (
    <BbkTouchable
      debounce={true}
      style={!isGradient && xMergeStyles([$buttonStyle, buttonStyle])}
      {...passThroughProps}
    >
      {rightTopHook}
      {rightBottomHook}
      {isGradient ? (
        <LinearGradient
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 1.0 }}
          locations={[0, 1]}
          colors={gradientColorArr || $gradientColorArr}
          style={xMergeStyles([$buttonStyle, buttonStyle])}
        >
          {children}
        </LinearGradient>
      ) : (
        <View className={c2xStyles.iconView}>{children}</View>
      )}
      {labelDom}
    </BbkTouchable>
  );
}

BbkButton.defaultProps = {
  buttonSize: tokenType.ButtonSize.M,
  buttonType: tokenType.ButtonType.Default,
};
const styles = StyleSheet.create({ $labelStyleRight: { right: 0 } });

export default withTheme(BbkButton);
