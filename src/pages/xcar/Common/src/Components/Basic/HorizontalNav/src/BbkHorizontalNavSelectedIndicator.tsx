import React from 'react';
import { xMergeStyles, XAnimated, xCreateAnimation } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import { color } from '../../../../Tokens';

const styles = StyleSheet.create({
  selectedIndicator: {
    backgroundColor: color.blueBase,
  },
});

export type Props = {
  xOffset?: number;
  width?: number;
  height?: number;
  bottom?: number;
  indicatorColor?: any;
  myIndicator?: any;
};

const BbkHorizontalNavSelectedIndicator = (props: Props) => {
  const { xOffset, width, height, bottom, indicatorColor, myIndicator } = props;
  const style: any = [
    styles.selectedIndicator,
    { width, height, backgroundColor: indicatorColor || color.blueBase },
  ];
  if (typeof bottom === 'number') {
    style.push({ bottom });
  }
  const [animationData, setAnimationData] = React.useState<any>(null);

  const animation = React.useMemo(() => {
    return xCreateAnimation({
      duration: 200,
      delay: 0,
      timingFunction: 'ease',
    });
  }, []);

  React.useEffect(() => {
    animation.translateX(xOffset || 0).step();
    setAnimationData(animation?.export());
  }, [xOffset, animation]);

  return myIndicator ? (
    <XAnimated.View animation={animationData} style={xMergeStyles(style)} >
      {myIndicator}
    </XAnimated.View>
  ) : (
    <XAnimated.View animation={animationData} style={xMergeStyles(style)} />
  );
};

BbkHorizontalNavSelectedIndicator.defaultProps = {
  xOffset: null,
  width: null,
  bottom: null,
  indicatorColor: null,
};

export default React.memo(BbkHorizontalNavSelectedIndicator);
