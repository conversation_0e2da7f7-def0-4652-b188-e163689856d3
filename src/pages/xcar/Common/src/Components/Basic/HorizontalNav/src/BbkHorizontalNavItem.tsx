import Image from '@c2x/components/Image';
import {
  xMergeStyles,
  XView as View,
  XLinearGradient as LinearGradient,
} from '@ctrip/xtaro';
import Platform from '@c2x/apis/Platform';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, CSSProperties } from 'react';
import c2xStyles from './bbkHorizontalNavItemC2xStyles.module.scss';

import { color, space, font } from '../../../../Tokens';
import { BbkUtils, BbkStyleUtil } from '../../../../Utils';
import { getThemeAttributes, withTheme } from '../../../../Theming';
import BbkComponentTouchable from '../../Touchable/src';
import BbkText from '../../Text';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';

const { getPixel } = BbkUtils;

const { blueBase } = color;
const styles = StyleSheet.create({
  view: {
    height: getPixel(22),
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewSmall: {
    height: getPixel(18),
  },
  text: {
    color: color.fontPrimary,
    paddingLeft: space.spaceS,
    paddingRight: space.spaceS,
    ...font.body3LightStyle,
  },
  textDisabled: { color: color.grayBase },
  textSelected: { color: color.blueBase, ...font.body3BoldStyle },
  subTitleText: {
    color: color.fontSubDark,
    ...font.labelSLightStyle,
    marginTop: getPixel(-6),
  },
  subTitleTextSelected: { color: color.blueBase },
  easyLifeTagStyle: {
    position: 'absolute',
    top: 0,
    right: getPixel(-2),
    ...BbkStyleUtil.getWH(80, 26),
  },
  newEasyLifeTagStyle: { right: 0, ...BbkStyleUtil.getWH(92, 33) },
});

export type Props = {
  id: string;
  onPress: () => {};
  title: string;
  accessibilityLabel?: string;
  disabled: boolean;
  selected: boolean;
  style: CSSProperties;
  small: boolean;
  theme?: Object;
  textStyle?: CSSProperties;
  textDisabledStyle?: CSSProperties;
  textSelectedStyle?: CSSProperties;
  subTitleSelectedStyle?: CSSProperties;
  subTitle?: string;
  isEasyLife?: boolean;
  animateIndicator?: boolean;
  iWidth?: number;
  iHeight?: number;
  iBottom?: number;
  useNewEasyLifeStyle?: boolean;
  selectedDisable?: boolean;
  indicatorColor?: string;
  isLinearIndicator?: boolean;
  connectElement?: React.ReactNode;
  testID?: string;
  isShowIndicator?: boolean;
};

const horizontalNavSelectedTextColor = 'horizontalNavSelectedTextColor';

const propsAreEqual = (prevProps, nextProps) =>
  prevProps.selected === nextProps.selected &&
  prevProps.disabled === nextProps.disabled &&
  prevProps.title === nextProps.title;

const BbkHorizontalNavItem = (props: Props) => {
  const {
    accessibilityLabel,
    disabled,
    selected,
    style,
    theme,
    small,
    title,
    textStyle,
    textSelectedStyle,
    textDisabledStyle,
    subTitle,
    subTitleSelectedStyle,
    isEasyLife,
    animateIndicator,
    iWidth,
    iHeight,
    iBottom = 0,
    useNewEasyLifeStyle,
    selectedDisable = true,
    indicatorColor,
    isLinearIndicator = false,
    connectElement,
    isShowIndicator = true,
    ...rest
  } = props;
  const accessibilityStates = [];
  const textSize = small ? 'sm' : 'base';
  const viewStyles = [];
  const textStyles = [];
  const subTitleStyles = [];
  viewStyles.push(styles.view);
  textStyles.push(styles.text);
  subTitleStyles.push(styles.subTitleText);
  const themeAttributes: any =
    getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) || {};
  const horizontalNavSelectedIndicatorColor = indicatorColor || blueBase;

  textStyles.push(textStyle);

  if (disabled) {
    accessibilityStates.push('disabled');
    textStyles.push(styles.textDisabled);
    textStyles.push(textDisabledStyle);
    subTitleStyles.push(styles.textDisabled);
    subTitleStyles.push(textDisabledStyle);
  } else if (selected) {
    textStyles.push(styles.textSelected);
    if (themeAttributes) {
      textStyles.push({
        color: themeAttributes[horizontalNavSelectedTextColor],
      });
      subTitleStyles.push({
        color: themeAttributes[horizontalNavSelectedTextColor],
      });
    }
    textStyles.push(textSelectedStyle);
    subTitleStyles.push(styles.subTitleTextSelected);
    subTitleStyles.push(subTitleSelectedStyle);
  }

  if (small) {
    viewStyles.push(styles.viewSmall);
  }

  if (style) {
    viewStyles.push(style);
  }
  // @ts-ignore
  const isAndroid = Platform.OS === 'android' || Platform.OS === 'harmony';
  const platformProps = isAndroid ? { borderlessBackground: false } : {};
  const easyLifeImgUrl = `https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/easyLifeTag${
    useNewEasyLifeStyle ? 2 : ''
  }.png`;

  return (
    <BbkComponentTouchable
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel || title}
      // @ts-ignore 升级072
      accessibilityStates={accessibilityStates}
      disabled={disabled || (selectedDisable && selected)}
      {...platformProps}
      {...rest}
      style={xMergeStyles(viewStyles)}
    >
      <BbkText textStyle={textSize} style={xMergeStyles(textStyles)}>
        {title}
      </BbkText>
      {!!connectElement && connectElement}
      {!!subTitle && (
        <BbkText style={xMergeStyles(subTitleStyles)}>{subTitle}</BbkText>
      )}
      {isEasyLife && (
        <Image
          style={xMergeStyles([
            styles.easyLifeTagStyle,
            useNewEasyLifeStyle && styles.newEasyLifeTagStyle,
          ])}
          resizeMode="contain"
          source={{ uri: easyLifeImgUrl }}
        />
      )}
      {!animateIndicator && selected && isShowIndicator && (
        <>
          {!isLinearIndicator ? (
            <View
              className={c2xStyles.indicator}
              style={{
                width: iWidth,
                height: iHeight,
                bottom: iBottom,
                backgroundColor: horizontalNavSelectedIndicatorColor,
              }}
            />
          ) : (
            <LinearGradient
              start={{ x: 0, y: 0.5 }}
              end={{ x: 1.0, y: 0.5 }}
              locations={[0, 1]}
              colors={[color.C_41A5F9, color.C_006FF6]}
              className={c2xStyles.indicatorLinear}
              style={{
                width: iWidth,
                height: iHeight,
                bottom: iBottom,
                backgroundColor: horizontalNavSelectedIndicatorColor,
              }}
            />
          )}
        </>
      )}
    </BbkComponentTouchable>
  );
};

// @ts-ignore
export default memo(withTheme(BbkHorizontalNavItem), propsAreEqual);
