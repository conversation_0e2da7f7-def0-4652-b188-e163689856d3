import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';
import c2xStyles from './bbkHorizontalNavC2xStyles.module.scss';

import { border, color } from '../../../../Tokens';
import { BbkUtils } from '../../../../Utils';
import AnimatedIndicator from './BbkHorizontalNavSelectedIndicator';

const { vw } = BbkUtils;

const { borderSizeLg } = border;

const styles = StyleSheet.create({
  nav: {
    borderColor: color.transparent,
    borderBottomColor: color.grayBorder,
    flexDirection: 'column',
    borderBottomWidth: border.borderSizeSm,
  },
  navSpaceAround: { flexGrow: 1 },
  inner: { flexDirection: 'row' },
  innerSpaceAround: { justifyContent: 'space-around' },
});

export type Props = {
  children: any;
  selectedId: string;
  spaceAround: boolean;
  style: any;
  animateIndicator?: boolean;
  indicatorWidth: number;
  indicatorHeight?: number;
  isAutoSlide?: boolean;
  animateDruation?: number;
  isScrollAnimated?: boolean;
  indicatorBottom?: number;
  indicatorColor?: any;
  animateIndicatorStyle?: CSSProperties;
  isLinearIndicator?: boolean;
  myIndicator?: any;
};

type State = {
  indicatorOffsetX?: number;
  indicatorWidth?: number;
  innerWidth?: number;
};

class BbkHorizontalNav extends React.Component<Props, State> {
  static defaultProps = {
    spaceAround: false,
    style: null,
    indicatorWidth: 0,
    animateIndicator: true,
  };

  childrenPositions = {};

  navRef: any;

  constructor(props) {
    super(props);
    this.state = {
      indicatorOffsetX: null,
      indicatorWidth: null,
      innerWidth: 0,
    };
  }

  /**
   * 设置选中的Tab
   * @param indicatorOffsetX 偏移量
   * @param indicatorWidth 宽度
   * @param isScrollAnimated 是否滚动条定位到选中的选项
   */
  setSelectedId = (indicatorOffsetX, indicatorWidth, isScrollAnimated) => {
    this.setState({
      indicatorOffsetX,
      indicatorWidth,
    });
    if (isScrollAnimated) {
      // Center the selected component
      const deviceWidth = vw(100);
      const screenCenterPoint = deviceWidth / 2;
      const nextCenterPoint = indicatorOffsetX + indicatorWidth / 2;
      const gap = nextCenterPoint - screenCenterPoint;
      this.navRef.scrollTo({ x: gap, y: 0, animated: true });
    }
  };

  UNSAFE_componentWillReceiveProps(nextProps: Props) {
    if (nextProps.selectedId && this.childrenPositions[nextProps.selectedId]) {
      const nextLayoutProps = this.childrenPositions[nextProps.selectedId];
      this.setSelectedId(
        nextLayoutProps.x,
        nextLayoutProps.width,
        nextProps.isAutoSlide !== false,
      );
    }
  }

  onChildLayout = (event: any, id: string) => {
    const { width, x } = event.nativeEvent.layout;
    const { selectedId, isScrollAnimated } = this.props;
    this.childrenPositions[id] = { width, x };

    if (selectedId === id) {
      this.setSelectedId(x, width, isScrollAnimated);
    }
  };

  render() {
    const {
      animateIndicator,
      indicatorWidth: iWidth,
      indicatorHeight: iHeight,
      children,
      selectedId,
      spaceAround,
      style,
      indicatorBottom,
      indicatorColor,
      animateDruation,
      animateIndicatorStyle,
      isLinearIndicator,
      myIndicator,
      ...rest
    } = this.props;

    const navStyle: any = [];
    navStyle.push(styles.nav);
    const innerViewStyle: any = [];
    innerViewStyle.push(styles.inner);

    if (spaceAround) {
      navStyle.push(styles.navSpaceAround);
      innerViewStyle.push(styles.innerSpaceAround);
    }

    if (style) {
      navStyle.push(style);
    }

    const enhancedChildren = React.Children.map(children, child => {
      const temp = {
        key: child.props.id,
        selected: selectedId === child.props.id,
        animateIndicator,
        iWidth,
        iHeight,
        iBottom: indicatorBottom,
        indicatorColor,
        onLayout: event => this.onChildLayout(event, child.props.id),
      };
      return React.isValidElement(child) && React.cloneElement(child, temp);
    });

    const renderIndicator = (indiWidth, indiHeight) => {
      if (!animateIndicator) {
        return null;
      }
      const { indicatorOffsetX, indicatorWidth } = this.state;
      if (indicatorOffsetX === null) {
        return null;
      }
      let marginL = 0;
      if (indiWidth > 0 && indiWidth < indicatorWidth) {
        marginL = (indicatorWidth - indiWidth) / 2;
      }

      const IndicatorComponent = AnimatedIndicator;
      return (
        <View
          className={c2xStyles.indicatorWrapper}
          style={xMergeStyles([{ marginLeft: marginL }, animateIndicatorStyle])}
        >
          <IndicatorComponent
            bottom={indicatorBottom}
            indicatorColor={indicatorColor}
            xOffset={indicatorOffsetX}
            width={indiWidth || iWidth}
            height={indiHeight || borderSizeLg}
            myIndicator={myIndicator}
          />
        </View>
      );
    };

    return (
      <ScrollView
        ref={ref => {
          this.navRef = ref;
        }}
        alwaysBounceHorizontal={false}
        contentContainerStyle={xMergeStyles(navStyle)}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        {...rest}
      >
        <View
          onLayout={e => {
            this.setState({
              innerWidth: e.nativeEvent.layout.width,
            });
          }}
          style={xMergeStyles(innerViewStyle)}
        >
          {enhancedChildren}
        </View>
        {renderIndicator(iWidth, iHeight)}
      </ScrollView>
    );
  }
}

export default BbkHorizontalNav;
