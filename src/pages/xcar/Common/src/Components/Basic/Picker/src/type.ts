import { number } from 'prop-types';

export interface IPicker {
  title: string;
  selected: string | number;
  list: Array<string | number>;
  unit?: string;
  selected2?: string | number;
  list2?: Array<string | number>;
  unit2?: string;
  confirmCallback: (...args) => void;
  cancelCallback?: (...args) => void;
  valueChangeCallback?: (...args) => number | string;
}
export interface ICtripIOSPicker extends IPicker {
  visible: boolean;
}
