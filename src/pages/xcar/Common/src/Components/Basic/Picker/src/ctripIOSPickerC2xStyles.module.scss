@import '../../../../Tokens/tokens/color.scss';

.button {
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 35px;
  padding-bottom: 35px;
}
.pickerWrapper {
  position: relative;
  flex-direction: row;
  height: 232px;
}
.pickerInner {
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.pickerUnitWrap {
  position: relative;
  height: 80px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.pickerBKLine {
  position: absolute;
  left: -40;
  width: 100vw;
  height: 10px;
  background-color: $transparent;
  z-index: 10;
}
