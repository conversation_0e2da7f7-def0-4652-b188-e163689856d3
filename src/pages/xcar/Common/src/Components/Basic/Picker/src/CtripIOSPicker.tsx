import { xMergeStyles } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import Dimensions from '@c2x/apis/Dimensions';
import React, { useState, memo, useEffect } from 'react'; // @ts-ignore
import { XView as View } from '@ctrip/xtaro';
// @ts-ignore
import { PickerIOS } from '@c2x/components/DatePickerIOS';
import c2xStyles from './ctripIOSPickerC2xStyles.module.scss';
import { BbkUtils } from '../../../../Utils';
import BbkTouchable from '../../Touchable/src';
import { color, font } from '../../../../Tokens';
import { ICtripIOSPicker } from './type';
import Text from '../../Text';
import BbkComponentModal, {
  BbkComponentModalAnimationPreset,
} from '../../Modal';
import BbkComponentText from '../../Text';

const { getPixel, ensureFunctionCall, compareProps } = BbkUtils;
const { width } = Dimensions.get('window');

const CtripIOSPicker: (props: ICtripIOSPicker) => any = ({
  title,
  selected,
  selected2,
  list,
  list2,
  unit,
  unit2,
  confirmCallback,
  cancelCallback,
  valueChangeCallback,
  visible,
}) => {
  const [selectedValue, setSelectedValue] = useState(selected);
  const [selectedValue2, setSelectedValue2] = useState(selected2);
  const animations = BbkComponentModalAnimationPreset('bottom');
  const hasList2 = list2 && list2.length > 0;

  const cancelReset = () => {
    setSelectedValue(selected);
    setSelectedValue2(selected2);
    ensureFunctionCall(cancelCallback);
  };

  useEffect(() => {
    setSelectedValue(selected);
  }, [selected]);

  useEffect(() => {
    setSelectedValue2(selected2);
  }, [selected2]);

  return (
    <BbkComponentModal
      modalVisible={visible}
      onRequestClose={cancelReset}
      {...animations}
    >
      <View style={styles.wrapper}>
        <View style={styles.headerWrapper}>
          <BbkTouchable onPress={cancelReset} className={c2xStyles.button}>
            <Text
              style={xMergeStyles([
                font.title2BoldStyle,
                {
                  color: color.fontPrimary,
                },
              ])}
              fontWeight="bold"
            >
              取消
            </Text>
          </BbkTouchable>
          <Text
            style={xMergeStyles([
              { color: color.fontPrimary },
              font.subTitle1Style,
            ])}
          >
            {title}
          </Text>
          <BbkTouchable
            onPress={() => {
              ensureFunctionCall(confirmCallback, null, [
                selectedValue,
                selectedValue2,
              ]);
            }}
            className={c2xStyles.button}
          >
            <Text
              style={xMergeStyles([
                font.title2BoldStyle,
                {
                  color: color.blueBase,
                },
              ])}
              fontWeight="bold"
            >
              确定
            </Text>
          </BbkTouchable>
        </View>
        <View className={c2xStyles.pickerWrapper}>
          {hasList2 && (
            <View style={xMergeStyles([styles.pickerLine, { top: 93 }])} />
          )}
          <View className={c2xStyles.pickerInner}>
            {/* @ts-ignore */}
            <PickerIOS
              style={hasList2 ? styles.pickerStyle : styles.flex1}
              selectedValue={selectedValue}
              onValueChange={data => {
                setSelectedValue(data as string);
                if (typeof valueChangeCallback === 'function') {
                  const newSelectedValue2 = valueChangeCallback(
                    data,
                    selectedValue2,
                  );
                  setSelectedValue2(newSelectedValue2);
                }
              }}
            >
              {list.map((item, i) => (
                //  @ts-ignore
                <PickerIOS.Item
                  key={`age_${i}`}
                  label={`${item}`}
                  value={item}
                />
              ))}
            </PickerIOS>
            {!!unit && (
              <View className={c2xStyles.pickerUnitWrap}>
                <View className={c2xStyles.pickerBKLine} style={{ top: 1 }} />
                <BbkComponentText style={font.subHeadStyle}>
                  {unit}
                </BbkComponentText>
                <View
                  className={c2xStyles.pickerBKLine}
                  style={{ bottom: 0 }}
                />
              </View>
            )}
          </View>
          {hasList2 && (
            <>
              <View className={c2xStyles.pickerInner}>
                {/* @ts-ignore */}
                <PickerIOS
                  style={styles.pickerStyle}
                  selectedValue={selectedValue2}
                  onValueChange={data => {
                    setSelectedValue2(data as string);
                  }}
                >
                  {list2.map((item, i) => (
                    // @ts-ignore
                    <PickerIOS.Item
                      key={`age_${i}`}
                      label={`${item}`}
                      value={item}
                    />
                  ))}
                </PickerIOS>
                {!!unit2 && (
                  <View className={c2xStyles.pickerUnitWrap}>
                    <View
                      className={c2xStyles.pickerBKLine}
                      style={{ top: 1 }}
                    />

                    <BbkComponentText style={font.subHeadStyle}>
                      {unit2}
                    </BbkComponentText>
                    <View
                      className={c2xStyles.pickerBKLine}
                      style={{ bottom: 0 }}
                    />
                  </View>
                )}
              </View>
              <View style={xMergeStyles([styles.pickerLine, { top: 139 }])} />
            </>
          )}
        </View>
      </View>
    </BbkComponentModal>
  );
};
export default memo(CtripIOSPicker, compareProps);
const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: '#fff',
    width,
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
  },
  headerWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderColor: color.grayBorder,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  pickerStyle: { width: 90 },
  flex1: { flex: 1 },
  pickerLine: {
    position: 'absolute',
    left: 0,
    width,
    height: StyleSheet.hairlineWidth,
    backgroundColor: color.guideStepSequenceColor,
    zIndex: 10,
  },
});
