import Platform from '@c2x/apis/Platform';

import { BbkUtils } from '../../../Utils';
import CtripIOSPicker from './src/CtripIOSPicker';
import CtripAndroidPicker from './src/CtripAndroidPicker';

const { selector } = BbkUtils;
// @ts-ignore
const isAndroid = Platform.OS === 'android' || Platform.OS === 'harmony';

const CtripPicker = selector(isAndroid, CtripAndroidPicker, CtripIOSPicker);

export { CtripPicker };
