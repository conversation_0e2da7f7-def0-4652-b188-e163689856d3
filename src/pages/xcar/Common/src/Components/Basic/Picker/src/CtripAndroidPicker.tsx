import React, { useEffect, memo } from 'react'; // @ts-ignore
import { Picker } from '@c2x/components/Picker';
import BbkComponentModal, {
  BbkComponentModalAnimationPreset,
} from '../../Modal';
import { BbkUtils } from '../../../../Utils';
import { ICtripIOSPicker } from './type';

const { compareProps } = BbkUtils;

const CtripAndroidPicker: React.FC<ICtripIOSPicker> = ({
  title,
  selected,
  list,
  confirmCallback,
  visible,
  cancelCallback = () => {},
}) => {
  const animations = BbkComponentModalAnimationPreset('bottom');
  useEffect(() => {
    if (visible) {
      Picker.init({
        pickerData: list,
        selectedValue: selected,
        pickerConfirmBtnText: '确定',
        pickerCancelBtnText: ' 取消 ',
        pickerTitleText: title,
        pickerConfirmBtnColor: [0, 134, 246, 1],
        pickerCancelBtnColor: [0, 134, 246, 1],
        pickerToolBarBg: [255, 255, 255, 1],
        pickerBg: [255, 255, 255, 1],
        pickerRowHeight: 50,
        pickerToolBarFontSize: 18,
        pickerFontSize: 18,
        wheelFlex: [1, 1],
        onPickerConfirm: data => {
          confirmCallback(data);
        },
        onPickerCancel: cancelCallback,
      });
      setTimeout(() => {
        Picker.show();
      }, 100);
    } else {
      // @zyr TODO harmony报错 先注释
      // Picker.hide();
    }
  }, [cancelCallback, confirmCallback, list, selected, title, visible]);
  return (
    <BbkComponentModal
      useModal={true}
      modalVisible={visible}
      onRequestClose={cancelCallback}
      // 解决蒙层覆盖年龄选择组件的场景，弹层出现时间要早于Picker打开时间，不然会被覆盖在下层
      animationInDuration={50}
      {...animations}
    />
  );
};
export default memo(CtripAndroidPicker, compareProps);
