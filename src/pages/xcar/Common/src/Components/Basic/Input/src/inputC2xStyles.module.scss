@import '../../../../Tokens/tokens/color.scss';

.wrapper {
  margin-top: 28px;
  position: relative;
}
.clearIcon {
  font-size: 32px;
  margin-left: 5px;
  margin-right: 5px;
  margin-top: 6px;
  margin-bottom: 6px;
}
.noEditableTitle {
  flex-direction: row;
  align-items: center;
}
.labelWrap {
  margin-left: 8px;
  height: 32px;
  padding-left: 12px;
  padding-right: 12px;
  border-width: 1px;
  border-color: rgba($blueBase, 0.5);
  border-radius: 4px;
  background-color: $storeDetailReviewBg;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.labelText {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $sesamePrimary;
  margin-top: -2px;
}
.subValue {
  margin-top: 8px;
}
.tipsIcon {
  font-size: 24px;
  margin-top: 6px;
  color: #111111;
}
.titleWrapper {
  overflow: hidden;
}
.titleAninmation {
  top: 50px;
  opacity: 1;
}
