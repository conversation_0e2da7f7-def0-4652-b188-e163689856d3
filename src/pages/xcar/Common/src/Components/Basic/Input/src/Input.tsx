/* eslint-disable @typescript-eslint/no-use-before-define */
import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  RefObject,
  CSSProperties,
} from 'react';
import {
  xMergeStyles,
  XView as View,
  XAnimated,
  xCreateAnimation,
  xDOMUtils,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import TextInput, { TextInputProps } from '@c2x/components/TextInput';
import Keyboard from '@c2x/apis/Keyboard';
import Platform from '@c2x/apis/Platform';

import c2xStyles from './inputC2xStyles.module.scss';
import { BbkUtils } from '../../../../Utils';
import { layout, font, color, icon, druation } from '../../../../Tokens';
import { InputFormatType } from '../../../../Logic/src/Passenger/PassengerType';
import Touchable from '../../Touchable/src';
import Text from '../../Text';
import { formatCNPhone, formatCNIdNumber } from './Utils';

const { getPixel, selector, ensureFunctionCall } = BbkUtils;
const animationTime = druation.animationDurationS;
const keyboardTypeList = {
  phone: 'ctrip-number',
  id: 'ctrip-identity',
  otherid: 'ctrip-number-and-capital', // 护照 回乡证 台胞证
  email: 'email-address',
  flight: 'ctrip-number-and-capital',
  int: 'ctrip-number',
  decimal: 'ctrip-decimal',
  default: 'default',
};

const idType = [InputFormatType.id, InputFormatType.otherid];
const phoneType = [InputFormatType.phone];

interface BbkInput extends TextInputProps {
  value: string;
  label?: string;
  error?: boolean;
  title?: string;
  titleIcon?: any;
  subValue?: string;
  placeholder?: string;
  placeholderTextStyle?: CSSProperties;
  editable?: boolean;
  isArrowRight?: boolean;
  errorTip?: string;
  style?: CSSProperties;
  nameStyle?: CSSProperties;
  inputStyle?: CSSProperties;
  inputWrapperStyle?: CSSProperties;
  infoTipStyle?: CSSProperties;
  tipStyle?: CSSProperties;
  leftChildren?: React.ReactElement;
  rightChildren?: React.ReactElement;
  onInputBlur?: (text: string) => void;
  onInputFocus?: (text: string) => void;
  onPressInfo?: () => void;
  onPress?: () => void;
  infoTip?: string;
  association?: boolean; // 是否显示联想组件
  isSpecialAnimation?: boolean; // 是否使用上滑缩小动画
  isValueSpit?: boolean; // 是否分割位数
  showClearWhileEditing?: boolean; // 是否展示一键清楚按钮
  valueStyle?: CSSProperties;
  formatType?: InputFormatType;
  noEditableValueRight?: React.ReactElement;
  maxLength?: number;
  valueNumberOfLines?: number;
  removeLineHeight?: boolean; // 是否移除textInput输入框的lineHeight属性，因为加了这个属性会导致XXX XXXXXXXXXXXXXXX这种情况换行，显示部分空白
  controlAnimation?: boolean;
  inputRefHandler?: (ref: RefObject<any>) => void;
  isShowTitle?: boolean;
  spaceLength?: number;
  hideAnimated?: boolean; // 是否隐藏提示标题动画
  transitionLeft?: number;
  textStyle?: CSSProperties;
  defaultPlaceholder?: string;
}
const BbkTextInput: React.FC<BbkInput> = ({
  title,
  titleIcon,
  value,
  label,
  subValue,
  error: errorProp,
  errorTip,
  infoTip,
  leftChildren,
  rightChildren,
  style,
  nameStyle,
  inputStyle,
  inputWrapperStyle,
  infoTipStyle,
  tipStyle,
  editable = true,
  isArrowRight = true,
  onPress,
  placeholder = '',
  placeholderTextStyle,
  association,
  onChangeText,
  onInputBlur,
  onInputFocus,
  onPressInfo,
  valueStyle,
  isSpecialAnimation = false,
  formatType = InputFormatType.default,
  transitionLeft = 0,
  noEditableValueRight,
  isValueSpit,
  showClearWhileEditing = true,
  maxLength,
  valueNumberOfLines,
  removeLineHeight,
  controlAnimation = false,
  inputRefHandler,
  isShowTitle = true,
  spaceLength = 2,
  textStyle,
  hideAnimated,
  defaultPlaceholder,
  testID,
  ...passthroughProps
}) => {
  const Wrapper = selector(onPress, Touchable, View);
  const [active, setActive] = useState(false);
  const [animated, setAnimated] = useState(!controlAnimation);
  const [error, setError] = useState(false);
  const [isRendered, setIsRendered] = useState(false);
  const [showTitle, setShowTitle] = useState(false);
  const animateRef = useRef(null);
  const [animationData, setAnimationData] = React.useState<any>(null);

  useEffect(() => {
    setError(errorProp);
  }, [errorProp]);
  useEffect(() => {
    setIsRendered(true);
  }, []);
  const onBlur = useCallback(() => {
    setActive(false);
    if (controlAnimation) {
      setAnimated(!controlAnimation);
    }
    let valueNow = value;
    if (isValueSpit && !!valueNow) {
      if (idType.includes(formatType)) {
        valueNow = formatCNIdNumber(valueNow);
      } else if (phoneType.includes(formatType)) {
        valueNow = formatCNPhone(valueNow);
      }
    }

    ensureFunctionCall(onChangeText, null, valueNow);
    ensureFunctionCall(onInputBlur);
  }, [value, formatType]);

  const onFocus = useCallback(() => {
    setActive(true);
    if (controlAnimation) {
      setAnimated(controlAnimation);
    }
    ensureFunctionCall(onInputFocus);
  }, []);

  const handleOnChangeText = useCallback(
    text => {
      if (maxLength > 0) {
        if (text.match(/\s+/)) {
          text = text.substr(0, maxLength + spaceLength);
        } else {
          text = text.substr(0, maxLength);
        }
      }
      ensureFunctionCall(onChangeText, null, text);
    },
    [formatType, onChangeText, value],
  );

  useEffect(() => {
    if ((!editable && value) || value || active) {
      setShowTitle(true);
    } else {
      setShowTitle(false);
    }
  }, [active, value, editable]);

  const animation = React.useMemo(() => {
    return xCreateAnimation({
      duration: animationTime,
      delay: 0,
      timingFunction: 'ease',
    });
  }, []);

  useEffect(() => {
    const left = showTitle ? 0 : transitionLeft;
    if (!animateRef || !animateRef.current || hideAnimated) {
      return;
    }
    if (showTitle) {
      if (isSpecialAnimation) {
        animation.left(left).top(0).step();
        setAnimationData(animation?.export());
        return;
      }
      animation.opacity(1).translateY(0).step();
      setAnimationData(animation?.export());
      return;
    }
    if (isSpecialAnimation) {
      animation.left(left).top(getPixel(50)).step();
      setAnimationData(animation?.export());
      return;
    }
    xDOMUtils.getBoundingClientRect({ node: animateRef.current }).then(res => {
      animation
        .opacity(1)
        .translateY(res.height || 0)
        .step();
      setAnimationData(animation?.export());
    });
  }, [showTitle, animation, transitionLeft]);
  const InfoWrapper = onPressInfo ? Touchable : View;

  const fontTitleColor =
    isSpecialAnimation && !showTitle ? color.fontSubLight : color.fontSubDark;

  const titleStyle = xMergeStyles([
    font.body3LightStyle,
    {
      color: fontTitleColor,
    },
    nameStyle,
  ]);

  // 上滑缩小动画样式
  const specialTitleAnimation = editable
    ? styles.titleAninmation
    : styles.editableTitleAninmation;
  return (
    <View className={c2xStyles.wrapper} style={style} testID={testID}>
      {isRendered && isShowTitle && (
        <View className={!isSpecialAnimation && c2xStyles.titleWrapper}>
          {animated && (
            <XAnimated.View
              animation={animationData}
              style={xMergeStyles([
                {
                  opacity: controlAnimation ? 1 : 0,
                  flexDirection: 'row',
                },
                isSpecialAnimation && specialTitleAnimation,
              ])}
              ref={animateRef}
            >
              {!!title && <Text style={titleStyle}>{title}</Text>}
              {!!titleIcon && (
                <Text
                  type="icon"
                  style={xMergeStyles([titleStyle, styles.titleIcon])}
                >
                  {titleIcon}
                </Text>
              )}
            </XAnimated.View>
          )}
          {!animated && !!value && (
            <View className={isSpecialAnimation && c2xStyles.titleAninmation}>
              {!!title && <Text style={titleStyle}>{title}</Text>}
              {!!titleIcon && (
                <Text
                  type="icon"
                  style={xMergeStyles([titleStyle, styles.titleIcon])}
                >
                  {titleIcon}
                </Text>
              )}
            </View>
          )}
        </View>
      )}
      <Wrapper
        style={xMergeStyles([
          styles.inputWrapper,
          {
            borderColor: active
              ? color.blueBase
              : error
                ? color.redBase
                : color.fontSubLight,
          },
          inputWrapperStyle,
        ])}
        onPress={onPress}
      >
        {leftChildren}
        {editable ? (
          <View style={xMergeStyles([layout.flexRow, layout.flex1])}>
            <TextInput
              style={xMergeStyles([
                removeLineHeight ? styles.input2 : styles.input,
                inputStyle,
                { color: color.fontPrimary },
                textStyle,
              ])}
              numberOfLines={1}
              multiline={false}
              blurOnSubmit={true}
              // @ts-ignore
              keyboardType={keyboardTypeList[formatType]}
              clearButtonMode="never"
              onSubmitEditing={Keyboard.dismiss}
              underlineColorAndroid="transparent"
              placeholderTextColor={color.fontSubLight}
              value={value}
              onBlur={onBlur}
              onFocus={onFocus}
              editable={editable}
              onChangeText={handleOnChangeText}
              placeholder={selector(
                active,
                placeholder || title,
                selector(isSpecialAnimation, '', defaultPlaceholder || title),
              )}
              ref={inputRefHandler}
              maxLength={maxLength}
              {...passthroughProps}
            />

            {!!value && active && showClearWhileEditing && (
              <Touchable
                onPress={() => ensureFunctionCall(onChangeText, null, '')}
              >
                <Text
                  type="icon"
                  className={c2xStyles.clearIcon}
                  style={{ color: color.fontSubDark }}
                >
                  {icon.circleCrossFilled}
                </Text>
              </Touchable>
            )}
          </View>
        ) : (
          <View
            style={xMergeStyles([
              layout.betweenHorizontal,
              styles.noEditable,
              styles.input,
            ])}
          >
            <View>
              {selector(
                value,
                <View className={c2xStyles.noEditableTitle}>
                  {valueNumberOfLines ? (
                    <Text
                      numberOfLines={valueNumberOfLines}
                      style={xMergeStyles([font.title2LightStyle, valueStyle])}
                    >
                      {value}
                    </Text>
                  ) : (
                    <>
                      <Text
                        style={xMergeStyles([
                          font.title2LightStyle,
                          valueStyle,
                        ])}
                      >
                        {value}
                      </Text>
                      {!!label && (
                        <View className={c2xStyles.labelWrap}>
                          <Text className={c2xStyles.labelText}>{label}</Text>
                        </View>
                      )}
                    </>
                  )}
                  {noEditableValueRight}
                </View>,
                <Text
                  style={xMergeStyles([
                    font.title2LightStyle,
                    { color: color.fontSubLight },
                    placeholderTextStyle,
                  ])}
                >
                  {selector(
                    isSpecialAnimation,
                    placeholder,
                    `${title} ${placeholder}`,
                  )}
                </Text>,
              )}
              {!!subValue && (
                <Text className={c2xStyles.subValue}>{subValue}</Text>
              )}
            </View>
            {rightChildren}
          </View>
        )}
        {!editable && isArrowRight && (
          <Text
            type="icon"
            style={{ fontSize: getPixel(28), color: color.fontSubLight }}
          >
            {icon.arrowRight}
          </Text>
        )}
        {!!editable && rightChildren}
      </Wrapper>
      {selector(
        error && !active,
        <View style={xMergeStyles([layout.betweenStart, styles.tips])}>
          <Text
            style={xMergeStyles([
              layout.flex1,
              { color: color.redBase },
              tipStyle,
            ])}
          >
            {`${errorTip}`}
          </Text>
          <Text
            type="icon"
            className={c2xStyles.tipsIcon}
            style={{ color: color.redBase }}
          >
            {icon.circleWithSigh}
          </Text>
        </View>,
        null,
      )}
      {selector(
        infoTip,
        <InfoWrapper
          style={xMergeStyles([layout.rowStart, styles.tips])}
          onPress={onPressInfo}
        >
          <Text
            style={xMergeStyles([
              { color: onPressInfo ? color.blueBase : color.fontSubDark },
              infoTipStyle,
            ])}
          >
            {`${infoTip}`}
          </Text>
          {!!onPressInfo && (
            <Text
              type="icon"
              className={c2xStyles.tipsIcon}
              style={{ color: color.blueBase }}
            >
              {icon.arrowRight}
            </Text>
          )}
        </InfoWrapper>,
        null,
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  titleAninmation: { top: getPixel(50), opacity: 1 },
  editableTitleAninmation: { opacity: 1 },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    marginTop: getPixel(8),
    paddingBottom: getPixel(16),
  },
  tips: { marginTop: getPixel(8) },
  noEditable: { flex: 1 },
  input: {
    padding: 0,
    flex: 1,
    textAlignVertical: 'center',
    ...font.title2LightStyle,
    ...Platform.select({
      ios: { lineHeight: 0 },
      android: {}, // @ts-ignore
      harmony: {},
      web: {},
    }),
  },
  input2: {
    fontSize: BbkUtils.getPixel(32),
    padding: 0,
    flex: 1,
    textAlignVertical: 'center',
  },
  titleIcon: {
    marginLeft: getPixel(5),
  },
});

export default BbkTextInput;
