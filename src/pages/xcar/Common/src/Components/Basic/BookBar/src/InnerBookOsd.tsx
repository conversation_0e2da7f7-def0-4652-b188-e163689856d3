import {
  xClassNames as classNames,
  xMergeStyles,
  XView as View,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';
import c2xStyles from './innerBookOsdC2xStyles.module.scss';

import Formatter from '../../CurrencyFormatter';
import Text from '../../Text';
import { BbkUtils } from '../../../../Utils';
import { color, font, layout, icon } from '../../../../Tokens';

const { getPixel, useMemoizedFn, isIos } = BbkUtils;

// 1: normal text, 2: discount text
export enum IPriceTextType {
  Normal = 1,
  Discount = 2,
}

type IBasePrice = {
  labelText: string;
  displayPrice: number;
  displayCurrency: string;
  currentPrice?: number;
  currentCurrency?: string;
  notices?: string[];
};

type IPriceText = string | IBasePrice;

type IBasePriceText = {
  text: string | IPriceText[];
  type: IPriceTextType;
};

type IBookBarProps = {
  style?: CSSProperties;
  totalPrice: IBasePrice;
  priceItems: IBasePrice[];
  priceTexts?: IBasePriceText[];
  noExtraInfo?: boolean;
  noticeTexts?: IBasePriceText[];
  osdPriceDetailVisible?: boolean;
};

type IPriceItemProps = { price: IBasePrice };
const styles = StyleSheet.create({
  priceLabel: {
    ...layout.flexRow,
    borderColor: color.orangePrice,
    borderWidth: StyleSheet.hairlineWidth,
    borderRadius: getPixel(4),
    paddingLeft: getPixel(6),
    paddingRight: getPixel(6),
    height: getPixel(32),
  },
  priceWrapperStyle: { paddingTop: isIos ? 0 : getPixel(3) },
  baseText: { color: color.blueGrayBase, ...font.caption1LightStyle },
  textPriceNew: { color: color.C_555555 },
  priceItem: { marginRight: getPixel(8), alignItems: 'flex-start' },
  priceStyleNew: {
    ...font.F_36_10_regular_TripNumberBold,
    color: color.C_006FF6,
    paddingBottom: getPixel(isIos ? 2 : 0),
  },
  currencyNew: {
    ...font.F_24_10_bold,
    paddingTop: getPixel(isIos ? 10 : 9),
    color: color.C_006FF6,
  },
});

const getSharkPrice = (price: IBasePrice) => {
  if (
    price.currentCurrency &&
    price.currentCurrency !== price.displayCurrency
  ) {
    return <Text className={c2xStyles.priceAbout}>约&nbsp;</Text>;
  }
  return null;
};

const PriceItem = (props: IPriceItemProps) => {
  const { price } = props;
  const { notices = [] } = price;
  const showSharkPrice =
    (price.currentCurrency &&
      price.currentCurrency !== price.displayCurrency) ||
    notices.length > 0;
  const grayStyle = xMergeStyles([styles.baseText, styles.textPriceNew]);

  return (
    <View style={xMergeStyles([layout.flexRowWrap, layout.flex1])}>
      <Formatter
        currency={price.displayCurrency}
        price={price.displayPrice}
        currencyStyle={xMergeStyles([styles.baseText, styles.textPriceNew])}
        priceStyle={xMergeStyles([styles.baseText, styles.textPriceNew])}
      />

      {showSharkPrice && (
        <View style={layout.flexRow}>
          <Text style={grayStyle}> (约</Text>
          <Formatter
            currency={price.currentCurrency}
            price={price.currentPrice}
            currencyStyle={grayStyle}
            priceStyle={grayStyle}
          />

          {notices.length > 0 && (
            <Text style={grayStyle}>{notices.join(',')}</Text>
          )}
          <Text style={grayStyle}>)</Text>
        </View>
      )}
    </View>
  );
};

const getPriceText = (x, i) => {
  const textStyle = React.useMemo(() => {
    let colorStyle = color.fontSubDark;
    if (x.type === 1) {
      colorStyle = color.C_888888;
      return xMergeStyles([styles.baseText, { color: colorStyle }]);
    }
    if (x.type === 2) {
      colorStyle = color.orangePrice;
      return xMergeStyles([font.F_20_10_regular, { color: colorStyle }]);
    }
    return xMergeStyles([styles.baseText, { color: colorStyle }]);
  }, []);

  const renderText = useMemoizedFn(text => {
    return <Text style={textStyle}>{text}</Text>;
  });

  if (typeof x.text === 'string') {
    return renderText(x.text);
  }
  return (
    <View style={layout.flexRow}>
      {x.text.map((t, j) =>
        typeof t === 'string' ? (
          renderText(t)
        ) : (
          <Formatter
            key={`${i}-${j}`}
            currency={t.displayCurrency}
            price={t.displayPrice}
            currencyStyle={textStyle}
            priceStyle={textStyle}
            wrapperStyle={styles.priceWrapperStyle}
          />
        ),
      )}
    </View>
  );
};

export const InnerBook: React.FC<IBookBarProps> = props => {
  const {
    totalPrice,
    priceItems = [],
    priceTexts = [],
    noExtraInfo,
    noticeTexts = [],
    osdPriceDetailVisible,
  } = props;
  const detailIcon = (
    <View className={c2xStyles.detail}>
      <Text className={c2xStyles.detailText}>明细</Text>
      <Text type="icon" className={c2xStyles.detailIcon}>
      {!!osdPriceDetailVisible ? icon.arrowDown : icon.arrowUp}
      </Text>
    </View>
  );

  const totalPriceFormatter = (
    <Formatter
      currency={totalPrice.displayCurrency}
      price={totalPrice.displayPrice}
      currencyStyle={styles.currencyNew}
      priceStyle={styles.priceStyleNew}
      isNew={true}
    />
  );

  return (
    <View style={layout.flexRow}>
      <View style={layout.flex1}>
        <View style={layout.flexRowWrap}>
          <Text className={c2xStyles.textTitle} fontWeight="medium">
            {totalPrice.labelText}
          </Text>
          <View className={c2xStyles.totalWrapper}>
            {getSharkPrice(totalPrice)}
            {totalPriceFormatter}
          </View>
          {!!priceTexts?.length && (
            <View style={styles.priceLabel}>
              {priceTexts.map((x, i) => getPriceText(x, i))}
            </View>
          )}
        </View>
        <View style={layout.flexRowWrap}>
          {priceItems.map((price, i) => (
            <View
              key={i}
              style={xMergeStyles([layout.flexRow, styles.priceItem])}
            >
              <Text
                className={classNames(
                  c2xStyles.baseText,
                  c2xStyles.textGrayNew,
                )}
                style={{ marginRight: getPixel(8) }}
              >
                {price.labelText}
              </Text>
              <PriceItem price={price} />
            </View>
          ))}
        </View>
        <View style={layout.flexRowWrap}>
          {noticeTexts.map((x, i) => getPriceText(x, i))}
        </View>
      </View>
      {!noExtraInfo && detailIcon}
    </View>
  );
};
export default InnerBook;
