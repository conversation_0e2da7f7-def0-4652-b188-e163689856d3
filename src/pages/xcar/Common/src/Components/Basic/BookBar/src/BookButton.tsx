import Image from '@c2x/components/Image';
import {
  xClassNames as classNames,
  xMergeStyles,
  XView as View,
  XLinearGradient as LinearGradient,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';

import Touchable from '../../Touchable/src';
import c2xStyles from './bookButtonC2xStyles.module.scss';
import Text from '../../Text';
import { BbkUtils } from '../../../../Utils';
import { color, font, layout, fontCommon, button } from '../../../../Tokens';
import { ButtonType } from '../../../../Tokens/tokens/tokenType';

const { getPixel } = BbkUtils;
export const DIMG04_PATH = 'https://dimg04.c-ctrip.com/images/';
const sprayBlueCorner = `${DIMG04_PATH}1tg4k12000cxv1rii421D.png`;

const sprayGrayCorner = `${DIMG04_PATH}1tg0112000d0mx9on8C5F.png`;

export enum IButtonColorType {
  orange = 0,
  blue = 1,
}
const styles = StyleSheet.create({
  buttonWrapper: { paddingRight: getPixel(32) },
  button: {
    ...button.buttonLStyle,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: getPixel(220),
    backgroundColor: color.orangeBase,
    paddingTop: getPixel(13),
    paddingBottom: getPixel(10),
  },
  sprayButton: {
    ...button.buttonLStyle,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: getPixel(220),
    backgroundColor: color.deepBlueBase,
    paddingTop: getPixel(13),
    paddingBottom: getPixel(10),
    ...button.buttonSprayStyle,
  },
  disableCtripButton: {
    backgroundColor: color.driverRadioBorder,
    color: color.fontSubDark,
  },
});

interface IBookButton {
  disabled: boolean;
  name: string;
  subName?: string;
  onPress?: () => void;
  buttonStyle?: CSSProperties;
  buttonWrapStyle?: CSSProperties;
  isShowDisableStyle?: boolean;
  buttonColorType?: IButtonColorType;
  testID?: string;
  buttonTextStyle?: CSSProperties;
  buttonGradientColors?: Array<string>;
  isShowFreeCancelLabel?: boolean;
  buttonType?: ButtonType;
  debounceTime?: number;
  isISDShelves2B?: boolean;
  virtualNumberEntry?: any;
}

const gradientColorArr = [
  color.linearGradientOrangeLight,
  color.linearGradientOrangeDark,
];

const gradientColorArrBlue = [
  color.linearGradientBlueLight,
  color.linearGradientBlueDark,
];

const gradientColorArrSpray = [color.deepBlueBase, color.deepBlueBase];

export const BookButtonCtrip: React.FC<IBookButton> = ({
  disabled,
  onPress,
  name,
  subName,
  buttonStyle,
  isShowDisableStyle,
  buttonWrapStyle,
  buttonColorType,
  testID,
  buttonTextStyle,
  buttonGradientColors,
  buttonType,
  debounceTime = 2000,
  isISDShelves2B,
  virtualNumberEntry,
}) => {
  const isGradient = !disabled || !isShowDisableStyle;
  const isSpray = buttonType === ButtonType.Spray;
  const fixButtonStyle = xMergeStyles([
    isSpray ? styles.sprayButton : styles.button,
    buttonStyle,
    disabled && isShowDisableStyle && styles.disableCtripButton,
  ]);
  const children = (
    <>
      <Text
        className={c2xStyles.ctripButtonText}
        style={xMergeStyles([
          buttonTextStyle,
          disabled && isShowDisableStyle && styles.disableCtripButton,
        ])}
        fontWeight="medium"
      >
        {name}
      </Text>
      {!!subName && (
        <Text
          className={classNames(
            c2xStyles.ctripButtonText,
            c2xStyles.subButtonText,
          )}
          style={disabled && isShowDisableStyle && styles.disableCtripButton}
        >
          {subName}
        </Text>
      )}
    </>
  );

  const defaultGradientColorArr =
    buttonColorType === IButtonColorType.blue
      ? gradientColorArrBlue
      : gradientColorArr;
  const buttonSprayImage = disabled ? sprayGrayCorner : sprayBlueCorner;
  return (
    <Touchable
      disabled={disabled}
      debounce={true}
      debounceTime={debounceTime}
      onPress={onPress}
      testID={testID}
      style={xMergeStyles([
        layout.flexRow,
        styles.buttonWrapper,
        buttonWrapStyle,
      ])}
    >
      {isGradient ? (
        isISDShelves2B ? (
          <View className={c2xStyles.buttonWrap}>
            {
              virtualNumberEntry
            }
            <LinearGradient
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 1.0, y: 1.0 }}
              locations={[0, 1]}
              colors={buttonGradientColors || gradientColorArrSpray}
              style={fixButtonStyle}
            >
              {children}
              {isSpray && (
                <Image
                  src={buttonSprayImage}
                  className={c2xStyles.sprayCornerStyle}
                />
              )}
            </LinearGradient>
          </View>
        ) : (
          <LinearGradient
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 1.0 }}
            locations={[0, 1]}
            colors={buttonGradientColors || gradientColorArrSpray}
            style={fixButtonStyle}
          >
            {children}
            {isSpray && (
              <Image
                src={buttonSprayImage}
                className={c2xStyles.sprayCornerStyle}
              />
            )}
          </LinearGradient>
        )
      ) : (
        <View style={fixButtonStyle}>
          {children}
          {isSpray && (
            <Image
              src={buttonSprayImage}
              className={c2xStyles.sprayCornerStyle}
            />
          )}
        </View>
      )}
    </Touchable>
  );
};
