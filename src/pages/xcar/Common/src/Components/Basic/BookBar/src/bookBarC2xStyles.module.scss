@import '../../../../Tokens/tokens/color.scss';

.bookEncourageIcon {
  font-size: 26px;
  text-align: center;
  color: $orangeBase;
  padding-top: 2px;
}
.bookEncourageText {
  margin-left: 12px;
  color: $orangeBase;
}
.bookPaymodesNew {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  padding-left: 24px;
  padding-right: 24px;
  padding-top: 16px;
  padding-bottom: 16px;
  background-color: $bookbarTipsOrangeBg;
}
.bookPaymodes {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 16px;
  padding-bottom: 16px;
  background-color: $bookbarTipsOrangeBg;
}
.paymentsTipNew {
  margin-right: 8px;
  color: $C_555555;
}
.paymentsTip {
  margin-right: 8px;
  color: $C_888888;
}
.payModesTextNew {
  color: $C_4673B2;
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.payModesText {
  color: $C_006FF6;
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.bookTipsText {
  color: $bookbarTipsGreenText;
  flex-shrink: 1;
}
.bookBarWrapper {
  flex-direction: row;
}
