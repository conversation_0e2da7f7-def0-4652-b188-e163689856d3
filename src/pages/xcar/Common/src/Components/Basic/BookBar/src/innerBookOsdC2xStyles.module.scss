@import '../../../../Tokens/tokens/color.scss';

.priceAbout {
  padding-top: 2px;
  color: $C_888888;
}
.detail {
  margin-right: 20px;
  margin-left: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.detailText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_006FF6;
}
.detailIcon {
  color: $C_006FF6;
  font-size: 26px;
}
.textTitle {
  margin-right: 8px;
  font-size: 28px;
  line-height: 44px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.totalWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 8px;
}
.baseText {
  color: $blueGrayBase;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.textGrayNew {
  color: $C_888888;
}
