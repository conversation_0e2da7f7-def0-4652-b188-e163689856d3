import { uniqBy as lodashUniqBy } from 'lodash-es';
import {
  xMergeStyles,
  XView as View,
  XBoxShadow,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';

import c2xStyles from './bookBarC2xStyles.module.scss';
import Loading from '../../Loading';
import Text from '../../Text';
import Touchable from '../../Touchable/src';
import { BbkUtils } from '../../../../Utils';
import { color, layout, font, icon } from '../../../../Tokens';
import { ButtonType } from '../../../../Tokens/tokens/tokenType';
import BbkLabel from '../../Label';
import { BookButtonCtrip, IButtonColorType } from './BookButton';
import { texts } from './Texts';
import UITestId from '../../UITestID';
import c2xCommonStyles from '../../../../Tokens/tokens/c2xCommon.module.scss';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';

const { getPixel, fixIOSOffsetBottom, adaptNoaNomalousBottom } = BbkUtils;
interface IPayMode {
  payMode: 1 | 2 | 3;
  payModeName: string;
}
const styles = StyleSheet.create({
  boxShadowWrap: {
    paddingBottom: fixIOSOffsetBottom() + adaptNoaNomalousBottom(),
    backgroundColor: color.bookbarBgColor,
  },
  bookInfo: {
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    paddingLeft: getPixel(32),
    flexDirection: 'row',
    minHeight: getPixel(120),
    alignItems: 'center',
  },
  bookTips: {
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    backgroundColor: color.bookbarTipsGreenBg,
  },
  bookEncourage: {
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    backgroundColor: color.orangeBg,
  },
  payModeLabel: {
    marginRight: getPixel(8),
    paddingLeft: getPixel(8),
    paddingRight: getPixel(8),
  },
});

interface IBookBar {
  disabled: boolean;
  buttonName: string;
  buttonSubName?: string;
  isLoading?: boolean;
  onPressBtn?: () => void;
  onPressBar?: () => void;
  payModes?: IPayMode[];
  goodsShelves?: any;
  tipText?: string;
  tipIcon?: string;
  style?: CSSProperties;
  innerStyle?: CSSProperties;
  buttonStyle?: CSSProperties;
  renderInner?: React.ReactNode;
  isShowDisableStyle?: boolean;
  paymentTips?: string;
  forceShowPayMode?: boolean;
  labelStyle?: CSSProperties;
  textStyle?: CSSProperties;
  renderTip?: React.ReactNode;
  renderCheckInfobar?: React.ReactNode;
  buttonColorType?: IButtonColorType;
  renderRightText?: React.ReactNode;
  buttonTextStyle?: CSSProperties;
  buttonGradientColors?: Array<string>;
  isShowFreeCancelLabel?: boolean;
  isOSDInterestPoints?: boolean;
  buttonType?: ButtonType;
  onPressNextPayModes?: () => void;
  payModeBarStyles?: CSSProperties;
  payModesText?: string;
  visible?: boolean;
  debounceTime?: number;
  buttonTestID?: string;
  payModeTestID?: string;
  barTestID?: string;
  isISDShelves2B?: boolean;
  virtualNumberEntry?: any;
  setBookBarHeight?: (height: number) => void;
}
const payModeLabelMap = {
  // 到店付
  1: [color.labelOrangeText, color.labelOrangeBg],
  // 现在付
  2: [color.labelBlueText, color.labelBlueBg],
  // 预付订金
  3: [color.labelGreenText, color.labelGreenBg],
  // 在线预授权
  4: [color.labelGreenText, color.labelGreenBg],
};

const getPayModeLabel = payMode =>
  payModeLabelMap[payMode] || payModeLabelMap[3];

const BookBar: React.FC<IBookBar> = ({
  isLoading,
  disabled,
  buttonName,
  buttonSubName,
  onPressBtn,
  innerStyle = layout.flexRow,
  style,
  renderInner,
  onPressBar,
  payModes = [],
  goodsShelves,
  tipText,
  tipIcon,
  buttonStyle,
  isShowDisableStyle = true,
  paymentTips,
  forceShowPayMode,
  renderTip,
  buttonColorType = 1,
  labelStyle,
  textStyle,
  renderRightText,
  buttonTextStyle,
  buttonGradientColors,
  isShowFreeCancelLabel,
  renderCheckInfobar,
  buttonType,
  onPressNextPayModes,
  payModeBarStyles,
  payModesText,
  isOSDInterestPoints,
  visible = true,
  debounceTime,
  buttonTestID,
  payModeTestID,
  barTestID,
  isISDShelves2B,
  virtualNumberEntry,
  setBookBarHeight,
}) => {
  const BookButton = BookButtonCtrip;
  const Wrapper = onPressBar ? Touchable : View;
  const payModesUniqByPayModeName = lodashUniqBy(payModes, 'payModeName');
  const { hint } = goodsShelves || {};
  const bookBarLayout = useMemoizedFn(event => {
    const height = event.nativeEvent.layout.height;
    setBookBarHeight?.(height);
  });

  return (
    <XBoxShadow
      style={styles.boxShadowWrap}
      coordinate={{
        x: 0,
        y: getPixel(-8),
      }}
      color="rgba(0, 0, 0, 0.12)"
      opacity={1}
      blurRadius={getPixel(12)}
      elevation={8}
    >
      {hint ? (
        <View style={xMergeStyles([layout.flexRowWrap, styles.bookEncourage])}>
          <Text type="icon" className={c2xStyles.bookEncourageIcon}>
            {icon.encourageIcon}
          </Text>
          <Text className={c2xStyles.bookEncourageText}>{hint}</Text>
        </View>
      ) : (
        (forceShowPayMode || payModesUniqByPayModeName.length > 1) &&
        visible && (
          <Touchable
            testID={payModeTestID}
            onPress={onPressNextPayModes}
            className={
              isOSDInterestPoints
                ? c2xStyles.bookPaymodesNew
                : c2xStyles.bookPaymodes
            }
            style={payModeBarStyles}
          >
            <Text
              className={classNames(
                c2xCommonStyles.c2xTextDefaultCss,

                isOSDInterestPoints
                  ? c2xStyles.paymentsTipNew
                  : c2xStyles.paymentsTip,
              )}
            >
              {paymentTips || texts.paymentTips}
            </Text>
            <View style={layout.flexRow}>
              {payModesText ? (
                <Text
                  className={classNames(
                    c2xCommonStyles.c2xTextDefaultCss,

                    isOSDInterestPoints
                      ? c2xStyles.payModesTextNew
                      : c2xStyles.payModesText,
                  )}
                >
                  {payModesText}
                </Text>
              ) : (
                payModesUniqByPayModeName.map(({ payMode, payModeName }, i) => (
                  <BbkLabel
                    key={payModeName}
                    text={payModeName}
                    labelStyle={xMergeStyles([
                      styles.payModeLabel,
                      {
                        backgroundColor: getPayModeLabel(payMode)[1],
                      },
                      labelStyle,
                    ])}
                    textStyle={xMergeStyles([
                      {
                        color: getPayModeLabel(payMode)[0],
                        ...font.labelLLightStyle,
                      },
                      textStyle,
                    ])}
                  />
                ))
              )}
            </View>
          </Touchable>
        )
      )}
      {tipText && (
        <View style={xMergeStyles([layout.flexRow, styles.bookTips])}>
          {tipIcon && (
            <Text
              type="icon"
              className={c2xStyles.bookTipsText}
              style={{
                marginRight: getPixel(8),
                flexShrink: 0,
              }}
            >
              {tipIcon}
            </Text>
          )}
          <Text className={c2xStyles.bookTipsText}>{tipText}</Text>
        </View>
      )}
      {renderTip}
      {renderCheckInfobar}
      <View
        className={c2xStyles.bookBarWrapper}
        style={xMergeStyles([
          {
            backgroundColor: color.bookbarBgColor,
          },
          style,
        ])}
        onLayout={event => bookBarLayout(event)}
      >
        <Wrapper
          style={xMergeStyles([layout.flex1, styles.bookInfo, innerStyle])}
          testID={barTestID}
          onPress={onPressBar}
        >
          {isLoading ? (
            // @ts-ignore
            <Loading />
          ) : (
            renderInner
          )}
        </Wrapper>
        {renderRightText || (
          <BookButton
            isISDShelves2B={isISDShelves2B}
            disabled={disabled}
            buttonColorType={buttonColorType}
            isShowDisableStyle={isShowDisableStyle}
            onPress={onPressBtn}
            name={buttonName}
            debounceTime={debounceTime}
            subName={buttonSubName}
            buttonType={buttonType}
            buttonStyle={buttonStyle}
            testID={buttonTestID || UITestId.car_testid_comp_bookbar_button}
            buttonTextStyle={buttonTextStyle}
            buttonGradientColors={buttonGradientColors}
            isShowFreeCancelLabel={isShowFreeCancelLabel}
            virtualNumberEntry={virtualNumberEntry}
          />
        )}
      </View>
    </XBoxShadow>
  );
};
BookBar.defaultProps = {
  renderCheckInfobar: null,
  renderTip: null,
};
export default BookBar;
