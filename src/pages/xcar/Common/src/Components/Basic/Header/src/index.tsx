/* eslint-disable @typescript-eslint/no-use-before-define */
import StyleSheet from '@c2x/apis/StyleSheet';
import Platform from '@c2x/apis/Platform';
import React, { useContext, CSSProperties, memo } from 'react'; // @ts-ignore
import { xMergeStyles, XView as View, XAnimated } from '@ctrip/xtaro';

import BbkComponentText from '../../Text';
import { BbkUtils, BbkConstants } from '../../../../Utils';
import { useWindowSizeChanged } from '../../../../Hooks';
import { setOpacity, color, font, icon } from '../../../../Tokens';
import { withTheme, getThemeAttributes } from '../../../../Theming';
import BbkTouchable from '../../Touchable/src';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import {
  FloatHeaderContext,
  getOpacityIconWrapStyle,
  getOpacityIconStyle,
  getOpacityStyle,
} from './Context';

export * from './Context';

const { selector, getPixel, ensureFunctionCall, fixOffsetTop } = BbkUtils;

const { DEFAULT_HEADER_HEIGHT } = BbkConstants;

export enum TextColorType {
  light = 'Light',
  dark = 'Dark',
}

export type ICHeader = {
  style?: CSSProperties;
  styleInner?: CSSProperties;
  contentStyle?: CSSProperties;
  sideLeftStyle?: CSSProperties;
  leftIcon?: string;
  isLeftIconCross?: boolean;
  leftIconColor?: string;
  leftIconStyle?: CSSProperties;
  title?: string;
  titleColor?: string;
  titleStyle?: CSSProperties;
  subtitle?: string;
  subtitleColor?: string;
  subtitleStyle?: CSSProperties;
  rightIcon?: string;
  rightIconColor?: string;
  rightIconStyle?: CSSProperties;
  isBottomBorder?: boolean;
  renderContent?: () => void;
  renderLeft?: (isHideAnimation?: boolean) => void;
  renderRight?: (isHideAnimation?: boolean) => void;
  onPressLeft?: () => void;
  onPressRight?: () => void;
  renderBottom?: () => void;
  textColorType?: TextColorType;
  backgroundOpacity?: number;
  theme?: any;
  isHideAnimation?: boolean;
  opacityAnimation?: number;
  fixOpacityAnimation?: number;
  containderStyle?: CSSProperties;
  isShowAnimation?: boolean;
  leftIconTestID?: string;
  serviceTestID?: string;
  titleTestID?: string;
  hideHeadFixTop?: boolean;
};

const BbkComponentHeader: React.FC<ICHeader> = ({
  onPressLeft,
  onPressRight,
  renderContent,
  renderBottom,
  renderLeft,
  renderRight,
  style,
  styleInner,
  contentStyle,
  sideLeftStyle,
  title,
  titleColor,
  titleStyle,
  subtitle,
  subtitleColor,
  subtitleStyle,
  leftIcon,
  leftIconColor,
  leftIconStyle,
  rightIcon,
  rightIconColor,
  rightIconStyle,
  isBottomBorder = true,
  isLeftIconCross,
  theme,
  textColorType = TextColorType.dark,
  backgroundOpacity = 1,
  isHideAnimation = false,
  opacityAnimation,
  fixOpacityAnimation,
  containderStyle,
  isShowAnimation = false,
  leftIconTestID,
  serviceTestID,
  titleTestID,
  hideHeadFixTop,
}) => {
  const width100Style = useWindowSizeChanged();
  const titleProps: IContent = {
    style: contentStyle,
    title,
    titleColor:
      titleColor ||
      (textColorType === TextColorType.light && theme.backgroundColor),
    titleStyle,
    subtitle,
    subtitleColor,
    subtitleStyle,
    titleTestID,
  };
  const themes = getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as any;
  const borderColor = (themes && themes.bbkLineColor) || color.grayBorder;

  let backgroundColor;
  if (style && style.backgroundColor) {
    backgroundColor = setOpacity(style.backgroundColor, backgroundOpacity);
  }
  const { opacity } = useContext(FloatHeaderContext);
  const showOpacity = opacity > 0;
  const Container = View;
  const isFuncRenderLeft = typeof renderLeft === 'function';
  const isFuncRenderRight = typeof renderRight === 'function';
  return (
    <View>
      <Container
        style={xMergeStyles([
          styles.wrapper,
          width100Style,
          isBottomBorder && showOpacity && styles.bottomLine,
          { borderColor },
          style,
          {
            backgroundColor,
            opacity: isHideAnimation ? fixOpacityAnimation : 1,
          },
          containderStyle,
          hideHeadFixTop && { paddingTop: 0 },
        ])}
      >
        <View style={xMergeStyles([styles.wrapperInner, styleInner])}>
          {selector(
            renderLeft,
            isFuncRenderLeft ? renderLeft() : renderLeft,
            <SideView
              icon={
                leftIcon ||
                selector(
                  isLeftIconCross,
                  icon.cross,

                  icon.arrowLeft,
                )
              }
              iconColor={
                leftIconColor ||
                (textColorType === TextColorType.light && theme.backgroundColor)
              }
              iconStyle={leftIconStyle}
              style={xMergeStyles([styles.sideLeft, sideLeftStyle])}
              onPress={onPressLeft}
              testID={leftIconTestID}
            />,
          )}
          {selector(renderContent, renderContent, <Content {...titleProps} />)}
          {selector(
            renderRight,
            isFuncRenderRight ? renderRight() : renderRight,
            <SideView
              icon={rightIcon}
              iconColor={
                rightIconColor ||
                (textColorType === TextColorType.light && theme.backgroundColor)
              }
              iconStyle={rightIconStyle}
              style={styles.sideRight}
              onPress={onPressRight}
            />,
          )}
        </View>
        {(!isHideAnimation || isShowAnimation) && <View>{renderBottom}</View>}
      </Container>

      {isHideAnimation && (
        <Container
          style={xMergeStyles([
            styles.wrapper,
            width100Style,
            isBottomBorder && showOpacity && styles.bottomLine,
            { borderColor },
            style,
            {
              backgroundColor,
              opacity: isHideAnimation ? opacityAnimation : 1,
              position: 'absolute',
              top: 0,
              left: 0,
            },
          ])}
        >
          <View style={xMergeStyles([styles.wrapperInner, styleInner])}>
            {selector(
              renderLeft,
              isFuncRenderLeft ? renderLeft(isHideAnimation) : renderLeft,
              <SideView
                icon={
                  leftIcon ||
                  selector(isLeftIconCross, icon.cross, icon.arrowLeft)
                }
                iconColor={
                  leftIconColor ||
                  (textColorType === TextColorType.light &&
                    theme.backgroundColor)
                }
                iconStyle={leftIconStyle}
                style={xMergeStyles([
                  styles.sideLeft,
                  sideLeftStyle,
                  styles.ctripIconBg,
                ])}
                onPress={onPressLeft}
              />,
            )}
            {selector(
              renderRight,
              isFuncRenderRight ? renderRight(isHideAnimation) : renderRight,
              <SideView
                icon={rightIcon}
                iconColor={
                  rightIconColor ||
                  (textColorType === TextColorType.light &&
                    theme.backgroundColor)
                }
                iconStyle={rightIconStyle}
                style={xMergeStyles([styles.sideRight, styles.ctripIconBg])}
                onPress={onPressRight}
              />,
            )}
          </View>
        </Container>
      )}
    </View>
  );
};

type ISideView = {
  icon?: string;
  iconColor?: string;
  iconStyle?: CSSProperties;
  style?: CSSProperties;
  onPress?: () => void;
  testID?: string;
};
const SideView: React.FC<ISideView> = ({
  icon,
  iconColor,
  style,
  iconStyle,
  onPress,
  testID,
}) => {
  if (!icon) return null;
  const Wrapper = selector(onPress, BbkTouchable, View);
  const iconStyles: any = xMergeStyles([styles.iconText, iconStyle]);
  if (iconColor) {
    iconStyles.color = iconColor;
  }

  const { opacity } = useContext(FloatHeaderContext);
  const opacityWrapStyle = getOpacityIconWrapStyle(opacity);
  const opacityStyle = getOpacityIconStyle(opacity);

  return (
    <Wrapper
      testID={testID}
      style={xMergeStyles([styles.sideContainer, style, opacityWrapStyle])}
      onPress={() => {
        ensureFunctionCall(onPress);
      }}
    >
      <BbkComponentText
        type="icon"
        style={xMergeStyles([iconStyles, opacityStyle])}
      >
        {icon}
      </BbkComponentText>
    </Wrapper>
  );
};

type IContent = {
  style?: CSSProperties;
  title?: string;
  titleColor?: string;
  titleStyle?: CSSProperties;
  subtitle?: string;
  subtitleColor?: string;
  subtitleStyle?: CSSProperties;
  titleTestID?: string;
};
const Content: React.FC<IContent> = ({
  style,
  title,
  titleColor,
  titleStyle,
  subtitle,
  subtitleColor,
  subtitleStyle,
  titleTestID,
}) => {
  if (!title) return null;
  const defaultTitleStyle = [
    selector(!subtitle, font.title1BoldStyle),
    { color: color.C_111111 },
  ];

  if (titleColor) {
    defaultTitleStyle.push({ color: titleColor });
  }
  const { opacity } = useContext(FloatHeaderContext);
  return (
    <View
      style={xMergeStyles([styles.content, style, getOpacityStyle(opacity)])}
    >
      <View testID={titleTestID}>
        <BbkComponentText
          numberOfLines={1}
          style={xMergeStyles(xMergeStyles(defaultTitleStyle), titleStyle)}
        >
          {title}
        </BbkComponentText>
      </View>
      {selector(
        subtitle,
        <BbkComponentText
          style={xMergeStyles([
            { color: color.C_111111 },
            { color: subtitleColor },
            subtitleStyle,
          ])}
        >
          {subtitle}
        </BbkComponentText>,
      )}
    </View>
  );
};

const sideTop = Platform.select({
  ios: getPixel(15),
  web: getPixel(15),
  android: getPixel(21),
  // @ts-ignore
  harmony: getPixel(21),
});

const sideWidth = Platform.select({
  ios: getPixel(64),
  web: getPixel(64),
  android: getPixel(64),
  // @ts-ignore
  harmony: getPixel(64),
});

const sideLeft = Platform.select({
  ios: getPixel(32),
  web: getPixel(32),
  android: getPixel(20),
  // @ts-ignore
  harmony: getPixel(20),
});
const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    margin: 0,
    paddingTop: fixOffsetTop(),
  },
  ctripIconBg: {
    borderRadius: getPixel(128),
    backgroundColor: setOpacity(color.black, 0.4),
  },
  bottomLine: {
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  wrapperInner: {
    minHeight: DEFAULT_HEADER_HEIGHT,
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingLeft: getPixel(48 + 32 + 40),
    paddingRight: getPixel(48 + 32 + 40),
    alignItems: 'center',
  },
  iconText: {
    fontSize: getPixel(48),
    color: color.C_111111,
  },
  sideContainer: {
    position: 'absolute',
    top: sideTop,
    bottom: sideTop,
    zIndex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sideRight: {
    width: getPixel(60),
    right: getPixel(32),
  },
  sideLeft: {
    width: sideWidth,
    minHeight: sideWidth,
    left: sideLeft,
  },
});

export default withTheme(BbkComponentHeader);

const Header = withTheme(memo(BbkComponentHeader));
export { Header };
