import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import React, { Component, CSSProperties } from 'react';

import PropTypes from 'prop-types';

import { color } from '../../../../Tokens';
import { withTheme, getThemeAttributes } from '../../../../Theming';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';

export interface IDashedLine {
  lineColor?: string;
  lineWidth?: number;
  isVertical?: boolean;
  style?: CSSProperties;
  theme?: any;
}

class DashedLine extends Component<IDashedLine, { inital: boolean }> {
  itemWidth = 3;

  marginWidth = 2;

  height = 0;

  width = 0;

  static propTypes = {
    lineColor: PropTypes.string,
    lineWidth: PropTypes.number,
    isVertical: PropTypes.bool,
  };

  static defaultProps = {
    lineColor: color.grayBorder,
    lineWidth: 1,
    isVertical: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      inital: false,
    };
    this.itemWidth = props.itemWidth || 3;
    this.marginWidth = props.marginWidth || 2;
  }

  getViewStyle = () => {
    const { lineColor, lineWidth, isVertical, theme } = this.props;
    const themes: any =
      getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) || {};
    const { bbkDashedLineColor = lineColor } = themes;

    let style;
    style = {
      width: lineWidth,
      height: this.itemWidth,
      marginBottom: this.marginWidth,
      backgroundColor: bbkDashedLineColor,
    };
    if (!isVertical) {
      style = {
        height: lineWidth,
        width: this.itemWidth,
        marginRight: this.marginWidth,
        backgroundColor: bbkDashedLineColor,
      };
    }
    return style;
  };

  getLines = () => {
    const viewStyle = this.getViewStyle();
    const { isVertical } = this.props;
    const { height, width } = this;
    const num = isVertical
      ? Math.ceil(height / (this.itemWidth + this.marginWidth))
      : Math.ceil(width / (this.itemWidth + this.marginWidth));
    const dom = [];
    for (let i = 0; i < num; i++) {
      dom.push(<View key={i} style={viewStyle} />);
    }
    return dom;
  };

  onLayout = e => {
    const { height, width } = e.nativeEvent.layout;
    this.height = height;
    this.width = width;
    if (!this.state.inital) {
      this.setState({ inital: true });
    }
  };

  render() {
    const { isVertical, style } = this.props;
    const lines = this.getLines();
    return (
      <View
        onLayout={this.onLayout}
        style={xMergeStyles([
          { flex: 1, flexDirection: isVertical ? 'column' : 'row' },
          style,
        ])}
      >
        {lines}
      </View>
    );
  }
}

export default withTheme(DashedLine);
