import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import React, { CSSProperties } from 'react';
import PropTypes from 'prop-types';
import BbkComponentText from '../../Text';
import { BbkUtils, BbkChannel } from '../../../../Utils';
import { toRMB } from '../../../../Shark/src/Index';
import NumberText from '../../NumberText';

const { getPixel } = BbkUtils;
interface IBUL10nNumberOption {
  usesGroupingSeparator?: boolean;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}

export interface IBUFormatCurrencyTextProps {
  price: number;
  currency: string;
  currencyStyle?: CSSProperties;
  priceStyle?: CSSProperties;
  wrapperStyle?: CSSProperties;
  options?: IBUL10nNumberOption;
  isNew?: boolean;
  testID?: string;
}

export default class BbkCurrencyFormatter extends React.Component<IBUFormatCurrencyTextProps> {
  static propTypes = {
    currency: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired,
    currencyStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
    priceStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
    isNew: PropTypes.bool,
  };

  constructor(props) {
    super(props);
  }

  currencyText = ({
    currency,
    currencyStyle,
    priceStyle,
    priceStr,
    wrapperStyle,
    testID,
  }) => {
    return (
      <BbkComponentText
        key={currency}
        style={xMergeStyles([wrapperStyle])}
        testID={testID}
      >
        <BbkComponentText style={xMergeStyles([currencyStyle])}>
          {toRMB(currency)}
        </BbkComponentText>

        <NumberText useSpaceCode={true} style={xMergeStyles([priceStyle])}>
          {priceStr}
        </NumberText>
      </BbkComponentText>
    );
  };

  currencyNewText = ({
    currency,
    currencyStyle,
    priceStyle,
    priceStr,
    wrapperStyle,
    testID,
  }) => {
    return (
      <View
        testID={testID}
        key={currency}
        style={xMergeStyles([wrapperStyle, { flexDirection: 'row' }])}
      >
        <BbkComponentText
          style={xMergeStyles([{ marginRight: getPixel(2) }, currencyStyle])}
        >
          {toRMB(currency)}
        </BbkComponentText>
        <BbkComponentText style={xMergeStyles([priceStyle])}>
          {priceStr}
        </BbkComponentText>
      </View>
    );
  };

  render() {
    const { currency, currencyStyle, priceStyle, wrapperStyle, isNew, testID } =
      this.props;
    const { price } = this.props;

    if (isNew) {
      return this.currencyNewText({
        currency,
        currencyStyle,
        priceStyle,
        priceStr: price,
        wrapperStyle,
        testID,
      });
    }
    return this.currencyText({
      currency,
      currencyStyle,
      priceStyle,
      priceStr: price,
      wrapperStyle,
      testID,
    });
  }
}
