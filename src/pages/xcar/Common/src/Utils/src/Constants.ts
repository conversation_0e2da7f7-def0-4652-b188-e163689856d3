import Platform from '@c2x/apis/Platform';
import Device from '@c2x/apis/Device';

// @ts-ignore

export const BBK_PLATFORM = {
  REACT: 'REACT',
  REACT_NATIVE: 'REACT_NATIVE',
};

export const BBK_CHANNEL_ENV = {
  TRIP: 'TRIP',
  CTRIP_ISD: 'CTRIP_ISD',
  CTRIP_OSD: 'CTRIP_OSD',
};

export const ENV_TYPE = {
  FAT: 'fat',
  UAT: 'uat',
  BATTLE: 'battle',
  PROD: 'prd',
};

export const REST_SOA = 'restapi/soa2';

export const DOMAIN_URL = {
  [ENV_TYPE.FAT]: 'gateway.m.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.UAT]: 'gateway.m.uat.qa.nt.ctripcorp.com',
  [ENV_TYPE.BATTLE]: 'gateway.m.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.PROD]: 'm.ctrip.com',
};

export const APP_ID = {
  TRIP: '37',
  CTRIP: '999999',
};

export const BBK_GLOBAL_VAR = {
  CHANNEL: '__BBK_CHANNEL__',
  APPID: '__crn_appId',
  PLATFORM: '__BBK_PLATFORM__',
  THEMING: '__BBK_THEMING__',
  LYCONFIG: '__BBK_LYCONFIG__',
};
export const DEFAULT_HEADER_HEIGHT = Platform.select({
  ios: (128 - 40) / 2, // 设计稿750，ios头部128，其中stutas bar 40
  android: (160 - 48) / 2, // 设计稿750，ios头部160，其中stutas bar 48
  // @ts-ignore
  harmony: (160 - 48) / 2,
  web: (128 - 40) / 2,
});

export const DEFAULT_RENTCENTERHEADER_HEIGHT = 380;

export const DEFAULT_FILTER_BAR_HEIGHT = 40;

export const DEVICE_TYPE = {
  /**
   * 红米 3S
   */
  MIR3S: 'Xiaomi_Redmi 3S',
  MATE10: 'HUAWEI_MHA-TL00',
  iPhoneXR: 'iPhone XR',
};
// @ts-ignore Device未导出statusHeight定义
export const ANDROID_STATUS_HEIGHT = Device.statusHeight
  ? // @ts-ignore
    Device.statusHeight
  : 24; // statusHeight 安卓沉浸屏状态栏

// @ts-ignore
const ua = (navigator && navigator.userAgent) || '';

export const isiPhoneX = Device.isiPhoneX || ua.indexOf('PhoneX') !== -1;

export const DEFAULT_IPHONEX_OFFSET_TOP = 44;

export const DEFAULT_IPHONEX_OFFSET_BOTTOM = 34;

export const OFFSET_TOP = Platform.select({
  // @ts-ignore
  ios:
    typeof Device.safeAreaTop === 'number'
      ? // @ts-ignore
        Device.safeAreaTop
      : Device.isiPhoneX
        ? DEFAULT_IPHONEX_OFFSET_TOP
        : 20,
  android: ANDROID_STATUS_HEIGHT,
  // @ts-ignore
  harmony: ANDROID_STATUS_HEIGHT,
  // @ts-ignore
  web:
    typeof Device.safeAreaTop === 'number'
      ? // @ts-ignore
        Device.safeAreaTop
      : isiPhoneX
        ? DEFAULT_IPHONEX_OFFSET_TOP
        : 20,
});

export const OFFSET_BOTTOM = Platform.select({
  // @ts-ignore
  ios:
    typeof Device.safeAreaBottom === 'number'
      ? // @ts-ignore
        Device.safeAreaBottom
      : Device.isiPhoneX
        ? DEFAULT_IPHONEX_OFFSET_BOTTOM
        : 0,
  android: 0,
  // @ts-ignore
  harmony:
    typeof Device.safeAreaBottom === 'number'
      ? // @ts-ignore
        Device.safeAreaBottom
      : Device.isiPhoneX
        ? DEFAULT_IPHONEX_OFFSET_BOTTOM
        : 0,
  // @ts-ignore
  web:
    typeof Device.safeAreaBottom === 'number'
      ? // @ts-ignore
        Device.safeAreaBottom
      : isiPhoneX
        ? DEFAULT_IPHONEX_OFFSET_BOTTOM
        : 0,
});

// 姓名颠倒local
export const NAME_REVERSE_LOCALE = ['ko_KR', 'ja_JP'];

// 用来更新服务器图片，有需要更改
export const timestamps = '20230329';
