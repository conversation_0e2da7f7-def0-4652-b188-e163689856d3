import { cancelFetch, fetch } from '@c2x/apis/Fetch';
import uuid from 'uuid';

export const DEFAULT_TIMEOUT = 15;

const canceledSequenceIds = new Map();

const timeoutError = {
  cancelTimeout: true,
};

interface KeyMapValue {
  requestPromise: Promise<any>;
  sequenceId: string;
}

interface LatestFetchOptions {
  latest?: boolean;
  keyExtractor?: (url, params) => string | Symbol;
  cancelHooks?: (url, params, sequenceId) => void;
  timeoutHooks?: (url, params) => void;
}

class KeyExtractorManager {
  private keyMap = new Map<string | Symbol, KeyMapValue>();

  addRequest = (
    key: string | Symbol,
    newPromise: Promise<any>,
    url,
    params,
    options?: LatestFetchOptions,
  ) => {
    if (this.keyMap.has(key)) {
      const oldSequenceId = this.getSequenceId(key);
      cancelFetch(url, {
        sequenceId: oldSequenceId,
      });
      canceledSequenceIds.forEach((value, keyName) => {
        if (
          new Date().getTime() - value >
          ((params.timeout || DEFAULT_TIMEOUT) + 1) * 1000
          // 为保证有效对照样本且Map对象不能无限增长，删除超时时长加1秒之外的sequenceId（如果不加1，可能在超时时，已删除sequenceId，导致误判断）
        ) {
          canceledSequenceIds.delete(keyName);
        }
      });
      canceledSequenceIds.set(oldSequenceId, new Date().getTime());
      if (options?.cancelHooks) {
        options?.cancelHooks(url, params, oldSequenceId);
      }
    }
    this.keyMap.set(key, {
      requestPromise: newPromise,
      sequenceId: params.sequenceId,
    });
  };

  getValue = (key: string | Symbol): KeyMapValue => {
    return this.keyMap.get(key);
  };

  promiseTimeout = (prom, time = DEFAULT_TIMEOUT, sequenceId) => {
    let timer;
    return Promise.race([
      prom,
      new Promise(resolve => {
        timer = setTimeout(resolve, time * 1000, {
          resolveTimeout: true,
          sequenceId,
        });
      }),
    ]).finally(() => clearTimeout(timer));
  };

  getRequest = (key: string | Symbol, params): Promise<any> => {
    return this.promiseTimeout(
      this.getValue(key)?.requestPromise,
      params.timeout,
      params.sequenceId,
    );
  };

  getSequenceId = (key: string | Symbol): string => {
    return this.getValue(key)?.sequenceId;
  };

  deleteRequest = (key: string | Symbol) => {
    this.keyMap.delete(key);
  };

  clearRequest = () => {
    this.keyMap.clear();
  };

  getSize = (): number => this.keyMap.size;
}

const keyManager = new KeyExtractorManager();

const defaultkeyExtractor = (url, params): string | Symbol => url;

export const clearRequest = () => keyManager.clearRequest();
export const getSize = () => keyManager.getSize();

export async function latestWrap(
  requestPromise: Promise<any>,
  url,
  params,
  options,
) {
  const { keyExtractor = defaultkeyExtractor, timeoutHooks } = options;
  const key = keyExtractor(url, params);
  keyManager.addRequest(key, requestPromise, url, params, options);
  try {
    const res = await keyManager.getRequest(key, params);
    keyManager.deleteRequest(key);
    if (res?.resolveTimeout) {
      const { sequenceId } = res;
      if (!canceledSequenceIds.has(sequenceId)) {
        throw timeoutError;
      } else {
        timeoutHooks(url, params, 'timeoutHooks2');
        return null;
      }
    }
    return res;
  } catch (err) {
    if (err.cancelTimeout && timeoutHooks) {
      timeoutHooks(url, params);
    }
    keyManager.deleteRequest(key);
    throw err;
  }
}

export default async function latestFetch(
  url: string,
  params: any,
  options?: LatestFetchOptions,
): Promise<any> {
  let result;
  if (options?.latest) {
    params.sequenceId = params.sequenceId || uuid();
    result = await latestWrap(fetch(url, params), url, params, options);
  } else {
    result = await fetch(url, params);
  }
  return result;
}
