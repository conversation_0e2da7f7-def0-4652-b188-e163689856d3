import lazyRequire from '@c2x/apis/lazyRequire';
import { Channel } from '../Util/Index';
// react-native-deprecated-custom-components 由@ctrip/crn 模块中指定的依赖, 这里直接使用, 不再从 react-native 中导入
const {
  FloatFromBottom,
  FloatFromRight,
} = require('react-native-deprecated-custom-components/src/NavigatorSceneConfigs');

const Debug = lazyRequire('../Containers/DebugerContainer');
const Market = lazyRequire('../Containers/MarketContainer');
const License = lazyRequire('../Containers/LicenseContainer');
const Coupon = lazyRequire('../Containers/CouponContainer');
const Home = lazyRequire('../Containers/HomePageContainer');
const List = lazyRequire('../Containers/ListPageContainer');
const VendorList = lazyRequire('../Containers/VendorListPageContainer');
// const Image = lazyRequire('../Containers/ImagePageContainer'); // Image入口屏蔽
const OrderDetail = lazyRequire('../Containers/OrderDetailContainer');
const Booking = lazyRequire('../Containers/BookingContainer');
const BookingIsd = lazyRequire('../Containers/BookingISDContainer');
const Product = lazyRequire('../Containers/ProductPageContainer');
const Guide = lazyRequire('../Containers/GuidePageContainer');
const DriverList = lazyRequire('../Containers/DriverListContainer');
const DriverEdit = lazyRequire('../Containers/DriverEditContainer');
const VehModal = lazyRequire('../Containers/VehiclePageContainer');
const Materials = lazyRequire('../Containers/MaterialsContainer');
const Policy = lazyRequire('../Containers/PolicyContainer');
// const DriverIntroduction = lazyRequire('../Pages/DriverIntroduction/Index'); // __TAG_APP_UNUSED__
// const City = lazyRequire('../Containers/CityPageContainer'); // __TAG_APP_UNUSED__
const Extras = lazyRequire('../Containers/ExtrasModalContainer');
const PackageIncludes = lazyRequire('../Containers/PackageIncludesContainer');
const OnlineAuth = lazyRequire('../Containers/OnlineAuthContainer');
const Credentials = lazyRequire('../Containers/CredentialsContainer');
const OrderCancel = lazyRequire('../Containers/OrderDetailCancelContainer');
const OrderRefundDetail = lazyRequire(
  '../Containers/OrderRefundDetailContainer',
);
const OrderChange = lazyRequire('../Containers/OrderChangeContainer');
const OrderLimitRulesPage = lazyRequire(
  '../Containers/OrderLimitRulesPageContainer',
);
const LimitMapPage = lazyRequire('../Pages/LimitMap/Index');
const OrderVialationRule = lazyRequire(
  '../Containers/OrderViolationRuleContainer',
);

const SupplementList = lazyRequire('../Containers/SupplementListContainer');
const RenewList = lazyRequire('../Containers/RenewListContainer');
const DamageDetail = lazyRequire('../Containers/DamageDetailContainer');
const ViolationDetail = lazyRequire('../Containers/ViolationDetailContainer');
const Supplement = lazyRequire('../Containers/SupplementContainer');
const Rerent = lazyRequire('../Containers/RerentContainer');
const AdvanceReturn = lazyRequire('../Containers/AdvanceReturnContainer');
const InsuranceOrderDetail = lazyRequire(
  '../Containers/InsuranceOrderDetailContainer',
);
const ModifyOrder = lazyRequire('../Containers/ModifyOrderContainer');
const ModifyOrderConfirm = lazyRequire(
  '../Containers/ModifyOrderConfirmContainer',
);
const ModifyOrderCoupon = lazyRequire(
  '../Containers/ModifyOrderCouponContainer',
);
const RebookHome = lazyRequire('../Containers/RebookHomeContainer');
const CarRentalCenter = lazyRequire(
  '../Containers/CarRentalCenterPageContainer',
);
const IsdAgreement = lazyRequire('../Pages/IsdAgreement/Index');
const MessageAssistant = lazyRequire('../Containers/MessageAssistantContainer');
const Instructions = lazyRequire('../Containers/InstructionsContainer');
const Member = lazyRequire('../Containers/MemberContainer');
const MemberDetail = lazyRequire('../Containers/MemberDetailContainer');
const MemberBirth = lazyRequire('../Containers/MemberBirthContainer');
const MemberPoints = lazyRequire('../Containers/MemberPointsContainer');
const VehicleDamageProve = lazyRequire(
  '../Containers/VehicleDamageProveContainer',
);
const RecommendVehicle = lazyRequire(
  '../Containers/RecommendVehiclePageContainer',
);
const Location = lazyRequire('../Pages/Location/Index');
const DepositFree = lazyRequire('../Containers/DepositFreeContainer');
const Album = lazyRequire('../Containers/Album/AlbumContainer');

export interface PageRouteType {
  component: any;
  name: string;
  isInitialPage?: boolean;
  sceneConfig?: any;
}

const rightSceneConfig = {
  ...FloatFromRight,
  defaultTransitionVelocity: 10,
};

const FloatFromBottomGesturesForbidden = { ...FloatFromBottom, gestures: null };

const Pages: Array<PageRouteType> = [
  {
    component: Home,
    name: 'Home',
    isInitialPage: true,
  },
  {
    component: List,
    name: 'List',
    sceneConfig: {
      ...FloatFromRight,
      defaultTransitionVelocity: 10,
    },
  },
  {
    component: Location,
    name: 'Location',
    sceneConfig: FloatFromBottomGesturesForbidden,
  },
  {
    component: VendorList,
    name: Channel.getPageId().VendorList.EN,
    sceneConfig: rightSceneConfig,
  },
  {
    component: Guide,
    name: 'Guide',
    sceneConfig: rightSceneConfig,
  },
  // Image入口屏蔽
  // {
  //   component: Image,
  //   name: 'Image',
  // },
  {
    component: Coupon,
    name: 'Coupon',
    // sceneConfig: FloatFromBottomGesturesForbidden,
  },
  {
    component: Booking,
    name: 'Booking',
    sceneConfig: rightSceneConfig,
  },
  {
    component: BookingIsd,
    name: 'BookingIsd',
    sceneConfig: rightSceneConfig,
  },
  {
    component: Product,
    name: 'Product',
  },
  {
    component: Market,
    name: 'Market',
  },
  {
    component: License,
    name: 'License',
  },
  {
    component: Debug,
    name: 'Debug',
  },
  {
    component: DriverList,
    name: 'DriverList',
    // sceneConfig: FloatFromBottomGesturesForbidden,
  },
  {
    component: DriverEdit,
    name: 'DriverEdit',
    // sceneConfig: FloatFromBottomGesturesForbidden,
  },
  {
    component: VehModal,
    name: 'VehModal',
    // sceneConfig: FloatFromBottomGesturesForbidden,
  },
  {
    component: OrderDetail,
    name: 'OrderDetail',
    // isInitialPage: true,
  },
  {
    component: OnlineAuth,
    name: 'OnlineAuth',
    sceneConfig: rightSceneConfig,
  },
  // __TAG_APP_UNUSED__
  // {
  //   component: City,
  //   name: 'City',
  // },
  {
    component: Materials,
    name: 'Materials',
    // sceneConfig: FloatFromBottomGesturesForbidden,
  },
  {
    component: Policy,
    name: 'Policy',
    sceneConfig: FloatFromBottomGesturesForbidden,
  },
  // __TAG_APP_UNUSED__
  // {
  //   component: DriverIntroduction,
  //   name: 'DriverIntroduction',
  //   // sceneConfig: FloatFromBottomGesturesForbidden,
  // },
  {
    component: Extras,
    name: Channel.getPageId().Extras.EN,
  },
  {
    component: PackageIncludes,
    name: Channel.getPageId().PackageIncludes.EN,
  },
  {
    component: Credentials,
    name: Channel.getPageId().Credentials.EN,
  },
  {
    component: OrderCancel,
    name: Channel.getPageId().OrderCancel.EN,
  },
  {
    component: OrderRefundDetail,
    name: Channel.getPageId().OrderRefundDetail.EN,
  },
  {
    component: OrderChange,
    name: Channel.getPageId().OrderChange.EN,
  },
  {
    component: SupplementList,
    name: Channel.getPageId().SupplementList.EN,
  },
  {
    component: RenewList,
    name: Channel.getPageId().RenewList.EN,
  },
  {
    component: ViolationDetail,
    name: Channel.getPageId().ViolationDetail.EN,
  },
  {
    component: DamageDetail,
    name: Channel.getPageId().DamageDetail.EN,
  },
  {
    component: Supplement,
    name: 'Supplement',
  },
  {
    component: Rerent,
    name: Channel.getPageId().Rerent.EN,
  },
  {
    /**
     * AdvanceReturn 提前还车页
     * 视觉稿：https://design.ctripcorp.com/s/63dcca30828ed8004003a57f?p=7E30D745-D131-435B-9D47-3568ABFB53D9&a=19BA914B-FB00-4163-A3A5-A200797C0B48
     */
    component: AdvanceReturn,
    name: Channel.getPageId().AdvanceReturn.EN,
  },
  {
    component: OrderLimitRulesPage,
    name: Channel.getPageId().OrderLimitRulesPage.EN,
  },
  {
    component: InsuranceOrderDetail,
    name: 'InsuranceOrderDetail',
  },
  {
    component: OrderVialationRule,
    name: 'OrderVialationRule',
  },
  {
    component: ModifyOrder,
    name: Channel.getPageId().ModifyOrder.EN,
  },
  {
    component: ModifyOrderConfirm,
    name: Channel.getPageId().ModifyOrderConfirm.EN,
  },
  {
    component: ModifyOrderCoupon,
    name: 'ModifyCoupon',
  },
  {
    component: RebookHome,
    name: Channel.getPageId().RebookHome.EN,
  },
  {
    component: CarRentalCenter,
    name: Channel.getPageId().CarRentalCenter.EN,
  },
  {
    component: IsdAgreement,
    name: Channel.getPageId().IsdAgreement.EN,
  },
  {
    component: MessageAssistant,
    name: Channel.getPageId().MessageAssistant.EN,
  },
  {
    component: Instructions,
    name: Channel.getPageId().Instructions.EN,
    sceneConfig: rightSceneConfig,
  },
  {
    component: Member,
    name: Channel.getPageId().Member.EN,
  },
  {
    component: MemberDetail,
    name: Channel.getPageId().MemberDetail.EN,
  },
  {
    component: MemberBirth,
    name: Channel.getPageId().MemberBirth.EN,
  },
  {
    component: MemberPoints,
    name: Channel.getPageId().MemberPoints.EN,
  },
  {
    component: VehicleDamageProve,
    name: Channel.getPageId().VehicleDamageProve.EN,
  },
  {
    component: RecommendVehicle,
    name: Channel.getPageId().RecommendVehicle.EN,
  },
  {
    component: LimitMapPage,
    name: Channel.getPageId().LimitMap.EN,
  },
  {
    component: DepositFree,
    name: Channel.getPageId().DepositFree.EN,
  },
  {
    component: Album,
    name: Channel.getPageId().Album.EN,
  },
];

export default Pages;
