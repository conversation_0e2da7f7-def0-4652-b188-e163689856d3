import Event from '@c2x/apis/Event';

import { EventName, LogKey } from './Constants/Index';
import { onAuthRealNameRegister } from './State/Sesame/Actions';
import { CarLog, CarABTesting, EventHelper } from './Util/Index';
import { getStore } from './State/StoreRef';

const sesameRealNameRegisterEvent = () => {
  EventHelper.removeEventListener(EventName.sesameRealNameRegisterCallback);
  EventHelper.addEventListener(
    EventName.sesameRealNameRegisterCallback,
    data => {
      let authCode = '';
      Object.keys(data).forEach(key => {
        if (key === 'url') {
          const params: Array<string> = data[key].split('&');
          params.forEach(p => {
            const urlValue = p.split('=');
            if (
              urlValue &&
              urlValue.length === 2 &&
              urlValue[0] === 'auth_code'
            ) {
              [, authCode] = urlValue;
            }
          });
        }
      });

      CarLog.LogTrace({
        key: LogKey.c_car_trace_sesame_credit,
        info: {
          type: EventName.sesameRealNameRegisterCallback,
          data,
          authCode,
        },
      });

      getStore()?.dispatch(
        onAuthRealNameRegister({
          authCode,
          showToast: CarABTesting.isCreditRent(),
        }),
      );
    },
  );
};

const addEventListener = () => {
  sesameRealNameRegisterEvent();
};

const removeEventListener = () => {
  Event.removeEventListener(EventName.sesameRealNameRegisterCallback);
};

export default removeEventListener;
export { addEventListener, removeEventListener };
