/* eslint-disable max-lines */
/* eslint-disable no-irregular-whitespace */
export interface QueryProductInfoRequestType {
  baseRequest?: BaseRequest | null;
  head?: MobileRequestHead | null;
  reference?: Reference | null;
  /**
   * 是否轮询，true轮询，false强制刷新
   */
  pooling?: boolean | null;
  /**
   * 取车地点
   */
  pickupPointInfo?: CtqQueryPoint | null;
  /**
   * 还车地点
   */
  returnPointInfo?: CtqQueryPoint | null;
  /**
   * 携程订单号
   */
  ctripOrderId?: number | null;
  /**
   * 供应商订单号
   */
  vendorOrderCode?: string | null;
  /**
   * 详情页分享扩展信息
   */
  shareExtInfo?: ShareExtInfo | null;
  /**
   * 缓存标志
   */
  vcCacheKey?: string | null;
  /**
   * 是否加购无忧租
   */
  upgradeEasyLife?: boolean | null;
  /**
   * 是否启用用服务端缓存（境外使用）
   */
  withCache?: boolean | null;
}
export interface QueryProductInfoResponseType {
  /**
   * 基础响应参数
   */
  baseResponse?: BaseResponse | null;
  responseStatus?: ResponseStatusType | null;
  /**
   * 是否命中缓存 0-未命中，1-列表页缓存，2-填写页缓存，3-有缓存未使用
   */
  cacheType?: number | null;
  /**
   * 是否变价
   */
  priceChange?: boolean | null;
  /**
   * 是否变价。和priceChange的含义完全一样。服务端为了兼容前端取错字段的场景。
   */
  isChange?: boolean | null;
  /**
   * 是否无库存
   */
  isSoldOut?: boolean | null;
  /**
   * 和整日租期比价
   */
  wholeDayPrice?: WholeDayPriceInfo | null;
  /**
   * 是否热门城市
   */
  isHotCity?: boolean | null;
  /**
   * 租车中心
   */
  rentCenter?: CtqRentCenter | null;
  /**
   * 还车租车中心
   */
  rRentCenter?: CtqRentCenter | null;
  /**
   * 是否携程优选
   */
  isSelected?: boolean | null;
  /**
   * 是否旗舰店
   */
  fType?: boolean | null;
  /**
   * 是否命中降级
   */
  needDownGrade?: boolean | null;
  /**
   * 携程优选详情
   */
  ctripSelected?: TitleAndDesc[] | null;
  /**
   * 取还车门店
   */
  pickupStoreInfo?: CtqStoreInfo | null;
  returnStoreInfo?: CtqStoreInfo | null;
  /**
   * 供应商信息
   */
  vendorInfo?: CtqVendorInfo | null;
  /**
   * 车型信息
   */
  vehicleInfo?: CtqVehicleInfo | null;
  /**
   * 供应商温馨提示
   */
  warmTips?: string[] | null;
  /**
   * 航班延误政策
   */
  flightDelayRule?: FlightDelayRetentionRules | null;
  /**
   * 修改订单信息
   */
  modifyOrderInfo?: ModifyOrderInfo | null;
  /**
   * 点评
   */
  commentInfo?: CtqCommentInfo | null;
  /**
   * 保险列表
   */
  packageInfos?: PackageInfo[] | null;
  /**
   * 产品信息
   */
  productDetails?: PackageDetail[] | null;
  /**
   * 同组车型描述
   */
  similarVehicleIntroduce?: SimilarVehicleIntroduce | null;
  /**
   * 指定车型描述
   */
  designatedVehicleIntroduce?: SimpleObject | null;
  /**
   * 行李箱说明信息
   */
  trunkInfo?: StringObject | null;
  /**
   * 国内事故故障说明
   */
  vendorInsuranceDesc?: VendorInsuranceDesc | null;
  /**
   * 变价信息
   */
  priceChangeInfo?: PriceChangeInfo | null;
  /**
   * 供应商支持证件类型
   */
  idCardTypes?: IDCardType[] | null;
  /**
   * 押金信息
   */
  depositInfo?: DepositInfo | null;
  /**
   * 详情页提示信息集合
   */
  promptInfos?: PromptObject[] | null;
  /**
   * 前端埋点数据
   */
  trackInfo?: TrackInfo | null;
  /**
   * 货架信息
   */
  gs?: GoodsShelves | null;
  /**
   * 新货架描述字段
   */
  gsDesc?: string | null;
  /**
   * 扩展字段
   */
  extra?: { [key: string]: string } | null;
  /**
   * 前端会根据此字段自动覆盖掉Reference中对应字段的值
   */
  referenceTemp?: ReferenceTemp | null;
  /**
   * 新填写页 预定须知栏
   */
  rentalMustReadTitle?: TitleAndDesc | null;
  /**
   * 新填写页 激励信息栏
   */
  encourageTitle?: string | null;
  /**
   * 缓存标志
   */
  vcCacheKey?: string | null;
  /**
   * 自营险公用信息
   */
  platformInsuranceExtra?: PlatformInsuranceExtra | null;
  /**
   * 1标识支持 0不支持(前端处理下未null的场景，按0处理)
   */
  imStatus?: number | null;
  /**
   * 跨境跨岛政策
   */
  crossLocationsInfo?: CrossLocationsInfo | null;
  /**
   * 取还方式信息列表
   */
  storeGuidInfos?: MergeGuidInfo[] | null;
  /**
   * 无忧租升级信息
   */
  easyLifeUpgradeInfo?: EasyLifeUpgrade | null;
  /**
   * 请求信息
   */
  requestInfo?: CtqRequestInfo | null;
  /**
   * 商品分级-等级提示
   */
  stockLevelPrompt?: string | null;
  /**
   * 无结果提示
   */
  detailRecommendInfo?: DetailRecommendInfo | null;
  /**
   * 驾照签发国政策
   */
  licenceCountryPolicy?: ExplainObject | null;
  /**
   * 新版跨境跨岛政策
   */
  crossPolicy?: CrossLocationsPolicy | null;
  /**
   * 地区保险售卖推荐话术
   */
  locationPrompt?: string | null;
  /**
   * 保险比较二级页标题
   */
  osdCompareTitle?: string[] | null;
  /**
   * 当前供应商所有套餐组合中不是都含有三者险话术
   */
  tplPrompt?: string | null;
  /**
   * 是否需要重试（目前是给详情页轮询使用的，如果返回为false则代表短时间内重试也不会有结果）
   */
  isRetry?: boolean | null;
  /**
   * 售卖属性
   */
  saleAttribute?: SaleAtr | null;
  /**
   * 详情页套餐过滤原因
   */
  filterReasons?: FilterReason[] | null;
  optionalContactMethods?: OptionalContactMethods;
  /**
   * 货架车型描述
   */
  commodityDescDTO?: CommodityDescDTO | null;
}
export interface CommodityDescDTO {
  /**
   * 半年内车龄、一年内车龄、两年内车龄、三年内车龄等
   */
  carAgeLabel?: string | null;
  /**
   * 新款，或者为空表示非新款
   */
  newModels?: string | null;
  /**
   * 免费取消、限时免费取消、有损取消
   */
  cancelRulesDesc?: string | null;
  /**
   * 基础服务、优享服务、无忧尊享服务
   */
  packageDesc?: string | null;
}
export interface FilterReason {
  packageId?: number | null;
  filterReason?: string | null;
}
export interface SaleAtr {
  /**
   * 是否是自助取还
   */
  selfService?: number | null;
}
export interface CrossLocationsPolicy {
  crossLocationsInfos?: CrossLocationsInfo[] | null;
  notes?: string[] | null;
  /**
   * 标题
   */
  title?: string | null;
}
export interface ExplainObject {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 内容
   */
  content?: string[] | null;
  /**
   * 内容带有样式
   */
  contentObject?: ContentObject[] | null;
  /**
   * 简要内容
   */
  summaryContent?: string[] | null;
  /**
   * 类型
   */
  type?: number | null;
  code?: string | null;
  urlName?: string | null;
  /**
   * 图标或链接地址
   */
  url?: string | null;
  urlList?: string[] | null;
  subObject?: ExplainObject[] | null;
  /**
   * 注释内容
   */
  note?: string | null;
  sortNum?: number | null;
  /**
   * 赫兹预付只允许信用卡
   */
  whitePayWays?: string | null;
  /**
   * 规则表格信息
   */
  table?: ChargeItemDetail[] | null;
  /**
   * 简要内容（带有样式）
   */
  summaryContentObject?: ContentObject[] | null;
  summaryTitle?: string | null;
  /**
   * 推荐状态:0不推荐,1推荐
   */
  optimalType?: string | null;
  /**
   * 简要提示
   */
  summaryObject?: ExplainObject[] | null;
  /**
   * 是否取车前免费取消，是否一小时以内确认
   */
  showFree?: number | null;
}
export interface ChargeItemDetail {
  title?: string | null;
  subTitle?: string | null;
  description?: string | null;
  /**
   * 子标题
   */
  complexSubTitle?: ContentObject | null;
  /**
   * 内容
   */
  contents?: ContentObject[] | null;
  code?: string | null;
  type?: number | null;
  size?: string | null;
  include?: boolean | null;
  currencyCode?: string | null;
  currenctDailyPrice?: number | null;
  currentTotalPrice?: number | null;
  localCurrencyCode?: string | null;
  localDailyPrice?: number | null;
  localTotalPrice?: number | null;
  showFree?: boolean | null;
  positiveDesc?: string | null;
  needDeposit?: boolean | null;
  retractable?: boolean | null;
  showPrice?: string | null;
  payMode?: number | null;
  sortNum?: number | null;
  items?: ChargeItemDetail[] | null;
  notices?: string[] | null;
  labels?: LabelDetail[] | null;
  positiveStatus?: boolean | null;
  isInstantConfirm?: boolean | null;
  tableTitle?: string | null;
  freezeDeposit?: boolean | null;
  showCreditCard?: boolean | null;
  activityCode?: string | null;
  discountType?: number | null;
  /**
   * 日历价
   */
  priceDailys?: PriceDaily[] | null;
  /**
   * 小时租文案
   */
  hourDesc?: string | null;
  originDailyPrice?: number | null;
  /**
   * 是否赫兹预付
   */
  isHertz?: boolean | null;
  /**
   * 富文本的提示
   */
  richNotices?: ContentObject[] | null;
}
export interface PriceDaily {
  /**
   * 日期
   */
  date?: string | null;
  /**
   * 原天价
   */
  oDprice?: string | null;
  /**
   * 展示金额
   */
  priceStr?: string | null;
  /**
   * 展示类型（前端样式用） 0 默认宽度， 1 day1-day2 展示宽度
   */
  showType?: number | null;
}
export interface LabelDetail {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 标签code
   */
  code?: string | null;
  /**
   * 标签类型
   */
  type?: number | null;
  grade?: number | null;
  /**
   * 排序
   */
  sortNum?: number | null;
  /**
   * 跳转链接
   */
  jumpUrl?: string | null;
}
export interface ContentObject {
  /**
   * 样式
   */
  contentStyle?: string | null;
  stringObjs?: StringObject[] | null;
}
export interface DetailRecommendInfo {
  /**
   * 错误code
   */
  errorCode?: string | null;
  /**
   * 错误类型，仅埋点统计
   */
  errorType?: string | null;
  /**
   * 主提示
   */
  title?: string | null;
  /**
   * 副提示
   */
  subTitle?: string | null;
  /**
   * 按钮
   */
  buttonInfo?: ButtonInfo[] | null;
  /**
   * 图片类型
   */
  picType?: string | null;
}
export interface ButtonInfo {
  /**
   * 按钮类型
   */
  buttonType?: string | null;
  /**
   * 按钮文案
   */
  buttonTitle?: string | null;
  /**
   * 是否是主按钮
   */
  isPrimary?: boolean | null;
}
export interface CtqRequestInfo {
  /**
   * 取车时间
   */
  pickupDate?: string | null;
  /**
   * 取车地点
   */
  pickupLocationName?: string | null;
  /**
   * 还车时间
   */
  returnDate?: string | null;
  /**
   * 还车地点
   */
  returnLocationName?: string | null;
  /**
   * 租期
   */
  rentalDay?: number | null;
  /**
   * 客源国id
   */
  sourceCountryId?: number | null;
  /**
   * 驾驶员年龄
   */
  age?: number | null;
  /**
   * 语言
   */
  language?: string | null;
  /**
   * 币种
   */
  currencyCode?: string | null;
  /**
   * 取车纬度
   */
  pLatitude?: number | null;
  /**
   * 还车经度
   */
  rLatitude?: number | null;
  /**
   * 还车纬度
   */
  rLongitude?: number | null;
  /**
   * 取车经度
   */
  pLongitude?: number | null;
  /**
   * 取车时间(前端需要string格式)
   */
  pDate?: string | null;
  /**
   * 还车时间
   */
  rDate?: string | null;
  /**
   * 取车地点城市id
   */
  pCityId?: number | null;
  /**
   * 还车地点城市id
   */
  rCityId?: number | null;
}
export interface EasyLifeUpgrade {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 无忧租升级提示
   */
  tips?: string | null;
  /**
   * 与普通租差价
   */
  gapPrice?: number | null;
  /**
   * 无忧租信息
   */
  easyLifeInfo?: SimpleEasyLifeInfo | null;
  /**
   * 是否加购无忧租
   */
  isCheck?: boolean | null;
  /**
   * 当前服务内容
   */
  currentService?: Content | null;
  /**
   * 无忧租套餐id
   */
  packageId?: string | null;
  /**
   * 还车经度
   */
  rLongitude?: number | null;
}
export interface Content {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 内容
   */
  content?: string | null;
  /**
   * 提示
   */
  hint?: string | null;
  /**
   * icon图片链接
   */
  url?: string | null;
}
export interface SimpleEasyLifeInfo {
  /**
   * 是否是无忧租
   */
  isEasyLife?: boolean | null;
  /**
   * 无忧租副标题
   */
  subTitle?: string | null;
  /**
   * 标签列表
   */
  tagList?: SimpleObject[] | null;
  /**
   * 无忧租简述
   */
  titleAbstract?: KeyAndValue[] | null;
}
export interface KeyAndValue {
  title?: string | null;
  description?: string | null;
  code?: string | null;
  type?: number | null;
  typeDesc?: string | null;
  sortNum?: number | null;
}
export interface MergeGuidInfo {
  /**
   * 取还方式
   */
  storeGuid?: string | null;
  /**
   * 地址
   */
  address?: string | null;
  /**
   * 类型 1. 取 2.还 3取还合并
   */
  type?: number | null;
}
export interface CrossLocationsInfo {
  /**
   * 类型 1跨岛 2跨州/省 3跨国
   */
  crossType?: number | null;
  /**
   * 政策列表
   */
  locations?: CrossislandLocation[] | null;
  /**
   * 通用政策
   */
  policies?: string[] | null;
  crossTypeName?: string | null;
  /**
   * 简要政策
   */
  summaryPolicies?: string[] | null;
  title?: string | null;
  subTitle?: string | null;
  summaryTitle?: string | null;
}
export interface CrossislandLocation {
  /**
   * 区域名称
   */
  name?: string | null;
  /**
   * 跨境状态 状态id 1 允许 2条件 3不允许
   */
  status?: number | null;
  /**
   * 状态名称
   */
  statusName?: string | null;
  /**
   * 首字母
   */
  firstChar?: string | null;
  /**
   * 政策
   */
  policy?: string | null;
  /**
   * 区域：国家id
   */
  regionId?: string | null;
  /**
   * 是否被选择
   */
  isSelected?: boolean | null;
}
export interface PlatformInsuranceExtra {
  /**
   * 激励标题
   */
  encourageTitle?: string | null;
  /**
   * 激励话术
   */
  encourageDesc?: string | null;
  /**
   * 理赔流程
   */
  claimProcedure?: SimpleText[] | null;
  /**
   * 保险code
   */
  code?: string | null;
}
export interface SimpleText {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 类型
   */
  type?: number | null;
  /**
   * 文案
   */
  content?: string[] | null;
}
export interface ReferenceTemp {
  pickWayInfo?: number | null;
  returnWayInfo?: number | null;
  /**
   * 是否卡拉比(0:否，1：是)  废弃
   */
  isKarabi?: number | null;
  /**
   * 是否卡拉比(0-否，1-是)
   */
  klb?: number | null;
  /**
   * 一嗨价格一致码
   */
  comPriceCode?: string | null;
}
export interface GoodsShelves {
  /**
   * id
   */
  id?: number | null;
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  sTitle?: string | null;
  /**
   * 提示
   */
  hint?: string | null;
  /**
   * 更多商品数量
   */
  mCount?: number | null;
}
export interface TrackInfo {
  /**
   * 供应商code
   */
  vendorCode?: string | null;
  /**
   * 供应商所属平台
   */
  vendorPlatFrom?: number | null;
  /**
   * 供应商支持免押类型  1:程信分，2：芝麻免押，3：两种都支持， 4：都不支持
   */
  depositFreeType?: number | null;
  /**
   * 支持免押类型 0:不支持 10：双免 20单免租车 30单免违章
   */
  depositType?: number | null;
  /**
   * 初始准入
   */
  riskOriginal?: string | null;
  /**
   * F原因
   */
  refuseType?: string | null;
  /**
   * 修改准入
   */
  riskFinal?: string | null;
  /**
   * 实际准入说明
   */
  riskFinalDesc?: string | null;
  /**
   * 芝麻结果
   */
  zhimaResult?: string | null;
  zhimaResultDesc?: string | null;
}
export interface PromptObject {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 内容
   */
  contents?: ContentObject[] | null;
  /**
   * 类型
   */
  type?: number | null;
  /**
   * 图标或链接地址
   */
  icon?: string | null;
  /**
   * 注释内容
   */
  note?: string | null;
  /**
   * 展示位置
   */
  locations?: EntryLocation[] | null;
  /**
   * 按钮
   */
  button?: ButtonObject | null;
  /**
   * 扩展按钮
   */
  buttonExt?: ButtonObject[] | null;
  /**
   * 筛选项
   */
  filterItem?: FilterItem | null;
  /**
   * 内容项
   */
  items?: CommonKvObject[] | null;
  /**
   * 表格项
   */
  table?: CommonKvObject[] | null;
  /**
   * 前端样式配置
   */
  style?: string | null;
  /**
   * 背景
   */
  backGroundUrl?: string | null;
  /**
   * 字体颜色
   */
  textColor?: TextColor | null;
  /**
   * 主标题图标
   */
  mainTextIcon?: string | null;
  /**
   * 唐图用户权益类型
   */
  tangChoiceTypes?: string | null;
  /**
   * 背景跳转url
   */
  jumpUrl?: string | null;
  /**
   * 素材id
   */
  materialId?: string | null;
  /**
   * 唐图补充信息
   */
  extraInfos?: { [key: string]: string } | null;
  /**
   * 带有样式的title
   */
  titleObject?: ContentObject | null;
  /**
   * 带有样式的子标题
   */
  subTitleObject?: ContentObject | null;
  /**
   * 子类型
   */
  subType?: number | null;
}
export interface TextColor {
  r?: number | null;
  g?: number | null;
  b?: number | null;
  a?: number | null;
}
export interface CommonKvObject {
  title?: ContentObject | null;
  desc?: ContentObject[] | null;
  note?: ContentObject | null;
}
export interface FilterItem {
  itemCode?: string | null;
  name?: string | null;
  code?: string | null;
  groupCode?: string | null;
  /**
   * 未运算类型 1 与运算 2或运算
   */
  bitwiseType?: number | null;
  /**
   * 二进制数
   */
  binaryDigit?: number | null;
  sortNum?: number | null;
  /**
   * 是否首页显示
   */
  isQuickItem?: boolean | null;
  quickSortNum?: number | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 活动信息
   */
  promotion?: Promotion | null;
  /**
   * 角标
   */
  mark?: string | null;
  /**
   * 位置code
   */
  positionCode?: string | null;
  /**
   * 筛选后图片
   */
  selectedIcon?: string | null;
  /**
   * 筛选项样式类型，null/0：筛选项，1：滑动条
   */
  itemType?: number | null;
  /**
   * 滑动条步长
   */
  step?: number | null;
}
export interface Promotion {
  type?: number | null;
  title?: string | null;
  description?: string | null;
  longTag?: string | null;
  longDesc?: string | null;
  couponDesc?: string | null;
  deductionPercent?: number | null;
  deductionAmount?: number | null;
  dayDeductionAmount?: number | null;
  /**
   * 券类型 0-默认，1-返现，2-立减
   */
  payofftype?: number | null;
  payoffName?: string | null;
  code?: string | null;
  isFromCtrip?: boolean | null;
  islimitedTimeOfferType?: boolean | null;
  sortNum?: number | null;
  strategySource?: string | null;
  earningsCost?: string | null;
  businessCost?: string | null;
  resourceCost?: string | null;
  configVersion?: string | null;
  /**
   * 是否可用
   */
  isEnabled?: boolean | null;
  /**
   * 生效时间
   */
  actionedDate?: string | null;
  /**
   * 过期时间
   */
  expiredDate?: string | null;
  /**
   * 扩展描述字段 ：1叠加信息描述 2不可用说明
   */
  extDesc?: string | null;
  /**
   * 是否选中
   */
  selected?: boolean | null;
  /**
   * 满金额
   */
  startAmount?: number | null;
  /**
   * 是否可叠加
   */
  isOverlay?: boolean | null;
  /**
   * id
   */
  promotionId?: number | null;
  /**
   * 叠加说明
   */
  overlayDesc?: string | null;
  /**
   * 券名
   */
  couponName?: string | null;
  /**
   * 单位
   */
  unitName?: string | null;
  /**
   * 列表中对应金额
   */
  popAmountTile?: string | null;
  /**
   * 减免类型
   */
  deductionType?: number | null;
  /**
   * 活动来源
   */
  discountType?: number | null;
  /**
   * 调价版本号
   */
  adjustPriceCode?: string | null;
  /**
   * 标签Code
   */
  labelCode?: string | null;
  /**
   * 优惠活动币种
   */
  unitCurrency?: string | null;
}
export interface ButtonObject {
  title?: string | null;
  type?: number | null;
  desc?: string | null;
  tips?: string | null;
  icon?: string | null;
}
export interface EntryLocation {
  /**
   * 车型组
   */
  groupCode?: string | null;
  /**
   * 车型组下车型位置
   */
  index?: number | null;
}
export interface DepositInfo {
  /**
   * 押金类型 1：单免租车，2：单免违章，3：双免
   */
  depositType?: number | null;
  /**
   * 押金类型名称
   */
  depositTypeName?: string | null;
  /**
   * 租车押金
   */
  carRentalDepositFee?: number | null;
  /**
   * 违章押金
   */
  illegalDepositFee?: number | null;
  /**
   * 押金标题
   */
  depositTitle?: ContentObject | null;
  /**
   * 租车押金介绍
   */
  carRentalDeposit?: CommonKvObject | null;
  /**
   * 违章押金介绍
   */
  illegalDeposit?: CommonKvObject | null;
  /**
   * 押金注释
   */
  depositNote?: ContentObject | null;
  /**
   * 押金说明
   */
  depositDesc?: CommonKvObject | null;
  /**
   * 信用租2期押金说明
   */
  depositDescV2?: CommonKvObject | null;
  /**
   * 信用租2期押金使用案例
   */
  depositCase?: ExplainObject | null;
  /**
   * 信用租2期下单提示
   */
  depositPrompt?: PromptObject | null;
  /**
   * 腾讯二期 线下免押流程
   */
  depositeProcedures?: PromptObject[] | null;
  /**
   * 腾讯二期 线下免押规则
   */
  depositeRule?: CommonTable | null;
}
export interface CommonTable {
  tableId?: string | null;
  tableHead?: string | null;
  url?: string | null;
  desc?: string | null;
  content?: TitleAndDesc[] | null;
  items?: CommonTableItem[] | null;
  explain?: CommonKvObject | null;
  /**
   * 押金服务规则说明
   */
  explainExt?: PromptObject | null;
}
export interface CommonTableItem {
  rowIndex?: string | null;
  columnIndex?: string | null;
  content?: string | null;
}
export interface IDCardType {
  /**
   * 证件类型
   */
  idCardType?: number | null;
  /**
   * 证件名
   */
  idCardName?: string | null;
}
export interface PriceChangeInfo {
  /**
   * 10:降价，20：涨价小于20,30：大于20，打底，31：缺异地，32：缺夜间，33：充电费，34
   * ：其他
   */
  code?: string | null;
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 内容
   */
  contents?: ContentObject[] | null;
  /**
   * 差价
   */
  priceDiff?: number | null;
  /**
   * 当前价格
   */
  currPrice?: number | null;
  /**
   * 明细
   */
  costItemChangeInfos?: CostItemChangeInfo[] | null;
  /**
   * 激励话术
   */
  encourageInfo?: ContentObject | null;
  /**
   * 按钮
   */
  buttons?: ButtonObject[] | null;
}
export interface CostItemChangeInfo {
  /**
   * 费用项code
   */
  serviceCode?: string | null;
  /**
   * 费用项
   */
  serviceName?: string | null;
  /**
   * 原始总价
   */
  sourceTotalPrice?: number | null;
  /**
   * 原始价格信息
   */
  source?: ContentObject[] | null;
  /**
   * 当前总价
   */
  currentTotalPrice?: number | null;
  /**
   * 当前价格信息
   */
  current?: ContentObject[] | null;
  /**
   * 差价
   */
  priceDiff?: number | null;
  /**
   * 差价信息
   */
  diff?: ContentObject[] | null;
  /**
   * 价格信息
   */
  priceDesc?: string | null;
}
export interface VendorInsuranceDesc {
  insurancedesc?: string | null;
  insurancelist?: VendorInsuranceItem[] | null;
  /**
   * 保险不承保项
   */
  exclusionDesc?: VendorInsuranceSubItem | null;
  /**
   * 车行险提示
   */
  vendorInsuranceTips?: string | null;
}
export interface VendorInsuranceSubItem {
  title?: string | null;
  desclist?: string[] | null;
}
export interface VendorInsuranceItem {
  title?: string | null;
  type?: number | null;
  desclist?: VendorInsuranceSubItem[] | null;
}
export interface StringObject {
  content?: string | null;
  style?: string | null;
  url?: string | null;
}
export interface SimpleObject {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 标题后缀
   */
  titleExtra?: string | null;
  /**
   * 标签类型
   */
  category?: number | null;
  /**
   * 导向类型
   */
  type?: number | null;
  /**
   * code
   */
  code?: string | null;
  /**
   * 类描述
   */
  typeDesc?: string | null;
  /**
   * 描述
   */
  description?: string | null;
  sortNum?: number | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 展示层级
   */
  showLayer?: number | null;
  /**
   * 颜色类型
   */
  colorCode?: string | null;
  subList?: SimpleObject[] | null;
  /**
   * 标签code
   */
  labelCode?: string | null;
  /**
   * 位置code
   */
  positionCode?: string | null;
  /**
   * 标签groupCode
   */
  groupCode?: string | null;
  /**
   * 填写页重构标签分级
   */
  tagGroups?: number | null;
  /**
   * 填写页重构标签排序
   */
  tagSortNum?: number | null;
  /**
   * 标签展示分行, 用于分行，groupId一样的放在一行
   */
  amountTitle?: string | null;
  /**
   * 货架二期标签合并组，1 营销类 2车辆类，3服务类      货架2.0,共减XX
   */
  groupId?: number | null;
  /**
   * 标签合并Id， mergeId大于0 ， 且一样的情况需要合并在一起展示， 如无忧组情况下， 押金双
   * 免，免费取消标签需要合并在一起展示
   */
  mergeId?: number | null;
  /**
   * 延时免费留车标签前缀租车中心
   */
  prefix?: string | null;
  /**
   * 国内调价项目新增：标签前缀id：1 标识调价
   */
  prefixTypeId?: number | null;
  /**
   * 合并后的标题：用于弹窗。      标签外露标题和弹窗里展示样式不一致，弹窗里是纯文本      国
   * 内调价项目新增(2023-08-25)
   */
  mergeTitle?: string | null;
  /**
   * 带有样式的标签描述
   */
  descriptionObject?: ContentObject[] | null;
}
export interface SimilarVehicleIntroduce {
  introduce?: SimpleObject | null;
  carProtection?: SimpleObject | null;
  cases?: VehicleGroupCase[] | null;
  /**
   * 视频
   */
  vedio?: string | null;
  /**
   * 视频封面
   */
  cover?: string | null;
}
export interface VehicleGroupCase {
  vehicleGroupCode?: string | null;
  vehicleGroupName?: string | null;
  representativeVehicleName?: string | null;
  vehicleGroupItems?: string | null;
}
export interface PackageDetail {
  insPackageId?: number | null;
  subType?: number | null;
  /**
   * 保险项
   */
  insuranceItems?: InsuranceItem[] | null;
  /**
   * 自营险Id
   */
  ctripInsuranceIds?: number[] | null;
  /**
   * 保险描述
   */
  insuranceDesc?: string[] | null;
  /**
   * 精选组合
   */
  combinations?: AdditionalCombination[] | null;
  /**
   * 套餐项
   */
  productInfoList?: ProductDetailInfo[] | null;
  /**
   * 理赔流程（车行险+自营险）
   */
  claimsProcess?: ExplainObject[] | null;
  /**
   * 保险分组
   */
  insuranceGroup?: InsuranceGroup[] | null;
  /**
   * 承保说明
   */
  extraDesc?: string[] | null;
  /**
   * 一级页面
   */
  briefInsuranceItems?: InsuranceItem[] | null;
  /**
   * 套餐对比页
   */
  insuranceCompareItems?: PackageContains[] | null;
  /**
   * trip自营险升级弹窗
   */
  upgradePlatformInsurance?: PackageUpgrade[] | null;
}
export interface PackageUpgrade {
  /**
   * 未加购自营险话术
   */
  origin?: string | null;
  /**
   * 加购自营险后话术
   */
  upgrade?: string | null;
}
export interface PackageContains {
  /**
   * 一级页面展示，true为绿勾，false红叉
   */
  contains?: boolean | null;
  /**
   * 一级页面展示文案
   */
  description?: string | null;
  type?: string | null;
  attr?: string | null;
  /**
   * true 展示，false不展示。 用于前端只展示部分内容时，服务端也能得到完整内容
   */
  show?: boolean | null;
  descriptionColorCode?: string | null;
  /**
   * 对于勾叉的补充描述
   */
  containsDescription?: string | null;
}
export interface InsuranceGroup {
  /**
   * 保险分组code
   */
  code?: string | null;
  /**
   * 分组名称
   */
  title?: string | null;
  /**
   * 分组描述
   */
  description?: string | null;
}
export interface ProductDetailInfo {
  /**
   * 产品code
   */
  productCode?: string | null;
  /**
   * 套餐分组code
   */
  bomGroupCode?: string | null;
  /**
   * 价格明细列表
   */
  priceInfoList?: IbuPriceInfo[] | null;
  /**
   * 是否必须航班号
   */
  needFlightNo?: boolean | null;
  showFlightNo?: boolean | null;
  /**
   * 航班延误政策
   */
  flightDelayInfo?: FlightDelayInfo | null;
  /**
   * 附加设备列表
   */
  equipments?: IbuEquipmentInfo[] | null;
  /**
   * 自营保险（国内）
   */
  ctripInsurances?: CtripInsuranceInfo[] | null;
  /**
   * 套餐包含项
   */
  packageItems?: IbuPackageItem[] | null;
  /**
   * 是否为裸价套餐
   */
  naked?: boolean | null;
  /**
   * 芝麻免押信息
   */
  zhiMaInfo?: ZhiMaInfo | null;
  /**
   * 取车材料（身份证明，驾照政策，信用卡，电子提车凭证）
   */
  pickUpMaterials?: ExplainObject[] | null;
  /**
   * 租车必读（确认，取消，押金，里程，燃油，年龄，费用说明，限制出行区域）
   */
  carRentalMustRead?: ExplainObject[] | null;
  /**
   * 租车必读信息表
   */
  rentalMustReadTable?: CommonTable[] | null;
  /**
   * 租车必读图片
   */
  rentalMustReadPicture?: TitleAndDesc[] | null;
  /**
   * 是否出查询银联卡产品按钮
   */
  searchUnionPay?: boolean | null;
  /**
   * 是否出出查询信用卡产品按钮
   */
  searchCreditCard?: boolean | null;
  /**
   * 跨境跨岛政策（纯文案）
   */
  crossPolicy?: string | null;
  /**
   * 跨境跨岛政策（结构化）
   */
  crossIslandInfo?: CrossIslandInfo | null;
  /**
   * 自营险
   */
  platformInsurance?: ExtraInsurance | null;
  /**
   * 附加产品模块信息（关联Equipments节点）
   */
  addProductInfo?: AddProductInfo | null;
}
export interface AddProductInfo {
  /**
   * 状态 （1：不展示，2：无库存，空：展示）
   */
  status?: number | null;
  /**
   * 附加产品模块说明
   */
  desc?: string | null;
}
export interface ExtraInsurance {
  insurance?: OSDInsuranceInfo | null;
  notes?: string | null;
  url?: string | null;
  /**
   * 保险声明
   */
  safeguardStatement?: string | null;
  /**
   * 跳转链接的展示文案
   */
  linkStatement?: string | null;
  /**
   * 国际站自营险版本号
   */
  version?: string | null;
  /**
   * 模块名称
   */
  moduleTile?: string | null;
  /**
   * 是否需要邮编 1需要，0不需要
   */
  needZipCode?: number | null;
  /**
   * 投保须知，一段长文案
   */
  insuranceNotice?: string | null;
  /**
   * 保险条款，一个pdf链接
   */
  insuranceClauses?: string | null;
}
export interface OSDInsuranceInfo {
  name?: string | null;
  /**
   * 商品返回的自增id
   */
  id?: number | null;
  custumerTotalPrice?: number | null;
  custumerDailyPrice?: number | null;
  code?: string | null;
  platformName?: string | null;
  coverage?: string | null;
  coverageDesc?: string | null;
  coverDescList?: string[] | null;
  coverDetailList?: OSDCoverDetail[] | null;
  localTotalPrice?: number | null;
  localDailyPrice?: number | null;
  /**
   * 保险供应商币种
   */
  localCurrencyCode?: string | null;
  /**
   * 保额货币
   */
  coverageCurrency?: string | null;
  /**
   * 用户货币的保额
   */
  custumerCoverage?: number | null;
  /**
   * 保险短描述
   */
  desc?: string | null;
  /**
   * 长描述
   */
  longDesc?: string | null;
  noCoverageContent?: string[] | null;
  /**
   * 保险类型
   */
  typeCode?: string | null;
  days?: number | null;
  /**
   * 用户币种,验单使用
   */
  customerCurrencyCode?: string | null;
}
export interface OSDCoverDetail {
  title?: string | null;
  /**
   * 字描述加黑部分
   */
  subTitle?: string | null;
  /**
   * 字描述
   */
  subDesc?: string | null;
  /**
   * 保额对应的保障范围
   */
  descList?: string[] | null;
}
export interface CrossIslandInfo {
  /**
   * crossType(类型 0 跨岛 1跨洲 2跨国)
   */
  crossType?: number | null;
  crossIslandLocationList?: CrossIslandLocation[] | null;
  crossIslandPolicyList?: CrossIslandPolicy[] | null;
  /**
   * 已选择的跨岛信息 #号分割
   */
  selectedCrossIslands?: string | null;
  tips?: string | null;
}
export interface CrossIslandPolicy {
  policyTitle?: string | null;
  policyType?: number | null;
  policyDescription?: string | null;
}
export interface CrossIslandLocation {
  locationId?: string | null;
  locationName?: string | null;
  crossStatus?: number | null;
}
export interface ZhiMaInfo {
  supportZhiMa?: boolean | null;
  zhiMaScore?: number | null;
  desc?: string | null;
}
export interface IbuPackageItem {
  code?: string | null;
  name?: string | null;
  desc?: string | null;
  sortNum?: number | null;
}
export interface CtripInsuranceInfo {
  name?: string | null;
  quantity?: number | null;
  vendorServiceCode?: string | null;
  localDailyPrice?: number | null;
  localTotalPrice?: number | null;
  localCurrencyCode?: string | null;
  currentDailyPrice?: number | null;
  currentTotalPrice?: number | null;
  currentCurrencyCode?: string | null;
  description?: string[] | null;
  allTags?: SimpleObject[] | null;
  longDescription?: string[] | null;
  extDescription?: string[] | null;
  /**
   * 0-可购买，1-已含，2-赠送，3-不可选择
   */
  type?: number | null;
  /**
   * 相同group只出一个
   */
  group?: number | null;
  /**
   * 唯一code
   */
  uniqueCode?: string | null;
  /**
   * 提示话术
   */
  tips?: string | null;
  /**
   * 提示话术
   */
  insuranceDetailDescription?: InsuranceDetailDescription | null;
  /**
   * 用于展示在二级页面的描述信息
   */
  descriptionV2?: string[] | null;
}
export interface InsuranceDetailDescription {
  items?: InsuranceDetailDescriptionItem[] | null;
  foot?: string | null;
}
export interface InsuranceDetailDescriptionItem {
  title?: string | null;
  contents?: InsuranceDetailDescriptionItemContent[] | null;
  head?: string | null;
  foot?: string | null;
  pageTitle?: string | null;
  pageUrl?: string | null;
}
export interface InsuranceDetailDescriptionItemContent {
  type?: string | null;
  title?: string | null;
  content?: string | null;
}
export interface IbuEquipmentInfo {
  equipmentType?: number | null;
  maxCount?: number | null;
  equipmentCode?: string | null;
  equipmentName?: string | null;
  equipmentDesc?: string | null;
  ageFrom?: number | null;
  ageFromUnit?: string | null;
  ageTo?: number | null;
  ageToUnit?: string | null;
  localTotalPrice?: number | null;
  localDailyPrice?: number | null;
  localCurrencyCode?: string | null;
  currentCurrencyCode?: string | null;
  currentTotalPrice?: number | null;
  currentDailyPrice?: number | null;
  payMode?: number | null;
  /**
   * 唯一code
   */
  uniqueCode?: string | null;
}
export interface FlightDelayInfo {
  /**
   * 话术1 概述
   */
  outline?: string | null;
  /**
   * 话术2+3 保留政策+收费
   */
  keepHours?: string | null;
  /**
   * 话术4 取消政策
   */
  cancelRules?: string | null;
}
export interface IbuPriceInfo {
  /**
   * 当前币种车辆租金
   */
  currentCarPrice?: number | null;
  /**
   * 当前币种日均价
   */
  currentDailyPrice?: number | null;
  /**
   * 当前币种总价
   */
  currentTotalPrice?: number | null;
  /**
   * 当地币种车辆租金
   */
  localCarPrice?: number | null;
  /**
   * 当地币种日均价
   */
  localDailyPrice?: number | null;
  /**
   * 当地币种总价
   */
  localTotalPrice?: number | null;
  /**
   * 当地币种code
   */
  localCurrencyCode?: string | null;
  /**
   * 当前币种code
   */
  currentCurrencyCode?: string | null;
  /**
   * 当前币种异地还车费
   */
  currentOnewayfee?: number | null;
  /**
   * 当前币种到店支付费用
   */
  currentPoaPrice?: number | null;
  /**
   * 当前币种预付费用
   */
  currentPrepaidPrice?: number | null;
  /**
   * 当地币种异地还车费
   */
  localOnewayfee?: number | null;
  /**
   * 当地币种到店支付费用
   */
  localPoaPrice?: number | null;
  /**
   * 当地币种预付费用
   */
  localPrepaidPrice?: number | null;
  /**
   * 是否包含异地还车费
   */
  isContainOnewayFee?: boolean | null;
  /**
   * 支付方式
   */
  payMode?: number | null;
  /**
   * 产品套餐唯一标识
   */
  productId?: string | null;
  /**
   * 套餐id
   */
  packageId?: number | null;
  /**
   * 套餐类型 4-hertz预付
   */
  packageType?: number | null;
  /**
   * 信用卡描述
   */
  creditDescription?: string | null;
  /**
   * 扩展信息
   */
  vcExtendRequest?: IbuVcExtendRequest | null;
  /**
   * 汇率
   */
  exchangeRate?: number | null;
  /**
   * 里程限制
   */
  mileInfo?: IbuMileageInfo | null;
  /**
   * 确认信息
   */
  confirmInfo?: IbuConfirmInfo | null;
  /**
   * 取消规则
   */
  cancelRule?: IbuCancelRuleInfo | null;
  /**
   * 年龄限制
   */
  ageRestriction?: IbuAgeInfo | null;
  /**
   * 信用卡信息
   */
  creditCardInfo?: IbuCreditCardInfo | null;
  /**
   * 全量标签,type:1-正向，2-负向，3-营销
   */
  allTags?: SimpleObject[] | null;
  /**
   * 保险起赔额信息
   */
  insuranceDetails?: IbuInsuranceDetail[] | null;
  /**
   * 费用明细
   */
  chargeList?: IbuChargeDetailInfo[] | null;
  /**
   * 优惠券信息
   */
  promotionInfo?: IbuPromotionInfo | null;
  /**
   * 供应商优惠
   */
  vendorPromotionList?: IbuPromotionInfo[] | null;
  /**
   * 租车保障（车行险重构后不再返回）
   */
  rentalGuarantee?: CtripInsuranceInfo[] | null;
  /**
   * 车行险重构后节点
   */
  rentalGuaranteeV2?: RentalGuarantee | null;
  /**
   * 价格code 国内下单使用
   */
  priceCode?: string | null;
  /**
   * 卡拉比套餐id
   */
  pkgSellingRuleId?: number | null;
  packageLevel?: string | null;
  /**
   * 给订单落全部标签
   */
  allTagsForOrder?: SimpleObject[] | null;
}
export interface RentalGuarantee {
  rentalGuaranteeTitle?: string[] | null;
  vendorServiceDesc?: string | null;
  vendorServiceSubDesc?: string | null;
  packageDetailList?: PackageDetailV2[] | null;
  purchasingNotice?: ListText | null;
  vendorServiceDetail?: VendorServiceDetail[] | null;
  /**
   * 商品域版本号
   */
  claimSettlementVersion?: string | null;
  /**
   * 优享和尊享服务最多收取7天
   */
  insuranceMaxAddDesc?: string | null;
}
export interface VendorServiceDetail {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 1表示车辆事故处理流程,2表示车行服务理赔说明
   */
  type?: number | null;
  /**
   * 描述
   */
  description?: string | null;
  /**
   * 具体步骤
   */
  contents?: SimpleText[] | null;
}
export interface ListText {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 是否包含在套餐内
   */
  contains?: boolean | null;
  /**
   * 是否需要展示大拇指
   */
  great?: boolean | null;
  /**
   * 文案
   */
  content?: string[] | null;
  /**
   * 表格区域
   */
  subContent?: TableComplete[] | null;
  type?: string | null;
  /**
   * 每个保险项下方的灰色小字
   */
  subDesc?: string[] | null;
}
export interface TableComplete {
  title?: string | null;
  desc?: string | null;
  table?: TableArray[] | null;
  /**
   * 表格样式 ：0/不返回 - 纵向，灰色在左，1 - 横向，灰色在上
   */
  styleType?: number | null;
}
export interface TableArray {
  key?: string | null;
  value?: TableItem | null;
}
export interface TableItem {
  /**
   * 是否需要展示大拇指
   */
  great?: boolean | null;
  /**
   * 表格内文案
   */
  content?: string | null;
}
export enum IInsuranceCode {
  BAS = '1002', // 基础
  ADV = '2001', // 优享
  PRE = '2011', // 尊享
  PREP = 'PREP', // 一口价
  prep = 'prep', // 一口价
  EHI_BAS = '1002', // 一嗨基础
  ONE_PRICE = '2012', // 无忧租一口价
}

export interface PackageDetailV2 {
  name?: string | null;
  currentCurrencyCode?: string | null;
  currentDailyPrice?: number | null;
  gapPrice?: number | null;
  cityEncourage?: string | null;
  description?: PackageContains[] | null;
  descriptionV2?: PackageContains[] | null;
  allTags?: SimpleObjectV2[] | null;
  insuranceDetailDescription?: ListText[] | null;
  /**
   * 套餐code
   */
  uniqueCode?: IInsuranceCode;
  /**
   * 0未下单，1 待确认，2支付中，3已支付，4 不可加购, 5 支付失败（售后）
   */
  status?: number | null;
  /**
   * 0可选，1已含，2赠送
   */
  type?: number | null;
  /**
   * 保险升级去支付的价格（售后用）
   */
  price?: number | null;
  /**
   * 售后保险订单id
   */
  additionalId?: string | null;
  /**
   * 前端选中后的激励文案展示
   */
  selectedEncourage?: string | null;
  /**
   * 下单时对应的版本，目前0A版，1～3 B版，4B优化后版本
   */
  version?: number | null;
}
export interface SimpleObjectV2 {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 标题后缀
   */
  titleExtra?: string | null;
  /**
   * 标签类型
   */
  category?: number | null;
  /**
   * 导向类型
   */
  type?: number | null;
  /**
   * code
   */
  code?: string | null;
  /**
   * 类描述
   */
  typeDesc?: string | null;
  /**
   * 描述
   */
  description?: string | null;
  sortNum?: number | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 展示层级
   */
  showLayer?: number | null;
  /**
   * 颜色类型
   */
  colorCode?: string | null;
  subList?: SimpleObjectV2[] | null;
  /**
   * 标签code
   */
  labelCode?: string | null;
  /**
   * 位置code
   */
  positionCode?: string | null;
  /**
   * 标签groupCode
   */
  groupCode?: string | null;
  /**
   * 填写页重构标签分级
   */
  tagGroups?: number | null;
  /**
   * 填写页重构标签排序
   */
  tagSortNum?: number | null;
  /**
   * 标签展示分行, 用于分行，groupId一样的放在一行
   */
  amountTitle?: string | null;
  /**
   * 货架二期标签合并组，1 营销类 2车辆类，3服务类      货架2.0,共减XX
   */
  groupId?: number | null;
  /**
   * 标签合并Id， mergeId大于0 ， 且一样的情况需要合并在一起展示， 如无忧组情况下， 押金双
   * 免，免费取消标签需要合并在一起展示
   */
  mergeId?: number | null;
  /**
   * 延时免费留车标签前缀租车中心
   */
  prefix?: string | null;
}
export interface IbuPromotionInfo {
  /**
   * 1-立减，2-返现
   */
  type?: number | null;
  /**
   * 优惠券标题
   */
  title?: string | null;
  /**
   * 优惠券描述
   */
  description?: string | null;
  /**
   * 折扣百分比
   */
  deductionPercent?: number | null;
  /**
   * 优惠金额
   */
  deductionAmount?: number | null;
  /**
   * 优惠券号
   */
  code?: string | null;
  /**
   * 长标签
   */
  longTag?: string | null;
  /**
   * 长描述
   */
  longDesc?: string | null;
}
export interface IbuChargeDetailInfo {
  code?: string | null;
  name?: string | null;
  desc?: string | null;
  quantity?: number | null;
  payMode?: number | null;
  /**
   * 不含税价格
   */
  netAmount?: number | null;
  /**
   * 含税价格
   */
  dueAmount?: number | null;
  currency?: string | null;
  /**
   * 是否包含在总价里
   */
  isIncludedInRate?: boolean | null;
}
export interface IbuInsuranceDetail {
  /**
   * 保险code
   */
  code?: string | null;
  /**
   * 保险名称
   */
  name?: string | null;
  /**
   * 货币code
   */
  currencyCode?: string | null;
  /**
   * 最小起赔额
   */
  minExcess?: number | null;
  /**
   * 最大起赔额，minExcess=maxExcess 表示起赔额为固定值
   */
  maxExcess?: number | null;
  /**
   * 最小保额
   */
  minCoverage?: number | null;
  /**
   * 最大保额，minCoverage=maxCoverage 表示保额为固定值
   */
  maxCoverage?: number | null;
}
export interface IbuCreditCardInfo {
  description?: string | null;
  cardList?: IbuCreditCardItem[] | null;
  depositCurrencyCode?: string | null;
  maxDeposit?: number | null;
  minDeposit?: number | null;
}
export interface IbuCreditCardItem {
  name?: string | null;
  type?: string | null;
  payWaysName?: string | null;
}
export interface IbuAgeInfo {
  description?: string | null;
  minDriverAge?: number | null;
  maxDriverAge?: number | null;
  youngDriverAge?: number | null;
  oldDriverAge?: number | null;
  youngDriverAgeDesc?: string | null;
  oldDriverAgeDesc?: string | null;
  licenceAge?: number | null;
  licenceAgeDesc?: string | null;
  youngDriverExtraFee?: IbuExtraFee | null;
  oldDriverExtraFee?: IbuExtraFee | null;
}
export interface IbuExtraFee {
  localCurrencyCode?: string | null;
  currentPrice?: number | null;
  localPrice?: number | null;
  /**
   * 0:day; 1:total.
   */
  feeType?: number | null;
}
export interface IbuCancelRuleInfo {
  /**
   * 是否全损
   */
  isTotalLoss?: boolean | null;
  /**
   * 是否免费取消
   */
  isFreeCancel?: boolean | null;
  /**
   * 当前是否免费取消
   */
  isFreeCancelNow?: boolean | null;
  /**
   * 免费取消时间
   */
  hours?: number | null;
  /**
   * 取消描述
   */
  cancelDescription?: string | null;
  /**
   * 取消规则激励话术
   */
  cancelEncourage?: string | null;
}
export interface IbuConfirmInfo {
  /**
   * 确认标题
   */
  confirmTitle?: string | null;
  /**
   * 确认描述
   */
  confirmDesc?: string | null;
  /**
   * 是否立即确认
   */
  confirmRightNow?: boolean | null;
  /**
   * 确认时间
   */
  confirmTime?: number | null;
}
export interface IbuMileageInfo {
  name?: string | null;
  /**
   * 是否限制里程
   */
  isLimited?: boolean | null;
  /**
   * 距离
   */
  distance?: number | null;
  /**
   * 距离单位
   */
  distanceUnit?: string | null;
  /**
   * 里程限制的时间单位,W：周，D：天，H：小时，P：租车期间
   */
  periodUnit?: string | null;
  /**
   * 收费金额
   */
  chargeAmount?: number | null;
  /**
   * 货币单位
   */
  chargeUnit?: string | null;
  /**
   * 数字
   */
  quantity?: number | null;
  /**
   * 里程描述(套餐包含项)
   */
  desc?: string | null;
  /**
   * 里程附加描述
   */
  mileAgeDesc?: string | null;
}
export interface IbuVcExtendRequest {
  responsePickUpLocationId?: string | null;
  responseReturnLocationId?: string | null;
  vendorVehicleId?: string | null;
}
export interface AdditionalCombination {
  /**
   * 套餐code
   */
  bomCode?: string | null;
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 包含项
   */
  codes?: string[] | null;
  /**
   * 描述
   */
  description?: string | null;
  /**
   * 货币
   */
  currency?: string | null;
  /**
   * 天价
   */
  dayPrice?: number | null;
  /**
   * 与最低价差价
   */
  gapPrice?: number | null;
  /**
   * 与上一层差价
   */
  stepPrice?: number | null;
  /**
   * 是否隐藏
   */
  hike?: boolean | null;
  /**
   * 总价
   */
  totalPrice?: number | null;
  /**
   * 最低价支付方式
   */
  payMode?: number | null;
  /**
   * 最低价支付方式
   */
  packageId?: number | null;
}
export interface InsuranceItem {
  code?: string | null;
  name?: string | null;
  /**
   * 外层展示
   */
  description?: string | null;
  /**
   * 保险详情激励话术标题
   */
  shortDescription?: string | null;
  /**
   * 保险详情激励话术文案
   */
  longDescription?: string | null;
  isInclude?: boolean | null;
  isFromCtrip?: boolean | null;
  insuranceTips?: SimpleObject[] | null;
  insuranceDetail?: PriceInsuranceDetail[] | null;
  /**
   * 自营险字段
   */
  converageExplain?: ExplainObject | null;
  /**
   * 自营险字段
   */
  unConverageExplain?: ExplainObject | null;
  /**
   * 自营险字段
   */
  claimProcess?: ExplainObject | null;
  insuranceGuaranteeUrl?: string | null;
  itemUrl?: string | null;
  claimMaterial?: string[] | null;
  /**
   * 自营险id
   */
  productId?: number | null;
  /**
   * 保险所属的分组
   */
  groupCode?: string | null;
  /**
   * 保额表格
   */
  coverDetailList?: OSDCoverDetail[] | null;
  /**
   * 投保须知
   */
  insuranceNotice?: string | null;
  /**
   * 保险条款
   */
  insuranceClauses?: string | null;
  /**
   * 起赔额表格
   */
  excessTable?: ExplainObject | null;
}
export interface PriceInsuranceDetail {
  packageId?: number | null;
  currencyCode?: string | null;
  minExcess?: number | null;
  maxExcess?: number | null;
  excessShortDesc?: string | null;
  excessLongDesc?: string | null;
  minCoverage?: number | null;
  maxCoverage?: number | null;
  coverageShortDesc?: string | null;
  coverageLongDesc?: string | null;
  /**
   * 带自营险的保险描述：原起配额100元
   */
  coverageWithPlatformInsurance?: string | null;
  /**
   * 不带自营险的起配额
   */
  coverageWithoutPlatformInsurance?: string | null;
  /**
   * 自营险二期新增字段，例：起赔额$100(约￥800)
   */
  coverageWithoutPlatformInsuranceV2?: string | null;
  /**
   * 出境自营险二期新增字段，例：起赔额$100(约￥800)及以下部分由国内保险公司承担
   */
  coverageWithPlatformInsuranceV2?: string | null;
}
export interface PackageInfo {
  /**
   * 打包套餐ID
   */
  insPackageId?: number | null;
  /**
   * 是否是默认选中的套餐
   */
  isDefault?: boolean | null;
  /**
   * 套餐名称
   */
  packageName?: string | null;
  /**
   * 货币
   */
  currencyCode?: string | null;
  /**
   * 精选产品
   */
  defaultBomCode?: string | null;
  /**
   * 默认套餐
   */
  defaultPackageId?: number | null;
  /**
   * 保障程度
   */
  guaranteeDegree?: number | null;
  /**
   * 是否是裸价套餐
   */
  naked?: boolean | null;
  /**
   * 打包套餐包含保险名称
   */
  insuranceNames?: string[] | null;
  /**
   * 当前套餐最低价
   */
  lowestDailyPrice?: number | null;
  /**
   * 与最低价套餐差价
   */
  gapPrice?: number | null;
  /**
   * 与上一级套餐差价
   */
  stepPrice?: number | null;
  /**
   * 无忧租type
   */
  subType?: number | null;
  /**
   * 无忧租tag
   */
  easyLifeTag?: SimpleObject[] | null;
  /**
   * 无忧租套餐描述名
   */
  descTitle?: string | null;
  /**
   * 无忧租套餐描述内容
   */
  description?: string | null;
  /**
   * 打包套餐提示
   */
  packageTips?: SimpleObject[] | null;
  /**
   * packageType =4 赫兹预付，0其他
   */
  packageType?: number | null;
  /**
   * 出境自营险二期激励弹窗文案
   */
  excessEncourage?: string | null;
  /**
   * 不推荐文案，高亮提示
   */
  noticeDescTitle?: string | null;
  /**
   * 是否优享套餐
   */
  isYouXiang?: number | null;
  /**
   * 是否为加购自营险前的套餐
   */
  isBasic?: number | null;
  /**
   * 优享和基础间的差价
   */
  youXiangGapPrice?: number | null;
}
export interface CtqCommentInfo {
  /**
   * 评分等级
   */
  level?: string | null;
  /**
   * 供应商描述
   */
  vendorDesc?: string | null;
  /**
   * 评论数量
   */
  commentCount?: number | null;
  /**
   * 去哪点评数
   */
  qCommentCount?: number | null;
  /**
   * 外露分
   */
  qExposed?: string | null;
  /**
   * 评分
   */
  overallRating?: string | null;
  /**
   * 最大评分
   */
  maximumRating?: number | null;
  /**
   * 点评标签
   */
  commentLabel?: string | null;
  /**
   * 是否有点评,1：有点评，0：无点评
   */
  hasComment?: number | null;
  /**
   * 无点评描述
   */
  noComment?: string | null;
  /**
   * 点评列表链接
   */
  link?: string | null;
  /**
   * 外露点评列表
   */
  commentList?: CommentDetailInfo[] | null;
}
export interface CommentDetailInfo {
  /**
   * 用户头像
   */
  avatar?: string | null;
  /**
   * 用户名
   */
  userName?: string | null;
  /**
   * 用户打分
   */
  userRate?: number | null;
  /**
   * 用户打分等级
   */
  userRateLevel?: number | null;
  /**
   * 点评内容
   */
  content?: string | null;
}
export interface ModifyOrderInfo {
  orderCurrentTotalPrice?: number | null;
  orderLocalTotalPrice?: number | null;
  orderCurrentPrepaidPrice?: number | null;
  orderCurrentPoaPrice?: number | null;
  orderLocalPoaPrice?: number | null;
  orderCustomerCurrency?: string | null;
  orderLocalCurrency?: string | null;
  payMode?: number | null;
  couponCode?: string | null;
  cost?: number | null;
}
export interface FlightDelayRetentionRules {
  title?: string | null;
  description?: string | null;
  subDesc?: string | null;
  rules?: Rule[] | null;
  tips?: string[] | null;
}
export interface Rule {
  code?: string | null;
  title?: string | null;
  subTitle?: string | null;
  descs?: string[] | null;
}
export interface CtqVehicleInfo {
  /**
   * 品牌id
   */
  brandId?: number | null;
  /**
   * 品牌英文名
   */
  brandEName?: string | null;
  /**
   * 品牌名
   */
  brandName?: string | null;
  /**
   * 车型名称
   */
  name?: string | null;
  /**
   * 车型中文名称
   */
  zhName?: string | null;
  /**
   * 车型code
   */
  vehicleCode?: string | null;
  /**
   * 车型图
   */
  imageUrl?: string | null;
  /**
   * 车型组逻辑code
   */
  groupCode?: string | null;
  /**
   * 车型组code
   */
  groupSubClassCode?: string | null;
  /**
   * 车型组名称
   */
  groupName?: string | null;
  /**
   * 发动机类型
   */
  transmissionType?: number | null;
  /**
   * 发动机类型名称
   */
  transmissionName?: string | null;
  /**
   * 乘客数量
   */
  passengerNo?: number | null;
  /**
   * 车门数量
   */
  doorNo?: number | null;
  /**
   * 行李数量
   */
  luggageNo?: number | null;
  /**
   * 排量信息
   */
  displacement?: string | null;
  /**
   * 结构
   */
  struct?: string | null;
  /**
   * 汽油
   */
  fuel?: string | null;
  /**
   * 变速箱
   */
  gearbox?: string | null;
  /**
   * 驾驶模式
   */
  driveMode?: string | null;
  /**
   * 款式
   */
  style?: string | null;
  /**
   * 图片列表
   */
  imageList?: string[] | null;
  /**
   * 用户实拍图片列表
   */
  userRealImageList?: string[] | null;
  /**
   * 用户实拍数量
   */
  userRealImageCount?: number | null;
  /**
   * 门店实拍图片列表
   */
  storeRealImageList?: string[] | null;
  /**
   * 相似车型图片列表
   */
  similarImageList?: string[] | null;
  /**
   * 指定车型图片列表
   */
  specializedImages?: string[] | null;
  /**
   * 车型视频（国内）
   */
  vedio?: string | null;
  /**
   * 是否指定车型，只有详情页会有
   */
  isSpecialized?: boolean | null;
  /**
   * 是否热销
   */
  isHot?: boolean | null;
  /**
   * 车型推荐描述
   */
  recommendDesc?: string | null;
  /**
   * 是否有空调
   */
  hasConditioner?: boolean | null;
  /**
   * 空调描述
   */
  conditionerDesc?: string | null;
  /**
   * 空间描述
   */
  spaceDesc?: string | null;
  /**
   * 相似车型描述
   */
  similarCommentDesc?: string | null;
  /**
   * 相似车型-移到product节点兼容已上线，后期不维护
   */
  vendorSimilarVehicleInfos?: CtqVendorSimilarVehicleInfo[] | null;
  /**
   * 国内车型配置图
   */
  vehicleAccessoryImages?: string[] | null;
  /**
   * 牌照
   */
  license?: string | null;
  /**
   * 牌照样式
   */
  licenseStyle?: string | null;
  /**
   * 牌照不限行文案
   */
  licenseDescription?: string | null;
  /**
   * 实拍图
   */
  realityImageUrl?: string | null;
  /**
   * 车型图集
   */
  sourcePicInfos?: SourcePictureInfo[] | null;
  /**
   * 行李容积
   */
  luggageSize?: string | null;
  /**
   * 年款
   */
  modeYear?: string | null;
  /**
   * 版本
   */
  versionName?: string | null;
  /**
   * 车型等级
   */
  vehicleLevel?: string | null;
  /**
   * 燃油编号
   */
  fuelNew?: string | null;
  /**
   * 动力类型（9：纯电动,1:曾程式 2：插电式混合动力 3：柴油 4：氢气 5：汽油 6：汽油+24v
   * 轻混  7：汽油+48v轻混  8：邮电混合10：其他）
   */
  oilType?: number | null;
  /**
   * 子车型组名称
   */
  groupSubName?: string | null;
  /**
   * 车型视频封面，与vedio相对应
   */
  cover?: string | null;
  /**
   * 车型推荐标签集合
   */
  recommendLabels?: LabelDetail[] | null;
  /**
   * 纯电动、增程式、插电式
   */
  fuelType?: string | null;
  /**
   * 后备箱容积：可放{值}个24寸行李箱
   */
  luggageNum?: string | null;
  /**
   * 巡航系统
   */
  guidSys?: string | null;
  /**
   * 手机互联
   */
  carPlay?: string | null;
  /**
   * 充电口
   */
  chargeInterface?: string | null;
  /**
   * 天窗
   */
  skylight?: string | null;
  /**
   * 续航{min-max}km
   */
  endurance?: string | null;
  /**
   * 快充{quick}小时，慢充{slow}小时
   */
  charge?: string | null;
  /**
   * 是否支持自动泊车
   */
  autoPark?: boolean | null;
  /**
   * 是否支持蓝牙
   */
  carPhone?: boolean | null;
  /**
   * 是否支持无钥匙启动
   */
  autoStart?: boolean | null;
  /**
   * 是否支持自动驻车
   */
  autoBackUp?: boolean | null;
  /**
   * 次车型组逻辑code（不是真实车型组，是特殊条件筛选出的一组车型，如电动车）
   */
  subGroupCode?: string | null;
  /**
   * vr页面地址
   */
  vr?: string | null;
  /**
   * 车型推荐id
   */
  vehiclesSetId?: string | null;
  /**
   * 车型组范围
   */
  groupNameScope?: string[] | null;
  /**
   * 多媒体相册信息
   */
  multimediaAlbums?: MultimediaAlbum[] | null;
  /**
   * 2、视频  3、VR （支持的多媒体类型，用于列表页展示多媒体图标）
   */
  mediaTypes?: number[] | null;
  /**
   * 车型唯一标识
   */
  vehicleKey?: string | null;
  /**
   * 驱动类型，二期新增字段，0：非四驱，1：四驱
   */
  driveType?: string | null;
  /**
   * 二期新增燃油类型字段
   */
  fuelMode?: string | null;
  /**
   * 自动泊车文案
   */
  autoParkDesc?: KeyAndValue | null;
  /**
   * 自动驻车文案
   */
  autoBackupDesc?: KeyAndValue | null;
  /**
   * 蓝牙文案
   */
  carPhoneDesc?: KeyAndValue | null;
  /**
   * 无钥匙启动文案
   */
  autoStartDesc?: KeyAndValue | null;
  /**
   * trip全部车型置顶车型
   */
  tripTopVehicle?: boolean | null;
}
export interface MultimediaAlbum {
  /**
   * 相册名称
   */
  albumName?: string | null;
  /**
   * 相册提示
   */
  note?: string | null;
  /**
   * 相册类型 1、官方相册  2、门店实拍（供应商）相册  3、用户实拍相册
   */
  albumType?: number | null;
  /**
   * 媒体组
   */
  mediaGroup?: MediaGroup[] | null;
  /**
   * 相册菜单导航
   */
  albumMenus?: AlbumMenus[] | null;
}
export interface AlbumMenus {
  /**
   * 菜单名称
   */
  menuName?: string | null;
  /**
   * 排序
   */
  sortNum?: number | null;
  /**
   * 菜单绑定的媒体组类型：-1 : 代表全部
   */
  mediaGroups?: number[] | null;
}
export interface MediaGroup {
  /**
   * 1、封面  2、视频   3、VR  4、外观   5、前排  6、后排   7、相册 （跳转到相册
   * 落地页）
   */
  groupType?: number | null;
  /**
   * 媒体组名称
   */
  groupName?: string | null;
  /**
   * 媒体组排序
   */
  groupSortNum?: number | null;
  /**
   * 媒体
   */
  medias?: Media[] | null;
}
export interface Media {
  /**
   * 媒体类型      1、图片  2、视频  3、VR
   */
  type?: number | null;
  url?: string | null;
  /**
   * 封面
   */
  cover?: string | null;
  /**
   * 排序
   */
  sortNum?: number | null;
}
export interface SourcePictureInfo {
  source?: number | null;
  type?: number | null;
  sourceName?: string | null;
  picList?: VehiclePicture[] | null;
}
export interface VehiclePicture {
  imageUrl?: string | null;
  imageClass?: number | null;
  imageType?: number | null;
  sortNum?: number | null;
}
export interface CtqVendorSimilarVehicleInfo {
  bizVendorCode?: string | null;
  vendorName?: string | null;
  vendorLogo?: string | null;
  similarVehicleInfos?: CtqSimilarVehicleInfo[] | null;
}
export interface CtqSimilarVehicleInfo {
  vehicleCode?: string | null;
  vehicleName?: string | null;
  vehicleImageUrl?: string | null;
}
export interface CtqVendorInfo {
  /**
   * 供应商id
   */
  bizVendorCode?: string | null;
  /**
   * 供应商名称
   */
  vendorName?: string | null;
  /**
   * 供应商图片地址
   */
  vendorImageUrl?: string | null;
  /**
   * 供应商code
   */
  vendorCode?: string | null;
  /**
   * 是否平台供应商
   */
  isBroker?: boolean | null;
  /**
   * 平台code
   */
  platformCode?: string | null;
  /**
   * 平台名称
   */
  platformName?: string | null;
  /**
   * 是否可用优惠券
   */
  haveCoupon?: boolean | null;
  /**
   * 全球连锁，当地连锁
   */
  vendorTag?: SimpleObject | null;
  preAuthType?: number | null;
  supportPreAuth?: number | null;
  prePayType?: number | null;
  /**
   * 卡拉比供应商ID
   */
  supplierId?: number | null;
  /**
   * 供应商全称
   */
  vendorFullName?: string | null;
  /**
   * 供应商营业执照
   */
  vendorLicenseUrl?: string | null;
  /**
   * 真实供应商名称，盲盒产品会覆盖vendorName ，所以这里建了一个实际的供应商名称， 用户后续下
   * 单使用
   */
  realVendorName?: string | null;
}
export interface CtqStoreInfo {
  /**
   * 门店code
   */
  storeCode?: string | null;
  /**
   * 供应商id
   */
  bizVendorCode?: string | null;
  /**
   * 门店电话
   */
  telephone?: string | null;
  /**
   * 门店名称
   */
  storeName?: string | null;
  /**
   * 门店地址
   */
  address?: string | null;
  /**
   * 门店经度
   */
  longitude?: number | null;
  /**
   * 门店纬度
   */
  latitude?: number | null;
  /**
   * 门店图片地址
   */
  mapUrl?: string | null;
  /**
   * 门店取还车指引
   */
  storeGuild?: string | null;
  /**
   * 门店取还车指引(取还车独立)
   */
  storeGuildSplit?: string | null;
  /**
   * 门店位置
   */
  storeLocation?: string | null;
  /**
   * 门店到达方式
   */
  storeWay?: string | null;
  /**
   * 门店营业时间
   */
  workTime?: CtqStoreWorkTimeInfo | null;
  /**
   * 门店服务
   */
  storeServiceList?: CtqStoreServiceInfo[] | null;
  /**
   * 门店类型（1门店，2送车点，4接送点）
   */
  storeType?: number | null;
  /**
   * 接送点地址
   */
  shuttlePointAddress?: string | null;
  /**
   * 接送点名称
   */
  shuttlePointName?: string | null;
  /**
   * 接送点营业时间
   */
  shuttlePointWorkTime?: CtqStoreWorkTimeInfo | null;
  /**
   * 接送点经度
   */
  shuttlePointLongitude?: number | null;
  /**
   * 接送点纬度
   */
  shuttlePointLatitude?: number | null;
  /**
   * 国家id
   */
  countryId?: number | null;
  /**
   * 国家名称
   */
  countryName?: string | null;
  /**
   * 大洲id
   */
  provinceId?: number | null;
  /**
   * 大洲名称
   */
  provinceName?: string | null;
  /**
   * 城市id
   */
  cityId?: number | null;
  /**
   * 城市名称
   */
  cityName?: string | null;
  /**
   * 是否机场
   */
  isAirportStore?: boolean | null;
  /**
   * 与取车地点的距离
   */
  distance?: string | null;
  /**
   * 拥有车型数量
   */
  productCount?: number | null;
  /**
   * 该门店最低价
   */
  lowestPrice?: number | null;
  /**
   * 用户评分信息 兼容地图模式 与product.commentInfo冗余
   */
  commentInfo?: CtqCommentInfo | null;
  /**
   * 1-取车门店，2-还车门店，3-取还车门店
   */
  type?: number | null;
  /**
   * isd
   */
  isrentcent?: number | null;
  pickUpLevel?: number | null;
  pickOffLevel?: number | null;
  pickUpOnDoor?: boolean | null;
  returnOnDoor?: boolean | null;
  sendTypeForPickUpCar?: number | null;
  sendTypeForPickOffCar?: number | null;
  psend?: number | null;
  rsend?: number | null;
  freeShuttle?: boolean | null;
  scoreInfo?: ScoreInfo | null;
  /**
   * 取/还车方式
   */
  wayInfo?: number | null;
  /**
   * 取还车方式展示类型  0 取还车分开展示  1 取还车合并展示
   */
  showType?: number | null;
  /**
   * 距离描述
   */
  distanceDesc?: string | null;
  /**
   * 供应商城市code
   */
  vendorCityCode?: string | null;
  /**
   * 供应商门店代码
   */
  vendorStoreCode?: string | null;
  /**
   * 洲ID（仅海外）
   */
  continentId?: number | null;
  /**
   * 洲名称
   */
  continentName?: string | null;
  /**
   * 标签
   */
  tags?: SimpleObject[] | null;
  /**
   * 门店所在机场名
   */
  storeAirportName?: string | null;
}
export interface ScoreInfo {
  store?: number | null;
  car?: number | null;
  suport?: number | null;
  clean?: number | null;
  exposed?: number | null;
}
export interface CtqStoreServiceInfo {
  /**
   * 服务名称
   */
  title?: string | null;
  /**
   * 服务描述
   */
  description?: string | null;
  /**
   * 服务类型code
   */
  typeCode?: string | null;
}
export interface CtqStoreWorkTimeInfo {
  /**
   * 营业时间描述
   */
  openTimeDesc?: string | null;
  /**
   * 开始营业时间
   */
  openTime?: string | null;
  /**
   * 结束营业时间
   */
  closeTime?: string | null;
  /**
   * 营业外时间描述
   */
  description?: string | null;
  /**
   * 全量营业时间 费用说明信息
   */
  fullDescription?: string | null;
  /**
   * trip详情页收费营业时间描述
   */
  chargeDescription?: string | null;
  /**
   * 付费时段的具体描述
   */
  chargeDetailDescription?: string | null;
  /**
   * 营业时间政策
   */
  businessTimePolicy?: ExplainObject | null;
}
export interface TitleAndDesc {
  title?: string | null;
  desc?: string | null;
  detail?: TitleWithType[] | null;
}
export interface TitleWithType {
  title?: string | null;
  desc?: string | null;
  type?: string | null;
}
export interface CtqRentCenter {
  id?: number | null;
  name?: string | null;
  icon?: string | null;
  backImage?: string | null;
  images?: string[] | null;
  /**
   * 展示位置
   */
  index?: number | null;
  /**
   * 是否新版租车中心
   */
  isNew?: number | null;
  /**
   * 租车中心地址
   */
  address?: string | null;
  /**
   * 租车中心标签
   */
  labels?: string[] | null;
  /**
   * 纬度
   */
  lat?: string | null;
  /**
   * 经度
   */
  lng?: string | null;
  /**
   * 筛选code
   */
  filterCode?: string | null;
  fromTime?: string | null;
  toTime?: string | null;
  /**
   * 城市id
   */
  cityId?: number | null;
}
export interface WholeDayPriceInfo {
  /**
   * 整日租期总价
   */
  totalPrice?: number | null;
  /**
   * 整日租期还车日期
   */
  returnDate?: string | null;
  /**
   * 使用的优惠券
   */
  couponCode?: string | null;
  /**
   * 整日租期优惠后总价
   */
  actualTotalPrice?: number | null;
}
export interface ResponseStatusType {
  timestamp?: string | null;
  ack?: AckCodeType | null;
  errors?: ErrorDataType[] | null;
  build?: string | null;
  version?: string | null;
  extension?: ExtensionType[] | null;
  /**
   * 描述信息
   */
  responseDesc?: string | null;
  userID?: string | null;
  msg?: string | null;
  /**
   * 响应编码（20000：成功）
   */
  responseCode?: number | null;
  code?: string | null;
  reason?: string | null;
}
export interface ExtensionType {
  /**
   * ExtensionType
   */
  id?: string | null;
  /**
   * ExtensionType
   */
  version?: string | null;
  /**
   * ExtensionType
   */
  contentType?: string | null;
  /**
   * ExtensionType
   */
  value?: string | null;
}
export interface ErrorDataType {
  message?: string | null;
  /**
   * A unique code that identifies the particular error
   *  condition that occurred.
   */
  errorCode?: string | null;
  /**
   * ErrorDataType
   */
  stackTrace?: string | null;
  /**
   * ErrorDataType
   */
  severityCode?: SeverityCodeType | null;
  /**
   * ErrorDataType
   */
  errorFields?: ErrorFieldType | null;
  /**
   * ErrorDataType
   */
  errorClassification?: ErrorClassificationCodeType | null;
}
export interface ErrorFieldType {
  /**
   * ErrorFieldType
   */
  fieldName?: string | null;
  /**
   * ErrorFieldType
   */
  errorCode?: string | null;
  /**
   * ErrorFieldType
   */
  message?: string | null;
}
export interface BaseResponse {
  isSuccess?: boolean | null;
  code?: string | null;
  returnMsg?: string | null;
  /**
   * 前端没有使用， 待确认
   */
  requestId?: string | null;
  cost?: number | null;
  showMessage?: string | null;
  extMap?: { [key: string]: string } | null;
  /**
   * tags for es
   */
  extraIndexTags?: { [key: string]: string } | null;
  apiResCodes?: string[] | null;
  hasResult?: boolean | null;
  /**
   * shopping返回错误code，埋点用
   */
  errorCode?: string | null;
  /**
   * shopping返回错误message，埋点用
   */
  message?: string | null;
}
export interface ShareExtInfo {
  /**
   * 分享时间
   */
  shareTime?: string | null;
}
export interface CtqQueryPoint {
  locationType?: number | null;
  cityId?: number | null;
  locationCode?: string | null;
  locationName?: string | null;
  poi?: CtqPoiInfo | null;
  date?: string | null;
  storeCode?: string | null;
  /**
   * 地址
   */
  address?: string | null;
  /**
   * 取车接送
   */
  doorToDoor?: DoorToDoor | null;
  /**
   * 上面送车圈（国内使用）
   */
  pickUpLevel?: number | null;
  /**
   * 上面拿车圈（国内使用）
   */
  pickOffLevel?: number | null;
  /**
   * 是否上门送车（国内使用）
   */
  pickupOnDoor?: boolean | null;
  /**
   * 是否上门拿车（国内使用）
   */
  dropOffOnDoor?: boolean | null;
  /**
   * 汇合点id
   */
  meetingPointId?: number | null;
  /**
   * poi 子类型 普通汇合点1 租车中心汇合点 :2
   */
  subPoiType?: number | null;
}
export interface DoorToDoor {
  /**
   * 是否需要
   */
  need?: boolean | null;
  /**
   * 上门车圈
   */
  level?: number | null;
  /**
   * 接送类型
   */
  type?: number | null;
}
export interface CtqPoiInfo {
  longitude?: number | null;
  latitude?: number | null;
  radius?: number | null;
}
export interface Reference {
  bizVendorCode?: string | null;
  vendorCode?: string | null;
  pStoreCode?: string | null;
  rStoreCode?: string | null;
  vehicleCode?: string | null;
  /**
   * 无忧租id
   */
  packageId?: string | null;
  packageType?: number | null;
  vcExtendRequest?: VcExtendRequest | null;
  decoratorVendorType?: number | null;
  easyLifeUpgradePackageId?: number | null;
  isEasyLife?: boolean | null;
  /**
   * 是否返回报价信息
   */
  withPrice?: boolean | null;
  packageId4CutPrice?: number | null;
  payMode?: number | null;
  bomCode?: string | null;
  productCode?: string | null;
  rateCode?: string | null;
  subType?: number | null;
  comPriceCode?: string | null;
  priceVersion?: string | null;
  priceVersionOfLowestPrice?: string | null;
  pCityId?: number | null;
  rCityId?: number | null;
  vendorVehicleCode?: string | null;
  age?: number | null;
  /**
   * 是否芝麻免押(0：否 1：是)
   */
  alipay?: boolean | null;
  /**
   * 芝麻信用押金全免(0:免租车押金 1：押金全免)
   */
  aType?: number | null;
  /**
   * 供应商是否支持免押
   */
  vendorSupportZhima?: boolean | null;
  /**
   * 爆款类型
   */
  hotType?: number | null;
  /**
   * 价格类型
   */
  priceType?: number | null;
  /**
   * 车型等级
   */
  vehicleDegree?: string | null;
  /**
   * 证件类型
   */
  idType?: number | null;
  fType?: number | null;
  rentCenterId?: number | null;
  /**
   * 是否携程精选
   */
  isSelect?: boolean | null;
  /**
   * 银联卡搜索（不支持为null）
   */
  unionCardFilter?: FilterItem | null;
  /**
   * 免押搜索（不支持为null）
   */
  noDepositFilter?: FilterItem | null;
  /**
   * 国内列表页标签带入填写页
   */
  labels?: SimpleObject[] | null;
  /**
   * 国内到达方式
   */
  pStoreNav?: string | null;
  rStoreNav?: string | null;
  /**
   * 是否上门取还车
   */
  pickUpOnDoor?: boolean | null;
  dropOffOnDoor?: boolean | null;
  /**
   * shopping使用的取车方式聚合字段
   */
  pickWayInfo?: number | null;
  /**
   * shopping使用的还车方式聚合字段
   */
  returnWayInfo?: number | null;
  /**
   * 送车上门类型
   */
  sendTypeForPickUpCar?: number | null;
  /**
   * 上门取车类型
   */
  sendTypeForPickOffCar?: number | null;
  hot?: number | null;
  /**
   * 供应商是否免违章押金
   */
  freeIllegalDeposit?: boolean | null;
  /**
   * 是否程信分免租车押金
   */
  creditFreeCarDeposit?: boolean | null;
  /**
   * 国内填写页分享需要
   */
  isdShareInfo?: IsdShareInfo | null;
  /**
   * 国内调价版本号
   */
  adjustVersion?: string | null;
  /**
   * 货架id
   */
  gsId?: number | null;
  /**
   * 是否是不需要限价（1：不需要）
   */
  noLp?: number | null;
  /**
   * 是否是电动车(0:否，1：是)
   */
  elct?: number | null;
  /**
   * 货架新版推荐语
   */
  gsDesc?: string | null;
  /**
   * 取车租车中心Id
   */
  pRc?: number | null;
  /**
   * 还车租车中心Id
   */
  rRc?: number | null;
  /**
   * 卡拉比skuId
   */
  skuId?: number | null;
  /**
   * 卡拉比套餐id
   */
  klbPId?: number | null;
  /**
   * 是否卡拉比(0-否，1-是)
   */
  klb?: number | null;
  /**
   * 商品使用的取车方式
   */
  pCType?: number | null;
  /**
   * 商品使用的还车方式
   */
  rCType?: number | null;
  /**
   * 取车圈id
   */
  pLevel?: number | null;
  /**
   * 还车圈id
   */
  rLevel?: number | null;
  /**
   * 优惠id
   */
  promtId?: number | null;
  /**
   * 是否领券订(0：否，1：是)
   */
  rCoup?: number | null;
  /**
   * 车型四字码
   */
  sippCode?: string | null;
  /**
   * 排序信息
   */
  sortInfo?: { [key: string]: string } | null;
  /**
   * 商品分级，ab实验埋点信息
   */
  vehicleLevelInfo?: VehicleLevelInfo | null;
  /**
   * 是否是新能源(0:否，1：是)
   */
  newEnergy?: number | null;
  /**
   * 平台（0-直连，10-PMS）
   */
  platform?: number | null;
  /**
   * 卡拉比取车门店id
   */
  kPSId?: number | null;
  /**
   * 卡拉比还车门店id
   */
  kRSId?: number | null;
  /**
   * 卡拉比服务商id
   */
  kVId?: number | null;
  /**
   * 携程取车圈Id
   */
  pLev?: number | null;
  /**
   * 携程还车圈Id
   */
  rLev?: number | null;
  /**
   * 1-卡拉比数据源
   */
  klbVersion?: number | null;
  /**
   * 卡拉比标准产品id
   */
  kVehicleId?: number | null;
  /**
   * 套餐价格信息集合（请求多套餐报价使用）
   */
  packagePriceInfos?: PackagePriceInfo[] | null;
  /**
   * 异门店Code
   */
  diffStoreCode?: string | null;
  /**
   * 产品类型 0 普通产品 1盲盒产品
   */
  productType?: number | null;
  /**
   * 盲盒需要的信息，用于接口带参，需要加密，服务端使用
   */
  secretBoxParam?: SecretBoxParam | null;
  /**
   * 库存等级（商品）
   */
  stockLevel?: string | null;
  /**
   * 报价信息Id， pkgSellingRuleID
   */
  pkgRuleId?: number | null;
  /**
   * 调价规则id
   */
  adjustRuleId?: string | null;
  /**
   * 车型唯一标识
   */
  vehicleKey?: string | null;
  /**
   * 是否是车型重构数据二期
   */
  isVehicle2?: boolean | null;
  /**
   * BAS（基础）、 ADV（优享）、PRE（尊享）、PREP（无忧租2024）
   */
  packageLevel?: string | null;
}
export interface SecretBoxParam {
  /**
   * 可能取到的车型
   */
  groupNameScope?: CtqSimilarVehicleInfo[] | null;
  /**
   * 租车押金
   */
  deposit?: number | null;
  /**
   * 违章押金
   */
  peccancyDeposit?: number | null;
  /**
   * 活动类型 1.常规活动 2.盲盒活动 （默认常规活动）
   */
  activityType?: number | null;
  /**
   * 车型id
   */
  ctripVehicleId?: number | null;
  /**
   * 门店id
   */
  storeId?: number | null;
  /**
   * 时间戳
   */
  timespan?: number | null;
  /**
   * 签名
   */
  sign?: string | null;
  /**
   * 盲盒优惠后总价
   */
  totalFeeOfSecretBox?: number | null;
  /**
   * 当前车型组优惠后的最低总价
   */
  minTotalFeeOfCurrentGroup?: number | null;
  /**
   * 车型组
   */
  vehicleGroup?: number | null;
}
export interface PackagePriceInfo {
  /**
   * 套餐规则(套餐类型 0-普通,1-无忧租)
   */
  packageType?: string | null;
  /**
   * 价格一致码
   */
  priceCode?: string | null;
}
export interface VehicleLevelInfo {
  /**
   * 车辆供应商提供的年限对应的等级
   */
  vLevel?: string | null;
  /**
   * 标签平台给的年限标签
   */
  apiCode?: string | null;
  /**
   * 实际使用的年限标签
   */
  labelCode?: string | null;
  /**
   * 车龄
   */
  carAge?: string | null;
}
export interface IsdShareInfo {
  orginaltotal?: number | null;
  isrec?: boolean | null;
  recommendOrder?: number | null;
  mergeId?: number | null;
  recsort?: number | null;
  rectype?: number | null;
  cvid?: number | null;
  rentalamount?: number | null;
  totalDailyPrice?: number | null;
  vdegree?: string | null;
  grantedcode?: string | null;
  mergeInfo?: MergeInfo[] | null;
}
export interface MergeInfo {
  vehicleId?: string | null;
  storeId?: string | null;
}
export interface VcExtendRequest {
  responsePickUpLocationId?: string | null;
  responseReturnLocationId?: string | null;
  vendorVehicleId?: string | null;
  /**
   * vc层报价关联信息，作为vc发起的透传字段
   */
  vendorRateReference?: string | null;
}
export interface MobileRequestHead {
  /**
   * h5: 09；原生：30；如果在app访问h5，是其他值。
   */
  syscode?: string | null;
  /**
   * MobileRequestHead
   */
  lang?: string | null;
  /**
   * MobileRequestHead
   */
  auth?: string | null;
  /**
   * 客户端id：09031038210794831462
   */
  cid?: string | null;
  /**
   * MobileRequestHead
   */
  ctok?: string | null;
  /**
   * MobileRequestHead
   */
  cver?: string | null;
  /**
   * MobileRequestHead
   */
  sid?: string | null;
  /**
   * MobileRequestHead
   */
  extension?: ExtensionFieldType[] | null;
}
export interface ExtensionFieldType {
  name?: string | null;
  value?: string | null;
}
export interface BaseRequest {
  sourceFrom?: string | null;
  requestId?: string | null;
  parentRequestId?: string | null;
  channelId?: number | null;
  /**
   * 分销渠道
   */
  allianceInfo?: AllianceInfoDTO | null;
  /**
   * 本地语言
   */
  locale?: string | null;
  /**
   * 货币
   */
  currencyCode?: string | null;
  /**
   * 移动设备信息
   */
  mobileInfo?: MobileDTO | null;
  /**
   * 客源国ID
   */
  sourceCountryId?: number | null;
  site?: string | null;
  language?: string | null;
  sessionId?: string | null;
  invokeFrom?: number | null;
  uid?: string | null;
  patternType?: number | null;
  clientId?: string | null;
  vid?: string | null;
  extraMaps?: { [key: string]: string } | null;
  platform?: string | null;
}
export interface MobileDTO {
  /**
   * 无线用户纬度
   */
  customerGPSLat?: number | null;
  /**
   * 无线用户经度
   */
  customerGPSLng?: number | null;
  /**
   * 用户手机类型Android、iOS等
   */
  mobileModel?: string | null;
  /**
   * 用户手机的SN编号，机器唯一标识
   */
  mobileSN?: string | null;
  /**
   * 用户IP地址
   */
  customerIP?: string | null;
  /**
   * 无线版本号
   */
  wirelessVersion?: string | null;
}
export interface AllianceInfoDTO {
  /**
   * 分销联盟
   */
  allianceId?: number | null;
  /**
   * 分销联盟二级分销ID
   */
  ouid?: string | null;
  /**
   * 分销联盟三级分销ID
   */
  sid?: number | null;
  /**
   * 分销商订单Id
   */
  distributorOrderId?: string | null;
  /**
   * 分销商用户Id
   */
  distributorUID?: string | null;
  distributorChannelId?: string | null;
}
enum ErrorClassificationCodeType {
  ServiceError = 0,
  ValidationError = 1,
  FrameworkError = 2,
  SLAError = 3,
}
enum SeverityCodeType {
  Error = 0,
  Warning = 1,
}
enum AckCodeType {
  Success = 0,
  Failure = 1,
  Warning = 2,
  PartialFailure = 3,
}
export interface OptionalContactMethods {
  /**
   * type: 联系方式类型 1:Wechat;2:Line;3:kakao talk;4:whatsa
   * pp;5:qqtitle:name
   */
  contactMethods?: KeyAndValue[] | null;
  /**
   * 提示文案
   */
  promptText?: string | null;
}
