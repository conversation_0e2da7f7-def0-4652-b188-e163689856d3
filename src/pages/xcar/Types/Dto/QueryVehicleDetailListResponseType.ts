import { ResponseMapType } from '@ctrip/rn_com_car/dist/src/CarFetch/src/FetchTypes';
import { ProductListType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';

export interface ExtMapType {
  key?: string;
}

export interface BaseResponseType {
  isSuccess?: boolean;
  code?: string;
  returnMsg?: string;
  requestId?: string;
  cost?: number;
  showMessage?: string;
  extMap?: ExtMapType;
  extraIndexTags?: ExtMapType;
  apiResCodes?: Array<string>;
  hasResult?: boolean;
  errorCode?: string;
  message?: string;
}

export interface ErrorFieldsType {
  FieldName?: string;
  ErrorCode?: string;
  Message?: string;
}

export interface ErrorsType {
  Message?: string;
  ErrorCode?: string;
  StackTrace?: string;
  SeverityCode?: any;
  ErrorFields?: Array<ErrorFieldsType>;
  ErrorClassification?: any;
}

export interface ExtensionType {
  Id?: string;
  Version?: string;
  ContentType?: string;
  Value?: string;
}

export interface ResponseStatusType {
  Timestamp?: Date;
  Ack?: string;
  Errors?: Array<ErrorsType>;
  Build?: string;
  Version?: string;
  Extension?: Array<ExtensionType>;
}

export interface SimilarVehicleInfosType {
  vehicleCode?: string;
  vehicleName?: string;
  vehicleImageUrl?: string;
}

export interface VendorSimilarVehicleInfosType {
  bizVendorCode?: string;
  vendorName?: string;
  vendorLogo?: string;
  similarVehicleInfos?: Array<SimilarVehicleInfosType>;
}

export interface PicListType {
  imageUrl?: string;
  imageClass?: number;
  imageType?: number;
  sortNum?: number;
}

export interface SourcePicInfosType {
  source?: number;
  type?: number;
  sourceName?: string;
  picList?: Array<PicListType>;
}

export enum MediaGroupType {
  /**
   * 全部
   */
  All = -1,
  /**
   * 封面
   */
  Cover = 1,
  /**
   * 视屏
   */
  Video = 2,
  /**
   * VR
   */
  VR = 3,
  /**
   * 外观
   */
  Appearance = 4,
  /**
   * 前排
   */
  Front = 5,
  /**
   * 后排
   */
  Back = 6,
  /**
   * 相册
   */
  Album = 7,
}

export enum AlbumType {
  /**
   * 官方相册
   */
  Official = 1,
  /**
   * 门店实拍（供应商）相册
   */
  Store = 2,
  /**
   * 用户实拍相册
   */
  User = 3,
}

export enum MediaType {
  /**
   * 图片
   */
  Picture = 1,
  /**
   * 视频
   */
  Video = 2,
  /**
   * VR
   */
  VR = 3,
}

export interface MediaInfo {
  type?: number;
  url?: string;
  cover?: string;
  sortNum?: number;
  groupName?: string;
  groupType?: MediaGroupType;
  index?: number;
  groupId?: string;
  itemCountInGroup?: number;
  itemIdInGroup?: number;
  isEqualListImage?: boolean;
}

export interface MediaGroupInfo {
  groupType?: MediaGroupType;
  groupName?: string;
  groupSortNum?: number;
  medias: Array<MediaInfo>;
}

export interface MultimediaAlbum {
  albumName?: string;
  note?: string;
  albumType?: AlbumType;
  mediaGroup?: Array<MediaGroupInfo>;
}

export interface VehicleInfoType {
  brandId?: number;
  brandEName?: string;
  brandName?: string;
  name?: string;
  zhName?: string;
  vehicleCode?: string;
  imageUrl?: string;
  groupCode?: string;
  groupSubClassCode?: string;
  groupName?: string;
  transmissionType?: number;
  transmissionName?: string;
  passengerNo?: number;
  doorNo?: number;
  luggageNo?: number;
  displacement?: string;
  struct?: string;
  fuel?: string;
  gearbox?: string;
  driveMode?: string;
  driveType?: string;
  style?: string;
  imageList?: Array<string>;
  userRealImageList?: Array<string>;
  storeRealImageList?: Array<string>;
  similarImageList?: Array<string>;
  specializedImages?: Array<string>;
  vedio?: string;
  isSpecialized?: boolean;
  isHot?: boolean;
  recommendDesc?: string;
  hasConditioner?: boolean;
  conditionerDesc?: string;
  spaceDesc?: string;
  similarCommentDesc?: string;
  vendorSimilarVehicleInfos?: Array<VendorSimilarVehicleInfosType>;
  vehicleAccessoryImages?: Array<string>;
  license?: string;
  licenseStyle?: string;
  licenseDescription?: string;
  realityImageUrl?: string;
  sourcePicInfos?: Array<SourcePicInfosType>;
  multimediaAlbums?: Array<MultimediaAlbum>;
  luggageSize?: string;
  modeYear?: string;
  versionName?: string;
  vehicleLevel?: string;
  fuelNew?: string;
  oilType?: number;
  video?: string;
  cover?: string;
  vr?: string;
}

export interface CommentListType {
  avatar?: string;
  userName?: string;
  userRate?: number;
  userRateLevel?: number;
  content?: string;
}

export interface CommentInfoType {
  level?: string;
  vendorDesc?: string;
  commentCount?: number;
  qCommentCount?: number;
  qExposed?: string;
  overallRating?: string;
  maximumRating?: number;
  commentLabel?: string;
  hasComment?: number;
  noComment?: string;
  link?: string;
  commentList?: Array<CommentListType>;
}

export interface DeductInfosType {
  dayAmount?: number;
  totalAmount?: number;
  payofftype?: number;
}

export interface PriceInfoType {
  currentDailyPrice?: number;
  currentOriginalDailyPrice?: number;
  oTPrice?: number;
  currentTotalPrice?: number;
  currentCurrencyCode?: string;
  localCurrencyCode?: string;
  marginPrice?: number;
  naked?: boolean;
  priceVersion?: string;
  priceType?: number;
  deductInfos?: Array<DeductInfosType>;
}

export interface VcExtendRequestType {
  responsePickUpLocationId?: string;
  responseReturnLocationId?: string;
  vendorVehicleId?: string;
}

export interface PromotionType {
  type?: number;
  title?: string;
  description?: string;
  longTag?: string;
  longDesc?: string;
  couponDesc?: string;
  deductionPercent?: number;
  deductionAmount?: number;
  dayDeductionAmount?: number;
  payofftype?: number;
  payoffName?: string;
  code?: string;
  isFromCtrip?: boolean;
  islimitedTimeOfferType?: boolean;
  sortNum?: number;
  strategySource?: string;
  earningsCost?: string;
  businessCost?: string;
  resourceCost?: string;
  configVersion?: string;
  isEnabled?: boolean;
  actionedDate?: string;
  expiredDate?: string;
  extDesc?: string;
  selected?: boolean;
  startAmount?: number;
  isOverlay?: boolean;
  promotionId?: number;
  overlayDesc?: string;
  couponName?: string;
  unitName?: string;
  popAmountTile?: string;
  deductionType?: number;
  discountType?: number;
  adjustPriceCode?: string;
}

export interface UnionCardFilterType {
  itemCode?: string;
  name?: string;
  code?: string;
  groupCode?: string;
  bitwiseType?: number;
  binaryDigit?: number;
  sortNum?: number;
  isQuickItem?: boolean;
  quickSortNum?: number;
  icon?: string;
  promotion?: PromotionType;
  mark?: string;
  positionCode?: string;
}

export interface SubListType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  labelCode?: string;
  positionCode?: string;
  groupCode?: string;
  tagGroups?: number;
  tagSortNum?: number;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
}

export interface SubListType2 {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  subList?: Array<SubListType>;
  labelCode?: string;
  positionCode?: string;
  groupCode?: string;
  tagGroups?: number;
  tagSortNum?: number;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
}

export interface LabelsType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  subList?: Array<SubListType2>;
  labelCode?: string;
  positionCode?: string;
  groupCode?: string;
  tagGroups?: number;
  tagSortNum?: number;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
}

export interface MergeInfoType {
  vehicleId?: string;
  storeId?: string;
}

export interface IsdShareInfoType {
  orginaltotal?: number;
  isrec?: boolean;
  recommendOrder?: number;
  mergeId?: number;
  recsort?: number;
  rectype?: number;
  cvid?: number;
  rentalamount?: number;
  totalDailyPrice?: number;
  vdegree?: string;
  grantedcode?: string;
  mergeInfo?: Array<MergeInfoType>;
}
export enum PlatformCode {
  direct = 0,
  pms = 10,
}
export interface ReferenceType {
  bizVendorCode?: string;
  vendorCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
  vehicleCode?: string;
  packageId?: string;
  packageType?: number;
  vcExtendRequest?: VcExtendRequestType;
  decoratorVendorType?: number;
  easyLifeUpgradePackageId?: number;
  isEasyLife?: boolean;
  withPrice?: boolean;
  packageId4CutPrice?: number;
  payMode?: number;
  bomCode?: string;
  productCode?: string;
  rateCode?: string;
  subType?: number;
  comPriceCode?: string;
  priceVersion?: string;
  priceVersionOfLowestPrice?: string;
  pCityId?: number;
  rCityId?: number;
  vendorVehicleCode?: string;
  age?: number;
  alipay?: boolean;
  aType?: number;
  vendorSupportZhima?: boolean;
  hotType?: number;
  priceType?: number;
  vehicleDegree?: string;
  idType?: number;
  fType?: number;
  rentCenterId?: number;
  isSelect?: boolean;
  unionCardFilter?: UnionCardFilterType;
  noDepositFilter?: UnionCardFilterType;
  labels?: Array<LabelsType>;
  pStoreNav?: string;
  rStoreNav?: string;
  pickUpOnDoor?: boolean;
  dropOffOnDoor?: boolean;
  pickWayInfo?: number;
  returnWayInfo?: number;
  sendTypeForPickUpCar?: number;
  sendTypeForPickOffCar?: number;
  hot?: number;
  freeIllegalDeposit?: boolean;
  creditFreeCarDeposit?: boolean;
  isdShareInfo?: IsdShareInfoType;
  adjustVersion?: string;
  gsId?: number;
  noLp?: number;
  gsDesc?: string;
  pRc?: number;
  rRc?: number;
  rCoup?: number;
  promtId?: number;
  klbVersion?: number;
  platform?: PlatformCode;
  packageLevel?: string;
}

export interface CommentInfoType2 {
  overallRating?: number;
  commentCount?: number;
  level?: string;
  newVendorDescDescription?: string;
}

export interface EasyLifeVsNormalInfoListType {
  title?: string;
  easyLifeDesc?: string;
  normalDesc?: string;
}

export interface EasyLifeInsuranceIncludesType {
  itemName?: string;
  coverage?: string;
}

export interface ItemListType {
  sortNum?: number;
  name?: string;
}

export interface ItemListType2 {
  sortNum?: number;
  name?: string;
  itemList?: Array<ItemListType>;
}

export interface ExtDescriptionListType {
  sortNum?: number;
  name?: string;
  itemList?: Array<ItemListType2>;
}

export interface EasyLifeInsuranceDetailType {
  bizVendorCode?: string;
  easyLifeInsuranceDesc?: Array<string>;
  easyLifeInsuranceAdditionalDesc?: Array<string>;
  easyLifeInsuranceUncludes?: Array<string>;
  easyLifeInsuranceIncludes?: Array<EasyLifeInsuranceIncludesType>;
  vendorName?: string;
  extDescriptionList?: Array<ExtDescriptionListType>;
  clauseUrl?: string;
}

export interface EasyLifeCtripInsuranceConfigType {
  withCtripInsurance?: boolean;
  productIds?: Array<number>;
}

export interface EasyLifeInfoType {
  isEasyLife?: boolean;
  pickupTips?: string;
  returnTips?: string;
  pickupLongTips?: string;
  returnLongTips?: string;
  tagList?: Array<LabelsType>;
  commentInfo?: CommentInfoType2;
  easyLifeVsNormalInfoList?: Array<EasyLifeVsNormalInfoListType>;
  easyLifeInsuranceDetail?: EasyLifeInsuranceDetailType;
  countryId?: number;
  cityList?: Array<number>;
  bizVendorCode?: string;
  freePreAuthorization?: boolean;
  originFreePreAuth?: boolean;
  needWechat?: boolean;
  easyLifeCtripInsuranceConfig?: EasyLifeCtripInsuranceConfigType;
  onewayfee?: number;
  freePreAuthDesc?: string;
  guidImages?: string;
}

export interface FilterAggregationsType {
  name?: string;
  groupCode?: string;
  binaryDigit?: number;
  checkType?: number;
}

export interface AdjustPriceInfoType {
  bAdjustDailyPrice?: number;
  bAdjustTotalPrice?: number;
  adjustStrategy?: string;
  cashbackStrategy?: string;
}

export interface DetailType {
  code?: string;
  name?: string;
  amount?: number;
  amountDesc?: string;
  desc?: string;
  showFree?: boolean;
}

export interface FeesType {
  code?: string;
  name?: string;
  amount?: number;
  amountStr?: string;
  subAmount?: number;
  subAmountStr?: string;
  qt?: number;
  desc?: string;
  detail?: Array<DetailType>;
  currencyCode?: string;
}

export interface PriceDailysType {
  date?: string;
  oDprice?: string;
  priceStr?: string;
  showType?: number;
}

export interface VendorPriceListType {
  // pStoreSortDesc?: string;
  // rStoreSortDesc?: string;
  vendorName?: string;
  // isMinTPriceVendor?: boolean;
  // vendorLogo?: string;
  commentInfo?: CommentInfoType;
  priceInfo?: PriceInfoType;
  reference?: ReferenceType;
  // referenceVersion?: string;
  // promotions?: Array<PromotionType>;
  // isSpecialized?: boolean;
  // sortNum?: number;
  // newVendorDesc?: string;
  // vendorTag?: LabelsType;
  pStoreRouteDesc?: string;
  // rStoreRouteDesc?: string;
  easyLifeInfo?: EasyLifeInfoType;
  // showIZuCheLogo?: boolean;
  // isBroker?: boolean;
  // platformName?: string;
  // platformCode?: string;
  allTags?: Array<LabelsType>;
  // evaluation?: LabelsType;
  // filterAggregations?: Array<FilterAggregationsType>;
  // extTitle?: string;
  // reactId?: string;
  // isPStoreSupportCdl?: boolean;
  // isRStoreSupportCdl?: boolean;
  // privilegesPromotions?: Array<PromotionType>;
  // qualityScore?: number;
  // sortScore?: number;
  // storeScore?: number;
  isSelect?: boolean;
  // orignalPriceStyle?: any;
  // stock?: number;
  // stockDesc?: string;
  // distance?: number;
  // rDistance?: number;
  // adverts?: number;
  // extMap?: ExtMapType;
  // payModes?: Array<number>;
  // freeDeposit?: number;
  // urge?: string;
  // actId?: string;
  // couId?: string;
  // joinerType?: string;
  // joinerStoreId?: string;
  // adjustPriceInfo?: AdjustPriceInfoType;
  // pickUpFee?: number;
  // pickOffFee?: number;
  // showVendor?: boolean;
  // isOrderVehicle?: boolean;
  // cyVendorName?: string;
  // card?: number;
  ctripVehicleCode?: string;
  // cashbackPre?: string;
  // modifySameStore?: boolean;
  fees?: Array<FeesType>;
  // dPriceDesc?: string;
  // priceDailys?: Array<PriceDailysType>;
  uniqueCode?: string;
  decorateVehicleName?: string;
  vehicleScopeDesc?: string;
  // newCar?: boolean;
  vehicleGroup?: number;
}

export interface SpecificProductGroupsType {
  title?: string;
  vendorPriceList?: Array<VendorPriceListType>;
}

export interface IncludeFeesType {
  tFees?: Array<string>;
  dFees?: Array<string>;
  desc?: string;
}

export interface FeeMapType {
  code?: string;
  name?: string;
  desc?: string;
}
export interface RecommendVehicle {
  /**
   * 车型code
   */
  vehicleCode?: string | null;
  /**
   * 可用搜索地址（r2:按同城异地推荐时使用）
   */
  availableLocation?: string | null;
  /**
   * 经度
   */
  longitude?: string | null;
  /**
   * 维度
   */
  latitude?: string | null;
}

export interface Recommendation {
  /**
   * 提示语
   */
  tilte?: string | null;
  /**
   * 提示副标题
   */
  subTitle?: string | null;
  /**
   * 推荐标题
   */
  recTitle?: string | null;
  /**
   * 推荐信息
   */
  recMessage?: string | null;
  /**
   * 可用还车时间
   */
  pickUpAvailableTime?: string | null;
  /**
   * 可用取车时间
   */
  returnAvailableTime?: string | null;
}

export interface RecommendInfo {
  /**
   * 推荐类型（r1：按时间推荐，r2:按同城异地推荐，r3:扩大搜索半径推荐，r4：推荐附近城市）
   */
  recommendType?: string | null;
  /**
   * 推荐车型信息
   */
  recommendVehicles?: RecommendVehicle[] | null;
  /**
   * 推荐语
   */
  recommendation?: Recommendation | null;
  /**
   * 原因
   */
  reason?: string | null;
  /**
   * 推荐语
   */
  recommend?: string | null;
}

export interface ExplainObject {
  /**
   * 标题
   */
  title?: string;
  /**
   * 子标题
   */
  subTitle?: string;
  /**
   * 内容
   */
  content?: string[];
}

export interface DetailPageTag {
  title?: string;
  lowestDailyPrice?: number;
  specificProductGroups?: SpecificProductGroupsType;
  filteredProductGroups?: SpecificProductGroupsType;
}

export interface QueryVehicleDetailListResponseType {
  baseResponse?: BaseResponseType;
  ResponseStatus?: ResponseStatusType;
  vehicleInfo?: VehicleInfoType;
  detailPageTags?: Array<DetailPageTag>;
  specificProductGroups?: SpecificProductGroupsType;
  filteredProductGroups?: SpecificProductGroupsType;
  includeFees?: IncludeFeesType;
  uniqSign?: string;
  feeMap?: Array<FeeMapType>;
  marketingAtmosphere?: number;
  appResponseMap?: ResponseMapType;
  isFromSearch?: boolean;
  promptInfos?: Array<PromptInfoType>;
  promotMap?: { [key: number]: string };
  extras?: any;
  imStatus?: number;
  recommendInfo?: RecommendInfo;
  recommendProducts?: ProductListType[];
  recommendVehicleList?: VehicleInfoType[];
  productGroupCodeUesd?: string;
  shareVehicleInfo?: any;
  /**
   * 价格说明
   */
  priceExplain?: ExplainObject;
  floor?: Array<Floor>;
}
export interface PromptInfoType {
  title?: string;
  subTitle?: string;
  contents?: Array<ComplexSubTitleType>;
  type?: number;
  icon?: string;
  note?: string;
  locations?: Array<LocationsType>;
  button?: ButtonType;
  buttonExt?: Array<ButtonType>;
  filterItem?: FilterItemType;
  items?: Array<ItemsType2>;
  table?: Array<ItemsType2>;
  extraInfos?: ExtraInfosType;
}

export interface ExtraInfosType {
  url?: string;
  desc?: string;
  amount?: string;
}

export interface ComplexSubTitleType {
  contentStyle?: string;
  stringObjs?: Array<StringObjsType>;
}

export interface LocationsType {
  groupCode?: string;
  index?: number;
}

export interface ButtonType {
  title?: string;
  type?: number;
  desc?: string;
  tips?: string;
  icon?: string;
}

export interface FilterItemType {
  itemCode?: string;
  name?: string;
  code?: string;
  groupCode?: string;
  bitwiseType?: number;
  binaryDigit?: number;
  sortNum?: number;
  isQuickItem?: boolean;
  quickSortNum?: number;
  icon?: string;
  promotion?: PromotionType;
  mark?: string;
  positionCode?: string;
}

export interface ItemsType2 {
  title?: ComplexSubTitleType;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: Array<ItemsType>;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
}

export interface StringObjsType {
  content?: string;
  style?: string;
  url?: string;
}

export interface ItemsType {
  title?: string;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
}
export interface StringObject {
  content?: string | null;
  style?: string | null;
  url?: string | null;
}
export interface ContentObject {
  /**
   * 样式
   */
  contentStyle?: string | null;
  stringObjs?: StringObject[] | null;
}

export interface SimpleObject {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 标题后缀
   */
  titleExtra?: string | null;
  /**
   * 标签类型
   */
  category?: number | null;
  /**
   * 导向类型
   */
  type?: number | null;
  /**
   * code
   */
  code?: string | null;
  /**
   * 类描述
   */
  typeDesc?: string | null;
  /**
   * 描述
   */
  description?: string | null;
  sortNum?: number | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 展示层级
   */
  showLayer?: number | null;
  /**
   * 颜色类型
   */
  colorCode?: string | null;
  subList?: SimpleObject[] | null;
  /**
   * 标签code
   */
  labelCode?: string | null;
  /**
   * 位置code
   */
  positionCode?: string | null;
  /**
   * 标签groupCode
   */
  groupCode?: string | null;
  /**
   * 填写页重构标签分级
   */
  tagGroups?: number | null;
  /**
   * 填写页重构标签排序
   */
  tagSortNum?: number | null;
  /**
   * 标签展示分行, 用于分行，groupId一样的放在一行
   */
  amountTitle?: string | null;
  /**
   * 货架二期标签合并组，1 营销类 2车辆类，3服务类      货架2.0,共减XX
   */
  groupId?: number | null;
  /**
   * 标签合并Id， mergeId大于0 ， 且一样的情况需要合并在一起展示， 如无忧组情况下， 押金双
   * 免，免费取消标签需要合并在一起展示
   */
  mergeId?: number | null;
  /**
   * 延时免费留车标签前缀租车中心
   */
  prefix?: string | null;
  /**
   * 国内调价项目新增：标签前缀id：1 标识调价
   */
  prefixTypeId?: number | null;
  /**
   * 合并后的标题：用于弹窗。      标签外露标题和弹窗里展示样式不一致，弹窗里是纯文本      国
   * 内调价项目新增(2023-08-25)
   */
  mergeTitle?: string | null;
  /**
   * 带有样式的标签描述
   */
  descriptionObject?: ContentObject[] | null;
}
export interface DeductInfo {
  /**
   * 优惠金额/天
   */
  dayAmount?: number | null;
  /**
   * 优惠金额
   */
  totalAmount?: number | null;
  /**
   * 优惠类型 （0/1：返现，2：立减）
   */
  payofftype?: number | null;
}
export interface CtqSimplePriceInfo {
  // 加价
  gapPrice?: number | null;
  /**
   * 当前币种日均价
   */
  currentDailyPrice?: number | null;
  /**
   * 当前币种原始日均价
   */
  curOriginDPrice?: number | null;
  /**
   * 当前币种原始日均价(无优惠情况下该字段被处理为0，因此仅用作特殊展示)
   */
  currentOriginalDailyPrice?: number | null;
  /**
   * 原总价
   */
  oTPrice?: number | null;
  /**
   * 当前币种总价
   */
  currentTotalPrice?: number | null;
  /**
   * 当前币种
   */
  currentCurrencyCode?: string | null;
  /**
   * 当地币种
   */
  localCurrencyCode?: string | null;
  /**
   * 差价（国际站修改订单）
   */
  marginPrice?: number | null;
  /**
   * 是否是裸价套餐（国际站使用）
   */
  naked?: boolean | null;
  /**
   * 价格版本（公共服务价格埋点使用）
   */
  priceVersion?: string | null;
  /**
   * 价格类型（国内使用）
   */
  priceType?: number | null;
  /**
   * 优惠信息
   */
  deductInfos?: DeductInfo[] | null;
}
export interface VcExtendRequest {
  responsePickUpLocationId?: string | null;
  responseReturnLocationId?: string | null;
  vendorVehicleId?: string | null;
  /**
   * vc层报价关联信息，作为vc发起的透传字段
   */
  vendorRateReference?: string | null;
}
export interface Promotion {
  type?: number | null;
  title?: string | null;
  description?: string | null;
  longTag?: string | null;
  longDesc?: string | null;
  couponDesc?: string | null;
  deductionPercent?: number | null;
  deductionAmount?: number | null;
  dayDeductionAmount?: number | null;
  /**
   * 券类型 0-默认，1-返现，2-立减
   */
  payofftype?: number | null;
  payoffName?: string | null;
  code?: string | null;
  isFromCtrip?: boolean | null;
  islimitedTimeOfferType?: boolean | null;
  sortNum?: number | null;
  strategySource?: string | null;
  earningsCost?: string | null;
  businessCost?: string | null;
  resourceCost?: string | null;
  configVersion?: string | null;
  /**
   * 是否可用
   */
  isEnabled?: boolean | null;
  /**
   * 生效时间
   */
  actionedDate?: string | null;
  /**
   * 过期时间
   */
  expiredDate?: string | null;
  /**
   * 扩展描述字段 ：1叠加信息描述 2不可用说明
   */
  extDesc?: string | null;
  /**
   * 是否选中
   */
  selected?: boolean | null;
  /**
   * 满金额
   */
  startAmount?: number | null;
  /**
   * 是否可叠加
   */
  isOverlay?: boolean | null;
  /**
   * id
   */
  promotionId?: number | null;
  /**
   * 叠加说明
   */
  overlayDesc?: string | null;
  /**
   * 券名
   */
  couponName?: string | null;
  /**
   * 单位
   */
  unitName?: string | null;
  /**
   * 列表中对应金额
   */
  popAmountTile?: string | null;
  /**
   * 减免类型
   */
  deductionType?: number | null;
  /**
   * 活动来源
   */
  discountType?: number | null;
  /**
   * 调价版本号
   */
  adjustPriceCode?: string | null;
  /**
   * 标签Code
   */
  labelCode?: string | null;
  /**
   * 优惠活动币种
   */
  unitCurrency?: string | null;
}
export interface FilterItem {
  itemCode?: string | null;
  name?: string | null;
  code?: string | null;
  groupCode?: string | null;
  /**
   * 未运算类型 1 与运算 2或运算
   */
  bitwiseType?: number | null;
  /**
   * 二进制数
   */
  binaryDigit?: number | null;
  sortNum?: number | null;
  /**
   * 是否首页显示
   */
  isQuickItem?: boolean | null;
  quickSortNum?: number | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 活动信息
   */
  promotion?: Promotion | null;
  /**
   * 角标
   */
  mark?: string | null;
  /**
   * 位置code
   */
  positionCode?: string | null;
  /**
   * 筛选后图片
   */
  selectedIcon?: string | null;
  /**
   * 筛选项样式类型，null/0：筛选项，1：滑动条
   */
  itemType?: number | null;
  /**
   * 滑动条步长
   */
  step?: number | null;
}
export interface MergeInfo {
  vehicleId?: string | null;
  storeId?: string | null;
}
export interface IsdShareInfo {
  orginaltotal?: number | null;
  isrec?: boolean | null;
  recommendOrder?: number | null;
  mergeId?: number | null;
  recsort?: number | null;
  rectype?: number | null;
  cvid?: number | null;
  rentalamount?: number | null;
  totalDailyPrice?: number | null;
  vdegree?: string | null;
  grantedcode?: string | null;
  mergeInfo?: MergeInfo[] | null;
}
export interface VehicleLevelInfo {
  /**
   * 车辆供应商提供的年限对应的等级
   */
  vLevel?: string | null;
  /**
   * 标签平台给的年限标签
   */
  apiCode?: string | null;
  /**
   * 实际使用的年限标签
   */
  labelCode?: string | null;
  /**
   * 车龄
   */
  carAge?: string | null;
}
export interface PackagePriceInfo {
  /**
   * 套餐规则(套餐类型 0-普通,1-无忧租)
   */
  packageType?: string | null;
  /**
   * 价格一致码
   */
  priceCode?: string | null;
}
export interface CtqSimilarVehicleInfo {
  vehicleCode?: string | null;
  vehicleName?: string | null;
  vehicleImageUrl?: string | null;
}
export interface SecretBoxParam {
  /**
   * 可能取到的车型
   */
  groupNameScope?: CtqSimilarVehicleInfo[] | null;
  /**
   * 租车押金
   */
  deposit?: number | null;
  /**
   * 违章押金
   */
  peccancyDeposit?: number | null;
  /**
   * 活动类型 1.常规活动 2.盲盒活动 （默认常规活动）
   */
  activityType?: number | null;
  /**
   * 车型id
   */
  ctripVehicleId?: number | null;
  /**
   * 门店id
   */
  storeId?: number | null;
  /**
   * 时间戳
   */
  timespan?: number | null;
  /**
   * 签名
   */
  sign?: string | null;
  /**
   * 盲盒优惠后总价
   */
  totalFeeOfSecretBox?: number | null;
  /**
   * 当前车型组优惠后的最低总价
   */
  minTotalFeeOfCurrentGroup?: number | null;
  /**
   * 车型组
   */
  vehicleGroup?: number | null;
}
export interface Reference {
  bizVendorCode?: string | null;
  vendorCode?: string | null;
  pStoreCode?: string | null;
  rStoreCode?: string | null;
  vehicleCode?: string | null;
  /**
   * 无忧租id
   */
  packageId?: string | null;
  packageType?: number | null;
  vcExtendRequest?: VcExtendRequest | null;
  decoratorVendorType?: number | null;
  easyLifeUpgradePackageId?: number | null;
  isEasyLife?: boolean | null;
  /**
   * 是否返回报价信息
   */
  withPrice?: boolean | null;
  packageId4CutPrice?: number | null;
  payMode?: number | null;
  bomCode?: string | null;
  productCode?: string | null;
  rateCode?: string | null;
  subType?: number | null;
  comPriceCode?: string | null;
  priceVersion?: string | null;
  priceVersionOfLowestPrice?: string | null;
  pCityId?: number | null;
  rCityId?: number | null;
  vendorVehicleCode?: string | null;
  age?: number | null;
  /**
   * 是否芝麻免押(0：否 1：是)
   */
  alipay?: boolean | null;
  /**
   * 芝麻信用押金全免(0:免租车押金 1：押金全免)
   */
  aType?: number | null;
  /**
   * 供应商是否支持免押
   */
  vendorSupportZhima?: boolean | null;
  /**
   * 爆款类型
   */
  hotType?: number | null;
  /**
   * 价格类型
   */
  priceType?: number | null;
  /**
   * 车型等级
   */
  vehicleDegree?: string | null;
  /**
   * 证件类型
   */
  idType?: number | null;
  fType?: number | null;
  rentCenterId?: number | null;
  /**
   * 是否携程精选
   */
  isSelect?: boolean | null;
  /**
   * 银联卡搜索（不支持为null）
   */
  unionCardFilter?: FilterItem | null;
  /**
   * 免押搜索（不支持为null）
   */
  noDepositFilter?: FilterItem | null;
  /**
   * 国内列表页标签带入填写页
   */
  labels?: SimpleObject[] | null;
  /**
   * 国内到达方式
   */
  pStoreNav?: string | null;
  rStoreNav?: string | null;
  /**
   * 是否上门取还车
   */
  pickUpOnDoor?: boolean | null;
  dropOffOnDoor?: boolean | null;
  /**
   * shopping使用的取车方式聚合字段
   */
  pickWayInfo?: number | null;
  /**
   * shopping使用的还车方式聚合字段
   */
  returnWayInfo?: number | null;
  /**
   * 送车上门类型
   */
  sendTypeForPickUpCar?: number | null;
  /**
   * 上门取车类型
   */
  sendTypeForPickOffCar?: number | null;
  hot?: number | null;
  /**
   * 供应商是否免违章押金
   */
  freeIllegalDeposit?: boolean | null;
  /**
   * 是否程信分免租车押金
   */
  creditFreeCarDeposit?: boolean | null;
  /**
   * 国内填写页分享需要
   */
  isdShareInfo?: IsdShareInfo | null;
  /**
   * 国内调价版本号
   */
  adjustVersion?: string | null;
  /**
   * 货架id
   */
  gsId?: number | null;
  /**
   * 是否是不需要限价（1：不需要）
   */
  noLp?: number | null;
  /**
   * 是否是电动车(0:否，1：是)
   */
  elct?: number | null;
  /**
   * 货架新版推荐语
   */
  gsDesc?: string | null;
  /**
   * 取车租车中心Id
   */
  pRc?: number | null;
  /**
   * 还车租车中心Id
   */
  rRc?: number | null;
  /**
   * 卡拉比skuId
   */
  skuId?: number | null;
  /**
   * 卡拉比套餐id
   */
  klbPId?: number | null;
  /**
   * 是否卡拉比(0-否，1-是)
   */
  klb?: number | null;
  /**
   * 商品使用的取车方式
   */
  pCType?: number | null;
  /**
   * 商品使用的还车方式
   */
  rCType?: number | null;
  /**
   * 取车圈id
   */
  pLevel?: number | null;
  /**
   * 还车圈id
   */
  rLevel?: number | null;
  /**
   * 优惠id
   */
  promtId?: number | null;
  /**
   * 是否领券订(0：否，1：是)
   */
  rCoup?: number | null;
  /**
   * 车型四字码
   */
  sippCode?: string | null;
  /**
   * 排序信息
   */
  sortInfo?: { [key: string]: string } | null;
  /**
   * 商品分级，ab实验埋点信息
   */
  vehicleLevelInfo?: VehicleLevelInfo | null;
  /**
   * 是否是新能源(0:否，1：是)
   */
  newEnergy?: number | null;
  /**
   * 平台（0-直连，10-PMS）
   */
  platform?: number | null;
  /**
   * 卡拉比取车门店id
   */
  kPSId?: number | null;
  /**
   * 卡拉比还车门店id
   */
  kRSId?: number | null;
  /**
   * 卡拉比服务商id
   */
  kVId?: number | null;
  /**
   * 携程取车圈Id
   */
  pLev?: number | null;
  /**
   * 携程还车圈Id
   */
  rLev?: number | null;
  /**
   * 1-卡拉比数据源
   */
  klbVersion?: number | null;
  /**
   * 卡拉比标准产品id
   */
  kVehicleId?: number | null;
  /**
   * 套餐价格信息集合（请求多套餐报价使用）
   */
  packagePriceInfos?: PackagePriceInfo[] | null;
  /**
   * 异门店Code
   */
  diffStoreCode?: string | null;
  /**
   * 产品类型 0 普通产品 1盲盒产品
   */
  productType?: number | null;
  /**
   * 盲盒需要的信息，用于接口带参，需要加密，服务端使用
   */
  secretBoxParam?: SecretBoxParam | null;
  /**
   * 库存等级（商品）
   */
  stockLevel?: string | null;
  /**
   * 报价信息Id， pkgSellingRuleID
   */
  pkgRuleId?: number | null;
  /**
   * 调价规则id
   */
  adjustRuleId?: string | null;
  /**
   * 车型唯一标识
   */
  vehicleKey?: string | null;
  /**
   * 是否是车型重构数据二期
   */
  isVehicle2?: boolean | null;
  /**
   * BAS（基础）、 ADV（优享）、PRE（尊享）、PREP（无忧租2024）
   */
  packageLevel?: string | null;
  /**
   * 是否是扩大召回
   */
  largeRadiusVersion?: string | null;
  /**
   * 列表页requestid
   */
  listRequestId?: string | null;
  /**
   * pickupLocationId
   */
  pickupLocationId?: string | null;
  /**
   * returnLocationId
   */
  returnLocationId?: string | null;
  /**
   * 产品Id
   */
  productId?: string | null;
  /**
   * 是否是车型组重构
   */
  isGroupNew?: boolean | null;
  /**
   * 限制套餐类型（abg套餐）
   */
  limitPkt?: boolean | null;
  /**
   * 费项标准化
   */
  platformCal?: boolean | null;
}
export interface Detail {
  /**
   * code
   */
  code?: string | null;
  /**
   * 名字
   */
  name?: string | null;
  /**
   * 费用金额
   */
  amount?: number | null;
  /**
   * 费用金额描述
   */
  amountDesc?: string | null;
  /**
   * 描述
   */
  desc?: string | null;
  /**
   * 免费展示字样
   */
  showFree?: boolean | null;
  /**
   * {0}{1} ×{2}天
   */
  size?: string | null;
}
export interface StandardFee {
  /**
   * 费用code
   */
  code?: string | null;
  /**
   * 费用名称
   */
  name?: string | null;
  /**
   * 费用金额
   */
  amount?: number | null;
  /**
   * 费用金额展示,例如¥100
   */
  amountStr?: string | null;
  /**
   * 子费用金额(优惠后日均价、优惠前总价)
   */
  subAmount?: number | null;
  /**
   * 子费用金额展示,如日均¥20
   */
  subAmountStr?: string | null;
  /**
   * 划价前日均价
   */
  originalDailyPrice?: number | null;
  /**
   * 数量
   */
  qt?: number | null;
  /**
   * 费用的描述
   */
  desc?: string | null;
  /**
   * 二级子费用
   */
  detail?: Detail[] | null;
  /**
   * 币种
   */
  currencyCode?: string | null;
}
export interface PriceDaily {
  /**
   * 日期
   */
  date?: string | null;
  /**
   * 原天价
   */
  oDprice?: string | null;
  /**
   * 展示金额
   */
  priceStr?: string | null;
  /**
   * 展示类型（前端样式用） 0 默认宽度， 1 day1-day2 展示宽度
   */
  showType?: number | null;
}
export interface FloorPackage {
  /**
   * 1002 - 基础，2001优享，2011尊享，PREP一口价
   */
  code?: PackageCode | null;
  /**
   * 基础保障等
   */
  name?: string | null;
  /**
   * 保险描述
   */
  insuranceDesc?: string[] | null;
  /**
   * 价格信息
   */
  priceInfo?: CtqSimplePriceInfo | null;
  /**
   * Reference
   */
  reference?: Reference | null;
  /**
   * 营销标签
   */
  marketingTags?: SimpleObject[] | null;
  /**
   * 费用明细
   */
  fees?: StandardFee[] | null;
  /**
   * 日历价
   */
  priceDailys?: PriceDaily[] | null;
  /**
   * 日均价描述文案
   */
  dPriceDesc?: string | null;
}
export interface Floor {
  /**
   * 楼层id，前端可做唯一标识
   */
  floorId?: string;
  /**
   * 是否最低价 1表示最低，0表示非最低
   */
  lowestPrice?: number;
  /**
   * 楼层名
   */
  floorName?: string[];
  /**
   * 携程优选
   */
  isSelect?: boolean;
  /**
   * 供应商名称
   */
  vendorName?: string;
  /**
   * 取车方式
   */
  pickWayInfo?: string;
  /**
   * 还车方式
   */
  returnWayInfo?: string;
  /**
   * 标签
   */
  alltags?: SimpleObject[];
  /**
   * 保险楼层
   */
  packageList?: FloorPackage[];
  /**
   * 点击埋点数据
   */
  clickLogData?: any;
  /**
   * 曝光埋点数据
   */
  exposureLogData?: number;
  /**
   * 自助取还
   */
  isSelfService?: number;
}

export enum PackageCode {
  /**
   * 基础
   */
  Basic = '1002',
  /**
   * 优享
   */
  Preferred = '2001',
  /**
   * 尊享
   */
  Exclusive = '2011',
  /**
   * PREP
   */
  PREP = 'PREP',
}
