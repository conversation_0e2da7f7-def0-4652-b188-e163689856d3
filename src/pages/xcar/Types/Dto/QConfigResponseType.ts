export type NationalDayWarningType = {
  end: string;
  start: string;
  title: string;
};

export type AddInstructDataType = {
  title: string;
  content: string;
};

export type LessModalInfoType = {
  title: string;
  contents: Array<{
    title: string;
    descList: Array<string>;
  }>;
};

interface HeadItem {
  title: string[];
  icon: string;
}

interface UseDescItem {
  title: string;
  desc: string;
}

interface ETCConfigData {
  headItem: HeadItem[];
  useDescTitle: string;
  useDescItems: UseDescItem[];
  bottomTip: string;
}

interface ETCUseHelperModalItem {
  title: string;
  desc: string;
}
export interface ETCUseHelperTabSection {
  title: string;
  items: ETCUseHelperModalItem[];
}

export interface QConfigType {
  qiweiConfig: any;
  // 产品详情页唐图入口开关
  vendorListTangramEntrance: boolean;
  // #首页日历节假日提示配置
  nationalDayWarning: NationalDayWarningType;
  // #度假模块入口
  selfDrivingEntryConfig: any[];
  // #首页芝麻验证入口是否展示
  hideZhima: boolean;
  // #首页slogan配置
  slogan: string[];
  // #加购保险提示信息
  moreServiceTip: string;
  // #点击添加多名驾驶员提示信息
  addInstructData: AddInstructDataType;
  // #保代开关
  insuranceFlag: boolean;
  // #首页、列表页是否要展示登录激励入口
  isShowLoginGuide: boolean;
  // #订单详情页是否调新的门店政策接口(18862/getBookingNotice)
  isGetBookingNotice: boolean;
  // #列表页哪些城市开启货架
  goodsShelfCityList: string[];
  // #首页挽留问卷调查组件
  surveySceneId: string;
  // #强制用户升级弹窗
  upgradeModalShowEndVersion: string;
  // 订单详情页统一发券弹窗
  popCouponSceneId: string;
  // 订祥取消原因文案匹配
  noCarReason: string;
  // 机场转场提示
  airportTransfer: any;
  // 首页弹框展示优先级
  modalsSort: string[];
  // 首页租车保障背景图url
  ensureTipImageUrl: string;
  // 首页租车保障背景图url-利益点显性化B版
  ensureTipImageUrlNew: string;
  // 首页租车保障背景图url
  ensureTipJumpUrl: string;
  // #开启列表页预请求渠道号
  preFetchListChannelIds: string[];
  fuelTypeModalInfo: any;
  // 首页使用graphql获取二屏数据
  getDataByGraphql: boolean;
  // 可持续计划弹层内容
  lessModalInfo: LessModalInfoType;
  // 头部是否展示im入口
  isShowIm: boolean;
  // 海外保险起赔额说明弹窗文案
  excessIntroduce: string;
  // 海外保险二期起赔额说明弹窗文案
  excessIntroduceNew: string;
  // 海外保险二期起赔额说明弹窗背景图1
  excessIntroduceBgOneNew: string;
  // 海外保险二期起赔额说明弹窗背景图2
  excessIntroduceBgTwoNew: string;
  // 海外保险二期起赔额说明弹窗描述
  excessIntroduceDesc: string;
  // 境外个人信息授权勾选开关
  personalInfoAuthCheck: boolean;
  // 首页展示营业执照开关1表示全国；2表示仅海南或者其他指定省；0表示不展示
  showBusinessLicense: ShowBusinessLicenseType;
  // 营业执照指定的省份
  businessLicenseProvinceIds: number[];
  // 首页发券弹窗sceneId
  homePopCouponSceneId: number;
  // 交叉导流页发券弹窗sceneId
  RecommendVehiclePopCouponSceneId: number;
  // ETC介绍弹层配置
  etcIntroModalConfig: ETCConfigData;
  // ETC使用帮助配置
  etcUseHelperModalConfig: ETCUseHelperTabSection[];
  // 显示海外列表提示用户停留太久的定时器弹窗
  showListTimer: boolean;
  // 订详inps配置id
  inpsSceneId: number;
  // 是否展示国内货架页分享按钮
  isShowIsdShareBtn: boolean;
  // 门店信息反馈开关
  isSurvey: boolean;
}

export enum ShowBusinessLicenseType {
  wholeCountry = 1,
  provinces = 2,
  noShow = 0,
}
