export enum IGuidInfoType {
  PickUp = 1,
  DropOff = 2,
  Merge = 3,
}

export interface IMergeGuidInfo {
  storeGuid: string;
  address: string;
  type: IGuidInfoType;
}

export interface ExtMapType {
  key?: string;
}

export interface BaseResponseType {
  isSuccess?: boolean;
  code?: string;
  returnMsg?: string;
  requestId?: string;
  cost?: number;
  showMessage?: string;
  extMap?: ExtMapType;
  extraIndexTags?: ExtMapType;
  apiResCodes?: Array<string>;
  hasResult?: boolean;
  errorCode?: string;
  message?: string;
}

export interface ErrorFieldsType {
  FieldName?: string;
  ErrorCode?: string;
  Message?: string;
}

export interface ErrorsType {
  Message?: string;
  ErrorCode?: string;
  StackTrace?: string;
  SeverityCode?: any;
  ErrorFields?: Array<ErrorFieldsType>;
  ErrorClassification?: any;
}

export interface SubListType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  labelCode?: string;
  positionCode?: string;
  groupCode?: string;
  tagGroups?: number;
  tagSortNum?: number;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
}

export interface SubListType2 {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  subList?: Array<SubListType>;
  labelCode?: string;
  positionCode?: string;
  groupCode?: string;
  tagGroups?: number;
  tagSortNum?: number;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
}
export enum VendorTagShowLayerEnum {
  out = 1, // 展示在外层
  easyLife = 0, // 展示在内层
}
export interface VendorTagType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: VendorTagShowLayerEnum;
  colorCode?: string;
  subList?: Array<SubListType2>;
  labelCode?: string;
  positionCode?: string;
  groupCode?: string;
  tagGroups?: number;
  tagSortNum?: number;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
}

export interface VendorInfoType {
  bizVendorCode?: string;
  vendorName?: string;
  vendorImageUrl?: string;
  vendorCode?: string;
  isBroker?: boolean;
  platformCode?: string;
  platformName?: string;
  haveCoupon?: boolean;
  vendorTag?: VendorTagType;
  preAuthType?: number;
  supportPreAuth?: number;
  prePayType?: number;
}

export interface WorkTimeType {
  openTimeDesc?: string;
  openTime?: string;
  closeTime?: string;
  description?: string;
}

export interface StoreServiceListType {
  title?: string;
  description?: string;
  typeCode?: string;
}

export interface CommentListType {
  avatar?: string;
  userName?: string;
  userRate?: number;
  userRateLevel?: number;
  content?: string;
}

export interface CommentInfoType {
  level?: string;
  vendorDesc?: string;
  commentCount?: number;
  qCommentCount?: number;
  qExposed?: string;
  overallRating?: string;
  maximumRating?: number;
  commentLabel?: string;
  hasComment?: number;
  noComment?: string;
  link?: string;
  commentList?: Array<CommentListType>;
}

export interface ScoreInfoType {
  store?: number;
  car?: number;
  suport?: number;
  clean?: number;
  exposed?: number;
}

export interface ITags {
  labelCode?: string;
  titleExtra?: string;
}

export interface PickupStoreInfoType {
  storeCode?: string;
  bizVendorCode?: string;
  telephone?: string;
  storeName?: string;
  address?: string;
  longitude?: number;
  latitude?: number;
  mapUrl?: string;
  storeGuild?: string;
  storeGuildSplit?: string;
  storeLocation?: string;
  storeWay?: string;
  workTime?: WorkTimeType;
  storeServiceList?: Array<StoreServiceListType>;
  storeType?: number;
  shuttlePointAddress?: string;
  shuttlePointName?: string;
  shuttlePointWorkTime?: WorkTimeType;
  shuttlePointLongitude?: number;
  shuttlePointLatitude?: number;
  countryId?: number;
  countryName?: string;
  provinceId?: number;
  provinceName?: string;
  cityId?: number;
  cityName?: string;
  isAirportStore?: boolean;
  distance?: string;
  productCount?: number;
  lowestPrice?: number;
  commentInfo?: CommentInfoType;
  type?: number;
  isrentcent?: number;
  pickUpLevel?: number;
  pickOffLevel?: number;
  pickUpOnDoor?: boolean;
  returnOnDoor?: boolean;
  sendTypeForPickUpCar?: number;
  sendTypeForPickOffCar?: number;
  psend?: number;
  rsend?: number;
  freeShuttle?: boolean;
  scoreInfo?: ScoreInfoType;
  wayInfo?: number;
  showType?: number;
  distanceDesc?: string;
  tags?: Array<ITags>;
}

export interface RentCenterType {
  id?: number;
  name?: string;
  icon?: string;
  backImage?: string;
  images?: Array<string>;
  index?: number;
  isNew?: number;
  address?: string;
  labels?: Array<string>;
  lat?: string;
  lng?: string;
  filterCode?: string;
  fromTime?: string;
  toTime?: string;
}

export interface SimilarVehicleInfosType {
  vehicleCode?: string;
  vehicleName?: string;
  vehicleImageUrl?: string;
}

export interface VendorSimilarVehicleInfosType {
  bizVendorCode?: string;
  vendorName?: string;
  vendorLogo?: string;
  similarVehicleInfos?: Array<SimilarVehicleInfosType>;
}

export interface PicListType {
  imageUrl?: string;
  imageClass?: number;
  imageType?: number;
  sortNum?: number;
}

export interface SourcePicInfosType {
  source?: number;
  type?: number;
  sourceName?: string;
  picList?: Array<PicListType>;
}

export interface VehicleInfoType {
  brandId?: number;
  brandEName?: string;
  brandName?: string;
  name?: string;
  zhName?: string;
  vehicleCode?: string;
  imageUrl?: string;
  groupCode?: string;
  groupSubClassCode?: string;
  groupName?: string;
  transmissionType?: number;
  transmissionName?: string;
  passengerNo?: number;
  doorNo?: number;
  luggageNo?: number;
  displacement?: string;
  struct?: string;
  fuel?: string;
  gearbox?: string;
  driveMode?: string;
  driveType?: string;
  style?: string;
  imageList?: Array<string>;
  userRealImageList?: Array<string>;
  storeRealImageList?: Array<string>;
  similarImageList?: Array<string>;
  specializedImages?: Array<string>;
  vedio?: string;
  isSpecialized?: boolean;
  isHot?: boolean;
  recommendDesc?: string;
  hasConditioner?: boolean;
  conditionerDesc?: string;
  spaceDesc?: string;
  similarCommentDesc?: string;
  vendorSimilarVehicleInfos?: Array<VendorSimilarVehicleInfosType>;
  vehicleAccessoryImages?: Array<string>;
  license?: string;
  licenseStyle?: string;
  licenseDescription?: string;
  realityImageUrl?: string;
  sourcePicInfos?: Array<SourcePicInfosType>;
  luggageSize?: string;
  modeYear?: string;
  versionName?: string;
  vehicleLevel?: string;
  fuelNew?: string;
  oilType?: number;
  userRealImageCount?: number;
  multimediaAlbums?: any;
}

export interface TitleAbstractItem {
  title: string;
  sortNum: number;
  description?: string;
  code?: string;
  type?: string;
  typeDesc?: string;
}

export interface EasyLifeInfoType {
  isEasyLife?: boolean;
  tagList?: Array<VendorTagType>;
  subTitle?: string;
  titleAbstract?: TitleAbstractItem[];
}

export interface StringObjsType {
  content?: string;
  style?: string;
  url?: string;
}

export interface ComplexSubTitleType {
  contentStyle?: string;
  stringObjs?: Array<StringObjsType>;
}

export interface LabelsType {
  title?: string;
  subTitle?: string;
  code?: string;
  type?: number;
  grade?: number;
  sortNum?: number;
}

export interface PriceDailysType {
  date?: string;
  oDprice?: string;
  priceStr?: string;
  showType?: number;
}

export interface ItemsType {
  title?: string;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
  priceDailys?: Array<PriceDailysType>;
  hourDesc?: string;
  great?: boolean;
}

export interface ItemsType2 {
  title?: string;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  positiveDesc?: string;
  needDeposit?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: Array<ItemsType>;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
  priceDailys?: Array<PriceDailysType>;
  hourDesc?: string;
  great?: boolean;
}
export interface MaterialsSubObject {
  title?: string;
  subTitle?: string;
  note?: string;
  content?: Array<string>;
}

export interface PickUpMaterialsType {
  title?: string;
  subTitle?: string;
  subObject: Array<MaterialsSubObject>;
}

export interface CancelRuleInfoType {
  title?: string;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: Array<ItemsType2>;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
  priceDailys?: Array<PriceDailysType>;
  hourDesc?: string;
  cancelType?: number;
  depositSimpleDescs?: any;
}

export interface SubObjectType {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  contentObject?: Array<ComplexSubTitleType>;
  summaryContent?: Array<string>;
  type?: number;
  code?: string;
  urlName?: string;
  url?: string;
  urlList?: Array<string>;
  note?: string;
  sortNum?: number;
}

export interface SubObjectType2 {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  contentObject?: Array<ComplexSubTitleType>;
  summaryContent?: Array<string>;
  type?: number;
  code?: string;
  urlName?: string;
  url?: string;
  urlList?: Array<string>;
  subObject?: Array<SubObjectType>;
  note?: string;
  sortNum?: number;
}

export enum PromptInfosCodeEnum {
  confirm = 'confirm',
  mileage = 'mileage',
  delay = 'delay',
  limitArea = 'limitArea',
  limitRule = 'limitRule',
}

export enum PromptInfosTypeEnum {
  positive = 0,
  negative = 1,
}
export interface PromptInfosType {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  contentObject?: Array<ComplexSubTitleType>;
  summaryContent?: Array<string>;
  type?: PromptInfosTypeEnum;
  code?: PromptInfosCodeEnum;
  urlName?: string;
  url?: string;
  urlList?: Array<string>;
  subObject?: Array<SubObjectType2>;
  note?: string;
  sortNum?: number;
  iconUrl?: string;
}

export interface QueryVehicleDetailInfoResponseType {
  baseResponse?: BaseResponseType;
  ResponseStatus?: ResponseStatusType;
  vendorInfo?: VendorInfoType;
  isSelected?: boolean;
  pickupStoreInfo?: PickupStoreInfoType;
  returnStoreInfo?: PickupStoreInfoType;
  rentCenter?: RentCenterType;
  rRentCenter?: RentCenterType;
  vehicleInfo?: VehicleInfoType;
  vehicleTags?: Array<VendorTagType>;
  easyLifeInfo?: EasyLifeInfoType;
  commentInfo?: CommentInfoType;
  cancelRuleInfo?: CancelRuleInfoType;
  depositInfo?: CancelRuleInfoType;
  promptInfos?: Array<PromptInfosType>;
  vcCacheKey?: string;
  vendorImUrl?: string;
  pickUpMaterials?: PickUpMaterialsType;
  storeGuidInfos?: Array<IMergeGuidInfo>;
  priceExplain?: ExplainObject;
  insuranceIntroduction?: any;
  carAge?: string;
  title?: [];
  carAgeTitle: any;
  labelsInfo?: [];
}

export interface OSDQueryOrderRequestType {
  head?: MobileRequestHead | null;
  baseRequest?: BaseRequest | null;
  /**
   * 订单ID
   */
  orderId?: number | null;
  isHistory?: boolean | null;
  ticket?: string | null;
}
export interface OSDQueryOrderResponseType {
  responseStatus?: ResponseStatusType | null;
  locale?: string | null;
  naked?: boolean | null;
  /**
   * 鉴权是否成功
   */
  checkSuccess?: boolean | null;
  /**
   * 鉴权失败时返回，神盾加密后的邮箱，二次鉴权使用
   */
  shieldEmail?: string | null;
  /**
   * 发送确认邮件记录
   */
  lastSendEmail?: string | null;
  /**
   * 订单基础信息
   */
  orderBaseInfo?: OrderBaseInfoDTO | null;
  /**
   * 继续支付信息
   */
  continuePayInfo?: ContinuePayInfoDTO | null;
  /**
   * 订单价格信息
   */
  orderPriceInfo?: OrderDetailPriceDTO | null;
  /**
   * 车型信息
   */
  vehicleInfo?: VehicleInfoDTO | null;
  /**
   * 供应商信息
   */
  vendorInfo?: VendorInfoDTO | null;
  /**
   * 燃油政策
   */
  fuelIsGiven?: boolean | null;
  fuelInfo?: IbuFuelInfo | null;
  /**
   * 里程限制
   */
  mileageAllowance?: MileInfo | null;
  /**
   * 取车门店信息
   */
  pickupStore?: LocationInfoDTO | null;
  /**
   * 还车门店信息
   */
  returnStore?: LocationInfoDTO | null;
  /**
   * 驾驶员信息
   */
  driverInfo?: DriverInfoDTO | null;
  /**
   * 套餐包含列表
   */
  packageIncludes?: string[] | null;
  /**
   * 套餐包含列表
   */
  groupingPackageIncludes?: GroupPackageItems[] | null;
  /**
   * 附加产品列表
   */
  extraInfos?: ExtraInfoDTO[] | null;
  /**
   * 额外驾驶员数量
   */
  additionalDriverCount?: number | null;
  /**
   * 信用卡信息
   */
  creditCardInfo?: CreditCardInfo | null;
  /**
   * 身份信息描述
   */
  identityDescription?: SimpleModel | null;
  /**
   * 保险起赔额描述
   */
  insuranceDescriptions?: IbuInsuranceDetailDTO[] | null;
  /**
   * 保险列表, 包含已购，未购等
   */
  insurance?: InsuranceDetailDTO[] | null;
  /**
   * 取消规则描述
   */
  cancelRuleInfo?: CancelRuleInfo | null;
  /**
   * 驾照描述
   */
  driverDescriptions?: MultiModel[] | null;
  /**
   * 温馨提示
   */
  warmTips?: string[] | null;
  /**
   * 年龄限制
   */
  ageInfo?: AgeInfo | null;
  /**
   * 到达信息
   */
  arrivalDetails?: SimpleModel[] | null;
  /**
   * 跨境信息
   */
  crossBorderDesc?: SimpleModel | null;
  /**
   * 退款进度
   */
  refundProgressList?: OrderRefundProgress[] | null;
  /**
   * ISD订单价格
   */
  isdFeeInfo?: IsdFeeInfo | null;
  /**
   * 订单价格（海内外通用）
   */
  feeInfo?: FeeDetailInfoV2 | null;
  isdVendorInsurance?: IsdVendorInsurance | null;
  /**
   * 携程保险信息
   */
  ctripInsuranceInfos?: CtripInsuranceInfo[] | null;
  /**
   * 可用保险展示信息
   */
  osdAvailableInsuranceDescInfo?: AvailableInsuranceDescInfo | null;
  /**
   * OSD: 开关一：app保险加购开关
   */
  appOrderDetailIsAddInsuranceNeeded?: boolean | null;
  /**
   * OSD:开关二：app保险理赔开关
   */
  appOrderDetailIsSettlementOfClaimOpen?: boolean | null;
  /**
   * 在线预授权信息
   */
  onlinePreAuth?: OnlinePreAuthDTO[] | null;
  /**
   * 发票信息
   */
  invoice?: InvoiceDto | null;
  /**
   * 租车中心
   */
  rentCenter?: RentCenterDto | null;
  /**
   * 修改订单需要的实体
   */
  modifyInfo?: ModifyOrderInfo | null;
  /**
   * 续租
   */
  orderRenewalEntry?: OrderRenewalInfo | null;
  /**
   * 强售保险提醒
   */
  insuranceTips?: KeyAndValue[] | null;
  baseResponse?: BaseResponse | null;
  /**
   * 保险列表
   */
  packageInfos?: OSDPackageInfo[] | null;
  /**
   * 产品信息
   */
  productDetails?: PackageDetail[] | null;
  /**
   * 补款信息
   */
  addPayments?: OrderAdditionalPayment[] | null;
  /**
   * 问答
   */
  faqListInfo?: FAQListInfo | null;
  /**
   * 租车管家新的url
   */
  isdCarMgImUrl?: string | null;
  creditInfo?: CreditInfo | null;
  isAlipay?: boolean | null;
  freeDeposit?: FreeDepositDTO | null;
  insuranceAndXProduct?: InsuranceAndXProduct[] | null;
  /**
   * 额外附加信息
   */
  extendedInfo?: ExtendedInfo | null;
  /**
   * 续租记录
   */
  renewalOrders?: RenewalOrderDTO[] | null;
  /**
   * 标签信息
   */
  labelsInfo?: LabelInfoDto[] | null;
  /**
   * 什么是冻结押金文案
   */
  freezeDepositExplain?: FreezeDepositExplain[] | null;
  modifyInfoDto?: ModifyInfoDTO | null;
  insuranceAndXProductDesc?: InsuranceAndXProductDesc[] | null;
  /**
   * 0 正常，1手机号查单
   */
  authType?: number | null;
  /**
   * 续租提示
   */
  renewalTips?: RenewalTips | null;
  /**
   * 超级会员
   */
  superVip?: SuperVipDTO | null;
  /**
   * 是否跳转确认页
   */
  jumpModifyToPay?: boolean | null;
  /**
   * 还车租车中心
   */
  rRentCenter?: RentCenterDto | null;
  /**
   * 申请退违约金信息
   */
  refundPenaltyInfo?: RefundPenaltyDTO | null;
  /**
   * 供应商im链接
   */
  vendorImUrl?: string | null;
  /**
   * 额外设备说明文案
   */
  extraInfoDesc?: string | null;
  earlyReturnRecord?: EarlyReturnRecord | null;
  /**
   * 履约变更记录
   */
  orderFulfillmentModifyInfo?: OrderFulfillmentModifyInfoDTO | null;
  /**
   * 履约信息变更提示
   */
  orderFulfillmentModifyInfoTip?: string | null;
}
export enum OrderFulfillmentModifyInfoStatusEnum {
  Unconfirmed = '1',
  Confirmed = '2',
  Rejected = '3',
  Cancelled = '4',
  PartiallyConfirmed = '5',
}
export interface OrderFulfillmentModifyInfoDTO {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 副标题
   */
  subTitle?: string | null;
  /**
   * 状态 1-未确认 2-已确认 3-已拒绝 4-已撤销 5-部分确认
   */
  status?: OrderFulfillmentModifyInfoStatusEnum | null;
  /**
   * 变更信息
   */
  fulfillmentModify?: FulfillmentModifyDTO | null;
  /**
   * 变更单id
   */
  modifyId?: number | null;
}
export interface FulfillmentModifyDTO {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 描述
   */
  desc?: string | null;
  /**
   * 详细变更信息
   */
  fulfillmentModifyInfoList?: FulfillmentModifyInfoDTO[] | null;
}
export enum FulfillmentModifyInfoTypeEnum {
  Time = 1,
  Location = 2,
}
export enum FulfillmentModifyInfoStatusEnum {
  Unconfirmed = 0,
  Confirmed = 1,
  Rejected = 2,
  Cancelled = 3,
  Invalid = 4,
}
export interface FulfillmentModifyInfoDTO {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 状态0-未确认 1-已确认 2-已拒绝 3-已撤销 4-已失效
   */
  status?: FulfillmentModifyInfoStatusEnum | null;
  /**
   * 修改后的信息
   */
  modifyInfo?: string | null;
  /**
   * 修改前信息
   */
  originalInfo?: string | null;
  /**
   * 修改类型，1-时间，2-地点
   */
  type?: FulfillmentModifyInfoTypeEnum | null;
  /**
   * 描述
   */
  desc?: string | null;
}
export interface EarlyReturnRecord {
  /**
   * 费用明细
   */
  feeList?: FeeItem[] | null;
  /**
   * 费用汇总信息
   */
  earlyReturnPriceInfo?: EarlyReturnPriceInfo | null;
  /**
   * 是否已经手动修改过，true改过
   */
  manually?: boolean | null;
  /**
   * 仅供参考或手动修改后的提示
   */
  referenceText?: string | null;
  /**
   * 申请时间
   */
  applyTime?: string | null;
  /**
   * 申请的还车时间
   */
  returnTime?: string | null;
  /**
   * 原始还车时间
   */
  oldReturnTime?: string | null;
  /**
   * 1，生效，0已被撤销
   */
  applyStatus?: number | null;
  pickUpTime?: string | null;
  /**
   * 原始租期
   */
  oldTerm?: number | null;
  /**
   * 新租期
   */
  newTerm?: number | null;
  policyText?: TitleAndDesc | null;
  /**
   * 可以取消的时间
   */
  cancellationTime?: string | null;
}
export interface TitleAndDesc {
  title?: string | null;
  desc?: string | null;
}
export interface EarlyReturnPriceInfo {
  /**
   * 原始总价
   */
  originTotalFee?: number | null;
  newTotalFee?: number | null;
  /**
   * 违约金比例
   */
  penaltyRate?: number | null;
  /**
   * 需要退补款金额，负数就是退款
   */
  payAmount?: number | null;
  /**
   * 违约金金额
   */
  penaltyAmount?: number | null;
}
export interface FeeItem {
  serviceCode?: string | null;
  title?: string | null;
  /**
   * 例如活动为标题时，子标题时活动名
   */
  subTitle?: string | null;
  /**
   * 字体样式，b加粗，无/空则默认
   */
  fieldStyle?: string | null;
  /**
   * 改变类型，0 无变化，1上涨，2下降，3不可用
   */
  changeType?: number | null;
  /**
   * 0, 普通费用项，1活动，2优惠券，3汇总类费用
   */
  type?: number | null;
  oldPrice?: FeeSubItem | null;
  newPrice?: FeeSubItem | null;
  /**
   * 是否是必选的费用
   */
  fixed?: boolean | null;
}
export interface FeeSubItem {
  subTitle?: string | null;
  amount?: number | null;
  price?: number | null;
  count?: number | null;
  unit?: string | null;
  localCurrencyCode?: string | null;
  /**
   * 日均价描述
   */
  dPriceDesc?: string | null;
  /**
   * 小时费描述
   */
  hourDesc?: string | null;
  /**
   * 日价
   */
  priceDailys?: DailyPriceDTO[] | null;
}
export interface DailyPriceDTO {
  /**
   * 日期
   */
  date?: string | null;
  /**
   * 展示金额
   */
  priceStr?: string | null;
  /**
   * 原天价
   */
  oDprice?: string | null;
  /**
   * 展示类型（前端样式用） 0 默认宽度， 1 day1-day2 展示宽度
   */
  showType?: number | null;
}
export interface RefundPenaltyDTO {
  /**
   * 申请进度 0 已提交 1 已同意 2 已拒绝
   */
  status?: number | null;
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 最晚处理时间
   */
  lastRefundTime?: string | null;
  /**
   * 申请退款金额
   */
  refundAmount?: number | null;
  /**
   * 订单违约金
   */
  penaltyAmount?: number | null;
  attrDto?: AttrDto | null;
}
export interface AttrDto {
  /**
   * 排序
   */
  sort?: number | null;
  /**
   * true 历史消息，false 当前消息，unknown，需要前端看曝光次数
   */
  history?: string | null;
}
export interface SuperVipDTO {
  /**
   * 超级会员
   */
  title?: string | null;
  /**
   * 子标题，会员状态对应的描述文案
   */
  subTitle?: string | null;
  /**
   * 状态，0-待支付，1-已支付，2-开卡失败，3-已取消
   */
  status?: number | null;
  /**
   * 取消政策超会文案
   */
  cancelText?: string | null;
  /**
   * 超会入口
   */
  button?: Button | null;
}
export interface Button {
  title?: string | null;
  description?: string | null;
  /**
   * 状态 0 不可用  1可用
   */
  statusType?: number | null;
  actionUrl?: string | null;
  icon?: string | null;
  type?: number | null;
}
export interface RenewalTips {
  /**
   * 续租文案
   */
  renewalTags?: RenewalTag[] | null;
}
export interface RenewalTag {
  /**
   * 文案描述
   */
  desc?: string | null;
  type?: number | null;
  code?: string | null;
  /**
   * 颜色
   */
  color?: string | null;
}
export interface InsuranceAndXProductDesc {
  /**
   * 文案类型：1-自营险产品购买说明 2-保险类产品升级说明 3-x产品购买说明
   */
  type?: number | null;
  /**
   * 文案描述
   */
  desc?: string[] | null;
}
export interface ModifyInfoDTO {
  /**
   * 修改前单号
   */
  beforeOrderId?: number | null;
  /**
   * 修改后单号
   */
  afterOrderId?: number | null;
  tipInfo?: ModifyTipInfo[] | null;
  modifyCancelRules?: ModifyCancelRule[] | null;
  /**
   * 最近一次的修改结果-3 修改失败 -2 超时未支付取消 -1 已取消 1 进行中 2 修改成功
   */
  modifyStatus?: number | null;
  /**
   * 最近一次的修改类型 1-原单修改、2-取消重订
   */
  modifyType?: number | null;
  /**
   * 取消重订说明文案
   */
  reorderTip?: string | null;
}
export interface ModifyCancelRule {
  /**
   * 取消文案
   */
  code?: string | null;
  content?: string | null;
  /**
   * 颜色，1 绿色 2 红色
   */
  color?: number | null;
  /**
   * 高亮展示的字
   */
  highLight?: string | null;
  /**
   * 弹窗文案
   */
  contentAlert?: string[] | null;
}
export interface ModifyTipInfo {
  /**
   * 1、修改成功toast文案 2、原单修改失败取消政策 3、修改失败提示文案 4、取消重订成功、失败提
   * 示 5、超时未支付失败文案
   */
  code?: string | null;
  content?: string | null;
  /**
   * 取消相关的描述的颜色，1 绿色
   */
  cancelTipColor?: number | null;
  title?: string | null;
}
export interface FreezeDepositExplain {
  contentStyle?: string | null;
  stringObjs?: StringObjs[] | null;
}
export interface StringObjs {
  content?: string | null;
  style?: string | null;
}
export interface LabelInfoDto {
  code?: string | null;
  type?: string | null;
  sort?: string | null;
  name?: string | null;
  desc?: string | null;
  marketGroupCode?: string | null;
  /**
   * 前端必传，目前暂时固定为10
   */
  colorCode?: string | null;
  /**
   * true 则展示在订详第二行，属于【服务+政策】标签
   */
  serviceType?: boolean | null;
  /**
   * mergeId 用于前端合并标签
   */
  mergeId?: number | null;
}
export interface RenewalOrderDTO {
  /**
   * 订单号
   */
  orderId?: number | null;
  /**
   * 续租订单号
   */
  renewalOrderId?: number | null;
  /**
   * 应收
   */
  totalAmount?: number | null;
  /**
   * 续租开始时间 格式：yyyy-MM-dd HH:mm:ss
   */
  startDate?: string | null;
  /**
   * 续租截止时间 格式：yyyy-MM-dd HH:mm:ss
   */
  endDate?: string | null;
  /**
   * 续租时长，单位：小时
   */
  hours?: number | null;
  /**
   * 续租来源 1-客人 2-客服 3-供应商 必填
   */
  source?: number | null;
  /**
   * 支付状态 0-待支付 1-支付中 2-支付成功 3-支付失败 4-部分退款 5-全额退款
   */
  payStatus?: number | null;
  /**
   * 支付状态描述
   */
  payStatusDesc?: string | null;
  /**
   * 续租状态 0-已提交 1-续租成功 2-续租失败 3-已取消
   */
  orderStatus?: number | null;
  /**
   * 续租状态 描述原订单的续租状态
   */
  orderStatusDesc?: string | null;
  /**
   * 取消类型 1-客人取消 2-客服取消 3-供应商取消 4-续租失败取消 5-超时未支付取消
   */
  cancelType?: number | null;
  /**
   * 违约金 默认：0
   */
  penaltyAmount?: number | null;
  /**
   * 退款流水号
   */
  refundBillNo?: string | null;
  /**
   * 费用明细
   */
  chargeInfoList?: ChargeListDTO[] | null;
  /**
   * 续租自营险
   */
  ctripInsurance?: RenewalCtripInsurance | null;
  /**
   * 续租提示
   */
  notice?: string | null;
  /**
   * 取消规则
   */
  cancelRule?: OrderCancelRule[] | null;
  /**
   * 退款进度
   */
  orderRefundProgressList?: OrderRefundProgress[] | null;
  /**
   * 提交时间
   */
  createTime?: string | null;
  /**
   * 支付成功时间
   */
  paySuccessTime?: string | null;
  /**
   * 续租成功时间
   */
  confirmTime?: string | null;
  /**
   * 取消时间
   */
  cancelTime?: string | null;
  /**
   * 续租状态
   */
  renewalStatus?: string | null;
  /**
   * 续租状态名
   */
  renewalStatusName?: string | null;
  /**
   * 续租列表状态描述
   */
  renewalStatusNameDesc?: string | null;
  /**
   * 续租状态描述
   */
  renewalStatusDesc?: string | null;
  /**
   * 能否取消
   */
  canCancel?: boolean | null;
  /**
   * 支付方式名称
   */
  payWayList?: ChargeItemDetail[] | null;
  /**
   * 支付方式
   */
  payMethodStr?: string | null;
}
export interface ChargeItemDetail {
  title?: string | null;
  subTitle?: string | null;
  description?: string | null;
  /**
   * 对应有换行的描述
   */
  descList?: string[] | null;
  /**
   * 1001 租车费，其他参考 http://conf.ctripcorp.com/pages/view
   * page.action?pageId=460368041
   */
  code?: string | null;
  type?: number | null;
  /**
   * 展示的数量
   */
  size?: string | null;
  /**
   * 数量
   */
  count?: number | null;
  /**
   * 单位
   */
  unit?: string | null;
  include?: boolean | null;
  currencyCode?: string | null;
  currentDailyPrice?: number | null;
  currentTotalPrice?: number | null;
  localCurrencyCode?: string | null;
  localDailyPrice?: number | null;
  localTotalPrice?: number | null;
  showFree?: boolean | null;
  retractable?: boolean | null;
  showPrice?: string | null;
  payMode?: number | null;
  sortNum?: number | null;
  items?: ChargeItemDetail[] | null;
  notices?: string[] | null;
  labels?: LabelDetail[] | null;
  positiveStatus?: boolean | null;
  isInstantConfirm?: boolean | null;
  /**
   * 日均价描述
   */
  dPriceDesc?: string | null;
  /**
   * 小时费描述
   */
  hourDesc?: string | null;
  /**
   * 日价
   */
  priceDailys?: DailyPriceDTO[] | null;
  /**
   * 划线价
   */
  originDailyPrice?: number | null;
  /**
   * 活动ID
   */
  activityId?: number | null;
}
export interface LabelDetail {
  title?: string | null;
  subTitle?: string | null;
  code?: string | null;
  type?: number | null;
  grade?: number | null;
  sortNum?: number | null;
}
export interface OrderCancelRule {
  /**
   * 是否免费取消
   */
  free?: number | null;
  /**
   * 取消规则标题
   */
  title?: string | null;
  /**
   * 取消规则内容
   */
  context?: string | null;
  /**
   * 取消时间
   */
  time?: string | null;
  /**
   * 当前时间是否适用
   */
  hit?: boolean | null;
  /**
   * 区间开始时间
   */
  start?: string | null;
  /**
   * 区间结束
   */
  end?: string | null;
}
export interface RenewalCtripInsurance {
  /**
   * BU渠道号（度假保险平台提供）可能已经废弃
   */
  appId?: number | null;
  /**
   * 保险条款链接
   */
  clauseUrl?: string | null;
  /**
   * 特别说明
   */
  description?: string | null;
  /**
   * 产品全名
   */
  fullName?: string | null;
  /**
   * 保险供应商ID
   */
  insuranceVendorId?: number | null;
  /**
   * 名称
   */
  name?: string | null;
  /**
   * 产品ID
   */
  productId?: number | null;
  /**
   * 卖价
   */
  sellingPrice?: number | null;
  /**
   * 保司代码 (如:平安保险PAZC)
   */
  insuranceCompanyCode?: string | null;
  /**
   * 产品Code
   */
  productCode?: string | null;
  /**
   * 保险状态 0：未下单，1：已下单，2,：已取消，3：取消失败，4：待回调，5：超时未回调/回调失败
   */
  singleStatus?: number | null;
  /**
   * 保险平台订单号
   */
  insuranceOrderId?: string | null;
  /**
   * 我携保险订单号
   */
  orderId?: number | null;
  /**
   * 保险确认号
   */
  insuranceConfirmNo?: string | null;
  /**
   * 保险详情url
   */
  detailUrl?: string | null;
  effectDate?: string | null;
  expiryDate?: string | null;
  expiryDesc?: string | null;
  /**
   * 是否免费续保的保险
   */
  freeRenewalInsurance?: boolean | null;
}
export interface ChargeListDTO {
  /**
   * 费用code
   */
  chargeCode?: string | null;
  /**
   * 费用名称
   */
  chargeName?: string | null;
  /**
   * 费用描述
   */
  chargeDesc?: string | null;
  /**
   * 数量
   */
  num?: number | null;
  /**
   * 费用单价
   */
  unitPrice?: number | null;
  /**
   * 费用单位
   */
  unit?: string | null;
  /**
   * 费用总价
   */
  totalPrice?: number | null;
  /**
   * 费用天数
   */
  quantity?: number | null;
  /**
   * 携程费用编号
   */
  ctripChargeCode?: string | null;
  /**
   * 携程费用名称
   */
  ctripChargeName?: string | null;
  /**
   * 日历价
   */
  priceDailys?: DailyPriceDTO[] | null;
  /**
   * 日均价描述
   */
  dPriceDesc?: string | null;
  /**
   * 小时费描述
   */
  hourDesc?: string | null;
}
export interface ExtendedInfo {
  /**
   * IM顶部开关
   */
  showCustomerCallModal?: boolean | null;
  /**
   * 机场赠送
   */
  giftText?: ItemInfo | null;
  /**
   * 其他说明，里程，燃油，限制区域 type 1 里程限制 2 油费说明， 3限制出行区域， 4 航班延
   * 误
   */
  orderExtDescList?: ExplainObject[] | null;
  /**
   * 航班延误政策
   */
  flightDelayRule?: FlightDelayRetentionRule | null;
  /**
   * 地址类型，0，租车中心项目订单，1租车中心项目之后订单
   */
  locationType?: number | null;
  /**
   * 订详页面底部描述
   */
  callBarDesc?: string | null;
  /**
   * 取车材料的描述文案
   */
  pickMaterialsText?: string[] | null;
  /**
   * title: {送/取车员姓名},actionUrl:{送/取车员电话}，type:1送，2取
   */
  storeAttendant?: Button | null;
  /**
   * 新无忧租标记字段
   */
  newNoWorry?: boolean | null;
  /**
   * 交易提醒文案
   */
  traderRemind?: string | null;
  /**
   * 卡拉比3期开关，开启1
   */
  klbVersion?: number | null;
  /**
   * 0-不是无忧租，1-2022之前的版本，2-2022之后的无忧租版本
   */
  noWorryVersion?: number | null;
  /**
   * 无忧租标签和提示
   */
  easyLifeInfo?: SimpleEasyLifeInfo | null;
  /**
   * 取车材料（身份证明，驾照政策，信用卡，电子提车凭证）
   */
  pickUpMaterials?: ExplainObject[] | null;
}
export interface SimpleEasyLifeInfo {
  /**
   * 无忧租副标题
   */
  subTitle?: string | null;
  tagList?: SimpleObject[] | null;
  /**
   * 无忧租简述
   */
  titleAbstract?: KeyAndValue[] | null;
}
export interface SimpleObject {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 类型
   */
  type?: number | null;
  /**
   * code
   */
  code?: string | null;
  /**
   * 类描述
   */
  typeDesc?: string | null;
  /**
   * 描述
   */
  description?: string | null;
  sortNum?: number | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 展示层级
   */
  showLayer?: number | null;
  /**
   * 颜色类型
   */
  colorCode?: number | null;
  subList?: SimpleObject[] | null;
  /**
   * 标题后缀
   */
  titleExtra?: string | null;
}
export interface FlightDelayRetentionRule {
  title?: string | null;
  description?: string | null;
  subDesc?: string | null;
  rules?: Rule[] | null;
  tips?: string[] | null;
}
export interface Rule {
  code?: string | null;
  title?: string | null;
  subTitle?: string | null;
  descs?: string[] | null;
}
export interface ExplainObject {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 内容
   */
  content?: string[] | null;
  /**
   * 简要类型
   */
  summaryContent?: string[] | null;
  /**
   * 类型
   */
  type?: number | null;
  code?: string | null;
  urlName?: string | null;
  /**
   * 图标或链接地址
   */
  url?: string | null;
  urlList?: string[] | null;
  subObject?: ExplainObject[] | null;
  /**
   * 注释内容
   */
  note?: string | null;
  sortNum?: number | null;
  contentObject?: ContentObject[] | null;
}
export interface ContentObject {
  /**
   * 一整行样式
   */
  contentStyle?: string | null;
  stringObjs?: StringObject[] | null;
}
export interface StringObject {
  content?: string | null;
  /**
   * 某行中某个关键字的特殊样式
   */
  style?: string | null;
  url?: string | null;
}
export interface ItemInfo {
  code?: string | null;
  title?: string | null;
  subTitle?: string | null;
  button?: Button | null;
  /**
   * 链接内容信息
   */
  linkContent?: HelpDetailLinkInfo[] | null;
  desc?: string | null;
  tips?: KeyAndValue | null;
  jsonData?: string | null;
  items?: ItemInfo[] | null;
  /**
   * 具有复杂样式的文案
   */
  contentObject?: ContentObject[] | null;
  /**
   * 位于下方的button。现在可能有多个按钮，位于不同位置
   */
  underButton?: Button | null;
}
export interface HelpDetailLinkInfo {
  /**
   * 标识ID
   */
  id?: number | null;
  /**
   * 标识title
   */
  title?: string | null;
  /**
   * 标识描述
   */
  desc?: string | null;
  /**
   * 若id为4，此字段值表示1-支付宝、2-微信、3、支付宝微信
   */
  type?: number | null;
  /**
   * 子列表
   */
  subItem?: HelpDetailLinkInfo[] | null;
}
export interface InsuranceAndXProduct {
  /**
   * 产品id
   */
  additionalId?: string | null;
  /**
   * 产品名称
   */
  name?: string | null;
  /**
   * 产品编号 1002-国内基础保障服务 2001-国内优享 2002-国内GPS 2003-国内儿童座
   * 椅 2011-国内尊享服务,200896-人生财物险
   */
  code?: string | null;
  /**
   * 产品标题
   */
  title?: string | null;
  /**
   * 产品说明 (保障内容描述、儿童座椅描述等)
   */
  description?: string[] | null;
  /**
   * 费用包含/不包描述tag
   */
  targetTag?: TargetTag[] | null;
  /**
   * 某些保险名称有特效的，0 无特效，1无忧租
   */
  specificName?: number | null;
  /**
   * *产品提供方（0-无，1-供应商 2-携程）
   */
  sourceFrom?: number | null;
  /**
   * 产品id
   */
  productId?: number | null;
  /**
   * 兼容人身保险的requestId
   */
  requestId?: string | null;
  /**
   * 兼容人身保险的orderTitle
   */
  orderTitle?: string | null;
  /**
   * 价格
   */
  price?: number | null;
  /**
   * 当地币种
   */
  localCurrencyCode?: string | null;
  /**
   * 当地币种总价
   */
  localTotalPrice?: number | null;
  /**
   * 当地币种总价
   */
  localDailyPrice?: number | null;
  /**
   * 当前币种总价
   */
  currentTotalPrice?: number | null;
  /**
   * 当前币种均价
   */
  currentDailyPrice?: number | null;
  /**
   * 当前币种
   */
  currentCurrencyCode?: string | null;
  /**
   * 数量
   */
  quantity?: number | null;
  /**
   * 数量名称
   */
  quantityName?: string | null;
  /**
   * 数量限制
   */
  maxQuantity?: number | null;
  /**
   * 现有库存
   */
  stock?: number | null;
  /**
   * 产品类别（1-租车保障及升级服务 2-附加产品）
   */
  group?: number | null;
  /**
   * 0未下单，1 待确认，2支付中，3已支付，4 不可加购, 5支付失败, 13已放弃
   */
  status?: number | null;
  /**
   * 是否是可升级的，true为升级项
   */
  canUpgrade?: boolean | null;
  /**
   * 升级所需要的金额
   */
  upgradeAmount?: number | null;
  /**
   * 额外描述：支付失败原因等
   */
  extDesc?: string | null;
  /**
   * 保险订单ID
   */
  insuranceOrderId?: string | null;
  /**
   * 能否去保代详情页面, 0-不可以，1-可以
   */
  toDetailStatus?: number | null;
  /**
   * 从何处购买的，1 售前，2 售后
   */
  boughtFrom?: number | null;
  /**
   * 事故处理
   */
  accident?: HelpDetailLinkInfo[] | null;
  /**
   * 服务详情底部文案描述
   */
  insContractBottomDesc?: string | null;
  /**
   * 底部文案描述
   */
  insServiceBottomDesc?: string | null;
  insBottomDesc?: BottomDesc | null;
  insAndXProductLabelInfos?: InsAndXProductLabelInfoDto[] | null;
}
export interface InsAndXProductLabelInfoDto {
  sort?: string | null;
  name?: string | null;
  /**
   * 颜色 0-灰色 1-蓝色
   */
  color?: string | null;
}
export interface BottomDesc {
  title?: string | null;
  desc?: string[] | null;
}
export interface TargetTag {
  title?: string | null;
  /**
   * 承担类型：0-无需承担 1-需要承担
   */
  type?: number | null;
  /**
   * 展示位置编号
   */
  code?: string | null;
  /**
   * 字体颜色 如BLACK
   */
  color?: string | null;
}
export interface FreeDepositDTO {
  /**
   * 押金状态 0-非免押订单 1-程信分 2-芝麻 3-供应商无条件免押，4-在线预授权一级页
   */
  depositStatus?: number | null;
  /**
   * 到店支付时展示可用押金类型：0 根据支付方式正常展示，1程信分免押，2芝麻，3在线预授权
   */
  showDepositType?: number | null;
  /**
   * 押金说明
   */
  depositExplain?: string | null;
  /**
   * 押金说明2
   */
  depositExplainV2?: DepositExplain[] | null;
  /**
   * 真实补足资金说明
   */
  realPayExplain?: string | null;
  /**
   * 真实补足资金类型，0 没有，1 未解冻/未完结 2，已解冻，完结
   */
  realPayType?: number | null;
  /**
   * 支付方式说明
   */
  payMethodExplain?: string | null;
  /**
   * 0不支持 10押金双免 20免租车押金 30免违章押金
   */
  freeDepositType?: number | null;
  /**
   * 免押金方式 1-程信分 2-芝麻免押 3-两种都支持 4-都不支持
   */
  freeDepositWay?: number | null;
  /**
   * 租车押金
   */
  preAmountForCar?: number | null;
  /**
   * 违章押金
   */
  preAmountForPeccancy?: number | null;
  /**
   * 待支付或抵扣的押金金额
   */
  preDepositAmount?: number | null;
  /**
   * 浮层-免押政策
   */
  depositItems?: DepositItem[] | null;
  /**
   * 浮层-真实补足金额说明
   */
  realPayItems?: DesList[] | null;
  /**
   * 押金扣款时间，在线预授权时需要
   */
  deductionTime?: string | null;
  /**
   * 是否超过取车前72小时
   */
  isBeforeNow?: boolean | null;
  /**
   * 超过后的文案
   */
  preAuthWarning?: string | null;
  /**
   * 去支付押金时的文案
   */
  tip?: DepositTip | null;
  /**
   * 无感实名文案
   */
  verifyTexts?: string[] | null;
  /**
   * 是否已实名 0-否 1-是
   */
  isVerifyUser?: number | null;
  /**
   * 浮层-一嗨免押政策
   */
  noteInfo?: NoteInfo | null;
  /**
   * 售后去免押是否是老芝麻 0-否 1-是
   */
  oldAlipayCredit?: number | null;
  /**
   * 押金预计解押时间描述
   */
  unfreezeTimeDesc?: string | null;
  /**
   * 双免10对应5，租车20-2，违章30-3
   */
  depositPayType?: number | null;
  /**
   * 免押文案
   */
  tips?: DepositTip[] | null;
  /**
   * 在线付押金
   */
  payOnlineInfo?: PayOnlineDTO | null;
  /**
   * 在线付押金金额
   */
  preAmountForPayOnline?: number | null;
  /**
   * 免押按钮与文案
   */
  freeDepositBtn?: FreeDepositButton | null;
  /**
   * 押金弹层是否展示冻结押金文案
   */
  showFreezeDeposit?: boolean | null;
  /**
   * 免押浮层名称
   */
  depositItemName?: string | null;
  /**
   * 免押/支付 进度
   */
  freeDepositProgress?: FreeDepositProgress | null;
  /**
   * 免押/支付状态及金额描述
   */
  freeDepositTitle?: string | null;
  /**
   * 是否展示免押标签
   */
  showFreeLabel?: boolean | null;
  /**
   * 可免押提示
   */
  tipsExplainDesc?: string | null;
  creditMap?: { [key: string]: string } | null;
  /**
   * 资金补足金额
   */
  complementaryAmount?: number | null;
  /**
   * 车辆是否免违章押金
   */
  freePreccancy?: boolean | null;
  /**
   * 押金浮层title
   */
  depositItemTitle?: DepositItemTitle | null;
}
export interface DepositItemTitle {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
}
export interface FreeDepositProgress {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 副标题
   */
  subTitle?: string | null;
  /**
   * 免押/支付进度
   */
  progressInfos?: FreeDepositProgressInfo[] | null;
}
export interface FreeDepositProgressInfo {
  /**
   * 主文案
   */
  mainText?: string | null;
  /**
   * 副文案
   */
  subText?: ProgressInfoSubDTO[] | null;
  links?: LinkDTO[] | null;
  /**
   * 1-支付、2-授权、3-车损扣费、4-转扣、5-补足、6-违章扣费、7-退还、8-解冻
   */
  type?: string | null;
  name?: string | null;
  /**
   * 级别 10-一级，20-二级
   */
  level?: string | null;
  /**
   * 颜色 0-置灰、1-橙色、2-绿色
   */
  color?: string | null;
}
export interface LinkDTO {
  url?: string | null;
  /**
   * 1-车损、2-违章、3-退款、4-补款
   */
  type?: string | null;
  desc?: string | null;
}
export interface ProgressInfoSubDTO {
  subDesc?: string | null;
  /**
   * 金额
   */
  feeAmount?: number | null;
  /**
   * 费用凭证
   */
  feeVoucher?: string[] | null;
  /**
   * 费用类型 1.ETC费用 2.续租 3.强行续租违约金 4.油/电费 5.加油/电服务费 6.停车费
   *  7.其他
   */
  feeType?: number | null;
  feeCode?: string | null;
}
export interface FreeDepositButton {
  title?: string | null;
  description?: string | null;
  /**
   * 状态 0 不可用  1可用
   */
  statusType?: number | null;
  actionUrl?: string | null;
  icon?: string | null;
  type?: number | null;
  auto?: boolean | null;
}
export interface PayOnlineDTO {
  payedTitle?: string | null;
  unPayTitle?: string | null;
  /**
   * 支付中弹层提示内容
   */
  payingDesc?: string | null;
}
export interface NoteInfo {
  title?: string | null;
  contents?: Contents[] | null;
  table?: TableDesc[] | null;
  items?: TableDesc[] | null;
}
export interface TableDesc {
  title?: Contents | null;
  desc?: Contents[] | null;
  note?: Contents | null;
}
export interface Contents {
  contentStyle?: string | null;
  stringObjs?: StringObjs[] | null;
}
export interface DepositTip {
  /**
   * 不能免押时的提示
   */
  warnTip?: string | null;
  /**
   * 免押时的文案list
   */
  texts?: Text[] | null;
  /**
   * 实名认证 3, 去验证 2, 确认免押 1
   */
  btnType?: number | null;
  /**
   * 去验证的文案
   */
  text2?: string | null;
  /**
   * 不能免押类型 1-一嗨不支持免押 2-已达最大笔数  3-F或其他情况，边界情况为不支持免押
   */
  warnType?: number | null;
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 副标题
   */
  subTitles?: string | null;
  /**
   * 按钮名称
   */
  btnName?: string | null;
  /**
   * 免押方式  0-F或其他情况不支持免押 1-程信分，2-芝麻，3-在线预授权，4-在线付押金
   */
  depositType?: number | null;
  /**
   * 售后去免押是否是老芝麻 0-否 1-是
   */
  oldAlipayCredit?: number | null;
  /**
   * 浮层-一嗨免押政策
   */
  noteInfo?: NoteInfo | null;
  /**
   * 实名认证文案
   */
  verifyTexts?: string[] | null;
  /**
   * 弹窗信息
   */
  popupInfo?: Popup | null;
  /**
   * 说明文案
   */
  explain?: Text[] | null;
  /**
   * 1-押金减免规则说明 2-补足资金退还说明
   */
  noteInfoType?: number | null;
  /**
   * 0-不可用，1-可用
   */
  status?: number | null;
  /**
   * 押金是否全部免收
   */
  completelyFree?: boolean | null;
}
export interface Popup {
  /**
   * 弹窗主文案
   */
  title?: string | null;
  /**
   * 弹窗副文案
   */
  subTitle?: Text | null;
  /**
   * 说明文案
   */
  explain?: Text[] | null;
}
export interface Text {
  title?: string | null;
  link?: Link | null;
}
export interface Link {
  text?: string | null;
  url?: string | null;
  /**
   * 1-押金减免规则说明 2-补足资金退还说明
   */
  linkType?: number | null;
  /**
   * 图标  help-问号
   */
  icon?: string | null;
  /**
   * 颜色 默认-灰色 blue-蓝色
   */
  color?: string | null;
}
export interface DesList {
  id?: number | null;
  type?: number | null;
  name?: string | null;
  description?: string | null;
  detailDesc?: string[] | null;
}
export interface DepositItem {
  /**
   * 押金列名 租车押金，违章押金
   */
  depositTitle?: string | null;
  /**
   * 金额
   */
  deposit?: number | null;
  /**
   * 状态  2 可退， 1免收
   */
  depositStatus?: number | null;
  /**
   * 说明
   */
  explain?: string | null;
}
export interface DepositExplain {
  /**
   * 押金说明
   */
  explain?: string | null;
  /**
   * 押金说明2 有坏账时才会用到
   */
  explainSupplement?: string | null;
  /**
   * 押金说明颜色 1-绿色 2-红色
   */
  color?: number | null;
  /**
   * 是否有坏账
   */
  badDebt?: boolean | null;
}
export interface CreditInfo {
  cashPledgeStatus?: number | null;
  deposit?: number | null;
  depositExplain?: string | null;
  requestid?: string | null;
  serviceAgreement?: string | null;
  wZDeposit?: number | null;
}
export interface FAQListInfo {
  faqLinkList?: string | null;
  faqList?: FAQInfoDto[] | null;
}
export interface FAQInfoDto {
  /**
   * 问题
   */
  questionContent?: string | null;
  /**
   * 回答
   */
  answerContent?: string | null;
  /**
   * livechath5/chat链接
   */
  url?: string | null;
  /**
   * 类似于：ctrip://wireless/hotel_netstar?
   */
  murl?: string | null;
  urlType?: string | null;
  /**
   * im+问题链接获取
   */
  imUrl?: string | null;
  questionId?: string | null;
  /**
   * OSD-
   */
  relationGuid?: string | null;
  /**
   * OSD：用于问题分类的一个东西
   */
  orderRule?: number | null;
}
export interface OrderAdditionalPayment {
  /**
   * 订单ID
   */
  orderId?: number | null;
  /**
   * 补款金额
   */
  amount?: number | null;
  /**
   * 补款原因
   */
  reason?: string | null;
  /**
   * 补款备注
   */
  remark?: string | null;
  /**
   * 支付状态 payStatus（支付状态：-1失败，0待支付，1成功）；payStatus=1时，pa
   * yTime有值，为支付时间
   */
  payStatus?: number | null;
  /**
   * 补款ID
   */
  additionalPaymentId?: number | null;
  /**
   * 支付时间
   */
  payTime?: number | null;
}
export interface PackageDetail {
  insPackageId?: number | null;
  /**
   * 保险项
   */
  insuranceItems?: InsuranceItem[] | null;
  /**
   * 自营险Id
   */
  ctripInsuranceIds?: number[] | null;
  /**
   * 保险描述
   */
  insuranceDesc?: string[] | null;
  /**
   * 精选组合
   */
  combinations?: AdditionalCombination[] | null;
  /**
   * 套餐项
   */
  productInfoList?: ProductDetailInfo[] | null;
  /**
   * 保险项
   */
  osdInsuranceItems?: OSDInsuranceItem[] | null;
}
export interface OSDInsuranceItem {
  code?: string | null;
  name?: string | null;
  shortDescription?: string | null;
  longDescription?: string | null;
  isInclude?: boolean | null;
  isFromCtrip?: boolean | null;
  insuranceTips?: SimpleObject[] | null;
  insuranceDetail?: PriceInsuranceDetail[] | null;
  /**
   * 自营险字段
   */
  converageExplain?: ExplainObject | null;
  /**
   * 自营险字段
   */
  unConverageExplain?: ExplainObject | null;
  /**
   * 自营险字段
   */
  claimProcess?: ExplainObject | null;
  insuranceGuaranteeUrl?: string | null;
}
export interface PriceInsuranceDetail {
  packageId?: number | null;
  currencyCode?: string | null;
  minExcess?: number | null;
  maxExcess?: number | null;
  excessShortDesc?: string | null;
  excessLongDesc?: string | null;
  minCoverage?: number | null;
  maxCoverage?: number | null;
  coverageShortDesc?: string | null;
  coverageLongDesc?: string | null;
}
export interface ProductDetailInfo {
  /**
   * 产品code
   */
  productCode?: string | null;
  /**
   * 套餐分组code
   */
  bomGroupCode?: string | null;
  /**
   * 价格明细列表
   */
  priceInfoList?: OSDPriceInfo[] | null;
  /**
   * 是否必须航班号
   */
  needFlightNo?: boolean | null;
  /**
   * 航班延误政策
   */
  flightDelayInfo?: FlightDelayInfo | null;
  /**
   * 是否必须信用卡信息
   */
  needCreditCardInfo?: boolean | null;
  /**
   * 是否支持多人驾驶
   */
  hasMutiDriver?: boolean | null;
  /**
   * 多人驾驶员数量
   */
  mutiDriverCount?: number | null;
  /**
   * 燃油信息
   */
  fuelInfo?: OSDFuelInfo | null;
  /**
   * 附加设备列表
   */
  equipments?: OSDEquipmentInfo[] | null;
  /**
   * 产品条款
   */
  termList?: OSDTermInfo[] | null;
  /**
   * 租车保障
   */
  rentalGuarantee?: CtripInsuranceInfoDTO[] | null;
  /**
   * 自营保险（国内）
   */
  ctripInsurances?: CtripInsuranceInfoDTO | null;
  /**
   * 套餐包含项
   */
  packageItems?: OSDPackageItem[] | null;
  /**
   * 是否为裸价套餐
   */
  naked?: boolean | null;
  /**
   * 芝麻免押信息
   */
  zhimaInfo?: ZhimaInfo | null;
  /**
   * 取车材料（身份证明，驾照政策，信用卡，电子提车凭证）
   */
  pickUpMaterials?: ExplainObject[] | null;
  /**
   * 租车必读（确认，取消，押金，里程，燃油，年龄，费用说明，限制出行区域）
   */
  carRentalMustRead?: ExplainObject[] | null;
  /**
   * 是否展示限行政策
   */
  isLimitTrans?: boolean | null;
  /**
   * 限行文案
   */
  limitTransTitle?: string | null;
  /**
   * 跨境跨岛政策（纯文案）
   */
  crossPolicy?: string | null;
  /**
   * 跨境跨岛政策（结构化）
   */
  crossIslandInfo?: CrossislandInfo | null;
  /**
   * 保险项
   */
  includeInsurances?: SimpleCommonObject[] | null;
  /**
   * 免费项目
   */
  freeItems?: SimpleCommonObject[] | null;
  /**
   * 特殊驾照政策
   */
  specialLicenceDesc?: string[] | null;
  /**
   * 供应商车型信息
   */
  vendorVehicleInfo?: VehicleInfo | null;
  /**
   * 驾照信息
   */
  driverLicenceList?: OSDDriverLicenceGroup[] | null;
  /**
   * 附加产品列表
   */
  attachedProducts?: OSDAttachedProduct[] | null;
  /**
   * 保险信息列表
   */
  insurances?: OSDInsuranceInfo[] | null;
  /**
   * 套餐包含项目，简单描述
   */
  minPackageItmes?: SimpleObject[] | null;
}
export interface OSDInsuranceInfo {
  code?: string | null;
  title?: string | null;
  desc?: string | null;
  longDesc?: string | null;
  coverageDesc?: string | null;
  unCoverageDesc?: string | null;
}
export interface OSDAttachedProduct {
  type?: string | null;
  title?: string | null;
  desc?: string | null;
}
export interface OSDDriverLicenceGroup {
  groupCode?: string | null;
  groupName?: string | null;
  items?: OSDDriverLicenceItem[] | null;
  sortNum?: number | null;
}
export interface OSDDriverLicenceItem {
  licenceType?: string | null;
  licenceName?: string | null;
  licenceDesc?: string | null;
  downloadUrl?: string | null;
  sortNum?: number | null;
}
export interface VehicleInfo {
  imageUrl?: string | null;
  vehicleName?: string | null;
  vehicleEName?: string | null;
  vehicleGroupName?: string | null;
  vehicleGroupEName?: string | null;
  vehicleGroupCode?: string | null;
  vehicleCode?: string | null;
  transmissionName?: string | null;
  transmissionType?: number | null;
  passengerDesc?: string | null;
  isSpecilized?: boolean | null;
  doorCountDesc?: string | null;
  isHot?: boolean | null;
  vendorVehicleCode?: string | null;
  passengerNum?: number | null;
  luggageNum?: number | null;
  luggageDesc?: string | null;
  doorNum?: number | null;
  vehComment?: string | null;
  spaceComment?: string | null;
  sippCode?: string | null;
  realityImageUrl?: string | null;
  simpleVehComment?: string | null;
  vehicleTip?: string | null;
  vehicleDescEn?: string | null;
  vehicleDescCn?: string | null;
  groupClassification?: string | null;
  abversion?: string | null;
  comment?: string | null;
  imageUrls?: string[] | null;
  isgranted?: boolean | null;
  grantedCode?: string | null;
  vdegree?: string | null;
}
export interface SimpleCommonObject {
  name?: string | null;
  code?: string | null;
  desc?: string | null;
}
export interface CrossislandInfo {
  /**
   * crossType(类型 0 跨岛 1跨洲 2跨国)
   */
  crossType?: number | null;
  crossislandLocationList?: CrossislandLocation[] | null;
  crossislandPolicyList?: CrossislandPolicy[] | null;
  /**
   * 已选择的跨岛信息 #号分割
   */
  selectedCrossIslands?: string | null;
  tips?: string | null;
}
export interface CrossislandPolicy {
  policyTitle?: string | null;
  policyType?: number | null;
  policyDescription?: string | null;
}
export interface CrossislandLocation {
  locationId?: string | null;
  locationName?: string | null;
  crossStatus?: number | null;
}
export interface ZhimaInfo {
  /**
   * 是否支持芝麻信用
   */
  isSupportZhima?: boolean | null;
  /**
   * 授权状态 （0：未验证 1：验证通过 2：已过期 3：验证未通过）
   */
  userAuthStatus?: UserAuthStatus | null;
  zhiMaScore?: number | null;
  desc?: string | null;
  supportZhiMa?: boolean | null;
}
export interface UserAuthStatus {
  /**
   * 实名
   */
  realName?: string | null;
  /**
   * 证件号
   */
  idNo?: string | null;
  /**
   * 证件类型
   */
  idType?: string | null;
  /**
   * 是否授权
   */
  authorize?: boolean | null;
  /**
   * 授权状态
   */
  authorizeStatus?: number | null;
  /**
   * 授权状态
   */
  authorizeStatusName?: string | null;
  /**
   * 是否过期
   */
  expired?: boolean | null;
  /**
   * 预授权订单号
   */
  orderId?: number | null;
  /**
   * 程信用是否满足
   */
  ctripSatisfy?: boolean | null;
  /**
   * 请求id
   */
  amount?: number | null;
  /**
   * 币种
   */
  currency?: string | null;
  /**
   * 请求id
   */
  requestid?: string | null;
  /**
   * 文案
   */
  texts?: TextInfoDto[] | null;
}
export interface TextInfoDto {
  title?: string | null;
  /**
   * 1是文案 2是icon
   */
  type?: string | null;
  style?: string | null;
}
export interface OSDPackageItem {
  code?: string | null;
  name?: string | null;
  desc?: string | null;
  sortNum?: number | null;
  /**
   * 套餐id
   */
  packageId?: number | null;
  type?: number | null;
  /**
   * 供应商套餐
   */
  vendorItemCode?: string | null;
  /**
   * 供应商套餐name
   */
  vendorItemName?: string | null;
  /**
   * 携程套餐code
   */
  ctripItemCode?: string | null;
  /**
   * 携程套餐描述
   */
  ctripItemDesc?: string | null;
  itemLongDesc?: string | null;
  /**
   * 承包范围
   */
  coverageDesc?: string | null;
  /**
   * 携程分组code
   */
  ctripGroupCode?: string | null;
  ctripGroupName?: string | null;
  /**
   * 携程分组标题
   */
  ctripGroupTitle?: string | null;
}
export interface CtripInsuranceInfoDTO {
  name?: string | null;
  vendorServiceCode?: string | null;
  localDailyPrice?: number | null;
  localTotalPrice?: number | null;
  localCurrencyCode?: string | null;
  currentDailyPrice?: number | null;
  currentTotalPrice?: number | null;
  currentCurrencyCode?: string | null;
  description?: string[] | null;
  allTags?: SimpleObject[] | null;
  longDescription?: string[] | null;
  /**
   * 0-已含，1-可购买，2-赠送
   */
  type?: number | null;
}
export interface OSDTermInfo {
  sortNum?: number | null;
  type?: number | null;
  title?: string | null;
  termItems?: OSDTermItem[] | null;
}
export interface OSDTermItem {
  sortNum?: number | null;
  title?: string | null;
  details?: OSDTermDetail[] | null;
}
export interface OSDTermDetail {
  sortNum?: number | null;
  payMode?: number | null;
  title?: string | null;
  description?: string | null;
}
export interface OSDEquipmentInfo {
  equipmentType?: number | null;
  maxCount?: number | null;
  equipmentCode?: string | null;
  equipmentName?: string | null;
  equipmentDesc?: string | null;
  ageFrom?: number | null;
  ageFromUnit?: string | null;
  ageTo?: number | null;
  ageToUnit?: string | null;
  localTotalPrice?: number | null;
  localDailyPrice?: number | null;
  localCurrencyCode?: string | null;
  currentCurrencyCode?: string | null;
  currentTotalPrice?: number | null;
  currentDailyPrice?: number | null;
  payMode?: number | null;
}
export interface OSDFuelInfo {
  /**
   * 是否送一箱油
   */
  isGiven?: boolean | null;
  /**
   * FRFB满油取还,FPO送一箱油,BATF买一箱油,FTENR买一箱油,SSF自助燃油政策
   */
  code?: string | null;
  name?: string | null;
  desc?: string | null;
}
export interface FlightDelayInfo {
  /**
   * 话术1 概述
   */
  outline?: string | null;
  /**
   * 话术2+3 保留政策+收费
   */
  keepHours?: string | null;
  /**
   * 话术4 取消政策
   */
  cancelRules?: string | null;
}
export interface OSDPriceInfo {
  /**
   * 当前币种车辆租金
   */
  currentCarPrice?: number | null;
  /**
   * 当前币种日均价
   */
  currentDailyPrice?: number | null;
  /**
   * 当前币种总价
   */
  currentTotalPrice?: number | null;
  /**
   * 当地币种车辆租金
   */
  localCarPrice?: number | null;
  /**
   * 当地币种日均价
   */
  localDailyPrice?: number | null;
  /**
   * 当地币种总价
   */
  localTotalPrice?: number | null;
  /**
   * 当地币种code
   */
  localCurrencyCode?: string | null;
  /**
   * 当前币种code
   */
  currentCurrencyCode?: string | null;
  /**
   * 当前币种异地还车费
   */
  currentOnewayfee?: number | null;
  /**
   * 当前币种到店支付费用
   */
  currentPoaPrice?: number | null;
  /**
   * 当前币种预付费用
   */
  currentPrepaidPrice?: number | null;
  /**
   * 当地币种异地还车费
   */
  localOnewayfee?: number | null;
  /**
   * 当地币种到店支付费用
   */
  localPoaPrice?: number | null;
  /**
   * 当地币种预付费用
   */
  localPrepaidPrice?: number | null;
  /**
   * 是否包含异地还车费
   */
  isContainOnewayFee?: boolean | null;
  /**
   * 支付方式
   */
  payMode?: number | null;
  /**
   * 产品套餐唯一标识
   */
  productId?: string | null;
  /**
   * 套餐id
   */
  packageId?: number | null;
  /**
   * 套餐类型 4-hertz预付
   */
  packageType?: number | null;
  /**
   * 信用卡描述
   */
  creditDescription?: string | null;
  /**
   * 扩展信息
   */
  vcExtendRequest?: OSDVcExtendRequest | null;
  /**
   * 汇率
   */
  exchangeRate?: number | null;
  /**
   * 里程限制
   */
  mileInfo?: OSDMileageInfo | null;
  /**
   * 确认信息
   */
  confirmInfo?: OSDConfirmInfo | null;
  /**
   * 取消规则
   */
  cancelRule?: OSDCancelRuleInfo | null;
  /**
   * 年龄限制
   */
  ageRestriction?: OSDAgeInfo | null;
  /**
   * 信用卡信息
   */
  creditCardInfo?: OSDCreditCardInfo | null;
  /**
   * 全量标签,type:1-正向，2-负向，3-营销
   */
  allTags?: SimpleObject[] | null;
  /**
   * 保险起赔额信息
   */
  insuranceDetails?: OSDInsuranceDetail[] | null;
  /**
   * 费用明细
   */
  chargeList?: OSDChargeDetailInfo[] | null;
  /**
   * 优惠券信息
   */
  promotionInfo?: SimplePromotionInfo | null;
  /**
   * 供应商优惠
   */
  vendorPromotionList?: SimplePromotionInfo[] | null;
}
export interface SimplePromotionInfo {
  /**
   * 1-立减，2-返现
   */
  type?: number | null;
  /**
   * 优惠券标题
   */
  title?: string | null;
  /**
   * 优惠券描述
   */
  description?: string | null;
  /**
   * 折扣百分比
   */
  deductionPercent?: number | null;
  /**
   * 优惠金额
   */
  deductionAmount?: number | null;
  /**
   * 优惠券号
   */
  code?: string | null;
  /**
   * 长标签
   */
  longTag?: string | null;
  /**
   * 长描述
   */
  longDesc?: string | null;
}
export interface OSDChargeDetailInfo {
  code?: string | null;
  name?: string | null;
  desc?: string | null;
  quantity?: number | null;
  payMode?: number | null;
  /**
   * 不含税价格
   */
  netAmount?: number | null;
  /**
   * 含税价格
   */
  dueAmount?: number | null;
  currency?: string | null;
  /**
   * 是否包含在总价里
   */
  isIncludedInRate?: boolean | null;
}
export interface OSDInsuranceDetail {
  /**
   * 保险code
   */
  code?: string | null;
  /**
   * 保险名称
   */
  name?: string | null;
  /**
   * 货币code
   */
  currencyCode?: string | null;
  /**
   * 最小起赔额
   */
  minExcess?: number | null;
  /**
   * 最大起赔额，minExcess=maxExcess 表示起赔额为固定值
   */
  maxExcess?: number | null;
  /**
   * 最小保额
   */
  minCoverage?: number | null;
  /**
   * 最大保额，minCoverage=maxCoverage 表示保额为固定值
   */
  maxCoverage?: number | null;
}
export interface OSDCreditCardInfo {
  description?: string | null;
  cardList?: OSDCreditCardItem[] | null;
  depositCurrencyCode?: string | null;
  maxDeposit?: number | null;
  minDeposit?: number | null;
}
export interface OSDCreditCardItem {
  name?: string | null;
  type?: string | null;
}
export interface OSDAgeInfo {
  description?: string | null;
  minDriverAge?: number | null;
  maxDriverAge?: number | null;
  youngDriverAge?: number | null;
  oldDriverAge?: number | null;
  youngDriverAgeDesc?: string | null;
  oldDriverAgeDesc?: string | null;
  licenceAge?: number | null;
  licenceAgeDesc?: string | null;
  youngDriverExtraFee?: OSDExtraFee | null;
  oldDriverExtraFee?: OSDExtraFee | null;
}
export interface OSDExtraFee {
  localCurrencyCode?: string | null;
  currentPrice?: number | null;
  localPrice?: number | null;
  /**
   * 0:day; 1:total.
   */
  feeType?: number | null;
}
export interface OSDCancelRuleInfo {
  /**
   * 是否全损
   */
  isTotalLoss?: boolean | null;
  /**
   * 是否免费取消
   */
  isFreeCancel?: boolean | null;
  /**
   * 当前是否免费取消
   */
  isFreeCancelNow?: boolean | null;
  /**
   * 免费取消时间
   */
  hours?: number | null;
  /**
   * 取消描述
   */
  cancelDescription?: string | null;
}
export interface OSDConfirmInfo {
  /**
   * 确认标题
   */
  confirmTitle?: string | null;
  /**
   * 确认描述
   */
  confirmDesc?: string | null;
  /**
   * 是否立即确认
   */
  confirmRightNow?: boolean | null;
  /**
   * 确认时间
   */
  confirmTime?: number | null;
}
export interface OSDMileageInfo {
  name?: string | null;
  /**
   * 是否限制里程
   */
  isLimited?: boolean | null;
  /**
   * 距离
   */
  distance?: number | null;
  /**
   * 距离单位
   */
  distanceUnit?: string | null;
  /**
   * 里程限制的时间单位,W：周，D：天，H：小时，P：租车期间
   */
  periodUnit?: string | null;
  /**
   * 收费金额
   */
  chargeAmount?: number | null;
  /**
   * 货币单位
   */
  chargeUnit?: string | null;
  /**
   * 数字
   */
  quantity?: number | null;
  /**
   * 里程描述(套餐包含项)
   */
  desc?: string | null;
  /**
   * 里程附加描述
   */
  mileAgeDesc?: string | null;
}
export interface OSDVcExtendRequest {
  responsePickUpLocationId?: string | null;
  responseReturnLocationId?: string | null;
  vendorVehicleId?: string | null;
}
export interface AdditionalCombination {
  /**
   * 套餐code
   */
  bomCode?: string | null;
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 包含项
   */
  codes?: string[] | null;
  /**
   * 描述
   */
  description?: string | null;
  /**
   * 货币
   */
  currency?: string | null;
  /**
   * 天价
   */
  dayPrice?: number | null;
  /**
   * 与最低价差价
   */
  gapPrice?: number | null;
  /**
   * 与上一层差价
   */
  stepPrice?: number | null;
  /**
   * 是否隐藏
   */
  hike?: boolean | null;
  /**
   * 总价
   */
  totalPrice?: number | null;
  /**
   * 最低价支付方式
   */
  payMode?: number | null;
  /**
   * 最低价支付方式
   */
  packageId?: number | null;
}
export interface InsuranceItem {
  productId?: number | null;
  title?: string | null;
  subTitle?: string | null;
  description?: string | null;
  longDescription?: string | null;
  unconveredInfo?: UnconveredInfo | null;
  code?: string | null;
  subDesc?: string | null;
  groupCode?: string | null;
  type?: number | null;
  name?: string | null;
  shortDescription?: string | null;
  isInclude?: boolean | null;
  isFromCtrip?: boolean | null;
  insuranceGuaranteeUrl?: string | null;
  itemUrl?: string | null;
  insuranceTips?: SimpleObject[] | null;
  insuranceDetail?: PriceInsuranceDetail[] | null;
  /**
   * 自营险字段
   */
  converageExplain?: ExplainObject | null;
  /**
   * 自营险字段
   */
  unConverageExplain?: ExplainObject | null;
  /**
   * 自营险字段
   */
  claimProcess?: ExplainObject | null;
}
export interface UnconveredInfo {
  title?: string | null;
  ctripItemCode?: string | null;
  itemList?: string[] | null;
}
export interface OSDPackageInfo {
  /**
   * 打包套餐ID
   */
  insPackageId?: number | null;
  /**
   * 是否是默认选中的套餐
   */
  isDefault?: boolean | null;
  /**
   * 套餐名称
   */
  packageName?: string | null;
  /**
   * 货币
   */
  currencyCode?: string | null;
  /**
   * 精选产品
   */
  defaultBomCode?: string | null;
  /**
   * 默认套餐
   */
  defaultPackageId?: number | null;
  /**
   * 保障程度
   */
  guaranteeDegree?: number | null;
  /**
   * 是否是裸价套餐
   */
  naked?: boolean | null;
  /**
   * 打包套餐包含保险名称
   */
  insuranceNames?: string[] | null;
  /**
   * 当前套餐最低价
   */
  lowestDailyPrice?: number | null;
  /**
   * 与最低价套餐差价
   */
  gapPrice?: number | null;
  /**
   * 与上一级套餐差价
   */
  stepPrice?: number | null;
  /**
   * 打包套餐提示
   */
  packageTips?: SimpleObject[] | null;
}
export interface BaseResponse {
  isSuccess?: boolean | null;
  code?: string | null;
  returnMsg?: string | null;
  requestId?: string | null;
  cost?: number | null;
}
export interface KeyAndValue {
  title?: string | null;
  description?: string | null;
  code?: string | null;
  type?: number | null;
  typeDesc?: string | null;
  sortNum?: number | null;
  extCode?: number | null;
}
export interface OrderRenewalInfo {
  renewalButton?: Button | null;
  renewalInfo?: KeyAndValue | null;
}
export interface ModifyOrderInfo {
  pickupcityId?: string | null;
  returncityId?: string | null;
  pickupStoreId?: string | null;
  returnStoreId?: string | null;
  vendorId?: string | null;
  vehicleId?: string | null;
  isgranted?: boolean | null;
  grantedCode?: string | null;
  ctripVehicleId?: string | null;
  pickupStoreServiceType?: string | null;
  returnStoreServiceType?: string | null;
  rateCategory?: string | null;
  rateCode?: string | null;
  payMode?: number | null;
  vdegree?: string | null;
  priceType?: number | null;
}
export interface RentCenterDto {
  /**
   * 是否租车中心
   */
  rentCenter?: boolean | null;
  /**
   * 租车中心id
   */
  id?: number | null;
  /**
   * 租车中心名字
   */
  name?: string | null;
  /**
   * 背景图片
   */
  bacimage?: string | null;
  /**
   * 租车中心图片
   */
  images?: string[] | null;
  isNew?: string | null;
  address?: string | null;
}
export interface InvoiceDto {
  /**
   * 配送信息
   */
  deliveryInfo?: DeliveryInfo | null;
  /**
   * 补开发票
   */
  makeUpInvoice?: MakeUpInvoiceDto | null;
  /**
   * 发票描述信息
   */
  invoiceDesc?: ItemInfo | null;
}
export interface MakeUpInvoiceDto {
  /**
   * 电子发票2，其他0
   */
  invoiceType?: number | null;
  amount?: number | null;
  /**
   * 是否需要6大要素 1可用 0 不可用
   */
  needSix?: number | null;
}
export interface DeliveryInfo {
  /**
   * 收件地址
   */
  deliveryAddress?: string | null;
  /**
   * 配送城市
   */
  deliveryCityName?: string | null;
  /**
   * 配送联系电话
   */
  deliveryContactTel?: string | null;
  /**
   * 配送区域
   */
  deliveryDistrictName?: string | null;
  /**
   * 配送物品类型 1-发票 2-GPS
   */
  deliveryGoodType?: number | null;
  /**
   * 收件人邮编
   */
  deliveryPostCode?: string | null;
  /**
   * 省份
   */
  deliveryProvinceName?: string | null;
  /**
   * 收件人姓名
   */
  deliveryReceiverName?: string | null;
  /**
   * 配送方式(1-送票 2-EMS 3-取票 4-邮寄 5-隔日快递)
   */
  deliveryType?: number | null;
}
export interface OnlinePreAuthDTO {
  /**
   * 1-租车预授权 2-违章预授权
   */
  preAuthType?: number | null;
  /**
   * 1-全额退款 2-部分退款 3-全额扣款
   */
  resultType?: number | null;
  /**
   * 部分退款时该字段有值（对于租车预授权和违章预授权 产品给出相应的原因类型）
   */
  lossType?: number | null;
  /**
   * 扣除金额
   */
  lossAmount?: number | null;
  /**
   * 扣除金额原因描述
   */
  lossReason?: string | null;
  /**
   * 凭证图片地址url
   */
  proofImg?: string | null;
  /**
   * 备注
   */
  remark?: string | null;
  /**
   * BillNO
   */
  billNo?: number | null;
  /**
   * 处理状态
   */
  processStatus?: number | null;
}
export interface AvailableInsuranceDescInfo {
  /**
   * 是否有可用保险
   */
  hasAvailableInsurance?: boolean | null;
  /**
   * 展示标题文案
   */
  title?: string | null;
  /**
   * 展示内容文案
   */
  content?: string | null;
  /**
   * 展示入口文案
   */
  entryContent?: string | null;
  /**
   * 入口url
   */
  url?: string | null;
}
export interface CtripInsuranceInfo {
  insuranceId?: number | null;
  clientName?: string | null;
  givenName?: string | null;
  surname?: string | null;
  /**
   * 保险产品Id
   */
  productId?: number | null;
  /**
   * 保险产品名称
   */
  productName?: string | null;
  /**
   * 状态
   */
  statusDesc?: string | null;
  /**
   * 入口
   */
  insuranceApplyEntry?: string | null;
  /**
   * 入口url
   */
  url?: string | null;
  /**
   * 保单Url
   */
  insuranceUrl?: string | null;
  /**
   * 保险描述
   */
  description?: string | null;
  /**
   * 被保人姓名
   */
  insured?: string | null;
  /**
   * 多保险被保人
   */
  subCtripInsuranceInfoList?: SubCtripInsuranceInfo[] | null;
  /**
   * 保险价格
   */
  insuranceAmount?: number | null;
  /**
   * 理赔信息
   */
  claimInfo?: ClaimInfo | null;
  /**
   * 保险订单ID
   */
  insuranceOrderId?: string | null;
  /**
   * 从何处购买的，1 售前，2 售后
   */
  boughtFrom?: number | null;
}
export interface ClaimInfo {
  /**
   * 保险产品Id
   */
  productId?: number | null;
  /**
   * 保险理赔按钮
   */
  claimBtton?: Button | null;
  /**
   * 理赔状态描述
   */
  claimStatusDesc?: string | null;
  /**
   * 理赔状态 1 审核中 2材料未通过 3理赔拒绝 4理赔完成已打款
   */
  claimStatusType?: number | null;
  /**
   * insuracneCode
   */
  insuracneCode?: string | null;
}
export interface SubCtripInsuranceInfo {
  /**
   * 保险产品Id
   */
  productId?: number | null;
  /**
   * 保险产品名称
   */
  productName?: string | null;
  /**
   * 状态
   */
  statusDesc?: string | null;
  /**
   * 入口
   */
  insuranceApplyEntry?: string | null;
  /**
   * 入口url
   */
  url?: string | null;
  /**
   * 保单Url
   */
  insuranceUrl?: string | null;
  /**
   * 保险描述
   */
  description?: string | null;
  /**
   * 被保人姓名
   */
  insured?: string | null;
}
export interface IsdVendorInsurance {
  insurancedesc?: string | null;
  insurancelist?: IsdVendorInsuranceDtos[] | null;
}
export interface IsdVendorInsuranceDtos {
  title?: string | null;
  type?: number | null;
  desclist?: IsdInsuranceDtos[] | null;
}
export interface IsdInsuranceDtos {
  title?: string | null;
  desclist?: string[] | null;
}
export interface FeeDetailInfoV2 {
  /**
   * 费用明细结构化
   */
  chargesInfos?: ChargeItemDetail[] | null;
  /**
   * 儿童座椅及其他设备费用
   */
  equipmentInfos?: ChargeItemDetail[] | null;
  /**
   * 修改订单费
   */
  modifyInfo?: ChargeItemDetail | null;
  /**
   * 活动&优惠集合
   */
  discountList?: ChargeItemDetail[] | null;
  /**
   * 活动-Deprecated
   */
  activityInfo?: ChargeItemDetail | null;
  /**
   * 优惠-Deprecated
   */
  couponInfos?: ChargeItemDetail[] | null;
  /**
   * 返现
   */
  cashBackInfo?: CashBackInfo | null;
  /**
   * title: 还车后可获, subTitle:平台补贴返现,description:还车完成后X个工
   * 作日内自动返至「我的钱包」        notices[0],订详页描述如：还车后返现¥20，ty
   * pe 0 未返现，1未失败，2返现成功        LabelDetail: 弹出的框，code
   * 1时，出联系客服
   */
  cashBackInfoV2?: ChargeItemDetail | null;
  /**
   * 押金
   */
  depositInfo?: CashBackInfo | null;
  /**
   * 不包含总价内费用明细结构化-Deprecated
   */
  notIncludeCharges?: ChargeItemDetail | null;
  /**
   * 价格汇总
   */
  chargesSummary?: ChargeItemDetail | null;
  /**
   * 租车总费用的组成
   */
  feeList?: ChargeItemDetail[] | null;
  /**
   * 位于首页的介绍
   */
  chargesBrief?: ChargeItemDetail | null;
  /**
   * 租期
   */
  rentalTerm?: number | null;
  /**
   * 用户积分
   */
  userPoints?: PointsItemDetail | null;
}
export interface PointsItemDetail {
  title?: string | null;
  subTitle?: string | null;
  description?: string | null;
  currencyCode?: string | null;
  currencyPrice?: number | null;
  /**
   * 积分倍率提示
   */
  pointsTip?: string | null;
  /**
   * 说明内容
   */
  pointsNotice?: TableDesc[] | null;
}
export interface CashBackInfo {
  currencyCode?: string | null;
  currentDailyPrice?: number | null;
  currentTotalPrice?: number | null;
  localCurrencyCode?: string | null;
  localDailyPrice?: number | null;
  localTotalPrice?: number | null;
  items?: ChargeItemDetail[] | null;
}
export interface IsdFeeInfo {
  /**
   * 订单卖价，默认0
   */
  salesAmount?: number | null;
  /**
   * 实收客人金额，默认0
   */
  actualAmount?: number | null;
  noPayAmount?: number | null;
  /**
   * 应收客人金额，默认0
   */
  firstPayAmount?: number | null;
  totalAmount?: number | null;
  orderAmount?: number | null;
  extraAmount?: number | null;
  /**
   * 立减
   */
  deductAmount?: number | null;
  /**
   * 后返
   */
  rebackAmount?: number | null;
  precar?: number | null;
  prepeccancy?: number | null;
  /**
   * 1 普通价格 2打包 3提前预付
   */
  priceType?: number | null;
  rateCode?: string | null;
  rateCategory?: string | null;
  isWholeday?: number | null;
  feeList?: IsdFeeDto[] | null;
  preAuthDesc?: string | null;
  /**
   * 用车预授权+应收客人金额
   */
  preAuthAmount?: number | null;
  preAuthDisplay?: number | null;
  /**
   * 打包租车费
   */
  totalRentalPrice?: number | null;
  /**
   * 总费用
   */
  rentCardTotalFee?: number | null;
  /**
   * 零散小时租期
   */
  exceedTenancy?: number | null;
  /**
   * 零散小时费用
   */
  exceedPrice?: number | null;
  /**
   * 日租金
   */
  dailyPrice?: number | null;
}
export interface IsdFeeDto {
  priceCode?: string | null;
  priceName?: string | null;
  shortDesc?: string | null;
  priceDesc?: string | null;
  amount?: number | null;
  quantity?: number | null;
  descdetail?: DescDetail[] | null;
}
export interface DescDetail {
  title?: string | null;
  desc?: string | null;
}
export interface OrderRefundProgress {
  /**
   * 对内响应流水号
   */
  billNo?: number | null;
  billItemNo?: number | null;
  currency?: string | null;
  /**
   * 退款金额
   */
  refundAmount?: number | null;
  /**
   * 退款状态
   */
  billStatus?: string | null;
  /**
   * 退款原因
   */
  remark?: string | null;
  /**
   * 处理状态 退款申请处理状态：0:处理中，1:处理成功
   */
  dealStatus?: number | null;
  /**
   * 退款申请成功的时间
   */
  createTime?: string | null;
  /**
   * 银行处理退款申请的时间 DealStatus为1时有效
   */
  dealTime?: string | null;
  /**
   * 退款周期 预计到用户帐上的时间 DealStatus为1时有效
   */
  refundCycle?: string | null;
  /**
   * refundCycle对应的key值
   */
  refundPeriodKey?: string | null;
  /**
   * 退款渠道
   */
  paymentWayID?: string | null;
  /**
   * 退款渠道文案
   */
  paymentWayName?: string | null;
  /**
   * 结构化的进度
   */
  progress?: SimpleObject[] | null;
  /**
   * 积分
   */
  points?: number | null;
  /**
   * 退款类型，1提前还车，0嚯null是其他
   */
  refundType?: number | null;
}
export interface AgeInfo {
  description?: string | null;
  minDriverAge?: number | null;
  maxDriverAge?: number | null;
  youngDriverAge?: number | null;
  oldDriverAge?: number | null;
  youngDriverAgeDesc?: string | null;
  oldDriverAgeDesc?: string | null;
  licenceAge?: number | null;
  licenceAgeDesc?: string | null;
}
export interface MultiModel {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 标题下有多段内容聚合
   */
  info?: ModelWithPattern[] | null;
}
export interface ModelWithPattern {
  /**
   * 描述
   */
  description?: string | null;
  /**
   * 是否高亮
   */
  highLight?: boolean | null;
}
export interface CancelRuleInfo {
  /**
   * 是否全损
   */
  isTotalLoss?: boolean | null;
  /**
   * 是否免费取消
   */
  isFreeCancel?: boolean | null;
  /**
   * 免费取消时间
   */
  hours?: number | null;
  /**
   * 取消描述
   */
  cancelDescription?: string | null;
  /**
   * 取消相关的描述，与当前订单状态相关
   */
  cancelTip?: string | null;
  title?: string | null;
  longTitle?: string | null;
  ruleList?: string[] | null;
  freeCancelDesc?: string | null;
  /**
   * 取消损失描述
   */
  cancelLossDesc?: string | null;
  /**
   * 取消损失费
   */
  cancelLossAmount?: number | null;
  /**
   * 取消损失费
   */
  isLossFree?: boolean | null;
  cancelReasons?: string[] | null;
  cancelRules?: CancelRuleInfoDTO[] | null;
  /**
   * 取消相关的描述的颜色，1 绿色
   */
  cancelTipColor?: number | null;
  /**
   * 取消政策的底部描述-感叹号部分
   */
  bottomDesc?: string | null;
  /**
   * 取消原因v2
   */
  cancelReasonsV2?: CancelReasonDTO[] | null;
  /**
   * 取消重订title
   */
  reOrderTitle?: string | null;
  /**
   * 取消重订解释
   */
  reOrderExplain?: string | null;
  /**
   * 修改订单的说明
   */
  modifyTip?: TitleAndDesc | null;
}
export interface CancelReasonDTO {
  code?: number | null;
  reason?: string | null;
  /**
   * 展示顺序
   */
  sort?: number | null;
  tip?: CancelTip | null;
  cancelReasonList?: CancelReasonDTO[] | null;
}
export interface CancelTip {
  title?: string | null;
  desc?: string[] | null;
  button?: string | null;
  titleSuppl?: TitleSupplDTO | null;
  code?: number | null;
}
export interface TitleSupplDTO {
  text?: string | null;
  /**
   * green-绿色 ； orange-橙色
   */
  color?: string | null;
}
export interface CancelRuleInfoDTO {
  /**
   * 是否免费取消
   */
  freeStatus?: number | null;
  /**
   * 废弃
   */
  free?: number | null;
  /**
   * 取消规则标题
   */
  title?: string | null;
  /**
   * 取消规则内容
   */
  context?: string | null;
  /**
   * 取消时间
   */
  time?: string | null;
  /**
   * 当前时间是否适用
   */
  hit?: boolean | null;
  /**
   * 取消时间描述
   */
  timeDesc?: string | null;
  /**
   * 违约金金额
   */
  lossFee?: string | null;
  startTime?: string | null;
}
export interface InsuranceDetailDTO {
  /**
   * 保险名称
   */
  name?: string | null;
  /**
   * 保险code
   */
  code?: string | null;
  /**
   * 保险标题，
   */
  title?: string | null;
  /**
   * 承保范围描述
   */
  coverageDesc?: string | null;
  /**
   * 不承保范围描述
   */
  unCoverageDesc?: string | null;
  /**
   * 某些保险名称有特效的，0 无特效，1无忧租
   */
  specificName?: number | null;
  /**
   * 来源：1 自营，2 车行提供（pms）
   */
  insFrom?: number | null;
  /**
   * 产品id
   */
  productId?: number | null;
  /**
   * 价格
   */
  prcie?: number | null;
  /**
   * 0未下单，1 待确认，2支付中，3已支付，4 不可加购
   */
  status?: number | null;
  /**
   * 是否是可升级的，true为升级项
   */
  canUpgrade?: boolean | null;
}
export interface IbuInsuranceDetailDTO {
  /**
   * 保险code
   */
  code?: string | null;
  /**
   * 保险名称
   */
  name?: string | null;
  /**
   * 货币code
   */
  currencyCode?: string | null;
  /**
   * 最小起赔额
   */
  minExcess?: number | null;
  /**
   * 最大起赔额，minExcess=maxExcess 表示起赔额为固定值
   */
  maxExcess?: number | null;
  /**
   * 最小保额
   */
  minCoverage?: number | null;
  /**
   * 最大保额，minCoverage=maxCoverage 表示保额为固定值
   */
  maxCoverage?: number | null;
  longDesc?: string | null;
  coverageDesc?: string | null;
  unCoverageDesc?: string | null;
  /**
   * ISD部分
   */
  productId?: number | null;
  shortDesc?: string | null;
  quantity?: number | null;
  status?: number | null;
  ordertitle?: string | null;
  requestid?: string | null;
  dlabel?: string[] | null;
  tlabel?: string[] | null;
  exttip?: string | null;
  descDetail?: DescDetail[] | null;
}
export interface SimpleModel {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 描述
   */
  description?: string | null;
  /**
   * 是否高亮
   */
  highLight?: boolean | null;
}
export interface CreditCardInfo {
  cards?: string[] | null;
  description?: string | null;
  depositCurrencyCode?: string | null;
  maxDeposit?: number | null;
  minDeposit?: number | null;
  payWaysNames?: string[] | null;
  whitePayWays?: string | null;
}
export interface ExtraInfoDTO {
  /**
   * 附加产品名称
   */
  name?: string | null;
  /**
   * 数量
   */
  count?: number | null;
  /**
   * 单价
   */
  unitPrice?: number | null;
  /**
   * 货币
   */
  currencyCode?: string | null;
  /**
   * 单位
   */
  unit?: string | null;
  description?: string | null;
}
export interface GroupPackageItems {
  /**
   * 标题
   */
  name?: string | null;
  /**
   * 1 燃油政策 2 里程限制 3 额外驾驶员 4保险 5税费 6其他
   */
  type?: number | null;
  /**
   * 套餐包含
   */
  items?: string[] | null;
  /**
   * 描述，若type=5为税费描述
   */
  descriptions?: string[] | null;
}
export interface DriverInfoDTO {
  /**
   * 姓名
   */
  name?: string | null;
  /**
   * 年龄
   */
  age?: string | null;
  /**
   * 邮箱
   */
  email?: string | null;
  /**
   * 联系方式
   */
  telphone?: string | null;
  /**
   * 区号
   */
  areaCode?: string | null;
  /**
   * 航班号、车次
   */
  flightNo?: string | null;
  /**
   * 证件类型
   */
  iDCardType?: number | null;
  /**
   * 身份证号
   */
  iDCardNo?: string | null;
  /**
   * 加密的身份证号
   */
  encrypIDCardNo?: string | null;
  /**
   * 加密的身份证号
   */
  distributionMobile?: string | null;
  distributionEmail?: string | null;
  /**
   * 身份证号明文
   */
  decryptIDCardNo?: string | null;
  /**
   * 联系方式明文
   */
  decryptTelphone?: string | null;
  /**
   * 邮箱明文
   */
  decryptMail?: string | null;
  lastName?: string | null;
  firstName?: string | null;
}
export interface LocationInfoDTO {
  /**
   * 当地时间
   */
  localDateTime?: string | null;
  /**
   * 门店名称
   */
  storeName?: string | null;
  /**
   * 门店Code
   */
  storeCode?: string | null;
  /**
   * 门店地址
   */
  storeAddress?: string | null;
  /**
   * 经度
   */
  longitude?: number | null;
  /**
   * 维度
   */
  latitude?: number | null;
  /**
   * 门店向导
   */
  storeGuide?: string | null;
  /**
   * 门店位置
   */
  storeLocation?: string | null;
  /**
   * 到达方式
   */
  storeWay?: string | null;
  /**
   * 门店电话
   */
  storeTel?: string | null;
  /**
   * 营业时间描述
   */
  storeOpenTimeDesc?: string | null;
  /**
   * 非营业时间服务说明
   */
  outOfHourDescription?: string | null;
  /**
   * 城市
   */
  cityName?: string | null;
  /**
   * 省
   */
  provinceName?: string | null;
  /**
   * 国家
   */
  countryName?: string | null;
  /**
   * 用户搜索地址
   */
  userSearchLocation?: string | null;
  /**
   * 门店图片地址
   */
  mapUrl?: string | null;
  fromTime?: string | null;
  toTime?: string | null;
  cityId?: number | null;
  storeSerivceName?: string | null;
  userAddress?: string | null;
  userLongitude?: number | null;
  userLatitude?: number | null;
  /**
   * 0取还车到店;1还车后店员免费送您到以下地址 2店员将车送到以下地址
   */
  serviceType?: string | null;
  /**
   * 取还车方式描述
   */
  serviceDetails?: string[] | null;
  addrTypeName?: string | null;
  /**
   * 门店ID
   */
  storeID?: number | null;
  commentCount?: number | null;
  pickUpOffLevel?: number | null;
  sendTypeForPickUpOffCar?: number | null;
  /**
   * 地点名
   */
  location?: Location | null;
  /**
   * 4的时候是接送点
   */
  storeType?: number | null;
  /**
   * 免责声明
   */
  disclaimer?: string | null;
  /**
   * 展示的地址
   */
  showLocation?: ShowLocation | null;
  /**
   * 接送点地址
   */
  pointLocation?: PointLocation | null;
}
export interface PointLocation {
  name?: string | null;
  address?: string | null;
  storeOpenTimeDesc?: string | null;
}
export interface ShowLocation {
  serviceTypeDesc?: string | null;
  addressTitle?: string | null;
  realAddress?: string | null;
  guideImages?: string[] | null;
  longitude?: string | null;
  latitude?: string | null;
  oldAddressTitle?: string | null;
}
export interface Location {
  locationType?: number | null;
  locationName?: string | null;
  locationNameEn?: string | null;
  locationCode?: string | null;
  continent?: Continent | null;
  country?: Country | null;
  province?: Province | null;
  city?: City | null;
  poiInfo?: PoiInfo | null;
}
export interface PoiInfo {
  /**
   * 纬度
   */
  latitude?: number | null;
  /**
   * 经度
   */
  longitude?: number | null;
  /**
   * 地图类型 高德 百度 etc.
   */
  type?: number | null;
}
export interface City {
  id?: number | null;
  name?: string | null;
  timeZone?: number | null;
  enName?: string | null;
}
export interface Province {
  id?: number | null;
  name?: string | null;
  enName?: string | null;
}
export interface Country {
  id?: number | null;
  name?: string | null;
  enName?: string | null;
}
export interface Continent {
  id?: number | null;
  name?: string | null;
  enName?: string | null;
}
export interface MileInfo {
  /**
   * 是否不限里程
   */
  isUnLimited?: boolean | null;
  /**
   * 限制的里程距离
   */
  limitedDistance?: number | null;
  /**
   * 里程距离的单位（如km）
   */
  limitedDistanceUnit?: string | null;
  /**
   * 里程限制的时间单位：        W：周        D：天        H：小时
   *   P：租车期间
   */
  limitedPeriodUnit?: string | null;
  /**
   * 超出里程单价
   */
  overUnitAmount?: number | null;
  /**
   * 超出里程数量
   */
  overQuantity?: number | null;
  /**
   * 超出里程单位
   */
  overDistanceUnit?: string | null;
  /**
   * 里程限制的时间单位：        W：周        D：天        H：小时
   *   P：租车期间
   */
  overPeriodUnit?: string | null;
  /**
   * 里程限制描述
   */
  mileAgeDesc?: string | null;
}
export interface IbuFuelInfo {
  /**
   * 是否送一箱油
   */
  isGiven?: boolean | null;
  code?: string | null;
  name?: string | null;
  desc?: string | null;
}
export interface VendorInfoDTO {
  /**
   * 供应商名称
   */
  vendorName?: string | null;
  /**
   * 供应商图片Url
   */
  vendorImageUrl?: string | null;
  /**
   * 立即确认
   */
  confirmRightNow?: boolean | null;
  /**
   * 确认时间
   */
  confirmDate?: number | null;
  /**
   * 确认时间字符串
   */
  confirmDateStr?: string | null;
  platformCode?: string | null;
  platformName?: string | null;
  vendorID?: number | null;
  vendorConfirmCode?: string | null;
  isSelf?: boolean | null;
  selfName?: string | null;
  vendorMobileImageUrl?: string | null;
  bookingNotice?: DesList[] | null;
  bizVendorCode?: string | null;
  subType?: number | null;
  /**
   * 评分
   */
  commentInfo?: CommentInfoDTO | null;
  /**
   * 是否是broker供应商
   */
  broker?: boolean | null;
}
export interface CommentInfoDTO {
  /**
   * 优选类型 1：携程优选
   */
  vendorGoodType?: number | null;
  /**
   * 披露的评分
   */
  exposedScore?: number | null;
  /**
   * 分数的最大值
   */
  topScore?: number | null;
  /**
   * 等级，返回示例： 很好
   */
  level?: string | null;
  /**
   * 标签，返回示例：取还方便
   */
  commentLabel?: string | null;
  /**
   * 评论条数
   */
  commentCount?: number | null;
  /**
   * 是否有评分
   */
  hasComment?: number | null;
  link?: string | null;
}
export interface VehicleInfoDTO {
  /**
   * 车型名称（ISD:携程车型名）
   */
  vehicleName?: string | null;
  /**
   * 是否是指定车型
   */
  special?: boolean | null;
  /**
   * 乘客数量
   */
  passengerNum?: number | null;
  /**
   * 大件行李数
   */
  luggageNum?: number | null;
  /**
   * 是否有空调
   */
  hasAC?: boolean | null;
  /**
   * 排挡（自动/手动）
   */
  transmission?: string | null;
  /**
   * 车型组名称
   */
  vehicleGroupName?: string | null;
  /**
   * 供应商车型Code
   */
  vendorVehicleCode?: string | null;
  /**
   * 车型图片
   */
  imageUrl?: string | null;
  /**
   * 相似车型图片集合
   */
  similarImageUrls?: string[] | null;
  /**
   * 海外相似车型合集
   */
  osdSimilarVehilceList?: SimilarVehicleInfo[] | null;
  /**
   * ISD:是否无忧租
   */
  granted?: boolean | null;
  grantCode?: string | null;
  /**
   * 排量
   */
  vehicleDisplacement?: string | null;
  /**
   * ISD:供应商车型编号
   */
  vendorVehicleID?: number | null;
  /**
   * ISD:携程车型ID
   */
  ctripVehicleID?: string | null;
  /**
   * ISD:车型等级
   */
  vehicleDegree?: string | null;
  /**
   * ISD:排量
   */
  displacement?: string | null;
  labels?: Label[] | null;
  isgranted?: boolean | null;
  grantedCode?: string | null;
  vdegree?: string | null;
  /**
   * 牌照
   */
  license?: string | null;
  /**
   * 牌照底色
   */
  licenseStyle?: string | null;
  /**
   * 车牌限行说明
   */
  licenseLimitDesc?: string | null;
  /**
   * 车门数量
   */
  doorNum?: number | null;
  /**
   * 年款
   */
  style?: string | null;
  /**
   * 10汽油 20柴油 30混动 40纯电 50其他
   */
  oilType?: number | null;
  /**
   * 变速箱:自动变速箱(AT)
   */
  gearbox?: string | null;
  /**
   * 驾驶模式:前置四驱
   */
  driveMode?: string | null;
  /**
   * 结构:三厢
   */
  struct?: string | null;
  /**
   * 汽油:汽油92号
   */
  fuel?: string | null;
  /**
   * 发动机类型名称
   */
  transmissionName?: string | null;
  /**
   * 车型内部图片集合
   */
  vehicleAccessoryImages?: string[] | null;
  /**
   * 汽油：汽油、汽油+24V轻混系统、汽油+48V轻混系统、汽油+90V轻混系统、 柴油：柴油、柴油+4
   * 8V轻混系统 混动：插电式混合动力、增程式、油电混合、汽油+天然气、汽油+CNG、汽油电驱 电动：纯
   * 电动 其他：氢燃料、氢燃料电池
   */
  fuelType?: string | null;
  /**
   * 巡航系统
   */
  guidSys?: string | null;
  /**
   * 手机互联
   */
  carPlay?: string | null;
  /**
   * 充电口
   */
  chargeInterface?: string | null;
  /**
   * 天窗
   */
  skylight?: string | null;
  /**
   * 续航{min-max}km
   */
  endurance?: string | null;
  /**
   * 快充{quick}小时，慢充{slow}小时
   */
  charge?: string | null;
  /**
   * 自动驻车
   */
  autoPark?: boolean | null;
  /**
   * 是否支持蓝牙
   */
  carPhone?: boolean | null;
  /**
   * 是否支持无钥匙启动
   */
  autoStart?: boolean | null;
  /**
   * 自动泊车
   */
  autoBackUp?: boolean | null;
  /**
   * 后备箱容积
   */
  luggageNumDesc?: string | null;
}
export interface Label {
  name?: string | null;
  description?: string | null;
}
export interface SimilarVehicleInfo {
  vehicleCode?: string | null;
  vehicleName?: string | null;
  vehicleImageUrl?: string | null;
}
export interface OrderDetailPriceDTO {
  /**
   * 套餐类型 4-hertz预付
   */
  packageType?: number | null;
  /**
   * 订单总价(客人支付货币)
   */
  currentTotalPrice?: number | null;
  /**
   * 币种（客人支付货币）
   */
  currentCurrencyCode?: string | null;
  /**
   * 订单总价（现付货币）
   */
  localTotalPrice?: number | null;
  /**
   * 币种（现付货币）
   */
  localCurrencyCode?: string | null;
  /**
   * 支付方式
   */
  payMode?: number | null;
  /**
   * 支付方式描述
   */
  payModeDesc?: string | null;
  /**
   * 预付金额
   */
  prepayPrice?: PriceInfo | null;
  /**
   * 门店付金额
   */
  localPrice?: PriceInfo | null;
  /**
   * 预付价格明细（准备废弃）
   */
  prepayPriceDetails?: PriceInfo[] | null;
  /**
   * 门店付价格明细（准备废弃）
   */
  localPriceDetails?: PriceInfo[] | null;
  /**
   * 价格明细
   */
  priceDetails?: PriceInfo[] | null;
  /**
   * 实际支付金额
   */
  payAmount?: number | null;
  /**
   * 优惠券金额
   */
  couponAmount?: number | null;
  /**
   * 价格说明 （目前仅hertz预付使用）
   */
  priceDescription?: string | null;
  /**
   * 优惠券列表
   */
  coupons?: OrderCouponsDTO[] | null;
}
export interface OrderCouponsDTO {
  /**
   * 优惠券代码
   */
  couponCode?: string | null;
  /**
   * 优惠券活动（策略）ID
   */
  promotionId?: number | null;
  /**
   * 是否需要返现
   */
  isNeedDebate?: boolean | null;
  /**
   * 优惠券金额
   */
  deductionAmount?: number | null;
  /**
   * 优惠券说明
   */
  remark?: string | null;
  /**
   * 显示名称
   */
  displayName?: string | null;
}
export interface PriceInfo {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 总价
   */
  totalPrice?: number | null;
  /**
   * 预付金额
   */
  prepaidPrice?: number | null;
  /**
   * 到店支付
   */
  poaPrice?: number | null;
  /**
   * 币种
   */
  currencyCode?: string | null;
  /**
   * 日均价
   */
  dayPrice?: number | null;
  /**
   * 异地还车费
   */
  oneWayFee?: number | null;
  /**
   * 总价是否包含异地还车费
   */
  oneWayFeeInclusive?: boolean | null;
  /**
   * 支付立减金额
   */
  reducePayAmount?: number | null;
}
export interface ContinuePayInfoDTO {
  /**
   * 需要继续支付
   */
  needContinuePay?: boolean | null;
  /**
   * 剩余分钟数
   */
  leftMinutes?: number | null;
  /**
   * 剩余秒数
   */
  leftSeconds?: number | null;
}
export interface OrderBaseInfoDTO {
  /**
   * 订单ID
   */
  orderId?: number | null;
  /**
   * 订单userID
   */
  uId?: string | null;
  /**
   * 订单渠道
   */
  channelType?: string | null;
  /**
   * 修改后订单
   */
  newOrderId?: string | null;
  /**
   * 修改前订单
   */
  oldOrderId?: string | null;
  /**
   * 下单时间
   */
  orderDate?: number | null;
  /**
   * 下单站点
   */
  orderLocale?: string | null;
  /**
   * 订单状态
   */
  orderStatus?: number | null;
  /**
   * 订单状态描述(包括续租)
   */
  orderStatusDesc?: string | null;
  /**
   * ISD:订单状态名称
   */
  orderStatusName?: string | null;
  /**
   * 我携状态值
   */
  orderStatusCtrip?: string | null;
  /**
   * 订单所有状态
   */
  allStatuses?: OrderStatusInfo[] | null;
  /**
   * 订单所有操作项
   */
  allOperations?: OrderOperation[] | null;
  /**
   * 订单Tip
   */
  orderTip?: OrderTipInfo | null;
  /**
   * 使用时间,也pickupDate
   */
  useDate?: number | null;
  /**
   * 返还时间
   */
  returnDate?: number | null;
  /**
   * 租期
   */
  duration?: number | null;
  /**
   * ISD 是否是特许经营
   */
  ftype?: number | null;
  useCityID?: number | null;
  useCity?: string | null;
  /**
   * ISD:自营供应商名称
   */
  selfName?: string | null;
  /**
   * ISD：供应商确认单号
   */
  vendorOrderCode?: string | null;
  /**
   * 天数
   */
  useQuantity?: number | null;
  processStatus?: number | null;
  /**
   * 最后允许支付的时间
   */
  lastEnablePayTime?: number | null;
  /**
   * ISD:订单类型（0.短租订单，1.长租订单，2.c2b订单）
   */
  orderType?: number | null;
  /**
   * ISD:支付类型 收款方式(1-现付;2-预付;12-预付定金)
   */
  payMode?: number | null;
  /**
   * 到店支付/在线支付/预付定金/在线预授权
   */
  payModeDesc?: string | null;
  distributionChannelId?: number | null;
  quickPayNo?: string | null;
  remark?: string | null;
  rateCode?: string | null;
  rateCategory?: string | null;
  grantedCode?: string | null;
  preAmountForCar?: number | null;
  preAmountForPeccancy?: number | null;
  /**
   * ISD：预授权类型：0:全部不支持 1: 支持租车预授权 2：支持违章预售券 3 :支持租车和违章预
   * 授权
   */
  preAmountType?: number | null;
  preAuthStatus?: number | null;
  vendorPreAuthInfo?: VendorPreAuthInfoDTO | null;
  preAmountDesc?: Label[] | null;
  freeCancelTime?: number | null;
  cancelRuleDesc?: string | null;
  isEasyLife?: boolean | null;
  subEasyLifeType?: number | null;
  /**
   * ISD 是否alipay
   */
  alipay?: boolean | null;
  /**
   * 是否无忧租
   */
  safeRent?: boolean | null;
  /**
   * 是否无忧租全部认证
   */
  successSafeRentAuth?: boolean | null;
  /**
   * ISD 是否展示合同快照
   */
  orderContact?: boolean | null;
  /**
   * 是否继续支付，后付预授权
   */
  continueBackPay?: boolean | null;
  /**
   * 程信分判定结果： F:程信分拒绝 N:需外查第三方T:程信分通过
   */
  creditRiskResult?: string | null;
  /**
   * 程信分准入判定使用的 requestId
   */
  creditRiskRequestId?: string | null;
  /**
   * 免押方式 0-非免押订单 1-程信分 2-芝麻 3-供应商无条件免押
   */
  freeDepositWay?: number | null;
  /**
   * 0不支持 10押金双免 20免租车押金 30免违章押金
   */
  freeDepositType?: number | null;
  /**
   * 强绑保险（0-无强绑 1-强绑基础险）
   */
  foreInsurance?: number | null;
  /**
   * 是否可签署合同
   */
  signContract?: boolean | null;
  /**
   * 是否卡拉比订单
   */
  calabiOrder?: boolean | null;
  /**
   * 其他操作按钮
   */
  extOperation?: OrderOperation[] | null;
}
export interface VendorPreAuthInfoDTO {
  /**
   * bool isSupportPre;    bool isForcePre;
   */
  preAuthDisplay?: number | null;
  preWay?: number | null;
  authdesc?: string | null;
  authMartket?: number | null;
  authLabel?: string | null;
  quickPayNo?: string | null;
}
export interface OrderTipInfo {
  /**
   * tip类型:        1:继续支付倒计时        2:确认时间提示        3:出
   * 行提示
   */
  tipType?: number | null;
  /**
   * tip内容
   */
  tipContent?: string | null;
  /**
   * tip内容，支持多行
   */
  tipContentArray?: string[] | null;
  warnTip?: string | null;
  warnType?: number | null;
  urgentWarning?: string | null;
}
export interface OrderOperation {
  /**
   * 操作ID1-去支付  2-取消  3-去点评  4-再次预订  5-打印提车单  6-打印电子发票
   *  7-修改订单  8-修改详情  9-查看修改后订单10-查看修改前订单 11-续租 12-取消修改
   *  13-订单详情 14-联系门店  15-取车材料
   */
  operationId?: number | null;
  /**
   * 按钮名称
   */
  buttonName?: string | null;
  /**
   * 是否有效
   */
  enable?: boolean | null;
  /**
   * 显示效果： none不显示
   */
  display?: string | null;
  /**
   * 0-正常露出点评，无积分奖励，1-正常露出，有积分奖励，2追评，3查看点评operationId=7
   * 时 跳转方式 0-浮层 1-二级页
   */
  code?: number | null;
  /**
   * 按钮上的标签，示例：最高150积分
   */
  label?: string | null;
  /**
   * 跳转地址
   */
  url?: string | null;
  /**
   * 按钮内容信息
   */
  contents?: KeyAndValue[] | null;
  /**
   * 不支持修改的原因，取消文案展示会用到 1、订单状态非已确认都不支持修改订单，2、一嗨不支持，3、供应
   * 商黑名单不支持 ，4、2小时以内不支持
   */
  disableCode?: number | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 在卡片中的排序
   */
  attrDTO?: AttrDto | null;
  /**
   * 额外属性
   */
  attrExtra?: AttrExtra[] | null;
}
export interface AttrExtra {
  code?: string | null;
  value?: string | null;
}
export interface OrderStatusInfo {
  /**
   * 排序码
   */
  sortNum?: number | null;
  /**
   * 订单状态描述
   */
  description?: string | null;
  /**
   * 是否已经过该状态
   */
  lived?: boolean | null;
}
export interface ResponseStatusType {
  timestamp?: string | null;
  ack?: AckCodeType | null;
  errors?: ErrorDataType[] | null;
  build?: string | null;
  version?: string | null;
  extension?: ExtensionType[] | null;
  /**
   * 描述信息
   */
  responseDesc?: string | null;
  /**
   * 响应码
   */
  responseCode: number;
  userID?: string | null;
  msg?: string | null;
}
export interface ExtensionType {
  /**
   * ExtensionType
   */
  id?: string | null;
  /**
   * ExtensionType
   */
  version?: string | null;
  /**
   * ExtensionType
   */
  contentType?: string | null;
  /**
   * ExtensionType
   */
  value?: string | null;
}
export interface ErrorDataType {
  message?: string | null;
  /**
   * A unique code that identifies the particular error
   *  condition that occurred.
   */
  errorCode?: string | null;
  /**
   * ErrorDataType
   */
  stackTrace?: string | null;
  /**
   * ErrorDataType
   */
  severityCode?: SeverityCodeType | null;
  /**
   * ErrorDataType
   */
  errorFields?: ErrorFieldType | null;
  /**
   * ErrorDataType
   */
  errorClassification?: ErrorClassificationCodeType | null;
}
export interface ErrorFieldType {
  /**
   * ErrorFieldType
   */
  fieldName?: string | null;
  /**
   * ErrorFieldType
   */
  errorCode?: string | null;
  /**
   * ErrorFieldType
   */
  message?: string | null;
}
export interface BaseRequest {
  sourceFrom?: string | null;
  requestId?: string | null;
  parentRequestId?: string | null;
  channelId?: number | null;
  channelType?: number | null;
  /**
   * 分销渠道
   */
  allianceInfo?: AllianceInfoDTO | null;
  /**
   * 本地语言
   */
  locale?: string | null;
  /**
   * 货币
   */
  currencyCode?: string | null;
  /**
   * 移动设备信息
   */
  mobileInfo?: MobileDTO | null;
  /**
   * 客源国ID
   */
  sourceCountryId?: number | null;
  site?: string | null;
  language?: string | null;
  sessionId?: string | null;
  invokeFrom?: number | null;
  uid?: string | null;
  patternType?: number | null;
  clientId?: string | null;
  vid?: string | null;
  extraTags?: { [key: string]: string } | null;
  extraMaps?: { [key: string]: string } | null;
  /**
   * 每个前端页面的pageId
   */
  pageId?: string | null;
  /**
   * 客户端版本
   */
  clientVersion?: string | null;
  /**
   * 设备号
   */
  deviceId?: string | null;
}
export interface MobileDTO {
  /**
   * 无线用户纬度
   */
  customerGPSLat?: number | null;
  /**
   * 无线用户经度
   */
  customerGPSLng?: number | null;
  /**
   * 用户手机类型Android、iOS等
   */
  mobileModel?: string | null;
  /**
   * 用户手机的SN编号，机器唯一标识
   */
  mobileSN?: string | null;
  /**
   * 用户IP地址
   */
  customerIP?: string | null;
  /**
   * 无线版本号
   */
  wirelessVersion?: string | null;
}
export interface AllianceInfoDTO {
  /**
   * 分销联盟
   */
  allianceId?: number | null;
  /**
   * 分销联盟二级分销ID
   */
  ouid?: string | null;
  /**
   * 分销联盟三级分销ID
   */
  sid?: number | null;
  /**
   * 分销商订单Id
   */
  distributorOrderId?: string | null;
  /**
   * 分销商用户Id
   */
  distributorUID?: string | null;
}
export interface MobileRequestHead {
  /**
   * h5: 09；原生：30；如果在app访问h5，是其他值。
   */
  syscode?: string | null;
  /**
   * MobileRequestHead
   */
  lang?: string | null;
  /**
   * MobileRequestHead
   */
  auth?: string | null;
  /**
   * 客户端id：09031038210794831462
   */
  cid?: string | null;
  /**
   * MobileRequestHead
   */
  ctok?: string | null;
  /**
   * MobileRequestHead
   */
  cver?: string | null;
  /**
   * MobileRequestHead
   */
  sid?: string | null;
  /**
   * MobileRequestHead
   */
  extension?: ExtensionFieldType[] | null;
}
export interface ExtensionFieldType {
  name?: string | null;
  value?: string | null;
}
enum ErrorClassificationCodeType {
  ServiceError = 0,
  ValidationError = 1,
  FrameworkError = 2,
  SLAError = 3,
}
enum SeverityCodeType {
  Error = 0,
  Warning = 1,
}
enum AckCodeType {
  Success = 0,
  Failure = 1,
  Warning = 2,
  PartialFailure = 3,
}
