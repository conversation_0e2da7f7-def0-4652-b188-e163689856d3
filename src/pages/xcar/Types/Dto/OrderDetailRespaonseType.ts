export interface ErrorFieldsType {
  fieldName?: string;
  errorCode?: string;
  message?: string;
}

export interface ErrorsType {
  message?: string;
  errorCode?: string;
  stackTrace?: string;
  severityCode?: any;
  errorFields?: Array<ErrorFieldsType>;
  errorClassification?: any;
}

export interface ExtensionType {
  id?: string;
  version?: string;
  contentType?: string;
  value?: string;
}

export interface ResponseStatusType {
  timestamp?: Date;
  ack?: string;
  errors?: Array<ErrorsType>;
  build?: string;
  version?: string;
  extension?: Array<ExtensionType>;
}

export interface AllStatusesType {
  sortNum?: number;
  description?: string;
  lived?: boolean;
}

export enum ModifyOrderAllOperationsCodeType {
  // operationId=7时 跳转方式 0-浮层 1-二级页
  modal = 0,
  page = 1,
}

export enum ModifyTipInfoCodeType {
  success = '1', // 修改成功toast文案
  failCancelRule = '2', // 修改失败取消政策
  failText = '3', // 修改失败提示文案
  reorderTip = '4', // 取消重订成功、失败提示
  payFail = '5', // 超时未支付失败文案
  ehiDeposit = '6', // 一嗨免押
}

export enum ModifyStatusType {
  fail = -3, // 修改失败
  payFail = -2, // 超时未支付取消
  canceled = -1, // 已取消
  processing = 1, // 进行中
  success = 2, // 修改成功
}

export enum ModifyType {
  originModify = 1, // 原单修改
  reBook = 2, // 取消重定
}

export interface ModifyTipInfoType {
  code?: ModifyTipInfoCodeType;
  content?: string;
  cancelTipColor?: boolean; // 取消相关的描述的颜色，1 绿色
}

export interface ModifyCancelRule {
  content?: string;
  contentAlert?: Array<string>;
  code?: string;
  color?: number;
  highLight?: string;
}

export interface ModifyInfoDtoType {
  beforeOrderId?: number; // 修改前单号
  afterOrderId?: number; // 修改后单号
  modifyStatus?: ModifyStatusType; // 最近一次的修改结果
  modifyType?: number; // 最近一次的修改类型
  tipInfo?: Array<ModifyTipInfoType>;
  modifyCancelRules?: Array<ModifyCancelRule>;
}

export interface AllOperationsType {
  operationId?: number;
  buttonName?: string;
  enable?: boolean;
  display?: string;
  code?: ModifyOrderAllOperationsCodeType;
  label?: string;
  url?: string;
  disableCode?: number;
}

export interface OrderTipType {
  tipType?: number;
  tipContent?: string;
  tipContentArray?: Array<string>;
  warnTip?: string;
  warnType?: number;
  urgentWarning?: string;
}

export interface VendorPreAuthInfoType {
  preAuthDisplay?: number;
  preWay?: number;
  authdesc?: string;
  authMartket?: number;
  authLabel?: string;
  quickPayNo?: string;
}

export interface PreAmountDescType {
  name?: string;
  description?: string;
}

export interface OrderBaseInfoType {
  orderId?: number;
  uId?: string;
  channelType?: string;
  newOrderId?: string;
  oldOrderId?: string;
  orderDate?: number;
  orderLocale?: string;
  orderStatus?: number;
  orderStatusDesc?: string;
  orderStatusName?: string;
  allStatuses?: Array<AllStatusesType>;
  allOperations?: Array<AllOperationsType>;
  orderTip?: OrderTipType;
  useDate?: number;
  returnDate?: number;
  duration?: number;
  ftype?: number;
  useCityID?: number;
  useCity?: string;
  selfName?: string;
  vendorOrderCode?: string;
  useQuantity?: number;
  processStatus?: number;
  lastEnablePayTime?: number;
  orderType?: number;
  payMode?: number;
  payModeDesc?: string;
  distributionChannelId?: number;
  quickPayNo?: string;
  remark?: string;
  rateCode?: string;
  rateCategory?: string;
  grantedCode?: string;
  preAmountForCar?: number;
  preAmountForPeccancy?: number;
  preAmountType?: number;
  preAuthStatus?: number;
  vendorPreAuthInfo?: VendorPreAuthInfoType;
  preAmountDesc?: Array<PreAmountDescType>;
  freeCancelTime?: number;
  cancelRuleDesc?: string;
  isEasyLife?: boolean;
  subEasyLifeType?: number;
  alipay?: boolean;
  safeRent?: boolean;
  successSafeRentAuth?: boolean;
  orderContact?: boolean;
  continueBackPay?: boolean;
  creditRiskResult?: string;
  creditRiskRequestId?: string;
  freeDepositWay?: number;
  freeDepositType?: number;
  foreInsurance?: number;
}

export interface ContinuePayInfoType {
  needContinuePay?: boolean;
  leftMinutes?: number;
  leftSeconds?: number;
}

export interface PrepayPriceType {
  title?: string;
  totalPrice?: number;
  prepaidPrice?: number;
  poaPrice?: number;
  currencyCode?: string;
  dayPrice?: number;
  oneWayFee?: number;
  oneWayFeeInclusive?: boolean;
  reducePayAmount?: number;
}

export interface CouponsType {
  couponCode?: string;
  promotionId?: number;
  isNeedDebate?: boolean;
  deductionAmount?: number;
  remark?: string;
  displayName?: string;
}

export interface OrderPriceInfoType {
  packageType?: number;
  currentTotalPrice?: number;
  currentCurrencyCode?: string;
  localTotalPrice?: number;
  localCurrencyCode?: string;
  payMode?: number;
  payModeDesc?: string;
  prepayPrice?: PrepayPriceType;
  localPrice?: PrepayPriceType;
  prepayPriceDetails?: Array<PrepayPriceType>;
  localPriceDetails?: Array<PrepayPriceType>;
  priceDetails?: Array<PrepayPriceType>;
  payAmount?: number;
  couponAmount?: number;
  priceDescription?: string;
  coupons?: Array<CouponsType>;
}

export interface OsdSimilarVehilceListType {
  vehicleCode?: string;
  vehicleName?: string;
  vehicleImageUrl?: string;
}

export interface VehicleInfoType {
  vehicleName?: string;
  special?: boolean;
  passengerNum?: number;
  luggageNum?: number;
  hasAC?: boolean;
  transmission?: string;
  vehicleGroupName?: string;
  vendorVehicleCode?: string;
  imageUrl?: string;
  similarImageUrls?: Array<string>;
  osdSimilarVehilceList?: Array<OsdSimilarVehilceListType>;
  granted?: boolean;
  grantCode?: string;
  vehicleDisplacement?: string;
  vendorVehicleID?: number;
  ctripVehicleID?: string;
  vehicleDegree?: string;
  displacement?: string;
  style?: string;
  labels?: Array<PreAmountDescType>;
  isgranted?: boolean;
  grantedCode?: string;
  vdegree?: string;
  license?: string;
  licenseStyle?: string;
  licenseLimitDesc?: string;
  doorNum?: number;
}

export interface BookingNoticeType {
  id?: number;
  type?: number;
  name?: string;
  description?: string;
  detailDesc?: Array<string>;
}

export interface CommentInfoType {
  vendorGoodType?: number;
  exposedScore?: number;
  topScore?: number;
  level?: string;
  commentLabel?: string;
  commentCount?: number;
}

export interface VendorInfoType {
  vendorName?: string;
  vendorImageUrl?: string;
  confirmRightNow?: boolean;
  confirmDate?: number;
  confirmDateStr?: string;
  platformCode?: string;
  platformName?: string;
  vendorID?: number;
  vendorConfirmCode?: string;
  isSelf?: boolean;
  selfName?: string;
  vendorMobileImageUrl?: string;
  bookingNotice?: Array<BookingNoticeType>;
  bizVendorCode?: string;
  subType?: number;
  commentInfo?: CommentInfoType;
}

export interface FuelInfoType {
  isGiven?: boolean;
  code?: string;
  name?: string;
  desc?: string;
}

export interface MileageAllowanceType {
  isUnLimited?: boolean;
  limitedDistance?: number;
  limitedDistanceUnit?: string;
  limitedPeriodUnit?: string;
  overUnitAmount?: number;
  overQuantity?: number;
  overDistanceUnit?: string;
  overPeriodUnit?: string;
  mileAgeDesc?: string;
}

export interface ContinentType {
  id?: number;
  name?: string;
  enName?: string;
}

export interface CityType {
  id?: number;
  name?: string;
  timeZone?: number;
  enName?: string;
}

export interface PoiInfoType {
  latitude?: number;
  longitude?: number;
  type?: number;
}

export interface LocationType {
  locationType?: number;
  locationName?: string;
  locationNameEn?: string;
  locationCode?: string;
  continent?: ContinentType;
  country?: ContinentType;
  province?: ContinentType;
  city?: CityType;
  poiInfo?: PoiInfoType;
}

export interface PickupStoreType {
  localDateTime?: string;
  storeName?: string;
  storeCode?: string;
  storeAddress?: string;
  longitude?: number;
  latitude?: number;
  storeGuide?: string;
  storeLocation?: string;
  storeWay?: string;
  storeTel?: string;
  storeOpenTimeDesc?: string;
  outOfHourDescription?: string;
  cityName?: string;
  provinceName?: string;
  countryName?: string;
  userSearchLocation?: string;
  mapUrl?: string;
  fromTime?: string;
  toTime?: string;
  cityId?: number;
  storeSerivceName?: string;
  userAddress?: string;
  userLongitude?: number;
  userLatitude?: number;
  serviceType?: string;
  serviceDetails?: Array<string>;
  addrTypeName?: string;
  storeID?: number;
  commentCount?: number;
  pickUpOffLevel?: number;
  sendTypeForPickUpOffCar?: number;
  location?: LocationType;
  storeType?: number;
  contactWayList?: ContactWay[];
}
export interface ContactWay {
  /**
   * 联系方式类型（1-WeChat 2-Line 3-kakao talk 4-wharapp 5-qq
   *  6-当地电话）
   */
  contactWayType?: string | null;
  /**
   * 联系方式账号
   */
  contactWayValue?: string | null;
  /**
   * 联系方式名称
   */
  contactWayName?: string | null;
  /**
   * 联系方式(掩码处理，当前仅针对电话)
   */
  maskContactWayValue?: string | null;
  /**
   * 国家码（当前仅针对电话）
   */
  areaCodeList?: string[] | null;
}

export interface DriverInfoType {
  name?: string;
  age?: string;
  email?: string;
  telphone?: string;
  areaCode?: string;
  flightNo?: string;
  iDCardType?: number;
  iDCardNo?: string;
  encrypIDCardNo?: string;
  decryptIDCardNo?: string; // 身份证号明文
  decryptTelphone?: string; // 联系方式明文
  distributionMobile?: string;
  distributionEmail?: string;
  /**
   * 社交账号联系方式
   */
  contactWayList?: ContactWay[] | null;
  /**
   * 是否可以修改（联系方式和航班被）
   */
  isChangeContact?: boolean | null;
  /**
   * 可修改的联系方式
   */
  optionalContactWayList?: ContactWay[] | null;
}

export interface GroupingPackageIncludesType {
  name?: string;
  type?: number;
  items?: Array<string>;
  descriptions?: Array<string>;
}

export interface ExtraInfosType {
  name?: string;
  count?: number;
  unitPrice?: number;
  currencyCode?: string;
  unit?: string;
}

export interface CreditCardInfoType {
  cards?: Array<string>;
  description?: string;
  depositCurrencyCode?: string;
  maxDeposit?: number;
  minDeposit?: number;
}

export interface IdentityDescriptionType {
  title?: string;
  description?: string;
  highLight?: boolean;
}

export interface DescDetailType {
  title?: string;
  desc?: string;
}

export interface InsuranceDescriptionsType {
  code?: string;
  name?: string;
  currencyCode?: string;
  minExcess?: number;
  maxExcess?: number;
  minCoverage?: number;
  maxCoverage?: number;
  longDesc?: string;
  coverageDesc?: string;
  unCoverageDesc?: string;
  productId?: number;
  shortDesc?: string;
  quantity?: number;
  status?: number;
  ordertitle?: string;
  requestid?: string;
  dlabel?: Array<string>;
  tlabel?: Array<string>;
  exttip?: string;
  descDetail?: Array<DescDetailType>;
}

export interface InsuranceType {
  name?: string;
  code?: string;
  title?: string;
  coverageDesc?: string;
  unCoverageDesc?: string;
  specificName?: number;
  insFrom?: number;
  productId?: number;
  prcie?: number;
  status?: number;
  canUpgrade?: boolean;
}

export interface CancelRulesType {
  freeStatus?: number;
  free?: number;
  title?: string;
  context?: string;
  time?: string;
  hit?: boolean;
}

export interface CancelRuleInfoType {
  isTotalLoss?: boolean;
  isFreeCancel?: boolean;
  hours?: number;
  cancelDescription?: string;
  cancelTip?: string;
  title?: string;
  longTitle?: string;
  ruleList?: Array<string>;
  freeCancelDesc?: string;
  cancelLossDesc?: string;
  cancelLossAmount?: number;
  isLossFree?: boolean;
  cancelReasons?: Array<string>;
  cancelRules?: Array<CancelRulesType>;
  cancelTipColor?: number;
  depositSimpleDescs?: any;
}

export interface InfoType {
  description?: string;
  highLight?: boolean;
}

export interface DriverDescriptionsType {
  title?: string;
  info?: Array<InfoType>;
}

export interface AgeInfoType {
  description?: string;
  minDriverAge?: number;
  maxDriverAge?: number;
  youngDriverAge?: number;
  oldDriverAge?: number;
  youngDriverAgeDesc?: string;
  oldDriverAgeDesc?: string;
  licenceAge?: number;
  licenceAgeDesc?: string;
}

export interface RefundProgressListType {
  billNo?: number;
  billItemNo?: number;
  currency?: string;
  refundAmount?: number;
  billStatus?: string;
  remark?: string;
  dealStatus?: number;
  createTime?: Date;
  dealTime?: Date;
  refundCycle?: string;
  refundPeriodKey?: string;
  paymentWayID?: string;
  paymentWayName?: string;
  refundType?: number;
}

export interface FeeListType {
  priceCode?: string;
  priceName?: string;
  shortDesc?: string;
  priceDesc?: string;
  amount?: number;
  quantity?: number;
  descdetail?: Array<DescDetailType>;
}

export interface IsdFeeInfoType {
  salesAmount?: number;
  actualAmount?: number;
  noPayAmount?: number;
  firstPayAmount?: number;
  totalAmount?: number;
  orderAmount?: number;
  extraAmount?: number;
  deductAmount?: number;
  rebackAmount?: number;
  precar?: number;
  prepeccancy?: number;
  priceType?: number;
  rateCode?: string;
  rateCategory?: string;
  isWholeday?: number;
  feeList?: Array<FeeListType>;
  preAuthDesc?: string;
  preAuthAmount?: number;
  preAuthDisplay?: number;
  totalRentalPrice?: number;
  rentCardTotalFee?: number;
  exceedTenancy?: number;
  exceedPrice?: number;
  dailyPrice?: number;
}

export interface LabelsType {
  title?: string;
  subTitle?: string;
  code?: string;
  type?: number;
  grade?: number;
  sortNum?: number;
}

export interface ItemsType {
  title?: string;
  subTitle?: string;
  description?: string;
  descList?: Array<string>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
}

export interface ItemsType3 {
  title?: string;
  subTitle?: string;
  description?: string;
  descList?: Array<string>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: Array<ItemsType>;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
}

export interface ChargesInfosType {
  title?: string;
  subTitle?: string;
  description?: string;
  descList?: Array<string>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: Array<ItemsType3>;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
}

export interface CashBackInfoType {
  currencyCode?: string;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  items?: Array<ChargesInfosType>;
}

export interface FeeInfoType {
  chargesInfos?: Array<ChargesInfosType>;
  equipmentInfos?: Array<ChargesInfosType>;
  activityInfo?: ChargesInfosType;
  couponInfos?: Array<ChargesInfosType>;
  cashBackInfo?: CashBackInfoType;
  depositInfo?: CashBackInfoType;
  notIncludeCharges?: ChargesInfosType;
  chargesSummary?: ChargesInfosType;
  chargesBrief?: ChargesInfosType;
  rentalTerm?: number;
}

export interface DesclistType {
  title?: string;
  desclist?: Array<string>;
}

export interface InsurancelistType {
  title?: string;
  type?: number;
  desclist?: Array<DesclistType>;
}

export interface IsdVendorInsuranceType {
  insurancedesc?: string;
  insurancelist?: Array<InsurancelistType>;
}

export interface SubCtripInsuranceInfoListType {
  productId?: number;
  productName?: string;
  statusDesc?: string;
  insuranceApplyEntry?: string;
  url?: string;
  insuranceUrl?: string;
  description?: string;
  insured?: string;
}

export interface ClaimBttonType {
  title?: string;
  description?: string;
  statusType?: number;
  actionUrl?: string;
  icon?: string;
  type?: number;
}

export interface ClaimInfoType {
  productId?: number;
  claimBtton?: ClaimBttonType;
  claimStatusDesc?: string;
  claimStatusType?: number;
  insuracneCode?: string;
}

export interface CtripInsuranceInfosType {
  insuranceId?: number;
  clientName?: string;
  givenName?: string;
  surname?: string;
  productId?: number;
  productName?: string;
  statusDesc?: string;
  insuranceApplyEntry?: string;
  url?: string;
  insuranceUrl?: string;
  description?: string;
  insured?: string;
  subCtripInsuranceInfoList?: Array<SubCtripInsuranceInfoListType>;
  insuranceAmount?: number;
  claimInfo?: ClaimInfoType;
  insuranceOrderId?: string;
  boughtFrom?: number;
}

export interface OsdAvailableInsuranceDescInfoType {
  hasAvailableInsurance?: boolean;
  title?: string;
  content?: string;
  entryContent?: string;
  url?: string;
}

export interface OnlinePreAuthType {
  preAuthType?: number;
  resultType?: number;
  lossType?: number;
  lossAmount?: number;
  lossReason?: string;
  proofImg?: string;
  remark?: string;
  billNo?: number;
  processStatus?: number;
}

export interface DeliveryInfoType {
  deliveryAddress?: string;
  deliveryCityName?: string;
  deliveryContactTel?: string;
  deliveryDistrictName?: string;
  deliveryGoodType?: number;
  deliveryPostCode?: string;
  deliveryProvinceName?: string;
  deliveryReceiverName?: string;
  deliveryType?: number;
}

export interface MakeUpInvoiceType {
  invoiceType?: number;
  amount?: number;
  needSix?: number;
}

export interface InvoiceType {
  deliveryInfo?: DeliveryInfoType;
  makeUpInvoice?: MakeUpInvoiceType;
}

export interface RentCenterType {
  rentCenter?: boolean;
  id?: number;
  name?: string;
  bacimage?: string;
  images?: Array<string>;
}

export interface ModifyInfoType {
  pickupcityId?: string;
  returncityId?: string;
  pickupStoreId?: string;
  returnStoreId?: string;
  vendorId?: string;
  vehicleId?: string;
  isgranted?: boolean;
  grantedCode?: string;
  ctripVehicleId?: string;
  pickupStoreServiceType?: string;
  returnStoreServiceType?: string;
  rateCategory?: string;
  rateCode?: string;
  payMode?: number;
  vdegree?: string;
  priceType?: number;
}

export interface RenewalInfoType {
  title?: string;
  description?: string;
  code?: string;
  type?: number;
  typeDesc?: string;
  sortNum?: number;
  extCode?: number;
}

export interface OrderRenewalEntryType {
  renewalButton?: ClaimBttonType;
  renewalInfo?: RenewalInfoType;
}

export interface BaseResponseType {
  isSuccess?: boolean;
  code?: string;
  returnMsg?: string;
  requestId?: string;
  cost?: number;
}

export interface SubListType {
  title?: string;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: number;
}

export interface SubListType2 {
  title?: string;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: number;
  subList?: Array<SubListType>;
}

export interface PackageTipsType {
  title?: string;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: number;
  subList?: Array<SubListType2>;
}

export interface PackageInfosType {
  insPackageId?: number;
  isDefault?: boolean;
  packageName?: string;
  currencyCode?: string;
  defaultBomCode?: string;
  defaultPackageId?: number;
  guaranteeDegree?: number;
  naked?: boolean;
  insuranceNames?: Array<string>;
  lowestDailyPrice?: number;
  gapPrice?: number;
  stepPrice?: number;
  packageTips?: Array<PackageTipsType>;
}

export interface UnconveredInfoType {
  title?: string;
  ctripItemCode?: string;
  itemList?: Array<string>;
}

export interface InsuranceDetailType {
  packageId?: number;
  currencyCode?: string;
  minExcess?: number;
  maxExcess?: number;
  excessShortDesc?: string;
  excessLongDesc?: string;
  minCoverage?: number;
  maxCoverage?: number;
  coverageShortDesc?: string;
  coverageLongDesc?: string;
}

export interface SubObjectType {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  type?: number;
  code?: string;
  urlName?: string;
  url?: string;
  urlList?: Array<string>;
  note?: string;
  sortNum?: number;
}

export interface SubObjectType2 {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  type?: number;
  code?: string;
  urlName?: string;
  url?: string;
  urlList?: Array<string>;
  subObject?: Array<SubObjectType>;
  note?: string;
  sortNum?: number;
}

export interface ConverageExplainType {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  type?: number;
  code?: string;
  urlName?: string;
  url?: string;
  urlList?: Array<string>;
  subObject?: Array<SubObjectType2>;
  note?: string;
  sortNum?: number;
}

export interface InsuranceItemsType {
  productId?: number;
  title?: string;
  subTitle?: string;
  description?: string;
  longDescription?: string;
  unconveredInfo?: UnconveredInfoType;
  code?: string;
  subDesc?: string;
  groupCode?: string;
  type?: number;
  name?: string;
  shortDescription?: string;
  isInclude?: boolean;
  isFromCtrip?: boolean;
  insuranceGuaranteeUrl?: string;
  itemUrl?: string;
  insuranceTips?: Array<PackageTipsType>;
  insuranceDetail?: Array<InsuranceDetailType>;
  converageExplain?: ConverageExplainType;
  unConverageExplain?: ConverageExplainType;
  claimProcess?: ConverageExplainType;
}

export interface CombinationsType {
  bomCode?: string;
  title?: string;
  codes?: Array<string>;
  description?: string;
  currency?: string;
  dayPrice?: number;
  gapPrice?: number;
  stepPrice?: number;
  hike?: boolean;
  totalPrice?: number;
  payMode?: number;
  packageId?: number;
}

export interface VcExtendRequestType {
  responsePickUpLocationId?: string;
  responseReturnLocationId?: string;
  vendorVehicleId?: string;
}

export interface MileInfoType {
  name?: string;
  isLimited?: boolean;
  distance?: number;
  distanceUnit?: string;
  periodUnit?: string;
  chargeAmount?: number;
  chargeUnit?: string;
  quantity?: number;
  desc?: string;
  mileAgeDesc?: string;
}

export interface ConfirmInfoType {
  confirmTitle?: string;
  confirmDesc?: string;
  confirmRightNow?: boolean;
  confirmTime?: number;
}

export interface CancelRuleType {
  isTotalLoss?: boolean;
  isFreeCancel?: boolean;
  isFreeCancelNow?: boolean;
  hours?: number;
  cancelDescription?: string;
}

export interface YoungDriverExtraFeeType {
  localCurrencyCode?: string;
  currentPrice?: number;
  localPrice?: number;
  feeType?: number;
}

export interface AgeRestrictionType {
  description?: string;
  minDriverAge?: number;
  maxDriverAge?: number;
  youngDriverAge?: number;
  oldDriverAge?: number;
  youngDriverAgeDesc?: string;
  oldDriverAgeDesc?: string;
  licenceAge?: number;
  licenceAgeDesc?: string;
  youngDriverExtraFee?: YoungDriverExtraFeeType;
  oldDriverExtraFee?: YoungDriverExtraFeeType;
}

export interface CardListType {
  name?: string;
  type?: string;
}

export interface CreditCardInfoType2 {
  description?: string;
  cardList?: Array<CardListType>;
  depositCurrencyCode?: string;
  maxDeposit?: number;
  minDeposit?: number;
}

export interface InsuranceDetailsType {
  code?: string;
  name?: string;
  currencyCode?: string;
  minExcess?: number;
  maxExcess?: number;
  minCoverage?: number;
  maxCoverage?: number;
}

export interface ChargeListType {
  code?: string;
  name?: string;
  desc?: string;
  quantity?: number;
  payMode?: number;
  netAmount?: number;
  dueAmount?: number;
  currency?: string;
  isIncludedInRate?: boolean;
}

export interface PromotionInfoType {
  type?: number;
  title?: string;
  description?: string;
  deductionPercent?: number;
  deductionAmount?: number;
  code?: string;
  longTag?: string;
  longDesc?: string;
}

export interface PriceInfoListType {
  currentCarPrice?: number;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  localCarPrice?: number;
  localDailyPrice?: number;
  localTotalPrice?: number;
  localCurrencyCode?: string;
  currentCurrencyCode?: string;
  currentOnewayfee?: number;
  currentPoaPrice?: number;
  currentPrepaidPrice?: number;
  localOnewayfee?: number;
  localPoaPrice?: number;
  localPrepaidPrice?: number;
  isContainOnewayFee?: boolean;
  payMode?: number;
  productId?: string;
  packageId?: number;
  packageType?: number;
  creditDescription?: string;
  vcExtendRequest?: VcExtendRequestType;
  exchangeRate?: number;
  mileInfo?: MileInfoType;
  confirmInfo?: ConfirmInfoType;
  cancelRule?: CancelRuleType;
  ageRestriction?: AgeRestrictionType;
  creditCardInfo?: CreditCardInfoType2;
  allTags?: Array<PackageTipsType>;
  insuranceDetails?: Array<InsuranceDetailsType>;
  chargeList?: Array<ChargeListType>;
  promotionInfo?: PromotionInfoType;
  vendorPromotionList?: Array<PromotionInfoType>;
}

export interface FlightDelayInfoType {
  outline?: string;
  keepHours?: string;
  cancelRules?: string;
}

export interface EquipmentsType {
  equipmentType?: number;
  maxCount?: number;
  equipmentCode?: string;
  equipmentName?: string;
  equipmentDesc?: string;
  ageFrom?: number;
  ageFromUnit?: string;
  ageTo?: number;
  ageToUnit?: string;
  localTotalPrice?: number;
  localDailyPrice?: number;
  localCurrencyCode?: string;
  currentCurrencyCode?: string;
  currentTotalPrice?: number;
  currentDailyPrice?: number;
  payMode?: number;
}

export interface DetailsType {
  sortNum?: number;
  payMode?: number;
  title?: string;
  description?: string;
}

export interface TermItemsType {
  sortNum?: number;
  title?: string;
  details?: Array<DetailsType>;
}

export interface TermListType {
  sortNum?: number;
  type?: number;
  title?: string;
  termItems?: Array<TermItemsType>;
}

export interface RentalGuaranteeType {
  name?: string;
  vendorServiceCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  localCurrencyCode?: string;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  currentCurrencyCode?: string;
  description?: Array<string>;
  allTags?: Array<PackageTipsType>;
  longDescription?: Array<string>;
  type?: number;
}

export interface PackageItemsType {
  code?: string;
  name?: string;
  desc?: string;
  sortNum?: number;
}

export interface TextsType {
  title?: string;
  type?: string;
  style?: string;
}

export interface UserAuthStatusType {
  realName?: string;
  idNo?: string;
  idType?: string;
  authorize?: boolean;
  authorizeStatus?: number;
  authorizeStatusName?: string;
  expired?: boolean;
  orderId?: number;
  ctripSatisfy?: boolean;
  amount?: number;
  currency?: string;
  requestid?: string;
  texts?: Array<TextsType>;
}

export interface ZhimaInfoType {
  isSupportZhima?: boolean;
  userAuthStatus?: UserAuthStatusType;
  zhiMaScore?: number;
  desc?: string;
  supportZhiMa?: boolean;
}

export interface CrossislandLocationListType {
  locationId?: string;
  locationName?: string;
  crossStatus?: number;
}

export interface CrossislandPolicyListType {
  policyTitle?: string;
  policyType?: number;
  policyDescription?: string;
}

export interface CrossIslandInfoType {
  crossType?: number;
  crossislandLocationList?: Array<CrossislandLocationListType>;
  crossislandPolicyList?: Array<CrossislandPolicyListType>;
  selectedCrossIslands?: string;
  tips?: string;
}

export interface IncludeInsurancesType {
  name?: string;
  code?: string;
  desc?: string;
}

export interface VendorVehicleInfoType {
  imageUrl?: string;
  vehicleName?: string;
  vehicleEName?: string;
  vehicleGroupName?: string;
  vehicleGroupEName?: string;
  vehicleGroupCode?: string;
  vehicleCode?: string;
  transmissionName?: string;
  transmissionType?: number;
  passengerDesc?: string;
  isSpecilized?: boolean;
  doorCountDesc?: string;
  isHot?: boolean;
  vendorVehicleCode?: string;
  passengerNum?: number;
  luggageNum?: number;
  luggageDesc?: string;
  doorNum?: number;
  vehComment?: string;
  spaceComment?: string;
  sippCode?: string;
  realityImageUrl?: string;
  simpleVehComment?: string;
  vehicleTip?: string;
  vehicleDescEn?: string;
  vehicleDescCn?: string;
  groupClassification?: string;
  abversion?: string;
  comment?: string;
  imageUrls?: Array<string>;
  isgranted?: boolean;
  grantedCode?: string;
  vdegree?: string;
}

export interface ItemsType2 {
  licenceType?: string;
  licenceName?: string;
  licenceDesc?: string;
  downloadUrl?: string;
  sortNum?: number;
}

export interface DriverLicenceListType {
  groupCode?: string;
  groupName?: string;
  items?: Array<ItemsType2>;
  sortNum?: number;
}

export interface AttachedProductsType {
  type?: string;
  title?: string;
  desc?: string;
}

export interface InsurancesType {
  code?: string;
  title?: string;
  desc?: string;
  longDesc?: string;
  coverageDesc?: string;
  unCoverageDesc?: string;
}

export interface ProductInfoListType {
  productCode?: string;
  bomGroupCode?: string;
  priceInfoList?: Array<PriceInfoListType>;
  needFlightNo?: boolean;
  flightDelayInfo?: FlightDelayInfoType;
  needCreditCardInfo?: boolean;
  hasMutiDriver?: boolean;
  mutiDriverCount?: number;
  fuelInfo?: FuelInfoType;
  equipments?: Array<EquipmentsType>;
  termList?: Array<TermListType>;
  rentalGuarantee?: Array<RentalGuaranteeType>;
  ctripInsurances?: RentalGuaranteeType;
  packageItems?: Array<PackageItemsType>;
  naked?: boolean;
  zhimaInfo?: ZhimaInfoType;
  pickUpMaterials?: Array<ConverageExplainType>;
  carRentalMustRead?: Array<ConverageExplainType>;
  isLimitTrans?: boolean;
  limitTransTitle?: string;
  crossPolicy?: string;
  crossIslandInfo?: CrossIslandInfoType;
  includeInsurances?: Array<IncludeInsurancesType>;
  freeItems?: Array<IncludeInsurancesType>;
  specialLicenceDesc?: Array<string>;
  vendorVehicleInfo?: VendorVehicleInfoType;
  driverLicenceList?: Array<DriverLicenceListType>;
  attachedProducts?: Array<AttachedProductsType>;
  insurances?: Array<InsurancesType>;
}

export interface OsdInsuranceItemsType {
  code?: string;
  name?: string;
  shortDescription?: string;
  longDescription?: string;
  isInclude?: boolean;
  isFromCtrip?: boolean;
  insuranceTips?: Array<PackageTipsType>;
  insuranceDetail?: Array<InsuranceDetailType>;
  converageExplain?: ConverageExplainType;
  unConverageExplain?: ConverageExplainType;
  claimProcess?: ConverageExplainType;
  insuranceGuaranteeUrl?: string;
}

export interface ProductDetailsType {
  insPackageId?: number;
  insuranceItems?: Array<InsuranceItemsType>;
  ctripInsuranceIds?: Array<number>;
  insuranceDesc?: Array<string>;
  combinations?: Array<CombinationsType>;
  productInfoList?: Array<ProductInfoListType>;
  osdInsuranceItems?: Array<OsdInsuranceItemsType>;
}

export interface AddPaymentsType {
  orderId?: number;
  amount?: number;
  reason?: string;
  remark?: string;
  payStatus?: number;
  additionalPaymentId?: number;
  payTime?: number;
}

export interface FaqListType {
  questionContent?: string;
  answerContent?: string;
  url?: string;
  murl?: string;
  urlType?: string;
  imUrl?: string;
  questionId?: string;
  relationGuid?: string;
  orderRule?: number;
}

export interface FaqListInfoType {
  faqLinkList?: string;
  faqList?: Array<FaqListType>;
}

export interface CreditInfoType {
  cashPledgeStatus?: number;
  deposit?: number;
  depositExplain?: string;
  requestid?: string;
  serviceAgreement?: string;
  wZDeposit?: number;
}

export interface DepositItemsType {
  depositTitle?: string;
  deposit?: number;
  depositStatus?: number;
  explain?: string;
}

export interface LinkType {
  text?: string;
  url?: string;
}

export interface TextsType2 {
  title?: string;
  link?: LinkType;
}

export interface TipType {
  warnTip?: string;
  texts?: Array<TextsType2>;
  btnType?: number;
}

export interface FreeDepositType {
  depositStatus?: number;
  showDepositType?: number;
  depositExplain?: string;
  payMethodExplain?: string;
  freeDepositType?: number;
  freeDepositWay?: number;
  preAmountForCar?: number;
  preAmountForPeccancy?: number;
  preDepositAmount?: number;
  depositItems?: Array<DepositItemsType>;
  deductionTime?: string;
  isBeforeNow?: boolean;
  preAuthWarning?: string;
  tip?: DepositTip;
  /**
   * 按钮与文案
   */
  tips?: DepositTip[] | null;
  /**
   * 在线付押金
   */
  payOnlineInfo?: PayOnlineDTO | null;
  /**
   * 在线付押金金额
   */
  preAmountForPayOnline?: number | null;
  /**
   * 免押按钮与文案
   */
  freeDepositBtn?: FreeDepositBtn | null;
}

export interface FreeDepositBtn {
  title?: string | null;
  description?: string | null;
  /**
   * 状态 0 不可用  1可用
   */
  statusType?: number | null;
  actionUrl?: string | null;
  icon?: string | null;
  type?: number | null;
}

export interface DepositTipsExplainProps {
  title: string;
  link: {
    text: string;
    linkType: number;
    icon: string;
    color?: string;
    url?: string;
  };
}
export interface DepositTip {
  /**
   * 不能免押时的提示
   */
  warnTip?: string | null;
  /**
   * 免押时的文案list
   */
  texts?: TextDTO[] | null;
  /**
   * 在线付押金4，实名认证 3, 去验证 2, 确认免押 1
   */
  btnType?: number | null;
  /**
   * 去验证的文案
   */
  text2?: string | null;
  /**
   * 不能免押类型 1-一嗨不支持免押 2-已达最大笔数  3-F或其他情况，边界情况为不支持免押
   */
  warnType?: number | null;
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 副标题
   */
  subTitles?: string | null;
  /**
   * 按钮名称
   */
  btnName?: string | null;
  /**
   * 免押方式  0-F或其他情况不支持免押 1-程信分，2-芝麻，3-在线预授权，4-在线付押金
   */
  depositType?: number | null;
  /**
   * 售后去免押是否是老芝麻 0-否 1-是
   */
  oldAlipayCredit?: number | null;
  /**
   * 浮层-一嗨免押政策
   */
  noteInfo?: NoteInfo | null;
  /**
   * 实名认证文案
   */
  verifyTexts?: string[] | null;

  explain?: DepositTipsExplainProps[];
  /**
   * 是否置灰 0-不可用，1-可用
   */
  status?: number;
}

export interface NoteInfo {
  title?: string | null;
  contents?: Contents[] | null;
  table?: TableDesc[] | null;
  items?: TableDesc[] | null;
}

export interface TableDesc {
  title?: Contents | null;
  desc?: Contents[] | null;
  note?: Contents | null;
}

export interface Contents {
  contentStyle?: string | null;
  stringObjs?: StringObjs[] | null;
}

export interface StringObjs {
  content?: string | null;
  style?: string | null;
}

export interface TextDTO {
  title?: string | null;
  link?: Link | null;
}

export interface Link {
  text?: string | null;
  url?: string | null;
}

export interface PayOnlineDTO {
  /**
   * 在线付押金描述文案
   */
  payOnlineDesc?: TextDTO[] | null;
  payedTitle?: string | null;
  unPayTitle?: string | null;
  payOnlineExplain?: string[] | null;
  payingDesc?: string | null;
}

export interface TargetTagType {
  title?: string;
  type?: number;
  code?: string;
  color?: string;
}

export interface SubItemType {
  id?: number;
  title?: string;
  desc?: string;
}

export interface SubItemType2 {
  id?: number;
  title?: string;
  desc?: string;
  subItem?: Array<SubItemType>;
}

export interface AccidentType {
  id?: number;
  title?: string;
  desc?: string;
  subItem?: Array<SubItemType2>;
}

export interface InsuranceAndXProductType {
  additionalId?: string;
  name?: string;
  code?: string;
  title?: string;
  description?: Array<string>;
  targetTag?: Array<TargetTagType>;
  specificName?: number;
  sourceFrom?: number;
  productId?: number;
  requestId?: string;
  orderTitle?: string;
  price?: number;
  localCurrencyCode?: string;
  localTotalPrice?: number;
  localDailyPrice?: number;
  currentTotalPrice?: number;
  currentDailyPrice?: number;
  currentCurrencyCode?: string;
  quantity?: number;
  quantityName?: string;
  maxQuantity?: number;
  stock?: number;
  group?: number;
  status?: number;
  canUpgrade?: boolean;
  upgradeAmount?: number;
  extDesc?: string;
  insuranceOrderId?: string;
  toDetailStatus?: number;
  boughtFrom?: number;
  accident?: Array<AccidentType>;
  insContractBottomDesc?: string;
  insServiceBottomDesc?: string;
}

export interface GiftTextType {
  code?: string;
  title?: string;
  subTitle?: string;
  button?: ClaimBttonType;
  linkContent?: Array<AccidentType>;
  desc?: string;
  tips?: RenewalInfoType;
  jsonData?: string;
}

export interface RulesType {
  code?: string;
  title?: string;
  subTitle?: string;
  descs?: Array<string>;
}

export interface FlightDelayRuleType {
  title?: string;
  description?: string;
  subDesc?: string;
  rules?: Array<RulesType>;
  tips?: Array<string>;
}

export interface ExtendedInfoType {
  showCustomerCallModal?: boolean;
  giftText?: GiftTextType;
  orderExtDescList?: Array<ConverageExplainType>;
  flightDelayRule?: FlightDelayRuleType;
  newNoWorry?: boolean; // 新无忧租标记字段
}

export interface ChargeInfoListType {
  chargeCode?: string;
  chargeName?: string;
  chargeDesc?: string;
  num?: number;
  unitPrice?: number;
  unit?: string;
  totalPrice?: number;
  ctripChargeCode?: string;
  ctripChargeName?: string;
}

export interface CtripInsuranceType {
  appId?: number;
  clauseUrl?: string;
  description?: string;
  fullName?: string;
  insuranceVendorId?: number;
  name?: string;
  productId?: number;
  sellingPrice?: number;
  insuranceCompanyCode?: string;
  productCode?: string;
  singleStatus?: number;
  insuranceOrderId?: string;
  orderId?: number;
  insuranceConfirmNo?: string;
  detailUrl?: string;
  effectDate?: string;
  expiryDate?: string;
  expiryDesc?: string;
}

export interface CancelRuleType2 {
  free?: number;
  title?: string;
  context?: string;
  time?: string;
  hit?: boolean;
  start?: Date;
  end?: Date;
}

export interface RenewalOrdersType {
  orderId?: number;
  renewalOrderId?: number;
  totalAmount?: number;
  startDate?: string;
  endDate?: string;
  hours?: number;
  source?: number;
  payStatus?: number;
  payStatusDesc?: string;
  orderStatus?: number;
  orderStatusDesc?: string;
  cancelType?: number;
  penaltyAmount?: number;
  refundBillNo?: string;
  chargeInfoList?: Array<ChargeInfoListType>;
  ctripInsurance?: CtripInsuranceType;
  notice?: string;
  cancelRule?: Array<CancelRuleType2>;
  orderRefundProgressList?: Array<RefundProgressListType>;
  createTime?: string;
  paySuccessTime?: string;
  confirmTime?: string;
  cancelTime?: string;
  renewalStatus?: string;
  renewalStatusName?: string;
  renewalStatusDesc?: string;
}

export interface LabelsInfoType {
  code?: string;
  type?: string;
  sort?: string;
  name?: string;
}

export interface OSDOrderDetail {
  responseStatus?: ResponseStatusType;
  locale?: string;
  naked?: boolean;
  checkSuccess?: boolean;
  shieldEmail?: string;
  lastSendEmail?: string;
  orderBaseInfo?: OrderBaseInfoType;
  continuePayInfo?: ContinuePayInfoType;
  orderPriceInfo?: OrderPriceInfoType;
  vehicleInfo?: VehicleInfoType;
  vendorInfo?: VendorInfoType;
  fuelIsGiven?: boolean;
  fuelInfo?: FuelInfoType;
  mileageAllowance?: MileageAllowanceType;
  pickupStore?: PickupStoreType;
  returnStore?: PickupStoreType;
  driverInfo?: DriverInfoType;
  packageIncludes?: Array<string>;
  groupingPackageIncludes?: Array<GroupingPackageIncludesType>;
  extraInfos?: Array<ExtraInfosType>;
  additionalDriverCount?: number;
  creditCardInfo?: CreditCardInfoType;
  identityDescription?: IdentityDescriptionType;
  insuranceDescriptions?: Array<InsuranceDescriptionsType>;
  insurance?: Array<InsuranceType>;
  cancelRuleInfo?: CancelRuleInfoType;
  driverDescriptions?: Array<DriverDescriptionsType>;
  warmTips?: Array<string>;
  ageInfo?: AgeInfoType;
  arrivalDetails?: Array<IdentityDescriptionType>;
  crossBorderDesc?: IdentityDescriptionType;
  refundProgressList?: Array<RefundProgressListType>;
  isdFeeInfo?: IsdFeeInfoType;
  feeInfo?: FeeInfoType;
  isdVendorInsurance?: IsdVendorInsuranceType;
  ctripInsuranceInfos?: Array<CtripInsuranceInfosType>;
  osdAvailableInsuranceDescInfo?: OsdAvailableInsuranceDescInfoType;
  appOrderDetailIsAddInsuranceNeeded?: boolean;
  appOrderDetailIsSettlementOfClaimOpen?: boolean;
  onlinePreAuth?: Array<OnlinePreAuthType>;
  invoice?: InvoiceType;
  rentCenter?: RentCenterType;
  modifyInfo?: ModifyInfoType;
  orderRenewalEntry?: OrderRenewalEntryType;
  insuranceTips?: Array<RenewalInfoType>;
  baseResponse?: BaseResponseType;
  packageInfos?: Array<PackageInfosType>;
  productDetails?: Array<ProductDetailsType>;
  addPayments?: Array<AddPaymentsType>;
  faqListInfo?: FaqListInfoType;
  isdCarMgImUrl?: string;
  creditInfo?: CreditInfoType;
  isAlipay?: boolean;
  freeDeposit?: FreeDepositType;
  insuranceAndXProduct?: Array<InsuranceAndXProductType>;
  extendedInfo?: ExtendedInfoType;
  renewalOrders?: Array<RenewalOrdersType>;
  labelsInfo?: Array<LabelsInfoType>;
}

export interface TitleAndDesc {
  title?: string | null;
  desc?: string | null;
}

export interface EarlyReturnRecord {
  /**
   * 费用明细
   */
  feeList?: FeeItem[] | null;
  /**
   * 费用汇总信息
   */
  earlyReturnPriceInfo?: EarlyReturnPriceInfo | null;
  /**
   * 是否已经手动修改过，true改过
   */
  manually?: boolean | null;
  /**
   * 仅供参考或手动修改后的提示
   */
  referenceText?: string | null;
  /**
   * 申请时间
   */
  applyTime?: string | null;
  /**
   * 申请的还车时间
   */
  returnTime?: string | null;
  /**
   * 原始还车时间
   */
  oldReturnTime?: string | null;
  /**
   * 1，生效，0已被撤销
   */
  applyStatus?: number | null;
  pickUpTime?: string | null;
  policyText?: TitleAndDesc | null;
  cancellationTime?: string;
}

export interface FeeItem {
  serviceCode?: string | null;
  title?: string | null;
  /**
   * 例如活动为标题时，子标题时活动名
   */
  subTitle?: string | null;
  /**
   * 字体样式，b加粗，无/空则默认
   */
  fieldStyle?: string | null;
  /**
   * 改变类型，0 无变化，1上涨，2下降，3不可用
   */
  changeType?: number | null;
  /**
   * 0, 普通费用项，1活动，2优惠券，3汇总类费用
   */
  type?: number | null;
  oldPrice?: FeeSubItem | null;
  newPrice?: FeeSubItem | null;
  /**
   * 是否是必选的费用
   */
  fixed?: boolean | null;
}

export interface FeeSubItem {
  subTitle?: string | null;
  amount?: number | null;
  price?: number | null;
  count?: number | null;
  unit?: string | null;
  localCurrencyCode?: string | null;
  /**
   * 日均价描述
   */
  dPriceDesc?: string | null;
  /**
   * 小时费描述
   */
  hourDesc?: string | null;
  /**
   * 日价
   */
  priceDailys?: DailyPriceDTO[] | null;
}

export interface DailyPriceDTO {
  /**
   * 日期
   */
  date?: string | null;
  /**
   * 展示金额
   */
  priceStr?: string | null;
  /**
   * 原天价
   */
  oDprice?: string | null;
  /**
   * 展示类型（前端样式用） 0 默认宽度， 1 day1-day2 展示宽度
   */
  showType?: number | null;
}

export interface EarlyReturnPriceInfo {
  /**
   * 原始总价
   */
  originTotalFee?: number | null;
  newTotalFee?: number | null;
  /**
   * 违约金比例
   */
  penaltyRate?: number | null;
  /**
   * 需要退补款金额，负数就是退款
   */
  payAmount?: number | null;
  /**
   * 违约金金额
   */
  penaltyAmount?: number | null;
}

export interface KeyAndValue {
  title?: string | null;
  description?: string | null;
  code?: string | null;
  type?: number | null;
  typeDesc?: string | null;
  sortNum?: number | null;
  extCode?: number | null;
}

export interface AttrDto {
  /**
   * 排序
   */
  sort?: number | null;
  /**
   * true 历史消息，false 当前消息，unknown，需要前端看曝光次数
   */
  history?: string | null;
}

export interface AttrExtra {
  code?: string | null;
  value?: string | null;
}

export interface OrderOperation {
  /**
   * 操作ID1-去支付  2-取消  3-去点评  4-再次预订  5-打印提车单  6-打印电子发票
   *  7-修改订单  8-修改详情  9-查看修改后订单10-查看修改前订单 11-续租 12-取消修改
   *  13-订单详情 14-联系门店  15-取车材料
   */
  operationId?: number | null;
  /**
   * 按钮名称
   */
  buttonName?: string | null;
  /**
   * 是否有效
   */
  enable?: boolean | null;
  /**
   * 显示效果： none不显示
   */
  display?: string | null;
  /**
   * 0-正常露出点评，无积分奖励，1-正常露出，有积分奖励，2追评，3查看点评operationId=7
   * 时 跳转方式 0-浮层 1-二级页
   */
  code?: number | null;
  /**
   * 按钮上的标签，示例：最高150积分
   */
  label?: string | null;
  /**
   * 跳转地址
   */
  url?: string | null;
  /**
   * 按钮内容信息
   */
  contents?: KeyAndValue[] | null;
  /**
   * 不支持修改的原因，取消文案展示会用到 1、订单状态非已确认都不支持修改订单，2、一嗨不支持，3、供应
   * 商黑名单不支持 ，4、2小时以内不支持
   */
  disableCode?: number | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 在卡片中的排序
   */
  attrDTO?: AttrDto | null;
  /**
   * 额外属性
   */
  attrExtra?: AttrExtra[] | null;
}

export enum AdvanceReturnTipType {
  passTime = 1, // 已过时间限制
  hasApply = 2, // 已申请提前还车
  hasWaitPayRenew = 3, // 有待支付续租订单
  default = 4, // 兜底逻辑
}
