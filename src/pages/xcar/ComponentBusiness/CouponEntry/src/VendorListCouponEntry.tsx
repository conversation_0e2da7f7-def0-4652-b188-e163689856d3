import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  XViewExposure,
} from '@ctrip/xtaro';

import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './vendorListCouponEntryC2xStyles.module.scss';
import { Utils, CarLog, AppContext } from '../../../Util/Index';
import { ImageUrl, UITestID, LogKey } from '../../../Constants/Index';
import {
  getServerRequestId,
  getListRequestId,
} from '../../../Global/Cache/ListReqAndResData';

import Channel from '../../../Util/Channel';
import { IListCouponEntry } from './Types';
import ListCouponItem from './ListCouponItem';
import texts from './Texts';

const { isAndroid, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  buttonText: {
    ...font.labelLBoldStyle,
    color: color.white,
    lineHeight: getLineHeight(isAndroid ? 26 : 32),
  },
});

const getCouponExposureData = vehicleCode => ({
  name: '曝光_租车券包',
  pageId: Channel.getPageId().VendorList.ID,
  info: {
    vehicleCode,
  },
});

const VendorListCouponEntry: React.FC<IListCouponEntry> = ({
  renderData = [],
  isReceiveAble,
  onPress,
  vehicleCode,
}) => {
  if (!renderData.length || !Utils.isCtripIsd()) return null;
  const handlePressBanner = () => {
    onPress();
    if (isReceiveAble) {
      CarLog.LogCode({
        name: '点击_租车券包_领取按钮',

        pageId: Channel.getPageId().VendorList.ID,
        vehicleCode,
      });
      CarLog.LogTrace({
        key: LogKey.cm_car_app_click_airanking,
        info: {
          name: '点击_租车券包_领取按钮',
          newMergeId: AppContext.currentNewMergeId,
          vehicleId: vehicleCode,
          serverRequestId: getServerRequestId(),
          requestId: getListRequestId(),
        },
      });
    } else {
      CarLog.LogCode({
        name: '点击_产品详情页_打开券包弹层',

        pageId: Channel.getPageId().VendorList.ID,
        vehicleCode,
      });
    }
  };
  return (
    <XViewExposure
      testID={CarLog.LogExposure(getCouponExposureData(vehicleCode))}
    >
      <View className={c2xStyles.gradientWrap}>
        <Touchable
          className={c2xStyles.wrap}
          onPress={handlePressBanner}
          testID={UITestID.car_testid_page_vendorList_show_coupon_modal}
        >
          <View className={c2xStyles.content}>
            <Image
              className={c2xStyles.couponTitle}
              src={`${ImageUrl.componentImagePath}CouponEntry/coupon_chaozhiyouhui.png`}
              mode="aspectFit"
            />

            {renderData.map(item => (
              <ListCouponItem
                key={item}
                name={item}
                isFromVendorListPage={true}
              />
            ))}
          </View>
          <View className={c2xStyles.rightWrap}>
            <LinearGradient
              className={c2xStyles.buttonWrap}
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 1.0, y: 1.0 }}
              locations={[0, 1]}
              colors={[color.listCouponLinearStart, color.listCouponLinearEnd]}
            >
              <Text style={styles.buttonText} fontWeight="medium">
                {isReceiveAble ? texts.couponEntryButton : texts.couponSeeMore}
              </Text>
            </LinearGradient>
          </View>
        </Touchable>
      </View>
    </XViewExposure>
  );
};
export default VendorListCouponEntry;
