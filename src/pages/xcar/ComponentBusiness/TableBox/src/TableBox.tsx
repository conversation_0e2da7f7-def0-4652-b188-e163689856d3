import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Formatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import {
  color,
  fontCommon,
  layout,
  icon,
  font,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { TabelType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import c2xStyles from './tableBoxC2xStyles.module.scss';
import {
  ItemsType2,
  MaterialsSubObject,
} from '../../../Types/Dto/QueryVehicleDetailInfoResponseType';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  colorPrimary: {
    color: color.fontPrimary,
  },
  lineThrough: {
    color: color.fontPrimary,
    textDecorationLine: 'line-through',
  },
  trLineNew: {
    borderColor: color.storeDetailNewBorder,
    borderStyle: 'solid',
    borderTopWidth: getPixel(1),
  },
  trLeftTextSub: {
    ...fontCommon.captionLightStyle,
    color: color.grayBase,
  },
  trRightPopText: {
    color: color.refundSuccess,
  },
  trRightPopTextNew: {
    color: color.C_3565B9,
  },
  tableWrapLast: {
    borderBottomWidth: getPixel(1),
    borderBottomColor: color.newBookingTableLine,
  },
  tableGrayBg: {
    backgroundColor: color.selfHelpBg,
  },
  tableHeaderText: {
    ...font.caption2BoldStyle,
    color: color.fontSecondary,
  },
  tableGreenBg: {
    backgroundColor: color.labelGreenBg,
  },
  tableGreatText: {
    ...font.caption2BoldStyle,
    color: color.greenBase,
    marginRight: getPixel(4),
  },
  lossFee: {
    ...fontCommon.body3LightStyle,
    color: color.easylifeTextBlack,
    marginBottom: getPixel(4),
  },
});

export interface ITableBox {
  items: ItemsType2[];
  notices?: string[];
  title?: string[];
  style?: CSSProperties;
  trLeftStyle?: CSSProperties;
  titleStyle?: CSSProperties;
  itemTitleStyle?: CSSProperties;
  itemDescriptionStyle?: CSSProperties;
  isISDShelves2B?: boolean;
}

export interface IMaterialsTableBox {
  style?: CSSProperties;
  items: Array<MaterialsSubObject>;
}

const getLossFee = item => {
  if (item?.currentTotalPrice) {
    return (
      <Formatter
        currency={item.currencyCode}
        price={item.currentTotalPrice}
        currencyStyle={styles.colorPrimary}
        priceStyle={styles.colorPrimary}
      />
    );
  }
  if (item?.lossFee > 0) {
    return (
      <Formatter
        currency={item.customerCurrency}
        price={item.lossFee}
        currencyStyle={styles.lossFee}
        priceStyle={styles.lossFee}
      />
    );
  }
  return null;
};

/**
 * 新售前售后车型弹窗展示的取消政策的表格
 * @param props
 * @returns
 */
export const TableBoxNew: React.FC<ITableBox> = props => {
  const {
    style,
    itemTitleStyle,
    itemDescriptionStyle,
    items,
    trLeftStyle,
    isISDShelves2B = false,
  } = props;
  if (!items?.length) return null;
  return (
    <View style={style}>
      <View className={c2xStyles.tableNew}>
        {items.map((item, index) => {
          return (
            <View
              key={item.title}
              style={xMergeStyles([
                index > 0 && styles.trLineNew,
                layout.startHorizontal,
              ])}
            >
              <View
                className={classNames(c2xStyles.trLeft, c2xStyles.trLeft2New)}
                style={trLeftStyle}
              >
                {!!item.subTitle && (
                  <Text style={itemTitleStyle}>{item.subTitle}</Text>
                )}
                {!!item.title && (
                  <Text className={c2xStyles.trLeftTextSubNew}>
                    {item.title}
                  </Text>
                )}
              </View>
              <View
                className={c2xStyles.trRightNew}
                style={layout.justifyCenter}
              >
                <View style={layout.justifyCenter}>
                  {getLossFee(item)}
                  {!!item.description && (
                    <View style={layout.flexRow}>
                      <Text
                        style={xMergeStyles([
                          !!item.currentTotalPrice && styles.trLeftTextSub,
                          itemDescriptionStyle,
                          item.showFree &&
                            (isISDShelves2B
                              ? styles.trRightPopTextNew
                              : styles.trRightPopText),
                        ])}
                      >
                        {item.description}
                      </Text>
                      {!!item.great && (
                        <Text type="icon" className={c2xStyles.greatIcon}>
                          {icon.encourageIcon}
                        </Text>
                      )}
                    </View>
                  )}
                </View>
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

enum StyleType {
  Vertical = 0,
  Horizontal = 1,
}

interface INewTableBox {
  items: Array<TabelType>;
  styleType?: StyleType;
}

export const NewTableBox: React.FC<INewTableBox> = props => {
  const { items, styleType } = props;
  const isHorizontal = styleType === StyleType.Horizontal;
  if (!items?.length) return null;
  if (items?.length === 1 && items?.[0]?.value?.content) {
    return (
      <View className={c2xStyles.formulaWrap}>
        <Text className={c2xStyles.formulaText} fontWeight="bold">
          {items[0].value.content}
        </Text>
      </View>
    );
  }
  return (
    <View>
      {items.map((item, index) => {
        return (
          <View
            key={`${item?.key}_${index}`}
            className={c2xStyles.tableItemWrap}
          >
            <View
              className={c2xStyles.tableKeyWrap}
              style={xMergeStyles([
                index === items.length - 1 && styles.tableWrapLast,
                isHorizontal && index === 0 && styles.tableGrayBg,
                !isHorizontal && styles.tableGrayBg,
              ])}
            >
              {!!item?.key && (
                <Text
                  className={c2xStyles.tableText}
                  style={xMergeStyles([
                    isHorizontal && index === 0 && styles.tableHeaderText,
                    !isHorizontal && styles.tableHeaderText,
                  ])}
                >
                  {item.key}
                </Text>
              )}
            </View>
            <View
              className={c2xStyles.tableValueWrap}
              style={xMergeStyles([
                index === items.length - 1 && styles.tableWrapLast,
                isHorizontal && index === 0 && styles.tableGrayBg,
                item?.value?.great && styles.tableGreenBg,
              ])}
            >
              {!!item?.value?.content && (
                <Text
                  className={c2xStyles.tableText}
                  style={xMergeStyles([
                    isHorizontal && index === 0 && styles.tableHeaderText,
                    item?.value?.great && styles.tableGreatText,
                  ])}
                >
                  {item.value.content}
                </Text>
              )}
              {item?.value?.great && (
                <Text type="icon" className={c2xStyles.tableGreatIcon}>
                  {icon.encourageIcon}
                </Text>
              )}
            </View>
          </View>
        );
      })}
    </View>
  );
};

export const DepositTableBoxNew: React.FC<ITableBox> = props => {
  const { style, items, notices } = props;
  if (!items?.length) return null;
  return (
    <View style={style}>
      <View className={c2xStyles.tableNew}>
        {items.map((item, index) => {
          return (
            <View
              key={item.code}
              style={xMergeStyles([
                index > 0 && styles.trLineNew,
                layout.startHorizontal,
              ])}
            >
              <View className={c2xStyles.depositTrLeft}>
                {!!item.title && (
                  <Text className={c2xStyles.depositText}>{item.title}</Text>
                )}
                {!!item.subTitle && (
                  <Text className={c2xStyles.trLeftTextSubNew}>
                    {item.subTitle}
                  </Text>
                )}
              </View>
              <View className={c2xStyles.trRightNew}>
                <View style={layout.flexRow}>
                  {!!item.currentTotalPrice && (
                    <Formatter
                      currency={item.currencyCode}
                      price={item.currentTotalPrice}
                      currencyStyle={
                        item.needDeposit
                          ? styles.colorPrimary
                          : styles.lineThrough
                      }
                      priceStyle={
                        item.needDeposit
                          ? styles.colorPrimary
                          : styles.lineThrough
                      }
                    />
                  )}
                  {!!item.positiveDesc && (
                    <Text className={c2xStyles.greenTextNew}>
                      {item.positiveDesc}
                    </Text>
                  )}
                </View>
                {!!item.description && (
                  <Text className={c2xStyles.depositText}>
                    {item.description}
                  </Text>
                )}
              </View>
            </View>
          );
        })}
      </View>
      {notices?.length > 0 && (
        <Text className={c2xStyles.notice}>{notices.join('\n')}</Text>
      )}
    </View>
  );
};

export const PickUpMaterialsTableBox: React.FC<IMaterialsTableBox> = props => {
  const { style, items } = props;
  if (!items?.length) return null;
  return (
    <View style={style}>
      <View className={c2xStyles.tableNew}>
        {items.map((item, index) => {
          return (
            <View
              key={item.title}
              style={xMergeStyles([
                index > 0 && styles.trLineNew,
                layout.startHorizontal,
              ])}
            >
              <View className={c2xStyles.trLeftNew}>
                {!!item.title && (
                  <Text className={c2xStyles.depositText}>{item.title}</Text>
                )}
                {!!item.subTitle && (
                  <Text className={c2xStyles.depositText}>{item.subTitle}</Text>
                )}
                {!!item.note && (
                  <Text className={c2xStyles.trLeftTextSubNew}>
                    {item.note}
                  </Text>
                )}
              </View>
              <View className={c2xStyles.trRightNew}>
                <View className={c2xStyles.depositTextWrap}>
                  {item.content.map(desc => (
                    <Text className={c2xStyles.depositText}>{desc}</Text>
                  ))}
                </View>
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default TableBoxNew;
