/**
 * 境外售后押金支付方式表格组件
 * 用于展示境外订单的押金信息,包含押金项目名称和金额
 * 支持显示押金状态(是否已免除)
 */
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './depositTableOsd.module.scss';
import { texts } from './Texts';
import { DepositItem } from '../../../Types/Dto/OSDQueryOrderType';
import Image from '../../../c2x/components/Image';

export interface IDepositInfo {
  title: string;
  items: Array<DepositItem>;
}

interface IDepositBox {
  depositItems: Array<DepositItem>;
}

const DepositTableOSD: React.FC<IDepositBox> = props => {
  const { depositItems } = props;

  if (!depositItems?.length) return null;

  return (
    // 押金表格容器
    <View className={c2xStyles.warp}>
      {/* 押金表格主体 */}
      <View className={c2xStyles.table}>
        {depositItems.map((item, itemIndex) => (
          // 每一行押金项目
          <View
            key={itemIndex}
            className={c2xStyles.trLine}
            style={xMergeStyles([
              layout.startHorizontal,
              itemIndex < depositItems.length - 1 && styles.borderBottom,
            ])}
          >
            {/* 押金项目标题列 */}
            <View className={c2xStyles.trTitle} style={layout.flexCenter}>
              <Text className={c2xStyles.tableText}>{item.depositTitle}</Text>
            </View>
            {/* 押金项目详情列 */}
            <View className={c2xStyles.trDesc}>
              {/* 押金金额和状态行 */}
              <Text style={layout.flexRow} className={c2xStyles.trAmountLine}>
                {!!item.depositDesc && (
                  <Text className={c2xStyles.tableText}>
                    {item.depositDesc}{' '}
                  </Text>
                )}
                {!!item.depositStatus && (
                  <Text className={c2xStyles.priceLabel} fontWeight="medium">
                    {item.depositStatus === 1
                      ? texts.showFree
                      : texts.retractable}
                  </Text>
                )}
              </Text>
              {/* 信用卡图标行 */}
              <View className={c2xStyles.creditImageWrap}>
                {item?.creditUrlList?.map((url, index) => (
                  <Image
                    className={c2xStyles.creditImage}
                    key={url}
                    src={url}
                  />
                ))}
              </View>
              {/* 押金说明行 */}
              <View>
                {!!item.explain && (
                  <Text className={c2xStyles.tableText}>{item.explain}</Text>
                )}
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  borderBottom: {
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
});

export default React.memo(DepositTableOSD);
