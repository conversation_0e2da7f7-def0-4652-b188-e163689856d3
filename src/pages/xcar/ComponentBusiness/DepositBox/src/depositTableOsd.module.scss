@import '../../../Common/src/Tokens/tokens/color.scss';

.warp {
  margin-top: 36px;
  border-radius: 2px;
}

.table {
  border: 1px solid $lineColor;

}

.secTitle {
  margin-bottom: 20px;
}

.desc {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  margin-bottom: 24px;
  color: $fontSecondary;
}

.trLine {
  border: 1px solid $lineColor;
}
.trTitle {
  width: 98px;
  padding: 0 25px;
}

.trDesc {
  flex: 1;
  padding: 16px 14px;
  border-left: 1px solid $lineColor;
}

.trAmountLine {
  margin-bottom: 4px;
}
.tableText {
  font-size: 24px;
  line-height: 32px;
  font-weight: normal;
  color: $fontSecondary;
  margin-top: 3px;
}
.priceLabel {
  margin-left: 12px;
  font-size: 22px;
  line-height: 32px;
  font-weight: medium;
  color: $labelGreenText;
}

.creditImageWrap {
  display: flex;
  flex-direction: row;
  margin-top: 3px;
}

.creditImage {
  width: 86px;
  height: 52px;
  margin-right: 8px;
}