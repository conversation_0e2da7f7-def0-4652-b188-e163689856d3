import Form, {
  DriverItem,
  IForm,
  PassengerOSD,
  PassengerISD,
} from './src/Form';
import FormOsd, { PassengerOSDNew } from './src/FormOsd';
import { IInputType } from './src/Type';
import Driver from './src/Driver';
import DriverNew from './src/DriverNew';

export {
  DriverItem,
  IForm,
  PassengerOSD,
  PassengerISD,
  Driver,
  DriverNew,
  IInputType,
  FormOsd,
  PassengerOSDNew,
};
export default Form;
