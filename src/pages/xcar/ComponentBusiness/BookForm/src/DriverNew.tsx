import React from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';

import { icon, font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  Certificate,
  Passenger,
  CertificateType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './DriverNew.module.scss';
import { texts, isdCertificateTypeList } from './Texts';
import { Utils } from '../../../Util/Index';
import { UITestID, ImageUrl } from '../../../Constants/Index';

const { isAndroid } = BbkUtils;

const getCertificateName = certificateObj => {
  if (!certificateObj?.certificateType) return '';
  const cerName = isdCertificateTypeList[certificateObj.certificateType];
  if (`${certificateObj.certificateType}` === '1') {
    const subValue = Utils.strFormat(`${certificateObj.certificateNo}`, 2);
    return `${cerName} ${subValue}`;
  }
  return `${cerName} ${certificateObj.certificateNo}`;
};

const getCertificateInfo = certificateObj => {
  let [name, number] = ['', ''];
  if (!certificateObj?.certificateType) return { name, number };
  name = isdCertificateTypeList[certificateObj.certificateType];
  if (certificateObj.certificateType === CertificateType.id) {
    number = Utils.strFormat(
      certificateObj?.certificateNo ? `${certificateObj.certificateNo}` : '',
      2,
    );
  } else {
    number = certificateObj.certificateNo;
  }
  return {
    name,
    number,
  };
};

const styles = StyleSheet.create({
  line: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: color.bookingOptimizationGrayBg,
  },
});

type IDriver = {
  passenger: Passenger;
  passengerIDCard: Certificate;
  noAnyPassenger?: boolean; // 是否账户里没有任何驾驶员
  isPassengerError: boolean;
  onPress?: () => void;
  isLoading?: boolean;
};

const Tip: React.FC<{ colorProp: string; text: string }> = ({
  colorProp,
  text,
}) => {
  return (
    <View className={c2xStyles.tipWrap}>
      <Text
        type="icon"
        className={c2xStyles.tipIcon}
        style={{ color: colorProp }}
      >
        {icon.circleI}
      </Text>
      <Text
        style={xMergeStyles([font.caption1LightStyle, { color: colorProp }])}
      >
        {text}
      </Text>
    </View>
  );
};

const Driver: React.FC<IDriver> = ({
  passenger,
  passengerIDCard,
  noAnyPassenger,
  isPassengerError,
  isLoading,
  onPress,
}) => {
  const isError = !passenger || !Object.keys(passenger).length;
  const subValue = getCertificateName(passengerIDCard);
  const certificateInfo = getCertificateInfo(passengerIDCard);
  const userNameImg = isAndroid
    ? `${ImageUrl.DIMG04_PATH}1tg3l12000lgb0zpdE336.png`
    : `${ImageUrl.DIMG04_PATH}1tg3f12000ldmuigsB4B0.png`;
  return (
    <View>
      <Touchable
        testID={UITestID.car_testid_page_booking_form_driver}
        debounce={true}
        onPress={onPress}
        className={c2xStyles.wrapper}
        style={styles.line}
      >
        <View className={c2xStyles.iconWrapper}>
          <Image src={userNameImg} className={c2xStyles.userNameImg} />
        </View>
        <View
          className={c2xStyles.contentWrapper}
          style={xMergeStyles([
            isPassengerError && { borderBottomColor: color.redBase },
          ])}
        >
          <View className={c2xStyles.infoRow}>
            <Text className={c2xStyles.titleText} fontWeight="medium">
              {passenger?.fullName}
            </Text>
            {certificateInfo.number && (
              <Text
                className={c2xStyles.certificateText}
                numberOfLines={1}
                fontWeight="medium"
              >
                {certificateInfo.number}
              </Text>
            )}
          </View>
          {isPassengerError && (
            <Tip
              colorProp={color.redBase}
              text={
                noAnyPassenger ? texts.drivers_addDriver : texts.emptyDriver
              }
            />
          )}
          {!isError && !subValue && !isLoading && (
            <Tip
              colorProp={color.orangeBase}
              text={texts.driver_click_noCertificate}
            />
          )}
        </View>
      </Touchable>
    </View>
  );
};
export default Driver;
