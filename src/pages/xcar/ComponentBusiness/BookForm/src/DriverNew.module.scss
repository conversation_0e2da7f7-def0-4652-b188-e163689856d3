@import '../../../Common/src/Tokens/tokens/color.scss';

.wrapper {
    padding-top: 32px;
    padding-bottom: 24px;
    display: flex;
    align-items: flex-start;
    flex-direction: row;
}

.iconWrapper {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
}

.icon {
    font-size: 36px;
    color: $recommendProposeBg;
    margin-right: 17px;
    margin-top: 1px;
}

.userNameImg {
    width: 36px;
    height: 36px;
    margin-right: 14px;
}

.contentWrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
}

.infoRow {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    width: 100%;
    row-gap: 14px;
    column-gap: 12px;
}

.titleText {
    font-size: 30px;
    line-height: 40px;
    font-family: PingFangSC-Medium;
    color: $recommendProposeBg;
}

.certificateText {
    font-size: 30px;
    line-height: 40px;
    font-family: TRIPNUMBER-MEDIUM;
    color: $recommendProposeBg;
}

.tipWrap {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 8px;
}

.tipIcon {
    font-size: 26px;
    margin-right: 8px;
    color: $recommendProposeBg;
}
