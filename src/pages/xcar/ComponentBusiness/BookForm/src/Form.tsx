import { debounce as lodashDebounce } from 'lodash-es';
import React, { Component, RefObject, memo } from 'react';
import {
  XView as View,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';

import Input from '@ctrip/rn_com_car/dist/src/Components/Basic/Input';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import MobileInput, {
  ICountryCode,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/MobileInput';
import { useNameReverse } from '@ctrip/rn_com_car/dist/src/Logic/src/Hooks/NameReverse';
import {
  Certificate,
  InputFormatType,
  Passenger as PassengerType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import { icon, color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { ensureFunctionCall } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { texts, isdCertificateTypeList } from './Texts';
import MobileInputNew from './MobileInput';
import { IInputType } from './Type';
import Driver from './Driver';
import { UITestID } from '../../../Constants/Index';
import {
  asMobileList,
  ContactType,
  LocalContactInfoType,
} from '../../../Constants/LocalContactsData';
import { CarLog } from '../../../Util/Index';
import c2xStyles from './formC2xStyles.module.scss';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const {
  isEmail,
  getPixel,
  reserveLetterAndWhiteSpaceOnly,
  removeCharacters,
  isMobile,
} = BbkUtils;
const noop = () => {};

// 静态参数
const getDrivers = () => [
  {
    type: IInputType.firstName,
    placeholder: texts.firstName_placeholder,
    errorTip: texts.firstName_errorTip,
    title: texts.firstName_title,
    autoCapitalize: 'characters',
  },
  {
    type: IInputType.lastName,
    placeholder: texts.lastName_placeholder,
    errorTip: texts.lastName_errorTip,
    title: texts.lastName_title,
    autoCapitalize: 'characters',
  },
  {
    type: IInputType.age,
    title: texts.age,
    editable: false,
  },
  {
    type: IInputType.idType,
    title: 'ID Type',
    editable: false,
  },
  {
    type: IInputType.idNumber,
    title: 'ID Number',
    keyboardType: 'numeric',
  },
  {
    type: IInputType.email,
    placeholder: texts.email_placeholder,
    errorTip: texts.email_errorTip,
    title: texts.email_title,
    autoComplete: 'email',
    keyboardType: 'email-address',
    association: true,
  },
  {
    type: IInputType.mobilePhone,
    placeholder: texts.mobile_placeholder,
    errorTip: texts.mobile_error,
    title: texts.mobile_title,
  },
  {
    type: IInputType.flightNumber,
    placeholder: texts.flight_placeholder,
    errorTip: texts.flight_errorTip,
    title: texts.flight_title,
    formatType: 'flight',
  },
  {
    type: IInputType.wechat,
    placeholder: texts.wechat_placeholder,
    errorTip: texts.wechat_errorTip,
    title: texts.wechat_title,
  },
  {
    type: IInputType.localContacts,
    placeholder: '账号（选填）',
    errorTip: '',
    title: '账号（选填）',
    defaultPlaceholder: '账号（选填）',
  },
];

const useDriver = (
  isId: boolean,
  isWechat: boolean,
  isFlightNumber: boolean,
  hasLocalContacts: boolean,
) => {
  const isNameReverse = useNameReverse();
  const ids = isId ? [IInputType.idType, IInputType.idNumber] : [];
  const wechats = isWechat ? [IInputType.wechat] : [];
  const first = IInputType.firstName;
  const last = IInputType.lastName;
  const { age } = IInputType;
  const flight = isFlightNumber ? [IInputType.flightNumber] : [];
  const localContacts = hasLocalContacts ? [IInputType.localContacts] : [];
  const others = [
    IInputType.mobilePhone,
    IInputType.email,
    ...wechats,
    ...localContacts,
    ...flight,
  ];

  return others;
};

// 文本处理
const inputValidKeydown = (v, name) => {
  let value = v;
  if (!value) return value;
  if (name == IInputType.firstName || name == IInputType.lastName) {
    value = reserveLetterAndWhiteSpaceOnly(value);
  }
  value = removeCharacters(value);
  return value;
};

// 表单校验
export const validForms = (type, data, needFlightNo, isCNMobile) => {
  let isValid = true;
  switch (type) {
    case IInputType.email:
      isValid = isEmail(data);
      break;
    case IInputType.mobilePhone:
      if (isCNMobile) {
        const newValue = data && data.replace(/\s+/g, '');
        isValid = isMobile(newValue);
      }
      break;
    case IInputType.flightNumber:
      if (needFlightNo && !data) {
        isValid = false;
      }
      break;
    case IInputType.localContacts:
      isValid = true;
      break;
    default:
      if (!data) isValid = false;
      break;
  }
  return isValid;
};

export interface DriverItem {
  type: string;
  value?: string;
  error?: boolean;
  isEnable?: boolean;
  contactType?: ContactType;
  contactTypeName?: string;
}

export interface IForm {
  changeFormData?: (data: DriverItem[]) => void;
  needFlightNo?: boolean; // 是否必填航班号
  ageInfoTip?: string;
  flightInfoTip?: string;
  flightErrorTip?: string;
  driverInfo?: DriverItem[];
  minDriverAge?: number;
  maxDriverAge?: number;
  handleAgeChange?: (age) => void;
  haneleCancelAge?: () => void;
  setFlightErrorTip?: (data: string) => void;
  validateFlightNo?: (data: { type: string; flightNo: string }) => void;
  isLoading?: boolean;
  isSpecialAnimation?: boolean;
  isId?: boolean;
  isWechat?: boolean;
  isIsd?: boolean;
  isPressFlightInfo?: boolean;
  isNewStyle?: boolean;
  sequences?: IInputType[]; // 决定input个数和顺序
  currentCountry?: ICountryCode; // CN需要
  theme?: any;
  isAirportStore?: boolean; // 是否机场店
  passengerInfo?: PassengerType;
  onPressIdType?: () => void;
  onPressAge?: () => void;
  onPressAreaCode?: () => void;
  onPressContact?: () => void;
  onPressLocalContacts?: () => void;
  renderPassenger?: React.ReactElement;
  onPressFlightInfo?: () => void;
  inputRefHandler?: (ref: RefObject<any>) => void;
  hasLocalContacts?: boolean;
  localContactsInputFocus?: (isfocus: boolean) => void;
  localContactsData?: LocalContactInfoType[];
  changeLocalContactsData?: (data: LocalContactInfoType[]) => void;
  showFlightNo?: boolean;
}
const Form: React.FC<IForm> = memo(
  ({
    driverInfo: data,
    minDriverAge,
    maxDriverAge,
    needFlightNo, // 是否航班号必填
    ageInfoTip,
    flightErrorTip,
    flightInfoTip,
    changeFormData,
    onPressIdType,
    onPressAreaCode,
    onPressContact,
    sequences,
    isLoading = false,
    isId = false,
    isIsd,
    isWechat = false,
    currentCountry,
    onPressFlightInfo,
    isSpecialAnimation,
    renderPassenger,
    isPressFlightInfo,
    inputRefHandler,
    isNewStyle,
    isAirportStore, // 是否是机场门店
    validateFlightNo,
    setFlightErrorTip,
    hasLocalContacts,
    onPressLocalContacts,
    localContactsInputFocus,
    localContactsData,
    changeLocalContactsData,
    showFlightNo,
  }) => {
    let sequence = sequences;
    if (!sequence)
      sequence = useDriver(isId, isWechat, showFlightNo, hasLocalContacts);
    const optionStr = `(${texts.optional})`;
    const areaCodeData = data.find(v => v.type === 'areaCode');
    const isCNMobile = areaCodeData?.value === '86';
    return (
      <>
        {renderPassenger}
        {sequence.map(type => (
          <InputItem
            isNewStyle={isNewStyle}
            inputRefHandler={inputRefHandler}
            key={type}
            currentCountry={currentCountry}
            areaCodeData={areaCodeData}
            item={data.find(v => v.type === type)}
            isCNMobile={isCNMobile}
            changeFormData={changeFormData}
            needFlightNo={needFlightNo}
            minDriverAge={minDriverAge}
            maxDriverAge={maxDriverAge}
            ageInfoTip={ageInfoTip}
            flightErrorTip={flightErrorTip}
            isPressFlightInfo={isPressFlightInfo}
            onPressFlightInfo={onPressFlightInfo}
            flightInfoTip={flightInfoTip}
            onPressIdType={onPressIdType}
            onPressAreaCode={onPressAreaCode}
            onPressContact={onPressContact}
            optionStr={optionStr}
            isIsd={isIsd}
            isSpecialAnimation={isSpecialAnimation}
            isAirportStore={isAirportStore}
            validateFlightNo={validateFlightNo}
            setFlightErrorTip={setFlightErrorTip}
            onPressLocalContacts={onPressLocalContacts}
            localContactsInputFocus={localContactsInputFocus}
            localContactsData={localContactsData}
            changeLocalContactsData={changeLocalContactsData}
          />
        ))}
      </>
    );
  },
);

interface IInputItem {
  item: any;
  areaCodeData: DriverItem;
  currentCountry: ICountryCode;
  changeFormData: (data: DriverItem[]) => void;
  needFlightNo: boolean;
  isSpecialAnimation: boolean;
  isCNMobile: boolean;
  minDriverAge: number;
  maxDriverAge: number;
  ageInfoTip: string;
  flightErrorTip: string;
  flightInfoTip: string;
  optionStr: string;
  setFlightErrorTip: (data: string) => void;
  validateFlightNo: (data: {
    type: string;
    flightNo: string;
    callbackFun: (res) => void;
  }) => void;
  handlePressAge?: () => void;
  onPressIdType?: () => void;
  onPressAreaCode?: () => void;
  onPressContact?: () => void;
  onPressFlightInfo?: () => void;
  onPressLocalContacts?: () => void;
  isIsd?: boolean;
  isPressFlightInfo?: boolean;
  isNewStyle?: boolean;
  isAirportStore?: boolean; // 是否机场店validateFlightNoCallBack
  inputRefHandler?: (ref: RefObject<any>) => void;
  localContactsInputFocus?: (isfocus: boolean) => void;
  localContactsData?: LocalContactInfoType[];
  changeLocalContactsData?: (data: LocalContactInfoType[]) => void;
}
interface IInputItemState {
  inputValue?: string;
  inputError?: boolean;
  isValidateFlightNumber?: boolean;
  flightValidateError?: string; // 航班号校验结果
  flightValidateValue?: string; // 校验的航班号
}

class InputItem extends Component<IInputItem, IInputItemState> {
  debouncedSearch: Function;

  isInputFlightNumber: boolean;

  constructor(props) {
    super(props);
    this.onChangeText = this.onChangeText.bind(this);
    this.onBlurEven = this.onBlurEven.bind(this);
    this.onEndEditing = this.onEndEditing.bind(this);
    this.onLocalContactsInputFocus = this.onLocalContactsInputFocus.bind(this);
    this.onLocalContactsBlurEven = this.onLocalContactsBlurEven.bind(this);
    this.debouncedSearch = lodashDebounce(this.updateFormData, 500);
    this.isInputFlightNumber = false;
    this.state = {
      inputValue: props?.item?.value || '',
      inputError: props?.item?.error || false,
      isValidateFlightNumber: false,
      flightValidateError: '',
      flightValidateValue: '',
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { item } = nextProps;
    const { value, error, type } = item || {};
    const { inputValue, inputError } = this.state;
    // 航班号输入框防抖状态中，不进行Reducer中的数据同步
    if (this.isInputFlightNumber) {
      return;
    }
    if (inputValue !== value || inputError !== error) {
      this.updateInput(value, error);
    }
  }

  updateInput(value, error) {
    const { inputValue, inputError } = this.state;
    if (value !== inputValue || error !== inputError) {
      this.setState({
        inputValue: value,
        inputError: error,
        isValidateFlightNumber: false,
        flightValidateError: '',
        flightValidateValue: '',
      });
    }
  }

  // 内容变更
  onChangeText(text: string) {
    const { inputValue } = this.state;
    if (text === inputValue) {
      return;
    }
    const {
      item,
      needFlightNo,
      isCNMobile,
      isAirportStore,
      localContactsData,
      changeLocalContactsData,
    } = this.props;
    const value = inputValidKeydown(text, item.type);
    const valid = validForms(item.type, value, needFlightNo, isCNMobile);
    this.updateInput(value, !valid);

    if (item.type === IInputType.localContacts && localContactsData?.length) {
      let contactType = -1;
      const curlocalContactsData = localContactsData?.map(info => {
        if (info.isSelected) {
          contactType = info.contactType;
        }
        return {
          ...info,
          data: info.isSelected ? value : info.data,
        };
      });
      CarLog.LogCode({
        name: '点击_填写页_其他联系方式_输入',

        info: {
          uidTypeSelected: contactType,
        },
      });
      this.debouncedSearch([
        {
          type: item.type,
          value,
          error: !valid,
          localContactsData: curlocalContactsData,
        },
      ]);
    } else {
      this.debouncedSearch([{ type: item.type, value, error: !valid }]);
    }
  }

  onLocalContactsInputFocus = () => {
    const { item } = this.props;
    const { localContactsInputFocus } = this.props;
    if (asMobileList.includes(item.contactType)) {
      ensureFunctionCall(localContactsInputFocus(true));
    }
  };

  // 失去焦点
  onBlurEven() {
    const { item, needFlightNo, isCNMobile } = this.props;
    const { value, error } = item || {};
    const { inputValue } = this.state;
    const valid = validForms(item.type, inputValue, needFlightNo, isCNMobile);
    // reducer中的 值或者校验结果 与当前组件中的 值或者校验结果 不一致时更新
    if (
      item.type !== IInputType.localContacts &&
      (value !== inputValue || !!error !== !valid)
    ) {
      this.debouncedSearch([
        { value: inputValue, type: item.type, error: !valid },
      ]);
    }
  }

  onLocalContactsBlurEven() {
    const { localContactsInputFocus } = this.props;
    ensureFunctionCall(localContactsInputFocus(false));
    this.onBlurEven();
  }

  // 航班号为必填 或者 取车门店为机场店且有填写航班号时 才进行校验
  needValidateFlightNo = inputValue => {
    const { needFlightNo, isAirportStore } = this.props;
    return needFlightNo || (!needFlightNo && isAirportStore && inputValue);
  };

  // 前端航班号基础校验
  baseValidateFlightNo = inputValue => {
    let isError = !!inputValue;
    // 如果输入了航班号，或者航班号没有输入且航班号是必填项时，前端校验失败
    const { needFlightNo } = this.props;
    if (needFlightNo && !inputValue) {
      isError = true;
    }
    return isError;
  };

  onFlightNoChangeText = (text: string) => {
    const { item } = this.props;
    const { inputValue } = this.state;
    if (text === inputValue) {
      return;
    }
    const value = inputValidKeydown(text, item.type);
    const isError = this.baseValidateFlightNo(value);
    this.isInputFlightNumber = true;
    // 输入框为空时需要同步当前校验状态到Reducer的场景
    // 1、前端必填为空校验失败
    // 2、前端非必填为空校验成功
    this.updateInput(value?.toUpperCase(), isError);
    this.debouncedSearch([
      { type: item.type, value: value?.toUpperCase(), error: isError },
    ]);
  };

  validateFlightNoCallBack = res => {
    const { setFlightErrorTip = noop, changeFormData = noop } = this.props;
    const { errorMessage, type, error, value } = res;
    this.setState({
      inputError: error,
      isValidateFlightNumber: false,
      flightValidateError: errorMessage,
      flightValidateValue: value,
    });
    // 航班号校验成功后，为了快速展示给用户校验结果 ，采用异步方式同步到Reducer
    setTimeout(() => {
      if (error) {
        setFlightErrorTip(errorMessage);
      }
      // 航班号校验结束，立即更新表单数据
      changeFormData([{ type, error, value }]);
    }, 0);
  };

  // 航班号失去焦点
  onFlightNoBlurEven = () => {
    const { item, validateFlightNo } = this.props;
    const { inputValue = '' } = this.state;
    const isError = this.baseValidateFlightNo(inputValue);
    this.updateInput(inputValue.toUpperCase(), isError);
    // 失去焦点，判断当前航班号是否输入有错误，有错误的话说明需要服务端校验航班号
    if (isError && !!inputValue) {
      this.setState({
        isValidateFlightNumber: true,
      });
      // 校验航班号有效性
      validateFlightNo({
        type: item.type,
        flightNo: inputValue.toUpperCase(),
        callbackFun: this.validateFlightNoCallBack,
      });
    }
  };

  // 输入结束
  onEndEditing() {
    const { item } = this.props;
    if (item.type === IInputType.email) return;
    const { inputValue, inputError } = this.state;
    // 存在服务端校验时会导致组件中的错误状态与Reducer中的错误状态不一致
    if (item.type !== IInputType.flightNumber) {
      this.updateInput(inputValue.toUpperCase(), inputError);
      this.debouncedSearch([{ ...item, value: inputValue.toUpperCase() }]);
    }
  }

  updateFormData = data => {
    this.isInputFlightNumber = false;
    const { changeFormData = noop } = this.props;
    const { flightValidateValue } = this.state;
    // 航班号输入防抖特殊处理
    // 解决用户输入航班号后更新Reducer的error状态为True，航班号校验结果更新Reducer的error状态为false
    // 两者执行顺序异常导致服务端校验正确的航班号被防抖更新error状态为true覆盖
    // 如果需要更新的内容，服务端校验已经结束，则不进行更新Reducer
    const flightInfo = data.find(item => item.type === IInputType.flightNumber);
    if (flightInfo?.value === flightValidateValue && !!flightValidateValue) {
      return;
    }
    changeFormData(data);
  };

  getLocalContactsInputName = prop => {
    const { contactTypeName, contactType } = prop || {};
    return contactType === ContactType.localPhone &&
      !contactTypeName?.startsWith('+')
      ? `+${contactTypeName}`
      : contactTypeName;
  };

  getContactInputTitle = () => {
    const { localContactsData } = this.props;
    if (!localContactsData?.length) {
      return '账号（选填）';
    }
    const data = localContactsData?.find(info => info.isSelected);
    if (data?.contactType === ContactType.localPhone) {
      return '当地电话（选填）';
    }
    return '账号（选填）';
  };

  render() {
    const {
      item,
      areaCodeData,
      currentCountry,
      changeFormData,
      needFlightNo,
      minDriverAge,
      maxDriverAge,
      ageInfoTip,
      flightInfoTip,
      handlePressAge,
      onPressIdType,
      onPressAreaCode,
      onPressContact,
      optionStr,
      flightErrorTip,
      onPressFlightInfo,
      isSpecialAnimation,
      isCNMobile,
      isPressFlightInfo,
      isIsd,
      isNewStyle,
      inputRefHandler,
      onPressLocalContacts,
    } = this.props;
    const {
      inputValue,
      inputError,
      isValidateFlightNumber,
      flightValidateError,
    } = this.state;
    if (!item) return null;
    const dataStatic = getDrivers().find(v => v.type === item.type) || {};
    const prop = { ...item, ...dataStatic, isSpecialAnimation };
    const StyledMobileInput = isNewStyle ? MobileInputNew : MobileInput;

    switch (item.type) {
      case IInputType.age:
        return (
          <Input
            {...prop}
            testID={UITestID.car_testid_page_booking_form_age_input}
            value={texts.ageYear(prop.value)}
            errorTip={texts.ageBetween(minDriverAge, maxDriverAge)}
            onPress={handlePressAge}
            infoTip={ageInfoTip}
          />
        );

      case IInputType.idType:
        return (
          <Input
            {...prop}
            testID={UITestID.car_testid_page_booking_form_idtype_input}
            onPress={onPressIdType}
          />
        );

      case IInputType.mobilePhone:
        return (
          <StyledMobileInput
            {...prop}
            value={inputValue}
            error={inputError}
            isLeftChildren={!isIsd}
            isValueSpit={isCNMobile}
            currentCountry={currentCountry}
            onPressAreaCode={onPressAreaCode}
            onPressContact={onPressContact}
            onChangeText={this.onChangeText}
            onInputBlur={this.onBlurEven}
            onAreaCodeChange={(value: ICountryCode) => {
              // 用户切换AreaCode，立刻重新验证区号
              const reCheckCNMobile = value?.countryCode === '86';
              const valid = validForms(
                item.type,
                inputValue,
                needFlightNo,
                reCheckCNMobile,
              );
              // 立刻检查并更新input框的输入valid验证提示
              this.updateInput(inputValue, !valid);
              // 用户提交表单时，对手机号的验证处于data的mobilePhone字段中，需要同步更新布尔值
              changeFormData([
                {
                  type: 'areaCode',
                  value: value.countryCode,
                },
                {
                  type: 'mobilePhone',
                  error: !valid,
                },
              ]);
            }}
            testID={UITestID.car_testid_page_booking_form_mobile_input}
            leftTestID={
              UITestID.car_testid_page_booking_form_mobile_input_areacode
            }
            onContactChange={(value: string) =>
              changeFormData([
                { type: IInputType.mobilePhone, value, error: false },
              ])
            }
            areaCode={areaCodeData && areaCodeData.value}
            inputRefHandler={inputRefHandler}
          />
        );

      case IInputType.localContacts:
        return (
          <XViewExposure
            testID={CarLog.LogExposure({
              name: '曝光_填写页_其他联系方式',

              info: {
                ifHasDefault: prop.value,
                uidTypeSelected: prop.contactType,
              },
            })}
          >
            <MobileInput
              {...prop}
              title={this.getContactInputTitle()}
              value={inputValue}
              error={inputError}
              isLeftChildren={!isIsd}
              isValueSpit={isCNMobile}
              currentCountry={currentCountry}
              onPressAreaCode={onPressLocalContacts}
              onPressContact={onPressContact}
              onInputFocus={this.onLocalContactsInputFocus}
              onChangeText={this.onChangeText}
              onInputBlur={this.onLocalContactsBlurEven}
              testID={UITestID.car_testid_page_booking_form_mobile_input}
              leftTestID={
                UITestID.car_testid_page_booking_form_mobile_input_areacode
              }
              areaCode={this.getLocalContactsInputName(prop)}
              inputRefHandler={inputRefHandler}
              formatType={InputFormatType.default}
            />
            <Text className={c2xStyles.localContactsTip}>
              便于门店联系，下单后可修改
            </Text>
          </XViewExposure>
        );

      case IInputType.flightNumber:
        // 航班号服务端校验结果优先级最高-> 服务端校验结果(下单等校验)提示 -> 组件默认非空校验提示
        const reducerError = inputValue
          ? flightErrorTip || prop?.errorTip
          : prop?.errorTip;
        let disPlayErrorTip = flightValidateError || reducerError;
        let disPlayError = inputError;
        // 如果是校验航班号过程中或者非空错误提示，则输入框有值则错误提示不展示
        if (
          isValidateFlightNumber ||
          (disPlayErrorTip === texts.flight_errorTip && !!inputValue)
        ) {
          disPlayErrorTip = '';
          disPlayError = false;
        }
        return (
          <>
            <Input
              {...prop}
              testID={UITestID.car_testid_page_booking_form_flightnumber_input}
              value={inputValue}
              error={disPlayError}
              title={`${prop.title} ${needFlightNo ? '' : optionStr}`}
              onChangeText={this.onFlightNoChangeText}
              onInputBlur={this.onFlightNoBlurEven}
              errorTip={disPlayErrorTip}
              onEndEditing={this.onEndEditing}
            />

            <View className={c2xStyles.flightTipWrap}>
              <Text type="icon" className={c2xStyles.flightTipIcon}>
                {icon.circleI}
              </Text>
              <Text className={c2xStyles.flightTipText}>
                {texts.flightNoTip}
              </Text>
            </View>
            {!!flightInfoTip && (
              <Touchable
                className={c2xStyles.tipWrap}
                disabled={!isPressFlightInfo}
                testID={UITestID.car_testid_page_booking_form_flightnumber_info}
                onPress={isPressFlightInfo && onPressFlightInfo}
              >
                <Text
                  className={classNames(
                    c2xCommonStyles.c2xTextDefaultCss,
                    isPressFlightInfo && c2xStyles.blue,
                  )}
                >
                  {flightInfoTip}
                  <View className={c2xStyles.tipsIconWrap}>
                    <Text
                      type="icon"
                      className={classNames(
                        c2xStyles.tipsIcon,
                        isPressFlightInfo && c2xStyles.blue,
                      )}
                    >
                      {icon.arrowRight}
                    </Text>
                  </View>
                </Text>
              </Touchable>
            )}
          </>
        );

      default:
        return (
          <Input
            {...prop}
            value={inputValue}
            error={inputError}
            testID={`${UITestID.car_testid_page_booking_form_input_item}_${prop?.type}`}
            onChangeText={this.onChangeText}
            onInputBlur={this.onBlurEven}
            onEndEditing={this.onEndEditing}
          />
        );
    }
  }
}

interface IPassenger {
  onPress?: () => void;
  value: string;
  label?: string;
  subValue: string;
  infoTip?: string;
  noEditableValueRight?: React.ReactElement;
}
export const Passenger: React.FC<IPassenger> = ({
  noEditableValueRight,
  value,
  label,
  subValue,
  infoTip,
  onPress,
}) => (
  // @ts-ignore
  <Input
    title={texts.driverTitle}
    value={value}
    label={label}
    noEditableValueRight={noEditableValueRight}
    subValue={subValue}
    onPress={onPress}
    testID={UITestID.car_testid_page_booking_form_driver}
    rightChildren={
      <Text
        type="icon"
        style={{ fontSize: getPixel(40), color: color.C_006FF6 }}
        testID={UITestID.car_testid_book_osd_driver}
      >
        {icon.user}
      </Text>
    }
    isArrowRight={false}
    editable={false}
    infoTip={infoTip}
    valueStyle={font.title2MediumStyle}
  />
);

interface PassengerISD {
  passenger: PassengerType;
  isCreditQualified?: boolean;
  isNewPassenger?: boolean;
  noAnyPassenger?: boolean;
  passengerError?: boolean;
  onPress?: () => void;
  curCertificates?: any;
  availableCertificates?: Array<string>;
  label?: React.ReactElement;
  passengerIDCard?: Certificate;
}
export const PassengerISD: React.FC<PassengerISD> = ({
  passenger = {},
  onPress,
  isNewPassenger,
  noAnyPassenger,
  passengerIDCard,
  passengerError,
  curCertificates = {},
  availableCertificates,
  label,
}) => {
  if (isNewPassenger) {
    return (
      <Driver
        isPassengerError={passengerError}
        passenger={passenger}
        passengerIDCard={passengerIDCard}
        noAnyPassenger={noAnyPassenger}
        label={label}
        onPress={onPress}
      />
    );
  }
  let subValue = '';
  // TODO 填写页的驾驶员展示除了跟上次编辑的驾驶员相关 还与供应商支持的证件类型有关
  const { certificateList = [], passengerId = '' } = passenger;
  const currType = curCertificates[passengerId];
  let certificateObj;
  certificateObj = certificateList.find(citem =>
    availableCertificates.includes(citem.certificateType),
  );
  if (availableCertificates.includes(currType)) {
    certificateObj = certificateList.find(
      item => item.certificateType === currType,
    );
  }
  if (certificateObj && certificateObj.certificateNo) {
    subValue = `${isdCertificateTypeList[certificateObj.certificateType]} ${
      certificateObj.certificateNo
    }`;
  }
  return (
    <Passenger
      onPress={onPress}
      subValue={subValue}
      value={passenger && passenger.fullName}
      noEditableValueRight={label}
    />
  );
};

interface PassengerOSD {
  passenger: PassengerType;
  label?: string;
  onPress?: () => void;
  infoTip?: string;
}
export const PassengerOSD: React.FC<PassengerOSD> = ({
  passenger,
  label,
  infoTip,
  onPress,
}) => {
  let nameValue = '';
  if (passenger && passenger.lastName && passenger.firstName) {
    const { lastName = '', firstName = '' } = passenger;
    nameValue = `${lastName}/${firstName}`;
  }
  return (
    <Passenger
      onPress={onPress}
      infoTip={infoTip}
      subValue={passenger && passenger.age && texts.ageYear(passenger.age)}
      value={nameValue}
      label={label}
    />
  );
};
export default Form;
