/* eslint-disable @typescript-eslint/no-use-before-define */
import StyleSheet from '@c2x/apis/StyleSheet';
import Platform from '@c2x/apis/Platform';
import Device from '@c2x/apis/Device';
import Image from '@c2x/components/Image';
import React, { CSSProperties, useMemo } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text/index';
import Input from '@ctrip/rn_com_car/dist/src/Components/Basic/Input/index';
import { color, icon, font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { InputFormatType } from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import c2xStyles from './mobileInputC2xStyles.module.scss';
import { GetABCache, Utils } from '../../../Util/Index';
import { ImageUrl } from '../../../Constants/Index';

const { getPixel, selector, getLineHeight, isAndroid } = BbkUtils;

const isISDShelves3 = GetABCache.isISDShelves3();

const BLACK_LIST = ['xiaomi'];
export interface IInputNew {
  onChangeText?: (texts: string) => void;
  style?: CSSProperties;
  title?: string;
  placeholder?: string;
  error?: boolean;
  errorTip?: string;
  [prop: string]: any;
}
export const InputNew: React.FC<IInputNew> = ({
  onChangeText,
  title,
  placeholder,
  error,
  errorTip,
  ...defautProps
}) => {
  const mobileImg = isAndroid
    ? `${ImageUrl.DIMG04_PATH}1tg3112000lgb0avrDEF8.png`
    : `${ImageUrl.DIMG04_PATH}1tg2012000ldmuw6jAD33.png`;

  const isInBlackList = useMemo(() => {
    const manufacturer = Device?.deviceManufacturer?.toLowerCase() || '';
    return BLACK_LIST.includes(manufacturer);
  }, []);
  return (
    <View
      style={xMergeStyles([
        layout.flex1,
        layout.rowStart,
        Utils.isCtripIsd() ? styles.fixWrapper : styles.wrapper,
        error && styles.errorLine,
      ])}
    >
      {!Utils.isCtripIsd() && (
        <View className={c2xStyles.title}>
          <Text style={font.title3LightStyle}>{title}</Text>
        </View>
      )}
      {isISDShelves3 && (
        <View
          className={classNames(
            c2xStyles.iconWrapper,
            isInBlackList && c2xStyles.mt5,
          )}
        >
          <Image src={mobileImg} className={c2xStyles.mobileImg} />
        </View>
      )}
      <View style={xMergeStyles([layout.flex1, layout.justifyCenter])}>
        {/* @ts-ignore */}
        <Input
          inputStyle={
            defautProps.value
              ? styles.coveredInputStyle
              : styles.coveredInputStyleLight
          }
          isShowTitle={false}
          onChangeText={onChangeText}
          error={false}
          style={styles.coveredWrapper}
          formatType={InputFormatType.phone}
          inputWrapperStyle={styles.coveredInputWrapper}
          isValueSpit={true}
          leftChildren={null}
          rightChildren={null}
          placeholderTextColor={
            isISDShelves3 ? color.addMoreDriver : color.darkGrayBorder
          }
          placeholder={placeholder}
          title={placeholder}
          textStyle={
            Utils.isCtripIsd()
              ? defautProps?.value?.length > 0
                ? styles.bookingOptimizationText
                : styles.bookingOptimizationPlaceHolder
              : null
          }
          {...defautProps}
        />

        {selector(
          error,
          <View style={layout.flexRow}>
            {!isISDShelves3 && (
              <Text type="icon" className={c2xStyles.tipIcon}>
                {icon.circleI}
              </Text>
            )}
            <Text
              style={xMergeStyles([
                font.caption1LightStyle,
                { color: color.redBase },
              ])}
            >
              {errorTip}
            </Text>
          </View>,
        )}
      </View>
    </View>
  );
};

export interface IMobileItem {
  onContactChange?: (value: string) => void;
  onChangeText?: (texts: string) => void;
  onPressContact?: () => void;
}
const MobileItem: React.FC<IMobileItem> = ({
  onChangeText,
  ...defautProps
}) => {
  return (
    <View
      style={xMergeStyles([
        layout.rowStart,
        Utils.isCtripIsd() &&
          !isISDShelves3 &&
          styles.bookingOptimizationWrapper,
      ])}
    >
      <InputNew {...defautProps} onChangeText={onChangeText} />
    </View>
  );
};
export default MobileItem;
const styles = StyleSheet.create({
  wrapper: {
    borderBottomColor: color.blueGrayBg,
    borderBottomWidth: StyleSheet.hairlineWidth,
    paddingTop: getPixel(28),
    paddingBottom: getPixel(28),
  },
  fixWrapper: { paddingTop: getPixel(24), paddingBottom: getPixel(24) },
  errorLine: {
    borderBottomColor: color.redBase,
  },
  coveredWrapper: {
    ...layout.flex1,
    marginTop: 0,
  },
  coveredInputStyle: {
    marginBottom: 0,
    ...font.title3MediumStyle,
    ...Platform.select({
      ios: { lineHeight: getLineHeight(0) },
      android: {},
      // @ts-ignore
      harmony: {},
      web: {},
    }),
  },
  coveredInputStyleLight: {
    marginBottom: 0,
    ...font.title3LightStyle,
  },
  coveredInputWrapper: {
    paddingTop: 0,
    marginTop: getPixel(0),
    paddingBottom: getPixel(0),
    borderBottomColor: color.transparent,
    borderBottomWidth: 0,
  },
  bookingOptimizationWrapper: {
    borderBottomColor: color.rentalDateDurationBg,
    borderBottomWidth: getPixel(1),
  },
  bookingOptimizationText: {
    ...font.subTitle1BoldStyle,
    color: color.recommendProposeBg,
    marginTop: getPixel(2),
  },
  bookingOptimizationPlaceHolder: {
    ...font.subTitle1RegularStyle,
  },
});
