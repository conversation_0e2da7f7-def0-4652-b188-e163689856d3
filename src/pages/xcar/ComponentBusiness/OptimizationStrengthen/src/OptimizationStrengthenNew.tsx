import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xMergeStyles,
} from '@ctrip/xtaro';

import { font, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './optimizationStrengthenNewC2xStyles.module.scss';
import TextIncludeSoldOut from '../../../Pages/VendorList/Components/TextIncludeSoldOut';
import { Utils } from '../../../Util/Index';
import Texts from './Texts';
import { IOptimizationStrengthen } from './Types';
import { ImageUrl, UITestID } from '../../../Constants/Index';

const getMaxByteStr = (str: string, maxByteLength: number) => {
  if (Utils.getByteLength(str) <= maxByteLength) return str;
  return `${Utils.getByteLengthStr(str, maxByteLength - 2)}...`;
};

const { getPixel, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  titleWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  titleWrapNoFlex: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  vendorNameNew: {
    color: color.C_111111,
    ...font.F_30_10_medium,
    marginTop: getPixel(isAndroid ? -2 : 2),
  },
});

const OptimizationStrengthen: React.FC<IOptimizationStrengthen> = ({
  vendorName,
  isShowRightArrow = true,
  isShowSubTitle = false,
  wrapStyle,
  titleWrapStyle,
  imgStyle,
  dotStyle,
  vendorNameStyle,
  arrowRightStyle,
  onPress,
  theme,
  imgModalUrl,
  vendorNameMaxByte = 20,
  onPressVendor,
  fromPage,
}) => {
  if (!vendorName) return null;
  const { soldOutTextColor } = theme;
  const carYouxuanBg = `${ImageUrl.DIMG04_PATH}1tg1r12000cyui9660F4A.png`;
  const carYouxuanGrayBg = `${ImageUrl.DIMG04_PATH}1tg4g12000cyuie0v8284.png`;
  const imguri = imgModalUrl || carYouxuanBg;
  const logoImg = (
    <Image
      source={{ uri: soldOutTextColor ? carYouxuanGrayBg : imguri }}
      resizeMode="contain"
      testID={UITestID.car_testid_comp_vendor_modal_title_logo}
      className={c2xStyles.optimizationImgNew}
      style={imgStyle}
    />
  );
  const logo = onPress ? (
    <BbkTouchable
      testID={UITestID.car_testid_comp_vendor_modal_title}
      onPress={onPress}
    >
      {logoImg}
    </BbkTouchable>
  ) : (
    logoImg
  );
  const titleDom = (
    <BbkTouchable
      style={xMergeStyles([
        fromPage === 'book' ? styles.titleWrapNoFlex : styles.titleWrap,
        titleWrapStyle,
      ])}
      testID={UITestID.car_testid_optimal_selection}
      onPress={onPressVendor}
    >
      {logo}
      <View
        className={c2xStyles.dot}
        style={xMergeStyles([
          dotStyle,
          {
            backgroundColor:
              soldOutTextColor || color.optimizationStrengthenDot,
          },
        ])}
      />

      <TextIncludeSoldOut
        style={xMergeStyles([styles.vendorNameNew, vendorNameStyle])}
        text={getMaxByteStr(vendorName, vendorNameMaxByte)}
      />

      {!soldOutTextColor && isShowRightArrow && (
        <BbkText
          type="icon"
          className={c2xStyles.arrowRightNew}
          style={arrowRightStyle}
        >
          {icon.right_arrow}
        </BbkText>
      )}
    </BbkTouchable>
  );
  if (!isShowSubTitle) return titleDom;
  return (
    <LinearGradient
      className={c2xStyles.wrap}
      style={wrapStyle}
      start={{ x: 0.5, y: 0.0 }}
      end={{ x: 0.5, y: 1.0 }}
      locations={[0, 1]}
      colors={[color.optimizationStrengthenGradientStart, color.white]}
    >
      {titleDom}
      <BbkTouchable
        onPress={onPress || Utils.noop}
        testID={UITestID.car_testid_optimizationstreng_detail}
      >
        <View className={c2xStyles.subTitleWrap}>
          <BbkText className={c2xStyles.subTitleText}>
            {Texts.optimizationStrengthenDesc}
          </BbkText>
          <BbkText type="icon" className={c2xStyles.subTitleArrowRight}>
            {icon.right_arrow}
          </BbkText>
        </View>
      </BbkTouchable>
    </LinearGradient>
  );
};
export default withTheme(OptimizationStrengthen);
