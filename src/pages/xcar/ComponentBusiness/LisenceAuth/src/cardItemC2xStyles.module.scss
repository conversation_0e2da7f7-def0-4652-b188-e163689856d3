@import '../../../Common/src/Tokens/tokens/color.scss';

.secTitle {
  font-size: 36px;
  line-height: 48px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.center {
  flex: 1;
  flex-direction: row;
  align-items: center;
  margin-right: 14px;
}
.section {
}
.headTitleWrap {
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  padding-bottom: 24px;
  position: relative;
}
.titleBadge {
  width: 10px;
  height: 36px;
  border-top-left-radius: 0px;
  border-top-right-radius: 4px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 4px;
  margin-right: 22px;
}
.contentWarp {
  padding-left: 32px;
  padding-right: 32px;
}
.selfServiceDriverWarp {
  padding-left: 42px;
}
.hairLine {
  margin-top: 24px;
}
.cardTipWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding-top: 5px;
  margin-bottom: -8px;
}
.cardTitleIcon {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
  margin-bottom: 12px;
  margin-right: 8px;
  top: -8px;
}
.cardTitle {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
  margin-bottom: 24px;
}
.imgbox {
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  height: 204px;
}
.imgCard {
  border-radius: 3px;
  width: 334px;
  height: 204px;
  justify-content: center;
  align-items: center;
}
.cardBg {
  width: 334px;
  height: 204px;
}
.cameraIconWrap {
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 2;
}
.cameraIcon {
  width: 72px;
  height: 72px;
  background-color: $blueBase;
  border-radius: 72px;
  justify-content: center;
  align-items: center;
  top: -8px;
}
.picicon {
  width: 30px;
  height: 24px;
}
.iconText {
  font-size: 24px;
  line-height: 34px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $C_0066F6;
  top: 6px;
}
.resultWrap {
}
.headerTip {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  margin-top: -10px;
}
.resultHeadTip {
  color: $redBase;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  width: 494px;
}
.verifyResult {
  padding: 32px;
  background-color: $grayBgSecondary;
  border-radius: 8px;
}
.lineContent {
  flex-direction: row;
}
.resultLineHead {
  width: 220px;
}
.resultItemTitle {
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
}
.disableText {
  color: $darkGrayBorder;
}
.resultItemValue {
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.resultIdCardIcon {
  font-size: 32px;
  color: $blueBase;
  margin-left: 14px;
}
.lineTipText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $redBase;
}
