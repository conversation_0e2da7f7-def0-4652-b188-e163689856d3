@import '../../../Common/src/Tokens/tokens/color.scss';

.underlineBox {
  border-bottom-width: 2px;
  border-color: #ff9a14;
  padding-bottom: 3px;
  margin-bottom: 22px;
}
.descHead {
  color: #ff9a14;
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  padding-top: 2px;
  padding-bottom: 2px;
}
.rightMoreWrap {
  display: flex;
  flex-direction: row;
  margin-top: -20px;
}
.rightMoreText {
  color: $white;
  font-size: 24px;
  line-height: 30px;
}
.rightMoreIcon {
  color: $white;
}
.titleWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.headerWrap {
  margin-bottom: 15px;
}
.driverImage {
  width: 300px;
  height: 200px;
}
.headTip {
  color: $white;
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-top: 8px;
  margin-bottom: 24px;
  text-align: center;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.headTipIcon {
  color: $white;
  margin-right: 8px;
  font-size: 14px;
}
.headTipText {
  color: $white;
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.desc {
  text-align: right;
}
.descWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.descTextItem {
  color: $white;
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-right: 12px;
  align-self: flex-start;
}
.descText {
  color: $white;
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  padding-top: 3px;
  padding-bottom: 3px;
  margin-bottom: 18px;
}
