import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import Dimensions from '@c2x/apis/Dimensions';
import BlurView from '@c2x/components/BlurView';
import React from 'react';
import {
  XView as View,
  xClassNames,
  xMergeStyles,
  xRouter,
  XScrollView,
} from '@ctrip/xtaro';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import {
  getPixel,
  isAndroid,
  useMemoizedFn,
  fixIOSOffsetBottom,
  vh,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { color, font, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import Button from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import BBkModal from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';

import { ButtonSize } from '@ctrip/rn_com_car/dist/src/Tokens/tokens/tokenType';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './lisenceAuthTipModalC2xStyles.module.scss';
import { CarStorage, CarLog } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import { texts } from './Texts';
import {
  LisenceCardFaceType,
  LisenceType,
} from '../../../State/OnlineAuth/FuntionTypes';
import { mapLisenceByCardFace } from '../../../State/OnlineAuth/Mappers';
import { imgPrefix2 } from './Constants';

const { width, height } = Dimensions.get('window');
const styles = StyleSheet.create({
  wrap: {
    height,
    width,
    position: 'absolute',
    top: 0,
    left: 0,
  },
  wrapContent: {
    paddingLeft: getPixel(40),
    paddingRight: getPixel(40),
    paddingTop: getPixel(isAndroid ? 80 : 160),
    height,
    width,
    position: 'absolute',
    top: 0,
    left: 0,
    flexDirection: 'column',
    flex: 1,
  },
  iKnowBtn: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: isAndroid ? 20 : fixIOSOffsetBottom(10),
  },
  btn: {
    backgroundColor: color.transparent,
    borderWidth: getPixel(1),
    borderColor: color.white,
    height: getPixel(88),
    paddingTop: 0,
    paddingBottom: 0,
    width: getPixel(670),
    marginTop: getPixel(50),
  },
  btnText: {
    ...font.title4LightStyle,
  },
});

const renderContent = (textArr, examples) => {
  return (
    <>
      {examples.map(el => {
        return (
          <View key={el?.title}>
            <View className={c2xStyles.titleWrap}>
              <View className={c2xStyles.underlineBox} style={el.titleStyle}>
                <BbkText className={c2xStyles.descHead}>{el?.title}</BbkText>
              </View>
              {!!el?.rightMoreText && (
                <Touchable
                  className={c2xStyles.rightMoreWrap}
                  onPress={el?.rightMoreClick}
                  testID={el?.rightExposureId}
                >
                  <BbkText className={c2xStyles.rightMoreText}>
                    {el.rightMoreText}
                  </BbkText>
                  <BbkText type="icon" className={c2xStyles.rightMoreIcon}>
                    {icon.arrowRight}
                  </BbkText>
                </Touchable>
              )}
            </View>
            <View
              className={xClassNames(c2xStyles.headerWrap, c2xStyles.titleWrap)}
            >
              {el?.images?.map(item => (
                <View key={item.text}>
                  <Image
                    className={c2xStyles.driverImage}
                    style={el.imageStyle}
                    src={item.image}
                    mode="aspectFit"
                  />

                  <View className={c2xStyles.headTip}>
                    <BbkText className={c2xStyles.headTipIcon} type="icon">
                      {icon.arrowUpFilled}
                    </BbkText>
                    <BbkText className={c2xStyles.headTipText}>
                      {item.text}
                    </BbkText>
                  </View>
                </View>
              ))}
            </View>
          </View>
        );
      })}
      <View className={c2xStyles.desc}>
        <View
          className={c2xStyles.underlineBox}
          style={{
            width: getPixel(87),
          }}
        >
          <BbkText className={c2xStyles.descHead}>
            {texts.exampleDescHead}
          </BbkText>
        </View>
        {textArr.map(text => (
          <View key={text} className={c2xStyles.descWrap}>
            <BbkText className={c2xStyles.descTextItem}>•</BbkText>
            <BbkText className={c2xStyles.descText}>{text}</BbkText>
          </View>
        ))}
      </View>
    </>
  );
};

export const getStorageLisenceNerverTip = async key => {
  const result = await CarStorage.load(key, true);
  return result === 'true';
};

export const saveStorageLisenceNerverTip = key => {
  CarStorage.save(key, true, undefined, true);
};

const modalTheme = { bbkModalMaskColor: color.R_0_0_0_0_65 };

const LisenceAuthTipModal = (props: {
  cardFaceType: LisenceCardFaceType;
  visible: boolean;
  onConfirm: Function;
}) => {
  const { onConfirm, visible, cardFaceType } = props;
  const lisenceType = mapLisenceByCardFace(cardFaceType);
  const openH5 = useMemoizedFn(() => {
    CarLog.LogCode({
      name: '点击_驾驶证示例页_查看电子驾驶证获取方式',
    });
    xRouter.navigateTo({
      url: 'https://m.ctrip.com/tangram/MTQ5NzMx?ctm_ref=vactang_page_149731&isHideNavBar=YES&apppgid=10650019968&statusBarStyle=1',
    });
  });
  const idCardTypeConfig = {
    desc: [
      '证件为有效证件（当前仅支持中国大陆二代身份证）',
      '拍摄角度正对证件，保证图片清晰无反光',
    ],
    example: [
      {
        title: '示例',
        images: [
          {
            image: `${imgPrefix2}/idCardA.png`,
            text: texts.exampleIdCardAText,
          },
          {
            image: `${imgPrefix2}/idCardB.png`,
            text: texts.exampleIdCardBText,
          },
        ],
        imageStyle: {
          marginBottom: lisenceType === LisenceType.Dirver ? getPixel(10) : 0,
          marginTop: lisenceType === LisenceType.Dirver ? getPixel(10) : 0,
        },
        titleStyle: {
          width: getPixel(60),
        },
      },
    ],
  };

  const driverTypeConfig = {
    desc: [
      '证件为有效中国大陆驾照，纸质与电子驾照二选一',
      '对有效中国大陆驾照正对角度拍摄，清晰无反光',
    ],
    example: [
      {
        title: '示例',
        images: [
          {
            image: `${imgPrefix2}/driver.png`,
            text: texts.exampleDriverAText,
          },
          {
            image: `${imgPrefix2}/driverOther.jpg`,
            text: texts.exampleDriverBText,
          },
        ],
        imageStyle: {
          marginBottom: lisenceType === LisenceType.Dirver ? getPixel(10) : 0,
          marginTop: lisenceType === LisenceType.Dirver ? getPixel(10) : 0,
        },
        titleStyle: {
          width: getPixel(60),
        },
      },
      {
        title: '电子驾驶证示例',
        rightMoreText: '如何获取电子驾驶证',
        rightMoreClick: openH5,
        rightExposureId: CarLog.LogExposure({
          name: '曝光_驾驶证示例页_查看电子驾驶证获取方式',
        }),
        images: [
          {
            image: `https://dimg04.c-ctrip.com/images/1tg2q12000iiknpyeABBD.png`,
            text: texts.exampleDriverAText,
          },
          {
            image: `https://dimg04.c-ctrip.com/images/1tg0i12000iikneokE6B0.png`,
            text: texts.exampleDriverBText,
          },
        ],
        titleStyle: {
          width: getPixel(205),
        },
        imageStyle: {
          width: getPixel(296),
          height: getPixel(540),
        },
      },
    ],
  };

  const config = {
    [LisenceType.IdCard]: idCardTypeConfig,
    [LisenceType.Dirver]: driverTypeConfig,
  };
  const { desc = [], example = [] } = config[lisenceType] || {};
  const tipContent = renderContent(desc, example);
  return (
    <BBkModal
      closeModalBtnTestID={
        UITestID.car_testid_page_online_lisenceauthtip_modal_closemask
      }
      isMask={true}
      modalVisible={visible}
      theme={modalTheme}
    >
      <BlurView
        blurType="dark"
        blurAmount={10}
        // @ts-ignore 升级072
        style={xMergeStyles([
          styles.wrap,
          isAndroid ? { backgroundColor: color.R_0_0_0_0_65 } : {},
        ])}
      />

      <View style={styles.wrapContent}>
        <XScrollView
          style={{ flex: 1 }}
          scrollY={true}
          showScrollbar={false}
          enhanced={true}
        >
          {tipContent}
        </XScrollView>

        <View style={styles.iKnowBtn}>
          <Button
            text={texts.authGotIt}
            testID={UITestID.car_testid_page_online_lisenceauthtip_modal_gotit}
            buttonSize={ButtonSize.L}
            buttonStyle={styles.btn}
            textStyle={styles.btnText}
            onPress={() => onConfirm(cardFaceType)}
          />
        </View>
      </View>
    </BBkModal>
  );
};

export default LisenceAuthTipModal;
