@import '../../../Common/src/Tokens/tokens/color.scss';

.headTitleWrap {
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  padding-bottom: 24px;
  position: relative;
}
.titleBadge {
  width: 10px;
  height: 36px;
  border-top-left-radius: 0px;
  border-top-right-radius: 4px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 4px;
  margin-right: 22px;
}
.flex1 {
  flex: 1;
}
.secTitle {
  font-size: 36px;
  line-height: 48px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.IDView {
  flex: 1;
  flex-direction: row;
  margin-bottom: 16px;
}
.IDViewLeft {
  width: 42px;
  align-items: center;
  justify-content: center;
}
.dot {
  width: 14px;
  height: 14px;
  border-radius: 14px;
  background-color: $C_006FF6;
}
.dot1DashLine {
  position: absolute;
  width: 100%;
  height: 80%;
  top: 70%;
  align-items: center;
}
.IDViewRight {
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.IDImageView {
  flex: 1;
  flex-direction: row;
  padding-right: 32px;
}
.faceRecTitleView {
  flex: 1;
  flex-direction: row;
  margin-top: 30px;
  align-items: center;
}
.faceViewLeft {
  width: 42px;
  height: 60px;
  align-items: center;
  justify-content: center;
}
.dot2DashLine {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 60%;
  align-items: center;
}
.selfServiceHeadTitleWrap {
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  position: relative;
}
.center {
  flex: 1;
  flex-direction: row;
  align-items: center;
}
.faceImage {
  height: 198px;
  width: 324px;
}
.section {
}
