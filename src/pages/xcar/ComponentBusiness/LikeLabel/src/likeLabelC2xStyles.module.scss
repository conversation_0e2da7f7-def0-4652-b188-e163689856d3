@import '../../../Common/src/Tokens/tokens/color.scss';

.mainContainer {
  width: 100vw;
  background-color: $white;
  padding-top: 32px;
}
.singleContainer {
  background-color: $white;
  margin-right: 10px;
}
.wrap {
  height: 40px;
  width: 218px;
  overflow: hidden;
  position: relative;
  padding-left: 26px;
  justify-content: center;
}
.singleWrap {
  height: 36px;
  width: 156px;
  padding-left: 0px;
  align-items: center;
  border-radius: 2px;
}
.textStyle {
  color: $white;
  font-size: 26px;
  line-height: 36px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
}
.singleTextStyle {
  font-size: 22px;
  line-height: 32px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.imgStyle {
  width: 16px;
  height: 40px;
  position: absolute;
  right: 0px;
}
