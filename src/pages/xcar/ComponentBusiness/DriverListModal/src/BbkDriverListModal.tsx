import StyleSheet from '@c2x/apis/StyleSheet';
import ScrollView from '@c2x/components/ScrollView'; /* eslint-disable */

/* bbk-component-business-migrate */
import React, { CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
/* eslint-disable */

/* bbk-component-business-migrate */

import {
  color,
  space,
  font,
  druation,
  styleSheet,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  getThemeAttributes,
  withTheme,
} from '@ctrip/rn_com_car/dist/src/Theming';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkComponentModal, {
  BbkComponentModalAnimationPreset,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import { EventHelper, GetABCache } from '../../../Util/Index';
import BbkComponentNoMatch from '../../ListNoMatch';
import BbkComponentDriverListAdd from '../../DriverListAdd';
import BbkComponentHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import BbkDriverListItem from '../../DriverListItem';
import { ImgType } from '../../ListNoMatch/src/NoMatchImg';
import {
  Passenger,
  CertificateType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import c2xStyles from './bbkDriverListModalC2xStyles.module.scss';
import { texts } from './Texts';
import { REQUIRED_THEME_ATTRIBUTES, ThemeProps } from './Theming';
import { UITestID } from '../../../Constants/Index';

const { getPixel } = BbkUtils;
const noop = (
  item: Passenger,
  index: number,
  driverLength: number,
  isSelectVaild?: boolean,
) => {};

type IProps = {
  style?: CSSProperties;
  data: Array<Passenger>;
  theme?: any;
  visible?: boolean;
  isModal?: boolean;
  isLeftIconCross?: boolean;
  onCancel?: () => void;
  onAddDriver?: () => void;
  onPress?: (item: Passenger) => void;
  onDelete?: (item: Passenger) => void;
  onEdit?: (
    item: Passenger,
    index: number,
    driverLength: number,
    isSelectVaild?: boolean,
  ) => void;
  isHeadBorder?: boolean;
  type?: string;
  passenger?: Passenger;
  yongAge?: number;
  oldAge?: number;
  availableCertificates?: Array<CertificateType>;
  curCertificates?: any;
  isSupportZhima?: boolean;
  isAuthorized?: boolean;
  userName?: string;
  isCreditRent?: boolean;
  isCtripCreditRent?: boolean;
  scrollToPassengerId?: string;
  addInstructData?: {
    title: string;
    content: string;
  };
};

type IState = {
  activeIndex: number;
  listLimit: number;
  isRendered: boolean;
  scrollEnabled: boolean;
};

class BbkDriverListModal extends React.Component<IProps, IState> {
  scrollRef = null;

  scrollToPassengerY = 0;

  timerIsRendered = null;
  timerListLimit = null;

  constructor(props) {
    super(props);
    this.state = {
      isRendered: false,
      activeIndex: 0,
      listLimit: 8,
      scrollEnabled: true,
    };
  }

  componentDidMount() {
    this.timerIsRendered = setTimeout(() => {
      this.setState({ isRendered: true });
    });
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.isRendered !== this.state.isRendered) {
      this.timerListLimit = setTimeout(() => {
        this.setState({ listLimit: 0 });
      });
    }
  }

  componentWillUnmount() {
    this.timerIsRendered && clearTimeout(this.timerIsRendered);
    this.timerListLimit && clearTimeout(this.timerListLimit);
  }

  getNoDriver() {
    const { onAddDriver } = this.props;
    if (!this.state.isRendered) return null;
    return (
      <View>
        {this.getSpacing()}
        <BbkComponentDriverListAdd onPress={onAddDriver} />
        {this.getSpacing()}

        <View className={c2xStyles.noDriverContainer}>
          <BbkComponentNoMatch
            type={ImgType.Comming_Soon}
            title={texts.driver_add_first}
            subTitle=""
            imgStyle={styles.noDriverImage}
            titleStyle={font.title1LightStyle}
            isShowOperateButton={false}
            isShowRentalDate={false}
          />
        </View>
      </View>
    );
  }

  chooseDriver = (driver, index, driverLength, isSelectVaild) => {
    const { onPress = noop, onCancel } = this.props;
    onCancel();
    setTimeout(() => onPress(driver, index, driverLength, isSelectVaild));
    EventHelper.sendEvent('chooseDriver', driver);
  };

  isEqualPassenger = (item: Passenger, passenger: Passenger) => {
    if (
      !!passenger &&
      !!passenger.passengerId &&
      passenger.passengerId === item.passengerId
    ) {
      return true;
    }
    return false;
  };

  clearSlider = () => {
    this.setState({ activeIndex: -1 });
  };

  onDriverListItemLayout = ({
    nativeEvent: {
      layout: { y },
    },
  }) => {
    this.scrollToPassengerY = y;
    this.scrollRef.scrollTo({
      x: 0,
      y: this.scrollToPassengerY,
      animated: true,
    });
  };

  lockScroll = () => {
    const timeoutId = setTimeout(() => {
      this.setState({ scrollEnabled: true });
      clearTimeout(timeoutId);
    }, 100);
  };

  getDriverList() {
    if (!this.state.isRendered) return null;
    const {
      data = [],
      type = 'isd',
      onDelete = noop,
      onEdit = noop,
      passenger,
      yongAge,
      oldAge,
      availableCertificates,
      onAddDriver,
      curCertificates = {},
      isSupportZhima,
      isAuthorized,
      userName,
      isCreditRent,
      isCtripCreditRent,
      scrollToPassengerId,
      addInstructData,
    } = this.props;
    const { activeIndex, listLimit, scrollEnabled } = this.state;
    const endTip =
      type === 'isd' ? `${texts.hasEnd} ~ ` : texts.drivers_noMoreDrivers;
    const renderData = listLimit ? data.slice(0, listLimit) : data;
    const driverLength = renderData.length;
    const isISDShelves3 = GetABCache.isISDShelves3();
    const { title: addDriverTitle, content: addDriverContent } = addInstructData || {};
    return (
      <ScrollView
        testID={UITestID.car_testid_page_driverlist_content}
        style={{ backgroundColor: color.grayBg, flex: 1 }}
        scrollEventThrottle={16}
        scrollEnabled={scrollEnabled}
        ref={ref => (this.scrollRef = ref)}
        showsVerticalScrollIndicator={false}
      >
        {this.getSpacing()}
        <BbkComponentDriverListAdd onPress={onAddDriver} />
        {this.getSpacing()}
        {renderData.map((item, index) => (
          <BbkDriverListItem
            key={`driverList_${index}`}
            availableCertificates={availableCertificates}
            curCertificates={curCertificates}
            data={item}
            isCreditRent={isCreditRent}
            isLast={index === data.length - 1}
            index={index}
            checked={this.isEqualPassenger(item, passenger)}
            onMove={() => {
              // 子View移动时，禁用列表页的滑动，100秒后释放
              this.setState({ activeIndex: index, scrollEnabled: false });
              this.lockScroll();
            }}
            isCanOperate={activeIndex === index}
            type={type}
            yongAge={yongAge}
            oldAge={oldAge}
            isSupportZhima={isSupportZhima}
            isAuthorized={isAuthorized}
            userName={userName}
            isCtripCreditRent={isCtripCreditRent}
            onPress={(driver, isSelectVaild) =>
              this.chooseDriver(driver, index + 1, driverLength, isSelectVaild)
            }
            onEdit={(driver, isSelectVaild) =>
              onEdit(driver, index + 1, driverLength, isSelectVaild)
            }
            onDelete={driver => onDelete(driver, index + 1, driverLength)}
            onLayout={
              this.isEqualPassenger(item, {
                passengerId: scrollToPassengerId,
              })
                ? this.onDriverListItemLayout
                : null
            }
          />
        ))}
        {isISDShelves3 && addDriverTitle && addDriverContent ? (
          <View className={c2xStyles.addDriversWrap}>
            <BbkText className={c2xStyles.textContainer}>
              <BbkText className={c2xStyles.addDriverTitle}>
                {addDriverTitle}：
              </BbkText>
              <BbkText className={c2xStyles.addDriverContent}>
                {addDriverContent}
              </BbkText>
            </BbkText>
          </View>
        ) : (
          <View className={c2xStyles.endView}>
            <BbkText style={xMergeStyles([font.title3LightStyle, styles.hasEnd])}>
              {endTip}
            </BbkText>
          </View>
        )}
        <View className={c2xStyles.endspacing} />
      </ScrollView>
    );
  }

  getSpacing() {
    return <View className={c2xStyles.spacing} />;
  }

  getContent = () => {
    const {
      data = [],
      onCancel,
      style,
      theme,
      isLeftIconCross = true,
      isHeadBorder,
    } = this.props;
    const themes =
      (getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as ThemeProps) ||
      ({} as ThemeProps);
    const { backgroundColor = color.white } = themes;

    return (
      <View
        testID={UITestID.car_testid_page_driverlist}
        className={c2xStyles.wrapper}
        style={{ backgroundColor, ...style }}
      >
        <BbkComponentHeader
          title={texts.drivers_select}
          onPressLeft={onCancel}
          style={styles.headerStyle}
          // styleInner={styles.header}
          isBottomBorder={isHeadBorder}
          leftIconTestID={UITestID.car_testid_page_driverlist_header_lefticon}
          isLeftIconCross={isLeftIconCross}
        />

        {data.length === 0 ? this.getNoDriver() : this.getDriverList()}
      </View>
    );
  };

  render() {
    const { visible, onCancel, isModal = true } = this.props;
    return isModal ? (
      <BbkComponentModal
        modalVisible={visible}
        {...BbkComponentModalAnimationPreset('bottom')}
        animationOutDuration={druation.animationDurationBase}
        isMask={false}
        onRequestClose={onCancel}
        useModal={false}
      >
        {this.getContent()}
      </BbkComponentModal>
    ) : (
      this.getContent()
    );
  }
}
const styles = StyleSheet.create({
  headerStyle: {
    backgroundColor: color.white,
  },
  header: {
    height: getPixel(88),
    paddingTop: getPixel(0),
    paddingBottom: getPixel(0),
  },
  noDriverImage: {
    width: getPixel(360),
    height: getPixel(360),
  },
  hasEnd: {
    color: color.hasEndColor,
  },
});

export default withTheme(BbkDriverListModal);
