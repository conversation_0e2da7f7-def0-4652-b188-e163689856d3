@import '@tokenColorScss';

.contentWrapExcess {
  width: 100vw;
  height: 774px;
}
.contentWrap {
  width: 100vw;
  height: 748px;
}
.imageBgExcess {
  width: 100vw;
  height: 774px;
  position: absolute;
  left: 0px;
  top: 0px;
}
.imageBg {
  width: 100vw;
  height: 748px;
  position: absolute;
  left: 0px;
  top: 0px;
}
.currentPackageNameWrap {
  width: 188px;
  height: 60px;
  position: absolute;
  left: 106px;
  top: 282px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.currentPackageNameText {
  font-size: 32px;
  line-height: 42px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $white;
}
.currentExcessEncourageWrap {
  width: 252px;
  height: 40px;
  position: absolute;
  left: 73px;
  top: 532px;
  flex-direction: row;
  align-items: center;
}
.currentExcessEncourageText {
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_4D6F94;
}
.youXiangGapPriceWrap {
  width: 272px;
  height: 50px;
  position: absolute;
  left: 418px;
  top: 476px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.fixYouxiangGapPriceWrap {
  top: 470px;
}
.youXiangGapPriceIcon {
  font-size: 24px;
  line-height: 34px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $greenBase;
  margin-right: 7px;
  margin-bottom: 8px;
}
.uintDay {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $greenBase;
  margin-bottom: 4px;
}
.buttonWrap {
  margin-top: 44px;
  margin-bottom: 16px;
  margin-left: 32px;
  margin-right: 32px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.notNeedButton {
  width: 256px;
  height: 88px;
  border-width: 1px;
  border-color: $easylifeBlue;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.notNeedText {
  font-size: 34px;
  line-height: 44px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $easylifeBlue;
}
.buyMoreButton {
  width: 406px;
  height: 88px;
  background-color: $easylifeBlue;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.buyMoreText {
  font-size: 34px;
  line-height: 44px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $white;
}
.cancelPolicyLabel {
  width: 202px;
  height: 46px;
  position: absolute;
  right: -10px;
  top: -23px;
}
