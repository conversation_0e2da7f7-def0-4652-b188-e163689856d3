import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import NumberText from '@ctrip/rn_com_car/dist/src/Components/Basic/NumberText';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';

import c2xStyles from './couponItemC2xStyles.module.scss';
import { ICouponItem } from './Types';
import { User, Utils } from '../../../Util/Index';
import { ImageUrl, UITestID } from '../../../Constants/Index';

const { getPixel, isAndroid, useMemoizedFn, autoProtocol } = BbkUtils;
const maxByteLength = 25; // 12个字 + 1个符号

export enum BGTYPE {
  THREE = 3,
  FOUR = 4,
  FIVE = 5,
}
export enum VALIDTYPE {
  VALID = 'valid',
  INVALID = 'invalid',
}
export enum COUPONTYPE {
  RECEIVABLE = 1,
  USEABLE = 2,
  DISABLED = 3,
  OUTOFSTOCK = 4,
  EXIRED = 5,
  NOTSTART = 6,
  ONLYNEW = 7,
  PURCHASED = 1000,
}
const styles = StyleSheet.create({
  leftWrap: {
    width: getPixel(200),
    marginLeft: getPixel(10),
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: getPixel(isAndroid ? 14 : 0),
  },
  currencyCode: {
    color: color.marketCouponPriceColor,
    marginBottom: getPixel(isAndroid ? -3 : 2),
    marginRight: getPixel(6),
    ...font.title1BoldStyle,
  },
  currencyCodeSmall: {
    marginBottom: getPixel(7),
  },
  value: {
    ...font.head1Style,
    color: color.marketCouponPriceColor,
    height: getPixel(64),
  },
  valueSmall: {
    ...font.head2Style,
  },
  discountText: {
    fontSize: getPixel(28),
    color: color.marketCouponPriceColor,
    marginBottom: getPixel(isAndroid ? -2 : 2),
    marginLeft: getPixel(2),
  },
  invalidTextColor: {
    color: color.grayBase,
  },
});

const CouponItem: React.FC<ICouponItem> = ({
  renderData = {},
  onReceive,
  isList,
  scrollRef,
  openDetail,
  vehicleCode,
  isVendorList,
}) => {
  const {
    promotionSecretId = '',
    promotionId,
    currencyCode = '',
    value = '',
    valueType,
    discountText = '',
    limitText = '',
    name = '',
    validPeriodDesc = '',
    cornerText = '',
    shortDesc = '',
    longDesc = '',
    status,
    statusName = '',
    invaildReason = '',
    deductionList = [],
    isActive = false,
    meetCouponStartDate = true,
  } = renderData;
  const [detailVisible, setDetailVisible] = useState(false);
  const ref = useRef(null);
  const layoutY = useRef(0);
  const isSmall = value.length > 3;
  const availableBgType =
    Utils.getByteLength(shortDesc) > maxByteLength ? BGTYPE.FIVE : BGTYPE.FOUR;
  const inAvailableBgType =
    Utils.getByteLength(invaildReason) > maxByteLength
      ? BGTYPE.FIVE
      : BGTYPE.FOUR;
  const bgType = isActive ? availableBgType : inAvailableBgType;
  const validType = isActive ? VALIDTYPE.VALID : VALIDTYPE.INVALID;
  const getBgUrl = useMemoizedFn(() => {
    if (validType === VALIDTYPE.VALID) {
      return availableBgType === BGTYPE.FIVE
        ? `${ImageUrl.CTRIP_EROS_URL}5_line_couponModalItemBg_valid.png`
        : `${ImageUrl.CTRIP_EROS_URL}4_line_couponModalItemBg_valid.png`;
    }
    return inAvailableBgType === BGTYPE.FIVE
      ? `${ImageUrl.CTRIP_EROS_URL}5_line_couponModalItemBg_invalid.png`
      : `${ImageUrl.CTRIP_EROS_URL}4_line_couponModalItemBg_invalid.png`;
  }); // eslint-disable-next-line @typescript-eslint/no-shadow
  const getCouponStatusUrl = useMemoizedFn(status => {
    switch (status) {
      case COUPONTYPE.USEABLE:
        return `${ImageUrl.CTRIP_EROS_URL}coupon_status_2.png`;
      case COUPONTYPE.DISABLED:
        if (isActive) {
          return `${ImageUrl.CTRIP_EROS_URL}coupon_status_2.png`;
        }
        return `${ImageUrl.BBK_IMAGE_PATH}coupon_status_3.png`;
      case COUPONTYPE.OUTOFSTOCK:
        return `${ImageUrl.CTRIP_EROS_URL}coupon_status_4.png`;
      case COUPONTYPE.EXIRED:
        return `${ImageUrl.BBK_IMAGE_PATH}coupon_status_5.png`;
      case COUPONTYPE.NOTSTART:
        return `${ImageUrl.BBK_IMAGE_PATH}coupon_status_6.png`;
      case COUPONTYPE.PURCHASED:
        return `${ImageUrl.DIMG04_PATH}1of1p12000c5bi63z49AB.png`;
      default:
        return '';
    }
  });
  let longDescList = [];
  if (typeof longDesc === 'string') {
    longDescList = longDesc.split('\\');
  }
  const isOnlyText = valueType === 1;
  useEffect(() => {
    setDetailVisible(openDetail);
  }, [openDetail]);
  const handlePressReceive = async () => {
    let isLogin = await User.isLogin();
    if (!isLogin) {
      isLogin = await User.toLogin();
    } else {
      onReceive({
        promotionSecretId,
        promotionId,
        isList,
        vehicleCode,
        isVendorList,
      });
    }
  };
  const onPressDetail = useCallback(() => {
    setDetailVisible(!detailVisible); // 关闭说明时，存在滚动长度不足的情况
    // 需要调用滚动来重置滚动位置
    if (isAndroid) {
      // 安卓 measure y 为 0
      scrollRef?.current?.scrollTo({ y: layoutY.current });
    } else if (ref.current?.$ref?.current?.measure) {
      ref.current.$ref.current.measure((x, y) => {
        scrollRef?.current?.scrollTo({ y });
      });
    }
  }, [detailVisible, setDetailVisible, ref]);

  const onLayout = useCallback(e => {
    layoutY.current = e.nativeEvent.layout.y;
  }, []);

  return (
    <View
      className={c2xStyles.container}
      ref={ref}
      onLayout={onLayout}
      testID={UITestID.car_testid_comp_coupon_item}
    >
      <View
        className={c2xStyles.wrap}
        style={{ height: getPixel(bgType === BGTYPE.FIVE ? 250 : 220) }}
      >
        <View style={styles.leftWrap}>
          <View className={c2xStyles.leftDownWrap}>
            {isOnlyText && value.length === 4 && (
              <View className={c2xStyles.onlyTextWrap}>
                <Text
                  className={classNames(
                    c2xStyles.onlyText,
                    !isActive && c2xStyles.invalidTextColor,
                  )}
                  fontWeight="medium"
                >
                  {value.substring(0, 2)}
                </Text>
                <Text
                  className={classNames(
                    c2xStyles.onlyText,
                    c2xStyles.onlyTextDown,
                    !isActive && c2xStyles.invalidTextColor,
                  )}
                  fontWeight="medium"
                >
                  {value.substring(2, 4)}
                </Text>
              </View>
            )}
            {!isOnlyText && !!currencyCode && (
              <Text
                style={xMergeStyles([
                  styles.currencyCode,
                  isSmall && styles.currencyCodeSmall,
                  !isActive && styles.invalidTextColor,
                ])}
                fontWeight="bold"
              >
                {currencyCode}
              </Text>
            )}
            {!isOnlyText && !!value && (
              <NumberText
                style={xMergeStyles([
                  styles.value,
                  isSmall && styles.valueSmall,
                  !isActive && styles.invalidTextColor,
                ])}
              >
                {value}
              </NumberText>
            )}
            {!isOnlyText && !!discountText && (
              <NumberText
                style={xMergeStyles([
                  styles.discountText,
                  !isActive && styles.invalidTextColor,
                ])}
              >
                {discountText}
              </NumberText>
            )}
          </View>
          {!!limitText && (
            <Text
              className={classNames(
                c2xStyles.limitText,
                isSmall && c2xStyles.limitTextSmall,
                !isActive && c2xStyles.invalidTextColor,
              )}
            >
              {limitText}
            </Text>
          )}
        </View>
        <View className={c2xStyles.rightWrap}>
          <View
            className={c2xStyles.rightContentWrap}
            style={{ height: getPixel(bgType === BGTYPE.FIVE ? 230 : 200) }}
          >
            <View>
              {!!name && (
                <Text
                  className={classNames(
                    c2xStyles.name,
                    !isActive && c2xStyles.invalidTextColor,
                  )}
                  numberOfLines={1}
                >
                  {name}
                </Text>
              )}
              {!!shortDesc && !invaildReason && (
                <Text className={c2xStyles.shortDesc} numberOfLines={2}>
                  {shortDesc}
                </Text>
              )}
              {!!validPeriodDesc && (
                <Text className={c2xStyles.validPeriodDesc} numberOfLines={1}>
                  {validPeriodDesc}
                </Text>
              )}
              {!!invaildReason && (
                <Text className={c2xStyles.invaildReason} numberOfLines={2}>
                  {invaildReason}
                </Text>
              )}
            </View>
            <Touchable
              testID={UITestID.car_testid_couponmodal_coupon_item_detail}
              className={c2xStyles.detailTitleWrap}
              onPress={onPressDetail}
            >
              <Text className={c2xStyles.detailTitle}>使用说明</Text>
              <Text type="icon" className={c2xStyles.arrow}>
                {detailVisible ? icon.arrowUp : icon.arrowDown}
              </Text>
            </Touchable>
          </View>
          {status === 1 && (
            <Touchable
              testID={UITestID.car_testid_couponmodal_coupon_item_receive}
              onPress={handlePressReceive}
            >
              <LinearGradient
                className={c2xStyles.buttonWrap}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 1.0 }}
                locations={[0, 1]}
                colors={[
                  color.listCouponLinearStart,
                  color.listCouponLinearEnd,
                ]}
              >
                <Text className={c2xStyles.buttonText}>{statusName}</Text>
              </LinearGradient>
            </Touchable>
          )}
        </View>
        {!!cornerText && (
          <LinearGradient
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 1.0 }}
            locations={[0, 1]}
            colors={[color.cornerLinearStart, color.cornerLinearEnd]}
            className={c2xStyles.newCorner}
          >
            <Text className={c2xStyles.cornerText} fontWeight="bold">
              {cornerText}
            </Text>
          </LinearGradient>
        )}
        {!meetCouponStartDate && (
          <Image
            className={c2xStyles.notStartCorner}
            src={autoProtocol(
              `${ImageUrl.componentImagePath}CouponEntry/not_start_coupon_corner.png`,
            )}
            mode="aspectFit"
          />
        )}
        {/* 类型 int, 策略状态枚举（1-可领、2-已领券可使用、3、已领券不可使用、4-已抢光、5-已过期、6-未开始、7-限新客可领, 1000-已购买) */}
        {(status === 2 ||
          status === 3 ||
          status === 4 ||
          status === 5 ||
          status === 6 ||
          status === 1000) && (
          <Image
            className={c2xStyles.couponSeal}
            src={autoProtocol(getCouponStatusUrl(status))}
            mode="aspectFit"
          />
        )}
        <Image
          className={c2xStyles.couponItemBg}
          style={{ height: getPixel(bgType === BGTYPE.FIVE ? 249 : 220) }}
          src={getBgUrl()}
          mode="aspectFit"
        />
      </View>
      <View className={c2xStyles.bottomWrap}>
        {detailVisible && (
          <View className={c2xStyles.detailWrap}>
            {deductionList.length > 0 && (
              <View className={c2xStyles.deductionList}>
                {deductionList.map((deductionItem, deductionIndex) => (
                  <View className={c2xStyles.deductionItem} key={deductionItem}>
                    <View className={c2xStyles.deductionNumWrap}>
                      <Text className={c2xStyles.deductionNum}>
                        {deductionIndex + 1}
                      </Text>
                    </View>
                    <View className={c2xStyles.deductionTextWrap}>
                      <Text
                        className={c2xStyles.deductionText}
                        numberOfLines={1}
                      >
                        {deductionItem}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>
            )}
            {longDescList.length > 0 && (
              <View className={c2xStyles.longDescList}>
                {longDescList.map(longDescItem => {
                  if (!longDescItem) return null;
                  return (
                    <View className={c2xStyles.longDescItem} key={longDescItem}>
                      <Text className={c2xStyles.longDesc}>
                        {longDescItem.trim()}
                      </Text>
                    </View>
                  );
                })}
              </View>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

export default CouponItem;
