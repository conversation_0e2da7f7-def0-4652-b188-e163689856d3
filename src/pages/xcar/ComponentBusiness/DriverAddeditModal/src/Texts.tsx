/* eslint-disable */

/* bbk-component-business-migrate */

export const texts = {
  get drivers_add() {
    return '新增驾驶员';
  },
  get drivers_edit() {
    return '编辑驾驶员';
  },
  get drivers_save() {
    return '保存';
  },
  get driver_name() {
    return '姓名';
  },
  get driver_cn_name() {
    return '中文姓名';
  },
  get driver_ctrip_name() {
    return '驾驶员真实姓名，与证件一致';
  },
  get driver_ctrip_lastname_placeholder() {
    return '如张小白填写为 "ZHANG"';
  },
  get driver_trip_lastname_placeholder() {
    return '例如 ZHANG';
  },
  get driver_ctrip_firstname_placeholder() {
    return '如张小白填写为 "XIAOBAI"';
  },
  get driver_trip_firstname_placeholder() {
    return '例如 XIAO BAI';
  },
  get driver_first_middle_mame() {
    return '名&中间名';
  },
  get driver_en_lastname() {
    return '英文姓';
  },
  get driver_en_firstname() {
    return '英文名';
  },
  get drivers_dateOfBirth() {
    return '出生日期';
  },
  drivers_dateOfBirthTooYoung: age =>
    `驾驶员最低年龄为${age}，请核对或填写其他符合要求的同伴作为驾驶员`,
  drivers_dateOfBirthTooOld: age =>
    `驾驶员最高年龄为${age}，请核对或填写其他符合要求的同伴作为驾驶员`,
  get driver_nationality() {
    return '国籍';
  },
  get country_region() {
    return '国家或地区';
  },
  get driver_certificate_type() {
    return '证件类型';
  },
  get driver_certificate_type_id() {
    return '身份证';
  },
  get driver_certificate_type_passport() {
    return '护照';
  },
  get driver_certificate_type_mtps() {
    return '台胞证';
  },
  get driver_certificate_type_homereturnpermit() {
    return '回乡证';
  },
  get driver_certificate_no() {
    return '证件号码';
  },
  driver_certificate_no_placeholder: certificate_type =>
    `驾驶员真实${certificate_type}号码，与证件一致`,
  get driver_phone() {
    return '联系手机';
  },
  get driver_phone_placeholder() {
    return '用于接收订单通知';
  },
  get driver_add_name_tip() {
    return '请确保姓名与证件一致';
  },
  get driver_add_name_tip_rule() {
    return '查看填写规则';
  },
  get driver_add_name_tip_can_rule() {
    return '可了解';
  },
  get driver_default_nationality() {
    return '';
  },
  get emptyDriver() {
    return '请选择驾驶员';
  },
  get driver_enter_fullname() {
    return '请输入正确的驾驶员姓名';
  },
  get driver_enter_certificate_type() {
    return '请选择证件类型';
  },
  get driver_enter_fullname_cn() {
    return '请输入中文姓名';
  },
  get driver_enter_last_name() {
    return '请输入英文姓';
  },
  get driver_enter_first_name() {
    return '请输入英文名';
  },
  get driver_enter_birthday() {
    return '请输入出生日期';
  },
  get driver_enter_nationality() {
    return '请选择国籍';
  },
  get driver_validate_error_id_name() {
    return '请输入正确的驾驶员中文姓名';
  },
  get driver_validate_error_other_name() {
    return '请输入纯中文姓名，或大写英文+空格，如WANG XIAOBAO';
  },
  get driver_validate_error_idno() {
    return '请输入正确的驾驶员身份证号码';
  },
  get driver_validate_error_idno_full() {
    return '年龄小于18周岁';
  },
  get driver_validate_error_mobile() {
    return '请输入正确的驾驶员联系手机';
  },
  get driver_validate_error_name_of_first() {
    return '第一个字必须为中文';
  },
  get driver_validate_error_name_of_no_special() {
    return '中文姓名不支持特殊符号，仅可填写1个·，生僻字用拼音代替';
  },
  get driver_validate_error_lastname() {
    return '仅可填写英文,不可包含空格';
  },
  get driver_validate_error_firstname() {
    return '仅可填写英文';
  },
  get mobile_error() {
    return '请输入正确的驾驶员联系手机';
  },
  fullAge: year => `${year}周岁`,
  driver_enter_certificate_no: certificate_type =>
    `请输入正确的驾驶员${certificate_type}号码`,
  lessLengthTip: len => {
    return `证件号码不可少于${len}位`;
  },
  moreThanLengthTip: len => {
    return `证件号码不可多于${len}位`;
  },
  driver_validate_error_idno_thanAge: age => {
    return `年龄大于${age}周岁`;
  },
  get driver_noCertificate() {
    return '请补充证件信息';
  },
  get driver_noAgree_toast() {
    return '请先勾选同意后再保存';
  },
  get driver_validate_error() {
    return '请完善驾驶员信息';
  },
};

export enum DriverTypeExceptId {
  // 护照
  passport = '2',
  // 回乡证
  homereturnpermit = '7',
  // 台胞证
  mtps = '8',
}
