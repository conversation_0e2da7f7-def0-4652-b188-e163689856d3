import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import CountryCode from '@c2x/apis/CountryCode';
import React, { CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
/* eslint-disable */

/* bbk-component-business-migrate */
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import { color, font, druation, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { KeyboardAwareScrollView } from '@c2x/extraPackages/react-native-keyboard-aware-scroll-view';
import {
  getThemeAttributes,
  withTheme,
} from '@ctrip/rn_com_car/dist/src/Theming';
import { BbkUtils, DateFormatter } from '@ctrip/rn_com_car/dist/src/Utils';
// @ts-ignore 升级072

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkInput from '@ctrip/rn_com_car/dist/src/Components/Basic/Input';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkComponentModal, {
  BbkComponentModalAnimationPreset,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import BbkComponentButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import BbkComponentHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import BbkMobileInput from '@ctrip/rn_com_car/dist/src/Components/Basic/MobileInput';
import RentalCarsDatePicker from '@ctrip/rn_com_car/dist/src/Components/Basic/CarDatePicker';
import BbkDriverCertificateInput from '../../DriverCertificateInput';
import {
  Passenger,
  AreaCode,
  InputFormatType,
  CertificateType,
  DriverTip,
  DriverTipStyle,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import c2xStyles from './bbkDriverAddEditModalC2xStyles.module.scss';
import { texts } from './Texts';
import { REQUIRED_THEME_ATTRIBUTES, ThemeProps } from './Theming';
import Utils from '../../../Util/Utils';
import Toast, { logValidate } from '../../../Util/Log/ToastLog';
import { UITestID } from '../../../Constants/Index';

const {
  getPixel,
  isMobile,
  isFirstName,
  isLastName,
  isForeignMobile,
  getFullAge,
} = BbkUtils;
const noop = item => item;
const noopShow = () => {};
const isdTypeList = [
  CertificateType.id,
  CertificateType.passport,
  CertificateType.homereturnpermit,
  CertificateType.mtps,
];

const isdCertificateTypeList = {
  1: texts.driver_certificate_type_id,
  2: texts.driver_certificate_type_passport,
  7: texts.driver_certificate_type_homereturnpermit,
  8: texts.driver_certificate_type_mtps,
};

const minDate = '1900-01-01';
const maxDate = dayjs().format('YYYY-MM-DD');

const callAreaCode: (
  callback: (e: AreaCode) => void,
  currentCountry?: AreaCode,
) => void = (callback, currentCountry) => {
  if (CountryCode) {
    if (!currentCountry) {
      throw new Error('Please input currentCountry');
    }
    const { countryCode, showName } = currentCountry;
    CountryCode.getCountryCode(
      { countryCode: Number(countryCode), showName, needSMS: 0 },
      result => {
        callback({
          showName: result && result.showName,
          countryCode: result && `${result.countryCode}`,
          countryName: result && result.countryName,
        });
      },
    );
  }
};

type IProps = {
  style?: CSSProperties;
  data: Passenger;
  theme?: any;
  visible?: boolean;
  isModal?: boolean;
  isLeftIconCross?: boolean;
  onCancel?: () => void;
  nameBlur?: (name: string) => void;
  showRuleModal?: () => void;
  onSave?: (item: Passenger) => void;
  fullNameClick?: () => void;
  lastNameClick?: () => void;
  firstNameClick?: () => void;
  nationalityNameClick?: () => void;
  birthDayClick?: () => void;
  certificateNoClick?: () => void;
  certificateTypeClick?: (type) => void;
  mobileClick?: () => void;
  areaCodeClick?: () => void;
  contactClick?: () => void;
  isHeadBorder?: boolean;
  isNameReverse?: boolean;
  isAdd?: boolean;
  type?: string;
  certificateType?: string;
  availableCertificates?: Array<CertificateType>;
  curCertificates?: any;
  yongAge?: number;
  oldAge?: number;
  tips?: Array<DriverTip>;
  hasCheckBox?: boolean;
};

type IReturnDate = {
  ptime?: string;
  rtime?: string;
};

const defaultReturnDate = {
  ptime: '',
  rtime: '',
};

const defaultPassenger = {
  passengerId: '',
  firstName: '',
  lastName: '',
  fullName: '',
  birthday: '',
  age: 0,
  nationality: '',
  nationalityName: '',
  countryCode: '86',
  mobile: '',
  email: '',
  certificateList: [],
  isCreditQualified: false,
};

const defaultAreaCode = {
  showName: '',
  countryCode: '',
  countryName: '',
};

type IState = {
  activeIndex: number;
  errorMessage: string;
  passenger: Passenger;
  formatMobile: string;
  formatCertificateNo: string;
  currentType: string;
  fullNameError: string;
  certificateTypeError: string;
  certificateNoError: string;
  mobileError: string;
  lastNameError: string;
  firstNameError: string;
  birthdayError: string;
  nationalityError: string;
  isAgreeTerms: boolean;
};

class BbkDriverAddEditModal extends React.Component<IProps, IState> {
  datePicker = null;

  static defaultProps = {
    hasCheckBox: true,
  };

  constructor(props) {
    super(props);
    let {
      availableCertificates = [],
      data = defaultPassenger,
      curCertificates = {},
    } = this.props;
    const { certificateList = [], passengerId = '' } = data;
    const allTyleList = isdTypeList;
    if (availableCertificates.length <= 0 || !availableCertificates.includes) {
      availableCertificates = allTyleList;
    }
    let effectiveNoType = [];
    certificateList.map(icitem => {
      if (icitem.certificateNo) {
        effectiveNoType.push(icitem.certificateType);
      }
    });
    let effectiveTypeList = allTyleList
      .concat(availableCertificates)
      .filter(
        v => allTyleList.includes(v) && availableCertificates.includes(v),
      );
    if (effectiveNoType.length > 0) {
      effectiveTypeList = effectiveTypeList
        .concat(effectiveNoType)
        .filter(
          v => effectiveTypeList.includes(v) && effectiveNoType.includes(v),
        );
    }
    let currentType = curCertificates[passengerId];
    let tempType = availableCertificates.includes(currentType)
      ? currentType
      : '';
    const currentCertificate =
      certificateList.find(
        item => item.certificateType === CertificateType.id,
      ) || {};

    const { certificateNo = '' } = currentCertificate;
    this.state = {
      activeIndex: 0,
      errorMessage: '',
      passenger: data,
      formatMobile: Utils.strFormat(data?.mobile, 1),
      formatCertificateNo: Utils.strFormat(certificateNo, 3),
      currentType:
        tempType ||
        props.certificateType ||
        effectiveTypeList[0] ||
        availableCertificates[0],
      fullNameError: '',
      certificateTypeError: '',
      certificateNoError: '',
      mobileError: '',

      lastNameError: '',
      firstNameError: '',
      birthdayError: '',
      nationalityError: '',
      isAgreeTerms: false, // 是否同意条款
    };
  }

  // 校验姓名
  validateFullName = () => {
    let isValidate = true;
    const { type = 'isd' } = this.props;
    const { passenger } = this.state;
    let { fullName = '' } = passenger;
    fullName = fullName.trim();
    switch (type) {
      case 'isd':
        if (!fullName) {
          this.setState({ fullNameError: texts.driver_enter_fullname });
          isValidate = false;
        }
        break;
      case 'osd':
        if (!fullName) {
          this.setState({ fullNameError: texts.driver_enter_fullname_cn });
          isValidate = false;
        } else if (/^[^\u4e00-\u9fa5]$/g.test(fullName.trim().substr(0, 1))) {
          this.setState({
            fullNameError: texts.driver_validate_error_name_of_first,
          });
          isValidate = false;
        } else if (
          !/^[a-zA-Z\u4e00-\u9fa5]+[·]?[a-zA-Z\u4e00-\u9fa5]*$/g.test(fullName)
        ) {
          this.setState({
            fullNameError: texts.driver_validate_error_name_of_no_special,
          });
          isValidate = false;
        }
        break;
      default:
        break;
    }

    return isValidate;
  };

  // 校验证件号码
  validateCertifacateNo = () => {
    let isValidate = true;
    const { passenger, currentType } = this.state;
    let { certificateList = [] } = passenger;
    const certificate =
      certificateList.find(item => item.certificateType === currentType) || {};
    let { certificateNo = '' } = certificate;
    certificateNo = certificateNo.replace(/\s/g, '');
    const { type = 'isd', yongAge, oldAge } = this.props;
    switch (type) {
      case 'isd':
        if (!certificateNo) {
          this.setState({
            certificateNoError: texts.driver_enter_certificate_no(
              isdCertificateTypeList[currentType],
            ),
          });
          isValidate = false;
        } else if (currentType === CertificateType.id) {
          const ageMsg = Utils.isInLimitAgeIdCard(
            certificateNo,
            oldAge,
            yongAge,
          );
          if (!!ageMsg) {
            this.setState({
              certificateNoError: Utils.isInLimitAgeIdCard(
                certificateNo,
                oldAge,
                yongAge,
              ),
            });
            isValidate = false;
          }
        }
        break;
      default:
        break;
    }
    return isValidate;
  };

  // 校验手机号码
  validateMobile = () => {
    let isValidate = true;
    const { type = 'isd' } = this.props;
    const { passenger } = this.state;
    let { mobile = '', countryCode } = passenger;
    mobile = mobile.replace(/\s/g, '');
    switch (type) {
      case 'isd':
        if (!mobile) {
          this.setState({ mobileError: texts.mobile_error });
          isValidate = false;
        }
        break;
      case 'osd':
        if (!mobile) {
          this.setState({ mobileError: texts.mobile_error });
          isValidate = false;
        } else if (
          (countryCode !== '86' && !isForeignMobile(mobile)) ||
          (countryCode === '86' && !isMobile(mobile, true))
        ) {
          this.setState({ mobileError: texts.mobile_error });
          isValidate = false;
        }
        break;
      default:
        break;
    }
    return isValidate;
  };

  validatePassenger = () => {
    const { passenger, currentType } = this.state;
    let {
      nationalityName,
      firstName = '',
      lastName = '',
      birthday,
      certificateList,
      mobile = '',
      countryCode,
      fullName,
    } = passenger;
    firstName = firstName.trim();
    lastName = lastName.trim();
    const { type = 'isd', yongAge, oldAge } = this.props;
    const age = getFullAge(birthday);
    let isValidate = true;
    let errorMsg = '';
    let value;
    switch (type) {
      case 'isd':
        // 校验fullName，如果是护照则不校验fullName
        if (
          currentType !== CertificateType.passport &&
          !this.validateFullName()
        ) {
          errorMsg = `FullName error`;
          value = fullName;
          isValidate = false;
        }

        if (!currentType) {
          this.setState({
            certificateTypeError: texts.driver_enter_certificate_type,
          });
          errorMsg = texts.driver_enter_certificate_type;
          isValidate = false;
        }
        // 校验证件号码
        if (!this.validateCertifacateNo()) {
          errorMsg = `Certificate No error`;
          value = `certificateList is ${JSON.stringify(
            certificateList,
          )}, currentType is ${currentType}`;
          isValidate = false;
        }
        // 校验手机号码
        if (!this.validateMobile()) {
          errorMsg = `Mobile error`;
          value = mobile;
          isValidate = false;
        }
        if (currentType === CertificateType.passport) {
          if (!lastName) {
            this.setState({ lastNameError: texts.driver_enter_last_name });
            errorMsg = texts.driver_enter_last_name;
            isValidate = false;
          } else if (!isLastName(lastName)) {
            this.setState({
              lastNameError: texts.driver_validate_error_lastname,
            });
            errorMsg = texts.driver_validate_error_lastname;
            value = lastName;
            isValidate = false;
          }
          if (!firstName) {
            this.setState({ firstNameError: texts.driver_enter_first_name });
            errorMsg = texts.driver_enter_first_name;
            isValidate = false;
          } else if (!isFirstName(firstName)) {
            this.setState({
              firstNameError: texts.driver_validate_error_firstname,
            });
            errorMsg = texts.driver_validate_error_firstname;
            value = firstName;
            isValidate = false;
          }
        }
        break;
      case 'osd':
        // 校验fullName
        if (!this.validateFullName()) {
          errorMsg = `FullName error`;
          value = fullName;
          isValidate = false;
        }
        if (!lastName) {
          this.setState({ lastNameError: texts.driver_enter_last_name });
          errorMsg = texts.driver_enter_last_name;
          isValidate = false;
        } else if (!isLastName(lastName)) {
          this.setState({
            lastNameError: texts.driver_validate_error_lastname,
          });
          errorMsg = texts.driver_validate_error_lastname;
          value = lastName;
          isValidate = false;
        }
        if (!firstName) {
          this.setState({ firstNameError: texts.driver_enter_first_name });
          errorMsg = texts.driver_enter_first_name;
          isValidate = false;
        } else if (!isFirstName(firstName)) {
          this.setState({
            firstNameError: texts.driver_validate_error_firstname,
          });
          errorMsg = texts.driver_validate_error_firstname;
          value = firstName;
          isValidate = false;
        }
        if (!birthday) {
          this.setState({ birthdayError: texts.driver_enter_birthday });
          errorMsg = texts.driver_enter_birthday;
          isValidate = false;
        }
        if (yongAge > 0 && age < yongAge) {
          this.setState({
            birthdayError: texts.drivers_dateOfBirthTooYoung(yongAge),
          });
          errorMsg = texts.drivers_dateOfBirthTooYoung(yongAge);
          value = age;
          isValidate = false;
        }
        if (oldAge > 0 && age > oldAge) {
          this.setState({
            birthdayError: texts.drivers_dateOfBirthTooOld(oldAge),
          });
          errorMsg = texts.drivers_dateOfBirthTooOld(oldAge);
          value = age;
          isValidate = false;
        }
        if (!nationalityName) {
          this.setState({ nationalityError: texts.driver_enter_nationality });
          errorMsg = texts.driver_enter_nationality;
          isValidate = false;
        }
        // 校验手机号码
        if (!this.validateMobile()) {
          errorMsg = `Mobile error`;
          value = `value is ${mobile}, countryCode is ${countryCode}`;
          isValidate = false;
        }
        break;
    }
    if (!isValidate) {
      logValidate(errorMsg, value);
    }
    return isValidate;
  };

  savePassengerFn = () => {
    Keyboard.dismiss();
    // 此时会触发BbkInput onblur事件(从而触发onChangeNo事件，需要延时执行保存验证事件，防止错误提示文案被清空）
    setTimeout(() => {
      // 先校验信息是否填写完整
      if (!this.validatePassenger()) {
        Toast.show(texts.driver_validate_error);
        return;
      }
      // 再校验是否勾选同意
      const { hasCheckBox } = this.props;
      if (!this.state.isAgreeTerms && hasCheckBox) {
        Toast.show(texts.driver_noAgree_toast);
        return;
      }
      this.savePassenger();
    }, 50);
  };

  savePassenger = () => {
    const { onSave = noop } = this.props;
    const { passenger, currentType } = this.state;
    let {
      fullName = '',
      firstName = '',
      lastName = '',
      mobile = '',
      certificateList = [],
    } = passenger;
    const resultDate = passenger?.birthday
      ? DateFormatter.ctripTimeFormat(passenger.birthday, 'YYYY-MM-DD')
      : '';
    const currentCertificate = certificateList.filter(
      item => item.certificateType === currentType,
    );
    let tempCurrentCertificate = JSON.parse(JSON.stringify(currentCertificate));
    tempCurrentCertificate.map(ic => {
      ic.certificateNo = (ic.certificateNo || '').replace(/\s/g, '');
    });

    const tempPassenger = {
      ...passenger,
      fullName: fullName.trim(),
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      birthday: resultDate,
      mobile: mobile.replace(/\s/g, ''),
      certificateList: tempCurrentCertificate,
    };
    // 如果是护照，fullName为可选字段，已经保存的fullName可以删除，常旅传递特殊字段Delete_flag可以删除fullName
    if (
      currentType === CertificateType.passport &&
      tempPassenger.fullName === ''
    ) {
      tempPassenger.fullName = 'Delete_flag';
    }
    this.validatePassenger() && onSave(tempPassenger);
  };

  RenderedRight = () => (
    <BbkComponentButton
      text={texts.drivers_save}
      buttonStyle={styles.saveBtn}
      textStyle={{
        color: color.blueBase,
      }}
      onPress={this.savePassengerFn}
    />
  );

  onShowRuleModal = () => {
    Keyboard.dismiss();
    const { showRuleModal = noopShow } = this.props;
    showRuleModal();
  };

  onAutoChangeLastNameAndFirstName = (lastName = '', firstName = '') => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    tempPassenger.lastName = lastName?.replace('·', '');
    tempPassenger.firstName = firstName?.replace('·', '');
    this.setState({ passenger: tempPassenger, lastNameError: '' });
  };

  onChangeName = texts => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    tempPassenger.fullName = texts;
    this.setState({ passenger: tempPassenger, fullNameError: '' });
  };

  onChangeLastName = texts => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    let value = texts;
    tempPassenger.lastName = value;
    this.setState({ passenger: tempPassenger, lastNameError: '' });
  };

  lastNameBlur = () => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    this.setState({ passenger: tempPassenger, lastNameError: '' });
  };

  onChangeFirstName = texts => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    let value = texts;
    tempPassenger.firstName = value;
    this.setState({ passenger: tempPassenger, firstNameError: '' });
  };

  firstNameBlur = () => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    this.setState({ passenger: tempPassenger, firstNameError: '' });
  };

  onChangeDateOfBirth = () => {
    const { birthDayClick = noopShow } = this.props;
    Keyboard.dismiss();
    this.datePicker && this.datePicker.show({ focusOnRtime: false });
    birthDayClick();
  };

  handleDateChange = (date: IReturnDate = defaultReturnDate) => {
    const { ptime } = date;
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    tempPassenger.birthday = ptime;
    const age = getFullAge(ptime);
    tempPassenger.age = age;
    this.setState({ passenger: tempPassenger, birthdayError: '' });
  };

  onChangeNo = texts => {
    let { passenger, currentType, formatCertificateNo } = this.state;
    let tempPassenger = { ...passenger };
    let { certificateList = [] } = tempPassenger;
    let tempcertificateList = JSON.parse(JSON.stringify(certificateList));
    let certificate = tempcertificateList.find(
      item => item.certificateType === currentType,
    );
    let curStateNo = certificate?.certificateNo;
    let idNoOrigin = (texts || '').replace(/\s*/g, '');
    // 去除中间的空格 带空格格式化输入框限制长度特殊处理 身份证限制21位，其他证件限制15位
    let idNo = idNoOrigin.substr(
      0,
      currentType === CertificateType.id ? 18 : 15,
    );
    if (!certificate) {
      certificate = {
        certificateType: currentType,
        certificateNo: idNo,
      };
      tempcertificateList.push(certificate);
    } else {
      certificate.certificateNo = idNo;
    }
    let curNo = formatCertificateNo;
    // 输入的证件号码与当前的证件号码不一致且输入的证件号码正好18位，则进行格式化
    const isNeedFormat =
      (idNo !== curStateNo && idNo?.length === 18) || idNoOrigin?.length > 18;
    if (currentType === CertificateType.id) {
      curNo = isNeedFormat ? Utils.strFormat(idNo, 3) : texts;
    }
    tempPassenger.certificateList = tempcertificateList;
    this.setState({
      passenger: tempPassenger,
      certificateNoError: '',
      formatCertificateNo: curNo,
    });
  };

  onChangeType = type => {
    const { certificateTypeClick = noop } = this.props;
    this.setState({
      currentType: type,
      certificateTypeError: '',
      certificateNoError: '',
      fullNameError: '',
      mobileError: '',
    });
    certificateTypeClick(isdCertificateTypeList[type]);
  };

  onNationalityChange = (area: AreaCode = defaultAreaCode) => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    const { showName, countryName } = area;

    tempPassenger.nationality = countryName;
    tempPassenger.nationalityName = showName;
    this.setState({ passenger: tempPassenger, nationalityError: '' });
  };

  onAreaCodeChange = (area: AreaCode = defaultAreaCode) => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    const { countryCode } = area;

    tempPassenger.countryCode = countryCode;
    this.setState({ passenger: tempPassenger, nationalityError: '' });
  };

  onContactChange = contact => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    tempPassenger.mobile = contact;
    this.setState({ passenger: tempPassenger, mobileError: '' });
  };

  getFirstNameLabel = () => {
    const { type = 'isd' } = this.props;
    switch (type) {
      case 'isd':
      case 'osd':
        return `${texts.driver_en_firstname} Given names`;
    }
  };

  getLastNameLabel = () => {
    const { type = 'isd' } = this.props;
    switch (type) {
      case 'isd':
      case 'osd':
        return `${texts.driver_en_lastname} Surname`;
    }
  };

  getBirthdayAgeLabel = () => {
    const { type = 'isd' } = this.props;
    const { passenger } = this.state;
    const { birthday, age } = passenger;
    if (birthday) {
      let resultDate = '';
      switch (type) {
        case 'isd':
        case 'osd':
          resultDate = DateFormatter.ctripTimeFormat(birthday, 'YYYY-MM-DD');
          break;
      }
      return `${resultDate}  ${texts.fullAge(age)}`;
    }
    return '';
  };

  onChangeMobile = texts => {
    const { passenger } = this.state;
    const tempPassenger = { ...passenger };
    const isMobileValueSpit = this.getIsMobileValueSpit();
    // 去除中间的空格 带空格格式化输入框限制长度特殊处理 国内电话11位，否则限制100位
    const formatTextsOrigin = (texts || '').replace(/\s*/g, '');
    const formatTexts = formatTextsOrigin.substr(
      0,
      isMobileValueSpit ? 11 : 100,
    );
    tempPassenger.mobile = formatTexts;
    // 输入的电话号码与当前的电话号码不一致且输入的电话号码正好11位，或者输入的内容大于最大字符,则进行格式化
    const isNeedFormat =
      (formatTexts !== passenger?.mobile && formatTexts?.length === 11) ||
      formatTextsOrigin?.length > 11;
    const formatMobile = isNeedFormat
      ? Utils.strFormat(tempPassenger.mobile, 1)
      : texts;
    this.setState({ passenger: tempPassenger, mobileError: '', formatMobile });
  };

  nameBlur = () => {
    const { nameBlur = noop, type = 'isd' } = this.props;
    const { passenger, currentType } = this.state;
    const { fullName } = passenger;

    // 如果是护照则不需要校验中文姓名
    if (currentType !== CertificateType.passport) {
      // 输入框失焦后需即时校验
      if (!this.validateFullName()) {
        return;
      }
    }
    // 国内身份证不需要转成英文
    if (type === 'isd' && currentType === CertificateType.id) {
      return;
    }
    nameBlur(fullName);
  };

  getIsMobileValueSpit = () => {
    const { passenger } = this.state;
    const { countryCode = '86' } = passenger;
    return !countryCode || countryCode === '86';
  };

  getContent = () => {
    const {
      onCancel,
      style,
      theme,
      isLeftIconCross = true,
      isHeadBorder,
      type = 'isd',
      availableCertificates,
      isNameReverse = false,
      isAdd = false,
      fullNameClick = noopShow,
      lastNameClick = noopShow,
      firstNameClick = noopShow,
      nationalityNameClick = noopShow,
      certificateNoClick = noopShow,
      mobileClick = noopShow,
      areaCodeClick = noopShow,
      contactClick = noopShow,
      tips,
      hasCheckBox = true,
    } = this.props;

    const {
      errorMessage,
      passenger,
      currentType,
      fullNameError,
      certificateTypeError,
      certificateNoError,
      mobileError,
      lastNameError,
      firstNameError,
      birthdayError,
      nationalityError,
      isAgreeTerms,
      formatMobile,
      formatCertificateNo,
    } = this.state;
    const {
      fullName,
      mobile,
      nationality,
      nationalityName,
      countryCode = '86',
      certificateList = [],
      lastName,
      firstName,
      birthday,
    } = passenger;
    const currentCertificate =
      certificateList.find(item => item.certificateType === currentType) || {};

    const { certificateNo = '' } = currentCertificate;
    const themes =
      (getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as ThemeProps) ||
      ({} as ThemeProps);
    const { backgroundColor = color.white } = themes;

    const birthdayAgeLabel = this.getBirthdayAgeLabel();
    const firstNameLabel = this.getFirstNameLabel();
    const lastNameLabel = this.getLastNameLabel();
    const isMobileValueSpit = this.getIsMobileValueSpit();
    const isCertificateInputSplit = currentType === CertificateType.id;

    const titleTip = tips?.find(item => item.style === DriverTipStyle.title);
    const contentTip = tips?.find(
      item => item.style === DriverTipStyle.content,
    );
    return (
      <View className={c2xStyles.wrapper} style={{ backgroundColor, ...style }}>
        <BbkComponentHeader
          title={isAdd ? texts.drivers_add : texts.drivers_edit}
          onPressLeft={onCancel}
          style={styles.headerStyle}
          // styleInner={styles.header}
          isBottomBorder={isHeadBorder}
          isLeftIconCross={isLeftIconCross}
          renderRight={this.RenderedRight()}
          leftIconTestID={UITestID.car_testid_comp_driver_edit_close}
        />

        <KeyboardAwareScrollView
          style={styles.contain}
          scrollEventThrottle={5}
          enableResetScrollToCoords={false}
          keyboardDismissMode="on-drag"
          keyboardShouldPersistTaps="handled"
          automaticallyAdjustContentInsets={false}
          keyboardOpeningTime={100}
          extraHeight={100}
        >
          {type === 'isd' && (
            <View className={c2xStyles.messageView}>
              <View className={c2xStyles.nameTip}>
                <BbkText
                  style={xMergeStyles([font.body3LightStyle, styles.message])}
                >
                  {texts.driver_add_name_tip}
                </BbkText>
              </View>
            </View>
          )}
          {type === 'osd' ? (
            <BbkTouchable
              onPress={() => this.onShowRuleModal()}
              testID={UITestID.car_testid_page_driveredit_showrule}
              className={c2xStyles.messageView}
            >
              <View>
                {type === 'osd' && (
                  <View className={c2xStyles.nameTip}>
                    <BbkText
                      style={xMergeStyles([
                        font.body3LightStyle,
                        styles.message,
                      ])}
                    >
                      {`${texts.driver_add_name_tip}。`}
                    </BbkText>
                    <BbkText
                      style={xMergeStyles([
                        font.body3LightStyle,
                        { color: color.blueBase },
                      ])}
                    >
                      {texts.driver_add_name_tip_rule}
                    </BbkText>
                    <BbkText
                      type="icon"
                      style={xMergeStyles([font.body3Style, styles.iconQues])}
                    >
                      {icon.arrowRight}
                    </BbkText>
                  </View>
                )}
              </View>
            </BbkTouchable>
          ) : (
            !!errorMessage && (
              <View className={c2xStyles.messageView}>
                <BbkText
                  style={xMergeStyles([font.body3Style, styles.message])}
                >
                  {errorMessage}
                </BbkText>
              </View>
            )
          )}
          <View className={c2xStyles.content}>
            <BbkInput
              value={fullName}
              onInputBlur={this.nameBlur}
              onInputFocus={() => fullNameClick()}
              onChangeText={this.onChangeName}
              testID={UITestID.car_testid_page_driveredit_input_fullname}
              placeholder={texts.driver_ctrip_name}
              title={texts.driver_cn_name}
              error={!!fullNameError}
              errorTip={fullNameError}
              removeLineHeight={true}
            />
            {(type === 'osd' ||
              isNameReverse ||
              (type === 'isd' &&
                this.state.currentType === CertificateType.passport)) && (
              <BbkInput
                value={lastName}
                onChangeText={this.onChangeLastName}
                onInputBlur={this.lastNameBlur}
                onInputFocus={() => lastNameClick()}
                title={lastNameLabel}
                testID={UITestID.car_testid_page_driveredit_input_lastname}
                placeholder={texts.driver_ctrip_lastname_placeholder}
                error={!!lastNameError}
                errorTip={lastNameError}
              />
            )}

            {(type === 'osd' ||
              (type === 'isd' &&
                this.state.currentType === CertificateType.passport)) && (
              <BbkInput
                value={firstName}
                onChangeText={this.onChangeFirstName}
                onInputBlur={this.firstNameBlur}
                onInputFocus={() => firstNameClick()}
                title={firstNameLabel}
                testID={UITestID.car_testid_page_driveredit_input_firstname}
                placeholder={texts.driver_ctrip_firstname_placeholder}
                error={!!firstNameError}
                errorTip={firstNameError}
              />
            )}

            {type === 'isd' && (
              <BbkDriverCertificateInput
                onPress={this.onChangeType}
                certificateType={currentType}
                availableCertificates={availableCertificates}
                type={type}
              />
            )}
            {type === 'isd' && (
              <View>
                {currentType === CertificateType.id && (
                  <BbkInput
                    value={formatCertificateNo}
                    title={texts.driver_certificate_no}
                    error={!!certificateNoError}
                    errorTip={certificateNoError}
                    spaceLength={0}
                    testID={
                      UITestID.car_testid_page_driveredit_input_certificate
                    }
                    onChangeText={this.onChangeNo}
                    onInputBlur={this.validateCertifacateNo}
                    placeholder={texts.driver_certificate_no_placeholder(
                      isdCertificateTypeList[currentType],
                    )}
                    onInputFocus={() => certificateNoClick()}
                    formatType={InputFormatType.id}
                  />
                )}
                {currentType !== CertificateType.id && (
                  <BbkInput
                    value={certificateNo}
                    title={texts.driver_certificate_no}
                    error={!!certificateNoError}
                    errorTip={certificateNoError}
                    testID={
                      UITestID.car_testid_page_driveredit_input_certificate
                    }
                    onChangeText={this.onChangeNo}
                    onInputBlur={this.validateCertifacateNo}
                    placeholder={texts.driver_certificate_no_placeholder(
                      isdCertificateTypeList[currentType],
                    )}
                    onInputFocus={() => certificateNoClick()}
                    formatType={InputFormatType.otherid}
                  />
                )}
              </View>
            )}

            {type !== 'isd' && (
              <BbkInput
                value={birthdayAgeLabel}
                editable={false}
                onPress={this.onChangeDateOfBirth}
                title={texts.drivers_dateOfBirth}
                testID={UITestID.car_testid_page_driveredit_input_birth}
                error={!!birthdayError}
                errorTip={birthdayError}
              />
            )}
            {type === 'osd' && (
              <BbkInput
                value={nationalityName}
                onPress={() => {
                  callAreaCode(this.onNationalityChange, {
                    showName: nationalityName,
                    countryCode,
                    countryName: nationalityName,
                  });
                  nationalityNameClick();
                }}
                testID={UITestID.car_testid_page_driveredit_input_nationality}
                editable={false}
                //@ts-ignore
                onChangeText={this.onNationalityChange}
                title={texts.driver_nationality}
                error={!!nationalityError}
                errorTip={nationalityError}
              />
            )}

            <BbkMobileInput
              title={texts.driver_phone}
              value={isMobileValueSpit ? formatMobile : mobile}
              placeholder={texts.driver_phone_placeholder}
              error={!!mobileError}
              errorTip={mobileError}
              testID={UITestID.car_testid_page_driveredit_input_mobile}
              leftTestID={UITestID.car_testid_page_driveredit_input_areacode}
              onChangeText={this.onChangeMobile}
              spaceLength={0}
              onInputBlur={this.validateMobile}
              onAreaCodeChange={this.onAreaCodeChange}
              onContactChange={this.onContactChange}
              onInputFocus={() => mobileClick()}
              onPressContact={() => contactClick()}
              onPressAreaCode={() => areaCodeClick()}
              isLeftChildren={type !== 'isd'}
              areaCode={countryCode}
              currentCountry={{
                countryCode,
              }}
            />
          </View>
          {!!titleTip?.content && !!contentTip?.content && (
            <View className={c2xStyles.tipsContainer}>
              <View className={c2xStyles.checkBoxWrap}>
                {!!hasCheckBox && (
                  <BbkText
                    type="icon"
                    className={c2xStyles.checkBox}
                    style={isAgreeTerms && styles.selectCheckBox}
                    testID={UITestID.car_testid_page_driveredit_agreeterms}
                    onPress={this.updateAgreeStatus}
                  >
                    {isAgreeTerms ? icon.newSquareTickFilled : icon.newSquare}
                  </BbkText>
                )}
                <BbkText className={c2xStyles.tipTitle} fontWeight="bold">
                  {titleTip.content}
                </BbkText>
              </View>
              <View className={c2xStyles.tipContent}>
                <BbkText className={c2xStyles.tipContentText}>
                  {contentTip.content}
                </BbkText>
              </View>
            </View>
          )}
        </KeyboardAwareScrollView>

        {type !== 'isd' && (
          <RentalCarsDatePicker
            ref={ref => (this.datePicker = ref)}
            visible
            mode="date"
            pickTitle={texts.drivers_dateOfBirth}
            pickerType={1}
            dateType="date"
            minTime={minDate}
            maxTime={maxDate}
            date={birthday || '1990-01-01'}
            onConfirm={this.handleDateChange}
            closeModalBtnTestID={
              UITestID.car_testid_rentalcarsdate_picker_closemask
            }
            leftIconTestID={
              UITestID.car_testid_rentalcarsdate_picker_header_lefticon
            }
            rightIconTestID={
              UITestID.car_testid_rentalcarsdate_picker_header_righticon
            }
          />
        )}
      </View>
    );
  };

  // 更新勾选框状态
  updateAgreeStatus = () => {
    this.setState({
      isAgreeTerms: !this.state.isAgreeTerms,
    });
  };

  render() {
    const { visible, onCancel, isModal = true } = this.props;
    return isModal ? (
      <BbkComponentModal
        modalVisible={visible}
        {...BbkComponentModalAnimationPreset('bottom')}
        animationOutDuration={druation.animationDurationBase}
        isMask={false}
        onRequestClose={onCancel}
        useModal={false}
      >
        {this.getContent()}
      </BbkComponentModal>
    ) : (
      this.getContent()
    );
  }
}
const styles = StyleSheet.create({
  saveBtn: {
    backgroundColor: color.white,
    position: 'absolute',
    right: 0,
    paddingTop: 0,
    paddingBottom: 0,
    minWidth: getPixel(88),
    minHeight: getPixel(88),
  },
  headerStyle: {
    backgroundColor: color.white,
  },
  header: {
    height: getPixel(88),
    paddingTop: getPixel(0),
    paddingBottom: getPixel(0),
  },
  contain: {
    flex: 1,
    backgroundColor: color.grayBg,
  },
  message: {
    color: color.fontPrimary,
  },
  iconQues: {
    color: color.blueBase,
  },
  selectCheckBox: {
    color: color.blueBase,
  },
});

export default withTheme(BbkDriverAddEditModal);
