@import '../../Common/src/Tokens/tokens/color.scss';

.limitTipCenterWrap {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.limitTipPic {
  width: 63px;
  height: 30px;
  margin-right: 8px;
}
.tipText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
  max-width: 535px;
}
.detailText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueBase;
}
.tipDetailTextIcon {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueBase;
}
.unLimitTipCenterWrap {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.unLimitTipPic {
  width: 84px;
  height: 30px;
  margin-right: 8px;
}
.warp {
  background-color: $white;
}
.dashLine {
  width: 686px;
  height: 2px;
  margin-bottom: 10px;
}
