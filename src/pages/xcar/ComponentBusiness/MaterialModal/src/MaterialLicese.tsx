import { isEmpty as lodashIsEmpty } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import {
  color as tokenColor,
  font,
  icon,
  layout,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTextItem from '@ctrip/rn_com_car/dist/src/Components/Basic/TextItem';

import {
  withTheme,
  getThemeAttributes,
} from '@ctrip/rn_com_car/dist/src/Theming';
import c2xStyles from './materialLiceseC2xStyles.module.scss';
import { Utils, CarLog } from '../../../Util/Index';

import { texts } from './Texts';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import { colors } from './Colors';
import {
  ItemTextProps,
  ILiceseBlockItemType,
  LicenseBlockProps,
  IMaterialLiceseBlock,
  DriverLicenseStatus,
  IMaterialLiceseProps,
} from './Types';

const { getPixel, useMemoizedFn } = BbkUtils;
const color = { ...tokenColor, ...colors };
const styles = StyleSheet.create({
  contentItem: {
    alignItems: 'flex-start',
    marginTop: getPixel(8),
    paddingBottom: 0,
  },
  licenseWrapper: {
    borderWidth: StyleSheet.hairlineWidth,
    marginTop: getPixel(24),
  },
  recommendWrapper: {
    height: getPixel(30),
    marginRight: getPixel(8),
    borderRadius: getPixel(4),
    paddingLeft: getPixel(6),
    paddingRight: getPixel(2),
    flexWrap: 'nowrap',
    overflow: 'hidden',
  },
  showMore: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    paddingTop: getPixel(32),
    paddingBottom: getPixel(32),
  },
  showMoreIcon: {
    marginLeft: getPixel(10),
    marginTop: getPixel(4),
  },
  licenseBlock: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    padding: getPixel(24),
  },
  marginTop32: {
    marginTop: getPixel(32),
  },
  button: {
    paddingTop: getPixel(12),
    paddingBottom: getPixel(12),
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
    borderRadius: getPixel(8),
    minWidth: getPixel(152),
    minHeight: getPixel(60),
    marginTop: getPixel(16),
    backgroundColor: color.bookingOptimizationBlue,
  },
  buttonText: {
    ...font.body3LightStyle,
  },
});

const ItemText: React.FC<ItemTextProps> = props => {
  const { theme, item, text } = props;
  return (
    <BbkText
      style={xMergeStyles([
        item.type === ILiceseBlockItemType.Normal && {
          ...font.body3LightStyle,
          color: theme.itemTextColor,
        },
        item.type === ILiceseBlockItemType.NumberAndGray && {
          ...font.caption2LightStyle,
          color: theme.noteTextColor,
        },
      ])}
    >
      {text}
    </BbkText>
  );
};

type IRecommendIconProps = {
  theme: any;
};

const RecommendIcon: React.FC<IRecommendIconProps> = props => {
  const { theme } = props;
  return (
    <View
      style={xMergeStyles([
        layout.flexRowWrap,
        styles.recommendWrapper,
        { backgroundColor: theme.recommendLabelColor },
      ])}
    >
      <BbkText type="icon" style={{ color: color.white }}>
        {icon.recommend2}
      </BbkText>
      <BbkText className={c2xStyles.recommendText}>{props.children}</BbkText>
    </View>
  );
};
const LicenseBlock: React.FC<LicenseBlockProps> = props => {
  const {
    block = {} as IMaterialLiceseBlock,
    theme,
    index,
    count,
    onPressHandleLicense = Utils.noop,
    onPressToLicense = Utils.noop,
  } = props;
  const { isRecommend, items = [], title, status, url } = block;
  const isShowRecommend = isRecommend && index === 0 && count > 1;
  const info = {
    certificateName: title,
  };
  const onPressHandleLicenseItem = useMemoizedFn(() => {
    onPressHandleLicense(url);
    CarLog.LogCode({
      name: '点击_取车材料页_立即办理',

      info,
    });
  });

  const onPressToLicenseItem = useMemoizedFn(() => {
    onPressToLicense();
    CarLog.LogCode({
      name: '点击_取车材料页_已办理',

      info,
    });
  });

  return (
    <View
      style={xMergeStyles([
        styles.licenseWrapper,
        { borderColor: theme.itemBlockBorderColor },
      ])}
    >
      <View
        style={xMergeStyles([
          layout.flexRowWrap,
          styles.licenseBlock,
          {
            backgroundColor: theme.itemTitleBgColor,
            borderColor: theme.itemBlockBorderColor,
          },
        ])}
      >
        {isShowRecommend && (
          <RecommendIcon theme={theme}>{texts.recommend}</RecommendIcon>
        )}
        <BbkText style={font.title3Style}>{title}</BbkText>
      </View>
      {items.map((item, i) => (
        <View
          key={i}
          className={c2xStyles.licenseWrapper2}
          style={{
            borderColor: theme.itemBlockBorderColor,
            borderTopWidth: i === 0 ? 0 : StyleSheet.hairlineWidth,
          }}
        >
          {item.title && (
            <ItemText item={item} text={item.title} theme={theme} />
          )}
          {item.content &&
            item.content.filter(Boolean).map((text, j) => (
              <View
                key={`${i}-${j}`}
                style={{
                  marginTop: j === 0 && !item.title ? 0 : getPixel(8),
                }}
              >
                <ItemText item={item} text={text} theme={theme} />
              </View>
            ))}
          {/** 办理等按钮追加在最后一条记录后 */}
          {i === items.length - 1 && status === DriverLicenseStatus.UnHandle && (
            <View className={c2xStyles.buttonWrap}>
              <BbkButton
                buttonStyle={styles.button}
                text={texts.handleRightNow}
                textStyle={styles.buttonText}
                testID={CarLog.LogExposure({
                  name: '曝光_取车材料页_立即办理',

                  info,
                })}
                onPress={onPressHandleLicenseItem}
              />
            </View>
          )}
          {i === items.length - 1 && status === DriverLicenseStatus.Handled && (
            <Touchable
              onPress={onPressToLicenseItem}
              className={c2xStyles.handledButton}
              testID={CarLog.LogExposure({
                name: '曝光_取车材料页_已办理',

                info,
              })}
            >
              <BbkText className={c2xStyles.handledText}>
                {texts.handled}
              </BbkText>
              <BbkText type="icon" className={c2xStyles.handledIcon}>
                {icon.arrowRight}
              </BbkText>
            </Touchable>
          )}
        </View>
      ))}
    </View>
  );
};

const MaterialLicese: React.FC<IMaterialLiceseProps> = props => {
  const {
    notes = [],
    title,
    noteTitle,
    blocks,
    isDefaultShowAll,
    onPressHandleLicense,
    onPressToLicense,
  } = props;
  let hasShowMore = false;
  if (isDefaultShowAll) {
    hasShowMore = false;
  } else {
    hasShowMore = blocks.length > 1;
  }

  const [collapsed, setCollapse] = React.useState(hasShowMore);

  const themes =
    (getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, props.theme) as any) || {};

  const {
    noteTextColor = color.materialNoteText,
    blockBorderColor = color.grayBorder,
    toggleBlockBtnColor = color.blueBase,
    itemBlockBorderColor = color.materialItemBlockBorder,
    itemTitleBgColor = color.materialItemTitleBg,
    itemTextColor = color.materialItemText,
    recommendLabelColor = color.materialRecommendLabel,

    textItemHeaderColor = color.fontSubDark,
    textItemDotColor = color.materialNoteText,
    textItemItemColor = color.materialNoteText,
  } = themes;

  const theme = {
    noteTextColor,
    blockBorderColor,
    toggleBlockBtnColor,
    itemBlockBorderColor,
    itemTitleBgColor,
    itemTextColor,
    recommendLabelColor,
  };

  const textItemTheme = {
    textItemHeaderColor,
    textItemDotColor,
    textItemItemColor,
  };
  const slicedBlocks = collapsed ? blocks.slice(0, 1) : blocks;

  const onToggleCollapse = () => {
    setCollapse(x => !x);
  };

  return (
    <View className={c2xStyles.marginTop40}>
      <BbkText style={font.title3Style}>{title}</BbkText>
      <View>
        {slicedBlocks.map((block, i) => (
          <LicenseBlock
            block={block}
            theme={theme}
            key={i}
            index={i}
            count={blocks.length}
            onPressHandleLicense={onPressHandleLicense}
            onPressToLicense={onPressToLicense}
          />
        ))}
        {hasShowMore && (
          <Touchable onPress={onToggleCollapse}>
            <View
              style={xMergeStyles([
                layout.flexRow,
                layout.flexCenter,
                styles.showMore,
                {
                  borderColor: theme.blockBorderColor,
                },
              ])}
            >
              <BbkText
                style={xMergeStyles([
                  font.body2LightStyle,
                  { color: theme.toggleBlockBtnColor },
                ])}
              >
                {collapsed ? texts.showMore : texts.showLess}
              </BbkText>
              <BbkText
                type="icon"
                style={xMergeStyles([
                  font.body2LightStyle,
                  styles.showMoreIcon,
                  { color: theme.toggleBlockBtnColor },
                ])}
              >
                {collapsed ? icon.circleArrowDown : icon.circleArrowUp}
              </BbkText>
            </View>
          </Touchable>
        )}
      </View>
      {!!noteTitle && (
        <BbkText
          style={xMergeStyles([
            font.body3LightStyle,
            styles.marginTop32,
            { color: theme.noteTextColor },
          ])}
        >
          {noteTitle}
        </BbkText>
      )}
      {!lodashIsEmpty(notes) && (
        <BbkTextItem
          type="TitleAndItem"
          dotStyle={{ marginTop: getPixel(14) }}
          headStyle={{ paddingBottom: 0 }}
          contentStyle={styles.contentItem}
          theme={textItemTheme}
          items={notes.map(note => ({ title: note }))}
        />
      )}
    </View>
  );
};

export default withTheme(MaterialLicese);
