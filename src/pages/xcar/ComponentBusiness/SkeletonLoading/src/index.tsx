import Image from '@c2x/components/Image';
import React, { Component, ReactNode, CSSProperties } from 'react';
import {
  XView as View,
  xMergeStyles,
  XAnimated,
  xCreateAnimation,
} from '@ctrip/xtaro';
import BbkChannel, { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { getPixel } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './skeletonLoading.module.scss';
import { Utils, CarABTesting, GetABCache } from '../../../Util/Index';
import { ImageUrl } from '../../../Constants/Index';

export enum PageType {
  List = 'list',
  Batch_List_Full = 'batch_list_full',
  Batch_List_Most = 'batch_list_most',
  Batch_List_Half = 'batch_list_half',
  Product = 'product',
  ProductHalf = 'productHalf',
  Area = 'area',
  CarRentalCenter = 'carRentalCenter',
  Ranking = 'ranking',
  VendorListLoading = 'vendorListLoading',
  VendorListVehicleExpand = 'VendorListVehicleExpand',
  VendorListVehicle = 'VendorListVehicle',
  BookingLoadingAll = 'BookingLoadingAll',
  BookingLoadingPart = 'BookingLoadingPart',
  BookingLoadingHalf = 'BookingLoadingHalf',
  BookingVehicle = 'BookingVehicle',
  BookingAddress = 'BookingAddress',
  BookingTags = 'BookingTags',
  BookingTagsAndLimits = 'BookingTagsAndLimits',
  BookingFreeCancel = 'BookingFreeCancel',
  BookingDriver = 'BookingDriver',
  BookingCoupon = 'BookingCoupon',
  RecommendVehicle = 'RecommendVehicle',
  OrderDetailFirstScreen1 = 'OrderDetailFirstScreen1',
  OrderDetailFirstScreen2 = 'OrderDetailFirstScreen2',
  LocationPOI = 'LocationPOI',
  LocationCity = 'LocationCity',
  CancelInfo = 'CancelInfo',
  HomeFlowRecommend = 'HomeFlowRecommend',
  Fulfillment = 'Fulfillment',
  BookVehicleISD = 'BookVehicleISD',
  BookTimeISD = 'BookTimeISD',
  BookCancelISD = 'BookCancelISD',
  BookingIsdDriver = 'BookingIsdDriver',
  BookingCouponNew = 'BookingCouponNew',
  BookingIncentive = 'BookingIncentive',
  BookingIsdLoadingAll = 'BookingIsdLoadingAll',
}

interface SkeletonLoadingPropsType {
  visible: boolean;
  pageName?: PageType;
  isShowBreathAnimation?: boolean;
  duration?: number;
  style?: CSSProperties;
  imageStyle?: CSSProperties;
  children?: ReactNode | ReactNode[];
  isSide?: boolean;
  showImg?: boolean;
  centerDom?: ReactNode | ReactNode[];
}

interface SkeletonLoadingStateType {
  visible: boolean;
  // @ts-ignore
  opacityAnim: Animated.Value;
}

class BbkComponentSkeletonLoading extends Component<
  SkeletonLoadingPropsType,
  SkeletonLoadingStateType
> {
  opacity: number;

  curOpacity: number;

  isTurnOnAnimation: boolean;

  constructor(props) {
    super(props);
    this.state = {
      visible: props.visible,
      opacityAnim: null,
    };
    this.opacity = 0.4;
    this.curOpacity = 1;
    this.isTurnOnAnimation = true;
  }

  componentDidMount() {
    if (this.props.isShowBreathAnimation !== false) {
      this.animateOpacity();
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.visible !== this.state.visible) {
      if (nextProps.visible) {
        this.animateOpacity();
      }
      this.setState({
        visible: nextProps.visible,
      });
    }
  }

  componentWillUnmount() {
    this.isTurnOnAnimation = false;
  }

  onAnimateOpacityEnd = () => {
    const { isShowBreathAnimation = true } = this.props;
    if (isShowBreathAnimation && this.isTurnOnAnimation) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      this.state.visible && this.animateOpacity();
    }
  };

  animateOpacity() {
    const num = this.curOpacity === 1 ? this.opacity : 1;
    const { duration = 800 } = this.props;
    const animateRef = xCreateAnimation({
      duration, // 和视觉确认 列表页骨架屏duration为800ms透明度改为0.5
      timingFunction: 'ease',
    });
    this.curOpacity = num;
    animateRef.opacity(num).step();
    this.setState({
      opacityAnim: animateRef.export(),
    });
  }

  renderItem = () => {
    let itemDom: any = null;
    const { pageName, children, imageStyle, isSide } = this.props;
    const darkUrl =
      BbkChannel.getThemingType() === BbkChannel.THEMING.dark ? '.dark' : '';

    const sideUrl = isSide ? '.side' : '';

    switch (pageName) {
      case PageType.List:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.BBK_IMAGE_PATH}listSkeletonLoading${darkUrl}${sideUrl}.jpg`}
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: BbkUtils.getPixel(1826) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.Product:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`https://pic.c-ctrip.com/car/osd/mobile/common/detailSkeletonLoading${darkUrl}.png`}
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: BbkUtils.vh(100) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.ProductHalf:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`https://pic.c-ctrip.com/car/osd/mobile/common/detailHalfSkeletonLoading${darkUrl}.png`}
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: BbkUtils.vh(100) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.Area:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.osdMobilePath}/common/cnAreaSkeletonLoading.png`}
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: BbkUtils.vh(100) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.Batch_List_Full:
        itemDom = (
          <Image
            resizeMode="stretch"
            src="https://images3.c-ctrip.com/BUS/tripRn/skeleton_full.png"
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: getPixel(1495) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.Batch_List_Most:
        itemDom = (
          <Image
            resizeMode="stretch"
            src="https://images3.c-ctrip.com/BUS/tripRn/skeleton_most_long.png"
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: BbkUtils.getPixel(1800) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.Batch_List_Half:
        itemDom = (
          <Image
            resizeMode="stretch"
            src="https://images3.c-ctrip.com/BUS/tripRn/skeleton_half_long.png"
            style={xMergeStyles([
              {
                width: BbkUtils.getPixel(610),
                height: BbkUtils.getPixel(1800),
              },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.CarRentalCenter:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.BBK_IMAGE_PATH}car_rental_center_loading.jpg`}
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: BbkUtils.vh(100) },
              imageStyle,
            ])}
          />
        );

        break;

      case PageType.Ranking:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={
              CarABTesting.isInfoFlow()
                ? `${ImageUrl.BBK_IMAGE_PATH}info_skeleton.png`
                : Utils.compatImgUrlWithWebp(
                    `${ImageUrl.DIMG04_PATH}0AS3c1200095ltnal6CAE.png`,
                  )
            }
            style={imageStyle}
          />
        );

        break;
      case PageType.VendorListLoading:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg6c12000hk6f9cq7DBD.png`}
            style={xMergeStyles([
              {
                width: BbkUtils.vw(100) - getPixel(48),
                height: getPixel(274),
              },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.VendorListVehicleExpand:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg2312000j7smv9oC30E.png`}
            className={c2xStyles.vendorListVehicleExpand}
            style={imageStyle}
          />
        );

        break;
      case PageType.VendorListVehicle:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg1a12000j7smttxD8F7.png`}
            className={c2xStyles.vendorListVehicle}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookingLoadingAll:
        itemDom = (
          <Image
            resizeMode="stretch"
            src="https://pages.c-ctrip.com/cars/bbk/resource/booking-loading-all.png"
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: getPixel(2021) },
              imageStyle,
            ])}
          />
        );
        break;
      case PageType.BookingIsdLoadingAll:
        itemDom = (
          <Image
            resizeMode="stretch"
            src="https://dimg04.c-ctrip.com/images/1tg4y12000liegobb3A5C.png"
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: getPixel(2021) },
              imageStyle,
            ])}
          />
        );
        break;
      case PageType.BookingLoadingPart:
        itemDom = (
          <Image
            resizeMode="stretch"
            src="https://pages.c-ctrip.com/cars/bbk/resource/booking-loading-part.png"
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: getPixel(1400) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.BookingLoadingHalf:
        itemDom = (
          <Image
            resizeMode="stretch"
            src="https://pages.c-ctrip.com/cars/bbk/resource/booking-loading-half.png"
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: getPixel(854) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.RecommendVehicle:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.BBK_IMAGE_PATH}vendor_list_item_loading.png`}
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: getPixel(293) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.LocationPOI:
        itemDom = (
          <Image
            resizeMode="stretch"
            src="https://pages.c-ctrip.com/cars/bbk/resource/location-poi-loading.png"
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: getPixel(1345) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.OrderDetailFirstScreen1:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.componentImagePath}OrderDetail/firstScreenLoading1.png`}
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: getPixel(446) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.OrderDetailFirstScreen2:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.componentImagePath}OrderDetail/firstScreenLoading2.png`}
            style={xMergeStyles([
              {
                width: BbkUtils.vw(100),
                height: getPixel(796),
                marginTop: getPixel(16),
              },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.LocationCity:
        itemDom = (
          <Image
            resizeMode="stretch"
            src="https://pages.c-ctrip.com/cars/bbk/resource/location-city-loading.png"
            style={xMergeStyles([
              { width: BbkUtils.vw(100), height: getPixel(1328) },
              imageStyle,
            ])}
          />
        );

        break;
      case PageType.CancelInfo:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.componentImagePath}OrderCancel/cancelInfoLoading.png`}
            className={c2xStyles.cancelInfoImg}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookingVehicle:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.BBK_IMAGE_PATH}booking_vehicle_loading.png`}
            className={c2xStyles.bookingVehicleImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookingAddress:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.BBK_IMAGE_PATH}booking_address_loading.png`}
            className={c2xStyles.bookingAddressImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookingTags:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.BBK_IMAGE_PATH}booking_tags_loading.png`}
            className={c2xStyles.bookingTagsImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookingTagsAndLimits:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg5512000cdjto3y49EC.png`}
            className={c2xStyles.bookingTagsLimits}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookingFreeCancel:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.BBK_IMAGE_PATH}free_cancel_bg.png`}
            className={c2xStyles.bookingFreeCancelImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookingDriver:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.BBK_IMAGE_PATH}booking_driver_loading.png`}
            className={c2xStyles.bookingDriverImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookingCoupon:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.BBK_IMAGE_PATH}booking_coupon_loading.png`}
            className={c2xStyles.bookingCouponImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookingCouponNew:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg1j12000lgh367jB55A.png`}
            className={c2xStyles.bookingCouponImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.HomeFlowRecommend:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.componentImagePath}FlowWithVehicle/flowEmpty.png`}
            className={c2xStyles.flowWithVehicleImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.Fulfillment:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg5l12000f77dpmbC3D4.png`}
            className={c2xStyles.fulfillmentImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookVehicleISD:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg4q12000l9cjkuq3136.png`}
            className={c2xStyles.bookVehicleImage}
            style={imageStyle}
          />
        );

        break;
      case PageType.BookTimeISD:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg4a12000l9cj58s0789.png`}
            className={c2xStyles.bookTimeImage}
            style={imageStyle}
          />
        );
        break;
      case PageType.BookCancelISD:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg5312000l9cjcet7C60.png`}
            className={c2xStyles.bookCancelImage}
            style={imageStyle}
          />
        );
        break;
      case PageType.BookingIsdDriver:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg3h12000lbd4fyi9A22.png`}
            className={c2xStyles.bookingIsdDriver}
            style={imageStyle}
          />
        );
        break;
      case PageType.BookingIncentive:
        itemDom = (
          <Image
            resizeMode="stretch"
            src={`${ImageUrl.DIMG04_PATH}1tg0612000lg75gdq6295.png`}
            className={c2xStyles.bookingIncentive}
            style={imageStyle}
          />
        );
        break;
      default:
        if (children) {
          itemDom = children;
        }
    }
    return itemDom;
  };

  render() {
    const { showImg = true, centerDom = null } = this.props;
    const { opacityAnim } = this.state;
    const itemDom = this.renderItem();
    if (!itemDom) return null;
    return (
      // @ts-ignore
      <View className={c2xStyles.baseBg} style={this.props.style}>
        <XAnimated.View
          style={{ opacity: 1 }}
          animation={opacityAnim}
          onTransitionEnd={this.onAnimateOpacityEnd}
        >
          {showImg && itemDom}
        </XAnimated.View>
        {centerDom}
      </View>
    );
  }
}
export default BbkComponentSkeletonLoading;
