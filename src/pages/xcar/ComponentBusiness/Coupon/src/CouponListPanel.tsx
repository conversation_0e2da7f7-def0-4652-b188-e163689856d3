/* eslint-disable @typescript-eslint/no-use-before-define */
import FlatList from '@c2x/components/FlatList';
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import { XView as View, xMergeStyles, XBoxShadow } from '@ctrip/xtaro';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import { CouponInfo } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailDtoType';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './couponListPanelC2xStyles.module.scss';
import CouponItem from './CouponItem';
import NoMatch from '../../ListNoMatch';
import { ImgType } from '../../ListNoMatch/src/NoMatchImg';
import { texts } from './Texts';
import { UITestID } from '../../../Constants/Index';

const { fixIOSOffsetBottom, getPixel, useMemoizedFn } = BbkUtils;
interface IEmptySelectionItemProps {
  onPress?: (coupon: CouponInfo | null) => void;
  selected: boolean;
}

const EmptySelectionItem: React.FC<IEmptySelectionItemProps> = props => {
  const { onPress, selected } = props;
  const handleEmptySelection = useMemoizedFn(() => {
    onPress(null);
  });
  const getSelectionStyle = useMemoizedFn(() => ({
    fontSize: getPixel(40),
    color: selected ? color.blueBase : color.fontGrayBlue,
  }));
  return (
    <XBoxShadow
      coordinate={{ x: 0, y: getPixel(4) }}
      color="rgba(0, 0, 0, 0.07)"
      opacity={1}
      blurRadius={getPixel(6)}
      elevation={2}
    >
      <Touchable
        style={xMergeStyles([
          layout.flexRow,
          styles.contentWrapper,
          styles.shadow,
        ])}
        onPress={handleEmptySelection}
        testID={UITestID.car_testid_comp_booking_couponModal_usable_item}
      >
        <BbkText
          style={xMergeStyles([layout.flex1, font.subTitle1MediumFlatStyle])}
          fontWeight="medium"
        >
          {texts.noUseCoupons}
        </BbkText>
        <BbkText type="icon" style={getSelectionStyle()}>
          {selected ? icon.circleTickFilledThin : icon.circleLineThin}
        </BbkText>
      </Touchable>
    </XBoxShadow>
  );
};
interface INoCoupon {
  usable: boolean;
}
const NoCoupon: React.FC<INoCoupon> = ({ usable }) => (
  <View className={c2xStyles.noCoupon}>
    <NoMatch
      type={ImgType.No_Coupon}
      title={
        usable ? texts.noAvailableCouponTips : texts.noUnavailableCouponTips
      }
      isShowOperateButton={false}
      isShowRentalDate={false}
      subTitle=""
      titleStyle={styles.noCouponTitle}
    />
  </View>
);

interface ICouponListProps {
  coupons: CouponInfo[];
  tabLabel?: string;
  selectedCoupon?: CouponInfo;
  onSelected?: (coupon: CouponInfo | null) => void;
  usable?: boolean;
}
const CouponListPanel: React.FC<ICouponListProps> = props => {
  const { coupons, onSelected, selectedCoupon, usable } = props;
  const renderItem = useMemoizedFn(({ item }) => {
    return (
      <CouponItem
        key={item.promotionId}
        selected={selectedCoupon?.code === item.code}
        onPress={onSelected}
        renderData={item}
        usable={usable}
      />
    );
  });
  const renderFooter = useMemoizedFn(() => {
    return (
      <View style={styles.footer}>
        {usable && (
          <EmptySelectionItem
            onPress={onSelected}
            selected={!selectedCoupon || !Object.keys(selectedCoupon).length}
          />
        )}
      </View>
    );
  });

  const keyExtractor = useMemoizedFn((item, i) => `${item.code}_${i}`);
  if (!coupons?.length) {
    return <NoCoupon usable={usable} />;
  }
  return (
    <FlatList
      style={styles.couponContentWrapper}
      data={coupons}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      ListFooterComponent={renderFooter}
    />
  );
};
const styles = StyleSheet.create({
  couponContentWrapper: {
    backgroundColor: color.white,
    flex: 1,
    paddingLeft: getPixel(14),
    paddingRight: getPixel(14),
    paddingTop: getPixel(22),
  },
  footer: {
    paddingBottom: fixIOSOffsetBottom(24),
  },
  contentWrapper: {
    backgroundColor: color.white,
    marginTop: getPixel(10),
    marginLeft: getPixel(8),
    marginRight: getPixel(8),
    paddingTop: getPixel(32),
    paddingBottom: getPixel(32),
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    borderRadius: getPixel(16),
  },
  shadow: {
    zIndex: 10,
  },
  noCouponTitle: {
    color: color.fontPrimary,
  },
});

export default CouponListPanel;
