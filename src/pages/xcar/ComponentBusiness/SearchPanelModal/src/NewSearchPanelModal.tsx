/* eslint-disable @typescript-eslint/no-use-before-define */
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import Header from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkComponentPageModal } from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import { color, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils, BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './newSearchPanelModalC2xStyles.module.scss';
import { UITestID } from '../../../Constants/Index';
import { NewSearchPanel } from '../../NewSearchPanel';
import { texts } from './Texts';
import { IbkSearchPanelModal } from './Types';

const { getPixel, ensureFunctionCall, selector } = BbkUtils;
const { DEFAULT_HEADER_HEIGHT } = BbkConstants;

const NewSearchPanelModal: React.FC<IbkSearchPanelModal> = ({
  onCancel,
  visible,
  useCRNModal,
  location = 'top',
  animateType = 'slideDown',
  wrapperStyle,
  closeIconStyle,
  headerStyle,
  headerText = texts.searchPanelTitle,
  headerTextStyle,
  tip,
  ...searchPanelProps
}) => {
  return (
    <BbkComponentPageModal
      visible={visible}
      location={location}
      animateType={animateType}
      onMaskPress={onCancel}
      isCrnModal={!!useCRNModal}
      closeModalBtnTestID={
        UITestID.car_testid_page_list_search_pannel_close_mask
      }
    >
      <View
        style={wrapperStyle}
        className={c2xStyles.wrapper}
        testID={UITestID.car_testid_page_list_search_pannel_modal}
      >
        <Header
          renderLeft={<View />}
          renderRight={
            <CloseIcon onPress={onCancel} closeIconStyle={closeIconStyle} />
          }
          renderContent={
            <View className={c2xStyles.headerContentWrap}>
              <Text
                style={xMergeStyles([styles.searchPanelTitle, headerTextStyle])}
                fontWeight="medium"
              >
                {headerText}
              </Text>
            </View>
          }
          style={xMergeStyles(styles.header, headerStyle)}
        />
        {tip}
        <NewSearchPanel {...searchPanelProps} />
      </View>
    </BbkComponentPageModal>
  );
};

const CloseIcon = ({ onPress, align = 'left', closeIconStyle }) => (
  <Touchable
    style={xMergeStyles([
      styles.side,
      selector(align === 'right', styles.sideRight, styles.sideLeft),
    ])}
    testID={UITestID.car_testid_page_list_search_pannel_close}
    onPress={() => {
      ensureFunctionCall(onPress);
    }}
  >
    <Text type="icon" className={c2xStyles.iconText} style={closeIconStyle}>
      {icon.cross}
    </Text>
  </Touchable>
);

const styles = StyleSheet.create({
  searchPanelTitle: {
    ...font.title1MediumStyle,
    color: color.fontPrimary,
  },
  header: {
    borderBottomWidth: 0,
    elevation: 0,
  },
  side: {
    position: 'absolute',
    height: DEFAULT_HEADER_HEIGHT,
    justifyContent: 'center',
  },
  sideLeft: {
    paddingLeft: getPixel(32),
    left: 0,
  },
  sideRight: {
    paddingRight: getPixel(32),
    right: 0,
  },
});

export default NewSearchPanelModal;
