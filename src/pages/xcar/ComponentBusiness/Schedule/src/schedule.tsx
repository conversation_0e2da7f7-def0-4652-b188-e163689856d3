import { isEqual as lodashIsEqual } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, ReactChildren, CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import NumberText from '@ctrip/rn_com_car/dist/src/Components/Basic/NumberText';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  color,
  font,
  icon,
  layout,
  tokenType,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { spaceS } from '@ctrip/rn_com_car/dist/src/Tokens/tokens/space';
import c2xStyles from './scheduleC2xStyles.module.scss';
import BbkDetailsBlock from '../../DetailsBlock';
import { GetAB, Utils } from '../../../Util/Index';

const { getPixel, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  iconStyle: {
    marginRight: spaceS,
    marginTop: getPixel(3),
  },
  subDescStyle: {
    marginTop: getPixel(10),
    color: color.fontSecondary,
    ...font.caption1LightStyle,
    lineHeight: getLineHeight(30),
  },
  title: {
    ...font.subTitle1BoldStyle,
    color: color.fontPrimary,
    lineHeight: getLineHeight(38),
    height: getPixel(38),
  },
  titleStyle: {
    color: color.fontPrimary,
    ...font.subTitle1MediumStyle,
    lineHeight: getLineHeight(38),
    height: getPixel(38),
  },
  iconText: {
    ...font.caption1LightStyle,
    lineHeight: getLineHeight(30),
    height: getPixel(30),
    marginRight: getPixel(8),
  },
  rightIconStyle: {
    fontSize: getPixel(24),
    left: getPixel(-8),
    top: getPixel(-1),
  },
  titleDesc: {
    ...font.caption1LightStyle,
    paddingBottom: 0,
    lineHeight: getLineHeight(30),
  },
});

interface IInsuranceDetail {
  title: string;
  renderTitle?: string;
  detail?: string | React.ReactNode;
  subTitle?: string;
  content?: string;
  testID?: string;
  taTestID?: string;
  rightTitleTestID?: string;
  detailPress?: () => void;
  titleIcon?: string;
  titleIconStyle?: CSSProperties;
  isShowRightIcon?: boolean;
  rightIconTextStyle?: CSSProperties;
  rightIconStyle?: CSSProperties;
  numberOfLines?: number;
  isShowRedIcon?: boolean;
  titleTextStyle?: CSSProperties;
  contentTextStyle?: CSSProperties;
  style?: CSSProperties;
  children?: ReactChildren;
}

interface IScheduleTitleWithDesc {
  title: string;
  titleDesc?: string;
}

const isEqual = (prevProps, nextProps) => lodashIsEqual(prevProps, nextProps);

const ScheduleTitleWithDescComp = (props: IScheduleTitleWithDesc) => {
  const { title, titleDesc } = props;
  return (
    <View style={layout.betweenHorizontal}>
      <NumberText
        style={xMergeStyles([
          styles.title,
          Utils.isCtripIsd() && styles.titleStyle,
        ])}
      >
        {title}
      </NumberText>
      <NumberText style={styles.titleDesc}>{titleDesc}</NumberText>
    </View>
  );
};

ScheduleTitleWithDescComp.defaultProps = {
  titleDesc: '',
};

export const ScheduleTitleWithDesc: React.FC<IScheduleTitleWithDesc> = memo(
  ScheduleTitleWithDescComp,
);

const ScheduleComponent = (props: IInsuranceDetail) => {
  const {
    title,
    renderTitle,
    detail,
    subTitle,
    titleTextStyle,
    content,
    contentTextStyle,
    detailPress,
    titleIconStyle,
    titleIcon,
    isShowRightIcon,
    rightIconTextStyle,
    rightIconStyle,
    children,
    numberOfLines,
    isShowRedIcon,
    style,
    testID,
    taTestID,
    rightTitleTestID,
  } = props;
  const isISDInterestPoints = GetAB.isISDInterestPoints();

  return (
    <View
      testID={testID}
      key={BbkUtils.uuid()}
      className={c2xStyles.AmountBlock}
      style={style}
    >
      <View style={layout.flexRow}>
        {BbkUtils.selector(
          titleIcon,
          <Text
            type="icon"
            style={xMergeStyles([styles.iconStyle, titleIconStyle])}
          >
            {titleIcon || icon.countDown}
          </Text>,
        )}
        <BbkDetailsBlock
          style={xMergeStyles([
            { paddingLeft: 0, paddingRight: 0, paddingTop: 0 },
            layout.flex1,
          ])}
          title={title}
          renderTitle={renderTitle}
          titleStyle={{ paddingBottom: 0 }}
          titleTextStyle={xMergeStyles([styles.title, titleTextStyle])}
          subTitle={subTitle}
          subTitleStyle={xMergeStyles([
            font.body3Style,
            { color: color.fontPrimary, marginTop: getPixel(14) },
          ])}
          subDescStyle={xMergeStyles([styles.subDescStyle, contentTextStyle])}
          subDesc={content}
          rightIconText={detail}
          rightColorType={
            isISDInterestPoints
              ? tokenType.ColorType.DeepBlue
              : tokenType.ColorType.Blue
          }
          rightIconStyle={xMergeStyles([styles.rightIconStyle, rightIconStyle])}
          rightIconTextStyle={xMergeStyles([
            styles.iconText,
            rightIconTextStyle,
          ])}
          isShowRightIcon={isShowRightIcon}
          onPress={detailPress}
          isTitleRight={true}
          numberOfLines={numberOfLines}
          isShowRedIcon={isShowRedIcon}
          taTestID={taTestID}
          rightTitleTestID={rightTitleTestID}
        />
      </View>
      {children}
    </View>
  );
};

ScheduleComponent.defaultProps = {
  renderTitle: '',
  detail: '',
  subTitle: undefined,
  content: undefined,
  testID: '',
  detailPress: () => {},
  titleIcon: '',
  titleIconStyle: {},
  isShowRightIcon: true,
  rightIconTextStyle: {},
  rightIconStyle: {},
  numberOfLines: 1,
  isShowRedIcon: false,
  titleTextStyle: {},
  contentTextStyle: {},
  style: {},
  children: null,
};

const Schedule: React.FC<IInsuranceDetail> = memo(ScheduleComponent, isEqual);
export default withTheme(Schedule);
