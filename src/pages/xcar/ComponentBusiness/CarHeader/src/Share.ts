import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import Share from '@c2x/apis/Share';
import { Channel, AppContext } from '../../../Util/Index';
import { CommonEnums, ImageUrl, ChannelId } from '../../../Constants/Index';
import { getStore } from '../../../State/StoreRef';
import { texts } from './Texts';

const { IEntranceName, IShareType } = CommonEnums;

const getIsHome = () =>
  AppContext?.PageInstance?.getPageId?.() &&
  AppContext?.PageInstance?.getPageId?.() === Channel?.getPageId?.()?.Home?.ID;

const getIsList = () =>
  AppContext?.PageInstance?.getPageId?.() &&
  AppContext?.PageInstance?.getPageId?.() === Channel?.getPageId?.()?.List?.ID;

const getShareDataList = () => {
  const { rentalDate, rentalLocation } =
    getStore()?.getState()?.LocationAndDate || {};
  const pickUpTime = dayjs(rentalDate?.pickUp?.dateTime).format(
    'YYYYMMDDHHmmss',
  );
  const dropOffTime = dayjs(rentalDate?.dropOff?.dateTime).format(
    'YYYYMMDDHHmmss',
  );
  const { pickUp, dropOff } = rentalLocation || {};

  const searchInfo = `&st=ser&fromurl=common&pcid=${pickUp?.cid}&rcid=${dropOff?.cid}&ptime=${pickUpTime}&rtime=${dropOffTime}&plat=${pickUp?.area?.lat}&plng=${pickUp?.area?.lng}&plname=${pickUp?.area?.name}&rlat=${dropOff?.area?.lat}&rlng=${dropOff?.area?.lng}&rlname=${dropOff?.area?.name}&channelId=${ChannelId.SHARE}`;
  const baseLinkUrl = `https://m.ctrip.com/webapp/cw/car/isd/Market.html?apptype=ISD_C_CW_MAIN${searchInfo}&landingto=`;
  const commonMiniProgramParams = `&apptype=ISD_C_WX&sharedList=true`;
  let linkUrl = ''; // H5链接拼接规则：http://conf.ctripcorp.com/pages/viewpage.action?pageId=360393219
  let miniProgramPath = ''; // 微信主版小程序链接拼接规则：http://conf.ctripcorp.com/pages/viewpage.action?pageId=1041924345

  if (getIsHome()) {
    linkUrl = `${baseLinkUrl}Home`;
    miniProgramPath = `/pages/carNew/isd/indexNew/index?landingto=home${commonMiniProgramParams}${searchInfo}`;
  } else if (getIsList()) {
    linkUrl = `${baseLinkUrl}List`;
    miniProgramPath = `/pages/carNew/isd/subPages/list/index?landingto=list${commonMiniProgramParams}${searchInfo}`;
  }

  if (linkUrl && miniProgramPath) {
    return [
      {
        shareType: IShareType.WeixinFriend,
        imageUrl: `${ImageUrl.CTRIP_EROS_URL}car_share_cover_img.png`,
        title: texts.carShareTitle,
        linkUrl,
        miniProgramPath,
      },
    ];
  }
  return [];
};
const onPressShare = () => {
  const dataList = getShareDataList();
  const businessCode = getIsHome() ? 'carRental' : 'carrental';
  // @ts-ignore
  Share.customShare({ dataList, businessCode }, () => {});
};
let lastTimestamp = 0;
const onDidClickOnEntrance = data => {
  if (data?.entranceName === IEntranceName.Share) {
    const timestamp = new Date().getTime();
    if (timestamp - lastTimestamp > 200) {
      onPressShare();
      lastTimestamp = timestamp;
    }
  }
};
export default onDidClickOnEntrance;
