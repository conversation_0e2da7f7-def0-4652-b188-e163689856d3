import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import Platform from '@c2x/apis/Platform';
import SideToolBox from '@c2x/components/SideToolBox';
import React, {
  memo,
  useContext,
  useState,
  useCallback,
  CSSProperties,
} from 'react';
import {
  XView as View,
  xRouter,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';

import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
// @ts-ignore 升级072

import {
  BbkUtils,
  DateFormatter,
  BbkConstants,
  BbkChannel,
} from '@ctrip/rn_com_car/dist/src/Utils';
import { color, layout, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkHeader, {
  ICHeader,
  FloatHeaderContext,
  getOpacityIconWrapStyle,
  getOpacityIconStyle,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import {
  getThemeAttributes,
  withTheme,
} from '@ctrip/rn_com_car/dist/src/Theming';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import FloatHoc from './FloatHoc';
import BbkProductNameItem from './ProductNameItem';
import { texts } from './Texts';
import { getSideToolBoxConfig, getMaxWidth, getFixPixel } from './Utils';
import { GetAB, Utils, CarLog } from '../../../Util/Index';
import { ImageUrl, UITestID, CommonEnums, LogKey } from '../../../Constants/Index';
import { getServerRequestId } from '../../../Global/Cache/ListReqAndResData';
import c2xHeaderContentStyle from './indexC2xHeaderContentStyle.module.scss';
import c2xStyles from './carHeader.module.scss';
import c2xDateContentContentStyle from './indexC2xDateContentContentStyle.module.scss';
import c2xLocationContentStyle from './indexC2xLocationContentStyle.module.scss';
import c2xHeaderRightSideStyle from './indexC2xHeaderRightSideStyle.module.scss';
import onDidClickOnEntrance from './Share';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { selector, htmlDecode, isAndroid, isIos, getLineHeight } = BbkUtils;
const { DEFAULT_HEADER_HEIGHT } = BbkConstants;
const noop = () => {};

interface DateContentProps {
  ptime: Date;
  rtime: Date;
  enablePress?: boolean;
  theme: any;
  wrapStyle?: CSSProperties;
  textStyle?: CSSProperties;
  splitIconStyle?: CSSProperties;
  splitIconContainerStyle?: CSSProperties;
  ptimeTestID?: string;
  rtimeTestID?: string;
}

const iconWrapWidth = getFixPixel(64);
const iconTouchWrapWidth = getFixPixel(88);
export interface carHeaderProps extends ICHeader {
  handleBackPress?: () => void;
  data: {
    ptime: Date;
    rtime: Date;
    pickupLocation: {
      locationType: number;
      locationCode: string;
      locationName: string;
    };
    returnLocation: {
      locationType: number;
      locationCode: string;
      locationName: string;
    };
  };
  channelType?: string;
  isShowProductName?: boolean;
  isHideHeader?: boolean;
  isSmallImage?: boolean;
  name?: string;
  isSimilar?: boolean;
  isHotLabel?: boolean;
  licenseTag?: string;
  licenseType?: string;
  pageId: string;
  enableChange?: boolean;
  enablePress?: boolean;
  showSearchSelectorWrap: () => void;
  showRight: boolean;
  currency?: string;
  isShowCurrency?: boolean;
  onPressHeaderRight?: (data: any) => void;
  renderRight?: any;
  page?: any;
  style?: CSSProperties;
  bizType?: string;
  toolBoxCustomerJumpUrl?: string;
  toolBoxStyleType?: number;
  isShare?: boolean;
  goShare?: () => void;
  toolBoxStyle?: CSSProperties;
  theme: any;
  isRenderContent?: boolean;
  isRenderLeft?: boolean;
  isHideRight?: boolean;
  sideLeftStyle?: CSSProperties;
  opacityAnimation?: any;
  fixOpacityAnimation?: any;
  isHideAnimation?: boolean;
  isFail?: boolean;
  vrUrl?: string;
}
const styles = StyleSheet.create({
  leftLocation: { paddingRight: getFixPixel(34), flex: 1 },
  rightLocation: { flex: 1, paddingLeft: getFixPixel(34) },
  width64: { width: getFixPixel(64) },
  ml24: { marginLeft: getFixPixel(24) },
  mr24: { marginRight: getFixPixel(24) },
  mr0: { marginRight: 0 },
  dateContentWrap: {
    position: 'relative',
    backgroundColor: color.grayBgSecondary,
    borderRadius: getFixPixel(8),
    height: getFixPixel(58),
    marginTop: getFixPixel(6),
  },
  opacity: {
    opacity: 0.3,
  },
  centerView: {
    flex: 1,
    justifyContent: 'center',
    height: DEFAULT_HEADER_HEIGHT,
  },
  mt1: {
    marginTop: getFixPixel(1),
  },
  sideLeft: Platform.select({
    ios: {
      marginTop: -getFixPixel(5),
    },
    android: {
      marginTop: getFixPixel(2),
    },
    // @ts-ignore
    harmony: {
      marginTop: getFixPixel(2),
    },
  }),
  sideLeftSize: {
    width: iconWrapWidth,
    height: iconWrapWidth,
  },
  renderSideLeft: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    paddingLeft: getFixPixel(10),
    paddingRight: getFixPixel(10),
  },
  shareView: {
    justifyContent: 'center',
    textAlign: 'center',
    alignItems: 'center',
  },
  normalShareIconView: {
    width: iconWrapWidth,
    height: iconWrapWidth,
    borderRadius: getFixPixel(32),
    ...layout.flexCenter,
  },
  smallIcon: {
    fontSize: getFixPixel(40),
    lineHeight: getLineHeight(38),
    marginTop: getFixPixel(7),
  },
  normalShare: {
    justifyContent: 'center',
    color: color.fontPrimary,
    textAlign: 'center',
  },
  shadowShareIconView: {
    backgroundColor: 'rgba(0,0,0,.4)',
    width: iconWrapWidth,
    height: iconWrapWidth,
    borderRadius: getFixPixel(32),
  },
  shadowShare: {
    justifyContent: 'center',
    color:
      BbkChannel.getThemingType() === BbkChannel.THEMING.dark
        ? color.fontPrimary
        : color.white,
    textAlign: 'center',
  },
  iconTouchWrap: {
    width: iconTouchWrapWidth,
    height: iconTouchWrapWidth,
    marginRight: getFixPixel(-12),
  },
  sideToolBox: {
    marginRight: getFixPixel(-22),
  },
});

const DisableControllComponent = ({
  disabled = false,
  children,
  style,
  disabledStyle = {},
}) => {
  const disableSelector = selector(disabled);
  return (
    <View
      style={xMergeStyles([
        style,
        disabledStyle,
        disableSelector(styles.opacity),
      ])}
    >
      {children}
      {disableSelector(
        <View
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            zIndex: 5,
          }}
        />,
      )}
    </View>
  );
};
const headerRightSideStyle = StyleSheet.create({
  wrapper: {
    paddingLeft: getFixPixel(30),
    paddingRight: getFixPixel(30),
    marginRight: -getFixPixel(30), // height: 56,
  },
  elsewhere: { paddingLeft: getFixPixel(14) },
});
const HeaderRightSide = ({
  currency,
  onCurrencyPress,
  elsewhere = false,
  isShowCurrency = true,
}) => {
  const { opacity } = useContext(FloatHeaderContext);
  return !isShowCurrency ? null : (
    <BbkTouchable
      style={xMergeStyles([
        layout.alignHorizontal,
        headerRightSideStyle.wrapper,
        selector(elsewhere, headerRightSideStyle.elsewhere),
        getOpacityIconWrapStyle(opacity),
      ])}
      onPress={onCurrencyPress}
    >
      <View style={layout.verticalItem}>
        <BbkText
          type="icon"
          className={c2xHeaderRightSideStyle.currencySign}
          style={getOpacityIconStyle(opacity)}
        >
          {htmlDecode(icon.currency)}
        </BbkText>
        <BbkText
          className={c2xHeaderRightSideStyle.currencyTex}
          style={getOpacityIconStyle(opacity)}
        >
          {currency}
        </BbkText>
      </View>
    </BbkTouchable>
  );
};
export const HeaderSideToolBox = ({
  pageId,
  bizType,
  styleType = 1,
  jumpUrl,
  style,
}) => {
  return (
    <View style={{ position: 'relative' }} collapsable={false}>
      <SideToolBox // @ts-ignore 升级072
        style={xMergeStyles([styles.iconTouchWrap, styles.sideToolBox, style])}
        config={getSideToolBoxConfig({ pageId, bizType, styleType, jumpUrl })}
        onDidClickOnEntrance={onDidClickOnEntrance}
      />
    </View>
  );
};
const locationContentStyle = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: getFixPixel(24),
    marginLeft: isIos ? getFixPixel(-24) : getFixPixel(-48),
    justifyContent: 'center',
  },
  item: { flex: 1 },
  mr68: { marginRight: getFixPixel(68) },
  ml24: { marginLeft: getFixPixel(24) },
  ml48: { marginLeft: getFixPixel(48) },
});
interface ILocationContent {
  pickupLocation: any;
  returnLocation: any;
  ptime: string;
  rtime: string;
  enablePress;
  theme?: any;
  wrapStyle?: CSSProperties;
  textStyle?: CSSProperties;
  blankWidth?: number;
  initMaxWidth?: number;
  dateStyle?: CSSProperties;
  isFromVendorListPage?: boolean;
  onPress?: () => void;
  pLocationStyle?: CSSProperties;
  rLocationStyle?: CSSProperties;
}
export const LocationContent = memo((props: ILocationContent) => {
  const {
    pickupLocation,
    returnLocation,
    ptime,
    rtime,
    enablePress,
    theme = {},
    wrapStyle = {},
    textStyle = {},
    blankWidth = 0,
    initMaxWidth = 0,
    dateStyle = {},
    isFromVendorListPage = false,
    pLocationStyle,
    rLocationStyle,
    onPress,
  } = props;
  const [maxWidth, setMaxWidth] = useState(
    Math.floor(initMaxWidth) || getFixPixel(260),
  );
  const pLocationName = pickupLocation.locationName;
  const rLocationName = returnLocation.locationName;
  const isSameLocation = pLocationName === rLocationName;
  const onLayout = e => {
    const { width } = e.nativeEvent.layout;
    const newMaxWidth = Math.floor(
      getMaxWidth(isSameLocation, width, blankWidth),
    );
    if (newMaxWidth != maxWidth) {
      setMaxWidth(newMaxWidth);
    }
  };
  return selector(isSameLocation)(
    <>
      <View
        style={xMergeStyles([
          locationContentStyle.wrapper,
          wrapStyle,
          isFromVendorListPage && styles.ml24,
        ])}
        onLayout={onLayout}
      >
        <LocationItem
          locationName={pLocationName}
          maxWidth={maxWidth}
          align="center"
          textStyle={textStyle}
        />
      </View>
      <DateContent
        ptime={ptime}
        rtime={rtime}
        wrapStyle={Utils.getPackageStyle([
          isFromVendorListPage ? locationContentStyle.ml24 : {},
          wrapStyle,
        ])}
        textStyle={xMergeStyles([textStyle, dateStyle])}
        enablePress={enablePress}
        ptimeTestID={UITestID.car_testid_page_vendorList_date_ptime}
        rtimeTestID={UITestID.car_testid_page_vendorList_date_rtime}
      />
    </>,
    <View
      style={xMergeStyles([
        locationContentStyle.wrapper,
        wrapStyle,
        isFromVendorListPage && styles.mr0,
      ])}
      onLayout={onLayout}
    >
      <View
        className={c2xLocationContentStyle.splitRight}
        style={pLocationStyle}
      >
        <LocationItem
          locationName={pLocationName}
          maxWidth={maxWidth}
          align="right"
          textStyle={textStyle}
        />

        <BbkText
          numberOfLines={1}
          className={classNames(
            c2xDateContentContentStyle.textContent,
            c2xDateContentContentStyle.item,
            c2xDateContentContentStyle.ptime,
          )}
          style={xMergeStyles([textStyle, dateStyle])}
        >
          {ptime}
        </BbkText>
      </View>
      <View className={c2xLocationContentStyle.splitIconContainer}>
        <BbkText
          type="icon"
          className={classNames(
            c2xLocationContentStyle.splitIcon,
            isFromVendorListPage && c2xStyles.c333,
          )}
          style={
            isFromVendorListPage && {
              fontSize: getFixPixel(28),
            }
          }
        >
          {htmlDecode(icon.oneWay)}
        </BbkText>
      </View>
      <View
        className={c2xLocationContentStyle.splitLeft}
        style={rLocationStyle}
      >
        <LocationItem
          locationName={rLocationName}
          maxWidth={maxWidth}
          align="left"
          textStyle={textStyle}
        />

        <BbkText
          numberOfLines={1}
          className={classNames(
            c2xDateContentContentStyle.textContent,
            c2xDateContentContentStyle.item,
            c2xDateContentContentStyle.rtime,
          )}
          style={xMergeStyles([textStyle, dateStyle])}
        >
          {rtime}
        </BbkText>
      </View>
    </View>,
  );
});
interface ILocationItemProps {
  locationName: string;
  align?: string;
  maxWidth?: number;
  textStyle?: CSSProperties;
}
const LocationItem = ({
  locationName,
  align = 'left',
  maxWidth,
  textStyle,
}: ILocationItemProps) => {
  return (
    <View style={{ maxWidth }}>
      <BbkText
        numberOfLines={1}
        className={c2xLocationContentStyle.textContent}
        style={xMergeStyles([{ textAlign: align }, textStyle])}
      >
        {locationName}
      </BbkText>
    </View>
  );
};
const dateContentContentStyle = StyleSheet.create({
  arrow: {
    color: color.fontPrimary,
    fontSize: getFixPixel(16),
    marginLeft: getFixPixel(4),
    marginTop: getFixPixel(4),
  },
});
const DateContent = withTheme(
  ({
    ptime,
    rtime,
    enablePress,
    theme = {},
    wrapStyle,
    textStyle,
    splitIconStyle,
    splitIconContainerStyle,
    ptimeTestID,
    rtimeTestID,
  }: DateContentProps) => (
    <View
      className={c2xDateContentContentStyle.wrapper}
      style={xMergeStyles([
        {
          flexDirection: 'row',
          alignItems: 'center',
          marginRight: getFixPixel(24),
          marginLeft: isIos ? getFixPixel(-24) : getFixPixel(-48),
          justifyContent: 'center',
        },
        wrapStyle,
      ])}
    >
      <View testID={ptimeTestID}>
        <BbkText
          numberOfLines={1}
          className={classNames(
            c2xDateContentContentStyle.textContent,
            c2xDateContentContentStyle.item,
            c2xDateContentContentStyle.ptime,
          )}
          style={textStyle}
        >
          {ptime}
        </BbkText>
      </View>
      <View
        className={c2xLocationContentStyle.splitIconContainer}
        style={splitIconContainerStyle}
      >
        <BbkText
          type="icon"
          className={c2xLocationContentStyle.splitIcon2}
          style={splitIconStyle}
        >
          {htmlDecode(icon.oneWay)}
        </BbkText>
      </View>
      <View testID={rtimeTestID}>
        <BbkText
          numberOfLines={1}
          className={classNames(
            c2xDateContentContentStyle.textContent,
            c2xDateContentContentStyle.item,
            c2xDateContentContentStyle.rtime,
          )}
          style={textStyle}
        >
          {rtime}
        </BbkText>
      </View>
      {/** 和视觉约定，不要向下箭头了，暂时去掉 */}
      {/* {selector(
      enablePress,
      <BbkText type="icon" style={[dateContentContentStyle.arrow, theme.bbkTextColor && { color: theme.bbkTextColor }]}>
      {htmlDecode(icon.arrowDownFilled)}
      </BbkText>,
      null,
      )} */}
    </View>
  ),
);
const headerContentStyle = StyleSheet.create({
  wrapper: {
    overflow: 'hidden',
    paddingTop: getFixPixel(4),
    paddingBottom: getFixPixel(4),
    marginRight: isIos ? getFixPixel(0) : getFixPixel(100),
    paddingLeft: isIos ? getFixPixel(0) : getFixPixel(24),
  },
  wrapperIOS: { marginRight: 0, paddingLeft: 0 },
  pressWrapper: { backgroundColor: color.grayBgSecondary, borderRadius: 4 },
});
export const HeaderContent = ({
  enablePress = true,
  onPress = noop,
  ptime,
  rtime,
  isShowProductName = false,
  channelType = '',
  isHideHeader = false,
  isSmallImage = false,
  name = '',
  isHotLabel = false,
  isSimilar = false,
  licenseTag = '',
  licenseType = '',
  isHideLinear = false,
  pickupLocation,
  returnLocation,
  isAndroidName,
  pLocationStyle = Utils.EmptyObj,
  rLocationStyle = Utils.EmptyObj,
  headContentStyle = Utils.EmptyObj,
  locationTextStyle = Utils.EmptyObj,
  dateStyle = Utils.EmptyObj,
  isFromVendorListPage,
  wrapStyle = Utils.EmptyObj,
  initMaxWidth,
  centerTestID,
}: any) => {
  const productName = isSmallImage
    ? isHideHeader
      ? name
      : texts.productDetail
    : name;
  const license = isSmallImage ? (isHideHeader ? licenseTag : '') : licenseTag;
  if (!enablePress) {
    return (
      <View
        style={xMergeStyles([
          headerContentStyle.wrapper,
          !isAndroidName && headerContentStyle.wrapperIOS,
          wrapStyle,
        ])}
      >
        {isShowProductName && !isAndroidName && (
          <BbkProductNameItem
            channelType={channelType}
            name={productName}
            isSimilar={isSimilar}
            isHotLabel={isHotLabel}
            licenseTag={license}
            licenseType={licenseType}
            isHideLinear={isHideLinear}
          />
        )}
        {!isShowProductName && (
          <LocationContent
            pickupLocation={pickupLocation}
            returnLocation={returnLocation}
            ptime={ptime}
            rtime={rtime}
            enablePress={enablePress}
            textStyle={locationTextStyle}
            dateStyle={dateStyle}
            initMaxWidth={initMaxWidth}
          />
        )}
        {!isShowProductName && (
          <View className={c2xHeaderContentStyle.editView}>
            <BbkText
              type="icon"
              className={classNames(
                c2xCommonStyles.c2xTextDefaultColor,

                GetAB.isISDInterestPoints()
                  ? c2xHeaderContentStyle.iconEditNew
                  : c2xHeaderContentStyle.iconEdit,
              )}
            >
              {icon.miniSearchEdit}
            </BbkText>
          </View>
        )}
      </View>
    );
  }
  return (
    <BbkTouchable
      style={xMergeStyles([
        headerContentStyle.wrapper,
        headerContentStyle.pressWrapper,
        headContentStyle,
        wrapStyle,
      ])}
      testID={centerTestID}
      onPress={onPress}
    >
      {isShowProductName && !isAndroidName && (
        <BbkProductNameItem
          name={productName}
          isSimilar={isSimilar}
          isHotLabel={isHotLabel}
          licenseTag={license}
          licenseType={licenseType}
          isHideLinear={isHideLinear}
        />
      )}
      {!isShowProductName && (
        <LocationContent
          pLocationStyle={pLocationStyle}
          rLocationStyle={rLocationStyle}
          pickupLocation={pickupLocation}
          returnLocation={returnLocation}
          ptime={ptime}
          rtime={rtime}
          enablePress={enablePress}
          textStyle={locationTextStyle}
          dateStyle={dateStyle}
          isFromVendorListPage={isFromVendorListPage}
          onPress={onPress}
          initMaxWidth={initMaxWidth}
        />
      )}
      {!isShowProductName && (
        <View className={c2xHeaderContentStyle.editView}>
          <BbkText
            type="icon"
            className={classNames(
              c2xCommonStyles.c2xTextDefaultColor,

              GetAB.isISDInterestPoints()
                ? c2xHeaderContentStyle.iconEditNew
                : c2xHeaderContentStyle.iconEdit,
            )}
          >
            {icon.miniSearchEdit}
          </BbkText>
        </View>
      )}
    </BbkTouchable>
  );
};

export const RenderCenterView: React.FC<any> = ({
  data,
  enableChange,
  enablePress,
  isShare = false,
  channelType,
  isService = false,
  isShowProductName = false,
  isHideHeader = false,
  isSmallImage = false,
  name = '',
  isSimilar = false,
  isHotLabel = false,
  licenseTag = '',
  licenseType = '',
  isHideLinear = false,
  showSearchSelectorWrap = () => {},
  isFail,
  headerContentShowAtCenter,
  wrapperStyle,
  headContentStyle = {},
  locationTextStyle,
  dateStyle,
  testID,
  centerTestID,
  isFromVendorListPage,
  initMaxWidth,
}) => {
  if (!data) return null;
  const { pickupLocation, returnLocation, ptime, rtime } = data;
  const { pickUpDateStr, dropOffDateStr } =
    DateFormatter.pickUpAndDropOffDateFormat(ptime, rtime);
  const { opacity } = useContext(FloatHeaderContext);

  let displayName = name;
  if (displayName.length >= 20) {
    displayName = `${displayName.substr(0, 17)}...`;
  }
  const logicDisplayName = isSmallImage
    ? isHideHeader
      ? name
      : texts.productDetail
    : name;
  // 国内标题如果右边按钮等于3个，字数限制为6，否则字数限制为11
  const maxLength = !!isShare && !!isService ? 6 : 11;
  const isIsdLittleName =
    channelType === 'isd' && logicDisplayName.length <= maxLength;

  let centerMarginRight = 0;
  if (!!isShare && !isIsdLittleName && !isFail) {
    centerMarginRight +=
      // @ts-ignore
      Platform.OS === 'android' || Platform.OS === 'harmony' ? 64 : 60;
  }
  if (!!isService && !isIsdLittleName && !isFail) {
    centerMarginRight +=
      // @ts-ignore
      Platform.OS === 'android' || Platform.OS === 'harmony' ? 64 : 60;
  }
  const centerViewStyle = {
    ...Platform.select({
      ios: {
        marginLeft: getFixPixel(104),
        marginRight: getFixPixel(104 + centerMarginRight),
      },
      android: {
        marginLeft: getFixPixel(112),
        marginRight: isShowProductName
          ? getFixPixel(112 + centerMarginRight)
          : 0,
      },
      // @ts-ignore
      harmony: {
        marginLeft: getFixPixel(112),
        marginRight: isShowProductName
          ? getFixPixel(112 + centerMarginRight)
          : 0,
      },
    }),
  };
  const androidNameStyle = {
    width: getFixPixel(750 - 128 - 128 - centerMarginRight),
    position: 'absolute',
    left: 0,
  };
  const productName = logicDisplayName;
  const license = isSmallImage ? (isHideHeader ? licenseTag : '') : licenseTag;
  const isAndroidName =
    channelType === 'isd' &&
    Platform.OS !== 'ios' &&
    !headerContentShowAtCenter;
  const isMore = channelType === 'isd' && productName.length >= 7;
  return (
    <View
      style={xMergeStyles([styles.centerView, centerViewStyle, wrapperStyle])}
      testID={testID}
    >
      {isAndroidName && isShowProductName && (
        <BbkProductNameItem
          isMore={isMore}
          androidNameStyle={androidNameStyle}
          channelType={channelType}
          name={productName}
          isSimilar={isSimilar}
          isHotLabel={isHotLabel}
          licenseTag={license}
          licenseType={licenseType}
          isHideLinear={isHideLinear}
        />
      )}
      <DisableControllComponent
        style={{
          flex: 1,
          justifyContent: 'center',
        }}
        disabled={!enableChange}
        disabledStyle={{ opacity: 1 }}
      >
        <HeaderContent
          ptime={pickUpDateStr}
          rtime={dropOffDateStr}
          isAndroidName={isAndroidName}
          isShowProductName={isShowProductName && !!name}
          isSmallImage={isSmallImage}
          isHideHeader={isHideHeader}
          name={displayName}
          isSimilar={isSimilar}
          isHotLabel={isHotLabel}
          licenseTag={licenseTag}
          licenseType={licenseType}
          isHideLinear={isHideLinear}
          channelType={channelType}
          pickupLocation={pickupLocation}
          returnLocation={returnLocation}
          enablePress={enablePress}
          onPress={showSearchSelectorWrap}
          headContentStyle={headContentStyle}
          locationTextStyle={locationTextStyle}
          dateStyle={dateStyle}
          isFromVendorListPage={isFromVendorListPage}
          initMaxWidth={initMaxWidth}
          centerTestID={centerTestID}
        />
      </DisableControllComponent>
    </View>
  );
};

const RenderRightIconView: React.FC<any> = ({
  isShow = false,
  toolBoxStyle,
  toolBoxStyleType,
  onPress,
  iconCode,
  isHideAnimation = false,
  title,
  testID,
  exposureData,
}) => {
  if (!isShow) return null;
  return (
    <BbkTouchable
      style={xMergeStyles([
        styles.iconTouchWrap,
        toolBoxStyle,
        styles.shareView,
      ])}
      onPress={onPress}
      testID={testID}
    >
      <XViewExposure
        style={
          isHideAnimation
            ? styles.shadowShareIconView
            : styles.normalShareIconView
        }
        testID={exposureData ? CarLog.LogExposure(exposureData) : ''}
      >
        <BbkText
          className={c2xStyles.shareIcon}
          style={xMergeStyles([
            {
              fontSize: getFixPixel(48),
              lineHeight: getFixPixel(64),
            },
            isHideAnimation ? styles.shadowShare : styles.normalShare,
            title && styles.smallIcon,
          ])}
          type="icon"
        >
          {iconCode}
        </BbkText>
        {!!title && (
          <BbkText
            className={c2xStyles.shareTitle}
            style={{
              fontSize: getFixPixel(20),
              lineHeight: getFixPixel(26),
            }}
          >
            {title}
          </BbkText>
        )}
      </XViewExposure>
    </BbkTouchable>
  );
};

const VREnter: React.FC<{ vrUrl: string; vehicleCode: string }> = ({
  vrUrl,
  vehicleCode,
}) => {
  const handleVR = useCallback(() => {
    xRouter.navigateTo({ url: vrUrl });
    CarLog.LogCode({
      name: '点击_产品详情页_VR入口',

      info: {
        vehicleCode,
      },
    });
    CarLog.LogTrace({
      key: LogKey.c_rn_car_trace_click,
      info: {
        name: '点击_产品详情页_VR入口',
        severRequestId: getServerRequestId(),
      },
    });
  }, [vrUrl]);
  return (
    <XViewExposure
      testID={CarLog.LogExposure({
        name: '曝光_产品详情页_VR入口',

        info: { vehicleCode },
      })}
    >
      <BbkTouchable onPress={handleVR}>
        <Image
          style={{
            width: getFixPixel(133),
            height: getFixPixel(46),
            marginRight: getFixPixel(36),
            marginTop: getFixPixel(21),
          }}
          src={`${ImageUrl.componentImagePath}Product/vrIcon.png`}
        />
      </BbkTouchable>
    </XViewExposure>
  );
};

const RenderRightView: React.FC<any> = ({
  enableChange,
  enablePress = true,
  data,
  currency,
  isShowCurrency = true,
  showRight = true,
  pageId,
  bizType,
  toolBoxCustomerJumpUrl,
  toolBoxStyleType,
  isShare,
  goShare = noop,
  toolBoxStyle,
  renderRight,
  onPressHeaderRight,
  isService,
  goService,
  isHideAnimation = false,
  vrUrl,
  vehicleCode,
  serviceTestID,
  shareExposureData,
}) => {
  if (!data || !enablePress) return null;
  const { pickupLocation, returnLocation } = data;
  const elsewhere = pickupLocation.locationName !== returnLocation.locationName;

  if (!showRight) {
    return null;
  }
  const styleType = isHideAnimation
    ? toolBoxStyleType
    : CommonEnums.ToolBoxStyleType.blackAndMore;
  return selector(
    renderRight,
    renderRight,
    <View
      className={c2xStyles.sideRight}
      style={{
        top: 0,
        bottom: 0,
        right: 0,
        paddingLeft: getFixPixel(32),
        paddingRight: getFixPixel(32),
      }}
    >
      <DisableControllComponent
        style={{
          flex: 1,
          justifyContent: 'center',
        }}
        disabled={Utils.isCtripOsd() ? false : !enableChange}
        disabledStyle={{ opacity: 1 }}
      >
        <View style={{ flexDirection: 'row', height: BbkUtils.getPixel(88) }}>
          {!!vrUrl && <VREnter vrUrl={vrUrl} vehicleCode={vehicleCode} />}
          {!!isService &&
            RenderRightIconView({
              isShow: isService,
              toolBoxStyle,
              toolBoxStyleType,
              onPress: goService,
              iconCode: icon.service,
              isHideAnimation,
              title: !isHideAnimation ? '客服' : '',
              testID: serviceTestID,
            })}
          {!!isShare &&
            RenderRightIconView({
              isShow: isShare,
              toolBoxStyle,
              toolBoxStyleType,
              onPress: goShare,
              iconCode: icon.share,
              isHideAnimation,
              title: !isHideAnimation ? '分享' : '',
              exposureData: shareExposureData,
            })}
          <HeaderSideToolBox
            pageId={pageId}
            bizType={bizType}
            styleType={styleType}
            jumpUrl={toolBoxCustomerJumpUrl}
            style={Utils.getPackageStyle([
              toolBoxStyle,
              styleType === CommonEnums.ToolBoxStyleType.blackAndMore &&
                isAndroid &&
                styles.mt1,
            ])}
          />
        </View>
      </DisableControllComponent>
    </View>,
  );
};

const RenderLeftView: React.FC<any> = ({
  styleType,
  onPressLeft,
  enableChange,
  enablePress = true,
  isHideAnimation = false,
  testID,
}) => {
  if (!enablePress) return null;
  const backIcon = icon.arrowLeftCtrip;
  return (
    <View
      style={xMergeStyles([styles.sideLeft, styles.renderSideLeft])}
      testID={UITestID.car_testid_page_vendorList_PageBack}
    >
      <DisableControllComponent
        style={{
          flex: 1,
          justifyContent: 'center',
        }}
        disabled={!enableChange}
      >
        <BbkTouchable
          style={xMergeStyles([styles.iconTouchWrap, styles.shareView])}
          onPress={onPressLeft}
          testID={testID}
        >
          <View
            style={
              isHideAnimation
                ? styles.shadowShareIconView
                : styles.normalShareIconView
            }
          >
            <BbkText
              className={c2xStyles.shareIcon}
              style={xMergeStyles([
                {
                  fontSize: getFixPixel(48),
                  lineHeight: getFixPixel(64),
                },
                isHideAnimation ? styles.shadowShare : styles.normalShare,
              ])}
              type="icon"
            >
              {backIcon}
            </BbkText>
          </View>
        </BbkTouchable>
      </DisableControllComponent>
    </View>
  );
};

const BbkComponentCarHeader: React.FC<carHeaderProps> = ({
  data,
  style,
  theme,
  enableChange,
  enablePress,
  handleBackPress,
  channelType,
  isShowProductName = false,
  name,
  isSimilar = false,
  isHotLabel = false,
  licenseTag = '',
  licenseType = '',
  isSmallImage = false,
  isHideHeader = false,
  page,
  currency,
  renderRight,
  toolBoxStyleType,
  isShare = false,
  goShare,
  isRenderLeft,
  isHideRight = false,
  isRenderContent = true,
  isShowCurrency = true,
  sideLeftStyle,
  onPressHeaderRight = () => {},
  showSearchSelectorWrap = () => {},
  isHideAnimation = false,
  opacityAnimation,
  fixOpacityAnimation,
  ...passThroughProps
}) => {
  const defaultBackPress = () => {
    page.pop();
  };

  if (!data) return null;
  const themeColor: any =
    getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) || {};
  const { opacity } = useContext(FloatHeaderContext);
  const widthStyle = useWindowSizeChanged();
  const renderLeftView = isHideAnimation => {
    return (
      <RenderLeftView
        onPressLeft={handleBackPress || defaultBackPress}
        styleType={toolBoxStyleType}
        enableChange={enableChange}
        isHideAnimation={isHideAnimation}
        testID={passThroughProps?.leftIconTestID}
        enablePress={true}
      />
    );
  };
  const renderRightView = isHideAnimation => {
    return (
      <RenderRightView
        enableChange={enableChange}
        isHideAnimation={isHideAnimation}
        enablePress={true}
        data={data}
        currency={currency}
        isShowCurrency={isShowCurrency}
        showRight={true}
        renderRight={renderRight}
        toolBoxStyleType={toolBoxStyleType}
        isShare={isShare}
        goShare={goShare}
        onPressHeaderRight={onPressHeaderRight}
        {...passThroughProps}
      />
    );
  };
  return (
    <BbkHeader
      onPressLeft={handleBackPress || defaultBackPress}
      style={{
        ...style,
        ...widthStyle,
      }}
      isHideAnimation={isHideAnimation}
      opacityAnimation={opacityAnimation}
      fixOpacityAnimation={fixOpacityAnimation}
      sideLeftStyle={xMergeStyles([
        styles.sideLeftSize,
        styles.sideLeft,
        sideLeftStyle,
      ])}
      styleInner={{ paddingTop: 0, paddingBottom: 0 }}
      renderLeft={isRenderLeft && renderLeftView}
      renderContent={
        isRenderContent ? (
          <RenderCenterView
            data={data}
            channelType={channelType}
            isShowProductName={isShowProductName}
            isHideHeader={isHideHeader}
            name={name}
            isSimilar={isSimilar}
            isHotLabel={isHotLabel}
            isSmallImage={isSmallImage}
            licenseTag={licenseTag}
            licenseType={licenseType}
            enableChange={enableChange}
            enablePress={enablePress}
            isShare={isShare}
            {...passThroughProps}
            showSearchSelectorWrap={showSearchSelectorWrap}
          />
        ) : null
      }
      renderRight={!isHideRight && renderRightView}
      {...passThroughProps}
    />
  );
};

export default FloatHoc(BbkComponentCarHeader);
