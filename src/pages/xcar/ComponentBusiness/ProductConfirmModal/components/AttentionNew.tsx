import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import ScrollView from '@c2x/components/ScrollView';
import React, { useCallback, useMemo, useState } from 'react';
import {
  XView as View,
  XImageBackground as ImageBackground,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
import LayoutChangeEvent from '@c2x/apis/LayoutChangeEvent';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { font, layout, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './attentionNewC2xStyles.module.scss';
import { texts } from '../Texts';
import {
  CancelRuleInfoType,
  PromptInfosType,
  PromptInfosCodeEnum,
  EasyLifeInfoType,
  PickUpMaterialsType,
  TitleAbstractItem,
} from '../../../Types/Dto/QueryVehicleDetailInfoResponseType';
import {
  TableBoxNew,
  DepositTableBoxNew,
  PickUpMaterialsTableBox,
} from '../../TableBox';
import { PolicyPressType } from '../../../State/Product/Enums';
import { LayoutPartEnum, VehicleInfoLogDataType } from '../Type';
import { Loading } from './MiniComponent';
import EmptyComponent, { ImgType } from '../../EmptyComponent/Index';
import { CarLog } from '../../../Util/Index';
import { PageParamType } from '../../../Types/Dto/QueryVehicleDetailListRequestType';
import { ImageUrl, UITestID } from '../../../Constants/Index';

const { getPixel, useMemoizedFn } = BbkUtils;
const styles = StyleSheet.create({
  cancelRuleWrapper: {
    marginTop: getPixel(17),
  },
  itemTitleStyle: {
    ...font.body3LightStyle,
    color: color.recommendProposeBg,
  },
  depositWrapper: {
    marginTop: getPixel(16),
  },
  easyLifeWrapperNew: {
    ...layout.flexRow,
    marginLeft: getPixel(32),
    marginRight: getPixel(32),
    height: getPixel(67),
  },
  easyLifeBgNew: {
    width: getPixel(638),
    height: getPixel(67),
  },
});

interface PromptInfosPropsType {
  promptInfos: PromptInfosType[];
  onLayout?: (e: LayoutChangeEvent) => void;
}

interface IPolicyList {
  policyList: any;
  policyBottom?: number;
  onPressPolice?: (policyType?: PolicyPressType) => void;
  setShadowVisible: (visible: boolean) => void;
  testID?: any;
}

const exposureKey = {
  delay: '曝光_门店弹层_延误政策',
  mileage: '曝光_门店弹层_里程政策',
  limitArea: '曝光_门店弹层_禁行区域',
};

const PromptInfosNew: React.FC<PromptInfosPropsType> = ({
  promptInfos,
  onLayout,
}) => {
  if (!promptInfos?.length) return null;
  return (
    <View onLayout={onLayout}>
      {promptInfos.map(v => (
        <XViewExposure
          className={c2xStyles.promptInfoWrapperNew}
          key={v.code}
          testID={
            exposureKey[v.code] &&
            CarLog.LogExposure({
              name: exposureKey[v.code],
            })
          }
        >
          <Text className={c2xStyles.promptInfosTitle} fontWeight="medium">
            {v.title}
          </Text>
          <View className={c2xStyles.promptInfoItemNew}>
            {v.content?.length > 0 &&
              v.content.map(s => (
                <View className={c2xStyles.row}>
                  <Text className={c2xStyles.promptInfoTextNew} key={s}>
                    {s}
                  </Text>
                </View>
              ))}
            {!!v.subTitle && (
              <Text className={c2xStyles.promptInfoTip}>{v.subTitle}</Text>
            )}
          </View>
        </XViewExposure>
      ))}
    </View>
  );
};

const PolicyList: React.FC<IPolicyList> = ({
  policyList,
  policyBottom,
  onPressPolice,
  setShadowVisible,
  testID,
}) => {
  const [wrapWidth, setWrapWidth] = useState(0);
  const [scrollViewWidth, setScrollViewWidth] = useState(0);
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const initialShadow = useMemoizedFn((wrapWidth, scrollViewWidth) => {
    if (wrapWidth > 0 && scrollViewWidth > 0) {
      if (scrollViewWidth > wrapWidth) {
        setShadowVisible(true);
      } else {
        setShadowVisible(false);
      }
    }
  });
  const wrapOnLayout = useMemoizedFn(event => {
    setWrapWidth(event.nativeEvent.layout.width);
    initialShadow(event.nativeEvent.layout.width, scrollViewWidth);
  });
  const scrollViewOnLayout = useMemoizedFn(event => {
    setScrollViewWidth(event.nativeEvent.layout.width);
    initialShadow(wrapWidth, event.nativeEvent.layout.width);
  });
  const onScroll = useMemoizedFn(event => {
    if (event.nativeEvent.contentOffset.x >= scrollViewWidth - wrapWidth - 1) {
      setShadowVisible(false);
    } else {
      setShadowVisible(true);
    }
  });
  return (
    <View
      style={{
        marginBottom: policyBottom,
      }}
      testID={testID}
    >
      <Touchable
        className={c2xStyles.morePolice}
        debounce={true}
        onPress={useMemoizedFn(() => {
          onPressPolice();
        })}
        testID={UITestID.car_testid_comp_vendor_modal_more_policy_btn}
      >
        <Text className={c2xStyles.depositTitle} fontWeight="medium">
          {texts.new_morePolice}
        </Text>
        <Text className={c2xStyles.depositArrow} type="icon">
          {icon.arrowRight}
        </Text>
      </Touchable>
      <ScrollView
        bounces={false}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        onLayout={wrapOnLayout}
        onScroll={onScroll}
      >
        <View className={c2xStyles.scrollView} onLayout={scrollViewOnLayout}>
          {policyList.map(
            (policy, index) =>
              index < 4 &&
              !!policy.itemTitle && (
                <Touchable
                  key={policy.itemTitle}
                  className={classNames(
                    c2xStyles.policeItemWrap,
                    index === 3 && c2xStyles.lastPoliceItemWrap,
                  )}
                  testID={`${UITestID.car_testid_page_vendorlist_product_modal_policy_item}_${policy.itemTitle}`}
                  debounce={true}
                  onPress={() => {
                    onPressPolice(policy.type);
                  }}
                >
                  <Text className={c2xStyles.policeItemText}>
                    {policy.itemTitle}
                  </Text>
                </Touchable>
              ),
          )}
        </View>
      </ScrollView>
    </View>
  );
};

interface EasyLifePropsType {
  easyLifeInfo: EasyLifeInfoType;
  onPress?: () => void;
  testID?: any;
}

interface ITitleSection {
  titleAbstract: TitleAbstractItem[];
}

// 无忧组优势标题
const TitleSection = React.memo(({ titleAbstract }: ITitleSection) => {
  const items = [...titleAbstract];
  items.sort((a, b) => a.sortNum - b.sortNum);

  return (
    <View style={layout.flexRow}>
      {items.map((item, index) => (
        <View className={c2xStyles.secTitleWrap} key={item.title}>
          <Text className={c2xStyles.subTitleNew} numberOfLines={1}>
            {item.title}
          </Text>
          {!!item.description && (
            <Text className={c2xStyles.starSignTitle}>*</Text>
          )}
          {index !== items.length - 1 && (
            <Text className={c2xStyles.subTitleNewPoint}>·</Text>
          )}
        </View>
      ))}
    </View>
  );
});

interface IDescSection {
  titleAbstract: TitleAbstractItem[];
}

// 无忧组优势说明
const DescSection = React.memo(({ titleAbstract }: IDescSection) => {
  const items = titleAbstract.filter(item => !!item.description);
  return (
    <>
      {items.map(item => (
        <View className={c2xStyles.descWrap} key={item.description}>
          <Text className={c2xStyles.starSignDesc}>*</Text>
          <Text className={c2xStyles.secDesc}>{item.description}</Text>
        </View>
      ))}
    </>
  );
});

export const EasyLifeNew: React.FC<EasyLifePropsType> = ({
  easyLifeInfo,
  onPress,
  testID,
}) => {
  if (!easyLifeInfo?.isEasyLife) return null;
  if (!easyLifeInfo?.subTitle && !easyLifeInfo?.titleAbstract) return null;
  let textArray = [];
  if (easyLifeInfo?.subTitle) {
    textArray = easyLifeInfo.subTitle.split('·');
  }
  const titleAbstract = easyLifeInfo?.titleAbstract;
  return (
    <View testID={testID}>
      <Touchable
        debounce={true}
        className={c2xStyles.easyLifeWrap}
        onPress={onPress}
        testID={UITestID.car_testid_comp_vendor_modal_easy_life_entry}
      >
        <ImageBackground
          source={{
            uri: `${ImageUrl.CTRIP_EROS_URL}store_easylife_bg.png`,
          }}
          imageStyle={styles.easyLifeBgNew}
          style={styles.easyLifeWrapperNew}
          resizeMode="contain"
        >
          <Image
            className={c2xStyles.easyLifeIconLeftNew}
            src={`${ImageUrl.DIMG04_PATH}1tg2i12000eapvfpvA28F.png`}
            mode="aspectFit"
          />

          <View className={c2xStyles.row}>
            {titleAbstract ? (
              <TitleSection titleAbstract={titleAbstract} />
            ) : (
              !!easyLifeInfo?.subTitle &&
              textArray.map((text, index) => {
                return (
                  <>
                    {index > 0 && (
                      <Text
                        className={c2xStyles.subTitleNewPoint}
                        numberOfLines={1}
                      >
                        ·
                      </Text>
                    )}
                    <Text className={c2xStyles.subTitleNew} numberOfLines={1}>
                      {text}
                    </Text>
                  </>
                );
              })
            )}
            <Text className={c2xStyles.easyLifeArrowTextNew} type="icon">
              {icon.arrowRight}
            </Text>
          </View>
        </ImageBackground>
      </Touchable>
      {!!titleAbstract && <DescSection titleAbstract={titleAbstract} />}
    </View>
  );
};

export const EasyLife2024: React.FC<{ onPress: () => void }> = ({
  onPress,
}) => {
  const onPressBanner = useMemoizedFn(() => {
    onPress?.();
    CarLog.LogCode({ name: '点击_携程优选_无忧租一口价' });
  });
  return (
    <Touchable
      debounce={true}
      className={c2xStyles.easyLife2024Wrapper}
      onPress={onPressBanner}
      testID={UITestID.car_testid_comp_vendor_modal_easy_life_entry}
    >
      <XViewExposure
        testID={CarLog.LogExposure({ name: '曝光_携程优选_无忧租一口价' })}
      >
        <Image
          testID="product_confirm_easylife_2024_banner"
          className={c2xStyles.easyLife2024Image}
          src={`${ImageUrl.DIMG04_PATH}1tg4312000e016ed4167C.png`}
          mode="aspectFit"
        />
      </XViewExposure>
    </Touchable>
  );
};

interface AttentionPropsType {
  cancelRuleInfo: CancelRuleInfoType;
  isDepositLoading: boolean;
  isDepositFail: boolean;
  isDepositSaleOut: boolean;
  depositInfo: CancelRuleInfoType;
  pickUpMaterials: PickUpMaterialsType;
  promptInfos: PromptInfosType[];
  pageParam: PageParamType;
  vehicleInfoLog: VehicleInfoLogDataType;
  onPressPolice?: (policyType?: PolicyPressType) => void;
  queryDepositInfo?: () => void;
  onLayoutWrapper: (e: LayoutChangeEvent, type: LayoutPartEnum) => void;
  onLayoutMileage: (e: LayoutChangeEvent, type: LayoutPartEnum) => void;
  handleDespositSaleOut: (data) => void;
  isOrderDetail?: boolean;
  storePolicy?: any;
  setBusinessLicenseVisible?: (data: boolean) => void;
  hasSupplierData?: boolean;
}

const Attention: React.FC<AttentionPropsType> = ({
  cancelRuleInfo,
  isDepositLoading,
  isDepositFail,
  depositInfo,
  promptInfos,
  vehicleInfoLog,
  pickUpMaterials,
  onLayoutWrapper,
  onLayoutMileage,
  onPressPolice,
  setBusinessLicenseVisible,
  queryDepositInfo,
  isOrderDetail,
  isDepositSaleOut,
  pageParam,
  handleDespositSaleOut,
  storePolicy,
  hasSupplierData,
}) => {
  const [showShadow, setShowShadow] = useState(false);
  const setShadowVisible = useMemoizedFn(visible => {
    if (visible !== showShadow) {
      setShowShadow(visible);
    }
  });
  const promptInfosLimit = useMemo(() => {
    return promptInfos?.filter(v =>
      [PromptInfosCodeEnum.limitArea, PromptInfosCodeEnum.mileage].includes(
        v.code,
      ),
    );
  }, [promptInfos]);
  const promptInfosOther = useMemo(() => {
    return promptInfos?.filter(
      v =>
        ![PromptInfosCodeEnum.limitArea, PromptInfosCodeEnum.mileage].includes(
          v.code,
        ),
    );
  }, [promptInfos]);

  const handleEmptyPress = useCallback(() => {
    if (isDepositSaleOut) {
      handleDespositSaleOut(pageParam);
    } else {
      queryDepositInfo();
    }
  }, [isDepositSaleOut, handleDespositSaleOut, queryDepositInfo, pageParam]);

  const handelBusinessLicense = useCallback(() => {
    setBusinessLicenseVisible(true);
  }, [setBusinessLicenseVisible]);

  const pickUpMaterialsTitle = `${pickUpMaterials?.title}${pickUpMaterials?.subTitle}`;
  const policyList = storePolicy?.[0]?.items;
  const policyBottom = isOrderDetail ? getPixel(32) : getPixel(12);
  const attentionBottom = isOrderDetail ? getPixel(60) : getPixel(0);
  return (
    <XViewExposure
      testID={CarLog.LogExposure({
        name: '曝光_预定须知',

        info: vehicleInfoLog,
      })}
      onLayout={useMemoizedFn(e =>
        onLayoutWrapper(e, LayoutPartEnum.Attention),
      )}
    >
      <View
        className={c2xStyles.wrapper}
        style={{ marginBottom: attentionBottom }}
      >
        <Text className={c2xStyles.attentionTitle} fontWeight="bold">
          {isOrderDetail ? texts.carRentalMustRead : texts.new_attention}
        </Text>
        {/* 取消政策 */}
        {!!cancelRuleInfo && (
          <XViewExposure
            className={c2xStyles.cancelRuleInfoWrap}
            testID={CarLog.LogExposure({ name: '曝光_门店弹层_取消政策' })}
          >
            {!!cancelRuleInfo?.subTitle && (
              <Text className={c2xStyles.cancelRuleTitle} fontWeight="medium">
                {cancelRuleInfo?.subTitle}
              </Text>
            )}
            {Object.values(cancelRuleInfo).length > 0 && (
              <TableBoxNew
                style={styles.cancelRuleWrapper}
                itemTitleStyle={styles.itemTitleStyle}
                itemDescriptionStyle={styles.itemTitleStyle}
                title={cancelRuleInfo?.tableTitle?.split('|')}
                items={cancelRuleInfo.items}
              />
            )}
            {!!cancelRuleInfo?.notices && (
              <Text className={c2xStyles.cancelRuleNotice}>
                {cancelRuleInfo?.notices}
              </Text>
            )}
          </XViewExposure>
        )}
        {/* 押金政策 */}
        {!isOrderDetail && (
          <XViewExposure
            className={c2xStyles.depositWrap}
            testID={CarLog.LogExposure({ name: '曝光_门店弹层_押金政策' })}
          >
            <Text className={c2xStyles.depositTitle} fontWeight="medium">
              {texts.depositPolice}
            </Text>
            {isDepositLoading && <Loading />}
            {isDepositFail && (
              <EmptyComponent
                imgType={ImgType.No_Response}
                showButton={true}
                title={isDepositSaleOut && texts.despositSaleOutTip}
                buttonText={texts.despositSaleOutButtonText}
                onButtonPress={handleEmptyPress}
              />
            )}
            {depositInfo && Object.values(depositInfo).length > 0 && (
              <DepositTableBoxNew
                style={styles.depositWrapper}
                items={depositInfo.items}
                notices={depositInfo.notices}
              />
            )}
          </XViewExposure>
        )}
        {/* 取车材料 */}
        {!isOrderDetail && pickUpMaterials && (
          <XViewExposure
            className={c2xStyles.pickUpMaterialsWrap}
            testID={CarLog.LogExposure({ name: '曝光_门店弹层_取车材料' })}
          >
            {!!pickUpMaterialsTitle && (
              <Text
                className={c2xStyles.pickUpMaterialsTitle}
                fontWeight="medium"
              >
                {pickUpMaterialsTitle}
              </Text>
            )}
            {pickUpMaterials.subObject?.length > 0 && (
              <PickUpMaterialsTableBox
                style={styles.depositWrapper}
                items={pickUpMaterials.subObject}
              />
            )}
          </XViewExposure>
        )}
        {/* 其他政策 */}
        <PromptInfosNew promptInfos={promptInfosOther} />
        {/* 限制政策 */}
        <PromptInfosNew
          promptInfos={promptInfosLimit}
          onLayout={useMemoizedFn(e =>
            onLayoutMileage(e, LayoutPartEnum.Mileage),
          )}
        />

        {/* 更多政策 */}
        {policyList?.length > 0 && (
          <View>
            <PolicyList
              setShadowVisible={setShadowVisible}
              policyList={policyList}
              onPressPolice={onPressPolice}
              policyBottom={policyBottom}
              testID={CarLog.LogExposure({ name: '曝光_门店弹层_更多政策' })}
            />

            {showShadow && (
              <Image
                className={c2xStyles.storeShadow}
                style={{ bottom: policyBottom }}
                src={`${ImageUrl.CTRIP_EROS_URL}store_shadow.png`}
              />
            )}
          </View>
        )}

        {/* 供应商营业执照入口 */}
        {hasSupplierData && (
          <Touchable
            className={c2xStyles.busLicenseWrap}
            testID={UITestID.car_testid_page_vendorlist_product_modal_license}
            debounce={true}
            onPress={handelBusinessLicense}
          >
            <Text className={c2xStyles.depositTitle} fontWeight="medium">
              {texts.businessLicense}
            </Text>
            <Text className={c2xStyles.depositArrow} type="icon">
              {icon.arrowRight}
            </Text>
          </Touchable>
        )}
      </View>
    </XViewExposure>
  );
};

export default Attention;
