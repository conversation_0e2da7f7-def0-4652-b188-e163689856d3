import { CSSProperties } from 'react';
import LayoutChangeEvent from '@c2x/apis/LayoutChangeEvent';
import { EasyLifeInfoType } from '../../Types/Dto/QueryVehicleDetailInfoResponseType';
import { IMergeGuidInfo } from '../../Pages/Booking/Types';
import { SecretBoxStage } from '../../Pages/OrderDetail/Types';
import { AlbumType } from '../../Types/Dto/QueryVehicleDetailListResponseType';
import { MultimediaAlbum } from '../../Types/Dto/QueryMultimediaAlbumResponseType';

export interface LocationType {
  way?: string;
  address?: string;
  rentCenterName?: string;
  rentCenterId?: number;
  pickupServiceType?: string;
  dropoffServiceType?: string;
}

export interface ImagesType {
  imageList: string[];
  video: string;
  videoCover: string;
  imagesTotalNumber: number;
  albumName?: string;
}

// 锚点位置枚举
export enum LayoutPartEnum {
  VehicleDetail = 'VehicleDetail',
  VehicleConfig = 'VehicleConfig',
  Attention = 'Attention',
  Reviews = 'Reviews',
  Mileage = 'Mileage',
  PickUp = 'PickUp',
  InUse = 'InUse',
  DropOff = 'DropOff',
  FeeDetail = 'FeeDetail',
  Before = 'before',
  PickupAndDrop = 'PickupAndDrop',
  VehicleDetailInner = 'VehicleDetailInner',
  CancelInfo = 'CancelInfo',
  PickUpMaterials = 'PickUpMaterials',
}

export interface FooterBarDataType {
  price?: number;
  originPrice?: number;
  discountPrice?: number;
  currencyCode?: string;
  unitDesc?: string;
  renderGapPrice?: React.ReactNode;
  renderPrice?: React.ReactNode;
}

export interface VehicleInfoLogDataType {
  vehicleName?: string;
  vehicleCode?: string;
  vendorName?: string;
  vendorCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
}

export interface ImagesPageParamsType {
  storeCode?: string;
  vehicleId?: string;
  categoryId?: number;
}

export interface StoreAlbumPageParamsType {
  storeId?: string;
  vehicleCode?: string;
  skuId?: number;
  albumType?: AlbumType;
}

export interface ILocationInfoType {
  title: string;
  dotColor: string;
  way?: string;
  address?: string;
  distance?: string;
  rentCenterName?: string;
  style?: CSSProperties;
  onPressRentalCenter?: () => void;
  onPressLocation?: () => void;
  isSelfService?: boolean;
  selfServiceSlogan?: string;
  testID?: string;
  centerTestID?: string;
}

export interface IStoreSelfServiceInfo {
  isSelfService?: boolean;
  text?: string;
}

export interface VehicleDetailProps {
  location: {
    pickup: LocationType | null;
    return: LocationType | null;
  };
  vehicleInfoLog: VehicleInfoLogDataType;
  pickupDistance: string;
  returnDistance: string;
  images: ImagesType;
  onPressLocation: (data: any) => void;
  onPressMore: () => void;
  onLayoutWrapper: (e: LayoutChangeEvent, type: LayoutPartEnum) => void;
  onLayoutVehicleConfig: (e: LayoutChangeEvent, type: LayoutPartEnum) => void;
  onPressRentalCenter: (isPickup: boolean, id: number) => void;
  onPressEasyLife?: () => void;
  vehicleTags?: any;
  licenseTag?: string;
  licenseType?: string;
  name?: string;
  easyLifeInfo?: EasyLifeInfoType;
  isEasyLife2024?: boolean;
  isOrderDetail?: boolean;
  storeGuidInfos?: Array<IMergeGuidInfo>;
  decorateVehicleName?: string;
  secretBoxStage?: SecretBoxStage;
  pickUpStoreSelfServiceInfo?: IStoreSelfServiceInfo;
  returnStoreSelfServiceInfo?: IStoreSelfServiceInfo;
  isSelfService?: boolean;
  storeAlbumPageParams?: StoreAlbumPageParamsType;
  queryMultimediaAlbum?: (data) => void;
  createPreFetch?: (data) => void;
  showETCIntroModal?: () => void;
  isNewEnergy?: boolean;
  guidePageParams?: any;
  pickupStoreId?: string;
  dropoffStoreId?: string;
}

export interface IImagesProps {
  imagesTotalNumber?: number;
  imageList?: string[];
  video?: string;
  videoCover?: string;
  vehicleInfoLog: VehicleInfoLogDataType;
  onPressMore: () => void;
  testID: any;
  storeAlbumPageParams?: StoreAlbumPageParamsType;
  queryMultimediaAlbum?: (data) => void;
  albumName?: string;
  isISDShelves2B?: boolean;
}

export interface IUserAlbum {
  userAlbum?: MultimediaAlbum;
  userAlbumUrl?: string;
  userAlbumTotalCount?: number;
}
