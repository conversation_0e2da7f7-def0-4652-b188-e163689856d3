/* eslint-disable @typescript-eslint/no-shadow */
import React, { useState } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
/* eslint-disable @typescript-eslint/no-shadow */
import BbkComponentText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkComponentTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import {
  TagItem,
  Description,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import { GetAB, Utils } from '../../../Util/Index';
import BbkAddedServiceDesc from './AddedServiceDesc';
import { texts } from './Texts';
import InsuranceCheckBox from '../../CheckBox';
import InsuranceLabelItem from './InsuranceLabelItem';
import styles from './Styles';
import { UITestID } from '../../../Constants/Index';

const maxByteLength = 30; // 14个字 + 2个符号

export enum ProductType {
  // 已含
  include = 1,
  // 未选择，用户可选
  select = 0,
  // 赠送
  present = 2,
}

export interface IRentalGuaranteeItem {
  name: string;
  type: number;
  index: number;
  vendorServiceCode: string;
  localCurrencyCode: string;
  uniqueCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  description?: Array<string>;
  descriptionNew?: Array<Description>;
  allTags?: Array<TagItem>;
  extDescription?: string;
  onPressButton?: (data) => void;
  onPressTitle?: (data) => void;
  selected?: boolean;
  isHideOnPress?: boolean;
  isLast?: boolean;
  showTopBorder?: boolean;
  isShowLabel?: boolean;
  isHideCheckBox?: boolean;
  isHideTitle?: boolean;
  isNewIsdBooking?: boolean;
}

const RentalGuaranteeItem: React.FC<IRentalGuaranteeItem> = ({
  name,
  type,
  vendorServiceCode,
  uniqueCode,
  localDailyPrice,
  localTotalPrice,
  localCurrencyCode,
  description,
  descriptionNew,
  allTags = [],
  extDescription,
  onPressButton,
  onPressTitle,
  index,
  selected,
  isHideOnPress,
  isLast = false,
  showTopBorder = true,
  isShowLabel = true,
  isHideCheckBox,
  isHideTitle,
  isNewIsdBooking,
}) => {
  const [isCheck, setIsCheck] = useState(false);
  const switchCheckStatus = () => {
    const checked = !isCheck;
    setIsCheck(checked);
    onPressButton({
      index,
      name,
      checked,
      vendorServiceCode,
      uniqueCode,
      localDailyPrice,
    });
  };

  if (selected !== isCheck) {
    setIsCheck(selected);
  }

  const Wrapper = isHideOnPress ? View : BbkComponentTouchable;
  const insuranceLabel = allTags?.filter(item => !item.code);
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  return (
    <View
      testID={UITestID.car_testid_page_order_detail_car_service}
      style={xMergeStyles([
        Utils.isCtripIsd() ? styles.indeTermNew : styles.indeTerm,
        showTopBorder && styles.indeTermBorder,
        isLast && styles.isLastIndeTerm,
      ])}
    >
      <View
        style={xMergeStyles([
          styles.indeTermCon,
          Utils.isCtripIsd() && styles.mr0,
        ])}
      >
        {!isHideTitle && (
          <Wrapper
            testID={`${UITestID.car_testid_page_booking_carservice_item}_${name}`}
            onPress={() => onPressTitle({ uniqueCode })}
          >
            <View
              style={xMergeStyles([
                styles.indNam,
                Utils.getByteLength(name) > maxByteLength && styles.flexStart,
              ])}
            >
              <BbkComponentText
                style={Utils.isCtripIsd() ? styles.indBriNew : styles.indBri}
              >
                {name}
              </BbkComponentText>
              {!isHideOnPress && (
                <View
                  testID={
                    UITestID.car_testid_page_order_detail_car_service_detail
                  }
                  style={styles.indNam}
                >
                  <BbkComponentText
                    style={xMergeStyles([
                      isISDInterestPoints
                        ? styles.indDetailNew
                        : styles.indDetail,
                      styles.indDetailMrg,
                    ])}
                  >
                    {texts.goDetail}
                  </BbkComponentText>
                  <BbkComponentText
                    type="icon"
                    style={xMergeStyles([
                      styles.titleIcon,
                      isISDInterestPoints
                        ? styles.indDetailNew
                        : styles.indDetail,
                    ])}
                  >
                    {icon.arrowRight}
                  </BbkComponentText>
                </View>
              )}
            </View>
          </Wrapper>
        )}

        <View>
          {Utils.isCtripIsd()
            ? descriptionNew?.map(
                (desc, index) =>
                  !!desc && (
                    <BbkAddedServiceDesc
                      key={`${desc}_${index}`}
                      desc={desc.description}
                      isContains={desc.contains}
                      allTags={allTags}
                      style={styles.w100}
                    />
                  ),
              )
            : description?.map(
                (desc, index) =>
                  !!desc && (
                    <BbkAddedServiceDesc
                      key={`${desc}_${index}`}
                      desc={desc}
                      allTags={allTags}
                    />
                  ),
              )}
        </View>
        <View>
          {!!extDescription && (
            <View style={styles.indExt}>
              <BbkComponentText style={styles.indExtTex}>
                {extDescription}
              </BbkComponentText>
            </View>
          )}
        </View>
        {isShowLabel && (
          <View style={xMergeStyles([styles.indNam, styles.labels])}>
            {insuranceLabel?.map(item => (
              <InsuranceLabelItem
                key={item.title}
                name={item.title}
                isBlue={true}
              />
            ))}
            <InsuranceLabelItem name={texts.includedByCar} />
          </View>
        )}
      </View>
      <View
        style={xMergeStyles([
          layout.alignHorizontal,
          !Utils.isCtripIsd() && styles.cur,
        ])}
      >
        {!isHideCheckBox && (
          <>
            <View style={styles.indeTermCost}>
              <View style={layout.flexRow}>
                {type === ProductType.select && (
                  <BbkComponentText style={styles.addSymbol}>
                    +
                  </BbkComponentText>
                )}
                <BbkCurrencyFormatter
                  currency={localCurrencyCode}
                  price={Utils.isCtripIsd() ? localDailyPrice : localTotalPrice}
                  currencyStyle={xMergeStyles([
                    styles.indePriDig,
                    type === ProductType.include && styles.fontPrimary,
                  ])}
                  priceStyle={xMergeStyles([
                    styles.indePriDigPrice,
                    type === ProductType.include && styles.fontPrimary,
                  ])}
                  wrapperStyle={layout.rowEnd}
                  isNew={true}
                />

                {Utils.isCtripIsd() && (
                  <BbkComponentText
                    style={xMergeStyles([
                      styles.uintDay,
                      type === ProductType.include && styles.uintDayInclude,
                    ])}
                  >
                    {texts.uintDay}
                  </BbkComponentText>
                )}
              </View>
            </View>
            {type === ProductType.select ? (
              <InsuranceCheckBox
                isSelected={isCheck}
                onPress={switchCheckStatus}
              />
            ) : (
              <BbkComponentText
                style={
                  Utils.isCtripIsd()
                    ? styles.indeTermStaNew
                    : styles.indeTermSta
                }
              >
                {type === ProductType.include
                  ? texts.listCombineIncluded
                  : texts.present}
              </BbkComponentText>
            )}
          </>
        )}
      </View>
    </View>
  );
};

export default RentalGuaranteeItem;
