import Image from '@c2x/components/Image';
import CRNLottieAnimation from '@c2x/components/LottieAnimation';
/**
 * LottieAnimation 组件封装
 * @date 2021-03-22 11:06:49
 */

import React, { useEffect, useRef, CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
import c2xStyles from './lottieAnimation.module.scss';
/**
 * LottieAnimation 组件封装
 * @date 2021-03-22 11:06:49
 */

import CarFetch from '../../../Util/CarFetch';

interface ILottieAnimation {
  jsonUrl?: string;
  imageUrl?: string;
  style?: CSSProperties;
  imageStyle?: CSSProperties;
  lottieStyle?: CSSProperties;
  loop?: boolean;
  getRef?: (ref: any) => void;
}

const LottieAnimation: React.FC<ILottieAnimation> = ({
  jsonUrl,
  imageUrl,
  style,
  imageStyle,
  lottieStyle,
  loop = true,
  getRef,
}) => {
  const Lottie = useRef(null);
  const [data, setData] = React.useState(null);
  useEffect(() => {
    async function fetchJSON() {
      const json = await CarFetch.getLottieJson(jsonUrl);
      setData(json);
    }
    fetchJSON();
  }, [jsonUrl]);
  useEffect(() => {
    if (data) {
      setTimeout(() => {
        Lottie?.current?.play(-1, -1);
        getRef?.(Lottie.current);
      }, 0);
    }
  }, [data, getRef]);
  return (
    <View style={style}>
      {data ? (
        <CRNLottieAnimation
          ref={Lottie}
          source={data}
          loop={loop}
          style={xMergeStyles([style, lottieStyle])}
        />
      ) : (
        imageUrl && (
          <Image
            className={c2xStyles.imageWrap}
            style={imageStyle}
            source={{
              uri: imageUrl,
            }}
            resizeMode="cover"
          />
        )
      )}
    </View>
  );
};

LottieAnimation.defaultProps = {
  jsonUrl: '',
  imageUrl: '',
  style: null,
  imageStyle: null,
  lottieStyle: null,
  loop: true,
  getRef: () => {},
};

export default LottieAnimation;
