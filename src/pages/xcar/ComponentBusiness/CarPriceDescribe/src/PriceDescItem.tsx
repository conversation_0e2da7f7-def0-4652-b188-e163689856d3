/* eslint-disable @typescript-eslint/naming-convention */
import StyleSheet from '@c2x/apis/StyleSheet';
/**
 *  列表页一级日均和总价UI
 */
import React, { CSSProperties } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { font, color, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyle from './priceDescItemC2xStyle.module.scss';
import { Platform } from '../../../Constants/Index';
import { GetAB } from '../../../Util/Index';
import UITestId from '../../../Constants/UITestID';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, isIos } = BbkUtils;
const noop = () => {};
const style = StyleSheet.create({
  priceOrigin: {
    textDecorationLine: 'line-through',
  },
  priceOriginGs2: {
    ...font.labelLStyle,
  },
  priceOriginGs2New: {
    ...font.F_24_10_regular,
  },
  singlePriceStyle: {
    marginBottom: -BbkUtils.getPixel(4),
  },
  totalPrice: {
    fontSize: font.labelLLightStyle.fontSize,
    color: color.fontSecondary,
  },
  totalPriceWrap: {
    marginTop: getPixel(isIos ? 3 : 11),
  },
  totalPriceNew: {
    ...font.F_22_10_regular,
    color: color.C_555555,
    top: getPixel(isIos ? -1 : 0),
  },
  totalPriceWrapStyle: {
    top: getPixel(isIos ? 0 : -2),
  },
  otherDesc: {
    ...font.labelLLightStyle,
    color: color.fontSecondary,
    marginTop: getPixel(4),
  },
  otherDescNew: {
    ...font.F_22_10_regular,
    color: color.C_555555,
    top: getPixel(isIos ? -1 : 0),
  },
  totalPriceMl: {
    marginLeft: getPixel(5),
  },
  totalPriceMR: {
    marginRight: getPixel(3),
  },
  totalPriceStyle: {
    fontSize: font.labelXLStyle.fontSize,
    color: color.fontSecondary,
  },
  totalPriceStyleNew: {
    fontSize: font.F_24_10_regular.fontSize,
    color: color.C_555555,
  },
  totalHelpIcon: {
    fontSize: getPixel(24),
    color: color.C_555555,
    top: getPixel(isIos ? 0 : 1),
  },
  singlePriceDescIcon: {
    fontSize: getPixel(24),
    color: color.fontSecondary,
  },
  singlePriceDescIconNew: {
    fontSize: getPixel(24),
    color: color.fontSecondary,
    top: getPixel(1),
  },
  dayAverageCurrency: {
    ...font.title1BoldStyle,
    fontSize: font.labelSLightStyle.fontSize,
    color: color.orangePrice,
  },
  dayAveragePrice: {
    color: color.orangePrice,
    ...font.title4BoldStyle,
  },
  dayOriginPriceWrap: {
    marginBottom: BbkUtils.isAndroid ? 0 : getPixel(2),
  },
});

export interface priceType {
  price: number;
  originalTotalPrice?: number;
}
interface IDayAveragePriceDesc {
  price: number;
  descText: string;
  descStyle?: CSSProperties;
  prefixDescText?: string;
  priceStyle?: CSSProperties;
  currencyStyle?: CSSProperties;
  originPriceStyle?: CSSProperties;
  originPriceWrapStyle?: CSSProperties;
  originPrice?: priceType;
  singlePriceWrapStyle?: CSSProperties;
  isNew?: boolean;
}

// 日均价
export const DayAveragePriceDesc = (props: IDayAveragePriceDesc) => {
  const {
    price,
    descText,
    prefixDescText,
    descStyle,
    priceStyle,
    currencyStyle,
    originPrice,
    originPriceStyle,
    originPriceWrapStyle,
    singlePriceWrapStyle,
    isNew,
  } = props;
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  return (
    <View className={c2xStyle.dayAveragePriceWrap}>
      {!!prefixDescText && (
        <BbkText className={c2xStyle.descText}>{prefixDescText}</BbkText>
      )}
      {originPrice?.price && (
        <BbkCurrencyFormatter
          price={originPrice?.price}
          currency={Platform.CN_CURRENCY_CODE}
          wrapperStyle={xMergeStyles([
            style.dayOriginPriceWrap,
            originPriceWrapStyle,
          ])}
          currencyStyle={xMergeStyles([
            style.priceOrigin,
            isISDInterestPoints
              ? style.priceOriginGs2New
              : style.priceOriginGs2,
            originPriceStyle,
          ])}
          priceStyle={xMergeStyles([
            style.priceOrigin,
            isISDInterestPoints
              ? style.priceOriginGs2New
              : style.priceOriginGs2,
            originPriceStyle,
          ])}
          isNew={isNew}
        />
      )}
      <BbkText
        className={classNames(
          c2xCommonStyles.c2xTextDefaultCss,
          isISDInterestPoints && isIos
            ? c2xStyle.descTextNew
            : c2xStyle.descText,
          c2xStyle.dailyPriceUnit,
        )}
        style={descStyle}
      >
        {descText}
      </BbkText>
      <BbkCurrencyFormatter
        wrapperStyle={xMergeStyles([
          BbkUtils.isAndroid && style.singlePriceStyle,
          singlePriceWrapStyle,
        ])}
        price={price}
        currency={Platform.CN_CURRENCY_CODE}
        currencyStyle={xMergeStyles([style.dayAverageCurrency, currencyStyle])}
        priceStyle={xMergeStyles([style.dayAveragePrice, priceStyle])}
        isNew={isNew}
        testID={UITestId.car_testid_comp_vehicle_day_price}
      />
    </View>
  );
};

interface ITotalPriceDesc {
  minTotalPrice?: number;
  minTotalPriceDesc?: string;
  minTotalPriceOtherDesc?: string;
  totalPriceStyle?: CSSProperties;
  totalPriceWrapStyle?: CSSProperties;
  totalPricePress?: () => void;
  onTotalPriceLayOut?: (e: any) => void;
  hasMarketTagsUpgrade?: boolean;
}
// 总价
export const TotalPriceDesc = (props: ITotalPriceDesc) => {
  const {
    minTotalPrice,
    minTotalPriceDesc,
    minTotalPriceOtherDesc,
    totalPriceStyle,
    totalPriceWrapStyle,
    totalPricePress = noop,
    onTotalPriceLayOut = noop,
    hasMarketTagsUpgrade = false,
  } = props;
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  return (
    <View
      style={{
        alignItems: 'flex-end',
      }}
    >
      {minTotalPrice >= 0 && (
        <View
          onLayout={onTotalPriceLayOut}
          onPress={totalPricePress}
          testID={UITestId.car_testid_comp_vehicle_price_help}
        >
          <View className={c2xStyle.singlePriceWrap}>
            <BbkTouchable
              style={layout.flexRow}
              onPress={totalPricePress}
              testID={UITestId.car_testid_pricedesc_totalprice}
            >
              {!!totalPricePress && !!minTotalPrice && (
                <BbkTouchable
                  className={
                    hasMarketTagsUpgrade
                      ? c2xStyle.helpIconUpgrade
                      : c2xStyle.helpIcon
                  }
                  onPress={totalPricePress}
                  testID={UITestId.car_testid_pricedesc_help}
                >
                  <BbkText style={style.totalHelpIcon} type="icon">
                    {icon.help}
                  </BbkText>
                </BbkTouchable>
              )}
              {!!minTotalPriceDesc && (
                <BbkText
                  style={xMergeStyles([
                    style.totalPriceNew,
                    !hasMarketTagsUpgrade && style.totalPriceMl,
                    isISDInterestPoints && style.totalPriceMR,
                  ])}
                >
                  {minTotalPriceDesc}
                </BbkText>
              )}
            </BbkTouchable>
            <View style={style.totalPriceWrap}>
              <BbkCurrencyFormatter
                testID={UITestId.car_testid_comp_vehicle_total_price}
                price={minTotalPrice}
                currency={Platform.CN_CURRENCY_CODE}
                wrapperStyle={style.totalPriceWrapStyle}
                currencyStyle={style.totalPriceNew}
                priceStyle={xMergeStyles([
                  style.totalPriceStyleNew,
                  totalPriceStyle,
                ])}
                // isNew={!!minTotalPriceOtherDesc} // todo-lxj 安卓特殊字体文字显示不全修复方案待确认
              />
            </View>
            {!!minTotalPriceOtherDesc && (
              <BbkText style={style.otherDescNew}>
                {minTotalPriceOtherDesc}
              </BbkText>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

export default {
  TotalPriceDesc,
  DayAveragePriceDesc,
};
