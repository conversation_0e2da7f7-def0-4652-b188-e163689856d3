@import '@tokenColorScss';

.rebookWrap {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  background-color: $orangeBg;
}
.tipInfo {
  flex-direction: row;
  margin-left: 24px;
  margin-right: 24px;
  padding-top: 16px;
  padding-bottom: 16px;
}
.penaltyTipInfo {
  width: 546px;
}
.rebookIcon {
  color: $orangeBase;
  font-size: 28px;
  margin-right: 8px;
  margin-top: 6px;
}
.rebookContent {
  color: $orangeBase;
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  flex: 1;
}
.applyPenalty {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-right: 24px;
  padding-top: 16px;
  padding-bottom: 16px;
  width: 156px;
}
.applyPenaltyText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueBase;
}
.applyPenaltyIcon {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueBase;
}
