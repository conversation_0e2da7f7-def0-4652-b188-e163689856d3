import React, {
  useEffect,
  CSSProperties,
  useState,
  useRef,
  ReactNode,
} from 'react';
import { XView as View, XAnimated, xCreateAnimation } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';

const { useMemoizedFn } = BbkUtils;

interface IVerticalMarqueeProps {
  /** 需要滚动的子元素 */
  children: ReactNode[];
  /** 单个项目高度（单位：px） */
  itemHeight?: number;
  /** 动画持续时间（单位：ms） */
  duration?: number;
  /** 滚动间隔时间（单位：ms） */
  interval?: number;
  /** 是否启用动画 */
  enableAnimate?: boolean;
  /** 容器样式 */
  style?: CSSProperties;
}

const VerticalMarquee: React.FC<IVerticalMarqueeProps> = props => {
  const {
    children,
    itemHeight,
    duration = 500,
    interval = 3000,
    enableAnimate,
    style,
  } = props;

  const [animationData, setAnimationData] = useState<any>(null);
  const translateLength = useRef(0);
  const setTimeoutTimer = useRef<any>(null);
  const animateTimer = useRef<any>(null);
  const initTimer = useRef<any>(null);
  const itemCount = children?.length || 0;

  const startAnimation = useMemoizedFn(anim => {
    if (animateTimer.current) {
      clearInterval(animateTimer.current);
    }
    if (initTimer.current) {
      clearTimeout(initTimer.current);
    }

    // 确保在所有 action 执行完后再开始动画
    Promise.resolve().then(() => {
      initTimer.current = setTimeout(() => {
        animateTimer.current = setInterval(() => {
          translateLength.current += 1;
          const y = -(translateLength.current * itemHeight);
          anim.translateY(y).step();
          setAnimationData(anim.export());

          if (translateLength.current - 1 === itemCount) {
            translateLength.current = 0;
            anim.translateY(0).step({ duration: 0 });
            setAnimationData(anim.export());

            if (setTimeoutTimer.current) {
              clearTimeout(setTimeoutTimer.current);
            }
            setTimeoutTimer.current = setTimeout(() => {
              translateLength.current = 1;
              anim.translateY(-itemHeight).step({ duration });
              setAnimationData(anim.export());
            }, 8);
          }
        }, interval);
      }, 0);
    });
  });

  useEffect(() => {
    if (enableAnimate) {
      const animation = xCreateAnimation({
        duration,
        delay: 0,
        timingFunction: 'ease-in-out',
      });
      setAnimationData(animation.export());
      startAnimation(animation);
    }

    return () => {
      if (animateTimer.current) {
        clearInterval(animateTimer.current);
      }
      if (setTimeoutTimer.current) {
        clearTimeout(setTimeoutTimer.current);
      }
      if (initTimer.current) {
        clearTimeout(initTimer.current);
      }
    };
  }, [startAnimation, enableAnimate, duration]);

  if (!children || children.length === 0) return null;

  return (
    <View style={style}>
      <XAnimated.View animation={animationData}>
        {children}
        {itemCount > 1 && React.cloneElement(children[0] as any)}
      </XAnimated.View>
    </View>
  );
};

export default React.memo(VerticalMarquee);
