import { clone as lodashClone } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import ScrollView from '@c2x/components/CScrollView';
import React, { useEffect, useState, CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, layout, icon, space } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkCheckBox from '@ctrip/rn_com_car/dist/src/Components/Basic/Checkbox';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import BbkRadioButton from '@ctrip/rn_com_car/dist/src/Components/Basic/RadioButton';
import {
  getThemeAttributes,
  withTheme,
} from '@ctrip/rn_com_car/dist/src/Theming';
import { BaseTheme } from '@ctrip/rn_com_car/dist/src/Theming/src/BaseTheme';
import c2xSelectMenuItemStyle from './indexC2xSelectMenuItemStyle.module.scss';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import { UITestID } from '../../../Constants/Index';
import { GetAB, Utils } from '../../../Util/Index';

const { selector } = BbkUtils;

export interface IBbkSelectMenu {
  filterData: Array<IBbkSelectItem>;
  type: SelectMenuType;
  onScroll?: () => void;
  onToggle?: (item: IBbkSelectItem, handleType?: string) => void;
  maxHeight?: number;
  maxWidth?: number;
  listThreshold?: number;
  isRadioCheckBox?: boolean;
  style?: CSSProperties;
  textStyle?: CSSProperties;
  theme?: BaseTheme;
}

export enum SelectMenuType {
  Single = 'single',
  Multiple = 'multiple',
}

export interface IBbkSelectMenuItem {
  type: SelectMenuType;
  item: IBbkSelectItem;
  isLastItem: boolean;
  isSelected: boolean;
  isRadioCheckBox?: boolean;
  textStyle?: CSSProperties;
  theme?: BaseTheme;
  onChoose: (isSelected: boolean) => void;
}

export type IBbkSelectItem = {
  name: string;
  code?: string | number;
  isSelected: boolean;
};
const SelectMenuItemStyle = StyleSheet.create({
  itemBorder: {
    borderStyle: 'solid',
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
});

const SelectMenuItem: React.FC<IBbkSelectMenuItem> = withTheme(
  ({
    type,
    item,
    isLastItem,
    isSelected,
    isRadioCheckBox,
    onChoose,
    theme,
    textStyle,
  }) => {
    const themes = getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as any;
    const borderColor = (themes && themes.bbkLineColor) || color.fontSubLight;

    return (
      <View>
        {selector(
          type === SelectMenuType.Multiple,
          <BbkTouchable
            className={c2xSelectMenuItemStyle.item}
            style={xMergeStyles([
              layout.flex1,
              !isLastItem && SelectMenuItemStyle.itemBorder,
              { borderColor },
            ])}
            onPress={() => {
              onChoose(!isSelected);
            }}
            testID={`${UITestID.car_testid_page_list_filtermodal_sort_item}_${item.name}`}
          >
            <BbkText
              className={c2xSelectMenuItemStyle.itemText}
              style={xMergeStyles([
                textStyle && textStyle,
                isSelected && {
                  color: color.deepBlueBase,
                },
              ])}
            >
              {item.name}
            </BbkText>
            {isRadioCheckBox ? (
              <BbkRadioButton
                checked={isSelected}
                onCheckedChange={(checked: boolean) => {
                  onChoose(checked);
                }}
                testID={`${UITestID.car_testid_page_list_filtermodal_sort_item_choose}_${item.name}`}
              >
                {icon.tick}
              </BbkRadioButton>
            ) : (
              <BbkCheckBox
                checked={isSelected}
                onCheckedChange={(checked: boolean) => {
                  onChoose(checked);
                }}
                testID={`${UITestID.car_testid_page_list_filtermodal_sort_item_choose}_${item.name}`}
              >
                {icon.tick}
              </BbkCheckBox>
            )}
          </BbkTouchable>,
          <BbkTouchable
            className={c2xSelectMenuItemStyle.item}
            style={xMergeStyles([
              layout.flex1,
              !isLastItem && SelectMenuItemStyle.itemBorder,
              { borderColor },
            ])}
            onPress={() => {
              onChoose(!isSelected);
            }}
            testID={`${UITestID.car_testid_page_list_filtermodal_sort_item}_${item.name}`}
          >
            <BbkText
              className={c2xSelectMenuItemStyle.itemText}
              style={
                isSelected && {
                  color: color.deepBlueBase,
                }
              }
            >
              {item.name}
            </BbkText>
            {isSelected && (
              <BbkText
                type="icon"
                className={c2xSelectMenuItemStyle.itemSingeSelectIcon}
              >
                {icon.circleTickFilled}
              </BbkText>
            )}
          </BbkTouchable>,
        )}
      </View>
    );
  },
);

const updateItemSelected = (filterData, currentItem, isSelected, type) => {
  const newFilterData = lodashClone(filterData);

  newFilterData.forEach(item => {
    if (currentItem && item.code === currentItem.code) {
      item.isSelected = isSelected;
    } else if (type === SelectMenuType.Single) {
      item.isSelected = false;
    }
  });

  return newFilterData;
};

export const SelectMenuContent: React.FC<IBbkSelectMenu> = ({
  filterData,
  type,
  textStyle,
  style,
  isRadioCheckBox = true,
  onToggle,
}) => {
  const [filterDataState, setFilterData] = useState(filterData);

  useEffect(() => {
    setFilterData(filterData);
  }, [filterData]);

  return (
    <View style={style}>
      {filterDataState &&
        filterDataState.length > 0 &&
        filterDataState.map((item, index) => {
          const isLastItem = index === filterDataState.length - 1;
          return (
            <SelectMenuItem
              type={type}
              item={item}
              key={item.code}
              isLastItem={isLastItem}
              isRadioCheckBox={isRadioCheckBox}
              isSelected={item.isSelected}
              textStyle={textStyle}
              onChoose={isSelected => {
                const newFilterData = updateItemSelected(
                  filterDataState,
                  item,
                  isSelected,
                  type,
                );
                setFilterData(newFilterData);
                if (type === SelectMenuType.Single) {
                  onToggle(item);
                } else {
                  onToggle(item, selector(isSelected, 'add', 'delete'));
                }
              }}
            />
          );
        })}
    </View>
  );
};

const BbkSelectMenu: React.FC<IBbkSelectMenu> = ({
  filterData,
  type,
  onScroll = () => {},
  onToggle = () => {},
  isRadioCheckBox,
  style,
  textStyle,
  theme,
}) => {
  const themes = getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as any;
  const backgroundColor: any =
    (themes && themes.backgroundColor) || color.white;
  const width100Style = useWindowSizeChanged();
  return (
    <View
      testID={UITestID.car_testid_page_list_filter_sort_modal}
      style={width100Style}
    >
      {
        // @ts-ignore
        <ScrollView
          style={{
            ...width100Style,
            backgroundColor,
          }}
          // @ts-ignore
          onScroll={onScroll}
        >
          <SelectMenuContent
            filterData={filterData}
            type={type}
            style={{
              paddingLeft: space.spaceXXL,
              paddingRight: space.spaceXXL,
              backgroundColor,
              flex: 1,
              ...style,
            }}
            textStyle={textStyle}
            isRadioCheckBox={isRadioCheckBox}
            onToggle={onToggle}
          />
        </ScrollView>
      }
    </View>
  );
};

export default withTheme(BbkSelectMenu);
