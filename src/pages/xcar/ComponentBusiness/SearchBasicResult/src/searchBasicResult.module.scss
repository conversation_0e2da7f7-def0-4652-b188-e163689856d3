@import '../../../Common/src/Tokens/tokens/color.scss';

.defaultLabel {
  background-color: $defaultLabel;
  border-radius: 4px;
  width: 48px;
  height: 32px;
  margin-top: 6px;
  margin-left: 32px;
  margin-right: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.defaultLabelTex {
  color: $white;
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-left: 2px;
}
.centerView {
}
.mainTextView {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.mainTex {
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.noResult {
  color: rgba($black, 0.3);
}
.subTextView {
  margin-top: 8px;
}
.subTex {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSubDark;
}
.wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  padding-bottom: 26px;
  padding-top: 26px;
  flex: 1;
}
.infoView {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.rightView {
  flex-direction: row;
  justify-content: flex-end;
}
.rightViewTex {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontGrayBlue;
  margin-right: 8px;
}
.tickView {
  align-self: center;
  margin-right: 8px;
}
.tick {
  color: $blueBase;
  font-size: 32px;
}
