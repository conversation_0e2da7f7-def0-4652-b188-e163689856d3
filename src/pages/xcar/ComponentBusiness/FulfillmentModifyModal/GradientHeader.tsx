import React from 'react';
import { XView as View, XLinearGradient as LinearGradient } from '@ctrip/xtaro';
import { color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkComponentTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkComponentText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './GradientHeaderC2xStyles.module.scss';

interface GradientHeaderProps {
  title: string;
  onClose: () => void;
}

const GradientHeader: React.FC<GradientHeaderProps> = ({ title, onClose }) => {
  return (
    <View className={c2xStyles.headerStyle}>
      <LinearGradient
        className={c2xStyles.headerGradient}
        colors={[color.C_EAF6FF, color.C_ECF6FF, color.C_FDFEFF, color.C_FFF]}
        locations={[0, 0.3, 0.7, 1]}
        start={{ x: 0.5, y: 0.0 }}
        end={{ x: 0.5, y: 1.0 }}
      />
      <View className={c2xStyles.headerContent}>
        <BbkComponentTouchable
          onPress={() => {
            if (onClose) onClose();
          }}
          className={c2xStyles.coverCloseIcon}
        >
          <BbkComponentText type="icon" className={c2xStyles.closeText}>
            {icon.cross}
          </BbkComponentText>
        </BbkComponentTouchable>

        <BbkComponentText className={c2xStyles.titleText}>
          {title}
        </BbkComponentText>
      </View>
    </View>
  );
};

export default GradientHeader;
