import React, { useState } from 'react';
import { XView as View, xClassNames as classNames } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Image from '@c2x/components/Image';
import { useSelector, useDispatch } from 'react-redux';
import BbkHalfPageModal from '../HalfPageModal';
import GradientHeader from './GradientHeader';
import {
  OrderFulfillmentModifyInfoStatusEnum,
  FulfillmentModifyInfoTypeEnum,
  FulfillmentModifyInfoStatusEnum,
  FulfillmentModifyInfoDTO,
} from '../../Types/Dto/QueryVehicleDetailInfoResponseType';
import { ImageUrl } from '../../Constants/Index';
import { ModifyConfirmStatus } from '../../State/OrderDetail/Types';
import {
  getOrderFulfillmentModifyInfo,
  getOrderId,
} from '../../State/OrderDetail/Selectors';
import { fulfillmentModifyConfirm } from '../../State/OrderDetail/Actions';
import c2xStyles from './fulfillmentModifyModalC2XStyle.module.scss';

const { getPixel, vw, isAndroid } = BbkUtils;

interface IFulfillmentModifyModal {
  visible: boolean;
  onClose: () => void;
}

interface InfoItemProps {
  data: {
    originalInfo?: FulfillmentModifyInfoDTO['originalInfo'];
    modifyInfo?: FulfillmentModifyInfoDTO['modifyInfo'];
    type?: FulfillmentModifyInfoDTO['type'];
    status?: FulfillmentModifyInfoDTO['status'];
    desc?: FulfillmentModifyInfoDTO['desc'];
  };
}

const LabelView = ({ isOriginal }: { isOriginal: boolean }) => (
  <View
    className={classNames(
      c2xStyles.labelContainer,
      isOriginal ? c2xStyles.labelOriginal : c2xStyles.labelNew,
    )}
  >
    <Text
      className={c2xStyles.labelText}
      style={{ lineHeight: isAndroid ? getPixel(30) : getPixel(32) }}
    >
      {isOriginal ? '原' : '新'}
    </Text>
  </View>
);

const InfoItem: React.FC<InfoItemProps> = ({ data }) => {
  const originalInfo = data?.originalInfo;
  const modifyInfo = data?.modifyInfo;
  const type = data?.type;
  const status = data?.status;
  const desc = data?.desc;
  const [originalInfoHeight, setOriginalInfoHeight] = useState(0);
  const [modifyInfoHeight, setModifyInfoHeight] = useState(0);

  if (!originalInfo || !modifyInfo) {
    return null;
  }

  const title = {
    [FulfillmentModifyInfoTypeEnum.Time]: '时间',
    [FulfillmentModifyInfoTypeEnum.Location]: '地点',
  }[type];

  return (
    <View className={c2xStyles.infoItemContainer}>
      <View className={c2xStyles.infoItem}>
        {status === FulfillmentModifyInfoStatusEnum.Rejected && (
          <Image
            src={`${ImageUrl.DIMG04_PATH}1tg0312000jz5ls5x1538.png`}
            className={c2xStyles.statusImage}
          />
        )}

        {status === FulfillmentModifyInfoStatusEnum.Invalid && (
          <Image
            src={`${ImageUrl.DIMG04_PATH}1tg0112000jz5miro49EB.png`}
            className={c2xStyles.statusImage}
          />
        )}

        <Text className={c2xStyles.infoTitle}>{title}</Text>

        <View>
          <View
            className={c2xStyles.infoContent}
            onLayout={event => {
              const { height } = event.nativeEvent.layout;
              setOriginalInfoHeight(height);
            }}
          >
            <LabelView isOriginal={true} />
            <Text
              className={classNames(
                c2xStyles.valueText,
                c2xStyles.originalText,
              )}
            >
              {originalInfo}
            </Text>
            <View
              className={classNames(
                c2xStyles.verticalLine,
                c2xStyles.verticalBottomLine,
              )}
              style={{
                height: originalInfoHeight * 0.5 - getPixel(22),
              }}
            />
          </View>

          <View className={c2xStyles.verticalLine} />

          <View
            className={c2xStyles.infoContent}
            onLayout={event => {
              const { height } = event.nativeEvent.layout;
              setModifyInfoHeight(height);
            }}
          >
            <View
              className={classNames(
                c2xStyles.verticalLine,
                c2xStyles.verticalTopLine,
              )}
              style={{
                height: modifyInfoHeight * 0.5 - getPixel(22),
              }}
            />
            <LabelView isOriginal={false} />
            <Text
              className={classNames(c2xStyles.valueText, c2xStyles.newText)}
            >
              {modifyInfo}
            </Text>
          </View>
        </View>
      </View>

      {desc && (
        <View className={c2xStyles.fulfillmentModifyRemind}>
          <Text type="icon" className={c2xStyles.fulfillmentModifyInfoIcon}>
            {icon.remind}
          </Text>
          <Text className={c2xStyles.fulfillmentModifyText}>{desc}</Text>
        </View>
      )}
    </View>
  );
};

const FulfillmentModifyModal: React.FC<IFulfillmentModifyModal> = ({
  visible,
  onClose,
}) => {
  const dispatch = useDispatch();
  const orderFulfillmentModifyInfo = useSelector(getOrderFulfillmentModifyInfo);
  const orderId = useSelector(getOrderId);

  const title = orderFulfillmentModifyInfo?.fulfillmentModify?.title || '';
  const modifyId = orderFulfillmentModifyInfo?.modifyId || '';
  const fulfillmentModifyInfoList =
    orderFulfillmentModifyInfo?.fulfillmentModify?.fulfillmentModifyInfoList ||
    [];
  const description = orderFulfillmentModifyInfo?.fulfillmentModify?.desc || '';
  const status = orderFulfillmentModifyInfo?.status;
  const showActionButtons =
    status === OrderFulfillmentModifyInfoStatusEnum.Unconfirmed;

  const pageModalProps = {
    visible,
    onMaskPress: onClose,
  };

  const handleConfirm = () => {
    onClose();
    dispatch(
      fulfillmentModifyConfirm({
        orderId,
        modifyId,
        confirmStatus: ModifyConfirmStatus.Agree,
      }),
    );
  };

  const handleCancel = () => {
    onClose();
    dispatch(
      fulfillmentModifyConfirm({
        orderId,
        modifyId,
        confirmStatus: ModifyConfirmStatus.Disagree,
      }),
    );
  };

  return (
    <BbkHalfPageModal
      pageModalProps={pageModalProps}
      headerDom={<GradientHeader title={title} onClose={onClose} />}
      contentStyle={{
        paddingBottom: getPixel(28),
        paddingTop: getPixel(28),
      }}
      footerShadow={false}
      footerChildren={
        <View
          className={c2xStyles.buttonContainer}
          style={{ marginBottom: isAndroid ? getPixel(55) : 0 }}
        >
          {showActionButtons ? (
            <>
              <Touchable
                className={classNames(c2xStyles.button, c2xStyles.cancelButton)}
                onPress={handleCancel}
              >
                <Text
                  className={classNames(
                    c2xStyles.buttonText,
                    c2xStyles.cancelButtonText,
                  )}
                >
                  不同意
                </Text>
              </Touchable>
              <Touchable
                className={classNames(
                  c2xStyles.button,
                  c2xStyles.confirmButton,
                )}
                onPress={handleConfirm}
              >
                <Text
                  className={classNames(
                    c2xStyles.buttonText,
                    c2xStyles.confirmButtonText,
                  )}
                >
                  同意
                </Text>
              </Touchable>
            </>
          ) : (
            <View style={{ height: getPixel(88) }} />
          )}
        </View>
      }
    >
      <View className={c2xStyles.container}>
        <View className={c2xStyles.infoContainer}>
          {fulfillmentModifyInfoList.map(item => (
            <InfoItem key={item.type} data={item} />
          ))}
        </View>

        <Text className={c2xStyles.tipText}>{description}</Text>
      </View>
    </BbkHalfPageModal>
  );
};

export default FulfillmentModifyModal;
