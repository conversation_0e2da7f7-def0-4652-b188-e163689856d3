@import '../../Common/src/Tokens/tokens/color.scss';

.headerStyle {
  position: relative;
  width: 100vw;
  height: 116px;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  overflow: hidden;
}

.headerGradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.headerContent {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.coverCloseIcon {
  position: absolute;
  left: 30px;
}

.closeText {
  font-size: 42px;
  font-weight: normal;
  color: $C_666;
}

.titleText {
  font-size: 34px;
  line-height: 42px;
  color: $C_333333;
  font-weight: bold;
  text-align: center;
}
