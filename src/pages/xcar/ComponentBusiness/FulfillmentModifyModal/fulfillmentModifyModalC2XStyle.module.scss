@import '../../Common/src/Tokens/tokens/color.scss';

.wrap {
  max-height: 80vh;
}

.container {
  padding-bottom: 24px;
}

.content {
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 28px;
  padding-bottom: 0;
}

.buttonContainer {
  width: 100vw;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 95px;
  padding-left: 32px;
  padding-right: 32px;
}

.button {
  flex: 1;
  height: 88px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cancelButton {
  background-color: $white;
  border-width: 2px;
  border-color: $C_006FF6;
  margin-right: 16px;
}

.confirmButton {
  background-color: $C_006FF6;
}

.buttonText {
  font-size: 34px;
  line-height: 42px;
}

.cancelButtonText {
  color: $C_006FF6;
}

.confirmButtonText {
  color: $white;
}

.infoContainer {
  gap: 32px;
}

.infoItemContainer {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.infoItem {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 38px 40px;
  border-width: 1px;
  border-color: $C_ddd;
  border-style: solid;
  border-radius: 16px;
}

.infoTitle {
  font-size: 30px;
  line-height: 40px;
  font-weight: bold;
  color: $fontPrimary;
  margin-right: 70px;
}

.infoContent {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.fulfillmentModifyRemind {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.fulfillmentModifyInfoIcon {
  line-height: 36px;
  color: $orangeBase;
  margin-right: 8px;
}

.fulfillmentModifyText {
  flex: 1;
  font-size: 24px;
  line-height: 36px;
  color: $orangeBase;
}

.verticalLine {
  margin-left: 16px;
  width: 1px;
  height: 24px;
  background-color: $C_B8C6D9;
}

.verticalTopLine {
  position: absolute;
  left: 0;
  top: 0;
}

.verticalBottomLine {
  position: absolute;
  left: 0;
  bottom: 0;
}

.labelContainer {
  width: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.labelOriginal {
  background-color: $C_B8C6D9;
}

.labelNew {
  background-color: $C_08A66F;
}

.labelText {
  width: 32px;
  font-size: 22px;
  color: $white;
  border-radius: 14px;
  text-align: center;
}

.valueText {
  font-size: 30px;
  line-height: 42px;
  width: 432px;
}

.originalText {
  color: $C_888888;
}

.newText {
  color: $C_111111;
}

.tipText {
  margin-top: 16px;
  font-size: 24px;
  color: $C_888888;
  line-height: 38px;
}

.statusImage {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 151px;
  height: 151px;
} 