@import '../../../Common/src/Tokens/tokens/color.scss';

.main {
  flex-direction: row;
  padding-top: 42px;
  padding-bottom: 32px;
  border-bottom-width: 1px;
  border-bottom-color: $lineColor;
}
.wrap {
  flex-direction: row;
  align-items: flex-start;
  flex: 1;
}
.leftIconStyle {
  color: $blueBase;
  margin-right: 10px;
  line-height: 40px;
  font-size: 32px;
}
.titleStyle {
  font-size: 32px;
  color: $blueGrayBase;
  font-weight: bold;
  line-height: 42px;
  max-width: 85%;
  flex-wrap: nowrap;
  font-family: PingFangSC-Regular;
}
.rightIconStyle {
  width: 40px;
  height: 40px;
  margin-left: 16px;
  background-color: $grayBg;
  border-radius: 50px;
  border-width: 1px;
  border-color: $grayPlaceholder;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 5px;
  padding-bottom: 5px;
}
.address {
  padding-left: 40px;
  padding-top: 12px;
}
.addressTex {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSubDark;
}
.tick {
  position: absolute;
  right: 32px;
  bottom: 0px;
  height: 156px;
  width: 32px;
  margin-left: 8px;
  flex-direction: row;
  align-items: center;
}
.tickStyle {
  font-size: 32px;
  line-height: 32px;
  color: $blueBase;
}
