/* eslint-disable default-case */
import Location from '@c2x/apis/Location';
import AlertDialog from '@c2x/apis/AlertDialog';
import DeviceEventEmitter from '@c2x/apis/DeviceEventEmitter';
import React, { PureComponent, CSSProperties } from 'react';
import {
  xApplication as Application,
  XView as View,
  xMergeStyles,
} from '@ctrip/xtaro';
/* bbk-component-business-migrate */

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import c2xStyles from './locationC2xStyles.module.scss';
import { Utils, CarLog, GetAB, CarFetch } from '../../../Util/Index';
import LogKeyDev from '../../../Constants/LogKeyDev';
import { CRNLocateType, LocationBizType } from '../../../Types/CRNTypes';

const { getPixel, isAndroid } = BbkUtils;

export interface PropsType {
  mainStyle?: CSSProperties;
  position?: LocationInfoProps;
  locationName?: string;
  onPressItem?: (data: any) => void;
  setPositionInfo?: (data: {}) => void;
  areaLocateModalDesc?: string;
}
interface StateProps {}

interface LocationInfoProps {
  positionInfo: any;
  positionLocation: any;
  positionStatus: {
    locationOn: boolean;
    locationStatus: string;
  };
}

export interface ILocateParams {
  crnLocateType: CRNLocateType;
  locationBizType?: LocationBizType;
  customerAlertMessage?: string;
}

const devTraceLocateRes = info => {
  const logInfo = {
    key: LogKeyDev.c_car_dev_location_res_pois,
    info,
  };
  CarLog.LogTraceDev(logInfo);
};

export const getLocationInfo = (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setPositionInfo = (data: any) => {},
  locateParams?: ILocateParams,
) => {
  const needBizType = Utils.compareVersion(Application.version, '8.41.2') >= 0;
  const params = {
    locateLevel: 0,
    bizType: locateParams?.locationBizType,
    locationType: locateParams?.crnLocateType || CRNLocateType.Default,
    customerAlertMessage: locateParams?.customerAlertMessage || '',
  };
  const locateConfig = needBizType ? params : { locateLevel: 0 };
  Location.locate(locateConfig)
    .then(async (data: any) => {
      const CN = ['CN', 'CHN'];
      const locationItem =
        data?.address?.pois?.length > 0 && data.address.pois[0];
      const lat =
        locationItem?.latitude ||
        locationItem?.point?.y.toFixed(6) ||
        (data?.address?.detailAddress && data.address.lat);
      const lng =
        locationItem?.longitude ||
        locationItem?.point?.x.toFixed(6) ||
        (data?.address?.detailAddress && data.address.lng);
      const name =
        locationItem?.name ||
        locationItem?.address ||
        data?.address?.detailAddress;
      const { province } = data.address;
      let cityId =
        data.ctripCity &&
        data.ctripCity.CityEntities[0] &&
        data.ctripCity.CityEntities[0].CityID;
      if (cityId === 669326) {
        cityId = 36;
      }
      const cityName =
        data.ctripCity &&
        data.ctripCity.CityEntities[0] &&
        data.ctripCity.CityEntities[0].CityName;
      let result = null;

      // 反查定位tag所对应的type类型
      const fetchParams = {
        poiInfo: {
          latitude: lat,
          longitude: lng,
          cityId,
          cityName,
          name,
          address: name,
          type: '',
          tag: locationItem?.tag,
        },
      };
      result = await CarFetch.completePoiInfo(fetchParams);

      const positionLocation = {
        cid: cityId,
        cname: cityName,
        country: data.ctripCity && data.ctripCity.CountryLocalName,
        realcountry: '',
        province,
        isDomestic: CN.indexOf(data.address.countryShortName) > -1,
        area: {
          id: '',
          locationName: name,
          countryShortName: data.address.countryShortName,
          name,
          ename: '',
          lat,
          lng,
          type: result?.poiInfo?.poiType || '',
          typename: '',
        },
        isFromPosition: true,
      };

      setPositionInfo({
        positionInfo: data,
        positionLocation,
        positionStatus: {
          locationOn: true,
          locationStatus: 'success',
        },
      });

      devTraceLocateRes({
        locateConfig,
        pois: data?.address?.pois,
      });
    })
    .catch(error => {
      setPositionInfo({
        positionStatus: {
          locationOn: false,
          locationStatus: 'failure',
        },
      });
      devTraceLocateRes({
        locateConfig,
        errorMessage: error?.errorMessage,
      });
    });
};

const webCheckLocationPermission = async () => {
  return { granted: 1 };
};

export const checkLocationPermission = (
  // eslint-disable-next-line @typescript-eslint/default-param-last, @typescript-eslint/no-unused-vars
  setPositionInfo = (data: any) => {},
  locateParams: ILocateParams,
) => {
  const checkLocationPermissionPromise =
    Utils.compareVersion(Application.version, '8.36.2') >= 0 && isAndroid
      ? webCheckLocationPermission
      : // @ts-ignore
        AlertDialog.checkLocationPermissionTimeRestrictPromise;
  checkLocationPermissionPromise().then((result: any) => {
    // eslint-disable-next-line eqeqeq
    if (result.granted == 1) {
      setPositionInfo({
        positionStatus: {
          locationOn: true,
          locationStatus: 'loading',
        },
      });
      getLocationInfo(setPositionInfo, locateParams);
    } else {
      setPositionInfo({
        positionStatus: {
          locationOn: false,
          locationStatus: 'closed',
        },
      });
    }
  });
};

const getLocationItem = (
  status,
  positionData,
  positionName,
  positionAddress,

  checkLocationPermission: (
    setPositionInfo,
    locateParams: ILocateParams,
  ) => void,
  setPositionInfo,
  onPressItem,
  isShowTickIcon,
  style,
  areaLocateModalDesc,
  // eslint-disable-next-line consistent-return
) => {
  switch (status) {
    case 'success':
      return (
        <BbkTouchable
          onPress={() =>
            onPressItem({
              rentalLocation: positionData,
            })
          }
          className={c2xStyles.main}
          style={xMergeStyles([
            style,
            { flexDirection: 'column', position: 'relative' },
          ])}
        >
          <View className={c2xStyles.wrap}>
            <BbkText type="icon" className={c2xStyles.leftIconStyle}>
              {icon.location_area}
            </BbkText>
            <BbkText numberOfLines={1} className={c2xStyles.titleStyle}>
              {positionName}
            </BbkText>
            <BbkTouchable
              hitSlop={{
                left: getPixel(10),
                right: getPixel(10),
                top: getPixel(10),
                bottom: getPixel(10),
              }}
              onPress={() =>
                checkLocationPermission(setPositionInfo, {
                  crnLocateType: CRNLocateType.Manual,
                  locationBizType: LocationBizType.CityChooseManual,
                  customerAlertMessage: areaLocateModalDesc,
                })
              }
            >
              <View className={c2xStyles.rightIconStyle}>
                <BbkText
                  type="icon"
                  style={{
                    color: color.grayBase,
                    fontSize: getPixel(28),
                  }}
                >
                  {icon.refresh}
                </BbkText>
              </View>
            </BbkTouchable>
          </View>
          <View className={c2xStyles.address}>
            <BbkText numberOfLines={1} className={c2xStyles.addressTex}>
              {positionAddress}
            </BbkText>
          </View>
          {isShowTickIcon && (
            <View className={c2xStyles.tick}>
              <BbkText type="icon" className={c2xStyles.tickStyle}>
                {icon.tick_area}
              </BbkText>
            </View>
          )}
        </BbkTouchable>
      );

    case 'loading':
      return (
        <View className={c2xStyles.main} style={style}>
          <BbkText type="icon" className={c2xStyles.leftIconStyle}>
            {icon.loading}
          </BbkText>
          <BbkText className={c2xStyles.titleStyle}>定位中...</BbkText>
        </View>
      );

    case 'failure':
      return (
        <BbkTouchable
          onPress={() =>
            checkLocationPermission(setPositionInfo, {
              crnLocateType: CRNLocateType.Manual,
              locationBizType: LocationBizType.CityChooseManual,
              customerAlertMessage: areaLocateModalDesc,
            })
          }
        >
          <View className={c2xStyles.main} style={style}>
            <BbkText type="icon" className={c2xStyles.leftIconStyle}>
              {icon.refresh}
            </BbkText>
            <BbkText className={c2xStyles.titleStyle}>
              定位失败，点击重试
            </BbkText>
          </View>
        </BbkTouchable>
      );

    case 'closed':
      return (
        <View className={c2xStyles.main}>
          <View style={{ alignItems: 'center' }}>
            <BbkText type="icon" className={c2xStyles.leftIconStyle}>
              {icon.circleWithSighFilled}
            </BbkText>
          </View>
          <BbkText className={c2xStyles.titleStyle}>未开启定位</BbkText>
        </View>
      );
  }
};

export const hasLocationPermission =
  // @ts-ignore
  Location.hasLocationPermission;

// Component < PropsType >
class BbkComponentLocation extends PureComponent<PropsType, StateProps> {
  appActiveCallBack = null;

  UNSAFE_componentWillMount() {
    this.appActiveCallBack = DeviceEventEmitter.addListener(
      'AppEnterForeground',
      this.handleAppActive,
    );
  }

  UNSAFE_componentWillUnmount() {
    this.appActiveCallBack?.remove?.();
  }

  componentWillUnmount() {
    this.appActiveCallBack?.remove?.();
  }

  handleAppActive = () => {
    const { setPositionInfo } = this.props;
    checkLocationPermission(setPositionInfo, {
      crnLocateType: CRNLocateType.Force,
      locationBizType: LocationBizType.CityChooseForce,
    });
  };

  render() {
    const {
      onPressItem,
      position,
      setPositionInfo,
      locationName,
      mainStyle,
      areaLocateModalDesc,
    } = this.props;
    const positionStatus =
      position.positionStatus && position.positionStatus.locationStatus;
    const positionData = position.positionLocation;
    const positionName =
      positionData && positionData.area && positionData.area.locationName;
    const locationItem =
      position?.positionInfo?.address?.pois?.length > 0 &&
      position.positionInfo.address.pois[0];
    const positionAddress =
      locationItem?.address || position?.positionInfo?.address?.detailAddress;
    const isShowTickIcon =
      positionData &&
      positionData.area &&
      positionData.area.name === locationName;

    return (
      <>
        {getLocationItem(
          positionStatus,
          positionData,
          positionName,
          positionAddress,
          checkLocationPermission,
          setPositionInfo,
          onPressItem,
          isShowTickIcon,
          mainStyle,
          areaLocateModalDesc,
        )}
      </>
    );
  }
}

export default withTheme(BbkComponentLocation);
