import { CSSProperties } from 'react';

import { IRentalCarsDatePickerProps } from '@ctrip/rn_com_car/dist/src/Components/Basic/CarDatePicker';
import { ItravelItems } from '../../../Pages/Home/Components/ItineraryCard/Type';
import { NationalDayWarningType } from '../../../Types/Dto/QConfigResponseType';

export interface ILocation {
  locationName: string;
  cityId?: number;
  latitude?: number;
  longtitude?: number;
  locationCode?: string;
  locationType?: number;
  id?: string;
  countryId?: number;
  countryName?: string;
  provinceId?: number;
  provinceName?: string;
  cityName?: string;
  timezone?: number;
}

type Plugin = {
  header?: React.ReactElement;
  center?: React.ReactElement;
  footer?: React.ReactElement;
};

export interface IBbkSearchPanel extends IRentalCarsDatePickerProps {
  theme: any;
  style: CSSProperties;
  ptime: string;
  rtime: string;
  minTime: string;
  maxTime: string;
  timeWarning?: string;
  insufficientTimeWarning?: string;
  nationalDayWarning?: string;
  nationalDayWarningConfig?: NationalDayWarningType;
  diffLocationWarning?: string;
  pcity: ILocation;
  rcity: ILocation;
  age: string | number;
  adultSelectNum: number;
  childSelectNum: number;
  showDropoff: boolean;
  showDatePicker: boolean;
  showAgePicker: boolean;
  showNumberPicker: boolean;
  isLocating?: boolean;
  adultMaxNum?: number;
  isShowNumSelect?: boolean;
  isShowAgeSelect?: boolean;
  isShowBorder?: boolean;
  isShowPeopleBorder?: boolean;
  expandPlugin?: Plugin;
  searchPanelButtonType?: string;
  isLazyLoad?: boolean;
  maxmonths: number;
  options?: {
    dropoffChain?: boolean;
    PickupGap?: number;
    Dropoff_Interval?: number;
    Default_Interval_Days?: number;
    TimeGap?: number;
  };
  showDetailDiff?: boolean;
  isShowItineraryCard?: boolean;
  isLoadingItineraryCardInfo?: boolean;
  selectedItineraryCard?: ItravelItems;
  travelItems?: ItravelItems[];
  onPressPickupCity?: () => void; // for ctrip
  onPressDropoffCity?: () => void; // for ctrip
  onPressPickupLocation?: () => void;
  onPressDropoffLocation?: () => void;
  onPressPickUpDate?: () => void;
  onPressDropOffDate?: () => void;
  onAgeChange?: (...age) => void;
  onTimeChange?: (times) => void;
  onPressSearch?: () => void;
  onPressAgeSelect?: () => void;
  onAgeCancel?: () => void;
  onPressAgeTip?: () => void;
  onAgeTipClose?: () => void;
  onTimeCancel?: () => void;
  onIsShowDropOffChange?: () => void;
  onPressNumberSelect?: () => void;
  onNumberConfirm?: (...age) => void;
  onNumberCancel?: () => void;
  setLocationInfo?: (data: any) => void;
  setDateInfo?: (data: any) => void;
  updateSelectedItineraryCard?: (data: any) => void;
  onPressLocation?: () => void;
  searchBtnLottieJson?: Object;
  searchBtnBg?: string;
  isShowLocationBar?: boolean;
  isShowAdvantage?: boolean;
  hasLocationPermission?: boolean;
  filterItems?: Array<any>;
  onPressFilter?: (data, index: number) => void;
  searchButtonText?: string;
  searchBtnTextIcon?: string;
  searchBtnTextIconStyle?: CSSProperties;
  searchBtnStyle?: CSSProperties;
  isShowBusinessLicense?: boolean;
}

export interface RenderFilterItemsP {
  nFilterItems: Array<any>;
  sesameBarTexts?: any;
  onPressFilter: (data, index: number) => void;
  style?: CSSProperties;
}
