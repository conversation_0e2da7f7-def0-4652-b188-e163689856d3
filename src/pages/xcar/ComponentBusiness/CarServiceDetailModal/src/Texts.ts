export const texts = {
  get serviceDetailTitle() {
    return '车行保障服务';
  },
  get serviceDetailTitleEnd() {
    return '详情';
  },
  serviceDetailInclude(msg) {
    return `${msg}包含以下内容`;
  },
  serviceHightLevelDetailInclude(msg) {
    return `除基础服务外，${msg}还可再享以下内容`;
  },
  serviceShelves2HightLevelDetailInclude(msg) {
    return `除基础保障外，${msg}还可再享以下内容`;
  },
  get serviceDetailExclusive() {
    return '发生以下情况时，您需自行承担损失';
  },
  get serviceClaimMoreTitle() {
    return '门店服务保障相关要求及须知';
  },
  get details() {
    return '详情';
  },
  get currentHave() {
    return '当前已含';
  },
  get insTablecurrentHave() {
    return '已含';
  },
  get updateToTheServer() {
    return '升级至此服务';
  },
  get updated() {
    return '已升级';
  },
  get selectTheServer() {
    return '选择此服务';
  },
  get dayUnit() {
    return '/天';
  },
  get selected() {
    return '已选择';
  },
  get currentlyPurchased() {
    return '当前已购买';
  },
  get list_day() {
    return '天';
  },
  CarService_upgradeTip(msg) {
    return `该项服务已免费升级至${msg}`;
  },
  get dailyPriceUnit() {
    return '日均';
  },
};
