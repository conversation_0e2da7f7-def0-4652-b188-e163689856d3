import React, { memo, useMemo } from 'react';
import { XView as View, XLinearGradient, xClassNames } from '@ctrip/xtaro';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { color, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import StyleSheet from '@c2x/apis/StyleSheet';
import BbkHorizontalNav, {
  BbkHorizontalNavItem,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/HorizontalNav';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { GetAB } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import c2xStyles from './carServiceDetailModalC2xStylesShelves2.module.scss';

interface IProps {
  selectedId?: string;
  data?: any;
  onCancel?: () => void;
  changeSelectedId?: (selectedId, index) => void;
}

const { getPixel } = BbkUtils;

const styles = StyleSheet.create({
  navWrap: {
    height: getPixel(88),
    flex: 1,
    borderBottomWidth: 0,
  },
  tab: {
    height: getPixel(88),
    flex: 1,
  },
  textStyle: {
    ...font.subTitle1MediumFlatStyle,
    color: color.fontSecondary,
  },
  textSelectedStyle: {
    ...font.subTitle1MediumFlatStyle,
    color: color.fontPrimary,
  },
});

// eslint-disable-next-line import/prefer-default-export
export const CarServiceDetailModalShelves2Header = memo((props: IProps) => {
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  const { selectedId, data, onCancel, changeSelectedId } = props;
  const isMoreItme = useMemo(() => {
    return data.length >= 2;
  }, [data]);
  return (
    <View
      testID={UITestID.car_testid_comp_booking_ins_detail_tab}
      className={c2xStyles.shadowTop}
    >
      <BbkTouchable
        className={c2xStyles.headerIconContainer}
        onPress={onCancel}
      >
        <BbkText className={c2xStyles.headerIcon} type="icon">
          {icon.cross}
        </BbkText>
      </BbkTouchable>
      <BbkHorizontalNav
        style={styles.navWrap}
        indicatorWidth={getPixel(120)}
        indicatorHeight={getPixel(4)}
        indicatorColor={isISDInterestPoints ? color.deepBlueBase : ''}
        selectedId={selectedId}
        animateIndicator={isMoreItme}
        myIndicator={
          <XLinearGradient
            start={{ x: 0.0, y: 1 }}
            end={{ x: 1.0, y: 1 }}
            locations={[0, 1]}
            colors={['rgb(65, 165, 249)', 'rgb(0, 111, 246)']}
            style={{
              width: getPixel(120),
              height: getPixel(4),
            }}
          />
        }
      >
        {data?.map((item, index) => (
          <BbkHorizontalNavItem
            key={item.uniqueCode}
            id={item.uniqueCode}
            title={item.name}
            style={styles.tab}
            textStyle={styles.textStyle}
            testID={`${UITestID.car_testid_page_booking_carservicedetail_modal_nav_item}_${item.name}`}
            textSelectedStyle={styles.textSelectedStyle}
            onPress={() => changeSelectedId(item.uniqueCode, index)}
            isShowIndicator={isMoreItme}
          />
        ))}
      </BbkHorizontalNav>
    </View>
  );
});
