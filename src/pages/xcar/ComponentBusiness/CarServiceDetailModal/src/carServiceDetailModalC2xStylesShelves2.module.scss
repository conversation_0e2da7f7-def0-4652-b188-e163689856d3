@import '../../../Common/src/Tokens/tokens/color.scss';

.wrap {
  height: 75vh;
  background-color: $white;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}
.shadowTop {
  background-color: $white;
  position: relative;
  z-index: 1;
  overflow: visible;
  padding-top: 8px;
  margin-top: 8px;
  border-radius: 32px 32px 0 0;
  flex-direction: row;
  padding-left: 60px;
  padding-right: 60px;
}
.shadowWrap {
  background-color: $white;
  position: relative;
  z-index: 1;
  overflow: visible;
}
.headerIconContainer {
  display: flex;
  position: absolute;
  padding-top: 30px;
  padding-left: 38px;
  z-index: 10;
}
.headerIcon {
  font-size: 40px;
  color: $C_666;
}
