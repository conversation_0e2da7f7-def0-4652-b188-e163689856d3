@import '../../../Common/src/Tokens/tokens/color.scss';

.contentOld {
  font-size: 32px;
  line-height: 42px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.price {
  border-radius: 8px;
  background-color: $tableBg;
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 20px;
  padding-bottom: 20px;
}
.flexRow {
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
}
.flexColumn {
  flex-direction: column;
  align-items: flex-start;
}
.dayDesc {
  color: $orangePrice;
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.totalPrice {
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueGrayBase;
}
.priceContent {
  margin-left: 8px;
  justify-content: flex-end;
  flex: 1;
}
.dailyPriceRow {
  margin-top: 9px;
}
.dot {
  width: 14px;
  height: 14px;
  border-radius: 7px;
  background-color: $dotBackgroundColor;
  position: relative;
  margin-left: 8px;
}
.sdot {
  width: 8px;
  height: 8px;
  background-color: $priceDescColor;
  border-radius: 4px;
  position: absolute;
  top: 3px;
  left: 3px;
}
.line {
  background-color: $priceDescColor;
  height: 2px;
  width: 75px;
  margin-left: -7px;
}
.triangle {
  width: 0px;
  height: 0px;
  border-top-width: 3px;
  border-top-color: transparent;
  border-right-width: 3px;
  border-right-color: transparent;
  border-bottom-width: 3px;
  border-bottom-color: transparent;
  border-left-width: 6px;
  border-left-color: $priceDescColor;
  padding-top: 1px;
}
.mark {
  color: $priceDescColor;
  font-size: 22px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  padding-left: 8px;
}
.totalPriceRow {
  margin-top: 9px;
}
.priceDescContont {
  margin-top: 40px;
}
.mb8 {
  margin-bottom: 8px;
}
.mr24 {
  margin-right: 24px;
}
.priceDescIcon {
  color: $priceDescIconColor;
  font-size: 30px;
}
