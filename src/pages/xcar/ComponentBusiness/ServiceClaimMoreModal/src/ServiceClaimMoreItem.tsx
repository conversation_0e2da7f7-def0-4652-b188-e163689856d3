import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { VendorServiceDetail } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import c2xStyles from './serviceClaimMoreItemC2xStyles.module.scss';
import { ClaimProcessItem } from '../../InsuranceDetail';
import { Utils } from '../../../Util/Index';

const { getPixel, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  titleStyle: {
    color: color.fontPrimary,
    ...font.title3BoldStyle,
    paddingBottom: getPixel(12),
  },
  noStyle: {
    width: getPixel(36),
    height: getPixel(36),
    borderRadius: getPixel(36),
    backgroundColor: color.refundPenaltyHelp,
  },
  noTextStyle: {
    color: color.white,
    ...font.body3Medium2Style,
  },
  contentStyle: {
    color: color.fontPrimary,
    ...font.title3LightStyle,
    lineHeight: getLineHeight(46),
  },
  rightStyle: {
    paddingBottom: getPixel(40),
  },
});

interface IServiceClaimMoreItem {
  data?: VendorServiceDetail;
  isLast?: boolean;
  onLayout?: (e) => void;
}

const ServiceClaimMoreItem: React.FC<IServiceClaimMoreItem> = ({
  data,
  isLast = false,
  onLayout = Utils.noop,
}) => {
  const { title, type, description, contents } = data;
  return (
    <View className={isLast && c2xStyles.lastWrap} onLayout={onLayout}>
      <View className={c2xStyles.wrap}>
        {!!title && (
          <BbkText className={c2xStyles.title} fontWeight="bold">
            {title}
          </BbkText>
        )}
        {!!description && (
          <BbkText className={c2xStyles.description}>{description}</BbkText>
        )}
        {contents?.map((item, index) => (
          <ClaimProcessItem
            key={`service_${item.title || item.content}`}
            type={type}
            rightStyle={styles.rightStyle}
            isLine={index !== contents.length - 1}
            isDashed={true}
            no={index + 1}
            title={item.title}
            content={item.content}
            titleStyle={styles.titleStyle}
            noStyle={styles.noStyle}
            noTextStyle={styles.noTextStyle}
            contentStyle={styles.contentStyle}
            numberLineBackgroundColor={color.itineraryCardRadioBoxTick}
          />
        ))}
      </View>
      {!isLast && <View className={c2xStyles.splitView} />}
    </View>
  );
};

export default memo(ServiceClaimMoreItem);
