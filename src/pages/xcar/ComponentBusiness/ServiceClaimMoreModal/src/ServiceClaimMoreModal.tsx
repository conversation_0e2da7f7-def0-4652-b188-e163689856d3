import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { PureComponent, CSSProperties } from 'react';
import { XView as View, XBoxShadow } from '@ctrip/xtaro';

import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkHorizontalNav, {
  BbkHorizontalNavItem,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/HorizontalNav';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { VendorServiceDetail } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import c2xStyles from './serviceClaimMoreModalC2xStyles.module.scss';
import c2xCarServiceStyles from './serviceClaimMoreModalC2xCarServiceStyles.module.scss';
import ServiceClaimMoreItem from './ServiceClaimMoreItem';
import { Utils } from '../../../Util/Index';
import { texts } from './Texts';
import BbkHalfPageModal from '../../HalfPageModal';
import { UITestID } from '../../../Constants/Index';

const { getPixel, fixIOSOffsetBottom, adaptNoaNomalousBottom } = BbkUtils;
const CarServiceStyles = StyleSheet.create({
  navWrap: {
    height: getPixel(88),
    flex: 1,
    borderBottomWidth: 0,
  },
  textStyle: {
    ...font.subTitle1MediumFlatStyle,
    color: color.fontSecondary,
  },
  textSelectedStyle: {
    ...font.subTitle1MediumFlatStyle,
    color: color.fontPrimary,
  },
  leftIconStyle: {
    color: color.fontSecondary,
  },
  headerWrap: {
    height: getPixel(96),
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
    position: 'relative',
    zIndex: 2,
    borderBottomWidth: 2,
    borderBottomColor: color.white,
    overflow: 'visible',
  },
  modalTitle: {
    ...font.title4BoldStyle,
    color: color.fontPrimary,
    textAlign: 'center',
    marginTop: getPixel(4),
    backgroundColor: color.transparent,
  },
  tab: {
    height: getPixel(88),
    alignItems: 'center',
    flex: 1,
  },
  modalWrap: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 0,
    paddingBottom: 0,
  },
});

const styles = StyleSheet.create({
  footerButton: {
    marginLeft: getPixel(32),
    marginRight: getPixel(32),
    marginTop: getPixel(16),
    marginBottom:
      fixIOSOffsetBottom() + getPixel(16) + adaptNoaNomalousBottom(),
    backgroundColor: color.deepBlueBase,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: getPixel(12),
  },
  scrollWrap: {
    paddingBottom: getPixel(12),
  },
});

interface IProps {
  visible: boolean;
  onCancel?: any;
  data?: Array<VendorServiceDetail>;
  modalBgStyle?: CSSProperties;
}

interface IState {
  selectedId: string;
  scrollHeight: Array<number>;
}

export default class ServiceClaimMoreModal extends PureComponent<
  IProps,
  IState
> {
  srollViewRef;

  isUserScroll;

  constructor(props) {
    super(props);
    this.state = {
      selectedId: '',
      scrollHeight: [],
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.visible !== this.props.visible) {
      this.setState({
        selectedId: nextProps.data?.[0]?.title,
      });
    }
  }

  changeSelectedId = (selectedId, index) => {
    this.isUserScroll = false;
    const { scrollHeight } = this.state;
    this.setState({
      selectedId,
    });
    if (scrollHeight.length > 0) {
      this.srollViewRef?.scrollTo({ y: Math.max(scrollHeight[index - 1], 0) });
    }
  };

  onScroll = e => {
    if (this.isUserScroll) {
      const { scrollHeight, selectedId } = this.state;
      const { data } = this.props;
      const { nativeEvent } = e;
      const { y } = nativeEvent.contentOffset;
      let navIndex = 0;
      let scrollY = 0;
      scrollHeight.map((item, index) => {
        scrollY += item;
        if (y > scrollY) {
          navIndex = index + 1;
        }
      });
      const currentSelectedId = data[navIndex].title;
      if (currentSelectedId !== selectedId) {
        this.setState({ selectedId: currentSelectedId });
      }
    }
  };

  onScrollBeginDrag = () => {
    this.isUserScroll = true;
  };

  onLayoutItem = (e, index) => {
    const { scrollHeight } = this.state;
    const { height } = e.nativeEvent.layout;
    scrollHeight[index] = height;
    this.setState({
      scrollHeight,
    });
  };

  render() {
    const { visible, onCancel = Utils.noop, data, modalBgStyle } = this.props;
    const { selectedId } = this.state;
    if (!data?.length) return null;
    const modalHeaderProps = {
      hasTopBorderRadius: true,
      showRightIcon: false,
      showLeftIcon: true,
      leftIconStyle: CarServiceStyles.leftIconStyle,
      onClose: onCancel,
      style: CarServiceStyles.headerWrap,
      titleStyle: CarServiceStyles.modalTitle,
      title: texts.serviceClaimMoreTitle,
    };
    return (
      <BbkHalfPageModal
        pageModalProps={{
          visible,
          onMaskPress: onCancel,
          // @ts-ignore
          modalTouchLayerStyle: modalBgStyle,
        }}
        closeModalBtnTestID={
          UITestID.car_testid_page_booking_serviceclaimmore_modal_closemask
        }
        modalHeaderProps={modalHeaderProps}
        contentStyle={CarServiceStyles.modalWrap}
      >
        <View
          testID={UITestID.car_testid_page_order_detail_car_service_claim_more}
          className={c2xCarServiceStyles.wrap}
          style={{ height: BbkUtils.vh(75) }}
        >
          <XBoxShadow
            className={c2xCarServiceStyles.shadowTop}
            coordinate={{ x: 0, y: getPixel(4) }}
            color="rgba(0, 0, 0, 0.08)"
            opacity={1}
            blurRadius={getPixel(8)}
            elevation={4}
          >
            <BbkHorizontalNav
              style={CarServiceStyles.navWrap}
              indicatorWidth={getPixel(240)}
              indicatorHeight={getPixel(4)}
              selectedId={selectedId}
            >
              {data?.map((item, index) => (
                <BbkHorizontalNavItem
                  key={item.title}
                  id={item.title}
                  title={item.title}
                  style={CarServiceStyles.tab}
                  textStyle={CarServiceStyles.textStyle}
                  textSelectedStyle={CarServiceStyles.textSelectedStyle}
                  onPress={() => this.changeSelectedId(item.title, index)}
                  testID={`${UITestID.car_testid_page_booking_serviceclaimmore_modal_nav_item}_${item.title}`}
                />
              ))}
            </BbkHorizontalNav>
          </XBoxShadow>
          <ScrollView
            ref={ref => (this.srollViewRef = ref)}
            onScroll={this.onScroll}
            onScrollBeginDrag={this.onScrollBeginDrag}
            scrollEventThrottle={16}
            style={styles.scrollWrap}
          >
            {data.map((item, index) => (
              <ServiceClaimMoreItem
                onLayout={e => {
                  this.onLayoutItem(e, index);
                }}
                key={item.title}
                data={item}
                isLast={index === data.length - 1}
              />
            ))}
          </ScrollView>
          <View>
            <XBoxShadow
              className={c2xStyles.footerButtonWrap}
              coordinate={{ x: 0, y: getPixel(-2) }}
              color="rgba(0, 0, 0, 0.08)"
              opacity={1}
              blurRadius={getPixel(8)}
              elevation={8}
            >
              <BbkTouchable
                testID={
                  UITestID.car_testid_page_booking_serviceclaimmore_modal_gotit
                }
                style={styles.footerButton}
                onPress={onCancel}
              >
                <BbkText className={c2xStyles.footerText} fontWeight="medium">
                  {texts.gotIt}
                </BbkText>
              </BbkTouchable>
            </XBoxShadow>
          </View>
        </View>
      </BbkHalfPageModal>
    );
  }
}
