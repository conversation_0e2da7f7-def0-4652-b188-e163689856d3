import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useMemo, CSSProperties } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XBoxShadow,
  XViewExposure,
} from '@ctrip/xtaro';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  RentalGuaranteeV2Type,
  PackageDetailListType,
  PurchasingNoticeType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import { color, icon, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './carServiceC2xStyles.module.scss';
import InsuranceBackground, { BackgroudType } from '../../ImageBackground';
import { InsuranceProductTip } from '../../Tips';
import { RentalGuaranteeItem } from '../../ValueAddedService';
import CarServiceTable from './CarServiceTable';
import { texts } from './Texts';
import { CarServiceFromPageTypes } from './Types';
import { CarLog, Utils } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, useMemoizedFn, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  rentalGuaranteeWrap: { marginLeft: getPixel(30), marginRight: getPixel(30) },
  noBg: {
    backgroundColor: color.transparent,
  },
  pt0: { paddingTop: 0 },
  bookingOptimizationWrap: {
    marginTop: getPixel(32),
  },
  bookingOptimizationInsuranceProductTipWrap: {
    paddingBottom: getPixel(32),
  },
  negaMt6: {
    marginTop: -getPixel(6),
  },
});

interface PressCarServiceDetailParams {
  data: PackageDetailListType[];
  purchasingNotice: PurchasingNoticeType;
}
export interface ICarService {
  isPriceLoading?: boolean;
  rentalGuaranteeV2: RentalGuaranteeV2Type;
  curInsPackageId?: number;
  selectedInsuranceId?: Array<string>;
  onPressButton?: (data) => void;
  onPressServiceClaimMore?: () => void;
  onPressCarServiceDetail?: (
    data?: PressCarServiceDetailParams | string,
  ) => void;
  style?: CSSProperties;
  insuranceProductTips?: string;
  haveInsBg?: boolean;
  isHideDetailBtn?: boolean;
  fromPage?: string;
  ptime?: string;
  rtime?: string;
  width?: number;
  isEhai?: boolean;
  isForceSingleServce?: boolean; // 是否强制显示一个服务
  isEasyLife2024?: boolean;
  isShowTableArrow?: boolean;
  isNewIsdBooking?: boolean;
}

const BbkComponentCarService: React.FC<ICarService> = ({
  rentalGuaranteeV2,
  onPressButton,
  onPressServiceClaimMore,
  onPressCarServiceDetail,
  selectedInsuranceId,
  style,
  insuranceProductTips,
  haveInsBg = true,
  isHideDetailBtn,
  fromPage,
  ptime,
  rtime,
  width,
  isEhai,
  isForceSingleServce,
  isEasyLife2024,
  isShowTableArrow,
  isNewIsdBooking,
}: ICarService) => {
  if (!rentalGuaranteeV2) return null;
  const { packageDetailList, purchasingNotice, purchased } = rentalGuaranteeV2;
  if (!packageDetailList?.length) return null;
  const isSingleServce = isForceSingleServce || packageDetailList?.length === 1;
  const isMoreService = !isSingleServce;
  let rentalGuaranteeData = isSingleServce && packageDetailList[0];
  // 2022-11-11 对于订详一级页面取purchased字段，弹层取purchasedSub
  if (isForceSingleServce) {
    rentalGuaranteeData = purchased;
  }
  const {
    name,
    type,
    uniqueCode: rentalUniqueCode,
    vendorServiceCode,
    currentDailyPrice,
    localTotalPrice,
    currentCurrencyCode,
    description,
    allTags,
  } = rentalGuaranteeData || {};

  const isSelected = selectedInsuranceId?.indexOf(rentalUniqueCode) > -1;
  const Wrapper = haveInsBg ? InsuranceBackground : View;
  const isFromOrderDetail = fromPage === CarServiceFromPageTypes.orderDetail;
  const showCarServiceDetailModal = useMemoizedFn(code => {
    if (fromPage === CarServiceFromPageTypes.orderDetail) {
      CarLog.LogCode({
        name: '点击_订单详情页_车行险详情',

        info: { insuranceCode: rentalUniqueCode },
      });
      onPressCarServiceDetail({
        data: packageDetailList,
        purchasingNotice,
      });
    } else {
      onPressCarServiceDetail(rentalUniqueCode);
    }
  });

  const { insuranceTipsOffset } = useMemo(() => {
    let len = 0;
    if (packageDetailList?.length) {
      packageDetailList?.forEach(item => {
        const clen = item?.cityEncourage?.length;
        len = clen > len ? clen : len;
      });
    }
    return {
      insuranceTipsOffset: len > 7 ? 0 : len > 0 ? 27 : 56,
    };
  }, [packageDetailList]);

  const showMoreServiceDetailModal = useMemoizedFn(() => {
    // 点服务详情按钮
    if (fromPage === CarServiceFromPageTypes.booking) {
      CarLog.LogCode({ name: '点击_填写页_服务详情' });
    }
    if (fromPage === CarServiceFromPageTypes.orderDetail) {
      onPressCarServiceDetail({
        data: packageDetailList,
        purchasingNotice,
      });
    } else {
      onPressCarServiceDetail();
    }
  });

  const showServiceClaimMoreModal = useMemoizedFn(() => {
    onPressServiceClaimMore();
    const enames = {
      [CarServiceFromPageTypes.booking]: '点击_填写页_查看理赔须知',

      [CarServiceFromPageTypes.orderDetail]: '点击_订单详情页_查看理赔须知',
    };
    const enName = enames[fromPage];
    if (enName) {
      CarLog.LogCode({
        name: enName,
      });
    }
  });
  const SingleServceWrapper = isFromOrderDetail ? BbkTouchable : View;
  const handlePressSingleServceWrapper = useMemoizedFn(() => {
    showCarServiceDetailModal(rentalUniqueCode);
  });
  return (
    <Wrapper
      type={
        isFromOrderDetail
          ? BackgroudType.SaleAfterService
          : BackgroudType.SaleBeforeService
      }
      style={style}
    >
      <View
        testID={UITestID.car_testid_comp_booking_ins_title}
        className={c2xStyles.wrap}
        style={Utils.isCtripIsd() && styles.bookingOptimizationWrap}
      >
        <View className={c2xStyles.titleWrap}>
          <BbkText
            className={classNames(
              c2xCommonStyles.c2xTextDefaultCss,
              Utils.isCtripIsd()
                ? c2xStyles.bookingOptimizationTitleText
                : c2xStyles.titleText,
              isNewIsdBooking && c2xStyles.titleTextNewIsdBking,
            )}
          >
            {texts.carServiceTitle}
          </BbkText>
          {!isHideDetailBtn &&
            (isNewIsdBooking ? (
              <BbkTouchable
                onPress={showMoreServiceDetailModal}
                testID="booking_service_detail_button"
                className={classNames(c2xStyles.mrf6, c2xStyles.detailWrap)}
              >
                <BbkText className={c2xStyles.detailTextNew}>
                  {texts.carServiceDetail}
                </BbkText>
                <BbkText type="icon" className={c2xStyles.detailIconNew}>
                  {icon.arrowRight}
                </BbkText>
              </BbkTouchable>
            ) : (
              <BbkTouchable
                onPress={showMoreServiceDetailModal}
                testID="booking_service_detail_button"
                className={c2xStyles.mrf6}
              >
                <XBoxShadow
                  coordinate={{ x: 0, y: getPixel(4) }}
                  color={
                    isFromOrderDetail && !isEasyLife2024
                      ? setOpacity(color.black, 0)
                      : setOpacity(color.black, 0.06)
                  }
                  opacity={1}
                  blurRadius={getPixel(16)}
                  elevation={isFromOrderDetail && !isEasyLife2024 ? 0 : 4}
                  className={classNames(
                    c2xStyles.detailWrap,
                    isFromOrderDetail && !isEasyLife2024 && c2xStyles.noBg,
                  )}
                >
                  <BbkText
                    className={c2xStyles.detailText}
                    style={isFromOrderDetail && !isEasyLife2024 && styles.noBg}
                  >
                    {texts.carServiceDetail}
                  </BbkText>
                  <BbkText type="icon" className={c2xStyles.detailIcon}>
                    {icon.arrowRight}
                  </BbkText>
                </XBoxShadow>
              </BbkTouchable>
            ))}
          {!!rentalGuaranteeV2?.insuranceMaxAddDesc && (
            <XBoxShadow
              coordinate={{ x: 0, y: getPixel(4) }}
              color={
                Utils.isCtripIsd()
                  ? setOpacity(color.black, 0)
                  : setOpacity(color.black, 0.06)
              }
              opacity={1}
              blurRadius={getPixel(16)}
              elevation={Utils.isCtripIsd() ? 0 : 4}
              className={
                Utils.isCtripIsd()
                  ? c2xStyles.bookingOptimizationTipWrap
                  : c2xStyles.tipWrap
              }
            >
              <BbkText
                className={classNames(
                  c2xCommonStyles.c2xTextDefaultCss,

                  Utils.isCtripIsd()
                    ? c2xStyles.bookingOptimizationTipText
                    : c2xStyles.tipText,
                )}
              >
                {rentalGuaranteeV2.insuranceMaxAddDesc}
              </BbkText>
            </XBoxShadow>
          )}
        </View>
      </View>
      {isSingleServce && (
        <SingleServceWrapper
          onPress={handlePressSingleServceWrapper}
          testID={UITestID.car_testid_page_booking_carservice_single}
          style={xMergeStyles([
            styles.rentalGuaranteeWrap,
            isNewIsdBooking && styles.negaMt6,
          ])}
        >
          <RentalGuaranteeItem
            name={name}
            type={type}
            onPressButton={onPressButton}
            onPressTitle={showCarServiceDetailModal}
            uniqueCode={rentalUniqueCode}
            vendorServiceCode={vendorServiceCode}
            localDailyPrice={currentDailyPrice}
            localTotalPrice={localTotalPrice}
            localCurrencyCode={currentCurrencyCode}
            showTopBorder={false}
            descriptionNew={description}
            allTags={allTags}
            isShowLabel={false}
            selected={isSelected}
            index={0}
            isHideCheckBox={isEasyLife2024 || isFromOrderDetail}
            isHideTitle={isEasyLife2024}
            isNewIsdBooking={isNewIsdBooking}
          />
        </SingleServceWrapper>
      )}
      {isMoreService && (
        <CarServiceTable
          data={rentalGuaranteeV2}
          onPressButton={onPressButton}
          onPressCarServiceDetail={onPressCarServiceDetail}
          selectedInsuranceId={selectedInsuranceId}
          fromPage={fromPage}
          ptime={ptime}
          rtime={rtime}
          width={width}
          isEhai={isEhai}
          isShowTableArrow={isShowTableArrow}
          isNewIsdBooking={isNewIsdBooking}
        />
      )}
      {!!insuranceProductTips &&
        (isNewIsdBooking ? (
          <XViewExposure className={c2xStyles.insuranceTipWrap}>
            <BbkTouchable
              className={c2xStyles.insuranceProductTip}
              onPress={showServiceClaimMoreModal}
              testID={UITestID.car_testid_page_order_detail_car_service_tips}
            >
              <BbkText className={c2xStyles.contentText}>
                <BbkText className={c2xStyles.moreText}>
                  门店服务保障相关要求及须知
                  <View>
                    <View
                      className={c2xStyles.moreIconWrap}
                      style={{ top: isAndroid ? getPixel(6) : getPixel(2) }}
                    >
                      <BbkText className={c2xStyles.moreIcon} type="icon">
                        {icon.arrowRight}
                      </BbkText>
                    </View>
                  </View>
                </BbkText>
              </BbkText>
            </BbkTouchable>
          </XViewExposure>
        ) : (
          <XViewExposure
            testID={CarLog.LogExposure({
              name: '曝光_填写页_车行服务_底部理赔说明模块',
            })}
          >
            <InsuranceProductTip
              isShowBorder={isSingleServce && Utils.isCtripIsd()}
              onPressMore={showServiceClaimMoreModal}
              insuranceTips={insuranceProductTips}
              style={xMergeStyles([
                isMoreService && styles.pt0,
                !isFromOrderDetail &&
                  insuranceTipsOffset &&
                  isMoreService && {
                    marginTop: -getPixel(insuranceTipsOffset),
                  },
                Utils.isCtripIsd() &&
                  styles.bookingOptimizationInsuranceProductTipWrap,
              ])}
            />
          </XViewExposure>
        ))}
    </Wrapper>
  );
};

export default memo(BbkComponentCarService);
