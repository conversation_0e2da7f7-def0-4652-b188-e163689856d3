@import '../../../Common/src/Tokens/tokens/color.scss';

.addImg {
  width: 14px;
  height: 17px;
  margin-right: 10px;
}
.newTipWrap {
  position: absolute;
  top: -19px;
  z-index: 999;
}
.newTriangle {
  width: 24px;
  height: 24px;
  transform: rotate(45deg);
  bottom: -10px;
  position: absolute;
  background-color: $C_318BF7;
}
.tipDescWrap {
  background-color: $C_318BF7;
  height: 44px;
  border-radius: 8px;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 14px;
  padding-right: 14px;
}
.tipText {
  font-size: 22px;
  line-height: 28px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $white;
  text-align: center;
}
.unSelectedWrap {
  margin-top: 20px;
  margin-bottom: 114px;
}
.eHaihasMoreBorder {
  border-right-width: 2px;
  border-left-width: 2px;
  border-style: solid;
  position: relative;
  z-index: 1;
  border-left-color: $white;
  border-right-color: $white;
}
.defaultBorder {
  border-width: 1.5px;
  border-style: solid;
  position: relative;
  z-index: 1;
  border-top-color: $dayText;
  border-bottom-color: $dayText;
  border-left-color: $white;
  border-right-color: $white;
}
.serviceHead {
  height: 192px;
  align-items: center;
}
.serviceIcon {
  width: 120px;
  height: 70px;
  margin-top: 24px;
}

.serviceIconNew {
  width: 56px;
  height: 56px;
  margin-top: 24px;
  margin-bottom: 16px
}
.serviceName {
  font-size: 28px;
  line-height: 36px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.detailWrap {
  flex-direction: row;
  align-items: center;
}
.detailTextNew {
  font-size: 22px;
  line-height: 42px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $deepBlueBase;
}
.detailText {
  font-size: 22px;
  line-height: 42px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueBase;
}
.detailIconNew {
  color: $deepBlueBase;
  font-size: 22px;
  line-height: 42px;
  margin-right: -8px;
}
.detailIcon {
  color: $blueBase;
  font-size: 22px;
  line-height: 42px;
  margin-right: -8px;
}
.serviceFoot {
  height: 150px;
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.footDescText {
  font-size: 22px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
  width: 120px;
}
.CNYImg {
  width: 14px;
  height: 17px;
  margin-right: 3px;
}
.selectedIndePriDigPrice {
  color: $C_111111;
  font-size: 26px;
  line-height: 36px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
}
.selectedPriceUnitDay {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_111111;
  margin-left: 2px;
}
.indePriDigPriceNew {
  color: $deepBlueBase;
  font-size: 26px;
  line-height: 36px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
}
.indePriDigPrice {
  color: $orangePrice;
  font-size: 26px;
  line-height: 36px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
}
.priceUnitDayNew {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $deepBlueBase;
  margin-left: 2px;
}
.priceUnitDay {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $orangePrice;
  margin-left: 2px;
}
.currentHave {
  color: $blueBase;
  opacity: 0.5;
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  padding-top: 9px;
  padding-bottom: 9px;
  margin-top: 3px;
}
.mt5 {
  margin-top: 5px;
}
.selectBtnShadow {
  width: 144px;
  height: 60px;
  position: absolute;
  top: 4px;
  left: -8px;
}
.selectTxtNew {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $deepBlueBase;
}
.selectTxt {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueBase;
}
.checkImg {
  width: 44px;
  height: 44px;
  position: absolute;
  bottom: -1px;
  right: -1px;
}
.tipWrap {
  width: 186px;
  position: absolute;
  bottom: -88px;
}
.triangle {
  width: 24px;
  height: 24px;
  transform: rotate(45deg);
  top: 6px;
  left: 85px;
  position: absolute;
  background-color: $greenBase;
}
.tipTextWrap {
  margin-top: 12px;
  align-content: center;
  align-items: center;
}
