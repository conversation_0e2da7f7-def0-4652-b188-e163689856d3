import { findLast as lodashFindLast } from 'lodash-es';
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import ScrollView from '@c2x/components/ScrollView';
import React, { memo, useEffect, useRef, useState } from 'react';
import {
  XView as View,
  xCreateAnimation,
  xMergeStyles,
  XAnimated,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { RentalGuaranteeV2Type } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import BbkDashedLine from '@ctrip/rn_com_car/dist/src/Components/Basic/Dashedline';
import c2xStyles from './carServiceTableC2xStyles.module.scss';
import BbkComponentCarServiceTableItem from './CarServiceTableItem';
import BbkComponentCarServiceTableItemOsd from './CarServiceTableItemOsd';
import CarServiceTableCell from './CarServiceTableCell';
import { ProductType } from '../../ValueAddedService';
import { texts } from './Texts';
import { UITestID, ImageUrl } from '../../../Constants/Index';
import { Utils } from '../../../Util/Index';
import { XAnimateExport } from '../../../Types/Animate';

const { getPixel, useMemoizedFn, autoProtocol } = BbkUtils;
const styles = StyleSheet.create({
  headWrapOsd: {
    width: getPixel(162),
    marginTop: getPixel(0),
  },
  iconWrap: {
    position: 'absolute',
    right: getPixel(46),
    top: getPixel(372),
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 3,
  },
  serviceInfo: {
    paddingLeft: getPixel(28),
  },
  scrollTable: {
    position: 'absolute',
    left: getPixel(166),
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
    top: getPixel(4),
    zIndex: 2,
    right: 0,
  },
  scrollTableEhaiMore: {
    width: getPixel(504),
    position: 'absolute',
    left: getPixel(166),
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
    top: getPixel(4),
    zIndex: 2,
    right: 0,
  },
  bookingOptimizationHead: {
    width: getPixel(144),
  },
  bookingOptimizationServiceHead: {
    width: getPixel(144),
    paddingLeft: getPixel(28),
  },
  bookingOptimizationServiceFoot: {
    width: getPixel(144),
    paddingLeft: getPixel(28),
  },
  bookingOptimizationScrollTable: {
    left: getPixel(160),
  },
  platformInsurance2ScrollWrap: {
    paddingRight: getPixel(32),
  },
  platformInsurance2TitleWrap: {
    width: getPixel(162),
    paddingLeft: getPixel(22),
  },
  platformInsurance2ServiceHead: {
    height: getPixel(110),
  },
  platformInsurance2ScrollTable: {
    left: getPixel(178),
  },
  platformInsurance2ServiceInfo: {
    paddingLeft: getPixel(22),
    paddingRight: getPixel(22),
    height: getPixel(90),
    marginTop: 0,
    flexDirection: 'column',
    justifyContent: 'center',
  },
});

export interface ICarServiceTable {
  data?: RentalGuaranteeV2Type;
  onPressButton?: (data) => void;
  onPressCarServiceDetail?: (uniqueCode) => void;
  selectedIndex?: number;
  selectedInsuranceId?: any[];
  fromPage?: string;
  ptime?: string;
  rtime?: string;
  width?: number;
  isEhai?: boolean;
  isPlatformInsurance2?: boolean;
  setModalSelectedInsuranceId?: (data: string[]) => void;
  curInsPackageId?: number;
  currentDefaultPackageId?: number;
  isPriceLoading?: boolean;
  isShowTableArrow?: boolean;
  isNewIsdBooking?: boolean;
}

const BbkComponentCarServiceTable: React.FC<ICarServiceTable> = ({
  data,
  onPressButton,
  onPressCarServiceDetail,
  selectedInsuranceId,
  fromPage,
  ptime,
  rtime,
  width,
  isEhai,
  isPlatformInsurance2,
  setModalSelectedInsuranceId,
  curInsPackageId,
  currentDefaultPackageId,
  isPriceLoading,
  isShowTableArrow,
  isNewIsdBooking,
}: ICarServiceTable) => {
  const scrollRef = useRef(null);
  const scrollX = useRef(0);
  const [headMarginBottom, setHeadMarginBottom] = useState(getPixel(84));
  const { rentalGuaranteeTitle, packageDetailList } = data || {};
  const pageSize = Math.min(3, packageDetailList?.length);
  let selectedIndex = -1;
  packageDetailList?.map((item, index) => {
    if (selectedInsuranceId?.indexOf(item.uniqueCode) > -1) {
      selectedIndex = index;
    }
  });
  const hasMore = packageDetailList?.length > 3;
  const [isShowArrowRight, setShowArrowRight] = useState(isEhai && hasMore);
  let serviceListWidth =
    width - getPixel(Utils.isCtripIsd() ? 124 : 150) - getPixel(68);
  if (isPlatformInsurance2) {
    serviceListWidth = width - getPixel(162 + 64);
  }
  const serviceItemWidth =
    isEhai && hasMore ? serviceListWidth / 2.7 : serviceListWidth / pageSize;
  const presentInsurance = lodashFindLast(
    packageDetailList,
    it => it.type === ProductType.present,
  );

  const [animatePlay, setAnimatePlay] = useState(false);
  const [animationStyle, setAnimationStyle] = useState<XAnimateExport>();

  useEffect(() => {
    if (!animatePlay && isShowTableArrow && !isPriceLoading) {
      const animation = xCreateAnimation({
        duration: 200,
        timingFunction: 'ease',
      });

      const animationSequence: XAnimateExport[] = [];

      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < 5; i++) {
        animationSequence.push(
          animation.translate(-20, 0).step().translate(0, 0).step().export(),
        );
      }

      let current = 0;

      const interval = setInterval(() => {
        setAnimationStyle(animationSequence[current]);
        current += 1;
        if (current >= animationSequence.length) {
          clearInterval(interval);
        }
      }, 400); // 每步间隔 400ms (200ms + 200ms)

      setAnimatePlay(true);
    }
  }, [isPriceLoading, isShowTableArrow, animatePlay]);

  const onPressHandle = useMemoizedFn(() => {
    scrollRef.current.scrollToEnd();
    setShowArrowRight(false);
  });

  useEffect(() => {
    if (hasMore) {
      if (selectedIndex > 2) {
        setTimeout(() => {
          scrollRef.current.scrollTo({
            x: -(selectedIndex - 3 * serviceItemWidth),
          });
        }, 0);
      } else if (selectedIndex < 2 && scrollX.current) {
        setTimeout(() => {
          scrollRef.current.scrollTo({ x: -scrollX.current });
        }, 0);
      }
    }
  }, [selectedInsuranceId]);
  const onMomentumScrollEnd = ({ nativeEvent }) => {
    scrollX.current = nativeEvent.contentOffset.x;
  };

  const hideArrowRight = useMemoizedFn(e => {
    scrollX.current = e.nativeEvent.contentOffset.x;
    if (scrollX.current > serviceItemWidth + getPixel(20)) {
      setShowArrowRight(false);
    }
  });

  const CarServiceTableItem = isPlatformInsurance2
    ? BbkComponentCarServiceTableItemOsd
    : BbkComponentCarServiceTableItem;
  return (
    <View testID={UITestID.car_testid_comp_booking_ins_content}>
      <View className={c2xStyles.contain}>
        {rentalGuaranteeTitle && (
          <View
            className={c2xStyles.head}
            style={xMergeStyles([
              Utils.isCtripIsd() && styles.bookingOptimizationHead,
              isPlatformInsurance2 && styles.headWrapOsd,
              { marginBottom: headMarginBottom },
            ])}
          >
            <View
              className={c2xStyles.serviceHead}
              style={xMergeStyles([
                Utils.isCtripIsd() && styles.bookingOptimizationServiceHead,
                isPlatformInsurance2 &&
                  xMergeStyles([
                    styles.platformInsurance2TitleWrap,
                    styles.platformInsurance2ServiceHead,
                  ]),
              ])}
            >
              <BbkText className={c2xStyles.serviceHeadText}>
                {isPlatformInsurance2
                  ? texts.carService
                  : texts.carServiceContent}
              </BbkText>
            </View>
            <View>
              <BbkDashedLine
                lineWidth={
                  isPlatformInsurance2 ? StyleSheet.hairlineWidth : getPixel(1)
                }
                itemWidth={3}
                lineColor={color.dayText}
              />
            </View>
            {rentalGuaranteeTitle?.map((item, index) => {
              return (
                <View
                  testID={UITestID.car_testid_comp_booking_ins_content_headitem}
                  key={`cell_${item}`}
                >
                  <CarServiceTableCell
                    isCenter={false}
                    style={
                      isPlatformInsurance2
                        ? styles.platformInsurance2ServiceInfo
                        : styles.serviceInfo
                    }
                    isFirst={index === 0}
                    isLast={index === rentalGuaranteeTitle.length - 1}
                    description={item}
                    isEhai={isEhai}
                    isPlatformInsurance2={isPlatformInsurance2}
                  />

                  {isPlatformInsurance2 &&
                    index !== rentalGuaranteeTitle.length - 1 && (
                      <View className={c2xStyles.dashedLineWrap}>
                        <BbkDashedLine
                          lineWidth={
                            isPlatformInsurance2
                              ? StyleSheet.hairlineWidth
                              : getPixel(1)
                          }
                          itemWidth={3}
                          lineColor={color.dayText}
                        />
                      </View>
                    )}
                </View>
              );
            })}
            <View>
              <BbkDashedLine
                lineWidth={
                  isPlatformInsurance2 ? StyleSheet.hairlineWidth : getPixel(1)
                }
                itemWidth={3}
                lineColor={color.dayText}
              />
            </View>
            <View
              className={c2xStyles.serviceFoot}
              style={xMergeStyles([
                Utils.isCtripIsd() && styles.bookingOptimizationServiceFoot,
                isPlatformInsurance2 && styles.platformInsurance2TitleWrap,
              ])}
            >
              <BbkText className={c2xStyles.serviceFootText} fontWeight="bold">
                {texts.carServiceChoose}
              </BbkText>
            </View>
          </View>
        )}
        {isEhai && hasMore && (
          <View
            className={c2xStyles.lineContainer}
            style={{ width: width - getPixel(68) }}
          />
        )}
        <ScrollView
          onScroll={hideArrowRight}
          horizontal={true}
          bounces={false}
          showsHorizontalScrollIndicator={false}
          scrollEnabled={hasMore}
          style={xMergeStyles([
            isEhai && hasMore ? styles.scrollTableEhaiMore : styles.scrollTable,
            isPlatformInsurance2
              ? styles.platformInsurance2ScrollTable
              : styles.bookingOptimizationScrollTable,
          ])}
          ref={scrollRef}
          onMomentumScrollEnd={onMomentumScrollEnd}
          onScrollEndDrag={hideArrowRight}
        >
          <View
            className={c2xStyles.scrollWrap}
            style={xMergeStyles([
              isPlatformInsurance2 && styles.platformInsurance2ScrollWrap,
              isEhai && hasMore && { paddingRight: getPixel(17) },
            ])}
          >
            {packageDetailList?.map((item, index) => (
              <CarServiceTableItem
                key={item.uniqueCode}
                isFirst={index === 0}
                isLast={index === packageDetailList.length - 1}
                isShowDefaultEncourage={selectedIndex < 1}
                selected={selectedInsuranceId?.indexOf(item.uniqueCode) > -1}
                onPressButton={onPressButton}
                onPressCarServiceDetail={onPressCarServiceDetail}
                index={index}
                width={serviceItemWidth}
                data={item}
                presentInsurance={presentInsurance}
                selectedInsuranceId={selectedInsuranceId}
                fromPage={fromPage}
                ptime={ptime}
                rtime={rtime}
                isEhai={isEhai}
                setHeadMarginBottom={setHeadMarginBottom}
                setModalSelectedInsuranceId={setModalSelectedInsuranceId}
                curInsPackageId={curInsPackageId}
                currentDefaultPackageId={currentDefaultPackageId}
                hasMore={hasMore}
                isNewIsdBooking={isNewIsdBooking}
              />
            ))}
          </View>
        </ScrollView>
        {isShowArrowRight && (
          <XAnimated.View style={styles.iconWrap} animation={animationStyle}>
            <Image
              src={autoProtocol(
                `${ImageUrl.DIMG04_PATH}1tg3s12000g2ngsca8C8A.png`,
              )}
              mode="aspectFill"
              className={c2xStyles.backgroundImage}
            />

            <View className={c2xStyles.iconCircle}>
              <BbkText
                type="icon"
                onPress={onPressHandle}
                className={c2xStyles.arrowIcon}
              >
                {icon.right_arrow}
              </BbkText>
            </View>
          </XAnimated.View>
        )}
      </View>
    </View>
  );
};
export default memo(BbkComponentCarServiceTable);
