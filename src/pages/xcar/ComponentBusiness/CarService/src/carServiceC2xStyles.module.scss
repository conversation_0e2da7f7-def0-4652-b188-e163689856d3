@import '../../../Common/src/Tokens/tokens/color.scss';

.wrap {
  margin-top: 38px;
  margin-left: 30px;
  margin-right: 30px;
}
.titleWrap {
  margin-bottom: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.bookingOptimizationTitleText {
  font-size: 34px;
  line-height: 42px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.titleText {
  font-size: 36px;
  line-height: 44px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $blackBase;
}
.mrf6 {
  margin-right: -6px;
}
.detailWrap {
  flex-direction: row;
  background-color: $white;
  height: 42px;
  width: 139px;
  border-radius: 21px;
  justify-content: center;
}
.noBg {
  background-color: $transparent;
}
.detailText {
  font-size: 22px;
  line-height: 42px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueBase;
}

.titleTextNewIsdBking {
  font-size: 34px;
  line-height: 43px;
  color: $C_111111;
}

.detailTextNew {
  font-size:26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_111111;
}
.detailIconNew {
  color: $C_111111;
  font-size: 26px;
  line-height: 36px;
  margin-right: -8px;
}

.detailIcon {
  color: $blueBase;
  font-size: 22px;
  line-height: 42px;
  margin-right: -8px;
}
.bookingOptimizationTipWrap {
  margin-bottom: 2px;
}
.tipWrap {
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 8px;
  padding-bottom: 8px;
  background-color: $white;
  align-items: center;
  border-radius: 21px;
  margin-right: 40px;
}
.bookingOptimizationTipText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.tipText {
  color: $fontPrimary;
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}

.insuranceProductTip {
  padding-bottom: 32px;
  margin-left: 33px;
  margin-right: 33px;
}
.noSpace {
  margin-left: 0;
  margin-right: 0;
}
.contentText {
  color: $C_888888;
  font-size: 24px;
}
.moreText {
  line-height: 36px;
  color: $C_888888;
  font-size: 26px;
}
.moreIcon {
  color: $C_888888;
  font-size: 26px;
}
.moreIconWrap {
  position: relative;
}

.insuranceTipWrap {
  margin-top: -16px;
}