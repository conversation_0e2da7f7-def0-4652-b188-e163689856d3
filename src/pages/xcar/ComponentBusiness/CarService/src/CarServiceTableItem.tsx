/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-nested-ternary */
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useState, memo, CSSProperties } from 'react';
import {
  XView as View,
  XBoxShadow,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import BbkDashedLine from '@ctrip/rn_com_car/dist/src/Components/Basic/Dashedline';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  color,
  layout,
  icon,
  setOpacity,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { PackageDetailListType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './carServiceTableItemC2xStyles.module.scss';
import { Utils, CarLog, GetAB } from '../../../Util/Index';
import { ProductType } from '../../ValueAddedService';
import { insuranceDoc } from '../../Common/src/Enums';
import CarServiceTableCell from './CarServiceTableCell';
import { CarServiceFromPageTypes } from './Types';
import { texts } from './Texts';
import { ImageUrl, UITestID } from '../../../Constants/Index';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, useMemoizedFn, isAndroid, isHarmony, isIos } = BbkUtils;
const tipWidth = 186;

const getIconImages = code => {
  switch (code) {
    case insuranceDoc.basic:
      return Utils.compatImgUrlWithWebp(
        `${ImageUrl.DIMG04_PATH}0AS34120009empbx41B44.png`,
      );
    case insuranceDoc.otherOptimal:
      return Utils.compatImgUrlWithWebp(
        `${ImageUrl.DIMG04_PATH}0AS34120009empfcj498E.png`,
      );
    case insuranceDoc.honour:
    case insuranceDoc.easyHonour:
    case insuranceDoc.aHiHonour:
    case insuranceDoc.aHiMillion:
      return Utils.compatImgUrlWithWebp(
        `${ImageUrl.DIMG04_PATH}0AS62120009empbphAE73.png`,
      );
    default:
      return Utils.compatImgUrlWithWebp(
        `${ImageUrl.DIMG04_PATH}0AS34120009empbx41B44.png`,
      );
  }
};

const getIconImagesNew = code => {
  switch (code) {
    case insuranceDoc.basic:
      return Utils.compatImgUrlWithWebp(
        `${ImageUrl.DIMG04_PATH}1tg2x12000kt9pnwgFAE4.png`,
      );
    case insuranceDoc.otherOptimal:
      return Utils.compatImgUrlWithWebp(
        `${ImageUrl.DIMG04_PATH}1tg5k12000kt9ku42046C.png`,
      );
    case insuranceDoc.honour:
    case insuranceDoc.easyHonour:
    case insuranceDoc.aHiHonour:
    case insuranceDoc.aHiMillion:
      return Utils.compatImgUrlWithWebp(
        `${ImageUrl.DIMG04_PATH}1tg5k12000kt9ku42046C.png`,
      );
    default:
      return Utils.compatImgUrlWithWebp(
        `${ImageUrl.DIMG04_PATH}1tg2x12000kt9pnwgFAE4.png`,
      );
  }
};


const selectBtnShadowImg = Utils.compatImgUrlWithWebp(
  `${ImageUrl.DIMG04_PATH}0AS6f120009enbf743F6D.png`,
);
const styles = StyleSheet.create({
  selectedWrap: {
    borderStyle: 'solid',
    backgroundColor: color.tableSelectedBg,
    borderTopColor: color.blueBase,
    borderBottomColor: color.blueBase,
    borderLeftColor: color.blueBase,
    borderRightColor: color.blueBase,
    borderWidth: getPixel(1.5),
    borderRadius: getPixel(12),
    marginTop: getPixel(-0.5),
  },
  selectedWrapNew: {
    borderStyle: 'solid',
    backgroundColor: color.tableSelectedBg,
    borderTopColor: color.deepBlueBase,
    borderBottomColor: color.deepBlueBase,
    borderLeftColor: color.deepBlueBase,
    borderRightColor: color.deepBlueBase,
    borderWidth: getPixel(1.5),
    borderRadius: getPixel(12),
    marginTop: getPixel(-0.5),
  },
  firstBorder: {
    borderStyle: 'solid',
    borderWidth: 0,
    borderLeftColor: color.dayText,
    borderLeftWidth: isAndroid ? getPixel(1.5) : getPixel(1),
  },
  lastBorder: {
    borderRightWidth: isAndroid ? getPixel(1.5) : getPixel(1),
    borderTopRightRadius: getPixel(12),
    borderBottomRightRadius: getPixel(12),
    borderRightColor: color.dayText,
  },
  tableItemWrap: {
    borderStyle: 'solid',
    borderTopColor: color.dayText,
    borderBottomColor: color.dayText,
    borderTopWidth: getPixel(1),
    borderBottomWidth: getPixel(isHarmony ? 2 : 1),
  },
  mr2: {
    marginRight: getPixel(2),
    marginLeft: getPixel(2),
  },
  selectBtn: {
    borderWidth: StyleSheet.hairlineWidth,
    borderStyle: 'solid',
    borderColor: color.blueBase,
    borderRadius: getPixel(8),
    width: getPixel(128),
    height: getPixel(48),
    backgroundColor: color.tableBg,
    ...layout.alignHorizontal,
  },
  selectBtnNew: {
    borderWidth: StyleSheet.hairlineWidth,
    borderStyle: 'solid',
    borderColor: color.deepBlueBase,
    borderRadius: getPixel(8),
    width: getPixel(128),
    height: getPixel(48),
    backgroundColor: color.tableBg,
    ...layout.alignHorizontal,
  },
  tipTextCon: {
    backgroundColor: color.greenBase,
    paddingLeft: isAndroid ? getPixel(14) : getPixel(16),
    paddingRight: isAndroid ? getPixel(14) : getPixel(16),
    paddingTop: getPixel(9),
    paddingBottom: getPixel(8),
    borderRadius: getPixel(8),
    maxHeight: getPixel(74),
  },
  borderRadiusTB: {
    borderTopRightRadius: getPixel(12),
    borderBottomRightRadius: getPixel(12),
  },
  selectedFix: {
    position: 'absolute',
    left: getPixel(-1.5),
    top: getPixel(0),
    right: getPixel(-1.5),
    bottom: getPixel(0.5),
    zIndex: 0,
  },
  b60: {
    bottom: getPixel(-60),
  },
});

const checkImg = `${ImageUrl.DIMG04_PATH}0AS1r120009dqkq4h671D.png_.webp`;

const addImg =
  'https://ak-d.tripcdn.com/images/0AS31120009dqmbdjEC26.png_.webp';
const CNYImg = `${ImageUrl.DIMG04_PATH}0AS6y120009dqmb60AC6F.png`;
const CNYImgInclude = `${ImageUrl.DIMG04_PATH}1tg4h12000ccrn2388CD2.png`;

export const AddImg: React.FC<{ style?: CSSProperties }> = ({ style }) => (
  <Image
    src={
      GetAB.isISDInterestPoints()
        ? `${ImageUrl.DIMG04_PATH}1tg7412000d0mx9ol0966.png`
        : addImg
    }
    mode="aspectFill"
    className={c2xStyles.addImg}
    style={style}
  />
);

interface ICarServiceTableTipIsd {
  descTitle: string;
  style?: CSSProperties;
  triangleStyle?: CSSProperties;
}
export const CarServiceTableTipIsd: React.FC<ICarServiceTableTipIsd> = memo(
  ({ descTitle, style, triangleStyle }: ICarServiceTableTipIsd) => {
    return (
      <XBoxShadow
        className={c2xStyles.newTipWrap}
        style={style}
        coordinate={{ x: 0, y: 4 }}
        color="rgba(0, 111, 246, 0.16)"
        opacity={1}
        blurRadius={2}
      >
        <View className={c2xStyles.newTriangle} style={triangleStyle} />
        <View className={c2xStyles.tipDescWrap}>
          <Text className={c2xStyles.tipText}>{descTitle}</Text>
        </View>
      </XBoxShadow>
    );
  },
);
CarServiceTableTipIsd.defaultProps = { style: null, triangleStyle: null };
export interface ICarServiceTableItem {
  data?: PackageDetailListType;
  index?: number;
  width?: number;
  selected?: boolean;
  isFirst?: boolean;
  isLast?: boolean;
  isShowDefaultEncourage?: boolean;
  onPressButton?: (data) => void;
  onPressCarServiceDetail?: (uniqueCode) => void;
  presentInsurance: PackageDetailListType | undefined;
  selectedInsuranceId?: string[];
  fromPage?: string;
  ptime?: string;
  rtime?: string;
  isEhai?: boolean;
  setHeadMarginBottom?: (data: number) => void;
  hasMore?: boolean;
  isNewIsdBooking?: boolean;
}
const BbkComponentCarServiceTableItem: React.FC<ICarServiceTableItem> = ({
  data,
  index,
  width = -1,
  selected,
  isFirst,
  isLast,
  isShowDefaultEncourage,
  onPressButton = Utils.noop,
  onPressCarServiceDetail = Utils.noop,
  presentInsurance,
  selectedInsuranceId,
  fromPage,
  ptime,
  rtime,
  isEhai,
  setHeadMarginBottom,
  hasMore,
  isNewIsdBooking,
}: ICarServiceTableItem) => {
  const {
    name,
    currentDailyPrice = 0,
    description,
    allTags,
    uniqueCode,
    type,
    localDailyPrice = 0,
    vendorServiceCode,
    gapPrice = 0,
    cityEncourage,
    selectedEncourage,
  } = data;
  const [isCheck, setIsCheck] = useState(false);
  const isIncluded = type === ProductType.include;
  const isPresent = type === ProductType.present; // eslint-disable-next-line consistent-return
  const switchCheckStatus = useMemoizedFn(() => {
    if (isCheck) return false;
    const checked = !isCheck;
    setIsCheck(checked);
    onPressButton({
      index,
      name,
      checked,
      vendorServiceCode,
      uniqueCode,
      localDailyPrice,
    });
    if (fromPage === CarServiceFromPageTypes.booking) {
      CarLog.LogCode({
        name: '点击_填写页_选择车行服务',
        info: { carAgentInsuranceCode: uniqueCode },
      });
    }
  });
  const handlePressCarServiceDetail = useMemoizedFn(() => {
    onPressCarServiceDetail(uniqueCode);
    CarLog.LogCode({
      name: '点击_填写页_车行服务_服务内容区域',
      info: { carAgentInsuranceCode: uniqueCode },
    });
  });
  const isFromOrderDetail = fromPage === CarServiceFromPageTypes.orderDetail;
  const isSelected =
    isCheck ||
    (!selectedInsuranceId?.length &&
      (presentInsurance ? isPresent : !isFromOrderDetail && isIncluded));
  if (selected !== isCheck) {
    setIsCheck(selected);
  }
  const dayGap = Utils.getDayDiff(ptime, rtime); // @ts-ignore
  const newDescription = isFromOrderDetail
    ? description.filter(item => item?.type !== 'summary')
    : description;
  const encourage = isSelected
    ? selectedEncourage
    : isShowDefaultEncourage
      ? cityEncourage
      : '';
  if (encourage) {
    setHeadMarginBottom(getPixel(118));
  }
  const moreText = Math.max(Math.ceil((encourage?.length - 14) / 2), 0); // 中文标点符号出现在换行第一行时，无法展示全，所以大于14个字时样式统一增加20个像素
  const offsetW = moreText > 0 ? 20 : 0;
  const tipW = tipWidth + moreText * 11 * 2 + offsetW;
  const triangleLeft = 84.5 + moreText * 11 + offsetW / 2;
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  const getIconImagesFn = isNewIsdBooking ? getIconImagesNew : getIconImages;
  return (
    <View
      testID={UITestID.car_testid_comp_booking_ins_content_item}
      className={c2xStyles.unSelectedWrap}
      style={width >= 0 && { width }}
    >
      <View
        style={
          isSelected &&
          xMergeStyles([
            styles.tableItemWrap,
            styles.selectedFix,
            isLast && styles.borderRadiusTB,
          ])
        }
      />

      <XBoxShadow
        coordinate={{ x: 0, y: getPixel(4) }}
        color={
          isIos && isSelected
            ? setOpacity(color.selectedShadowBg, 0.2)
            : setOpacity(color.selectedShadowBg, 0)
        }
        opacity={1}
        blurRadius={getPixel(12)}
        className={
          isEhai && hasMore
            ? c2xStyles.eHaihasMoreBorder
            : c2xStyles.defaultBorder
        }
        style={xMergeStyles([
          !isSelected && !(isEhai && hasMore) && styles.tableItemWrap,
          isFirst && !isSelected && styles.firstBorder,
          isLast && !isSelected && !(isEhai && hasMore) && styles.lastBorder,
          isSelected &&
            (isISDInterestPoints
              ? styles.selectedWrapNew
              : styles.selectedWrap),
        ])}
      >
        <BbkTouchable
          testID={UITestID.car_testid_comp_booking_ins_content_btn}
          onPress={handlePressCarServiceDetail}
          className={c2xStyles.serviceHead}
        >
          <Image
            src={getIconImagesFn(uniqueCode) || getIconImagesFn(insuranceDoc.basic)}
            mode="aspectFit"
            className={isNewIsdBooking ? c2xStyles.serviceIconNew : c2xStyles.serviceIcon}
          />

          <Text className={c2xStyles.serviceName} fontWeight="bold">
            {name}
          </Text>
          <View className={c2xStyles.detailWrap}>
            <Text
              className={classNames(
                c2xCommonStyles.c2xTextDefaultCss,
                isISDInterestPoints
                  ? c2xStyles.detailTextNew
                  : c2xStyles.detailText,
              )}
            >
              {texts.detail}
            </Text>
            <Text
              type="icon"
              className={classNames(
                c2xCommonStyles.c2xTextDefaultColor,
                isISDInterestPoints
                  ? c2xStyles.detailIconNew
                  : c2xStyles.detailIcon,
              )}
            >
              {icon.arrowRight}
            </Text>
          </View>
        </BbkTouchable>
        <BbkDashedLine
          lineWidth={getPixel(1)}
          itemWidth={3}
          lineColor={color.dayText}
          style={styles.mr2}
        />

        <BbkTouchable
          testID={`${UITestID.car_testid_page_booking_carservice_item}_${index}`}
          onPress={switchCheckStatus}
        >
          {newDescription?.map((item, index) => (
            <CarServiceTableCell
              key={`${item.description}_${index}`}
              contains={item.contains}
              onPressCarServiceDetail={switchCheckStatus}
              description={item.description}
              descriptionColorCode={item?.descriptionColorCode}
              containsDescription={item.containsDescription}
              isFirst={index === 0}
              isLast={index === newDescription.length - 1}
              allTags={allTags}
              isEhai={isEhai}
            />
          ))}
        </BbkTouchable>

        <BbkDashedLine
          lineWidth={getPixel(1)}
          itemWidth={3}
          lineColor={color.dayText}
          style={styles.mr2}
        />

        <BbkTouchable
          testID={UITestID.car_testid_comp_booking_ins_content_item_btn}
          onPress={switchCheckStatus}
          className={c2xStyles.serviceFoot}
        >
          {uniqueCode === insuranceDoc.basic && !!presentInsurance ? (
            <Text className={c2xStyles.footDescText}>
              {texts.upgradedToForFree(presentInsurance.name)}
            </Text>
          ) : (
            <>
              {!(isFromOrderDetail && isIncluded) && (
                <View style={layout.alignHorizontal}>
                  {/** 2022-11-22 售前基础服务不展示加购价展示日价 */}
                  {uniqueCode === insuranceDoc.basic ? (
                    <>
                      <Image
                        src={CNYImgInclude}
                        mode="aspectFill"
                        className={c2xStyles.CNYImg}
                      />

                      <Text
                        className={c2xStyles.selectedIndePriDigPrice}
                        fontWeight="bold"
                      >
                        {currentDailyPrice}
                      </Text>
                      {dayGap > 1 && (
                        <Text className={c2xStyles.selectedPriceUnitDay}>
                          {texts.uintDay}
                        </Text>
                      )}
                    </>
                  ) : (
                    <>
                      <AddImg />
                      <Image
                        src={
                          isISDInterestPoints
                            ? `${ImageUrl.DIMG04_PATH}1tg1212000d2wh80k3404.png`
                            : CNYImg
                        }
                        mode="aspectFill"
                        className={c2xStyles.CNYImg}
                      />

                      <Text
                        className={classNames(
                          c2xCommonStyles.c2xTextDefaultCss,
                          isISDInterestPoints
                            ? c2xStyles.indePriDigPriceNew
                            : c2xStyles.indePriDigPrice,
                        )}
                      >
                        {gapPrice}
                      </Text>
                      {dayGap > 1 && (
                        <Text
                          className={classNames(
                            c2xCommonStyles.c2xTextDefaultCss,
                            isISDInterestPoints
                              ? c2xStyles.priceUnitDayNew
                              : c2xStyles.priceUnitDay,
                          )}
                        >
                          {texts.uintDay}
                        </Text>
                      )}
                    </>
                  )}
                </View>
              )}
              {isCheck && !isIncluded && (
                <Text className={c2xStyles.currentHave}>{texts.selected}</Text>
              )}
              {!isFromOrderDetail && uniqueCode === insuranceDoc.basic && (
                <Text className={c2xStyles.currentHave}>
                  {texts.insTablecurrentHave}
                </Text>
              )}
            </>
          )}
          {isFromOrderDetail && isIncluded && (
            <Text className={c2xStyles.currentHave}>
              {texts.currentlyPurchased}
            </Text>
          )}
          {!isSelected &&
            !(isFromOrderDetail && isIncluded) &&
            uniqueCode !== insuranceDoc.basic && (
              <View className={c2xStyles.mt5}>
                <Image
                  src={selectBtnShadowImg}
                  className={c2xStyles.selectBtnShadow}
                />

                <View
                  style={
                    isISDInterestPoints ? styles.selectBtnNew : styles.selectBtn
                  }
                >
                  <Text
                    className={classNames(
                      c2xCommonStyles.c2xTextDefaultCss,
                      isISDInterestPoints
                        ? c2xStyles.selectTxtNew
                        : c2xStyles.selectTxt,
                    )}
                  >
                    {texts.select}
                  </Text>
                </View>
              </View>
            )}
          {!!isSelected && (
            <Image
              src={
                isISDInterestPoints
                  ? `${ImageUrl.DIMG04_PATH}1tg5912000d0mx9c68626.png`
                  : checkImg
              }
              mode="aspectFill"
              className={c2xStyles.checkImg}
            />
          )}
        </BbkTouchable>
      </XBoxShadow>
      {!!encourage && (
        <View
          className={c2xStyles.tipWrap}
          style={xMergeStyles([
            encourage?.length < 8 && styles.b60,
            { left: (width - getPixel(tipW)) / 2, width: getPixel(tipW) },
          ])}
        >
          <View
            className={c2xStyles.triangle}
            style={{ left: getPixel(triangleLeft) }}
          />

          <View className={c2xStyles.tipTextWrap}>
            <View
              style={xMergeStyles([
                styles.tipTextCon,
                { maxWidth: getPixel(tipW) },
              ])}
            >
              <Text className={c2xStyles.tipText}>{encourage}</Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};
export default memo(BbkComponentCarServiceTableItem);
