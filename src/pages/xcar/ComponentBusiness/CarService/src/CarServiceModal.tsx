import { findLast as lodashFindLast, pull as lodashPull } from 'lodash-es';
import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useState, useEffect } from 'react';
import { xMergeStyles } from '@ctrip/xtaro';
import { BbkComponentPageModal } from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import BbkModalHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalHeader/src/Index';
import BbkComponentModalFooter from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalFooter/src/Index';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  RentalGuaranteeV2Type,
  PackageDetailListType,
  PurchasingNoticeType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import BbkButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import { texts } from './Texts';
import BbkComponentCarService from '..';
import { ProductType } from '../../ValueAddedService';
import ServiceClaimMoreModal from '../../ServiceClaimMoreModal';
import CarServiceDetailModal, {
  CarServiceDetailFooter,
  CarServiceStyles,
} from '../../CarServiceDetailModal';
import InsuranceBackground, { BackgroudType } from '../../ImageBackground';
import { UITestID } from '../../../Constants/Index';

const { vh, vw, getPixel, useMemoizedFn, adaptNoaNomalousBottom } = BbkUtils;
const styles = StyleSheet.create({
  wrap: {
    height: vh(90),
    backgroundColor: color.white,
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
    overflow: 'hidden',
    borderWidth: 0,
  },
  headerWrap: {
    height: getPixel(130),
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
    backgroundColor: color.transparent,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: color.white,
  },
  footerWrap: {
    paddingTop: getPixel(16),
    ...layout.flexRowWrapCenterEnd,
    ...layout.flex1,
    paddingBottom: getPixel(16) + adaptNoaNomalousBottom(),
    paddingRight: getPixel(32),
  },
  footerBtn: {
    width: getPixel(222),
    height: getPixel(88),
    paddingTop: 0,
    paddingBottom: 0,
    borderRadius: getPixel(12),
    backgroundColor: color.grayBorder,
  },
  footerBtnText: {
    ...font.title4MediumStyle,
    color: color.grayBase,
  },
  mtf16: {
    marginTop: -getPixel(16),
  },
  mtf8: {
    marginTop: getPixel(-8),
  },
  transparent: { backgroundColor: color.blackTransparent },
});

interface IProps {
  visible: boolean;
  onCancel?: any;
  ptime?: string;
  rtime?: string;
  insuranceProductTips?: string;
  rentalGuaranteeV2?: RentalGuaranteeV2Type;
  isEhai?: boolean;
  dayGap?: number;
  buyIsdInsuranceOrder?: (data) => void;
  fromPage?: string;
  detailModalVisible?: boolean;
  claimMoreVisible?: boolean;
  detailModalData?: {
    data?: Array<PackageDetailListType>;
    purchasingNotice?: PurchasingNoticeType;
  };
  closeCarServiceDetail: () => void;
  closeServiceClaimMore: () => void;
  claimMoreModalRefFn?: any;
}

const CarServiceModal: React.FC<IProps> = ({
  visible,
  onCancel,
  insuranceProductTips,
  rentalGuaranteeV2,
  ptime,
  rtime,
  buyIsdInsuranceOrder,
  fromPage,
  detailModalVisible,
  detailModalData,
  closeCarServiceDetail,
  claimMoreVisible,
  closeServiceClaimMore,
  isEhai,
  dayGap,
  claimMoreModalRefFn,
}) => {
  const [addOnCodes, setAddOnCodes] = useState([]);
  const [carServiceDetailVisible, setcarServiceDetailVisible] = useState(false);
  const [showServiceDetailIndex, setshowServiceDetailIndex] = useState(0);
  const [serviceClaimMoreVisible, setserviceClaimMoreVisible] = useState(false);
  const [showServiceDetailCode, setShowServiceDetailCode] = useState('');
  const footerButtonName = texts.goPay;
  const onPressServiceClaimMore = useMemoizedFn(() => {
    setserviceClaimMoreVisible(true);
  });

  const onPressCarServiceDetail = useMemoizedFn((code?) => {
    setcarServiceDetailVisible(true);
    setShowServiceDetailCode(code);
  });

  useEffect(() => {
    if (detailModalVisible) {
      onPressCarServiceDetail();
    }
    if (claimMoreVisible) {
      onPressServiceClaimMore();
    }
  }, [detailModalVisible, claimMoreVisible]);

  useEffect(() => {
    const defaultAddOnCodes = lodashFindLast(
      rentalGuaranteeV2?.packageDetailList,
      item => item?.type === ProductType.include,
    );
    setAddOnCodes([defaultAddOnCodes?.uniqueCode]);
  }, [rentalGuaranteeV2]);

  const onPressButton = useMemoizedFn(({ uniqueCode: code, index }) => {
    let codes = [...addOnCodes];
    if (addOnCodes.includes(code)) {
      lodashPull(codes, code);
    } else {
      codes = [code];
    }
    setAddOnCodes(codes);
    setshowServiceDetailIndex(index);
  });

  const hideCarServiceDetail = useMemoizedFn(() => {
    setcarServiceDetailVisible(false);
    closeCarServiceDetail();
    setShowServiceDetailCode('');
  });

  const hideServiceClaimMore = useMemoizedFn(() => {
    setserviceClaimMoreVisible(false);
    closeServiceClaimMore();
  });

  React.useImperativeHandle(
    claimMoreModalRefFn,
    () => ({
      hideServiceClaimMore: () => {
        hideServiceClaimMore();
      },
    }),
    [hideServiceClaimMore],
  );

  const changeSelectInsurance = useMemoizedFn(data => {
    const code = data.addOnCode;
    const idx = rentalGuaranteeV2?.packageDetailList?.findIndex(
      item => item?.uniqueCode === code,
    );
    setshowServiceDetailIndex(idx);
    setAddOnCodes([code]);
  });

  const currentData =
    rentalGuaranteeV2?.packageDetailList?.[showServiceDetailIndex] || {};

  const footerBtnClick = useMemoizedFn(() => {
    buyIsdInsuranceOrder({ ...currentData, packageDetail: currentData });
  });
  return (
    <>
      <BbkComponentPageModal
        location="bottom"
        animateType="slideUp"
        animateDuration={300}
        visible={visible}
        onMaskPress={onCancel}
        closeModalBtnTestID={UITestID.car_testid_carservice_modal_closemask}
      >
        <InsuranceBackground
          type={BackgroudType.SaleAfterService}
          style={styles.wrap}
        >
          <BbkModalHeader
            hasTopBorderRadius={true}
            showRightIcon={false}
            showLeftIcon={true}
            leftIconStyle={xMergeStyles([
              CarServiceStyles.leftIconStyle,
              styles.mtf8,
            ])}
            onClose={onCancel}
            style={styles.headerWrap}
            leftIconTestID={
              UITestID.car_testid_carservice_modal_header_lefticon
            }
            titleStyle={xMergeStyles([
              CarServiceStyles.modalTitle,
              styles.mtf8,
            ])}
            title={texts.upgradeCarService}
          />

          <ScrollView
            testID={UITestID.car_testid_page_order_detail_car_service_modal}
            style={styles.mtf16}
          >
            <BbkComponentCarService
              insuranceProductTips={insuranceProductTips}
              rentalGuaranteeV2={rentalGuaranteeV2}
              selectedInsuranceId={addOnCodes}
              onPressButton={onPressButton}
              onPressServiceClaimMore={onPressServiceClaimMore}
              onPressCarServiceDetail={onPressCarServiceDetail}
              haveInsBg={false}
              fromPage={fromPage}
              ptime={ptime}
              rtime={rtime}
              width={vw(100)}
              isHideDetailBtn={true}
              isEhai={isEhai}
            />
          </ScrollView>
          {currentData?.type === ProductType.include ? (
            <BbkComponentModalFooter isShadow={true} style={styles.footerWrap}>
              <BbkButton
                buttonStyle={styles.footerBtn}
                testID={UITestID.car_testid_carservice_modal_footer_button}
                textStyle={styles.footerBtnText}
                text={footerButtonName}
                disabled={true}
              />
            </BbkComponentModalFooter>
          ) : (
            <CarServiceDetailFooter
              data={rentalGuaranteeV2?.packageDetailList}
              currentData={currentData}
              selectedInsuranceId={addOnCodes[addOnCodes.length - 1]}
              dayGap={dayGap}
              onPressBtn={footerBtnClick}
              onCancle={onCancel}
              buttonText={footerButtonName}
              fromPage={fromPage}
              isDetailFooter={false}
              isFromServiceModal={true}
            />
          )}
        </InsuranceBackground>
      </BbkComponentPageModal>
      <CarServiceDetailModal
        visible={carServiceDetailVisible}
        showIndex={showServiceDetailIndex}
        onCancel={hideCarServiceDetail}
        data={detailModalData?.data || rentalGuaranteeV2?.packageDetailList}
        purchasingNotice={
          detailModalData?.purchasingNotice ||
          rentalGuaranteeV2?.purchasingNotice
        }
        ptime={ptime}
        rtime={rtime}
        dayGap={dayGap}
        selectedInsuranceIds={addOnCodes}
        changeSelectInsurance={changeSelectInsurance}
        fromPage={fromPage}
        haveFooter={!detailModalVisible}
        showServiceDetailCode={showServiceDetailCode}
        modalBgStyle={visible && styles.transparent}
      />
      {/* 门店服务保障相关要求及须知 */}
      <ServiceClaimMoreModal
        visible={serviceClaimMoreVisible}
        onCancel={hideServiceClaimMore}
        data={rentalGuaranteeV2?.vendorServiceDetail}
        modalBgStyle={visible && styles.transparent}
      />
    </>
  );
};

export default CarServiceModal;
