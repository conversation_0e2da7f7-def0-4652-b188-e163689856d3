import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import PhotoBrowser from '@c2x/apis/PhotoBrowser'; /* eslint-disable */

/* bbk-component-business-migrate */
import React, { memo, useCallback, CSSProperties } from 'react';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text/src/Text';
import {
  XView as View,
  XImageProps as ImageProps,
  xMergeStyles,
} from '@ctrip/xtaro';
/* eslint-disable */

/* bbk-component-business-migrate */

// PhotoBrowser文档：http://pages.release.ctripcorp.com/BaseBiz-Open/basebizcomponentsbook/CRN/ImageBrorwse/CRNImageBrorwse.html

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkCarImage from '../../CarImage';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './imageListC2xStyles.module.scss';
import { UITestID } from '../../../Constants/Index';

const { getPixel, getProcImageUrl, ProcImageParamsType } = BbkUtils;
interface IImage {
  resizeMode?: string;
}
interface VehicleImgProps {
  /**
   * 图片数据
   */
  imgList: ImageObject[];
  /**
   * 是否横向滚动
   */
  scroll?: boolean;
  /**
   * 是否点击图片放大
   * 默认 true
   */
  zoom?: boolean;
  /**
   * 每个图片点击的回调
   * params: index 图片的 index
   */
  onPress?: (index: number) => void;
  /**
   * 覆盖图片的 ImageProps
   */
  imgProps?: IImage;
  /**
   * 图片列表的包裹样式
   */
  listWrapStyle?: CSSProperties;
  /**
   * 每个图片的包裹样式
   * 可用于 label 的绝对定位
   */
  imgWrapStyle?: CSSProperties;
  /**
   * 图片样式
   */
  imgStyle?: CSSProperties;
  /**
   * 最后一张图片的样式
   */
  lastImgWrapStyle?: CSSProperties;
  // @ts-ignore
  procImageParam?: ProcImageParamsType; // 图片裁剪参数
  onImageLoadError?: (error: any, imageUrl: string) => void;
  cornerText?: string; // 是否展示角标
  wholeImgList?: []; // 全部图片
}

export interface ImageObject {
  imageUrl: string;
  /**
   * 与图片同级的 ReactElement,
   * 方便插入一些 label
   */
  slotDom?: any;
}
const styles = StyleSheet.create({
  imgStyle: {
    width: getPixel(440),
    height: getPixel(294),
  },
  listWrapStyle: {},
});

// 定义ImageListItem组件的props类型
interface ImageListItemProps {
  imageUrl: string;
  slotDom?: React.ReactNode;
  index: number;
  isLast: boolean; // 用于判断是否是最后一项，以应用lastImgWrapStyle
  zoom: boolean;
  imgWrapStyle?: any;
  lastImgWrapStyle?: any;
  imgStyle?: any;
  imgProps?: any;
  procImageParam?: any;
  onImageLoadError: (error: any, url: string) => void;
  cornerText?: string;
  // 以下两个props用于onImagePress回调
  wholeImgList?: []; // 全部图片
  imgList: ImageObject[]; // 注意：这里我们传递整个imgList，因为回调中需要
  onPress?: (index: number) => void;
}

// 提取ImageListItem组件
const ImageListItem = memo((props: ImageListItemProps) => {
  const {
    imageUrl,
    slotDom,
    index,
    isLast,
    zoom,
    imgWrapStyle,
    lastImgWrapStyle,
    imgStyle,
    imgProps,
    procImageParam,
    onImageLoadError,
    cornerText,
    wholeImgList,
    imgList,
    onPress,
  } = props;

  // 使用useCallback定义图片点击事件
  const onImagePress = useCallback(() => {
    // 使用wholeImgList（如果存在）或imgList作为图片浏览器数据源
    PhotoBrowser.showWithScrollCallback(
      wholeImgList || imgList,
      [],
      index,
      {},
      () => {},
    );
    if (onPress) {
      onPress(index);
    }
  }, [wholeImgList, imgList, index, onPress]); // 依赖项包括这些变量

  // 处理图片URL
  const cImageUrl = getProcImageUrl(imageUrl, procImageParam);

  return (
    <BbkTouchable
      disabled={!zoom}
      onPress={onImagePress}
      className={c2xStyles.imgWrapStyle}
      style={xMergeStyles([
        imgWrapStyle,
        isLast && lastImgWrapStyle, // 如果是最后一项，则应用lastImgWrapStyle
      ])}
      testID={UITestID.car_testid_comp_vendor_modal_vehicle_pic}
    >
      {/* 如果是第一项且cornerText存在，则显示角标 */}
      {index === 0 && cornerText && (
        <View className={c2xStyles.corner}>
          <Text className={c2xStyles.cornerText}>{cornerText}</Text>
        </View>
      )}
      <BbkCarImage
        source={{ uri: cImageUrl }}
        resizeMode="cover"
        style={xMergeStyles([styles.imgStyle, imgStyle])}
        {...imgProps}
        onError={error => {
          onImageLoadError(error, cImageUrl);
        }}
      />
      {slotDom}
    </BbkTouchable>
  );
});

const ImageList = memo((props: VehicleImgProps) => {
  const {
    imgList,
    scroll,
    zoom = true,
    onPress,
    imgProps,
    listWrapStyle,
    imgWrapStyle,
    imgStyle,
    lastImgWrapStyle,
    procImageParam,
    onImageLoadError = () => {},
    cornerText,
    wholeImgList,
  } = props;
  // 生成子组件列表
  const children = imgList.map(({ imageUrl, slotDom }, index) => {
    // 计算是否是最后一项
    const isLast = index === imgList.length - 1;
    return (
      <ImageListItem
        key={imageUrl}
        imageUrl={imageUrl}
        slotDom={slotDom}
        index={index}
        isLast={isLast}
        zoom={zoom}
        imgWrapStyle={imgWrapStyle}
        lastImgWrapStyle={lastImgWrapStyle}
        imgStyle={imgStyle}
        imgProps={imgProps}
        procImageParam={procImageParam}
        onImageLoadError={onImageLoadError}
        cornerText={cornerText}
        wholeImgList={wholeImgList}
        imgList={imgList} // 传递整个imgList，因为回调中需要
        onPress={onPress}
      />
    );
  });
  return scroll ? (
    <ScrollView
      style={xMergeStyles([styles.listWrapStyle, listWrapStyle])}
      horizontal
      showsHorizontalScrollIndicator={false}
      testID={UITestID.car_testid_page_vendorList_carInfoModal_image}
    >
      {children}
    </ScrollView>
  ) : (
    <View className={c2xStyles.listWrapStyle} style={listWrapStyle}>
      {children}
    </View>
  );
});

export default ImageList;
