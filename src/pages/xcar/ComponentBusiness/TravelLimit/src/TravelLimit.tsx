import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useEffect, useRef, useState, useMemo } from 'react';
import { xMergeStyles, XViewExposure } from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './travelLimitC2xStyles.module.scss';
import { ITravelLimit, ICrossType } from './Types';
import TravelLimitItem from './TravelLimitItem';
import Note from './Note';
import { CarLog } from '../../../Util/Index';
//小程序业务差异化：解耦textindex
import * as OrderDetailTextsV2 from '../../../Constants/OrderDetail';
import {
  getOrderDetailStatusData,
  OrderDetailStatusKeyList,
  setOrderDetailStatusData,
} from '../../../Global/Cache/OrderStatusData';
import CountriesListModal from '../../CountriesListModal';
import { CrossStatus } from '../../../Types/Dto/DetailType';

const { getPixel, ensureFunctionCall, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  pb8: {
    paddingBottom: getPixel(8),
  },
});

const TravelLimit: React.FC<ITravelLimit> = ({
  title,
  moduleTitle,
  crossLocationsInfos,
  notes,
  selectedResult,
  onLayout,
  logBaseInfo,
  directOpen,
  handleMainscrollerScroll,
  style,
  setTravelLimitSelectedResult,
  setOrderDetailTravelLimitSelectedResult,
}) => {
  const anchorTimer = useRef(null);
  const [modalVisible, setModalVisible] = useState(false);
  useEffect(() => {
    if (anchorTimer.current) {
      clearTimeout(anchorTimer.current);
      anchorTimer.current = null;
    }
    if (directOpen) {
      const delayTime = isAndroid ? 800 : 200;
      anchorTimer.current = setTimeout(() => {
        if (
          Number(directOpen) === OrderDetailTextsV2.DirectOpen.TravelLimit &&
          !getOrderDetailStatusData(
            OrderDetailStatusKeyList.hasDirectOpenTravelLimit,
          )
        ) {
          const y = getOrderDetailStatusData(
            OrderDetailStatusKeyList.travelLimitLayoutY,
          );
          ensureFunctionCall(handleMainscrollerScroll?.(y));
          setOrderDetailStatusData(
            OrderDetailStatusKeyList.hasDirectOpenTravelLimit,
            true,
          );
        }
      }, delayTime);
    }

    return () => {
      clearTimeout(anchorTimer.current);
      anchorTimer.current = null;
    };
  }, [directOpen, handleMainscrollerScroll]);

  const onCLoseModal = useMemoizedFn(() => {
    setModalVisible(false);
  });

  const onSelectCountry = useMemoizedFn(() => {
    setModalVisible(true);
  });

  const countriesModalData = crossLocationsInfos?.find(
    item => item.crossType === ICrossType.CrossCountry,
  );
  const {
    title: modalTitle,
    subTitle: modalSubTitle,
    locations,
  } = countriesModalData || {};
  const enAbleLocations = useMemo(() => {
    return locations?.filter(item =>
      [CrossStatus.Allow, CrossStatus.Conditional].includes(item.status),
    );
  }, [locations]);
  return (
    <>
      <XViewExposure
        onLayout={onLayout}
        testID={CarLog.LogExposure({
          name: '曝光_产品详情页_跨境跨岛政策',

          info: {
            ...logBaseInfo,
            crossType: crossLocationsInfos?.map(item => item?.crossTypeName),
          },
        })}
        className={c2xStyles.wrap}
        style={xMergeStyles([!notes?.[0] && styles.pb8, style])}
      >
        {!!title && (
          <Text className={c2xStyles.title} fontWeight="medium">
            {moduleTitle || title}
          </Text>
        )}
        {crossLocationsInfos?.length > 0 &&
          crossLocationsInfos.map((item, index) => (
            <TravelLimitItem
              key={item.crossTypeName}
              title={item.title}
              subTitle={item.subTitle}
              crossType={item.crossType}
              crossTypeName={item.crossTypeName}
              summaryTitle={item.summaryTitle}
              summaryPolicies={item.summaryPolicies}
              locations={enAbleLocations}
              isFirst={index === 0}
              selectedResult={selectedResult}
              logBaseInfo={logBaseInfo}
              onSelectCountry={onSelectCountry}
            />
          ))}
        <Note note={notes?.[0]} />
      </XViewExposure>
      <CountriesListModal
        visible={modalVisible}
        title={modalTitle}
        subTitle={modalSubTitle}
        locations={enAbleLocations}
        selectedLocationsProps={selectedResult}
        confirmCallback={setTravelLimitSelectedResult}
        setOrderDetailTravelLimitSelectedResult={
          setOrderDetailTravelLimitSelectedResult
        }
        onClose={onCLoseModal}
      />
    </>
  );
};

export default memo(TravelLimit);
