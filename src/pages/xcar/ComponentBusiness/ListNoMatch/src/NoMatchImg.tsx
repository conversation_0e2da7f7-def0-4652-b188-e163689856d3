import Image from '@c2x/components/Image';
import React, { Component, CSSProperties } from 'react';
import { xMergeStyles } from '@ctrip/xtaro';
import { BbkStyleUtil, BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import * as ImageUrl from '../../../Constants/ImageUrl';

const { autoProtocol } = BbkUtils;

export enum ImgType {
  No_Network = 'animation_no_network',
  No_Search_Result = 'animation_search_no_result',
  No_Review = 'animation_search_no_result',
  // No_Review = 'animation_no_review', // this pic is rectangular and not suitable for this component, so changed to 'No_Search_Result' pic temporarily , confirmed by zjj
  Failed_To_Load = 'animation_failed_to_load',
  No_Coupon = 'animation_no_coupon_v2',
  No_Pic = 'animation_no_pic',
  Comming_Soon = 'animation_comming_soon',
  No_Response = 'load_fail',
  System_Error = 'system_error',
  Vendor_List_No_Match = 'vendor_list_no_match',
  Vendor_List_Sold_Out = 'vendor_list_sold_out',
}
const ImgTypeMap = {
  animation_no_network: `${ImageUrl.BBK_IMAGE_PATH}system_error.png`,
  animation_search_no_result: `${ImageUrl.DIMG04_PATH}1tg1x12000gdev971FDE6.png`,

  animation_failed_to_load: `${ImageUrl.BBK_IMAGE_PATH}animation_failed_to_load.png`,
  animation_no_coupon_v2: `${ImageUrl.DIMG04_PATH}1tg4l12000gdfj7m6DA82.png`,

  animation_no_pic: `${ImageUrl.BBK_IMAGE_PATH}animation_no_pic.png`,

  animation_comming_soon: `${ImageUrl.DIMG04_PATH}1tg1x12000gdev971FDE6.png`,

  load_fail: `${ImageUrl.BBK_IMAGE_PATH}load_fail.png`,

  system_error: `${ImageUrl.BBK_IMAGE_PATH}system_error.png`,

  vendor_list_no_match: `${ImageUrl.DIMG04_PATH}1tg3s12000g5zpjjv5892.png`,

  vendor_list_sold_out: `${ImageUrl.DIMG04_PATH}1tg4l12000gdfj7m6DA82.png`,
};

export interface NoMatchImgPropsType {
  type: ImgType;
  imgStyle?: CSSProperties;
  newImgStyle?: CSSProperties;
  isShowRentalDate?: boolean;
  imgViewStyle?: CSSProperties;
}

export default class NoMatchImg extends Component<NoMatchImgPropsType> {
  getImgStyle = () => {
    const { isShowRentalDate } = this.props;
    const showWH = 150;
    const noShowWH = 240;

    return isShowRentalDate
      ? BbkStyleUtil.getWH(showWH, showWH)
      : BbkStyleUtil.getWH(noShowWH, noShowWH);
  };

  render() {
    const { type, imgStyle, imgViewStyle, newImgStyle } = this.props;
    return (
      <Image
        style={xMergeStyles([
          this.getImgStyle(),
          { ...imgStyle },
          imgViewStyle,
          newImgStyle,
        ])}
        src={
          type === ImgType.No_Network
            ? autoProtocol(
                '//pic.c-ctrip.com/car/osd/mobile/bbk/resource/netError.png',
              )
            : ImgTypeMap[type]
        }
        mode="aspectFit"
      />
    );
  }
}
