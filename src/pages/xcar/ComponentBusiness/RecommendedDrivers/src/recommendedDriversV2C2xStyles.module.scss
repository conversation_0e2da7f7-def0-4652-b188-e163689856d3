@import '../../../Common/src/Tokens/tokens/color.scss';

.driverItem {
  width: 130px;
  height: 50px;
  margin-right: 16px;
  background-color: $tableGrayBg;
  border-radius: 8px;
  position: relative;
}
.title {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
  margin-top: 1px;
}
.titleSelected {
  font-size: 24px;
  line-height: 32px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $bookingOptimizationBlue;
}
.icon {
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: -1px;
  right: -1px;
}
.moreWrapper {
  background-color: $white;
  padding-left: 32px;
  padding-top: 33px;
  padding-bottom: 33px;
}
.followMoreWrapper {
  margin-left: 16px;
}
.moreText {
  color: $bookingOptimizationBlue;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.moreIcon {
  font-size: 26px;
  margin-right: 4px;
  color: $bookingOptimizationBlue;
}
.wrapper {
  padding-left: 32px;
  padding-right: 32px;
  margin-top: -3px;
}
.scrollContentWrap {
  flex-direction: row;
  align-items: center;
}
.moreWrapperNew {
  background-color: $white;
  padding: 29px 0 31px 16px;
}
.moreTextNew {
  font-size: 26px;
  line-height: 36px;
  color: $blueGrayBase;
}
.moreIconNew {
  font-size: 26px;
  color: $blueGrayBase;
}
.shadowContainer {
  position: absolute;
  width: 32px;
  height: 135px;
  left: -32px;
}
