import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, {
  memo,
  useCallback,
  useEffect,
  useRef,
  useMemo,
  useState,
} from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XBoxShadow,
} from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  color,
  layout,
  icon,
  setOpacity,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import useLazyLoad from '@ctrip/rn_com_car/dist/src/Hooks';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { Passenger } from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import c2xStyles from './recommendedDriversV2C2xStyles.module.scss';
import AutoScrollView from './components/HistoricalAutoScrollView';
import { texts } from './Texts';
import { ImageUrl, UITestID } from '../../../Constants/Index';
import { CarLog, GetABCache } from '../../../Util/Index';

const { getPixel, vw } = BbkUtils;
const styles = StyleSheet.create({
  withShadow: {
    overflow: 'hidden',
  },
  inner: {
    paddingTop: getPixel(24),
    paddingBottom: getPixel(32),
    marginBottom: getPixel(-32),
  },
  fixInner: {
    paddingTop: getPixel(0),
    paddingBottom: getPixel(8),
    marginBottom: getPixel(-32),
  },
  driverItemSelected: {
    backgroundColor: color.driverItemSelectedBg,
    borderWidth: GetABCache.isISDShelves3() ? getPixel(2) : getPixel(1),
    borderColor: color.bookingOptimizationBlue,
  },
});

type IDriverItem = {
  isSelected?: boolean;
  title: string;
  onPress: (item: Passenger) => void;
  item: Passenger;
};

const DriverItem: (props: IDriverItem) => React.ReactElement = ({
  isSelected,
  title,
  onPress,
  item,
}) => {
  const handlePress = useCallback(() => {
    CarLog.LogCode({
      name: '点击_填写页_常旅切换',
    });
    onPress(item);
  }, [onPress, item]);

  if (!title) return null;

  // 姓名超出4字以上中间截断，保留前2字+最后一字
  const fixTitle =
    title.length > 3 ? `${title.substr(0, 2)}...${title.substr(-1, 1)}` : title;
  return (
    <Touchable
      debounce={true}
      debounceTime={800}
      onPress={handlePress}
      testID={`${UITestID.car_testid_page_booking_form_recommend_driver_item}_${fixTitle}`}
      className={c2xStyles.driverItem}
      style={xMergeStyles([
        layout.flexCenter,
        isSelected && styles.driverItemSelected,
      ])}
    >
      <Text
        className={classNames(
          c2xStyles.title,
          isSelected && c2xStyles.titleSelected,
        )}
      >
        {fixTitle}
      </Text>
      {isSelected && (
        <Image
          className={c2xStyles.icon}
          src={`${ImageUrl.BBK_IMAGE_PATH}booking_selected.png`}
          mode="aspectFit"
        />
      )}
    </Touchable>
  );
};
interface IMoreItem {
  isFixed?: boolean;
  onPress?: () => void;
  isScrollEnd?: boolean;
}
const MoreItem: React.FC<IMoreItem> = memo(({ isFixed, onPress }) => {
  return (
    <XBoxShadow
      coordinate={{ x: getPixel(-8), y: 0 }}
      color={
        isFixed ? setOpacity(color.black, 0.02) : setOpacity(color.black, 0)
      }
      opacity={1}
      blurRadius={getPixel(8)}
    >
      <Touchable
        debounce={true}
        onPress={onPress}
        testID={UITestID.car_testid_page_booking_form_recommend_driver_more}
        className={
          isFixed ? c2xStyles.moreWrapper : c2xStyles.followMoreWrapper
        }
        style={layout.flexRow}
      >
        <Text className={c2xStyles.moreText}>{texts.showMoreAndAdd}</Text>
        <Text type="icon" className={c2xStyles.moreIcon} />
      </Touchable>
    </XBoxShadow>
  );
});

const MoreItemNew: React.FC<IMoreItem> = memo(
  ({ isFixed, onPress, isScrollEnd }: IMoreItem) => {
    return (
      <Touchable
        debounce={true}
        onPress={onPress}
        testID={UITestID.car_testid_page_booking_form_recommend_driver_more}
        className={
          isFixed ? c2xStyles.moreWrapperNew : c2xStyles.followMoreWrapper
        }
        style={layout.flexRow}
      >
        {isFixed && !isScrollEnd && (
          <Image
            source={{
              uri: `${ImageUrl.DIMG04_PATH}1tg3712000lccy54z19ED.png`,
            }}
            resizeMode="contain"
            className={c2xStyles.shadowContainer}
          />
        )}
        <Text className={c2xStyles.moreTextNew}>更多</Text>
        <Text type="icon" className={c2xStyles.moreIconNew}>
          {icon.arrowRight}
        </Text>
      </Touchable>
    );
  },
);

export interface RecommendedDriversProps {
  passenger: Passenger;
  passengers: Passenger[];
  onPressPassenger?: (data: Passenger) => void;
  onPressMore?: () => void;
}
const RecommendedDrivers: (
  props: RecommendedDriversProps,
) => React.ReactElement = ({
  passengers,
  passenger,
  onPressPassenger,
  onPressMore,
}) => {
  const scroll = useRef(null);
  useEffect(() => {
    scroll?.current?.scrollTo({ x: 0, y: 0 });
  }, [passengers]);

  const isLazyLoad = useLazyLoad(10);

  const [isScrollEnd, setIsScrollEnd] = useState(false);

  const renderPassengers = useMemo(() => {
    return isLazyLoad ? passengers : passengers.slice(0, 3);
  }, [passengers, isLazyLoad]);

  const onScroll = useCallback(
    event => {
      const { contentOffset, contentSize, layoutMeasurement } =
        event.nativeEvent;
      const isEnd =
        contentOffset.x + layoutMeasurement.width >= contentSize.width;
      if (isScrollEnd !== isEnd) {
        setIsScrollEnd(isEnd);
      }
    },
    [isScrollEnd, setIsScrollEnd],
  );

  if (!passengers?.length) return null;
  const isMoreItemFixed = passengers?.length > 3;
  const MoreItemWrap = GetABCache.isISDShelves3() ? MoreItemNew : MoreItem;
  return (
    <View className={c2xStyles.wrapper}>
      <View
        style={xMergeStyles([
          layout.flexRow,
          styles.inner,
          isMoreItemFixed && styles.fixInner,
        ])}
      >
        <View style={xMergeStyles([layout.flexRow, styles.withShadow])}>
          <AutoScrollView
            forwordRef={scroll}
            maxWidth={
              isMoreItemFixed
                ? vw(100) - getPixel(163) - getPixel(32)
                : vw(100) - getPixel(32)
            }
            onScroll={onScroll}
          >
            <View className={c2xStyles.scrollContentWrap}>
              {renderPassengers.map(item => (
                <DriverItem
                  key={item?.passengerId}
                  title={item?.fullName}
                  isSelected={passenger?.passengerId === item?.passengerId}
                  onPress={onPressPassenger}
                  item={item}
                />
              ))}
              {!isMoreItemFixed && (
                <MoreItemWrap onPress={onPressMore} isFixed={isMoreItemFixed} />
              )}
            </View>
          </AutoScrollView>
          {isMoreItemFixed && (
            <MoreItemWrap
              onPress={onPressMore}
              isFixed={isMoreItemFixed}
              isScrollEnd={isScrollEnd}
            />
          )}
        </View>
      </View>
    </View>
  );
};

export default RecommendedDrivers;
