@import '../../../Common/src/Tokens/tokens/color.scss';

.contentWrap {
  background-color: $white;
  padding-top: 22px;
  padding-bottom: 22px;
  padding-left: 22px;
  padding-right: 22px;
  border-radius: 12px;
}
.topWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.userIconWrap {
  width: 40px;
  height: 40px;
  border-radius: 25px;
  justify-content: center;
  align-items: center;
}
.userIcon {
  color: $white;
  font-size: 28px;
  line-height: 38px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-top: -2px;
}
.ml10 {
  margin-left: 10px;
}
.blueTxt {
  color: $blueBase;
  font-size: 26px;
  line-height: 36px;
  font-family: PingFangSC-Regular;
}
.ComContainer {
  background-color: $white;
  border-radius: 16px;
  overflow: hidden;
}
.titlePic {
  margin-top: -7px;
  margin-left: 32px;
  margin-right: 16px;
}
.titleTxt {
  font-size: 26px;
  color: $cityLabelText2;
  margin-top: -1px;
  line-height: 36px;
  font-family: PingFangSC-Regular;
}
.borderWrap {
  margin-top: -7px;
  padding-left: 12px;
  padding-right: 12px;
  padding-bottom: 12px;
}
.flexRowBetween {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.conHeadWrap {
  margin-bottom: 26px;
}
.conHeadTitTxt {
  font-size: 30px;
  color: $fontPrimary;
  font-weight: bold;
}
.linkTxtWithArrow {
  font-size: 26px;
  color: $blueBase;
}
.iconTxt {
  padding-top: 4px;
  font-size: 22px;
}
.itemRight {
  margin-left: 48px;
}
.itemWrap {
  padding-bottom: 26px;
}
.lineWrap {
  width: 4px;
  position: absolute;
  height: 165%;
  left: -32px;
  top: 10px;
}
.circle {
  width: 12px;
  height: 12px;
  border-radius: 12px;
  left: -36px;
  top: 10px;
  background-color: $fontSubLight;
}
.absIcon {
  position: absolute;
}
.overGifWrap {
  top: 2px;
  left: -48px;
  background-color: $easylifeBg3;
  flex: 1;
  border-radius: 36px;
  overflow: hidden;
}
.overIconWrap {
  top: 13px;
  left: -42px;
  background-color: $easylifeBg3;
}
.overIcon {
  width: 24px;
  height: 24px;
  font-size: 24px;
  color: $successGreen;
  margin-top: -6px;
}
.gray {
  color: $fontGrayBlue;
}
.linkTxt {
  font-size: 24px;
  color: $blueBase;
}
