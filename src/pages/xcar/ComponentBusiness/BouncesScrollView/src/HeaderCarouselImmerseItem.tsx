import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import VideoPlayerView from '@c2x/components/VideoPlayerView';
import PhotoBrowser from '@c2x/apis/PhotoBrowser';
import React from 'react';
import {
  xGetNetworkType,
  XImageBackground as ImageBackground,
  XView as View,
  xRouter,
  xMergeStyles,
} from '@ctrip/xtaro';

import memoizeOne from 'memoize-one';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { SizeablePureComponent } from '@ctrip/rn_com_car/dist/src/Components/Basic/SizeableComponent';
import c2xStyles from './headerCarouselImmerseItemC2xStyles.module.scss';
import { AppContext, CarLog } from '../../../Util/Index';
import { defaultVideoPlayImage } from '../../CarImage';
import PlayProgress from './PlayProgress';
import TimeProgress from './TimeProgress';
import LottieAnimation from '../../LottieAnimation/index';
import {
  ImageUrl,
  CommonEnums,
  UITestID,
  LogKey,
} from '../../../Constants/Index';
import {
  getServerRequestId,
  getListRequestId,
} from '../../../Global/Cache/ListReqAndResData';
import BouncesScrollContanst from './Constants';
import { MediaType } from '../../../Types/Dto/QueryVehicleDetailListResponseType';
import {
  IHeaderCarouselImmerseItemProps,
  IHeaderCarouselImmerseItemState,
} from './Types';

const { CAROUSEL_HEIGHT } = BouncesScrollContanst;
const { vw, getPixel, fixOffsetTop, getProcImageUrl, ProcImageParamsType } =
  BbkUtils;
const ImageHeight = (getPixel(104) - fixOffsetTop(0)) / 2;
const styles = StyleSheet.create({
  slide: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    backgroundColor: color.white,
  },
  slideSmall: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    backgroundColor: color.white,
    paddingTop: ImageHeight + fixOffsetTop(0),
    paddingBottom: ImageHeight,
    paddingLeft: getPixel(78),
    paddingRight: getPixel(78),
  },
  video: {
    flex: 1,
  },
  image: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  vrImage: {
    width: getPixel(197),
    height: getPixel(80),
    left: getPixel(-6),
  },
  coverImage: {
    height: CAROUSEL_HEIGHT,
    position: 'absolute',
    top: 0,
    left: 0,
  },
  playProgress: {
    position: 'absolute',
    bottom: 0,
    backgroundColor: color.R_0_0_0_0_3,
  },
  progressStyle: {
    backgroundColor: color.C_006ff6,
  },
});

const defaultCarImage = `${ImageUrl.DIMG04_PATH}1tg1z12000hz7yycw7F82.png`;

const getImageUrl = memoizeOne((type, url, cover) => {
  // 图片展示时，进行裁剪， VR&视屏&图片裁剪的图片字段不一致
  if (type === MediaType.VR || type === MediaType.Video) {
    return getProcImageUrl(cover, ProcImageParamsType.productTop);
  }
  return getProcImageUrl(url, ProcImageParamsType.productTop);
});

export default class HeaderCarouselImmerseItem extends SizeablePureComponent<
  IHeaderCarouselImmerseItemProps,
  IHeaderCarouselImmerseItemState
> {
  video;

  bouncesMoreRef;

  playProgressRef;

  timeProgressRef;

  currentTime = 0;

  progressTime = 0;

  currentVideoPlayerWindowMode;

  videoConfig = {
    bizType: 'carrental',
    cacheType: 1,
    isNotLooping: false, // 是否循环播放
    isShowWifiTipsEveryTime: true, // 是否每次无wifi时提示 (若为true，则每个播放器实例无wifi时都会有一次提示)
    controlStyle: 0, // 控制栏样式 （0为带时间轴）
    controlStyleInEmbed: 1, // 小屏幕时控制栏样式
    scalingModeInEmbed: 1,
    isOffsetStatusBarInFullScreenAndroid: true,
    isHideMuteBtnInEmbed: true,
    coverImageMode: 0,
    isCustomMute: true,
    isMute: true,
    noUnifiedMute: true, // 音量键是否走统一控制
  };

  constructor(props) {
    super(props);
    this.state = {
      isPlay: false,
      isPause: true,
      isMute: true,
      showCustomCoverImage: true, // 公共的视频播放器里的视频封面图不会在视频没有加载好之前加载，所以会有空百态闪过，安卓上较明显
      contentWidth: vw(100),
      didMount: false,
    };
    this.play = this.play.bind(this);
    this.pause = this.pause.bind(this);
  }

  componentWillUnmount() {
    this.video?.release?.();
  }

  onWindowModeChangedEvent = data => {
    if (
      data.videoPlayerWindowMode === CommonEnums.VideoPlayerWindowMode.Embed
    ) {
      this.pause();
    }
  };

  onStateChanged = data => {
    const isPlay =
      data?.videoPlayerState === CommonEnums.VideoPlayerState.Playing;
    if (this.state.isPlay !== isPlay) {
      this.setState({
        isPlay,
      });
    }
    const isPause = CommonEnums.PauseState.includes(data?.videoPlayerState);
    if (this.state.isPause !== isPause) {
      this.setState({
        isPause,
      });
    }
  };

  onPlayProgress = data => {
    const { currentTime } = data;
    // const time = Math.floor(currentTime);
    if (currentTime !== this.progressTime) {
      this.progressTime = currentTime;
      this.playProgressRef?.updateProgress(currentTime, data?.totalTime);
      this.timeProgressRef?.updateProgress(currentTime);
    }
    this.currentTime = currentTime;
    this.props.onPlayProgressClickCallback?.(data);
  };

  onBackAndroid = () => {
    // 处理安卓物理键返回事件
    if (
      !this.currentVideoPlayerWindowMode ||
      this.currentVideoPlayerWindowMode ===
        CommonEnums.VideoPlayerWindowMode.Embed
    ) {
      this.props.pop?.();
    } else {
      this.video?.onBackPressed?.();
    }
  };

  onMediaPress = () => {
    const { media, isShowFullImmerse, index, vehicleCode, onImagePress } =
      this.props;
    const { type, url } = media;
    switch (type) {
      case MediaType.Video:
        if (!isShowFullImmerse) {
          if (this.state.isPlay) {
            this.pause();
            // 如果传了自定义图片点击事件，则使用自定义点击事件
            if (onImagePress) {
              onImagePress(media, index);
            } else {
              this.showVideoWithImages(index);
            }
          } else {
            this.play();
          }
        }
        break;
      case MediaType.VR:
        // 如果传了自定义图片点击事件，则使用自定义点击事件
        if (onImagePress) {
          onImagePress(media, index);
        } else {
          xRouter.navigateTo({ url });
          CarLog.LogCode({
            name: '点击_产品详情页_VR入口',

            info: {
              vehicleCode,
            },
          });
          CarLog.LogTrace({
            key: LogKey.cm_car_app_click_airanking,
            info: {
              name: '点击_产品详情页_VR入口',
              newMergeId: AppContext.currentNewMergeId,
              vehicleId: vehicleCode,
              serverRequestId: getServerRequestId(),
              requestId: getListRequestId(),
              vid: 
            },
          });
        }
        break;
      default:
        // 如果传了自定义图片点击事件，则使用自定义点击事件
        if (onImagePress) {
          onImagePress(media, index);
        } else {
          this.showVideoWithImages(index);
        }
        break;
    }
  };

  onVrIconPress = () => {
    const { media, vehicleCode } = this.props;
    const { url } = media;
    xRouter.navigateTo({ url });
    CarLog.LogCode({
      name: '点击_产品详情页_VR入口',

      info: {
        vehicleCode,
      },
    });
    CarLog.LogTrace({
      key: LogKey.cm_car_app_click_airanking,
      info: {
        name: '点击_产品详情页_VR入口',
        newMergeId: AppContext.currentNewMergeId,
        vehicleId: vehicleCode,
        serverRequestId: getServerRequestId(),
        requestId: getListRequestId(),
      },
    });
  };

  loadVideo = () => {
    // 不自动播放
    if (this.props.isNotAutoPlay) {
      return;
    }
    xGetNetworkType({
      success: res => {
        // 初始化视频组件完成后 进行视频播放 并吧dom节点传出;
        if (res.networkType === CommonEnums.NetworkType.WIFI) {
          // onlyImmersePlay: 设置为true时只有沉浸式大图视频才播放，小图不播放
          !this.props.onlyImmersePlay && this.play();
        }
        this.props.loadVideo?.(this.video);
      },
    });
  };

  clickMute = () => {
    const { isMute } = this.state;
    this.setState({
      isMute: !isMute,
    });
    this.handleMute(!isMute);
  };

  handleMute = muted => {
    this.video?.changeMute({ mute: muted });
  };

  showVideoWithImages = (index = 0) => {
    const { totalPhotos } = this.props;

    const photoList = [];
    totalPhotos?.forEach(media => {
      const {
        url,
        cover,
        type,
        groupName,
        groupId,
        itemIdInGroup,
        itemCountInGroup,
      } = media;
      const photo: any = {
        imageTitle: groupName,
        imageType: 0,
        groupId,
        itemIdInGroup,
        itemCountInGroup,
        imageUrl:
          type === MediaType.Video || type === MediaType.VR ? cover : url,
      };
      if (type === MediaType.Video) {
        photo.imageType = 1;
        photo.videoPlayerModel = {
          videoUrl: url,
          coverImageUrl: cover,
          isNotLooping: false,
          isShowWifiTipsEveryTime: 0,
          bizType: 'carrental',
          seekTime: this.currentTime,
          describeText: groupName,
          pageIndex: itemIdInGroup,
          pageCount: itemCountInGroup,
        };
      }
      photoList.push(photo);
    });
    const meta = {
      businessCode: 'carrental',
      pageId: AppContext.PageInstance.getPageId(),
    };
    PhotoBrowser.showWithScrollCallback(photoList, [], index, meta, () => {});
  };

  play() {
    // 视频播放事件
    if (!this.state.isPlay) {
      this.video?.play();
      if (this.state.showCustomCoverImage) {
        this.setState({
          showCustomCoverImage: false,
        });
      }
    }
  }

  pause() {
    if (this.state.isPlay) {
      this.video?.pause();
    }
  }

  onWindowSizeChanged() {
    this.setState({
      contentWidth: vw(100),
    });
  }

  componentDidMount() {
    // 绕过国内详情页DidUpdate检测
    this.setState({
      didMount: true,
    });
  }

  render() {
    const {
      media,
      carouselHeight,
      onlyImmersePlay,
      isShowMute,
      isShowTimer,
      isShowProgress = true,
      style,
      index,
    } = this.props;
    const { isMute, contentWidth, didMount } = this.state;
    if (!media) {
      return null;
    }
    const { type, url, cover, isEqualListImage } = media;
    const videoConfig = {
      ...this.videoConfig,
      videoUrl: url,
      coverImageUrl: getImageUrl(type, url, cover),
    };
    const isVr = type === MediaType.VR;
    return (
      <View style={style}>
        {/** 视屏 */}
        {type === MediaType.Video && !!url && (
          <Touchable
            key={-1}
            className={c2xStyles.slide}
            style={{ height: carouselHeight }}
            activeOpacity={1}
            testID={`${UITestID.car_testid_multi_media_item}_${index}`}
            onPress={this.onMediaPress}
          >
            <Touchable
              className={c2xStyles.slideShade}
              activeOpacity={1}
              onPress={this.onMediaPress}
              testID={`${UITestID.car_testid_multi_media_item_bg_button}_${index}`}
            />

            {!!url && didMount && (
              <View
                style={xMergeStyles([
                  { width: contentWidth, height: CAROUSEL_HEIGHT },
                  this.props?.videoStyle,
                ])}
              >
                <VideoPlayerView
                  // @ts-ignore 升级072
                  onLayout={this.loadVideo}
                  testID={`${UITestID.car_testid_multi_media_item_video}_${index}`}
                  style={styles.video}
                  crnParamData={videoConfig}
                  ref={view => (this.video = view)}
                  onWindowModeChangedEvent={this.onWindowModeChangedEvent}
                  onVideoPlayerStateChangedEvent={this.onStateChanged}
                  onMuteButtonClickCallback={e => {
                    this.handleMute?.(e.muted);
                  }}
                  onPlayProgressClickCallback={this.onPlayProgress}
                />

                {url && isShowProgress && (
                  <PlayProgress
                    // @ts-ignore
                    ref={ref => (this.playProgressRef = ref)}
                    style={styles.playProgress}
                    progressStyle={styles.progressStyle}
                  />
                )}
              </View>
            )}
            {!!url && isShowMute && (
              <Touchable
                testID={`${UITestID.car_testid_multi_media_item_mute}_${index}`}
                className={c2xStyles.muteWrap}
                onPress={this.clickMute}
              >
                <Image
                  className={c2xStyles.muteImage}
                  src={
                    isMute
                      ? `${ImageUrl.DIMG04_PATH}1tg1b12000cj4a8cx615C.png`
                      : `${ImageUrl.DIMG04_PATH}1tg3t12000cj4aid24223.png`
                  }
                />
              </Touchable>
            )}
            {!!url && isShowTimer && (
              <TimeProgress
                // @ts-ignore
                ref={ref => (this.timeProgressRef = ref)}
              />
            )}
            {this.state.isPause && !onlyImmersePlay && (
              <Touchable
                className={c2xStyles.videoPlay}
                onPress={this.play}
                testID={`${UITestID.car_testid_multi_media_item_play}_${index}`}
                activeOpacity={1}
              >
                <Image
                  className={c2xStyles.videoPlayImage}
                  defaultSource={{ uri: defaultVideoPlayImage }}
                  src={defaultVideoPlayImage}
                />
              </Touchable>
            )}
            {this.state.showCustomCoverImage && (
              <Image
                style={xMergeStyles([
                  styles.coverImage,
                  { width: contentWidth },
                  this.props?.videoStyle,
                ])}
                resizeMode="cover"
                src={getImageUrl(type, url, cover)}
              />
            )}
          </Touchable>
        )}
        {/** 图片 */}
        {type !== MediaType.Video && (
          <Touchable
            style={xMergeStyles([
              { height: carouselHeight },
              isEqualListImage ? styles.slideSmall : styles.slide,
            ])}
            activeOpacity={1}
            onPress={this.onMediaPress}
            testID={`${UITestID.car_testid_multi_media_item_picture}_${index}`}
          >
            <ImageBackground
              // @ts-ignore
              defaultSource={{ uri: defaultCarImage }}
              source={{ uri: getImageUrl(type, url, cover) }}
              style={xMergeStyles([
                styles.image,
                isVr ? this.props?.vrStyle : this.props?.imageStyle,
              ])}
              imageStyle={isVr ? this.props?.vrStyle : this.props?.imageStyle}
              resizeMode="cover"
            >
              {isVr && (
                <Touchable
                  onPress={this.onVrIconPress}
                  testID={`${UITestID.car_testid_multi_media_item_vr}_${index}`}
                >
                  <LottieAnimation
                    jsonUrl={`${ImageUrl.FILE_PATH}6/carpic_no_mark/1tg5g12000cjb5lo805D3.json`}
                    style={styles.vrImage}
                  />
                </Touchable>
              )}
            </ImageBackground>
          </Touchable>
        )}
      </View>
    );
  }
}
