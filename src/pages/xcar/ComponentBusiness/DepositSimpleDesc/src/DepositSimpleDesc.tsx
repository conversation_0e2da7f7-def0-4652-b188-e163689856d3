import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import {
  XView as View,
  xClassNames as classNames,
  XImage as Image,
} from '@ctrip/xtaro';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './DepositSimpleDescC2xStyles.module.scss';
import { ImageUrl } from '../../../Constants/Index';

interface IDepositSimpleDesc {
  depositSimpleDescs?: any;
  depositInfo?: any;
  showExtra?: boolean;
  showEhiFreeDepositModalVisible?: () => void;
  isShowCredit: boolean;
  noBg?: boolean;
  style?: any;
}
const DepositSimpleDesc: React.FC<IDepositSimpleDesc> = memo(
  ({
    depositSimpleDescs,
    depositInfo,
    showExtra,
    showEhiFreeDepositModalVisible,
    isShowCredit,
    noBg,
    style,
  }: IDepositSimpleDesc) => {
    return (
      <View className={c2xStyles.dptWrap} style={style}>
        {depositSimpleDescs?.map?.((item, index) => (
          <View className={classNames(c2xStyles.dptRow)} key={item.title}>
            <View
              className={classNames(
                c2xStyles.dptLeft,
                index === 0 ? c2xStyles.top8 : c2xStyles.top4,
              )}
            >
              <BbkText className={classNames(c2xStyles.title)}>
                {item.title}
              </BbkText>
              <Image
                src={
                  index === 0
                    ? `${ImageUrl.DIMG04_PATH}1tg4e12000ky839xnFE12.png`
                    : `${ImageUrl.DIMG04_PATH}1tg6t12000ky83jay10CB.png`
                }
                className={classNames(c2xStyles.dotBg)}
              />
            </View>
            <View
              className={classNames(
                c2xStyles.dptRight,
                index === 0 ? c2xStyles.dptPd : c2xStyles.bottom8,
              )}
            >
              {item?.descs?.map(desc => {
                if (desc?.length) {
                  return (
                    <BbkText
                      key={desc}
                      className={classNames(
                        index === 0 && c2xStyles.dptTopTxt,
                        index === 1 && c2xStyles.dptBtTxt,
                        c2xStyles.itemTxt,
                      )}
                    >
                      {desc}
                    </BbkText>
                  );
                }
              })}
              {!!item?.notice && (
                <BbkTouchable
                  className={classNames(c2xStyles.richTxt)}
                  debounce={true}
                  onPress={showEhiFreeDepositModalVisible}
                >
                  <BbkText className={c2xStyles.itemTxt}>{item.notice}</BbkText>
                  <BbkText
                    type="icon"
                    className={classNames(c2xStyles.rightIcon, c2xStyles.c11)}
                  >
                    {icon.arrowRight}
                  </BbkText>
                </BbkTouchable>
              )}
            </View>
          </View>
        ))}
        {!noBg && (
          <Image
            className={c2xStyles.dptBg}
            src={
              isShowCredit
                ? `${ImageUrl.DIMG04_PATH}1tg1512000ky8t5ad90BA.png`
                : `${ImageUrl.DIMG04_PATH}1tg5s12000ky8t0x18A46.png`
            }
          />
        )}
      </View>
    );
  },
);

export default DepositSimpleDesc;
