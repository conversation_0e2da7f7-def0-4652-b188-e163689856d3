@import '../../../Common/src/Tokens/tokens/color.scss';

.dptWrap {
  padding: 25px 8px 25px 8px;
  border-radius: 8px;
  background: $C_f5f6fa;
  position: relative;
  // margin-top: 24px;
}
.dptRow {
  display: flex;
  flex-direction: row;
  width: 100%;
  z-index: 2;
}
.dptPd {
  padding-bottom: 30px;
  padding-top: 8px;
}
.dptLeft {
  height: 42px;
  width: 104px;
  background: $white;
  border: 1px solid $C_F1F1F1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 21px;
  margin-right: 12px;
  position: relative;
  z-index: 100;
}
.dptRight {
  border-left: 2px solid $C_888888;
  padding-left: 16px;
  position: relative;
  display: flex;
  flex:1;
  //align-items: center;
  justify-content: center;
  //flex-wrap: wrap;
}
.dptBtTxt {
  top: -6px;
}
.dptTopTxt {
  top: -2px;
}
.bottom8 {
  padding-bottom: 2px ;
}

.top8{
  position: relative;
  top: 8px;
}
.top4 {
  position: relative;
  top: -4px;
}
.dotBg {
  width: 24px;
  height: 23px;
  position: absolute;
  left:104px;
}
.richTxt {
  display: flex;
  flex-direction: row;
  align-items: center;
  top: 4px;
}
.c11 {
  color: $C_111111
}
.title {
  font-size: 24px;
  color: $C_111111;
  flex-wrap: wrap;
  position: relative;
  line-height: 40px;
}
.itemTxt {
  font-size: 26px;
  color: $C_111111;
  flex-wrap: wrap;
  position: relative;
  line-height: 45px;
}
.dptBg {
  width: 70px;
  height: 88px;
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 1;
}