import { get as lodashGet } from 'lodash-es';

import uuid from 'uuid';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Event from '@c2x/apis/Event';
import initPageInstance from './PageInstance';
// import { EventName } from '../Constants/Index';

export interface MarketInfoType {
  channelId: string;
  childChannelId: string;
  sId: string;
  aId: string;
  visitortraceId: string;
  awakeTime: string;
  vid: string;
  isLoadFinished?: boolean;
  sourceId?: string;
  ouid?: string;
}

export interface CacheType {}

export interface UserInfoType {
  UserID?: string;
  userId?: string;
  Auth?: string;
  mobile?: string;
}

export interface CarEnvType {
  buildTime?: string;
  appType: string;
  env?: string;
  testid?: string;
  mock?: string;
}

export interface UrlQueryType {
  payStatus?: string;
  age?: string;
  apptype?: any;
  data?: any;
  landingto?: string;
  fromurl?: string;
  st?: string;
  orderId?: string;
  listParameter?: any;
  CRNModuleName?: string;
  backPageName?: string;
  location?: string;
  // 已支持的筛选项
  filters?: Array<string>;
  vehgroupid?: string;
  env?: string;
  testid?: string;
  mock?: string;
  initialPage?: string;
  isShowSearchConditionTip?: string;
  snapshotOid?: string;
  // eslint-disable-next-line camelcase
  cw_env?: string;
  channelid?: string;
  orderScrollToByName?: string;
  fromPage?: string;
  cityselectorlocation?: string;
  comeFrom?: string;
  insuranceOrderId?: string;
  renewalOrderId?: string;
  status?: string;
  token?: string;
  isAddIns?: number;
  vehicleid?: string;
  ParentDistibutionChannelId?: string;
  // 修改订单-取消重订参数
  ctripOrderId?: string;
  vendorId?: string;
  vehicleId?: string;
  ctripVehicleCode?: string;
  storeCode?: string;
  passenger?: string;
  homeSearchFormRefEvent?: string; // 首页和列表页筛选项联动
  isHomeCombine?: string; // 标识是否从融合首页跳转来的
  directOpen?: string;
  directOpenSub?: string;
  selectedId?: string;
  pageChannel?: string; // ctrip_home_page 平台首页mcd中配置数据
  abVersion?: string; // a/b/c/d/e 平台首页mcd中配置数据
  showBtn?: boolean; // 特权页是否展示去租车按钮
  currentDailyPrice?: string; // 榜单日均价带入列表页比较价格
  uniqSign?: string; // 榜单服务端缓存带入列表页
  orignScenes?: string; // 列表请求来源场景 默认0  1-榜单
  rankingStatusCode?: string; // 榜单是否命中缓存
  topProductsVersion?: string; // 标识服务端榜单版本
  pcid?: number; // 服务端跳转的取车城市id
  homeTabAbVersion?: string; // 融合首页tab拆分ab实验信息（key|val）
  recommendPrice?: string;
  promotionId?: string;
  filterPoi?: FilterPoiType;
  license?: string; // 榜单跳转带入牌照信息
  klbVersion?: string; // 卡拉比3期新增参数，用于首页订单卡片跳转Guide
  scene?: string; // 卡拉比3期新增参数，场景值，用于标识从哪里打开的Guide
  pageUniqueId?: string; // 跳转产品详情页存储storage的标识
  skuId?: string; // 营销跳转变价埋点新增字段
  vehicleDayPrice?: string; // 营销跳转变价埋点新增字段
  licenseTitle?: string; // 营业执照页面标题
  licenseData?: string; // 营业执照图片和描述信息
  pageId?: string;
  isSelfService?: string; // 是否是自助取还的在线认证页，传'true'代表是
  from?: string; // 页面跳转来源
  link?: string; // 跳转URL
  nonJumpFlow?: string; // 无忧租置顶标志
  miniprogramType?: string; // 分享的小程序类型 0:正式版 1:开发版 2:体验版
  osdOriginOrderId?: string; // 境外修改订单原单orderId
  depth?: string; // 记录两车交叉组件深度
  traceId?: string; // 记录两车交叉组件traceId
  categoryCode?: string; // 记录两车交叉组件categoryCode
  categoryCodes?: string; // 记录两车交叉组件categoryCodes
  debug?: string;
}

/* eslint-disable @typescript-eslint/naming-convention */
interface HomeTabAbInfoType {
  key: string;
  val: string;
}

export interface ABTestingType {
  trace: string;
  datas: any;
  newDetail?: boolean;
  values: {
    newOrderDetail?: boolean;
    newOsdOrderDetailVersion?: string;
    newIsdOrderDetailVersion?: string;
  };
  listPageIsEffect: boolean;
  homeTabInfo?: HomeTabAbInfoType;
}

export interface LanguageInfoType {
  language: string;
  locale: string;
  site: string;
  currency: string;
  standardLocale?: string;
}

export interface UserTraceType {
  queryVid?: string;
  queryRecommendId?: string;
  orderId?: number;
  uniqueOrderId?: number;
  queryRecommendCount?: number;
  queryRecommendButtonClicked?: boolean;
  cachedLocationAndDate?: string;
  cachedDriverAgeAndNumber?: string;
  serverRequestId?: string; // 服务端列表页查询唯一标记
  latestTime?: number; // 最后更新时间
}

export interface UserFetchCacheIdType {
  queryListCacheId?: string;
  actionType?: string;
}

export interface RouterLoaderList {
  groupCode?: string;
  filters?: Array<string>;
  vehicleId?: string;
  isDispatch?: boolean;
  currentDailyPrice?: string;
  uniqSign?: string;
  license?: string;
  skuId?: string;
  vehicleDayPrice?: string;
}

export interface RouterLoaderType {
  List?: RouterLoaderList;
}

export interface CallCityAreaPageInfoType {
  pageName?: string;
}

export interface ListCacheIntervalType {
  homeReady: Date;
  listCacheBuild: Date;
}

export interface InsuranceRulesType {
  insuranceRequestId: string;
  insuranceSelectedIds: Array<string>;
}

export interface LocationDistanceType {
  oneKm: number;
  pWalkDistance: number;
  rWalkDistance: number;
  pWalkInfo: string;
  pDriveInfo: string;
  rWalkInfo: string;
  rDriveInfo: string;
}

export interface BookingType {
  traceCode: string;
}

export interface FlutterType {
  routers: Array<string>;
}

export enum FilterPoiType {
  Default = '0', // 0 或不传，不屏蔽
  ISD = '1', // 屏蔽国内
  OSD = '2', // 屏蔽境外
}

export interface IVehicleLogInfo {
  batchCode: string;
  vehicleKey: string;
  bigGroupId: number; // 大聚合ID，车型卡片中报价第一的车型ID
  bigGroupRank: number; // 大聚合车型ID列表页排序
  smallGroupId: number; // 小聚合ID，报价合并中报价第一个的车型ID
  smallGroupRank: number; // 车型卡片中报价对应的排序位置
  isSelectedVendorTop1: boolean; // 卡片中报价中优选车型是否是排第一个
  quoteRank: number; // 报价列表中报价排序位置
  filterInfo: string; // 列表页筛选条件
}

export interface AppContextType {
  ABTesting?: ABTestingType;
  MarketInfo?: MarketInfoType;
  Cache?: CacheType;
  CarEnv?: CarEnvType;
  LanguageInfo?: LanguageInfoType;
  UserInfo?: UserInfoType;
  UrlQuery?: UrlQueryType;
  OriginUrlQuery?: UrlQueryType;
  ContentUrlQuery?: UrlQueryType;
  Url?: string;
  PageInstance?: any;
  UserTrace?: UserTraceType;
  UserFetchCacheId?: UserFetchCacheIdType;
  PreFetchCache?: any;
  RouterLoader?: RouterLoaderType;
  env?: string;
  testid?: string;
  mock?: string;
  ListCacheInterval: ListCacheIntervalType;
  ISDIMurl: string;
  preLoadFrom: Array<'native' | 'js'>;
  CallCityAreaPageInfo?: CallCityAreaPageInfoType;
  HomeSearchFormRef?: any;
  LocationDistance: LocationDistanceType;
  orderScrollToByName?: string;
  InsuranceRules?: InsuranceRulesType;
  Booking?: BookingType;
  hasLogBadTime?: boolean;
  encryptUid?: string;
  fromType?: string;
  originOrderId?: string; // 修改订单的携程订单号
  modifyVendorOrderCode?: string; // 修改订单的供应商订单号
  originVendorId?: number; // 修改订单的原单供应商
  originalCouponCode?: string; // 修改订单的原单优惠券
  originalActivityId?: string; // 修改订单的原单活动id
  originalActivityName?: string; // 修改订单的原单活动名称
  channelId?: string;
  eid?: string;
  isModifyOrderRebook?: boolean;
  fatEnv?: string;
  Flutter?: FlutterType;
  driveTime?: number; // 降级逻辑-百度地图返回的驾车时间
  isHomeCombine?: boolean; // 标识是否从融合首页跳转来的
  isHomeCombineEventSwitch?: boolean; // 标识是否是融合首页向自定义导航头页面发送同步事件
  isListCombineEventSwitch?: boolean; // 标识是否是融合首页跳转的列表页向融合首页发送同步事件
  hasUpdatePickupOnDoorCode?: boolean;
  hasPreLoadCommentList?: boolean;
  queryProductsRequestId?: string; // 记录queryProducts请求的requestId，供榜单缓存查询
  hasUseHomeTabLocationData?: boolean; // 记录在初始化LocationAndDate state时，是否使用过storage中的值
  platHomeModuleInfo?: string; // 融合首页tabid相关信息
  filterPoi?: FilterPoiType;
  klbVersion?: number; // URL传参数是否是卡拉比数据
  orignScenes?: number; // URL传参数的列表请求来源场景
  isMarketing?: number; // URL传参数的列表请求是否是营销跳转
  selfServiceSwitch?: string; // 自助取还开关
  easyLifeSwitch?: string; // 无忧租2024开关
  traceSwitch?: boolean; // 是否开启了Trace开关
  countriesInfo?: any; // 国家信息列表
  enablePush?: boolean; // 页面是否可以push
  isReachedBookingPage?: boolean; // 是否到达过填写页
  vehicleLogInfo: IVehicleLogInfo; // 列表页点击车型时的埋点信息
  goodsShelvesTwoSwitch: string; // 货架二期开关
  listRequestId?: string; // 列表页第一页数据请求的requestId
  searchSuggestSwitch?: string; // 车型选择优化1期开关
  goodsShelvesTwoABVersion?: string; // 货架二期AB版本
}

const baseContext: AppContextType = {
  ABTesting: {
    trace: '',
    datas: {},
    values: {
      newOrderDetail: false,
      newOsdOrderDetailVersion: '',
      newIsdOrderDetailVersion: '',
    },
    // 新版列表全部跳转新版详情页
    newDetail: true,
    listPageIsEffect: true,
    // 两车tab拆分实验
    homeTabInfo: {
      key: '',
      val: '',
    },
  },
  MarketInfo: {
    channelId: '',
    childChannelId: '',
    sId: '',
    aId: '',
    visitortraceId: '',
    awakeTime: '',
    vid: '',
  },
  Cache: {},
  CarEnv: { buildTime: '', appType: '' },
  LanguageInfo: {
    language: 'cn',
    locale: 'zh_cn',
    site: 'zh',
    currency: 'CNY',
  },
  UserInfo: {},
  UrlQuery: {},
  OriginUrlQuery: {}, // 对于融合首页版本，代表的是在拼接MCD配置地址前的跳转参数
  ContentUrlQuery: {}, // 对于融合首页版本，代表的是MCD配置contenturl中的跳转参数
  Url: '',
  PageInstance: initPageInstance,
  UserTrace: { queryVid: uuid(), latestTime: 0, queryRecommendCount: 0 },
  ListCacheInterval: { homeReady: new Date(), listCacheBuild: new Date() },
  UserFetchCacheId: { queryListCacheId: '' },
  PreFetchCache: {},
  RouterLoader: { List: null },
  ISDIMurl: '',
  CallCityAreaPageInfo: {
    pageName: '',
  },
  HomeSearchFormRef: null,
  LocationDistance: {
    oneKm: 1000,
    pWalkDistance: 0,
    rWalkDistance: 0,
    pWalkInfo: '',
    pDriveInfo: '',
    rWalkInfo: '',
    rDriveInfo: '',
  },
  InsuranceRules: {
    insuranceRequestId: '',
    insuranceSelectedIds: [],
  },
  Booking: {
    traceCode: '',
  },
  hasLogBadTime: false,
  encryptUid: '',
  fromType: '',
  originOrderId: '',
  modifyVendorOrderCode: '',
  channelId: '',
  eid: '',
  isModifyOrderRebook: false,
  isHomeCombine: false,
  isHomeCombineEventSwitch: false,
  isListCombineEventSwitch: false,
  Flutter: {
    routers: [],
  },
  driveTime: 0,
  hasUpdatePickupOnDoorCode: false,
  hasPreLoadCommentList: false,
  queryProductsRequestId: '',
  hasUseHomeTabLocationData: false,
  platHomeModuleInfo: '',
  filterPoi: FilterPoiType.Default,
  klbVersion: null,
  orignScenes: null,
  isMarketing: null,
  preLoadFrom: [],
  selfServiceSwitch: '',
  traceSwitch: false,
  countriesInfo: [], // 国家ID信息列表
  isReachedBookingPage: false, // 是否到达过填写页
  enablePush: true, // 初始值为true
  vehicleLogInfo: {
    batchCode: '',
    vehicleKey: '',
    bigGroupId: null,
    bigGroupRank: null,
    smallGroupId: null,
    smallGroupRank: null,
    isSelectedVendorTop1: null,
    quoteRank: null,
    filterInfo: '',
  },
  goodsShelvesTwoSwitch: '0',
};

const getAppContext = (): AppContextType => BbkUtils.cloneDeep(baseContext);

let appContext = getAppContext();

const setMarketInfo = (market: MarketInfoType) => {
  appContext.MarketInfo = market;
};

const setABTesting = (value: ABTestingType) => {
  const datas = { ...appContext.ABTesting.datas, ...value };

  const abTraceStr = [];
  Object.keys(datas).forEach(m => {
    if (datas[m]?.ExpVersion) {
      abTraceStr.push(`${datas[m].ExpCode}|${datas[m].ExpVersion}`);
    }
  });

  appContext.ABTesting = {
    ...appContext.ABTesting,
    datas,
    trace: abTraceStr.join(','),
  };
};

const setABNewDetail = (value: boolean) => {
  appContext.ABTesting.newDetail = value;
};

const setUserInfo = (userInfo: any) => {
  const $userInfo = userInfo || {};
  if ($userInfo.data && $userInfo.data.UserId && !$userInfo.data.UserID) {
    // android：uInfo.UserId，ios：uInfo.UserID
    $userInfo.data.UserID = $userInfo.data.UserId;
  }
  appContext.UserInfo = $userInfo.data;
};

const getUserId = () => {
  return appContext?.UserInfo?.UserID;
};

const setUrl = (url: string) => {
  appContext.Url = url;
};
const setCountriesInfo = countriesInfo => {
  appContext.countriesInfo = countriesInfo;
};
const setUrlQuery = (
  urlQuery: UrlQueryType,
  key: string = null,
  value: any = null,
) => {
  appContext.UrlQuery = urlQuery || {};
  if (key) {
    appContext.UrlQuery[key] = value;
  }
};

const clearUrlQuery = (key: string) => {
  appContext.UrlQuery[key] = undefined;
};

const setOriginUrlQuery = (urlQuery: UrlQueryType) => {
  appContext.OriginUrlQuery = urlQuery || {};
};

const setContentUrlQuery = (urlQuery: UrlQueryType) => {
  appContext.ContentUrlQuery = urlQuery || {};
};

const setLanguageInfo = (language: LanguageInfoType) => {
  appContext.LanguageInfo = language;
};

const resetAppContext = () => {
  appContext = getAppContext();
};

const setPageInstance = pageInstance => {
  appContext.PageInstance = pageInstance;
};

const setLanguageCurrency = currency => {
  appContext.LanguageInfo.currency = currency;
};

const setInsuranceRules = (insuranceSelectedIds: Array<string>) => {
  appContext.InsuranceRules.insuranceRequestId = uuid();
  appContext.InsuranceRules.insuranceSelectedIds = insuranceSelectedIds || [];
};

const setEncryptUid = encryptUid => {
  appContext.encryptUid = encryptUid;
};

const setFromType = fromType => {
  appContext.fromType = fromType;
};

const setOriginOrderId = originOrderId => {
  appContext.originOrderId = originOrderId;
};

const setModifyVendorOrderCode = modifyVendorOrderCode => {
  appContext.modifyVendorOrderCode = modifyVendorOrderCode;
};

const setOriginVendorId = originVendorId => {
  appContext.originVendorId = originVendorId;
};

const setOriginalCouponCode = originalCouponCode => {
  appContext.originalCouponCode = originalCouponCode;
};

const setOriginalActivityId = originalActivityId => {
  appContext.originalActivityId = originalActivityId;
};

const setOriginalActivityName = originalActivityName => {
  appContext.originalActivityName = originalActivityName;
};

const setChannelId = channelId => {
  appContext.channelId = channelId;
};

const setEid = eid => {
  appContext.eid = eid;
};

const setIsModifyOrderRebook = isModifyOrderRebook => {
  appContext.isModifyOrderRebook = isModifyOrderRebook;
};

const setIsHomeCombine = isHomeCombine => {
  appContext.isHomeCombine = isHomeCombine;
};

// 融合首页向自定义导航头页面发送同步事件开关
const setIsHomeCombineEventSwitch = isHomeCombineEventSwitch => {
  appContext.isHomeCombineEventSwitch = isHomeCombineEventSwitch;
};

// 融合首页跳转的列表页向融合首页发送同步事件开关
const setIsListCombineEventSwitch = isListCombineEventSwitch => {
  appContext.isListCombineEventSwitch = isListCombineEventSwitch;
};

const setFatEnv = env => {
  appContext.fatEnv = env;
};

const setSelfServiceSwitch = selfServiceSwitch => {
  appContext.selfServiceSwitch = selfServiceSwitch;
};

const setEasyLifeSwitch = easyLifeSwitch => {
  appContext.easyLifeSwitch = easyLifeSwitch;
};

const setEnablePush = enablePush => {
  appContext.enablePush = enablePush;
};

export interface SetCarEnvType {
  carEnv: CarEnvType;
  notDisPatch?: boolean;
  notDisPatchPreFetch?: boolean;
}
const setCarEnv = (carEnv: CarEnvType) => {
  // 场景：
  // 首页Tab切换时，需要触发 changeAppEnvironment
  // 城市区域页Tab页切换，需要触发 changeCurrentEnv
  // 选择城市时，需要触发 changeAppEnvironment
  // note: dispatch 已迁移到setCarEnv中
  appContext.CarEnv = { ...appContext.CarEnv, ...carEnv };
  appContext.env = appContext.env || carEnv.env;
  appContext.testid = appContext.testid || carEnv.testid;
  appContext.mock = appContext.mock || carEnv.mock;
};

const setUserTraceQueryVid = ({
  nextDriverAgeAndNumber = '',
  nextLocationAndDate = '',
}) => {
  const {
    queryRecommendButtonClicked,
    queryVid,
    cachedDriverAgeAndNumber,
    cachedLocationAndDate,
  } = appContext.UserTrace;

  if (queryRecommendButtonClicked) {
    appContext.UserTrace.queryRecommendCount += 1;
    appContext.UserTrace.queryRecommendId = queryVid;
    appContext.UserTrace.queryRecommendButtonClicked = false;
  } else {
    appContext.UserTrace.queryRecommendId = '';
  }
  if (
    cachedDriverAgeAndNumber !== nextDriverAgeAndNumber ||
    cachedLocationAndDate !== nextLocationAndDate
  ) {
    appContext.UserTrace.queryVid = uuid();
    appContext.UserTrace.cachedLocationAndDate = nextLocationAndDate;
    appContext.UserTrace.cachedDriverAgeAndNumber = nextDriverAgeAndNumber;
    appContext.UserTrace.latestTime = +new Date();
  }
};

// 点击下单后，重置latestTime=0，标识已经记录过用户从列表页到下单的时长
// 避免重复提交和验单重复时，记录用户停留时长
const resetUserTraceLatestTime = () => {
  appContext.UserTrace.latestTime = 0;
};

const setUserTraceRecommondButtonClicked = () => {
  appContext.UserTrace.queryRecommendButtonClicked = true;
};

const setUserTraceOrderId = ({
  orderId,
  uniqueOrderId,
}: {
  orderId: number;
  uniqueOrderId: number;
}) => {
  appContext.UserTrace.orderId = orderId;
  appContext.UserTrace.uniqueOrderId = uniqueOrderId;
};

// 接口启用缓存后，在构建缓存Key时加入一个变量，用来标识当前缓存是否已失效
// 使用场景 1：
//   queryListCacheId, 列表页数据缓存，用户在首页或产品详情页填写页认证了芝麻之后，更新当前值使缓存失效
//   首页跳转列表页时，会传入queryListCacheId，当芝麻状态更新时变更当前值
const setUserFetchCacheId = (
  { queryListCacheId = uuid(), actionType = '' }: UserFetchCacheIdType = {
    queryListCacheId: uuid(),
    actionType: '',
  },
) => {
  if (queryListCacheId) {
    appContext.UserFetchCacheId.queryListCacheId = queryListCacheId;
    /* eslint-disable @typescript-eslint/no-use-before-define */
    const { isListCombineEventSwitch, isHomeCombineEventSwitch } = AppContext;
    // queryListCacheId 更改需同步到融合首页，确保首页和列表页缓存一致
    if (isListCombineEventSwitch || isHomeCombineEventSwitch) {
      // 不能使用EventHelper，会有循环依赖，改为直接引用
      Event.sendEvent('removeListCache', {
        isHomeCombineEventSwitch,
        actionType,
      });
    }
  }
};

const setPreFetchCache = (key: string, value: any) => {
  if (
    key &&
    value &&
    (lodashGet(value, 'BaseResponse.isSuccess') ||
      lodashGet(value, 'BaseResponse.IsSuccess'))
  ) {
    appContext.PreFetchCache[key] = value;
  }
};

const getPreFetchCacheByKey = (key: string) =>
  key && appContext.PreFetchCache[key]
    ? appContext.PreFetchCache[key]
    : undefined;

const setRouterListLoader = (data: RouterLoaderList) => {
  appContext.RouterLoader.List = data;
};

const setRouterListLoaderDispatch = (isDispatch: boolean) => {
  if (appContext.RouterLoader.List) {
    appContext.RouterLoader.List.isDispatch = isDispatch;
  }
};

const setListCacheInterval = data => {
  appContext.ListCacheInterval = {
    ...appContext.ListCacheInterval,
    ...data,
  };
};

const setISDIMurl = url => {
  appContext.ISDIMurl = url;
};

const setCallCityAreaPageInfo = (pageName: string) => {
  appContext.CallCityAreaPageInfo.pageName = pageName;
};

const setHomeSearchFormRef = (ref: any) => {
  appContext.HomeSearchFormRef = ref;
};

const setLocationDistance = (data: any) => {
  appContext.LocationDistance = {
    ...appContext.LocationDistance,
    ...data,
  };
};

const clearLocationDistance = () => {
  appContext.LocationDistance = baseContext.LocationDistance;
};

const setBookingTraceCode = () => {
  appContext.Booking.traceCode = uuid();
};

const setHasLogBadTime = flag => {
  appContext.hasLogBadTime = flag;
};

const setFlutter = ({ routers }: { routers: string }) => {
  appContext.Flutter.routers = routers ? routers.split(',') : [];
};

const setListPageIsEffect = isEffect => {
  appContext.ABTesting.listPageIsEffect = isEffect;
};

const setDriveTime = driveTime => {
  appContext.driveTime = driveTime;
};

const clearDriveTime = () => {
  appContext.driveTime = baseContext.driveTime;
};

const setHasUpdatePickupOnDoorCode = flag => {
  appContext.hasUpdatePickupOnDoorCode = flag;
};

const setHasPreLoadCommentList = (flag: boolean) => {
  appContext.hasPreLoadCommentList = flag;
};

const setQueryProductsRequestId = (requestId: string) => {
  appContext.queryProductsRequestId = requestId;
};

const setServerRequestId = (serverRequestId: string) => {
  appContext.UserTrace.serverRequestId = serverRequestId;
};

const setHomeTabInfo = (info: HomeTabAbInfoType) => {
  appContext.ABTesting.homeTabInfo = info;
};

const setHasUseHomeTabLocationData = (hasUse: boolean) => {
  appContext.hasUseHomeTabLocationData = hasUse;
};

const setPlatHomeModuleInfo = (info: string) => {
  appContext.platHomeModuleInfo = info;
};

const setFilterPoi = (type: FilterPoiType) => {
  appContext.filterPoi = type;
};

const setKlbVersion = version => {
  appContext.klbVersion = version ? Number(version) : null;
};

const setOrignScenes = scenes => {
  appContext.orignScenes = scenes ? Number(scenes) : null;
};

const setIsMarketing = isMarketing => {
  appContext.isMarketing = isMarketing ? Number(isMarketing) : null;
};

const setPreLoadFrom = (preLoadFrom: 'native' | 'js') => {
  if (!appContext?.preLoadFrom) {
    appContext.preLoadFrom = [];
  }
  appContext.preLoadFrom = [...appContext.preLoadFrom, preLoadFrom];
};

const updateAppContextValueFromProps = (context: AppContextType) => {
  appContext = { ...appContext, ...context };
};

const setTraceSwitch = trace => {
  appContext.traceSwitch = !!trace;
};

const setIsReachedBookingPage = isReachedBookingPage => {
  appContext.isReachedBookingPage = isReachedBookingPage;
};

const setVehicleLogInfo = vehicleLogInfo => {
  appContext.vehicleLogInfo = {
    ...appContext.vehicleLogInfo,
    ...vehicleLogInfo,
  };
};

const setGoodsShelvesTwoSwitch = goodsShelvesTwoSwitch => {
  appContext.goodsShelvesTwoSwitch = goodsShelvesTwoSwitch;
};
const setGoodsShelvesTwoABVersion = goodsShelvesTwoABVersion => {
  appContext.goodsShelvesTwoABVersion = goodsShelvesTwoABVersion;
};

const setIsdSearchCar = searchSuggestSwitch => {
  appContext.searchSuggestSwitch = searchSuggestSwitch;
};
const setListRequestId = listRequestId => {
  appContext.listRequestId = listRequestId;
};

const AppContext = {
  get preLoadFrom(): Array<'native' | 'js'> {
    return appContext.preLoadFrom;
  },
  get ISDIMurl(): string {
    return appContext.ISDIMurl;
  },
  get ABTesting(): ABTestingType {
    return appContext.ABTesting;
  },
  get MarketInfo(): MarketInfoType {
    return appContext.MarketInfo;
  },
  get Cache(): CacheType {
    return appContext.Cache;
  },
  get CarEnv(): CarEnvType {
    return appContext.CarEnv;
  },
  get LanguageInfo(): LanguageInfoType {
    return appContext.LanguageInfo;
  },
  get UserInfo(): UserInfoType {
    return appContext.UserInfo;
  },
  get Url() {
    return appContext.Url;
  },
  get UrlQuery(): UrlQueryType {
    return appContext.UrlQuery;
  },
  get OriginUrlQuery(): UrlQueryType {
    return appContext.OriginUrlQuery;
  },
  get ContentUrlQuery(): UrlQueryType {
    return appContext.ContentUrlQuery;
  },
  get PageInstance() {
    return appContext.PageInstance;
  },
  get UserTrace(): UserTraceType {
    return appContext.UserTrace;
  },
  get UserFetchCacheId() {
    return appContext.UserFetchCacheId;
  },
  get PreFetchCache() {
    return appContext.PreFetchCache;
  },
  get RouterLoader() {
    return appContext.RouterLoader;
  },
  get Env() {
    return appContext.env;
  },
  get TestId() {
    return appContext.testid;
  },
  get Mock() {
    return appContext.mock;
  },
  get ListCacheInterval() {
    return appContext.ListCacheInterval;
  },
  get CallCityAreaPageBackPageName() {
    return appContext.CallCityAreaPageInfo.pageName;
  },
  get HomeSearchFormRef() {
    return appContext.HomeSearchFormRef;
  },
  get LocationDistance() {
    return appContext.LocationDistance;
  },
  get InsuranceRules() {
    return appContext.InsuranceRules;
  },
  get Booking() {
    return appContext.Booking;
  },
  get hasLogBadTime() {
    return appContext.hasLogBadTime;
  },
  get encryptUid() {
    return appContext.encryptUid;
  },
  get fromType() {
    return appContext.fromType;
  },
  get originOrderId() {
    return appContext.originOrderId;
  },
  get modifyVendorOrderCode() {
    return appContext.modifyVendorOrderCode;
  },
  get originVendorId() {
    return appContext.originVendorId;
  },
  get originalCouponCode() {
    return appContext.originalCouponCode;
  },
  get originalActivityId() {
    return appContext.originalActivityId;
  },
  get originalActivityName() {
    return appContext.originalActivityName;
  },
  get channelId() {
    return appContext.channelId;
  },
  get eid() {
    return appContext.eid;
  },
  get isModifyOrderRebook() {
    return appContext.isModifyOrderRebook;
  },
  get isHomeCombine() {
    return appContext.isHomeCombine;
  },
  get isHomeCombineEventSwitch() {
    return appContext.isHomeCombineEventSwitch;
  },
  get isListCombineEventSwitch() {
    return appContext.isListCombineEventSwitch;
  },
  get fatEnv() {
    return appContext.fatEnv;
  },
  get Flutter() {
    return appContext.Flutter;
  },
  get listPageIsEffect() {
    return appContext.ABTesting.listPageIsEffect;
  },
  get driveTime() {
    return appContext.driveTime;
  },
  get hasUpdatePickupOnDoorCode() {
    return appContext.hasUpdatePickupOnDoorCode;
  },
  get hasPreLoadCommentList() {
    return appContext.hasPreLoadCommentList;
  },

  get queryProductsRequestId() {
    return appContext.queryProductsRequestId;
  },

  get homeTabAbInfo() {
    return appContext.ABTesting.homeTabInfo;
  },

  get hasUseHomeTabLocationData() {
    return appContext.hasUseHomeTabLocationData;
  },

  get platHomeModuleInfo() {
    return appContext.platHomeModuleInfo;
  },

  get filterPoi() {
    return appContext.filterPoi;
  },

  get klbVersion() {
    return appContext.klbVersion;
  },

  get orignScenes() {
    return appContext.orignScenes;
  },

  get isMarketing() {
    return appContext.isMarketing;
  },
  get selfServiceSwitch() {
    return appContext.selfServiceSwitch;
  },
  get easyLifeSwitch() {
    return appContext.easyLifeSwitch;
  },
  get traceSwitch() {
    return appContext.traceSwitch;
  },
  get countriesInfo() {
    return appContext.countriesInfo;
  },
  get enablePush() {
    return appContext.enablePush;
  },
  get isReachedBookingPage() {
    return appContext.isReachedBookingPage;
  },
  get vehicleLogInfo() {
    return appContext.vehicleLogInfo;
  },
  get goodsShelvesTwoSwitch() {
    return appContext.goodsShelvesTwoSwitch;
  },
  get goodsShelvesTwoABVersion() {
    return appContext.goodsShelvesTwoABVersion;
  },
  get listRequestId() {
    return appContext.listRequestId;
  },
  get searchSuggestSwitch() {
    return appContext.searchSuggestSwitch;
  },
  setPreLoadFrom,
  resetAppContext,
  setMarketInfo,
  setABTesting,
  setABNewDetail,
  setLanguageInfo,
  setUserInfo,
  getUserId,
  setUrl,
  setUrlQuery,
  clearUrlQuery,
  setPageInstance,
  setLanguageCurrency,
  setCarEnv,
  setUserTraceQueryVid,
  setUserTraceRecommondButtonClicked,
  setUserTraceOrderId,
  setUserFetchCacheId,
  setPreFetchCache,
  getPreFetchCacheByKey,
  setRouterListLoader,
  setRouterListLoaderDispatch,
  setListCacheInterval,
  setISDIMurl,
  setCallCityAreaPageInfo,
  setHomeSearchFormRef,
  setLocationDistance,
  clearLocationDistance,
  setInsuranceRules,
  setBookingTraceCode,
  setHasLogBadTime,
  setEncryptUid,
  setFromType,
  setOriginOrderId,
  setModifyVendorOrderCode,
  setOriginVendorId,
  setOriginalCouponCode,
  setOriginalActivityId,
  setOriginalActivityName,
  setChannelId,
  setEid,
  setIsModifyOrderRebook,
  setIsHomeCombine,
  setFatEnv,
  setFlutter,
  setListPageIsEffect,
  setDriveTime,
  clearDriveTime,
  setHasUpdatePickupOnDoorCode,
  setIsHomeCombineEventSwitch,
  setIsListCombineEventSwitch,
  setHasPreLoadCommentList,
  setQueryProductsRequestId,
  setServerRequestId,
  setHomeTabInfo,
  setHasUseHomeTabLocationData,
  setOriginUrlQuery,
  setContentUrlQuery,
  setPlatHomeModuleInfo,
  setFilterPoi,
  setKlbVersion,
  setOrignScenes,
  setIsMarketing,
  resetUserTraceLatestTime,
  setSelfServiceSwitch,
  setEasyLifeSwitch,
  updateAppContextValueFromProps,
  setTraceSwitch,
  setCountriesInfo,
  setEnablePush,
  setIsReachedBookingPage,
  setVehicleLogInfo,
  setGoodsShelvesTwoSwitch,
  setGoodsShelvesTwoABVersion,
  setListRequestId,
  setIsdSearchCar,
};

export default AppContext;
