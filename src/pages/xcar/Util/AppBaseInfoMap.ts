import { get as lodashGet } from 'lodash-es';
// cSpell:words orderstatus, distibutionChannelId, socactivityid, isQunarApp, alliSid, visitortraceId, packageAbversion
import Platform from '@c2x/apis/Platform';
import { xApplication as Application } from '@ctrip/xtaro';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { isListInPage, packageAbversion } from './ABTesting';
import { AgeConfig, Platform as PlatformRouter } from '../Constants/Index';
import AppContext from './AppContext';
import Utils from './Utils';
import * as TimeZoneHelper from './TimeZoneHelper';
import * as CarServerABTesting from './ServerABTesting';
import { getPlatform } from './Environment';
import * as ProductSelectors from '../Global/Cache/ProductSelectors';
import CreateChannelCode from './channelCode';
import ChannelUtils from './Channel';
import {
  LogBasicInfoType,
  PageIdReturnType,
  LogBooleanValType,
} from '../Types/CarLogTypes';
import { getStore } from '../State/StoreRef';

const parseBoolValue = (value: boolean) =>
  value ? LogBooleanValType.TRUE : LogBooleanValType.FALSE;

export const getPageNameByID = pageId => {
  if (!pageId) return '';
  const pageMap = ChannelUtils.getPageId();
  const targetKey = Object.keys(pageMap).find(
    page => pageMap[page].ID === pageId,
  );
  // @ts-ignore
  return pageMap[targetKey].EN;
};

const getPageId = (newData): PageIdReturnType => {
  const res = { ...newData };
  const getPageIdFn = lodashGet(AppContext, 'PageInstance.getPageId');
  if (!res.pageId && typeof getPageIdFn === 'function') {
    res.pageId = String(getPageIdFn());
    res.pageName = getPageNameByID(res.pageId);
  }
  return res;
};

// Store没有数据时，返回的打底埋点数据
const DefaultStateBasicInfo = {
  age: '',
  defaultAge: LogBooleanValType.FALSE,
  orderId: '',
  pickUpLat: '',
  dropOffLat: '',
  pickUpLng: '',
  dropOffLng: '',
  pickUpPoiVersion: '',
  dropOffPoiVersion: '',
  orderstatus: '',
  pickupCityId: '',
  pickupCityName: '',
  pickupLocationCode: '',
  pickupLocationType: '',
  pickupLocationName: '',
  pickupDateTime: '',
  dropOffCityId: '',
  dropOffCityName: '',
  dropOffLocationCode: '',
  dropOffLocationType: '',
  dropOffLocationName: '',
  dropOffDateTime: '',
  productDropOffTime: '',
  isDifferentLocation: LogBooleanValType.FALSE,
  isSendCar: '2',
  isPickupCar: '2',
  residency: '66',
  countryCode: '',
  countryName: '',
};

const formatTime = date => {
  const time = new Date(date);
  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');
  const hours = String(time.getHours()).padStart(2, '0');
  const minutes = String(time.getMinutes()).padStart(2, '0');
  const seconds = String(time.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const getBeijingTime = currentDate => {
  const beijingOffset = 8 * 60; // 北京时间的时区偏移量，单位为分钟
  const totalOffset = currentDate.getTimezoneOffset() + beijingOffset; // 总的时区偏移量，单位为分钟
  return new Date(currentDate.getTime() + totalOffset * 60 * 1000);
};

export const getLogBasicInfoByState = () => {
  const state = getStore()?.getState();
  const { orderBaseInfo } = state?.OrderDetail || {};
  const { rentalDate, rentalLocation } = state?.LocationAndDate || {};
  const { progress } = state?.List || {};
  const { storeName: pStoreName = '', storeCode: pStoreCode = '' } =
    ProductSelectors.getProductRes()?.pickupStoreInfo || {};
  const { storeName: rStoreName = '', storeCode: rStoreCode = '' } =
    ProductSelectors.getProductRes()?.returnStoreInfo || {};

  const age = state?.DriverAgeAndNumber?.age;
  const countryInfo = state?.CountryInfo;

  const { pickUp = {}, dropOff = {} } = rentalLocation || {};
  const { area: pickUpArea = {} } = pickUp;
  const { area: dropOffArea = {} } = dropOff;
  const cityTimeZone = TimeZoneHelper.Instance.getCityTimeZone(pickUp?.cid);
  const pickupTime = dayjs(lodashGet(rentalDate, 'pickUp.dateTime')).format(
    'YYYY-MM-DD HH:mm:ss',
  );
  const dropOffTime = dayjs(lodashGet(rentalDate, 'dropOff.dateTime')).format(
    'YYYY-MM-DD HH:mm:ss',
  );
  const productDropOffTime = dayjs(
    lodashGet(rentalDate, 'productDropOff.dateTime'),
  ).format('YYYY-MM-DD HH:mm:ss');

  const { countryId, countryCode, countryName } = countryInfo || {};

  return {
    age,
    defaultAge: parseBoolValue(age === AgeConfig.DEFAULT_AGE.getVal()),
    orderId: orderBaseInfo?.orderId ? String(orderBaseInfo?.orderId) : '',
    pickUpLat: rentalLocation?.pickUp?.area?.lat || '',
    dropOffLat: rentalLocation?.dropOff?.area?.lat || '',
    pickUpLng: rentalLocation?.pickUp?.area?.lng || '',
    dropOffLng: rentalLocation?.dropOff?.area?.lng || '',
    pickUpPoiVersion: `${rentalLocation?.pickUp?.version || ''}`,
    dropOffPoiVersion: `${rentalLocation?.dropOff?.version || ''}`,
    orderstatus: orderBaseInfo?.orderStatusDesc ?? '',
    orderStatus: orderBaseInfo?.orderStatusDesc ?? '',
    pickupCityId: pickUp.cid,
    pickupCityName: pickUp.cname,
    pickUpCountryName: rentalLocation?.pickUp?.country,
    pickupLocationCode: pickUpArea.id,
    pickupLocationType: pickUpArea.type,
    pickupLocationName: pickUpArea.name,
    pickupDateTime: pickupTime,
    dropOffCityId: dropOff.cid,
    dropOffCityName: dropOff.cname,
    dropOffCountryName: rentalLocation?.dropOff?.country,
    dropOffLocationCode: dropOffArea.id,
    dropOffLocationType: dropOffArea.type,
    dropOffLocationName: dropOffArea.name,
    dropOffDateTime: dropOffTime,
    productDropOffTime,
    isDifferentLocation: parseBoolValue(pickUpArea.id !== dropOffArea.id),
    isSendCar: '2',
    isPickupCar: '2',
    residency: `${countryId}` || '66',
    countryCode,
    countryName,
    timeZone: Utils.isCtripIsd() ? 8 : cityTimeZone?.timeZone,
    timeZoneType: Utils.isCtripIsd() ? null : cityTimeZone?.type,
    pStoreCode,
    pStoreName,
    rStoreCode,
    rStoreName,
    pageLoadingStatus: progress === 1 ? 2 : 1, // 列表页加载状态
  };
};

export const logBasicInfo = (isNoState: boolean = false): LogBasicInfoType => {
  const curDate = new Date();
  const logStateInfo = isNoState
    ? DefaultStateBasicInfo
    : getLogBasicInfoByState();
  const pageIdData = getPageId({});
  const channelCodes = CreateChannelCode({});
  Object.keys(channelCodes).forEach(key => {
    channelCodes[key] = `${channelCodes[key]}`;
  });
  const distributionChannelId = AppContext.MarketInfo.channelId;

  return {
    CRNModuleName: AppContext.UrlQuery.CRNModuleName || '',
    logIdentification: PlatformRouter.LOG_TYPE.FRONT_END_APP,
    sourceFrom: AppContext.CarEnv.appType,
    platform: getPlatform(),
    businessType: Utils.getBusinessType(),
    distributionChannelId,
    distibutionChannelId: distributionChannelId,
    channelId: Utils.isQunarApp()
      ? AppContext.MarketInfo.channelId
      : AppContext.MarketInfo.childChannelId,
    sId: AppContext.MarketInfo.sId,
    alliSid: AppContext.MarketInfo.sId,
    allianceId: AppContext.MarketInfo.aId,
    visitortraceId: AppContext.MarketInfo.visitortraceId,
    sourceId: AppContext.MarketInfo.sourceId || '',
    abVersion: packageAbversion(),
    partialVersion: AppContext.CarEnv.buildTime || '',
    crnVersion: Application.version || '',
    uId: AppContext.UserInfo.userId || '',
    telephone: '',
    currentTime: formatTime(curDate),
    // @ts-ignore
    beijingTime: formatTime(getBeijingTime(curDate)),
    awakeTime: AppContext.MarketInfo.awakeTime,
    ...AppContext.LanguageInfo,
    ...pageIdData,
    queryVid: AppContext.UserTrace.queryVid || '',
    queryRecommendId: AppContext.UserTrace.queryRecommendId || '',
    creditVersion: ProductSelectors.getCreditVersion() || '',
    creditRentAbVersion: 'B',
    listIsBatch: LogBooleanValType.FALSE,
    listInPage: parseBoolValue(isListInPage()),
    isNewDetail: parseBoolValue(Utils.isCtripIsd()),
    devicePlatform: Platform.OS || '',
    ...channelCodes,
    ParentDistibutionChannelId:
      AppContext.UrlQuery.ParentDistibutionChannelId || '',
    scene: '',
    socactivityid: '',
    openid: '',
    serverRequestId: AppContext.UserTrace.serverRequestId, // 服务端传给前端的唯一标识（用于串联排序、曝光、点击和下单埋点）
    listRequestId: AppContext.listRequestId, // 前端列表页请求生成的唯一标识（用于串联排序、曝光、点击和下单埋点）
    isHomeCombine: parseBoolValue(AppContext.isHomeCombine as boolean), // 标识是否从融合首页跳转来的
    platHomeModuleInfo: AppContext.platHomeModuleInfo,
    staticQuery: JSON.stringify(AppContext.ContentUrlQuery),
    isRecommendDetail: parseBoolValue(CarServerABTesting.isRecommend()),
    ...logStateInfo,
  };
};
