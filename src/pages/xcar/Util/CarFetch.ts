import Loading from '@c2x/apis/Loading';
import uuid from 'uuid';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { Platform, StorageKey } from '../Constants/Index';
import { getProductRequestId } from '../Global/Cache/ProductSelectors';
import CarFetchBase from './CarFetch/CarFetchBase';
import AppContext from './AppContext';
import Utils from './Utils';
import { parameterBuilder } from './CarFetchHelper';
import * as ProductSelectors from '../Global/Cache/ProductSelectors';
import CommonReqAndResData from '../Global/Cache/CommonReqAndResData';
import {
  QueryPriceCalendarRequestType,
  QueryPriceCalendarResponseType,
} from '../ComponentBusiness/CalendarPrice/types';
import CarStorage from './CarStorage';

// 出境使用 27140，国内使用 18631
const getShoppingServerCodeByEnv = () =>
  Utils.isCtripOsd() ? '27140' : '18631';

class CarFetch extends CarFetchBase {
  getCityList = (params, cancelable: boolean = false) =>
    this.getFetchObject('13589/getCategoryCity.json', params, cancelable);

  queryAppCountryId = params =>
    this.getFetchObject('14804/queryCountryId', params, false);

  createOrder = params => {
    const { extraMaps, appRequestMap, ...passthroughParams } = params;
    return this.getFetchObject(
      '18631/createOrder',
      {
        reqInfo: passthroughParams,
        parentRequestId: getProductRequestId(),
        extraMaps,
        appRequestMap,
      },
      false,
    );
  };

  saveSnapshot = params =>
    this.getFetchObject(
      '18631/AddSnapShot',
      {
        ...params,
        parentRequestId: getProductRequestId(),
      },
      false,
    );

  // 售后查询营业执照信息
  getOrderDetailLicense = params =>
    this.getFetchObject('18631/getLicense', params, false);

  collectCoupon = (couponCode: string) =>
    this.getFetchObject(
      '15465/collectCoupon',
      { couponCode, appType: Platform.APP_TYPE.IBU_APP },
      false,
    );

  // h5 代客下单获取优惠券接口
  collectCouponCodeByUid = (couponCode: string) =>
    this.getFetchObject(
      '14791/collectCouponCodeByUid',
      {
        couponCode,
        encryptUid: AppContext.encryptUid,
        useStation: 70,
        unionType: 0,
        eid: AppContext.eid,
        channelId: AppContext.channelId,
        fromType: AppContext.fromType,
        clientId: uuid(),
      },
      false,
    );

  queryStoreGuide = async params => {
    return this.getFetchObject(
      '18631/queryStoreGuide',
      {
        ...params,
        parentRequestId: getProductRequestId(),
      },
      false,
    );
  };

  queryDriverList = params =>
    this.getFetchObject('18631/queryCommonPassenger', params, false);

  deleteDriver = params =>
    this.getFetchObject('18631/deleteCommonPassenger', params, false);

  modifyDriver = params =>
    this.getFetchObject('18631/modifyCommonPassenger', params, false);

  getWordPinyin = params =>
    this.getFetchObject('13867/getWordPinyin.json', params, false);

  getLimitContent = params =>
    this.getFetchObject('18631/translimit', params, false);

  getC2BStatus = () =>
    this.getFetchObject('13617/C2BUserDemandOrder', {}, false);

  getRouterAdapter = params =>
    this.getFetchObject('13589/getRoute.json', params, false);

  getListProduct = params =>
    Promise.resolve().then(() =>
      this.getFetchObject(
        `${getShoppingServerCodeByEnv()}/queryProducts?batch=${
          params?.vendorGroup === undefined ? '' : params?.vendorGroup
        }`,

        {
          ...params,
          extraMaps: {
            klbVersion: AppContext.klbVersion, // URL传参数是否是卡拉比数据
            orignScenes: AppContext.orignScenes, // URL传参数的列表请求来源场景
            isMarketing: AppContext.isMarketing, // URL传参数的列表请求是否是营销跳转
          },
        },
        false,
      ),
    );

  getRecommendProducts = params =>
    this.getFetchObject('18631/queryRecommendProducts', params, false);

  getReceivePromotion = params =>
    this.getFetchObject(
      '18631/getReceivePromotion',
      { ...params, invokeFrom: '2' },
      false,
    );

  receivePromotion = params =>
    this.getFetchObject(
      '18631/receivePromotion',
      { ...params, invokeFrom: '2' },
      false,
    );

  getVendorListInsuranceDetails = params =>
    this.getFetchObject('18631/queryInsurance', params, false);

  getIdCardList = async params => {
    const productRes = ProductSelectors.getBaseResData()?.idCardTypes;
    if (productRes) {
      return {
        iclist: productRes.map(v => ({
          idtype: String(v.idCardType),
          typename: v.idCardName,
        })),
      };
    }
    const cardRes = await this.getFetchObject(
      '18862/isdVendorSupportCardType',
      params,
      false,
    );
    return {
      iclist: cardRes?.vendorCardTypes.map(v => ({
        idtype: String(v.idCardType),
        typename: v.idCardName,
      })),
    };
  };

  getSesameAuthentication = params =>
    this.getFetchObject('18631/getAlipayAuth', params, false);

  sesameAuthenticationCallback = params =>
    this.getFetchObject('18631/zhimaCallback', params, false);

  cancelSesameAuthentication = params =>
    this.getFetchObject('18631/cancelZhimaAuth', params, false);

  getImageList = params =>
    this.getFetchObject('18631/queryVehiclePics', params, false);

  queryOrder = params =>
    this.getFetchObject('18862/OSDQueryOrder', params, false);

  querySimilarVehicle = params =>
    this.getFetchObject('18862/querySimilarVehicle', params, false);

  createOrderAdditionalPay = params =>
    this.getFetchObject('18862/createOrderAdditionalPay', params, false);

  queryPmsInfo = params =>
    this.getFetchObject('18631/pmsPickTaskInfo', params, false);

  queryCancelFee = params =>
    this.getFetchObject('18862/queryOrderCancelFee', params, false);

  cancelOSDOrder = params =>
    this.getFetchObject('18862/OSDCancelOrder', params, false);

  createComment = params =>
    this.getFetchObject('18862/createComment', params, false);

  modifyOrder = params =>
    this.getFetchObject('18862/modifyOrder', params, false);

  submitScoreAndSuggestions = params =>
    this.getFetchObject('18862/createNps', params, false);

  queryScoreAndSuggestions = params =>
    this.getFetchObject('18862/queryNps', params, false);

  queryCarAssistantV2 = params =>
    this.getFetchObject('18862/queryCarAssistantV2', params, false);

  queryOrderPriceInfo = params =>
    this.getFetchObject('18862/queryPriceInfo', params, false);

  queryProductInfo = async (params, options = {}) => {
    return this.getFetchObject(
      `${getShoppingServerCodeByEnv()}/queryProductInfo`,
      {
        ...params,
        parentRequestId: params.requestId,
      },
      false,
      options,
    );
  };

  queryPriceInfo = async (params, options = {}) => {
    return this.getFetchObject(
      `${getShoppingServerCodeByEnv()}/querypriceinfo`,
      {
        ...params,
        parentRequestId: params.requestId,
      },
      false,
      options,
    );
  };

  feeDeductionQuery = params =>
    this.getFetchObject('14608/queryCharges.json', params, false);

  checkGpsStock = params =>
    this.getFetchObject('12681/selectP.json', params, false);

  createTempGpsOrder = params =>
    this.getFetchObject('12681/orderP.json', params, false);

  getGpsOrderStatus = params =>
    this.getFetchObject('12681/gpsOrderQuery.json', params, false);

  ISDCreateInsOrder = params =>
    this.getFetchObject('13617/CreateInsOrder.json', params, false);

  ISDBuyInsOrder = params =>
    this.getFetchObject('18862/saveOrderInsAndXProduct.json', params, false);

  getCustomerService = params =>
    this.getFetchObject('14608/getcustomerservice.json', params, false);

  queryTime = params => this.getFetchObject('13589/queryTime', params, false);

  saveAuthedCommonPassenger = () =>
    this.getFetchObject('18631/saveAuthedCommonPassenger', {}, false);

  getPayToken = param =>
    this.getFetchObject('18631/createPayOrder', param, false);

  ctripContinuePay = param =>
    this.getFetchObject('18862/continuePay', param, false);

  queryCityList = params =>
    this.getFetchObject('13609/getCityList', params, false);

  queryAreaList = params =>
    this.getFetchObject('13609/getAreaList', params, false);

  querySearchList = params =>
    this.getFetchObject('13609/searchByGlobal', params, true, { latest: true });

  longToShortLink = param =>
    this.getFetchObject('18631/longToShortLink', param, false);

  getEasyLifeTagInfo = param =>
    this.getFetchObject('18631/getEasyLifeTagInfo', param, false);

  getEasyLifeTagInfoWithCatch = param =>
    this.getFetchObjectWithCatch('18631/getEasyLifeTagInfo', param, false);

  getDriverOcrCheck = params =>
    this.getFetchObject('13589/ocrcheck', params, false);

  updateOrderAuthInfo = params =>
    this.getFetchObject('18862/safeRentAuth', params, false);

  queryCertificate = params =>
    this.getFetchObject('18862/queryCertificateV3', params, false);

  saveCertificate = params =>
    this.getFetchObject('18862/saveCertificateV3', params, false);

  updateAuthCertificate = params =>
    this.getFetchObject('18862/UpdateAuthCertificateV3', params, false);

  // Base64图片上传V2 接口改造内容 http://conf.ctripcorp.com/pages/viewpage.action?pageId=1377467223
  uploadBase64Image = async param => {
    const { channel = 'car', scene, rand = uuid() } = param || {};
    const env = await this.getEnvType();
    if (!param.notShowLoading) {
      Loading.showIconicLoading();
    }
    const domain = Utils.getImageUploadDomainURL(env);
    const url = `https://${domain}/image/v2/api/base64upload?channel=${channel}&public=${param.public}&scene=${scene}&rand=${rand}`;
    // @ts-ignore
    return (global as any)
      .fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Nephele ${rand?.replace(/-/g, '')}/${dayjs().format(
            'YYYYMMDDHHmm',
          )}/${channel}/Y29va2llLWN0cmlwOmN0aWNrZXQ=`,
        },
        body: `{"base64Str":"${param.base64}"}`,
      })
      .then(response => response.json())
      .then(response => {
        if (!param.notShowLoading) {
          Loading.hideIconicLoading();
        }
        return response;
      })
      .catch(error => {
        if (!param.notShowLoading) {
          Loading.hideIconicLoading();
        }
        throw error;
      });
  };

  // 订单详情状态变更截图接口
  // eslint-disable-next-line max-len
  updateOrderdetailShot = param =>
    this.getFetchObject('18862/uploadOrderDetailSnapshot', param, false);

  // 信用租成功后调用
  getDepositNotice = param =>
    this.getFetchObject('18862/DepositNotice', param, false);

  // 预授权传requestid
  updatePayment = param =>
    this.getFetchObject('18862/ISDUpdatePayment', param, false);

  changeOrder = param => this.getFetchObject('18862/changeOrder', param, false);

  fetchEasyLifeList = param =>
    this.getFetchObject('18631/getProducts4Index', param, false);

  fetchActivityGroup = param =>
    this.getFetchObject('13617/activityGroupMultiCities', param, false);

  fetchSearchLimousinevehicle = param =>
    this.getFetchObject(
      '13617/isdsearchmulticitieslimousinevehicle',
      param,
      false,
    );

  fetchSearchVehicles = param =>
    this.getFetchObject('13837/searchMultiCitiesVehicles', param, false);

  getCredentialsListApi = param =>
    this.getFetchObject('18862/queryOrderContactPic', param, false, undefined);

  getSuplementListApi = param =>
    this.getFetchObject('18862/queryDeduction', param, false);

  queryAdditionPayment = param =>
    this.getFetchObject('18862/queryAdditionPayment', param, false);

  rentalMustRead = (param, options?: any) =>
    this.getFetchObject('18631/rentalMustRead', param, false, options);

  queryOsdPolicy = param =>
    this.getFetchObject('18862/queryPolicy', param, false);

  getQConfig = async () => {
    const config = CommonReqAndResData.getData(
      CommonReqAndResData.keyList.configRes,
    );
    if (config) {
      return new Promise(resolve => {
        resolve(config);
      });
    }
    const param = {
      keys: [
        {
          key: 'sourceFromConfig',
        },
      ],
    };
    const parameter = parameterBuilder({
      param,
      cachePolicy: {
        enableCache: true,
      },
      verifyResponseIsSuccess: response => {
        return response?.infos?.length > 0;
      },
    });
    const { response } = await this.getFetchObjectWithCatch(
      '18631/config',
      parameter,
      false,
    );
    if (!response?.infos?.length) return null;
    return response?.infos.reduce((memo, info) => {
      const { content } = info;
      if (content === 'true') {
        return { ...memo, [info.key]: true };
      }
      if (content === 'false') {
        return { ...memo, [info.key]: false };
      }
      try {
        return { ...memo, [info.key]: JSON.parse(content) };
      } catch (e) {
        return { ...memo, [info.key]: content };
      }
    }, {});
  };

  getLastOrder = params =>
    this.getFetchObject('18862/queryHomePageCard', params, false);

  getListWarningInfo = params =>
    this.getFetchObject('19984/listWarningInfo', params, false);

  iSDUpdateFreeDepositInfo = params =>
    this.getFetchObject('18862/iSDUpdateFreeDepositInfo', params, false);

  // 行程城市信息关键字查询
  getDistrictCityMapping = params =>
    this.getFetchObject('13034/getDistrictCityMapping', params, false);

  queryRenewalProduct = params =>
    this.getFetchObject('18862/queryRenewalOrder', params, false);

  queryRenewalPenalty = params =>
    this.getFetchObject('18862/queryRenewalPenalty', params, false);

  saveRenewalOrder = params =>
    this.getFetchObject('18862/saveRenewalOrder', params, false);

  createPaymentReferenceNo = params =>
    this.getFetchObject('18862/createPaymentReferenceNo', params, false);

  cancelRenewalOrder = params =>
    this.getFetchObject('18862/cancelRenewalOrder', params, false);

  queryInsuranceOrder = params =>
    this.getFetchObject('18862/queryInsuranceOrder', params, false);

  // 获取保代跳转地址
  getInsConfirmUrl = params =>
    this.getFetchObject('13546/buildInsuranceParams', params, false);

  modifyOrderV2 = params =>
    this.getFetchObject('18862/modifyOrderV2', params, false);

  cancelModify = (orderId: number) =>
    this.getFetchObject('18862/cancelModify', { orderId }, false);

  modifyPrice = params =>
    this.getFetchObject('18862/modifyPrice', params, false);

  modifyConfirm = params =>
    this.getFetchObject('18862/modifyConfirm', params, false);

  // 判断订单相关状态
  queryOrderStatus = params =>
    this.getFetchObject('18862/queryOrderStatus', params, false);

  // 订单详情门店政策
  getBookingNotice = params =>
    this.getFetchObject('18862/getBookingNotice', params, false);

  // 获取日历价
  queryPriceCalendar: (
    params: QueryPriceCalendarRequestType,
  ) => Promise<QueryPriceCalendarResponseType> = params =>
    this.getFetchObject('18631/queryPriceCalendar', params, false);

  // 获取取车门店降级信息
  queryDownGradeInfo = params =>
    this.getFetchObject('18631/queryDownGradeInfo', params, false);

  // 获取特权落地页信息接口
  queryUserPrivilegeList = params =>
    this.getFetchObject('14791/queryUserPrivilegeList', params, false);

  // 获取补款修改订单信息
  modifyToPay = params =>
    this.getFetchObject('18862/modifyToPay', params, false);

  // 判断首页超会入口信息
  getIndexSuperVip = params =>
    this.getFetchObject('18631/getIndexSuperVip', params, false);

  // 租车中心首页
  homeController = (params, options = {}) =>
    this.getFetchObject('18631/homeController', params, false, options);

  // 租车中心落地页
  rentCenterPage = params =>
    this.getFetchObject('18631/rentCenterPage', params, false);

  // 获取订单详情页取还车指引
  queryOrderDetailStoreGuide = params =>
    this.getFetchObject('18862/getMapGuide', params, false);

  // 查询VOC问题
  queryQuestionnaire = params =>
    this.getFetchObject('18862/queryQuestionnaire', params, false);

  // 回答VOC问题
  saveQuestionnaire = params =>
    this.getFetchObject('18862/saveQuestionnaire', params, false);

  // 预订条款
  reservationTerms = params =>
    this.getFetchObject('18631/reservationTerms', params, false);

  // 分享信息
  queryShareInfo = params =>
    this.getFetchObject('18631/queryShareInfo', params, false);

  // 新版详情报价列表
  queryVehicleDetailList = (params, option?: any) =>
    this.getFetchObject('18631/queryVehicleDetailList', params, false, option);

  // 国内货架一期详情报价列表
  queryVehicleDetailListV2 = (params, option?: any) =>
    this.getFetchObject(
      '18631/queryVehicleDetailListV2',
      params,
      false,
      option,
    );

  // 新版详情弹窗
  queryVehicleDetailInfo = (params, option?: any) =>
    this.getFetchObject('18631/queryVehicleDetailInfo', params, false, option);

  // 获取中台点评
  getCommentSummary = (params, option?: any) =>
    this.getFetchObject('18631/getCommentSummary', params, false, option);

  // 获取行程卡信息
  getAboutToTravel = params =>
    this.getFetchObject('13609/getAboutToTravel', params, false);

  querySignStatus = params =>
    this.getFetchObject('18862/querySignStatus', params, false);

  // 查询事件进度
  queryServiceProgress = params =>
    this.getFetchObject('18862/queryServiceProgress', params, false);

  // 客服进度催处理
  urgeServiceProgress = params =>
    this.getFetchObject('18862/urgeServiceProgress', params, false);

  // 查询会员权益
  queryMembershipRights = params =>
    this.getFetchObject('18631/queryMembershipRights', params, false);

  // 车型榜单
  queryTopProducts = params =>
    this.getFetchObject(
      '18631/queryTopProducts',
      {
        ...params,
        extraMaps: {
          isFilterPrice: '', // 榜单屏蔽列表页精减vendorPriceList节点
        },
      },
      false,
    );

  // 获取Lottie json
  getLottieJson = (url: string) => {
    // @ts-ignore
    return (global as any)
      .fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(response => response.json())
      .then(response => response)
      .catch(error => {
        throw error;
      });
  };

  queryWarningList = params =>
    this.getFetchObject('18631/queryWarningList', params, false);

  queryVirtualNumber = (storeCode: string, reference) =>
    this.getFetchObjectWithCatch(
      '18631/queryStoreVNumber',
      { storeCode, reference },
      false,
    );

  // 订单详情页加油劵领劵入口
  queryOrderWelfare = params =>
    this.getFetchObject('18862/queryOrderWelfare', params, false);

  queryOrderInsAndXProduct = params =>
    this.getFetchObject('18862/queryOrderInsAndXProduct', params, false);

  // 首页会员等级模块数据
  queryMembershipRights4Index = params =>
    this.getFetchObject('18631/queryMembershipRights4Index', params, false);

  // 获取营销主题配置
  queryThemeConfig = params =>
    this.getFetchObject('14791/querySkins', params, false);

  // 获取信息流推荐cityId
  getStreamRecCityId = params =>
    this.getFetchObject('18631/getStreamRecCityId', params, false);

  // 首页云南文旅入口
  queryYunnanWelfare = params =>
    this.getFetchObject('18631/queryYunnanWelfare', params, false);

  cancelInfo = params => this.getFetchObject('18862/cancelInfo', params, false);

  // 推荐页查询车型产品
  queryRecommendProducts = params =>
    this.getFetchObject('18631/queryFlightRecProducts', params, false);

  // 获取订详首屏静态数据
  getOrderDetailFirstScreenData = params =>
    this.getFetchObject('18862/queryOrderSecondOpen', params, false);

  // 订祥虚拟小号
  queryOrderNumber = params =>
    this.getFetchObject('18862/queryOrderNumber', params, false);

  // 获取客服链接
  getImUrl = params =>
    this.getFetchObject('18631/CustomerServiceChatUrl', params, false);

  // 校验航班号
  checkFlightNo = params =>
    this.getFetchObject('18631/CheckFlightNo', params, false);

  // 免押落地页数据
  getDepositInfo = () =>
    this.getFetchObjectWithCatch(
      '18631/getDepositInfo',
      {
        extraMaps: {
          osdmyldy: '1',
        },
      },
      false,
    );

  // 首页一起去自驾模块
  queryTripRecommend = params =>
    this.getFetchObject('18631/queryRoadTripRecomd', params, false);

  // 查询提前还车费用
  queryEarlyReturnPrice = params =>
    this.getFetchObject('18862/queryEarlyReturnPrice', params, false);

  // 创建提前还车记录
  createEarlyReturnRecord = params =>
    this.getFetchObject('18862/createEarlyReturnRecord', params, false);

  // 取消提前还车
  cancelEarlyReturn = params =>
    this.getFetchObject('18862/cancelEarlyReturn', params, false);

  // 修改旅行限制
  modifyCrossLocation = params =>
    this.getFetchObject('18862/modifyCrossLocation', params, false);

  // 驾照翻译件订单列表查询接口
  queryDriverLicenseOrders = params =>
    this.getFetchObject('10098/GetAllorders', params, false);

  // 查询额外设备
  queryEquipmentInfo = params =>
    this.getFetchObjectWithCatch('18631/queryEquipmentInfo', params, false);

  // 通过城市id反查省份
  getCitys = params => this.getFetchObject('13609/getcitys', params, false);

  // 查询海外保险订单状态
  queryExtraInsurance = params =>
    this.getFetchObject('18862/queryExtraInsurance', params, false);

  // 人脸识别Token获取
  getFaceToken = params =>
    this.getFetchObject('18862/getFaceToken', params, false);

  // 多媒体相册查询接口
  queryMultimediaAlbum = params =>
    this.getFetchObject('18631/queryMultimediaAlbum', params, false);

  // 查询指定客源国驾照政策
  queryLicencePolicy = params =>
    this.getFetchObject('18631/queryLicencePolicy', params, false);

  // 自助取还获取车机状态
  queryVehicleStatus = params =>
    this.getFetchObject('18862/queryVehicleStatus', params, false);

  // 自助取还操作车机
  selfServiceOperation = params =>
    this.getFetchObject('18862/selfServiceOperation', params, false);

  // 无忧租2024套餐对比
  queryPackageComparison = params =>
    this.getFetchObjectWithCatch('18631/queryPackageComparison', params, false);

  queryFulfillment = params =>
    this.getFetchObjectWithCatch('31474/queryOrderFulfill', params, false);

  uploadFulfillSnapshot = params =>
    this.getFetchObject('31474/uploadFulfillSnapshot', params, false);

  // 自助取还还车校验
  checkSubmitReturnCar = params =>
    this.getFetchObject('18862/checkSubmitReturnCar', params, false);

  // 订详获取邮件DID-门店消息
  queryOrderNoticeFromDid = params =>
    this.getFetchObject('31474/queryOrderNoticeFromDid', params, false);

  // 定位数据反查类型接口
  completePoiInfo = params =>
    this.getFetchObject('13609/completePoiInfo', params, false);

  // 获取境外修改订单说明
  queryOsdModifyOrderNote = params =>
    this.getFetchObjectWithCatch(
      '18862/queryOsdModifyOrderNote',
      params,
      false,
    );

  queryAppCountryIdList = async params => {
    // 读内存数据
    if (AppContext.countriesInfo?.length > 0) {
      return {
        countries: AppContext.countriesInfo,
        appResponseMap: {
          isFromStorage: true,
          networkCost: 0,
        },
      };
    }
    // 读缓存数据
    const storageCountryKey = StorageKey.COUNTRY_ID_LIST;
    const before = +new Date();
    const storage = await CarStorage.loadAsync(storageCountryKey);
    if (typeof storage === 'string') {
      const result = Utils.stringToJson(storage);
      if (result?.appResponseMap && result?.countries?.length > 0) {
        result.appResponseMap.isFromStorage = true;
        result.appResponseMap.networkCost = +new Date() - before;
        AppContext.setCountriesInfo(result?.countries);
        return result;
      }
    }

    // 读接口数据
    const result = await this.getFetchObject(
      '13609/getCountries',
      params,
      false,
    );
    // 国家ID数据缓存30天
    if (result?.countries?.length > 0) {
      AppContext.setCountriesInfo(result?.countries);
      CarStorage.save(storageCountryKey, result, '30d');
    }
    return result;
  };

  // 查询提前还车费用
  checkPoi = params => this.getFetchObject('27140/checkPoi', params, false);

  // 订详合并接口
  queryMainOrder = params =>
    this.getFetchObject('18862/queryMainOrder', params, false);

  // 查询ipoll配置
  getIpollConfig = params =>
    this.getFetchObject('18631/getIpollConfig', params, false);

  // 车型搜索联想词建议接口
  searchSuggestion = params =>
    this.getFetchObject('18631/searchSuggestion', params, false);

  // 发送埋点数据到服务端
  trackPage = params => this.getFetchObject('19978/trackPage', params, false);
}

export default new CarFetch();
