import {
  get as lodashGet,
  startsWith as lodashStartsWith,
  forEach as lodashForEach,
  debounce as lodashDebounce,
  isError as lodashIsError,
} from 'lodash-es';
/* eslint-disable no-underscore-dangle */
// cSpell:words thirdpartyChannles
import Image, {
  prefetch as ImagePrefetch,
  imageGetSize,
} from '@c2x/components/Image';
import Permission from '@c2x/apis/Permission';
import Platform from '@c2x/apis/Platform';
import Log from '@c2x/apis/Log';
import Dimensions from '@c2x/apis/Dimensions';
import URL from '@c2x/apis/URL';
import {
  xEnv,
  xApplication as Application,
  xRouter,
  XStatusBar as StatusBar,
  xUbt,
} from '@ctrip/xtaro';
import qs from 'qs';
// TODO JENNY 观察性能1
import utc from '@ctrip/rn_com_car/dist/src/Dayjs/src/plugin/utc';

import dayjs, { Dayjs } from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import uuid from 'uuid';
import { ENV_TYPE, DOMAIN_URL } from '@ctrip/rn_com_car/dist/src/CarFetch';

import memoize from 'memoize-one';
import Country from '../Constants/Country';
import * as ApiResCode from '../Constants/ApiResCode';
import * as ImageUrl from '../Constants/ImageUrl';
import { CoordinateType } from '../ComponentBusiness/PickdropMap/src/Constants';

import {
  IMAGE_UPLOAD_DOMAIN_URL,
  APP_TYPE,
  APP_ID,
  BUSINESS_TYPE,
  RENTAL_GAP,
  BUS_TYPE,
  DROPOFF_INTERVAL,
  ClientType,
  SIDE_TOOL_BIZ_TYPE,
  LIST_SHOW_VENDOR_NUM,
  COMPONENT_CHANNEL,
  EASYLIFE_LANDING_URL,
} from '../Constants/Platform';
import AppContext from './AppContext';
import { thirdpartyChannles } from '../Constants/OrderDetail';
import Channel from './Channel';
import LogKeyDev from '../Constants/LogKeyDev';
import ErrorKey from '../Constants/ErrorKey';

/* eslint-disable @typescript-eslint/naming-convention */
export interface storageLoadParamType {
  key: string;
  domain?: string;
  isSecret?: boolean;
}

const OSDTypes = [
  APP_TYPE.OSD_C_APP,
  APP_TYPE.OSD_C_CW,
  APP_TYPE.OSD_C_H5,
  APP_TYPE.OSD_Q_APP,
  APP_TYPE.OSD_ZUCHE_APP,
];

const ISDTypes = [
  APP_TYPE.ISD_C_APP,
  APP_TYPE.ISD_C_CW,
  APP_TYPE.ISD_C_H5,
  APP_TYPE.ISD_Q_APP,
  APP_TYPE.ISD_ZUCHE_APP,
];

const noop = () => null;

const timeOutMessageList = [
  '{"cancelTimeout":true}',
  '"_promise_error_ignore":true', // 系统请求异常
];

class Utils {
  static noop = noop;

  static rentalPickerOption = null;

  static lastAppType = '';

  static getEnvType(): string {
    let env = xEnv.getDevEnv()?.toLowerCase();
    if (env === 'prod') {
      env = 'prd';
    }
    return env;
  }

  static getDomainURL(env: string): string {
    return DOMAIN_URL[env] || DOMAIN_URL[ENV_TYPE.PROD];
  }

  static getImageUploadDomainURL(env: string): string {
    return (
      IMAGE_UPLOAD_DOMAIN_URL[env] || IMAGE_UPLOAD_DOMAIN_URL[ENV_TYPE.PROD]
    );
  }

  static getCoordinateType() {
    return Utils.isCtripIsd() ? CoordinateType.Mars : CoordinateType.Geocentric;
  }

  /**
   * APP Type is oneof : ISD_C_APP & OSD_C_APP & OSD_T_APP
   * How to define App type? AppId + urlAppType is unique
   * AppId : Enum APP_ID
   * urlAppType : Enum APP_TYPE
   * 37 + OSD_T_APP
   * 999999 + OSD_C_APP
   * 999999 + ISD_C_APP
   * @param {string} urlAppType from url CRNModuleName=rn_xtaro_car_main&CRNType=1&apptype=OSD_C_APP
   * @return {string} return Enum APP_TYPE
   */
  static getAppType(urlAppType: string = ''): string {
    const upper = urlAppType.toUpperCase();
    /* eslint-disable dot-notation */
    // @ts-ignore
    if (global.__crn_appId === APP_ID.TRIP) return APP_TYPE.OSD_T_APP;
    if (upper === APP_TYPE.OSD_C_APP) return APP_TYPE.OSD_C_APP;
    if (upper === APP_TYPE.ISD_C_APP) return APP_TYPE.ISD_C_APP;

    return APP_TYPE.ISD_C_APP;
  }

  static getParams = (data: any): any => {
    let result = {};
    try {
      result = JSON.parse(decodeURIComponent(data));
      // eslint-disable-next-line no-empty
    } catch (e) {}
    return result;
  };

  static urlParse = (data: any): any => {
    if (!data) return data;
    let result = decodeURIComponent(data);
    try {
      result = JSON.parse(result);
      // eslint-disable-next-line no-empty
    } catch (e) {}
    return result;
  };

  static getNewAppType = (
    isDomestic,
    nowAppType = AppContext.CarEnv.appType,
  ) => {
    let appType = nowAppType;
    const defaultAppType = isDomestic ? APP_TYPE.ISD_C_APP : APP_TYPE.OSD_C_APP;

    if (isDomestic) {
      switch (nowAppType) {
        case APP_TYPE.OSD_C_APP:
          appType = defaultAppType;
          break;
        case APP_TYPE.OSD_C_H5:
          appType = APP_TYPE.ISD_C_H5;
          break;
        case APP_TYPE.OSD_Q_APP:
          appType = APP_TYPE.ISD_Q_APP;
          break;
        case APP_TYPE.OSD_ZUCHE_APP:
          appType = APP_TYPE.ISD_ZUCHE_APP;
          break;
        default:
          appType = defaultAppType;
          break;
      }
    } else {
      switch (nowAppType) {
        case APP_TYPE.ISD_C_APP:
          appType = defaultAppType;
          break;
        case APP_TYPE.ISD_C_H5:
          appType = APP_TYPE.OSD_C_H5;
          break;
        case APP_TYPE.ISD_Q_APP:
          appType = APP_TYPE.OSD_Q_APP;
          break;
        case APP_TYPE.ISD_ZUCHE_APP:
          appType = APP_TYPE.OSD_ZUCHE_APP;
          break;
        default:
          appType = defaultAppType;
          break;
      }
    }
    return appType;
  };

  static getUrlCityId = urlQuery => {
    const { pcid, data } = urlQuery;
    let clientPcid = 0;
    try {
      const clientParams = Utils.getParams(data);
      clientPcid = lodashGet(clientParams, 'rentalLocation.pickUp.cid');
      // eslint-disable-next-line no-empty
    } catch (e) {}
    return Number(pcid || clientPcid || 0);
  };

  static getRediectCityId = () =>
    Utils.getUrlCityId(AppContext.OriginUrlQuery) ||
    Utils.getUrlCityId(AppContext.UrlQuery);

  static getBusinessType(): string {
    if (Utils.isCtripOsd()) {
      return BUSINESS_TYPE.OSD;
    }
    if (Utils.isCtripIsd()) {
      return BUSINESS_TYPE.ISD;
    }

    return BUSINESS_TYPE.UNKNOW;
  }

  static getCurrentEnv(): string {
    if (Utils.isCtripOsd()) {
      return COMPONENT_CHANNEL.OSD;
    }
    if (Utils.isCtripIsd()) {
      return COMPONENT_CHANNEL.ISD;
    }
    return BUSINESS_TYPE.ISD;
  }

  // 获取其它环境：当前环境是国内,其它环境就是境外;当前环境是境外,其它环境就是国内
  static getOtherEnv(): string {
    return Utils.isCtripIsd() ? COMPONENT_CHANNEL.OSD : COMPONENT_CHANNEL.ISD;
  }

  static isTrip(): boolean {
    // @ts-ignore
    return global.__crn_appId === APP_ID.TRIP;
  }

  static isCtripOsd(): boolean {
    return OSDTypes.includes(AppContext.CarEnv.appType);
  }

  static isCtripIsd(): boolean {
    return ISDTypes.includes(AppContext.CarEnv.appType);
  }

  static isCtripIsdByType(appType): boolean {
    return ISDTypes.includes(appType);
  }

  static isCtripOsdByType(appType): boolean {
    return OSDTypes.includes(appType);
  }

  static getType(): string {
    if (Utils.isCtripOsd()) {
      return 'osd';
    }
    if (Utils.isCtripIsd()) {
      return 'isd';
    }
    return '';
  }

  static isCtripApp(): boolean {
    // @ts-ignore
    return global.__crn_appId === APP_ID.CTRIP;
  }

  static isQunarApp(): boolean {
    return AppContext.CarEnv.appType === APP_TYPE.OSD_Q_APP;
  }

  // get ubt info
  static getUBT(): any {
    let ubt = {};
    // @ts-ignore
    const crnPV = Log.createPV({});
    /* eslint-disable */
    // @ts-ignore
    if (crnPV && crnPV._pv && crnPV._pv.data) {
      /* eslint-disable */
      // @ts-ignore
      ubt = crnPV._pv.data;
    }

    return ubt;
  }

  static promisable(asyncFunc: Function): any {
    return (...args: any[]) =>
      new Promise(resolve => {
        const callback = (...cargs: any[]) => {
          let data;
          if (cargs && cargs.length === 1) {
            data = cargs[0];
          } else {
            data = cargs;
          }
          resolve(data);
        };

        const newArgs = args;
        if (args.length) {
          const index = args.findIndex(p => p === 'callback');
          if (index > -1) {
            newArgs[index] = callback;
          }
        } else {
          newArgs[0] = callback;
        }
        asyncFunc(...newArgs);
      });
  }

  static dateTimeFormat = (str: string): string => {
    let result = '';
    if (str !== null && str !== undefined && str.length === 14) {
      result = `${str.substr(0, 4)}-${str.substr(4, 2)}-${str.substr(
        6,
        2,
      )} ${str.substr(8, 2)}:${str.substr(10, 2)}:${str.substr(12, 2)}`;
    }
    return result;
  };

  static fullImgProtocal = imgUrl => {
    if (lodashStartsWith(imgUrl, '//')) {
      return `https:${imgUrl}`;
    }

    return imgUrl;
  };

  static getRentalGap = () => {
    if (Utils.isCtripOsd()) {
      return RENTAL_GAP.OSD;
    }
    if (Utils.isCtripIsd()) {
      return RENTAL_GAP.ISD;
    }
    return RENTAL_GAP.UNKNOW;
  };

  static getBusType = () => {
    if (Utils.isCtripIsd()) {
      return BUS_TYPE.ISD;
    }
    if (Utils.isCtripOsd()) {
      return BUS_TYPE.OSD;
    }
    return 0;
  };
  // @ts-ignore
  static isAndroid = Platform.OS === 'android' || Platform.OS === 'harmony';

  static heightWithStatusBar =
    Dimensions.get('window').height + (StatusBar.currentHeight || 0);

  static openUrlWithTicket = url => {
    const ticket = new Date().getTime();
    xRouter.navigateTo({ url: `${url}&cache=${ticket}` });
  };

  static autoProtocol = (url: string, protocol: string = 'https') => {
    if (!url) return url;
    if (url.includes('http:') || url.includes('https:')) return url;
    /**
     * case1: //m.ctrip.com/webapp/cars/osd/osd/osdinvoice?id=6002282663
     * case2: ://m.ctrip.com/webapp/cars/osd/osd/osdinvoice?id=6002282663
     */
    if (url.startsWith('://')) return `${protocol}${url}`;
    if (url.startsWith('//')) return `${protocol}:${url}`;
    return url;
  };

  static openPDF = (url: string) => {
    Utils.isAndroid
      ? URL.openURLWithDefaultBrowser(Utils.autoProtocol(url))
      : xRouter.navigateTo({ url: Utils.autoProtocol(url) });
  };

  /**
   * 将json对象转换成&连接的字符串
   * @param
   */
  static toParams = (param, isToLowerCase?: boolean, needEncode?: boolean) => {
    let result = '';
    if (param) {
      for (let name in param) {
        let infoData = param[name];
        if (needEncode) {
          infoData = encodeURIComponent(infoData);
        }
        // if (typeof infoData === 'object') {
        //   for (let subName in infoData) {
        //     if (infoData[subName]) {
        //       const keyName = isToLowerCase ? subName.toLowerCase() : subName;
        //       result += "&" + keyName + "=" + infoData[subName];
        //     }
        //   }
        // } else {
        const keyName = isToLowerCase ? name?.toLowerCase() : name;
        result += '&' + keyName + '=' + infoData;
        //}
      }
    }
    return result.substring(1);
  };

  static isObjectArray = (data: any) => {
    const isArrayType =
      Object.prototype.toString.call(data) === '[object Array]';
    const keys = Object.keys(data);
    return isArrayType && keys.length === 1 && keys[0] === '0';
  };

  static isObject = (data: any) =>
    Object.prototype.toString.call(data) === '[object Object]';

  static objectArrayToArray = (data: any) =>
    Utils.isObjectArray(data) ? data[0] : data;

  static toRMB = (currencyCode: string) => {
    // currencyCode === 'CNY' ? Symbol.RMB : currencyCode // todo 现在会返回为 @@RMB
    return currencyCode === 'CNY' ? '¥' : currencyCode;
  };

  static cloneDeep = content => JSON.parse(JSON.stringify(content));

  static getDropOffInterval = () => {
    if (Utils.isCtripOsd()) {
      return DROPOFF_INTERVAL.OSD;
    }
    if (Utils.isCtripIsd()) {
      return DROPOFF_INTERVAL.ISD;
    }
    return DROPOFF_INTERVAL.UNKNOW;
  };

  static getRentalPickerOption = () => {
    if (
      !Utils.rentalPickerOption ||
      Utils.lastAppType !== AppContext.CarEnv.appType
    ) {
      Utils.lastAppType = AppContext.CarEnv.appType;
      Utils.rentalPickerOption = {
        Dropoff_Interval: Utils.getDropOffInterval(),
        Default_Interval_Days: Utils.getRentalGap(),
      };
    }
    return Utils.rentalPickerOption;
  };

  static getClientType = () => {
    let ct = ClientType.ctrip;
    if (Utils.isQunarApp()) {
      ct = ClientType.qunar;
    } else if (Platform.OS === 'web') {
      ct = ClientType.h5;
    } else if (
      parseInt(AppContext.MarketInfo.channelId, 10) ===
      parseInt(thirdpartyChannles.holiday, 10)
    ) {
      ct = ClientType.holiday;
    }
    return ct;
  };
  static getUrlParam = (url, name) => {
    const reg = `(\\?|&)${name}=([^&]+)(&|$)`;
    // const re = new RegExp("(\\?|&)" + name + "=([^&]+)(&|$)", 'i');
    const re = new RegExp(reg, 'i');
    const m = url.match(re);
    return m ? m[2] : '';
  };
  static getQueryString = url => {
    return Utils.getQueryParams(url, { orderId: 0, driverLienceNo: 0 });
  };
  static getQueryParams: any = (url, defaultObj = {}) => {
    if (url === '') return defaultObj;
    const params = url.substr(url.indexOf('?') + 1).split('&');
    const queryString = defaultObj;
    for (let i = 0; i < params.length; ++i) {
      const p = params[i].split('=');
      if (p.length !== 2) {
        queryString[params[i].substr(0, params[i].indexOf('='))] = params[
          i
        ].substr(params[i].indexOf('=') + 1);
      } else {
        queryString[p[0]] = decodeURIComponent(p[1].replace(/\+/g, ' '));
      }
    }
    return queryString;
  };
  // 将对象的key转为小写，兼容老版详情页的字段
  static keyMapping = source => {
    if (Array.isArray(source)) {
      return source.map(item => Utils.keyMapping(item));
    } else if (typeof source === 'object') {
      const o = {};
      for (const key in source) {
        o[key.toLowerCase()] = source[key];
      }
      return o;
    } else {
      return source;
    }
  };

  static getRequestUrl = async (url: string, protocol: string = 'https') => {
    const env = Utils.getEnvType();
    const domain = Utils.getDomainURL(env);
    return `${protocol}://${domain}${url}`;
  };

  static jumpUrl = async params => {
    const d = params || {};
    const { path = '', pageId, enName } = d;
    if (
      path.includes('ctrip://wireless') ||
      path.includes('CRNModuleName') ||
      params.isQunar
    ) {
      xRouter.navigateTo({ url: path });
    } else if (path.includes('http://') || path.includes('https://')) {
      xRouter.navigateTo({ url: path });
    } else {
      let url = await Utils.getRequestUrl(path);
      xRouter.navigateTo({ url: url });
    }
  };

  static formatCNPhone(value = '') {
    const SPACE = ' ';
    value = value.replace(/\s+/g, '');
    const sub1 = value.substr(0, 3);
    const sub2 = value.substr(3, 4);
    const sub3 = value.substr(7);
    const getSpace = str => (str && str.length > 0 ? SPACE : '');
    return `${sub1}${getSpace(sub2)}${sub2}${getSpace(sub3)}${sub3}`;
  }

  static formatCNCertificateNo(value = '') {
    const SPACE = ' ';
    value = value.replace(/\s+/g, '');
    const sub1 = value.substr(0, 6);
    const sub2 = value.substr(6, 4);
    const sub3 = value.substr(10, 4);
    const sub4 = value.substr(14);
    const getSpace = str => (str && str.length > 0 ? SPACE : '');
    return `${sub1}${getSpace(sub2)}${sub2}${getSpace(sub3)}${sub3}${getSpace(
      sub4,
    )}${sub4}`;
  }

  /**
   * 输入内容时自动添加空格
   * @param {*} type
   *  1:手机号 3_4_4
   *  2:身份证 6_4_4_...
   *  3:身份证 6_4_4_4
   */
  static strFormat = (str, type) => {
    if (!type) return str;
    if (type === 1) {
      return Utils.formatCNPhone(str);
    }
    if (type === 2) {
      let i = 0;
      let s = '';
      while (str.length > i) {
        if (i === 0) {
          s = str.substr(0, 6);
          if (s.length === 6) s += ' ';
          i += 6;
        } else {
          const s1 = str.substr(i, 4);
          s += s1.length === 4 ? `${s1} ` : s1;
          i += 4;
        }
      }
      return s;
    }
    if (type === 3) {
      return Utils.formatCNCertificateNo(str);
    }
    return str;
  };
  /** 去除空格 */
  static strTrim(str) {
    if (!str) return str;
    return str.replace(/\s+/g, '');
  }

  // in: format('{0} is the {1} powerful language{2}, {1}!' ,'javascript', 'most', '!')
  // out: 'javascript is the most powerful language!, most!'
  static format(str, ...params) {
    return str.replace(/{(\d+)}/g, function (match, number) {
      return typeof params[number] != 'undefined' ? params[number] : match;
    });
  }

  static getUrlHost(env = Application.env) {
    let host;
    const ev = env;
    switch (ev) {
      case 'fat':
        host = 'https://m.fat10668.qa.nt.ctripcorp.com';
        break;
      case 'uat':
        host = 'https://m.uat.qa.nt.ctripcorp.com';
        break;
      case 'prd':
        host = 'https://m.ctrip.com';
        break;
      default:
        host = 'https://m.ctrip.com';
    }
    return host;
  }

  static getTripH5Host(env = Application.env) {
    let host;
    const ev = env;
    switch (ev) {
      case 'fat':
        host = 'https://hk.fat1.qa.nt.tripqate.com/';
        break;
      case 'uat':
        host = 'https://hk.fat1.qa.nt.tripqate.com/';
        break;
      case 'prd':
        host = 'https://hk.trip.com/';
        break;
      default:
        host = 'https://hk.trip.com/';
    }
    return host;
  }

  static getHost(env = Application.env) {
    let host;
    const ev = env;
    switch (ev) {
      case 'fat':
        host = 'https://m.fat10668.qa.nt.ctripcorp.com';
        break;
      case 'uat':
        host = 'https://m.uat.qa.nt.ctripcorp.com';
        break;
      case 'prd':
        host = 'https://m.ctrip.com';
        break;
      default:
        host = 'https://m.ctrip.com';
    }
    return host;
  }
  static getCarhireHost(env = Application.env) {
    let host;
    const ev = env;
    switch (ev) {
      case 'fat':
        host = 'https://m.fat486.qa.nt.ctripcorp.com';
        break;
      case 'uat':
        host = 'https://m.uat.qa.nt.ctripcorp.com';
        break;
      default:
        host = 'https://m.ctrip.com';
    }
    return host;
  }

  static getRentalMaxMonth = () => (Utils.isCtripIsd() ? 6 : 12);

  static getSideToolBizType = () => {
    if (Utils.isCtripIsd()) {
      return SIDE_TOOL_BIZ_TYPE.ISD;
    } else if (Utils.isCtripOsd()) {
      return SIDE_TOOL_BIZ_TYPE.OSD;
    }
    return '';
  };

  static getListVendorShowNum = () => {
    if (Utils.isCtripIsd()) {
      return LIST_SHOW_VENDOR_NUM.ISD;
    } else if (Utils.isCtripOsd()) {
      return LIST_SHOW_VENDOR_NUM.OSD;
    }
    return 3;
  };

  static colorLog = (value, ...args) => {
    /* eslint-disable */
    // @ts-ignore
    // console.log(`%c${value}`, 'color: red', ...args);
  };

  static HMTCid = [58, 720, 3849, 617]; // 港澳台cid
  static isIsdCountry = v =>
    v.country === Country.DEFAULT_COUNTRY || v.isDomestic === true;

  /**
   * 比较前后对象是否相等，用于重复渲染判断控制
   * @param prev
   * @param next
   * @returns
   */
  static compareProps = (prev, next) => {
    let isEqual = true;
    if (!(prev && next)) {
      return false;
    }
    lodashForEach(prev, (value, key) => {
      let objIsEqual = false;
      if (typeof prev[key] === 'object') {
        try {
          objIsEqual = JSON.stringify(prev[key]) === JSON.stringify(next[key]);
        } catch (e) {
          objIsEqual = prev[key] === next[key];
        }
      } else {
        objIsEqual = prev[key] === next[key];
      }
      isEqual = isEqual && objIsEqual;
    });

    return isEqual;
  };

  static compareObject = (prev, next) => {
    if (!(prev && next)) {
      return false;
    }
    return JSON.stringify(prev) === JSON.stringify(next);
  };

  /**
   * 防止重复渲染的object对象
   */
  static EmptyObj = {};

  static EmptyArray = [];

  static getMaskCode = ({
    code = '*',
    length = 4,
  }: {
    code?: string;
    length?: number;
  }) => {
    // fix issue:http://git.dev.sh.ctripcorp.com/open-source/rn_xtaro_car_main/issues/44
    // ''.padStart is not a function. (In '''.padStart(void 0===o?4:o,u)', '''.padStart' is undefined)
    // ''.padStart(length, code);
    return Array(length).fill(code).join('');
  };

  static identifyIdMask = (identifyID: string) => {
    if (!identifyID) return identifyID;

    const maskStr = Utils.getMaskCode({});
    if (identifyID.length == 15 || identifyID.length == 18) {
      return identifyID.substr(0, 8) + maskStr + identifyID.substr(12);
    }
    return identifyID;
  };

  static phoneNumberMask = (phone: string) => {
    let maskPhone = phone.replace(/\s+/g, '') || '';
    if (!maskPhone || maskPhone.length < 11) return phone;
    const maskStr = Utils.getMaskCode({});
    return maskPhone.substring(0, 3) + maskStr + maskPhone.substring(7);
  };

  static getPhoneList = storePhone => {
    if (!storePhone) return [];
    const reg = /\/|;|，|；/;
    const phoneList = storePhone
      .split(reg)
      .map(tel => tel.split('，'))
      .reduce((p, c) => p.concat(c), [])
      .filter(str => str);
    return phoneList;
  };

  static wrapDebounce = (fn, time = 100) => {
    return lodashDebounce(fn, time, {
      leading: true,
      trailing: false,
    });
  };

  static getComponentByChannel = (config, isCommonChannel?: boolean) => {
    let componentKey = COMPONENT_CHANNEL.COMMON;
    if (!!isCommonChannel) return config[componentKey];
    if (Utils.isCtripOsd()) {
      componentKey = COMPONENT_CHANNEL.OSD;
    } else if (Utils.isCtripIsd()) {
      componentKey = COMPONENT_CHANNEL.ISD;
    }

    return config[componentKey] || config[COMPONENT_CHANNEL.COMMON] || noop;
  };

  static toNumber(data) {
    const number = Number(data);
    return typeof number === 'number' && !Number.isNaN(number) ? number : 0;
  }

  // 比较版本号
  // example: v1 7.8.0
  //          v2 7.10
  // result   -1
  //
  // rule     v1===v2     0
  //          v1>v2       1
  //          v1<v2      -1
  // internalVersion: "812.000"
  // version: "8.12.0"
  // compareVersion('8.11.0','8.11')      = 1
  // compareVersion('8.11.0','8.11.0')    = 0
  // compareVersion('8.11.0','8.12')      =-1
  static compareVersion(v1 = '', v2 = '') {
    const pV1 = v1.split('.');
    const pV2 = v2.split('.');
    const len = pV1.length < pV2.length ? pV1.length : pV2.length;
    for (let i = 0; i < len; i++) {
      let a = parseInt(pV1[i], 0);
      let b = parseInt(pV2[i], 0);
      a = Utils.toNumber(a);
      b = Utils.toNumber(b);
      if (a > b) {
        return 1;
      }
      if (a < b) {
        return -1;
      }
    }
    if (pV1.length > pV2.length) return 1;
    if (pV1.length < pV2.length) return -1;
    return 0;
  }

  // 根据当前AppType获取 currentEnv和nextEnv
  // currentEnv=ISD, nextEnv=OSD
  // currentEnv=OSD, nextEnv=ISD
  static getCarEnv(nextAppType?: string) {
    let currentEnv = '';
    let nextEnv = '';
    if (!nextAppType) {
      currentEnv = Utils.isCtripIsd()
        ? COMPONENT_CHANNEL.ISD
        : COMPONENT_CHANNEL.OSD;
      nextEnv =
        currentEnv === COMPONENT_CHANNEL.ISD
          ? COMPONENT_CHANNEL.OSD
          : COMPONENT_CHANNEL.ISD;
    } else {
      nextEnv = Utils.isCtripIsdByType(nextAppType)
        ? COMPONENT_CHANNEL.ISD
        : COMPONENT_CHANNEL.OSD;
      currentEnv =
        nextEnv === COMPONENT_CHANNEL.ISD
          ? COMPONENT_CHANNEL.OSD
          : COMPONENT_CHANNEL.ISD;
    }
    return {
      currentEnv,
      nextEnv,
    };
  }

  static closestDate(date, gap) {
    date = date || dayjs();
    gap = gap || 30;
    const m_date = dayjs(date).startOf('minute');
    const minute = m_date.minute();
    const count = Math.floor(60 / gap);
    if (minute > gap * count) {
      return m_date.clone().add(1, 'hours').minute(0);
    }
    for (let i = 0; i < count; i++) {
      const min = gap * i,
        max = gap * (i + 1);
      if (minute > min && minute < max) {
        if (i + 1 == count) {
          return m_date.clone().add(1, 'hours').minute(0);
        } else {
          return m_date.clone().minute(max);
        }
      }
    }
    return m_date.clone();
  }

  static getUniqRequestKeyWithEnv() {
    return `${AppContext.CarEnv.appType}_${uuid()}`;
  }

  static appendMarkToUniqRequestKey(uniqRequestKey, isNeedCallBack) {
    return `${uniqRequestKey}/${isNeedCallBack}`;
  }

  static getListBatchGroups() {
    if (Utils.isCtripIsd()) {
      return [0];
    } else {
      return [0, 1];
    }
  }

  /**
   * 将qs.parse结果中的数组转换为字符串
   * eg:
   * 输入url：'channelId=1&aid=2&sid=3&channelId=1&aid=2&sid=3'
   * qs.parse 输出：{channelId:['1','1'],aid:['2','2'],sid:['3','3']}
   * composeQsParse输出：{channelId:'1',aid:'2',sid:'3'}
   * */
  static composeQsParse = (url: string) => {
    const query = qs.parse(url);
    Object.keys(query).forEach(key => {
      if (Array.isArray(query[key])) {
        const val = query[key];
        query[key] = val[val.length - 1];
      }
    });
    return query;
  };

  static compatImgUrlWithWebp = (imgUrl: string) => {
    return `${imgUrl}_.webp`;
  };

  /**
   * 根据不同的身份类型校验姓名
   * @param {string} idtype 身份类型
   * @param {string} val 姓名
   */

  static nameForIDCheck(val = '') {
    return (
      /^[\u4e00-\u9fff㑇䶮・·•]{2,25}$/.test(val) &&
      /[^・·•]$/.test(val) &&
      /^[^・·•]/.test(val)
    );
  }

  static nameForIDTypeCheck(idtype, val = '') {
    if (Number(idtype) === 1) {
      return Utils.nameForIDCheck(val);
    }
    return (
      Utils.nameForIDCheck(val) ||
      (/^[A-Z]+[\s・·•][A-Z]+$/.test(val) && /^.{2,50}$/.test(val))
    );
  }

  static changeObject2QueryString(params) {
    var temp = [];
    for (var k in params) {
      // 2022-10-11 过滤掉无效的值
      if (![undefined, null].includes(params[k])) {
        temp.push(k + '=' + params[k]);
      }
    }
    return temp.join('&');
  }

  static getPackageStyle = memoize(
    (styleList: Array<any>) => {
      let style = null;
      styleList.forEach(styleItem => {
        if (styleItem) {
          style = Object.assign({}, style, styleItem);
        }
      });
      return style;
    },
    (newInputs, lastInputs) => {
      if (newInputs.length !== lastInputs.length) {
        return false;
      }

      const newStyleList = newInputs && newInputs[0];
      const lastStyleList = lastInputs && lastInputs[0];

      if (newStyleList.length !== lastStyleList.length) {
        return false;
      }

      for (let i = 0; i < newStyleList.length; i++) {
        if (newStyleList[i] !== lastStyleList[i]) {
          return false;
        }
      }
      return true;
    },
  );
  /**
   * 校验身份证号是否大于多少周岁,小于多少周岁，小于18岁
   * @param {string} idCard 身份证号
   */
  static isInLimitAgeIdCard = (idCard = '', oldAge = 0, yongAge = 0) => {
    let birth = '';
    if (idCard.length == 15) {
      birth = idCard.replace(/^\d{6}(\d{2})(\d{2})(\d{2}).+$/, '19$1-$2-$3');
    } else if (idCard.length == 18) {
      birth = idCard.replace(/^\d{6}(\d{4})(\d{2})(\d{2}).+$/, '$1-$2-$3');
    }
    if (!!birth) {
      const regularAge = 18;
      const time = dayjs(birth);
      // 小于18岁
      let now = dayjs().add(-regularAge, 'year');
      if (time > now) {
        return `驾驶员最小年龄为${regularAge}岁，请更换驾驶员`;
      }
      // 大于oldAge
      if (oldAge > 0 && oldAge > regularAge) {
        now = dayjs().add(-oldAge, 'year');
        if (time < now) {
          return `当前供应商要求，驾驶员最大年龄为${oldAge}岁，请更换驾驶员或选择其他车行`;
        }
      }
      // 小于yongAge
      if (yongAge > 0 && yongAge > regularAge) {
        now = dayjs().add(-yongAge, 'year');
        if (time > now) {
          return `当前供应商要求，驾驶员最小年龄为${yongAge}岁，请更换驾驶员或选择其他车行`;
        }
      }
    }
    return '';
  };

  static getCurPageId = () => {
    const pageIdFunc = AppContext?.PageInstance?.getPageId;
    return pageIdFunc ? pageIdFunc() : '';
  };

  static is835AndAbove = () => {
    return Utils.compareVersion(Application.version, '8.35.0') >= 0;
  };

  static isImageUrl(url) {
    if (typeof url === 'string') {
      return url.match(/\.(jpeg|jpg|gif|png)$/) != null;
    } else {
      return false;
    }
  }

  // 文本超出最大长度，返回最大长度减一拼上点点点
  static handleTextOverflow(text, maxLength) {
    let handledText = text;
    if (text?.length > maxLength) {
      handledText = text.substring(0, maxLength - 1) + '...';
    }
    return handledText;
  }

  static getCheckRealNameUrl = () => {
    const { env } = Application;
    switch (env) {
      case 'dev':
      case 'fat':
        return 'https://secure.fat18.qa.nt.ctripcorp.com/webapp/paywallet/realname';
      case 'uat':
        return 'https://secure.uat.qa.nt.ctripcorp.com/webapp/paywallet/realname';
      case 'prod':
      default:
        return 'https://secure.ctrip.com/webapp/paywallet/realname';
    }
  };

  static getMemberLevelUrl = () => {
    return 'https://m.ctrip.com/webapp/member/newindex?isHideNavBar=YES';
  };

  static getMemberPointsUrl = () => {
    const { env } = Application;
    switch (env) {
      case 'dev':
      case 'fat':
        return 'https://m.fat401.qa.nt.ctripcorp.com/webapp/rewards/mypoint';
      case 'uat':
        return 'https://m.uat.qa.nt.ctripcorp.com/webapp/rewards/mypoint';
      case 'prod':
      default:
        return 'https://m.ctrip.com/webapp/rewards/mypoint?backtype=1';
    }
  };

  static getLogStrValue = value => (value > 0 ? '1' : '0');

  static hasLocationPermissionPromise = async () => {
    return new Promise(resolve => {
      // http://conf.ctripcorp.com/pages/viewpage.action?pageId=724821111
      Permission.checkPermissions({ functions: ['location'] }, result => {
        resolve({ result });
      });
    });
  };

  // 解决Number的toFixed保留小数bug 6.005.toFixed(2) = 6.00
  static toFixed = (num: number, bit: number) => {
    const numStr = num.toString();
    let numValue = Number(num);
    const bitInt = parseInt(bit.toString(), 10);
    const roundArray = [0, -1, -2, -3, -4, 5, 4, 3, 2, 1];
    if (!(numValue > 0) || !(bitInt > 0)) return numValue;
    const decimalIndex = numStr.indexOf('.');
    const roundNum = numStr.substr(decimalIndex + bitInt + 1, 1);
    const times = 10 ** (bitInt + 1);
    if (decimalIndex > -1) {
      numValue = parseInt((numValue * times).toString(), 10);
      return (numValue + roundArray[roundNum]) / times;
    }
    return numValue;
  };

  /**
   * 浮点运算通用方法
   * @param arg1 参数1
   * @param arg2 参数2
   * @param type  type = 0 加  type = 1 减  type = 2 乘  type = 3 除
   * @returns
   */
  static BaseOperation = (arg1, arg2, type) => {
    let r1;
    let r2;
    let m = 0;
    let n = 0;
    try {
      r1 = arg1.toString().split('.')[1].length;
    } catch (e) {
      r1 = 0;
    }
    try {
      r2 = arg2.toString().split('.')[1].length;
    } catch (e) {
      r2 = 0;
    }
    n = Math.max(r1, r2);
    m = 10 ** n;
    const first = Number((arg1 || 0).toString().replace('.', ''));
    const second = Number((arg2 || 0).toString().replace('.', ''));
    switch (type) {
      case 0:
        return ((arg1 * m + arg2 * m) / m).toFixed(n);
      case 1:
        return ((arg1 * m - arg2 * m) / m).toFixed(n);
      case 2:
        return ((first * second) / 10 ** (r1 + r2)).toString();
      case 3:
        return (first / second / 10 ** (r2 - r1)).toString();
      default:
        return NaN;
    }
  };

  static Add = (arg1, arg2) => {
    return Utils.BaseOperation(arg1, arg2, 0);
  };

  static Sub = (arg1, arg2) => {
    return Utils.BaseOperation(arg1, arg2, 1);
  };

  static Multiply = (arg1, arg2) => {
    return Utils.BaseOperation(arg1, arg2, 2);
  };

  static Division = (arg1, arg2) => {
    return Utils.BaseOperation(arg1, arg2, 3);
  };

  // 判断是否为接口异常: 没有error, 或者error中的信息含'502','ack'
  static validateIsServerError = (errorInfo: string) => {
    if (!errorInfo) return true;
    let isFlag = false;
    if (errorInfo) {
      ApiResCode.SERVER_ERROR.forEach(code => {
        if (errorInfo?.toLowerCase()?.includes(code)) {
          isFlag = true;
        }
      });
    }
    return isFlag;
  };

  // 判断Vendor是否已售罄
  static validateIsSaleOut = (saleOutList, vendorPriceInfo) => {
    if (!saleOutList?.length) {
      return false;
    }
    if (
      vendorPriceInfo?.vendorPriceKey &&
      saleOutList.includes(vendorPriceInfo.vendorPriceKey)
    ) {
      return true;
    }
    if (vendorPriceInfo?.reference) {
      const curKey = Utils.getProductKey(vendorPriceInfo?.reference);
      if (saleOutList.includes(curKey)) {
        return true;
      }
    }

    return false;
  };

  // 获取字符串字节长度
  static getByteLength = (str: string) => {
    if (!str?.length) return 0;
    let byteLength = 0;
    for (let i = 0, len = str.length; i < len; i++) {
      if (str.charCodeAt(i) > 255) {
        //字符编码大于255，说明是双字节字符
        byteLength += 2; //则累加2个
      } else {
        byteLength++; //否则递加一次
      }
    }
    return byteLength;
  };

  // 截取指定字节长度内的字符串
  static getByteLengthStr = (str: string, maxByteLength: number) => {
    if (Utils.getByteLength(str) <= maxByteLength) return str;
    let strByteCount = 0;
    let cutStr = '';
    for (let i = 0, len = str.length; i < len; i++) {
      strByteCount += Utils.getByteLength(str.charAt(i));
      if (strByteCount > maxByteLength) {
        cutStr = str.substring(0, i);
        break;
      } else if (strByteCount === maxByteLength) {
        cutStr = str.substring(0, i + 1);
        break;
      }
    }
    return cutStr;
  };
  static getMaxByteStr = (str: string, maxByteLength: number) => {
    if (Utils.getByteLength(str) <= maxByteLength) return str;
    return `${Utils.getByteLengthStr(str, maxByteLength - 2)}...`;
  };

  static isBetween = (begin: string, end: string) =>
    dayjs().isAfter(dayjs(begin)) && dayjs().isBefore(dayjs(end));

  static dayJsUtc = ({ date, utcOffset, format }) => {
    // @ts-ignore
    if (!dayjs.utc || !dayjs().utcOffset) {
      // @ts-ignore
      dayjs.extend(utc);
    }
    // @ts-ignore
    return dayjs(date).utcOffset(utcOffset).format(format);
  };

  static convertToBeijinUtcOffset = time => {
    try {
      // 服务端返回/Date(1678424400000+0800)/,时区不为8，直接使用有差异,用1678424400000来计算时间
      let tempTime = time.match(/\d+/)?.[0] || time;
      // @ts-ignore
      if (!dayjs.utc || !dayjs().utcOffset) {
        // @ts-ignore
        dayjs.extend(utc);
      }
      // @ts-ignore
      return dayjs(parseInt(tempTime)).utcOffset(8);
    } catch (error) {
      return time;
    }
  };

  static getDayDiff = (btime, etime) => {
    const startDate = dayjs(btime);
    const endDate = dayjs(etime);
    if (startDate.valueOf() > endDate.valueOf()) {
      return Math.ceil(startDate.diff(endDate, 'days', true));
    }
    return Math.ceil(endDate.diff(startDate, 'days', true));
  };
  static getBbkImageUrl = memoize((imageName: string) => {
    return `${ImageUrl.BBK_IMAGE_PATH}${imageName}`;
  });

  // 判断是否为融合首页tab拆分后的新版本
  static isNewHomeTab() {
    return (
      AppContext.ABTesting.homeTabInfo.key &&
      AppContext.ABTesting.homeTabInfo.val === 'B'
    );
  }

  // 判断是否可以打开融合首页置顶模块
  static isShowHomeHeader() {
    return (
      Utils.isNewHomeTab() &&
      AppContext.isHomeCombine &&
      Utils.compareVersion(Application.version, '8.60.0') >= 0
    );
  }

  // 获取无忧租落地页
  static getEasyLifeLandingURL() {
    let url = '';
    if (Utils.isCtripIsd()) {
      url = EASYLIFE_LANDING_URL.ISD;
    } else {
      url = EASYLIFE_LANDING_URL.OSD;
    }
    return `${Utils.getHost()}${url}`;
  }

  static isValid = (params: Array<string | number> | any) => {
    if (Array.isArray(params)) {
      return (
        !params.includes(undefined) &&
        !params.includes('') &&
        !params.includes(null)
      );
    }
    return params !== undefined && params !== '' && params !== null;
  };

  static isCoordinateValid = (coordinate: any) => {
    const invalidValue = [undefined, '', null, 0];
    return !invalidValue.includes(coordinate);
  };

  static isEqual = (reqParam: string | number, stateParam: string | number) =>
    `${reqParam}` === `${stateParam}`;

  static isToNumberEqual = (
    reqParam: string | number,
    stateParam: // 如果两个值都为undefined或者null时，会被判定为相等，该场景无需重复捕获异常（会被KEY_PARAMETERS_MISSING作为异常捕获）
    string | number,
  ) => reqParam === stateParam || Number(reqParam) === Number(stateParam);

  static isRequestParamCoincident = (requestInfo, state) => {
    if (!requestInfo || !state) return false;
    const {
      pDate,
      rDate,
      pLatitude,
      pLongitude,
      pickupLocationName,
      rLatitude,
      rLongitude,
      returnLocationName,
    } = requestInfo || {};
    const { rentalDate = {}, rentalLocation = {} } = state || {};
    const pickUpArea = rentalLocation?.pickUp?.area;
    const dropOffArea = rentalLocation?.dropOff?.area;
    const timeFormat = 'YYYYMMDDHHmmss';

    return (
      Utils.isEqual(
        pDate,
        dayjs(rentalDate?.pickUp?.dateTime).format(timeFormat),
      ) &&
      Utils.isEqual(
        rDate,
        dayjs(rentalDate?.dropOff?.dateTime).format(timeFormat),
      ) &&
      Utils.isToNumberEqual(pLatitude, pickUpArea?.lat) &&
      Utils.isToNumberEqual(pLongitude, pickUpArea?.lng) &&
      Utils.isEqual(pickupLocationName, pickUpArea?.name) &&
      Utils.isToNumberEqual(rLatitude, dropOffArea?.lat) &&
      Utils.isToNumberEqual(rLongitude, dropOffArea?.lng) &&
      Utils.isEqual(returnLocationName, dropOffArea?.name)
    );
  };

  static composeError2String(error?: Error | Object) {
    if (lodashIsError(error)) {
      return JSON.stringify(error, Object.getOwnPropertyNames(error));
    }
    return JSON.stringify(error);
  }

  static getErrorMessage(error?: Error | any) {
    if (lodashIsError(error)) {
      return error?.message;
    }
    return JSON.stringify(error);
  }

  static getIsTimeOut(response: any, error: Error | any): boolean {
    const errorStr = Utils.getErrorMessage(error);
    const isTimeout =
      timeOutMessageList.findIndex(f => errorStr?.includes(f)) > -1;
    // 进catch时,response会为空, 若同时error信息也为空，则视为请求超时
    return (!response && !error) || isTimeout;
  }

  static getFrontEndExpCode(response, error, latest = false) {
    const isTimeOut = Utils.getIsTimeOut(response, error);
    if (isTimeOut) {
      return latest ? ApiResCode.TraceCode.E1005 : ApiResCode.TraceCode.E1004;
    }
    return ApiResCode.TraceCode.E1001;
  }

  // 产品详情页openUrl下一个产品详情页storage的关联id
  static openUrlVendorListPageUniqueId() {
    return (
      AppContext.UrlQuery.initialPage === Channel.getPageId().VendorList.EN &&
      AppContext.UrlQuery.pageUniqueId
    );
  }

  static getProductKey(reference) {
    if (!reference) {
      xUbt.logDevTrace(LogKeyDev.c_car_dev_not_reference, {
        key: LogKeyDev.c_car_dev_not_reference,
        info: {
          name: 'notReference',
        },
      });
      return '0_0';
    }
    const {
      skuId = 0,
      packageType = 0,
      vehicleCode,
      bizVendorCode,
      pStoreCode,
      rStoreCode,
      isVehicle2,
      vehicleKey,
      pLev = -1,
      rLev = -1,
      originPsType,
      productId,
    } = reference;
    let key = '';
    if (Utils.isCtripOsd()) {
      if (isVehicle2) {
        key = `${vehicleKey}_${bizVendorCode}_${packageType}_${pStoreCode}_${rStoreCode}_${pLev}_${rLev}`;
      } else {
        key = `${vehicleCode}_${bizVendorCode}_${packageType}_${pStoreCode}_${rStoreCode}_${pLev}_${rLev}`;
      }
    } else {
      key = `${skuId}_${packageType}_${originPsType}_${productId}`;
    }
    return key;
  }

  static getLabelTraceInfo = (labels?: Array<any>) => {
    const labelInfo = [];
    labels?.forEach(label => {
      if (label?.labelCode) {
        labelInfo.push({
          tagCode: label?.labelCode,
          tagName: label?.title,
          tagType: label?.category,
        });
      }
    });
    return labelInfo;
  };

  /**
   * 获取时间组件单位修正分钟数
   * 单位30分钟，向上取整，如果当前时间为 2022/10/01 10:11，则该函数返回的时间为 2022/10/01 10:30
   * @param time 当前时间
   * @returns
   */
  static fixAddMinuteGap = (time: Dayjs, format = 'YYYY-MM-DD HH:mm:ss') => {
    const roundedMinutes = Math.ceil(time.minute() / 30) * 30;
    const roundedTime = time.minute(roundedMinutes).second(0).millisecond(0);
    return roundedTime.format(format);
  };

  // 匹配座机号和分机号以,分隔的形式
  static isFixedTelephone(number: string) {
    const regex = /^\d{11,12},\d{4,6}$/;
    return regex.test(number);
  }
  // 如果是座机号,分机号的形式加#号可以加快分机号收号速度
  static getGoodFixedTelephone(number: string) {
    if (this.isFixedTelephone(number) && Utils.isAndroid) {
      return encodeURIComponent(`${number}#`);
    }
    return number;
  }

  // 检测滚动是否到达第x个位置
  static isScrollReachPos = (pos, offsetY) => {
    const POS_DIFF = 80; // 滚动误差
    return pos - POS_DIFF < offsetY && offsetY < pos + POS_DIFF;
  };

  static stringToJson = str => {
    try {
      return JSON.parse(str);
    } catch {
      return str;
    }
  };

  static getImageSize = (url, successCallback, errorCallback) => {
    try {
      if (url) {
        imageGetSize?.(url, successCallback, errorCallback);
      }
    } catch (error) {
      xUbt.logDevTrace(ErrorKey.e_image_getSize, {
        error,
        imageUrl: url,
        expCode: ApiResCode.TraceCode.E1004,
      });
    }
  };

  // 安卓图片预加载
  static imagePrefetchForAndroid = (image: string) => {
    const successCallback = () => {};
    const errorCallback = error => {
      xUbt.logDevTrace(LogKeyDev.c_car_dev_trace_image_prefetch_fail, {
        key: LogKeyDev.c_car_dev_trace_image_prefetch_fail,
        info: {
          error,
          image,
          isAndroid: Utils.isAndroid,
        },
      });
    };
    try {
      // @ts-ignore
      Image?.getSize(image, successCallback, errorCallback); // 安卓框架对getSize做了图片域名收敛和添加后缀，可以用于图片预加载
    } catch (error) {
      errorCallback(error);
    }
  };

  // 图片预加载
  static imagePrefetch = (images: string[]) => {
    // 图片预加载埋点
    xUbt.logDevTrace(LogKeyDev.c_car_dev_trace_image_prefetch, {
      key: LogKeyDev.c_car_dev_trace_image_prefetch,
      info: {
        isAndroid: Utils.isAndroid,
        curPageId: Utils.getCurPageId(),
        prefetchImages: images,
      },
    });
    return images?.map(image => {
      if (Utils.isAndroid) {
        return Utils.imagePrefetchForAndroid(image);
      }
      return ImagePrefetch?.(image)?.catch(error => {
        xUbt.logDevTrace(LogKeyDev.c_car_dev_trace_image_prefetch_fail, {
          key: LogKeyDev.c_car_dev_trace_image_prefetch_fail,
          info: {
            error,
            image,
            isAndroid: Utils.isAndroid,
          },
        });
      });
    });
  };
  static isJSONString(str) {
    try {
      const parsed = JSON.parse(str);
      return typeof parsed === 'object' && parsed !== null;
    } catch (e) {
      return false;
    }
  }

  static splitNumber(str) {
    const allNumber = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const result = [];
    let isHasNumber = false;
    let isNumber;
    let index = -1;
    for (let cIndex in str) {
      const item = str[cIndex];
      if (allNumber.includes(item)) {
        isHasNumber = true;
        isNumber = true;
      } else {
        isNumber = false;
      }
      if (result[index]?.isNumber !== isNumber) {
        result.push({
          isNumber,
          text: item,
        });
        index += 1;
      } else {
        result[index].text += item;
      }
    }
    return {
      result,
      isHasNumber,
    };
  }
}

// @ts-ignore
global.colorLog = Utils.colorLog;
// @ts-ignore
global.compareProps = Utils.compareProps;

export default Utils;
