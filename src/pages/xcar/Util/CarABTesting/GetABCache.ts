import Log from '@c2x/apis/Log';
import {
  ABKey,
  ABValueType,
  GetABValueType,
  GetAllCacheABValueType,
} from './ABKey';
import AppContext from '../AppContext';
import Utils from '../Utils';
import CarStorage from '../CarStorage';
import StorageKey from '../../Constants/StorageKey';
import LogKeyDev from '../../Constants/LogKeyDev';
import { KeyType } from './Types';

const CACHE_AB_EXPIRES = '365d';

/**
 * 异步缓存ab实验结果
 */
export const cacheAB = async (keys: Array<KeyType>) => {
  if (keys?.length > 0) {
    const resultList = {};
    // 获取需要缓存的ab实验结果
    keys.forEach(key => {
      const abValue = GetABValueType(key.expCode);
      // 需要缓存，则添加
      if (abValue?.isCache) {
        resultList[key.expCode] = AppContext.ABTesting?.datas?.[key.expCode];
      }
    });

    // 如果有需要缓存的ab实验结果
    if (Object.keys(resultList).length > 0) {
      // 读取缓存ab实验
      const abCacheStr = await CarStorage.loadAsync(
        StorageKey.CAR_CACHE_AB_RESULT,
      );
      let abCache = null;
      try {
        abCache = JSON.parse(abCacheStr);
      } catch (e) {
        abCache = {};
      }
      CarStorage.save(
        StorageKey.CAR_CACHE_AB_RESULT,
        JSON.stringify({ ...abCache, ...resultList }),
        CACHE_AB_EXPIRES,
      );
    }
  }
};

/**
 * 同步读取缓存ab实验结果
 */
export const syncCacheAb = () => {
  const cacheABList = GetAllCacheABValueType();
  if (cacheABList?.length > 0) {
    try {
      const abCacheStr = CarStorage.loadSync(StorageKey.CAR_CACHE_AB_RESULT);
      const abCache = JSON.parse(abCacheStr);
      const syncCache: any = {};
      cacheABList.forEach(abItem => {
        if (abCache[abItem.key]) {
          syncCache[abItem.key] = abCache[abItem.key];
        }
      });
      if (Object.keys(syncCache).length > 0) {
        AppContext.setABTesting(syncCache);
      }
      Log.logDevTrace(LogKeyDev.c_car_trace_sync_ab, {
        key: LogKeyDev.c_car_trace_sync_ab,
        info: {
          eventResult: !!syncCache,
          abKeys: cacheABList,
          abCache,
          abResult: syncCache,
        },
      });

      // eslint-disable-next-line no-empty
    } catch (e) {
      Log.logDevTrace(LogKeyDev.c_car_trace_sync_ab, {
        key: LogKeyDev.c_car_trace_sync_ab,
        info: {
          eventResult: false,
          expMsg: e?.message,
        },
      });
    }
  }
};

// 从AppContext存储的实验版本数据判断是否是新版
export const isNewABVersionFromCache = (ABInfo: ABValueType) => {
  return ABInfo.newVersionCode.includes(
    AppContext.ABTesting?.datas?.[ABInfo.key]?.ExpVersion,
  );
};

export const isNewCVersionFromCache = (ABInfo: ABValueType) => {
  return AppContext.ABTesting?.datas?.[ABInfo.key]?.ExpVersion === 'C';
};

export const isNewBVersionFromCache = (ABInfo: ABValueType) => {
  return AppContext.ABTesting?.datas?.[ABInfo.key]?.ExpVersion === 'B';
};

// 境外列表页二批重刷
export const isOSDListAllRefresh = () =>
  Utils.isCtripOsd() && isNewABVersionFromCache(ABKey.OSDListAllRefresh);

// 门店信息问卷
export const isStoreInfoSurvey = () =>
  Utils.isCtripOsd() && isNewABVersionFromCache(ABKey.storeInfoSurvey);

// 海外排序对接AI
export const isAiSort2 = () =>
  Utils.isCtripOsd() && isNewABVersionFromCache(ABKey.aiSort2);

// 国内超级补贴活动
export const isSuperSubsidy = () =>
  Utils.isCtripIsd() && isNewABVersionFromCache(ABKey.superSubsidy);

// 国内列表页ipoll
export const isISDListIpoll = () =>
  Utils.isCtripIsd() && isNewABVersionFromCache(ABKey.ISDListIpoll);

// 国内详情页ipoll
export const isISDProductIpoll = () =>
  Utils.isCtripIsd() && isNewABVersionFromCache(ABKey.ISDProductIpoll);

// 出境列表页ipoll
export const isOSDListIpoll = () =>
  Utils.isCtripOsd() && isNewABVersionFromCache(ABKey.OSDListIpoll);
// 出境详情页ipoll
export const isOSDProductIpoll = () =>
  Utils.isCtripOsd() && isNewABVersionFromCache(ABKey.OSDProductIpoll);

// 出境新版订详
export const isOSDOrderDetail = () =>
  Utils.isCtripOsd() && isNewABVersionFromCache(ABKey.OSDOrderDetail);

// 货架三期填写页改版
export const isISDShelves3 = () =>
  Utils.isCtripIsd() && isNewABVersionFromCache(ABKey.isISDShelves3);

// 货架三期C版
export const isISDShelves3C = () =>
  Utils.isCtripIsd() && isNewCVersionFromCache(ABKey.isISDShelves3);
