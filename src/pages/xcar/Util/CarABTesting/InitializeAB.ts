// 函数命名规范：[prefix]+[description]
//      initializeABOrderDetail
//      initializeAB...
//      initializeAB...

import { Utils } from '../Index';
import { ABKey } from './ABKey';
import { getABTestingInfoSync } from './ABTesting';
import { InitialABKeys, KeyType } from './Types';

/**
 * 页面批量初始化实验
 * @param abKeys 初始化实验列表
 * @returns 实验结果列表
 */
export const initializeABPage = (abKeys: InitialABKeys) => {
  const { keys, isdKeys, osdKeys } = abKeys;
  let result = [];
  let isdResult = [];
  let osdResult = [];
  if (keys?.length > 0) {
    result = getABTestingInfoSync(keys);
  }
  if (isdKeys?.length > 0 && Utils.isCtripIsd()) {
    isdResult = getABTestingInfoSync(isdKeys);
  }
  if (osdKeys?.length > 0 && Utils.isCtripOsd()) {
    osdResult = getABTestingInfoSync(osdKeys);
  }
  return [...result, ...isdResult, ...osdResult];
};

// 应当在发送埋点前，constructor中调用

// 境外首页&列表页都开启分流的实验
// 重要：修改此处代码时，必须同时修改首页包实验，否则导致预请求失效
// 代码位置：https://git.dev.sh.ctripcorp.com/car/rn_car_home_osd/-/blob/master-070/src/Util/CarABTesting/InitializeAB.ts?ref_type=heads#L31
export const osdHomeAndListABKeys = [
  { expCode: ABKey.OSDListAllRefresh.key },
  { expCode: ABKey.aiSort2.key },
  { expCode: ABKey.OSDListIpoll.key },
];

// 境外列表页非页面初始化key
export const osdListConditionABKeys = [];

// 首页实验批量初始化
export const initializeABHomePage = () => {
  const keys: KeyType[] = [];
  const isdKeys: KeyType[] = [{ expCode: ABKey.ISDHomeFlowOptimize.key }];
  const osdKeys: KeyType[] = osdHomeAndListABKeys;
  return initializeABPage({
    keys,
    isdKeys,
    osdKeys,
  });
};

// 列表页实验批量初始化
export const initializeABListPage = () => {
  const keys: KeyType[] = [];
  const isdKeys: KeyType[] = [
    { expCode: ABKey.ISDListIpoll.key },
    { expCode: ABKey.isISDShelves3.key },
  ];
  const osdKeys: KeyType[] = osdHomeAndListABKeys;
  return initializeABPage({
    keys,
    isdKeys,
    osdKeys,
  });
};

// 填写页实验批量初始化
export const initializeABBookingPage = () => {
  const keys: KeyType[] = [];
  const isdKeys: KeyType[] = [];
  const osdKeys: KeyType[] = [];
  return initializeABPage({
    keys,
    isdKeys,
    osdKeys,
  });
};

// 详情页实验批量初始化
export const initializeABVendorListPage = () => {
  const keys: KeyType[] = [];
  const isdKeys: KeyType[] = [
    { expCode: ABKey.ISDProductIpoll.key },
    { expCode: ABKey.isISDShelves3.key },
  ];
  return initializeABPage({
    keys,
    isdKeys,
  });
};

// 境外详情页批量初始化
export const initializeABProductPage = () => {
  const keys: KeyType[] = [{ expCode: ABKey.showStandardImg.key }];
  const isdKeys: KeyType[] = [];
  const osdKeys: KeyType[] = [{ expCode: ABKey.OSDProductIpoll.key }];
  return initializeABPage({
    keys,
    isdKeys,
    osdKeys,
  });
};

// 城市区域搜索页
export const initializeABLocationPage = () => {
  const keys: KeyType[] = [];
  const isdKeys: KeyType[] = [];
  const osdKeys: KeyType[] = [];
  return initializeABPage({
    keys,
    isdKeys,
    osdKeys,
  });
};

// 订单详情页
export const initializeABOrderDetailPage = () => {
  const keys: KeyType[] = [];
  const isdKeys: KeyType[] = [];
  const osdKeys: KeyType[] = [{ expCode: ABKey.storeInfoSurvey.key }];
  return initializeABPage({
    keys,
    isdKeys,
    osdKeys,
  });
};

// 地图指引页
export const initializeABGuidePage = () => {
  const keys: KeyType[] = [];
  const isdKeys: KeyType[] = [];
  const osdKeys: KeyType[] = [];
  return initializeABPage({
    keys,
    isdKeys,
    osdKeys,
  });
};
