import { xApplication as Application } from '@ctrip/xtaro';
// 函数命名规范：[prefix]+[description]
//      getAbVerOrderCardShowTip => string (A|B|C|D...)
//      isNewBookingCoupon => boolean
//      getAbVer...
//      isNew...

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Utils from '../Utils';
import { ABKey, ABValueType } from './ABKey';
import { getABTestingInfoSync } from './ABTesting';

const { versionCompare } = BbkUtils;

const isNewABVersion = (ABInfo: ABValueType) => {
  const [AB] = getABTestingInfoSync({ expCode: ABInfo.key });
  return ABInfo.newVersionCode.includes(AB.ExpVersion);
};

// 是否是特定版本
export const isEqualABVersion = (ABInfo: ABValueType, abVersions: string[]) => {
  const [AB] = getABTestingInfoSync({ expCode: ABInfo.key });
  return abVersions.includes(AB.ExpVersion);
};

// 供应商详情页海外售卖保险AB实验结果

// ！！！注意：多个新版须按照上面注释中的写法区分不同新版，上面的命名是既有代码改造，新增代码不要这样命名！！！
// 非初始化获取AB版本，改用GetABCache文件方法

// 首页用户分层，APP版本 >= 8.59.6
export const isHomeUserLayering = () =>
  versionCompare(Application.version, '8.59.6') > -1;

// 首页用户分层二期，APP版本 >= 8.59.6
export const isHomeUserLayering2BVer = () =>
  versionCompare(Application.version, '8.59.6') > -1 && Utils.isCtripIsd();

//  海外提升成交率，新版取消订单页面
export const isOSDNewOrderCancel = isKlbVersion =>
  Utils.isCtripOsd() && isKlbVersion;

// 车型卡片车图缩小以及门座标签合并
export const isISDInterestPoints = () => Utils.isCtripIsd();

// 首页用户分流优化
export const isISDHomeFlowOptimize = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.ISDHomeFlowOptimize);

export const isISDProductBack = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.ISDProductBack);

export const isOSDProductBack = () =>
  Utils.isCtripOsd() && isNewABVersion(ABKey.OSDProductBack);

// 国内列表页ipoll
export const isISDListIpoll = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.ISDListIpoll);

// 出境列表页ipoll
export const isOSDListIpoll = () =>
  Utils.isCtripOsd() && isNewABVersion(ABKey.OSDListIpoll);

// 出境新版订详
export const isOSDOrderDetail = () =>
  Utils.isCtripOsd() && isNewABVersion(ABKey.OSDOrderDetail);

export const isISDHomeQiwei = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.ISDHomeQiwei);

export const isLoginAB = () => isNewABVersion(ABKey.loginAB);

// 国内超级补贴活动
export const isSuperSubsidy = () => isNewABVersion(ABKey.superSubsidy);
