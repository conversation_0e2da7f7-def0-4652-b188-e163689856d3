import { get as lodashGet } from 'lodash-es';
import Device from '@c2x/apis/Device';
import { xApplication as Application } from '@ctrip/xtaro';

import uuid from 'uuid';
import Utils from '../Utils';
import AppContext from '../AppContext';
import * as CarABTesting from '../ABTesting';
import * as GetABCache from '../CarABTesting/GetABCache';
import * as GetAB from '../CarABTesting/GetAB';
import { getPlatform } from '../Environment';
import { Platform } from '../../Constants/Index';
import * as ProductSelectors from '../../Global/Cache/ProductSelectors';
import * as ListResSelectors from '../../Global/Cache/ListResSelectors';
import { isCreditRent, isListInPage, packageAbversion } from '../ABTesting';
import CreateChannelCode from '../channelCode';
import Channel from '../Channel';
import { getStore } from '../../State/StoreRef';

const getBaseRequest = ({
  requestId = uuid(),
  parentRequestId = '',
  channelType = 7,
  appType,
  invokeFrom = '',
  pageId = '',
  insVersion = '1',
  extraMaps = {},
  listExtraMaps = {},
  orderId = '',
  extraTags = {},
}) => {
  const { latitude, longitude }: any = Device.deviceInfo || {};
  const state = getStore()?.getState();

  const sourceFrom =
    appType || AppContext.CarEnv.appType || Platform.APP_TYPE.IBU_APP;
  const channelCodes = CreateChannelCode({});
  const isOrderDetail =
    AppContext?.PageInstance?.getPageId?.() === Channel?.getPageId()?.Order?.ID;
  // 是否是无忧组2022版本：售后根据服务字段返回，售前默认是2022版本
  const isEasyLife2022 = isOrderDetail
    ? state?.OrderDetail?.extendedInfo?.noWorryVersion === 2
    : true;

  return {
    sourceFrom,
    channelType,
    requestId,
    invokeFrom,
    parentRequestId,
    platform: getPlatform(),
    site: AppContext.LanguageInfo.site,
    language: AppContext.LanguageInfo.language,
    locale: AppContext.LanguageInfo.locale,
    currencyCode: AppContext.LanguageInfo.currency,
    sourceCountryId: state?.CountryInfo?.countryId || '',
    channelId: Number(AppContext.MarketInfo.channelId),
    clientVersion: AppContext.CarEnv.buildTime,
    clientid: lodashGet(Device, 'deviceInfo.clientID') || '',
    vid: AppContext.MarketInfo.vid,
    patternType: Utils.getBusinessType(),
    mobileInfo: {
      customerGPSLat: Number(latitude) || 0,
      customerGPSLng: Number(longitude) || 0,
      mobileModel: `${Device.deviceType}`,
      wirelessVersion: `${Application.version}`,
    },
    allianceInfo: {
      allianceId: Number(AppContext.MarketInfo.aId) || 0, // 为number类型
      ouid: '1',
      sid: Number(AppContext.MarketInfo.sId) || 0,
      distributorUID: '1',
    },
    extraMaps: {
      batchVersion: '', // 国内列表页分批加载标记
      // todo-xt 字段需要规范成listPageVersion, 待服务端修正后接入
      pageVersion: isListInPage() ? 'true' : '', // 国内列表页分页加载标记
      depositVersion: isCreditRent() ? '1.0' : '',
      creditVersion: ProductSelectors.getCreditVersion() || '',
      abVersion: Utils.isCtripIsd()
        ? AppContext.ABTesting.trace
        : packageAbversion(),
      partialVersion: AppContext.CarEnv.buildTime,
      queryProductsVersion: 'cashbackDemand',
      crnVersion: '37', // 参考 http://conf.ctripcorp.com/pages/viewpage.action?pageId=499951799
      queryVid: AppContext.UserTrace.queryVid,
      sourceFrom,
      platform: getPlatform(),
      onePost: '1', // 标志列表页国内只发送一批请求
      ctripVersion: 1, // 1表示保险合规上线
      insVersion, // 1表示售前支付按钮在选购自营险的情况下展示成'提交订单'
      encryptUid: AppContext.encryptUid, // security Uid 加密后的UID, 该值不为空时，代表是OP代客人操作
      fromType: AppContext.fromType, // 类似fromUrl, 代表操作类别，1=修改订单，2=代客下单
      originOrderId: AppContext.originOrderId, // 原OrderId(用于修改订单)
      channelId: AppContext.channelId, // 修改订单与代客下单分别会有不同的渠道号
      eid: AppContext.eid, // 后台登入的OP人员的Id(EID)
      commentVersion: 1, // 1表示点评接入中台上线
      ehaiDepositVersion: '1.0', // 一嗨双免
      priceUnitedVersion: Utils.isCtripIsd() ? '2' : '',
      // 权益/营销活动/无忧租B版保持与A版一致
      directRenewalVersion: Utils.isCtripIsd() ? '1' : '', // 直连续租
      tangramAbt: 'B', // 新列表页样式
      snapshotVersion: 'v4', // 交易快照
      rentCenter: '1',
      detailPageVersion: '2', // 重构版本,1:新版填写页 2:新版详情页&新版填写页
      poiProject: 'B', // POI项目标识, Value = [A,B,C,D,'']
      goodsShelves: '2',
      membershipRightsV: '1',
      feeGroup: Utils.isCtripIsd() ? '1' : '', // 订单详情页费用明细是否分组展示的标记
      isFilterPrice: Utils.isCtripIsd() ? '1' : '', // 列表页是否精减vendorPriceList节点标记
      payOnlineVersion: '1', // 订详在线付押金
      aboutToTravelVersion: '1', // 行程卡迭代GS46
      karabiVersion: '1', // 卡拉比项目标识，前端无业务逻辑，只给服务端用于起始版本判断
      isNewRecommend: '1', // 是否支持无少结果推荐，1为支持
      orderDetailRestStruct: Utils.isCtripIsd() ? '1' : '', // 订详整体优化
      insuranceDetail: Utils.isCtripIsd() ? 4 : 0,
      vehicleDamageAuditVersion: '1', // 车损项目
      receiveCoupon: '1', // 领券订标识
      filterProject: 'B', // 筛选升级切B版
      poiNewVersion: '2', // 两车poi项目标记
      rankingVersion: '2', // 2 榜单新版-读取其他用户搜索缓存
      groupNameVersion: Utils.isCtripIsd() ? '1' : '', // 如果是B版用户传递1获取4个字车型组(经济轿车、舒适轿车、豪华轿车)
      lateDepositVer: Utils.isCtripIsd() || isOrderDetail ? '3' : '1', // 免押后置
      serverRequestId: AppContext.UserTrace.serverRequestId, // 服务端传给前端的唯一标识（用于串联排序、曝光、点击和下单埋点）
      streamVersion: CarABTesting.isInfoFlow(),
      telVersion: '1', // storeTel分隔符使用'/'
      orderDetailCallBack: 1,
      newPayment: 1, // 新中台支付版本
      labelOptimizeVer: '', // 新版标签
      openAwardVersion: Utils.isCtripIsd() ? 1 : 0, // 订详新版点评按钮添加赚取积分激励
      ...channelCodes,
      ...extraMaps,
      orderId,
      isNewSearchNoResult: '1', // 新版搜索无结果
      isFilterRecommend: '1', // 新版筛选无结果透传参数
      isNewEnergy: '1', // 新能源车型展示
      filterPoi: AppContext.filterPoi, // POI 屏蔽
      klbDataFlag: 1, // 卡拉比3期标识
      osdKlbDataFlag: '1', // 卡拉比4期标识
      mdtcy: 1, // 新版优选弹窗标签同步标识
      easyLifeVersion: isEasyLife2022 ? '1' : '0', // 无忧组2022版本
      earlyReturnVersion: 1, // 提前还车版本
      licensePlateVersion: 2, // 列表页牌照类型聚合展示项目
      osdDepositFree: '1', // 出境免押
      secretBoxVersion: 1, // 盲盒项目
      osdCtripIns: '2', // 区分海外与trip自营险的样式 自营险二期传2
      vendorListVrAB2: '1', // 标识VR2链接实验
      osdProductFailGuide: '1', // 标识是境外详情页失败优化项目
      indexUser: '1', // 用户分层二期
      cancelInfoVersion: '1', // 海外挽留项目中确认取消违约金添加高亮字段标识
      selfService: '2', // 自助取还标识
      selfServiceSwitch:
        ListResSelectors.getSelfServiceSwitch() ||
        AppContext.selfServiceSwitch ||
        '', // 自助取还开关
      multimediaVersion: '1', // 多媒体二期版本标识字段
      flightPrevilegeV: Utils.isCtripOsd() ? '2' : '0', // 机票二期
      meetingpoint: Utils.isCtripIsd() ? '1' : '0',
      osdRecommendVersion: Utils.isCtripOsd() ? '1' : '0', // 无结果推荐
      isCarModelSortingOptimization: '1', // 列表页二批重刷
      isListNew: Utils.isCtripOsd() ? '1' : '0', // 境外性能优化提升项目
      osdNewDetailPage: Utils.isCtripOsd() ? '1' : '0', // 出境详情页优化2期-取车材料版本标识
      osdProductInfoOpt: Utils.isCtripOsd() ? '1' : '0',
      indexStreamVersion: GetAB.isISDHomeFlowOptimize() ? '1' : '0', // 首页信息流优化
      confirmABFlag: Utils.isCtripOsd() ? '1' : '0', // 境外确认政策优化项目
      equipmentNewVersion: '1', // 额外设备 AB 下线
      // 非初始化获取AB值使用 GetABCache
      osdAddOnChoice: '1',
      osdVehicleRefactor: Utils.isCtripOsd() ? '1' : '0', // 海外车型重构二期
      onlineInvoicingVer: Utils.isCtripIsd() ? '1' : '0', // 国内开票线上化
      osdContactTemplates: Utils.isCtripOsd() ? '1' : '0', // 供应商多语言合同模板
      packageLevelVersion: Utils.isCtripIsd() ? '1' : '0', // 无忧租2024
      packageLevelSwitch: AppContext.easyLifeSwitch || '0', // 无忧租2024屏蔽普通无忧租的标志
      nonJumpFlow:
        Utils.isCtripIsd() && AppContext.UrlQuery.nonJumpFlow === '1'
          ? '1'
          : '0', // 无忧租一口价车型组置顶标志，取值来自URL，列表页和queryVehicleDetailList接口共同使用
      ...listExtraMaps,
      newOsdVoucher: '1', // 海外提车凭证项目
      newOsdVoucherV2: '1', // 提车凭证V2
      osdGuideStep: '1', // 海外取车指引订后guideStep字段返回依据
      goodsShelvesAB: 'C', // 国内货架一期前端实验结果
      pickUpServiceVersion: '1', // 海外送车上门
      photoUploadPhase2Flag: '1', // 海外拍照二期
      osdNoResultCompVersion: '1', // 无结果补偿聚合项目标识
      zeroPay: '1', // 支持0元下单标识
      abg: '1', // abg 免押项目标识
      goodsShelvesTwoVersion: '1', // 货架二期标识
      goodsShelvesTwoSwitch: AppContext.goodsShelvesTwoSwitch || '0', // 货架二期 ab 结果
      goodsShelvesTwoABVersion: AppContext.goodsShelvesTwoABVersion || '', // 货架二期 ab版本
      isReduceDuplicateVehicleCardVersion: '1', // 减少重复车型卡片2期项目
      osdCreditDepositFree: 1, // 海外信用租标识
      searchSuggestVersion: '1', // 车型选择优化1期-列表页搜筛
      goodsShelvesThreeSwitch: GetABCache.isISDShelves3() ? '1' : '0', // 货架三期 ab 结果
      isAiSort2: GetABCache.isAiSort2() ? '1' : '0', // 海外排序对接AI
    },
    extraTags: {
      abVersion: AppContext.ABTesting.trace, // 订单详情兼容透传abVersion
      ctripVersion: 1, // 1表示保险合规上线-订详
      commentVersion: 1, // 1表示点评接入中台上线
      poiProject: 'B', // POI项目标识, Value = [A,B,C,D,'']
      aboutToTravelVersion: '1', // 行程卡迭代GS46
      poiNewVersion: '2', // 两车poi项目标记
      openAwardVersion: Utils.isCtripIsd() ? 1 : 0, // 订详新版点评按钮添加赚取积分激励
      filterPoi: AppContext.filterPoi, // POI 屏蔽
      meetingpoint: Utils.isCtripIsd() ? '1' : '0',
      meetingPointVersion: Utils.isCtripIsd() ? '1' : '0', // 汇合点版本支持
      osdNewDetailPage: '1', // 出境详情页优化2期-取车材料版本标识 详情页版本标识，历史原因，要求传2次
      manOrderFlag: '1', // '1':合并接口调用只需订单号的接口；'2':合并接口调用需要依赖订详信息的返回
      ...extraTags,
    },
    extMap: {},
    pageId,
  };
};

export default getBaseRequest;
