// 埋点注册地址: http://cdataportal.ctripcorp.com/acq/register
// 埋点注册地址:http://traceinfo.ctripcorp.com/
// 埋点规范(字段已和BI确认，埋点时需要按规范编写字段):
//  http://conf.ctripcorp.com/display/CAR/Log+Design
const LogKey = {
  CLICK_KEY: 'c_rn_car_app_click', // 点击埋点
  c_rn_car_trace_click: 'c_rn_car_trace_click', // trace 点击埋点
  EXPOSURE_EKY: 'c_car_trace_exposure', // 通用曝光埋点key
  c_car_trace_page_active_time: '129950', // 页面激活时间埋点,Trace/上注册Key [对应trace key 113120]
  c_car_trace_list_each_res: '122790', // 分批次请求埋点
  c_car_trace_list_final_res: '122791', // 搜索无结果
  c_car_trace_list_exposure: '129618', // 曝光埋点
  c_car_trace_list_vehicle_exposure_222013: '164281', // 一级车型曝光埋点
  c_car_trace_list_vehicle_exposure_new: '203765', // 新一级车型曝光埋点 todo-xt
  c_car_trace_list_layer_exposure_222013: '164220', // 弹层曝光埋点
  c_car_page_search: '124472',
  c_car_trace_create_order: '129582', // 下单埋点
  c_car_trace_sesame_credit: '129583', // 【预留】芝麻免押金
  c_car_trace_payment: '129584', // 支付信息
  c_car_trace_page_useable: '129585', // 【预留】页面可用性
  c_car_trace_page_pre_load: '129586', // 【预留】缓存预加载
  c_car_trace_page_cache: '129587', // 【预留】缓存命中
  c_car_trace_user_form: '129991', // 用户表单校验,Trace/上注册Key [对应trace key 129988]
  c_car_trace_error_log: '129990', // 异常跟踪,Trace/上注册Key [对应trace key 129825]
  c_car_trace_page_interactive_time: '129956', // 页面性能埋点,Trace/上注册Key [对应trace key 113241]
  c_car_trace_product_price: '131196', // 变价埋点
  c_car_trace_product_page_load: '132242', // 产品详情页打开成功率
  c_car_trace_city_exposure: '133852', // 城市页曝光埋点
  c_car_trace_area_exposure: '133853', // 区域页曝光埋点
  c_car_trace_search_exposure: '133854', // 搜索曝光埋点
  c_car_trace_search_nores: '133855', // 搜索无结果
  c_car_trace_action_log: '133226', // 交互埋点
  c_car_trace_snapshot: '133399', // 交易快照埋点
  c_car_trace_list_cache_interval: '133860', // 列表页请求构建缓存间隔埋点
  c_car_my_history_isd: '103873',
  c_car_my_favorite_isd: '103874',
  c_car_my_history_osd: '101687',
  c_car_my_favorite_osd: '101688',
  c_car_trace_list_second_page_interactive_time: '158091',
  c_car_trace_product_pop_exposure: '156827', // 产品详情页弹框曝光埋点
  c_car_trace_product_price_load: '157694', // 价格接口成功率
  c_car_trace_list_no_result_exposure: '', // 搜索无结果曝光
  c_car_trace_list_quick_filter: '158378', // 列表页快筛曝光
  c_car_trace_order_page_exposure_222025: '165593', // 订单详情页曝光埋点
  c_car_trace_product_pv_10650041522: '165570', // 详情页pv埋点
  c_car_trace_book_insurance_222017: '165032', // 保险合规-进入填写页是否包含保险
  c_car_trace_book_insurance_activetime_222017: '170382', // 保险合规-保代页面停留时长
  c_car_trace_book_insurance_callback_222017: '170396', // 保险合规-回调埋点
  c_car_trace_book_insurance_changeprice_222017: '170401', // 保险合规-变价弹层曝光
  c_car_trace_list_noresult_exposure: '165001', // 搜索无结果曝光
  c_car_trace_product_price_exposure_10650041522: '171447', // 详情页零散小时费弹框曝光埋点
  c_car_trace_product_pricepop_close_10650041522: '171446', // 详情页关闭零散小时费弹框埋点
  c_car_trace_order_onlineauth_ocr_result:
    'c_car_trace_order_onlineauth_ocr_result',
  c_car_trace_order_onlineauth_check_result:
    'c_car_trace_order_onlineauth_check_result',
  c_car_trace_list_questionnaire_exposure_222013: '170689', // 列表页问卷采集入口曝光埋点
  c_car_trace_book_query_price_222017: '168608', // 详情页和填写页价格曝光，包括 a. 每次进页面时 b. 每次在页面调价格接口时
  c_car_trace_book_pay_callback_222017: '168610', // 填写页支付回调结果
  c_car_trace_book_deposit_payment_exposure_222017: '168982', // 填写页押金支付方式曝光
  c_car_trace_book_zhima_go_auth_222017: '168984', // 填写页芝麻去验证按钮曝光，包括二次验证，不含实名
  c_car_trace_order_rerent_query_10650047205:
    'c_car_trace_order_rerent_query_10650047205',
  c_car_trace_order_rerent_save_10650047205:
    'c_car_trace_order_rerent_save_10650047205',
  c_car_exposure_write_driver_recommend:
    'c_car_exposure_write_driver_recommend', // 驾驶人推荐值曝光
  c_car_exposure_write_coupon: 'c_car_exposure_write_coupon', // 填写页曝光优惠券模块
  c_car_exposure_write_activity: 'c_car_exposure_write_activity', // 填写页曝光活动模块
  c_car_trace_position_info: 'c_car_trace_position_info', // 定位信息埋点
  c_car_exposure_productdetail_picture_10650041522: '174940', // 详情页车型图片曝光埋点
  vac_car_trace_product_goodsshelf: '177095', // 货架信息产品详情页填写页漏斗埋点
  c_car_trace_area_city_selector_info: '180750', // 区域页城市选择器数据埋点
  c_car_trace_city_selector_callback_data: '180762', // 城市选择器回调数据
  c_car_trace_city_selector_data: '180903', // 城市区域页数据埋点
  c_car_trace_list_cache_expire: '185502', // 列表页缓存过期弹层曝光埋点
  c_car_trace_network_data: '181200', // 点击报价时当时的网络情况
  vac_car_trace_write_ehiwindow_s: '184129', // 一嗨双免弹层曝光埋点
  c_car_trace_list_goods_shelf_ab: '186543', // 列表页货架AB埋点
  vac_car_page_layer_exposure: '186837', // 租车页面alert弹层曝光埋点
  c_car_trace_newhome_zhimastatus_track: '190201', // 首页用户芝麻状态埋点
  vac_car_trace_module_exposure: '190130', // 首页用户免押模块的曝光
  vac_car_trace_datetimepick_click: '191481', // 时间组件进入退出费时度埋点
  vac_car_write_module_exposure: 'vac_car_write_module_exposure', // Booking Page 拿去花模块曝光埋点
  vac_car_trace_list_usersearch_result: '191267', // 列表页用户筛选变更搜索结果埋点
  c_car_trace_app_shark_default: '194708', // 未取到翻译的 Shark Key,Trace上注册Key
  vac_car_trace_orderdetail_depoistfree_result:
    'vac_car_trace_orderdetail_depoistfree_result', // 点击_订单详情页_免押授权结果
  vac_car_abt_home_xbutripschedule_exposure: '198969', // 首页行程卡曝光埋点
  c_car_exposure_area_search_res: '202787', // 区域搜索结果曝光
  c_car_exposure_cityselect_search_res: '202788', // 城市搜索结果曝光
  c_car_exposure_product_quote_10650068646: '201072', // 车型详情页报价卡片曝光埋点
  c_car_trace_module_exposure: '205598', // 榜单进入列表页异常结果toast曝光，其中时间过期曝光对非榜单场景也生效
  car_productdetail_search_res_trace: '228455', // 详情页重搜后，用户带新搜索条件返回列表页时，列表页搜索埋点才记录新查询的数据
  car_write_driver_recommend_trace: '227652', // 填写页推荐驾驶员曝光
  car_trip_detail_package_expo: '241704',
  c_car_ctrip_isd_search_seed: '298499', // 国内前端用户搜索行为埋点
  c_car_ctrip_osd_list_search_seed: '298500', // 出境前端用户搜索行为埋点
  c_car_app_click_airanking: '301450', // 国内排序实时正反馈埋点
};

// 标识日志上报时不接受延迟，需要立即提交
const IsUnSupportDelayUpload = [LogKey.c_car_trace_create_order];

export { IsUnSupportDelayUpload };
export default LogKey;
