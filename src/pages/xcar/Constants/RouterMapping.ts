export default {
  Home: 'pages/xcar/entry/home/<USER>',
  List: 'pages/xcar/entry/list/index',
  Location: 'pages/xcar/entry/location/index',
  VendorList: 'pages/xcar/entry/vendorlist/index',
  Guide: 'pages/xcar/entry/guide/index',
  Coupon: 'pages/xcar/entry/coupon/index',
  Booking: 'pages/xcar/entry/booking/index',
  BookingIsd: 'pages/xcar/entry/bookingIsd/index',
  Product: 'pages/xcar/entry/product/index',
  Market: 'pages/xcar/entry/market/index',
  License: 'pages/xcar/entry/license/index',
  Debug: 'pages/xcar/entry/debug/index',
  DriverList: 'pages/xcar/entry/driverlist/index',
  DriverEdit: 'pages/xcar/entry/driveredit/index',
  VehModal: 'pages/xcar/entry/vehmodal/index',
  OrderDetail: 'pages/xcar/entry/orderdetail/index',
  OnlineAuth: 'pages/xcar/entry/onlineauth/index',
  Materials: 'pages/xcar/entry/materials/index',
  Policy: 'pages/xcar/entry/policy/index',
  Extras: 'pages/xcar/entry/extras/index',
  PackageIncludes: 'pages/xcar/entry/packageincludes/index',
  Credentials: 'pages/xcar/entry/credentials/index',
  OrderCancel: 'pages/xcar/entry/ordercancel/index',
  OrderRefundDetail: 'pages/xcar/entry/orderrefunddetail/index',
  OrderChange: 'pages/xcar/entry/orderchange/index',
  SupplementList: 'pages/xcar/entry/supplementlist/index',
  RenewList: 'pages/xcar/entry/renewlist/index',
  ViolationDetail: 'pages/xcar/entry/violationdetail/index',
  DamageDetail: 'pages/xcar/entry/damagedetail/index',
  Supplement: 'pages/xcar/entry/supplement/index',
  Rerent: 'pages/xcar/entry/rerent/index',
  AdvanceReturn: 'pages/xcar/entry/advancereturn/index',
  OrderLimitRulesPage: 'pages/xcar/entry/orderlimitrulespage/index',
  InsuranceOrderDetail: 'pages/xcar/entry/insuranceorderdetail/index',
  OrderVialationRule: 'pages/xcar/entry/ordervialationrule/index',
  ModifyOrder: 'pages/xcar/entry/modifyorder/index',
  ModifyOrderConfirm: 'pages/xcar/entry/modifyorderconfirm/index',
  ModifyCoupon: 'pages/xcar/entry/modifycoupon/index',
  RebookHome: 'pages/xcar/entry/rebookhome/index',
  CarRentalCenter: 'pages/xcar/entry/carrentalcenter/index',
  IsdAgreement: 'pages/xcar/entry/isdagreement/index',
  MessageAssistant: 'pages/xcar/entry/messageassistant/index',
  Instructions: 'pages/xcar/entry/instructions/index',
  Member: 'pages/xcar/entry/member/index',
  MemberDetail: 'pages/xcar/entry/memberdetail/index',
  MemberBirth: 'pages/xcar/entry/memberbirth/index',
  MemberPoints: 'pages/xcar/entry/memberpoints/index',
  VehicleDamageProve: 'pages/xcar/entry/vehicledamageprove/index',
  RecommendVehicle: 'pages/xcar/entry/recommendvehicle/index',
  LimitMap: 'pages/xcar/entry/limitmap/index',
  DepositFree: 'pages/xcar/entry/depositfree/index',
  Album: 'pages/xcar/entry/album/index',
};
