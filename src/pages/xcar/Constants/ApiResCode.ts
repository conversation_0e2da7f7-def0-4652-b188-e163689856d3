import Enums from '../ComponentBusiness/Common/src/Enums';

const { PROMOTION_WEEK, PROMOTION_MONTH } = Enums.SpecialFilterCode;
export const ListResCode = { C200: '200', C201: '201' };

export const TraceCode = {
  /**
   * Network request failed
   */
  E1001: 'E1001',
  /**
   * 继续支付失败
   */
  E1002: 'E1002',
  /**
   * Saga logic 执行时的异常
   */
  E1003: 'E1003',
  /**
   * 请求超时
   */
  E1004: 'E1004',
  /**
   * latest请求超时
   */
  E1005: 'E1005',
  /**
   * 输入校验异常
   */
  E1006: 'E1006',
  /**
   * 其他自定义监控
   */
  E9999: 'E9999',
};

export const TraceMsg = {
  E1001: '网络异常', // Network request failed
  E1002: '继续支付失败', // 继续支付失败
};

export const ListPromptType = {
  RentalStoreEnter: 0,
  YXEnter: 1,
  <PERSON><PERSON><PERSON>an_hasResult: 2,
  QiangDan_NoResult: 3,
  SpecialDateEnter: 4,
  EncourageEnter: 5,
  TicketPrivileges: 6,
  Promotion: 7,
  SuperMenber: 8,
  RentCenterEnter: 9,
  EasyLifeEnter: 10,
  Market: 11, // 营销：icon+文字
  MarketBanner: 12, // 营销：图片banner
  FeedBackEnter: 13, // 问卷采集
  CouponEntry: 14,
  YunnanEnter: 15, // 云南文旅入口
  SecretBoxBanner: 16, // 盲盒banner
  SelfServiceTip: 17, // 自助取还提示
  SelfServiceBanner: 18, // 自助取还Banner
  EasyLife2024: 19, // 无忧租2024
  AdditionalDriver: 21, // 填写页额外驾驶员弹层数据
  BookingConfirm: 20, // 填写页挽留弹窗
  SearchCar: 22, // 搜筛中插
};

export const EasyLifeCode = 'easyLife';
export const EasyLife2024Code = 'PREP';
export const EasyLife2024GroupCode = 'prep';
export const EasyLife2024FilterCode = 'groupCode_prep';

export const ITEM_CODE = {
  YOUXUAN: 'Vendor_-1',
  RentalStoreEnter: 'Vendor_0',
  FlapShip: 'Vendor_-2',
  SelfService: 'SelfService_Support', // 自助取还筛选code
};

export const ITEM_CODE_NAME = {
  YOUXUAN: '携程优选',
};

// StoreService_PickupOnDoor 和 PickReturn_StationPR 的值会在 apiFetchRentCenterInfo 中更新
export const FILTER_CODE = {
  PickUpOnDoor: 'Service_PickupOnDoor',
  StoreService_PickupOnDoor: 'PickReturn_PickupOnDoor',
  StoreService_DiffFree: 'StoreService_FreeOW',
  PickReturn_StationPR: 'PickReturn_StationPR',
  PROMOTION_WEEK,
  PROMOTION_MONTH,
};

export const PICKUP_ON_DOOR_NAME = {
  NAME: '送车上门',
};

export const SPECIAL_FILTER_CODE = {
  WEEK: '周租优惠',
  MONTH: '月租优惠',
};

// 处理首页选中筛选项但响应中无筛选项的场景 对应的code
export const homeFiltersCodes = [
  FILTER_CODE.StoreService_PickupOnDoor,
  PROMOTION_WEEK,
  PROMOTION_MONTH,
];

// code的值会在 apiFetchRentCenterInfo 中更新
export const filterItems = [
  {
    get name() {
      return '只看送车上门';
    },
    get code() {
      return FILTER_CODE.StoreService_PickupOnDoor;
    },
    isSelected: false,
  },
];

// 处理首页选中筛选项但响应中无筛选项的场景 对应的code和name
export const homeFilters = [
  {
    code: FILTER_CODE.StoreService_PickupOnDoor,
    name: PICKUP_ON_DOOR_NAME.NAME,
  },
  {
    code: PROMOTION_WEEK,
    name: SPECIAL_FILTER_CODE.WEEK,
  },
  {
    code: PROMOTION_MONTH,
    name: SPECIAL_FILTER_CODE.MONTH,
  },
];

export const BRAND_FILTER_PREFIX = 'Brand_';

export const VENDOR_FILTER_PREFIX = 'Vendor_';

export const ListResErrorCode = {
  E1000003: '1000003', // 代表服务端缓存过期
  E1000002: '1000002', // 代表筛选无结果
};

// 列表页局部请求时，必须要更新的字段
export const ListNeedUpdateField = [
  'appResponseMap',
  'baseResponse',
  'allVehicleCount',
  'allVendorPriceCount',
  'hasResultWithoutFilter',
  'isFromSearch',
  'isLastPage',
  'productGroups',
  'productGroupsHashCode',
  'uniqSign',
  'isNoTopProduct',
];

// 列表页点击筛选和车型组时需要更新的字段
export const ListNeedUpdateField2 = [
  'storeList',
  'vehicleList',
  'vendorList',
  'productGroupCodeUesd',
  'filteredRecommendProducts',
  'mergeVehicleExtendInfos',
  'promptInfos',
];

// 列表页请求下一页时，需单独处理的字段
export const ListNextPageExpField = ['productGroups'];

// 接口异常关键信息
export const SERVER_ERROR = ['502', 'response_ack_failure'];

// 榜单命中缓存
export const QueryTopProductsStatusCode = {
  NOT_OPEN: 'NOT_OPEN',
  TASK_ERROR: 'TASK_ERROR',
  TASK_NULL: 'TASK_NULL',
  WITH_CACHE: 'WITH_CACHE',
  WITH_OUT_CACHE: 'WITH_OUT_CACHE',
};

// 详情页接口响应
export const VehicleDetailListResCode = {
  NO_VEHICLE: '1000002',
  CACHE_INVALID: '1000003',
  SEARCH_NO_RESULT: '1000008',
  FILTER_NO_RESULT: '1000009',
};

// 继续支付接口响应code
export const ContinuePayResultCode = {
  VerificationFailed: '40013', // 验证未通过
};

// 一嗨供应商code
export const ehaiVendorCode = '13088';
// 耀东方供应商code
export const ydfVendorCode = '15004351';

// 取还车方式
export const StoreServiceTypeCode = {
  // 站内取还
  PickUpInStation: 16,
  // 免费上门送取车
  PickUpInDoor: 1,
  // 收费上门送取车
  TollPickUpInDoor: 2,
};

// 车型组id
export const VehGroupId = {
  // 新能源
  NewEnergy: 'newenergy',
};

// 新能源车型类型
export const NewEnergyVehFuelTypes = ['纯电动', '增程式', '插电式'];

// 芝麻认证状态
export const ZhimaResult = {
  unAuthorized: 0, // 未认证
  authorized: 1, // 已认证
  outofDate: 2, // 已过期
  failAuthorized: 3, // 验证失败
  unRealName: 5, // 未实名
  under18: 6, // 未满18
  insufficientQuota: 7, // 免押额度不足
  partAuthorized: 103, // 已认证有资金补足
};

export const DepositFreeType = {
  risk: 1, // 只支持程信分
  zhima: 2, // 只支持芝麻
  riskeAndZhima: 3, // 支持程信分和芝麻
  noSupport: 4, // 不支持免押
};

// 订单能升级服务的type类型
export const OrderCanUpgradeInsTypes = ['one', 'more'];

// 重复下单拦截类型
export const OrderCheckResultCode = {
  weakInterception: 'PBK_2_6_1', // 弱拦截
  strongInterception: 'PBK_2_6', // 强拦截
};

// 车型资源code：B类资源-code3574，C类资源-code4621
export const VehicleResourceLabelCodes = ['3574', '4621'];
