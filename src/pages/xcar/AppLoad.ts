import Device from '@c2x/apis/Device';
/* eslint-disable global-require */
// @ts-ignore 8.25版本 tsd没有导出IBUSharkUtil

import MarketInfo from './Util/MarketInfo';
import { AppContext, Utils, CarStorage, CarLog, User } from './Util/Index';
import { Platform, LogKey, LogKeyDev } from './Constants/Index';
import { PageSearchTraceType } from './Constants/CommonEnums';
import BuildTime from './BuildTime';
import { addEventListener } from './AppListener';
import { getStore, initializeAppState } from './State/StoreRef';
import getUrlQuery, { getUrl } from './SwitchEnv/urlQuery';
import initLocationAndDate from './SwitchEnv/storeAction';

const StorageKey = {
  CAR_CROSS_LIST_PARAMETER_ISD: 'car_cross_list_parameter_isd',
  CAR_CROSS_LIST_PARAMETER_OSD: 'car_cross_list_parameter_osd',
  DEBUG: 'debugEntrance',
};
export enum MarketInfoPlatform {
  App = 'app',
}

const marketInfo = MarketInfo.getInstance(MarketInfoPlatform.App);

const initializeNewHomeParams = (urlQuery, url) => {
  // 首页融合不包含修改订单
  const notFromModifyOrder = !urlQuery?.ctripOrderId;
  // 2022-8-3 兼容H5唤醒时，因url中的参数公共解析异常，urlQuery中无数据，导致在融合首页却打开的是非融合首页的页面
  const isInPlatHome =
    urlQuery?.isHomeCombine === 'true' || url?.includes('isHomeCombine=true');
  AppContext.setIsHomeCombine(notFromModifyOrder && isInPlatHome);
};

const initializeUserFetchCacheId = () => {
  const urlQuery = getUrlQuery();
  if (urlQuery && urlQuery.queryListCacheId) {
    AppContext.setUserFetchCacheId({
      queryListCacheId: urlQuery.queryListCacheId,
      actionType: 'initializeUserFetchCacheId',
    });
  }
};

// 记录融合首页tab拆分的AB实验信息
const initHomeTabABInfo = urlQuery => {
  const abtforsubtabKey = urlQuery?.abtforsubtabKey;
  const abtforsubtabVal = urlQuery?.abtforsubtab;
  const result: any = {};
  if (abtforsubtabKey && abtforsubtabVal) {
    result[abtforsubtabKey] = {
      ExpCode: abtforsubtabKey,
      ExpVersion: abtforsubtabVal,
    };
    AppContext.setABTesting(result);
    AppContext.setHomeTabInfo({ key: abtforsubtabKey, val: abtforsubtabVal });
  }

  // 记录从融合首页透传到其它页面的ab实验信息
  const homeTabAbVersionInfo = urlQuery?.homeTabAbVersionInfo;
  const result2: any = {};
  if (homeTabAbVersionInfo) {
    const [abKey, abVal] = homeTabAbVersionInfo.split('|');
    if (abKey && abVal) {
      result2[abKey] = {
        ExpCode: abKey,
        ExpVersion: abVal,
      };
      AppContext.setABTesting(result2);
    }
  }
};

// 初始化urlQuery
const initUrlQuery = () => {
  const urlQuery = getUrlQuery();
  AppContext.setUrlQuery(urlQuery);
};

const initializePropsUrl = () => {
  const urlQuery = getUrlQuery();
  const url = getUrl();
  initHomeTabABInfo(urlQuery);
  AppContext.setUrl(url);
  initUrlQuery();
  initializeNewHomeParams(urlQuery, url);
};

// initialize car environment
// buildTime, apptype
const initializeCarEnv = () => {
  AppContext.setCarEnv({
    buildTime: BuildTime,
    appType: Utils.getAppType(AppContext.UrlQuery.apptype),
    env: AppContext.UrlQuery.env,
    testid: AppContext.UrlQuery.testid,
    mock: AppContext.UrlQuery.mock,
  });
  AppContext.setABNewDetail(true);
};

// initialize list query parameter
const initializeListQueryParameter = async () => {
  let listKey = '';
  if (Utils.isCtripIsd()) {
    listKey = StorageKey.CAR_CROSS_LIST_PARAMETER_ISD;
  } else if (Utils.isCtripOsd()) {
    listKey = StorageKey.CAR_CROSS_LIST_PARAMETER_OSD;
  }
  try {
    const parameter = await CarStorage.loadAsync(listKey, false);
    if (parameter) {
      AppContext.setUrlQuery(
        AppContext.UrlQuery,
        'listParameter',
        JSON.parse(parameter),
      );
    }
  } catch (e) {
    // console.log(e)
  }
};

export const setUserTraceQueryVid = () => {
  const state = getStore().getState();
  const nextDriverAgeAndNumber = JSON.stringify(state.DriverAgeAndNumber);
  const nextLocationAndDate = JSON.stringify(state.LocationAndDate);
  const { queryVid } = AppContext.UserTrace;
  AppContext.setUserTraceQueryVid({
    nextDriverAgeAndNumber,
    nextLocationAndDate,
  });
  if (queryVid !== AppContext.UserTrace.queryVid) {
    CarLog.LogTrace({
      key: LogKey.c_car_page_search,
      info: {
        pageId: Utils.getCurPageId(),
        traceType: PageSearchTraceType.AppLoad,
      },
    });
  }
};

const initializeListQueryParameterSync = () => {
  let listKey = '';
  if (Utils.isCtripIsd()) {
    listKey = StorageKey.CAR_CROSS_LIST_PARAMETER_ISD;
  } else if (Utils.isCtripOsd()) {
    listKey = StorageKey.CAR_CROSS_LIST_PARAMETER_OSD;
  }
  try {
    const parameter = CarStorage.privateLoadSync(listKey, false);
    if (parameter) {
      AppContext.setUrlQuery(
        AppContext.UrlQuery,
        'listParameter',
        JSON.parse(parameter),
      );
    }
  } catch (e) {
    // console.log(e)
  }
};

const initializeDebugStorage = async () => {
  const urlQuery = getUrlQuery();
  // debug model
  if (urlQuery && urlQuery.debug === 'true') {
    const result = await CarStorage.loadAsync(StorageKey.DEBUG);
    if ((typeof result === 'boolean' && result) || result === 'true') {
      CarStorage.remove(StorageKey.DEBUG);
    } else {
      CarStorage.save(StorageKey.DEBUG, 'true');
    }
  }
};

export const getMarketInfoParam = (param = {}) => ({
  query: AppContext.UrlQuery,
  appid: Platform.APP_ID.CTRIP,
  platform: MarketInfoPlatform.App,
  path: AppContext?.PageInstance?.props?.app?.url,
  pageID: Utils.getCurPageId(),
  ubt: Utils.getUBT(),
  ...param,
});

const setMarketInfoToAppContext = (isLoadFinished = false) => {
  const {
    ChannelID,
    ChildChannelId,
    VisitortraceId,
    SID,
    AllianceID,
    AwakeTime,
    sourceId,
    OUID,
  } = marketInfo.getCarMarket() || {};
  const { vid } = Utils.getUBT();

  AppContext.setMarketInfo({
    channelId: ChannelID,
    childChannelId: ChildChannelId,
    visitortraceId: VisitortraceId,
    sId: SID,
    aId: AllianceID,
    awakeTime: AwakeTime,
    vid,
    isLoadFinished,
    sourceId,
    ouid: OUID,
  });
};

const initializeChannelId = () => {
  const url = getUrl();
  const param = getMarketInfoParam({ path: url });
  marketInfo.setCarChannelID(param);
  setMarketInfoToAppContext();
};

const initializeMarketInfo = async () => {
  const url = getUrl();
  const param = getMarketInfoParam({ path: url });
  await marketInfo.setCarMarket(param);
  setMarketInfoToAppContext(true);
};

const appLoad = async () => {
  initializeMarketInfo();
  setTimeout(() => {
    initializeDebugStorage();
    initializeAppState();
    setUserTraceQueryVid();
    addEventListener();
  }, 0);
};

const appPreLoadSync = () => {
  AppContext.setLanguageInfo({
    locale: 'zh_cn',
    site: 'cn',
    currency: 'CNY',
    language: 'cn',
    standardLocale: 'zh-CN',
  });
  Device.setStatusBarStyle('darkContent');
};

export const initialStoreWithParam = () => {
  const urlQuery = getUrlQuery();
  const initialPage = urlQuery?.initialPage || 'Home';
  const fakeUrl = decodeURIComponent(AppContext.Url);
  const isMarket = initialPage === 'Market';
  const isListClient = initialPage === 'List' && urlQuery?.st === 'client';
  const orderId = urlQuery?.orderId;
  const isOrderDetail = initialPage === 'OrderDetail' && !!orderId;

  const store = getStore();
  // 初始化LocationAndDate，非App渠道生效
  initLocationAndDate(store);

  if (isListClient || isMarket) {
    store?.dispatch({
      type: 'Market/LOAD',
      data: { fakeUrl },
    });
  }

  if (isOrderDetail) {
    setTimeout(() => {
      const isLogin = User.isLoginSync();
      if (isLogin) {
        store?.dispatch({
          type: 'ORDER/QUERY_FIRST_SCREEN_DATA',
          data: { orderId, isAppLoad: true },
        });
      }
    });
  }
  CarLog.LogTraceDev({
    key: LogKeyDev.c_car_trace_cross_params,
    info: { fakeUrl },
  });
};

export {
  appPreLoadSync,
  initializePropsUrl,
  initializeChannelId,
  initializeCarEnv,
  initializeUserFetchCacheId,
  initializeListQueryParameter,
  initializeListQueryParameterSync,
  appLoad,
};
