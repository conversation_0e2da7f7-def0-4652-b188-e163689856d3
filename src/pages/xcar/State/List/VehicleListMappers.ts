import {
  find as lodashFind,
  get as lodashGet,
  map as lodashMap,
  isEqual as lodashIsEqual,
  omit as lodashOmit,
} from 'lodash-es';
/* eslint-disable @typescript-eslint/no-unused-vars */

import { tokenType, icon, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import memoize from 'memoize-one';
import produce from 'immer';
import { createSelector } from 'reselect';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { ProductListType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import {
  Utils,
  CarLog,
  AppContext,
  CarServerABTesting,
  GetABCache,
} from '../../Util/Index';
import {
  VehicleAttribute,
  ListConstants,
  ApiResCode,
  CommonEnums,
} from '../../Constants/Index';
import Texts from '../../Pages/List/Texts';
import {
  getVehAndProductList,
  getVehGroupList,
  getAllProductGroups,
  getVehicleList,
  getYXEnterInfo,
  getEncourageEnterInfo,
  getSpecialDateEnterInfo,
  getRentalStoreEnterInfo,
  getSimilarVehicleIntroduce,
  getDesignatedVehicleIntroduce,
  getFavoriteInfo,
  getQiangDanEnterInfo,
  getTrunkInfo,
  getStoreList,
  getQiangDanNoEnterInfo,
  getEasyLifeEnter,
  getRentCenterEnter,
  getFeedBackEnter,
  isDiffLocation,
  getRequestId,
  getMarketEnter,
  getIsPickupStation,
  getIsDropOffStation,
  getBaseProductGroups,
  getCurProductGroupId,
  getPromptInfos,
  getYunnanEnter,
  getSecretBoxInfo,
  getMergeVehicleExtendInfos,
  getFirstPageSelfServiceBannerInfo,
  getIsVehicle2,
  getEasyLife2024PromptInfo,
  getSearchCarPromptInfo,
} from '../../Global/Cache/ListResSelectors';
import { getTenancyDays, getVendorStore, getLogDataFromState } from './Mappers';
import { getBitsFilter, getSelfServiceBannerInfo } from './Selectors';
import {
  ITEM_TYPE as LabelCodeType,
  CREDITRENT_LABELCODE_TYPES,
  ItemLabelCodeType,
} from '../../ComponentBusiness/PackageIncludes/src/Types';
import LogKeyDev from '../../Constants/LogKeyDev';
import { Enums } from '../../ComponentBusiness/Common';
import {
  Floor,
  PriceInfoType,
  VehicleInfoType,
  VendorPriceListType,
} from '../../Types/Dto/QueryVehicleDetailListResponseType';
import Channel from '../../Util/Channel';
import { TotalPriceModalType } from '../../ComponentBusiness/Common/src/Enums';
import { ListResSelectors } from '../../Global/Cache/Index';
import { RecommendType, LicenseShowType } from '../../Constants/ListEnum';
import {
  getZhiMaTag,
  getRestAssuredTag,
  getAllocationLables,
  getEnergyBaseLabels,
  getEnergyAllocationLabels,
  getGroupLabel,
  mappingLabel,
  getHasSelfService,
  getSimilarVehicleTitle,
} from './Method';
import { ILableCode } from '../../Constants/CommonEnums';

const { getPixel } = BbkUtils;
let count = 0;
const { electricCar } = ListConstants;

export const getVehicleLabelsHorizontal = (
  vehicleProps = {},
  showDisplacement = true,
) => {
  const isRefactor = getIsVehicle2();
  const vehicle: any = vehicleProps;
  const { passengerNo, doorNo: serverDoorNo, displacement, fuelType } = vehicle;
  const doorNo = Utils.isCtripIsd() ? 0 : serverDoorNo;
  const passengerNoItem = passengerNo && {
    text: Utils.isCtripIsd() ? passengerNo : `${passengerNo}${'座'}`,
    icon: {
      iconContent: icon.seat,
    },
  };
  const doorNoItem = doorNo && {
    text: Utils.isCtripIsd() ? doorNo : `${doorNo}${'门'}`,
    icon: {
      iconContent: icon.door,
    },
  };
  let vehicleLabelsHorizontal = [
    Utils.isCtripIsd() &&
      showDisplacement &&
      displacement && {
        text: displacement,
        icon: {
          iconContent:
            displacement === electricCar ? icon.electricCar : icon.gasoline3,
        },
      },
    passengerNoItem,
    doorNoItem,
    (Utils.isCtripIsd() || !isRefactor) &&
      fuelType && {
        text: fuelType,
        icon: {
          iconContent: icon.fuelType,
        },
      },
  ];

  vehicleLabelsHorizontal = vehicleLabelsHorizontal.filter(v => v);
  return vehicleLabelsHorizontal as any;
};

export const getVehicleLabels = (vehicle: any = {}) => {
  const {
    hasConditioner,
    conditionerDesc,
    transmissionName,
    transmissionType,
    driveType,
    luggageNo,
    fuelMode,
    fuel,
  } = vehicle;
  const isRefactor = getIsVehicle2();
  let vehicleLabels = [
    transmissionName && {
      text: transmissionName,
      icon: {
        iconContent:
          transmissionType === VehicleAttribute.TRANSMISSION.Auto
            ? icon.circleAFilled
            : icon.circleMFilled,
      },
      type: 'displacement',
    },
    hasConditioner &&
      conditionerDesc && {
        text: conditionerDesc,
        icon: {
          iconContent: icon.snow,
        },
      },
    Utils.isCtripOsd() &&
      isRefactor &&
      fuelMode && {
        text: fuel,
        icon: {
          iconContent: icon.fuelType,
        },
        type: fuelMode,
      },
    Utils.isCtripOsd() &&
      isRefactor &&
      driveType === '1' && {
        text: '四驱',
        type: 'driveMode',
        icon: {
          iconContent: icon.quattro,
          iconVersion: 'v2',
        },
      },
    Utils.isCtripOsd() &&
      luggageNo && {
        text: `${luggageNo}${'个24寸行李箱'}`,
        rightIcon: {
          iconContent: icon.remind,
        },
        count: luggageNo,
        type: 'luggage',
      },
  ];

  vehicleLabels = vehicleLabels.filter(v => !!v?.text);

  return vehicleLabels;
};

export const getBreakLabel = memoize((allLabels: any = {}) => {
  // 境外列表页标签分两行展示 第一行展示座位｜门数｜变速器类型｜燃油类型
  // 第二行展示 驱动类型｜行李箱信息
  const firstLabels = [];
  const secondLabels = [];
  allLabels.forEach(item => {
    if (item?.type === 'driveMode' || item?.type === 'luggage') {
      secondLabels.push(item);
    } else {
      firstLabels.push(item);
    }
  });
  return { firstLabels, secondLabels };
});

const getSecretBoxGroupLabel = groupNameScope => {
  const groupLabel = [];
  if (!groupNameScope) return groupLabel;
  if (groupNameScope.length > 1) {
    groupLabel.push({
      text: Texts.optional,
      icon: {
        iconContent: '',
      },
    });
  }
  const groupNameScopeDesc = groupNameScope?.join(' ');
  groupLabel.push({
    text: groupNameScopeDesc,
    icon: {
      iconContent: icon.car,
    },
  });
  return groupLabel;
};

const getSecretBoxSecondLabels = (vehicle, passengerNoRange) => {
  if (!passengerNoRange) return [];
  const { transmissionName, transmissionType } = vehicle;
  return [
    passengerNoRange &&
      mappingLabel({
        text: passengerNoRange,
        icon: {
          iconContent: icon.seat,
        },
      }),
    transmissionName && {
      text: transmissionName,
      icon: {
        iconContent:
          transmissionType === VehicleAttribute.TRANSMISSION.Auto
            ? icon.circleAFilled
            : icon.circleMFilled,
      },
      type: 'displacement',
    },
  ];
};

export const getVehicleItemData = memoize(
  (
    vehicleCode,
    hotType,
    isSpecialized,
    notGroup,
    modifySameVehicle = undefined,
    productRef = undefined,
    vehicleList?,
    groupNameScope?,
    passengerNoRange?,
    isEasyLife2024?,
  ) => {
    const newVehicleList = vehicleList || getVehicleList();
    const vehicleMap = newVehicleList?.filter(
      item => item?.vehicleCode === vehicleCode,
    );
    let vehicle = null;
    // 如果筛选车型code出现多辆车且有限行牌照参数，则进行牌照过滤
    if (productRef?.license && vehicleMap?.length > 1) {
      vehicle =
        vehicleMap?.find(item => item.license === productRef?.license) || {};
    } else {
      vehicle = vehicleMap?.[0] || {};
    }

    const {
      name,
      groupCode,
      groupName,
      isHot,
      license: vehicleLicense,
      licenseStyle: vehicleLicenseStyle,
      imageList = [],
      recommendDesc,
      spaceDesc,
      similarCommentDesc,
      displacement,
      realityImageUrl,
      recommendLabels = [],
      fuelType,
      vehiclesSetId,
      mediaTypes = [],
      fuel,
      driveType,
      vehicleTags,
    } = vehicle;

    // fix 牌照数据展示不对 http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
    const license = productRef?.license || vehicleLicense;
    const licenseStyle = productRef?.licenseStyle || vehicleLicenseStyle;

    const eventResult = !!(name && imageList?.length && vehicleCode);
    if (!eventResult) {
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_dev_list_not_show_vehicle,
        info: {
          vehicleList: newVehicleList,
          vehicleCode,
          vehicle,
          name,
          imageList,
          requestId: getRequestId(),
          // 如果无车型名称或者无车图，埋点记录vehicleCode和requestId
          eventResult,
          errorInfo: {
            name,
            imageList,
            vehicleCode,
            requestId: getRequestId(),
          },
        },
      });
    }

    count += 1;
    // 非多年款展示排量，多年款车型vehicle.style没有值展示排量，多年款车型是无忧租一口价展示排量
    const showDisplacement = notGroup || !vehicle.style || isEasyLife2024;

    const vehicleLabelsGroupName = getGroupLabel(vehicle);
    const vehicleLabelsHorizontal = getVehicleLabelsHorizontal(
      vehicle,
      showDisplacement,
    );
    const vehicleLabels = getVehicleLabels(vehicle);
    const secretBoxGroupLabel = getSecretBoxGroupLabel(groupNameScope);
    const secretBoxSecondLabels = getSecretBoxSecondLabels(
      vehicle,
      passengerNoRange,
    );

    const vehicleName =
      isEasyLife2024 && vehicle.style ? `${name}(${vehicle.style})` : name;

    return {
      vehicleHeader: {
        vehicleCode,
        vehicleName,
        groupId: groupCode,
        groupName,
        isSimilar: !isSpecialized,
        isHotLabel: isHot,
        hotType,
        licenseLabel: license,
        licenseType: licenseStyle,
        realityImageUrl, // 详情页头图
        modifySameVehicle,
        mediaTypes, // 多媒体类型
        vehicleTags, // AI排序标签
      },
      vehicleDesc: {
        imgUrl: Utils.fullImgProtocal(imageList[0]),
        vehicleImageLabel: license,
        vehicleLabelsGroupName,
        vehicleLabelsHorizontal,
        vehicleLabels,
        spaceDesc,
        similarCommentDesc,
        recommendLabels,
        isNewEnergy: vehicle?.esgInfo?.reducedCarbonEmissionRatio > 0,
        secretBoxGroupLabel,
        secretBoxSecondLabels,
        fuel,
        driveType,
      },
      recommendDesc,
      count,
      displacement,
      style: vehicle.style,
      vehiclesSetId,
    };
  },
);

const getPriceDescProps = (
  priceInfo = {},
  privilegesPromotions = {},
  cashbackPre = '',
) => {
  const {
    currentOriginalDailyPrice,
    currentTotalPrice,
    currentCurrencyCode = '',
    currentDailyPrice,
    deductInfos,
    oTPrice,
  }: PriceInfoType = priceInfo;
  const { title }: any = privilegesPromotions;
  const res = {
    // todo: 没有日价时需要取总价
    // totolText: days(7),
    dayText: Utils.isCtripOsd() ? '' : `/${Texts.listDay}`,
    [title && 'saleLabel']: title,
  };

  // 总价大于等于0展示
  // 2020-04-14 境外不展示总价暂时由前端控制，后续首页上线后可改成使用服务端控制
  if (!Utils.isCtripOsd() && currentTotalPrice >= 0) {
    res.totalPrice = {
      price: currentTotalPrice,
      currency: Utils.toRMB(currentCurrencyCode),
      originalTotalPrice: oTPrice,
    };
    res.totolText = Texts.total;
  }

  // 划价大于0才展示
  if (currentOriginalDailyPrice > 0) {
    res.originPrice = {
      // todo: 没有日价时需要取总价
      price: currentOriginalDailyPrice,
      currency: Utils.toRMB(currentCurrencyCode),
    };
  }

  // 日价大于等于0展示
  if (currentDailyPrice >= 0) {
    res.dayPrice = {
      price: currentDailyPrice,
      currency: Utils.toRMB(currentCurrencyCode),
    };
  }

  // 返前描述
  if (deductInfos && deductInfos.length > 0) {
    const hasReturn = deductInfos.find(f => f.payofftype === 1);
    if (hasReturn) {
      res.promotionDesc = cashbackPre;
    }
  }

  if (Utils.isCtripIsd()) {
    const days = getTenancyDays();
    res.dayText = Texts.dailyPriceUnit(days);
    if (res.promotionDesc) {
      res.promotionDesc = Texts.returnBeforeDesc;
    }
  }

  return res;
};

const getVendorLabel =
  (colorType?: string, noBg: boolean = true, iconType?: string) =>
  ({ text, iconContent }: { text: string; iconContent?: string }) => ({
    labelStyle: [{ justifyContent: 'flex-start' }],
    icon: {
      iconStyle: {
        fontSize: getPixel(32),
      },
      iconContent,
      iconType,
    },
    text,
    textStyle: {
      ...font.body3LightStyle,
    },
    colorType: tokenType.ColorType.Red,
    size: tokenType.LabelSize.L,
    noBg,
  });

const getSoldOutLabel = text => text && getVendorLabel()({ text });

const getTag = (
  { code, labelCode }: { code?: string; labelCode?: string },
  allTags = [],
) =>
  lodashFind(
    allTags,
    item =>
      (code && item?.code === code) ||
      (labelCode && item?.labelCode === labelCode),
  );

// 一嗨全国连锁
export const getNationalChainTag = allTags =>
  getTag(
    {
      code: LabelCodeType.NATIONALCHNAIN,
    },
    allTags,
  );

export const getProductDetailModalTitle = title => title?.join?.(' | ');

const getDiffFreeTag = allTags =>
  getTag(
    {
      labelCode: LabelCodeType.DIFFFREE,
    },
    allTags,
  );

const getVendorHeaderProps = vendor => {
  const {
    vendorLogo,
    vendorName,
    vendorTag = {},
    commentInfo = {},
    easyLifeInfo = {},
    isSelect,
    reference,
    allTags,
  } = vendor;
  const storeList = getStoreList() || Utils.EmptyArray;
  const store = storeList.find(v => reference.pStoreCode === v?.storeCode);
  const {
    commentCount,
    level,
    overallRating,
    maximumRating,
    commentLabel,
    hasComment,
  } = commentInfo;
  const isFlagShip = !!reference.fType;
  // 评分标签 type 1表示正向 蓝色  2负向 灰色 20200303更新 统一默认蓝色
  return {
    vendorLogo,
    vendorName,
    title: vendorTag.title,
    brandCode: vendorTag?.code,
    scoreDesc: level || '',
    commentDesc: commentCount > 0 ? Texts.review(commentCount) : '',
    hasComment,
    commentLabel,
    score: overallRating,
    totalScore: maximumRating,
    scoreLow: false,
    isEasyLife: easyLifeInfo.isEasyLife,
    isSelect,
    isRentCenter: !!store?.isrentcent,
    // 优选和旗舰店共存时，优先级：优选>旗舰店
    isFlagShip: !isSelect && isFlagShip,
    // 详情页预加载
    isOptimize: isSelect,
    nationalChainTag: getNationalChainTag(allTags),
  };
};

export const getVendorIsCreditRent = vendor =>
  CREDITRENT_LABELCODE_TYPES.includes(
    lodashGet(getZhiMaTag(vendor?.allTags), 'labelCode'),
  );

export const getVendorItemData = memoize(
  ({ vendor, vendorIndex }, { vehicleCode, vehicleIndex }) => {
    const {
      priceInfo,
      reference,
      privilegesPromotions,
      urge,
      cashbackPre,
      allTags,
    } = vendor;
    const priceDescProps = getPriceDescProps(
      priceInfo,
      privilegesPromotions,
      cashbackPre,
    );
    let tagsWithoutMarket = [];
    let marketLabels = [];
    if (!Utils.isCtripIsd() && allTags) {
      [tagsWithoutMarket, marketLabels] = allTags.reduce(
        (result, tag) => {
          result[tag.colorCode === LabelCodeType.DISCOUNT ? 1 : 0].push(tag);
          return result;
        },
        [[], []],
      );
    }

    // const vendorLabelItems = getVendorLabelItems(vendor);
    const soldOutLabel = getSoldOutLabel(urge);
    const vendorHeaderProps = getVendorHeaderProps(vendor);
    // 信用租
    const isCreditRent = getVendorIsCreditRent(vendor);
    // const eventResult = !!(
    //   vendor?.vendorName &&
    //   vendor?.priceInfo?.currentDailyPrice &&
    //   vehicleCode
    // );
    // const requestId = getRequestId();
    // const traceInfo = eventResult
    //   ? {
    //       vehicleIndex,
    //       vendorIndex,
    //       vehicleCode,
    //       eventResult,
    //       requestId,
    //     }
    //   : {
    //       vendor,
    //       priceDescProps,
    //       vendorHeaderProps,
    //       reference,
    //       vehicleIndex,
    //       vendorIndex,
    //       vehicleCode,
    //       eventResult,
    //       requestId,
    //     };
    // CarLog.LogTraceDev({
    //   key: LogKeyDev.c_car_dev_trace_osdlist_vendor_data,
    //   info: traceInfo,
    // });
    return {
      priceDescProps,
      vendor,
      // 国内资源才有库存
      soldOutLabel,
      vendorHeaderProps,
      reference,
      vehicleIndex,
      vendorIndex,
      vehicleCode,
      isCreditRent,
      diffFreeTag: getDiffFreeTag(allTags),
      tagsWithoutMarket,
      marketLabels,
    };
  },
);

export const getVendorListData = (vendorPriceList, vehicleInfo) =>
  lodashMap(vendorPriceList, (vendor, vendorIndex) => {
    const vendorItemData = getVendorItemData(
      { vendor, vendorIndex },
      vehicleInfo,
    );
    return vendorItemData;
  });

// 通过车型code找到对应的车型报价信息
export const getVendorPriceListByVehicleCode = (
  vehicleCode,
  productTopInfo,
  activeGroupId = '',
  curProductList?,
  license?,
) => {
  const baseProductGroups = getBaseProductGroups();
  const groupId = activeGroupId || getCurProductGroupId();
  const curProductGroup = baseProductGroups.find(f => f.groupCode === groupId);
  const productList = curProductList || curProductGroup?.productList;
  const curProduct = productList?.find(
    f =>
      f?.vehicleCode === vehicleCode &&
      f?.productTopInfo === productTopInfo &&
      f?.productRef?.license === license,
  );
  return curProduct?.vendorPriceList;
};

const getValidVendorPriceList = product => {
  if (product?.vendorPriceList?.length > 0) {
    return product.vendorPriceList;
  }
  return null;
};

// 获取车型年款&排量信息
export const getVehicleInfoByCode = (
  vehicleCode,
  vehicleList?,
  productRef?,
) => {
  const curVehicleList = vehicleList || getVehicleList() || [];
  const curVehicleInfo = curVehicleList.find(
    f => f?.vehicleCode === vehicleCode,
  );
  // 如果找不到当前车型，则进行埋点记录
  if (!curVehicleInfo) {
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_list_not_find_vehicle,
      info: {
        vehicleList: curVehicleList,
        vehicleCode,
        requestId: getRequestId(),
      },
    });
    return null;
  }
  // 牌照合并项目，年款车型点击进入传入 productRef 覆盖牌照
  if (productRef && curVehicleInfo) {
    const licenseVehicleInfo = produce(curVehicleInfo, vehicleInfo => {
      // eslint-disable-next-line no-param-reassign
      vehicleInfo.licenseStyle = productRef?.licenseStyle;
      // eslint-disable-next-line no-param-reassign
      vehicleInfo.license = productRef?.license;
    });
    return licenseVehicleInfo;
  }
  return curVehicleInfo;
};

// 获取盲盒填写页首屏数据
export const getSecretBoxFirstScreenParamToBooking = priceInfo => {
  return {
    vendorInfo: {
      vendorName: priceInfo?.vendorName,
    },
    isSelect: priceInfo?.isSelect,
    isEasyLife: priceInfo?.easyLifeInfo?.isEasyLife,
    nationalChainTagTitle: getNationalChainTag(priceInfo?.allTags)?.title,
    allTags: {
      vehicleTagList: priceInfo?.allTags?.filter(flex => flex?.groupId === 2),
    },
  };
};

// 标签等信息下沉到服务端后的新方法 http://conf.ctripcorp.com/pages/viewpage.action?pageId=890634386
export const getVendorListEnterDataNew = product => {
  const {
    vehicleCode,
    minTPrice,
    minDPrice,
    isOptim,
    isEasy,
    pTag,
    outTags = [],
    priceSize,
    minDOrinPrice,
    isCredit,
    rCoup,
    pWay,
    productRef,
    type = 1, // 产品类型 0 普通产品 1盲盒产品
  } = product;

  const minTotalPriceOtherDesc = priceSize > 1 ? Texts.recommendPriceTitle : '';
  const originPrice =
    minDOrinPrice > 0
      ? {
          price: minDOrinPrice,
        }
      : null;

  const { displacement, style: vehicleStyle } =
    getVehicleInfoByCode(vehicleCode) || {};

  return {
    hasOptimize: isOptim,
    hasEasyLife: !CarServerABTesting.isBlockOldEasyLife() && isEasy,
    hasCreditRent: isCredit,
    price: minDPrice,
    minTotalPriceDesc: Texts.total,
    minTotalPrice: minTPrice,
    minTotalPriceOtherDesc,
    descText: Texts.dailyPriceUnit(getTenancyDays()),
    tagInfo: getZhiMaTag(outTags),
    vehicleCode,
    restAssuredTag: getRestAssuredTag(outTags),
    originPrice,
    marketTags: pTag ? [pTag] : [],
    firstVendor: null,
    vendorListDesc: `${`${priceSize}个报价`}`,
    rCoup,
    pickUpDesc: pWay,
    productRef,
    displacement,
    vehicleStyle,
    type,
    hasSelfService: getHasSelfService(outTags),
  };
};

// 获取最低价的vendorPrice
export const getMinDPriceData = vendorPriceList => {
  const sortedVendorPriceList = [...vendorPriceList];
  sortedVendorPriceList.sort(
    (a, b) =>
      (a?.priceInfo?.currentDailyPrice ?? 0) -
      (b?.priceInfo?.currentDailyPrice ?? 0),
  );
  return sortedVendorPriceList?.[0];
};

export const getVendorListEnterData = (
  product,
  vehicleInfo?,
  needReturnFirstVendor?,
  activeGroupId = '',
  onlyReturnFirstVendor = false,
) => {
  if (Utils.isCtripIsd()) {
    return getVendorListEnterDataNew(product);
  }

  const {
    priceGroup = [],
    lowestPrice,
    vehicleCode,
    minTPrice,
    minDPrice,
    productTopInfo,
    productRef,
    type,
  } = product;

  const vendorPriceList =
    getValidVendorPriceList(product) ||
    getVendorPriceListByVehicleCode(
      vehicleCode,
      productTopInfo,
      activeGroupId,
      null,
      productRef?.license,
    ) ||
    [];

  const priceList = [lowestPrice];
  const priceInfoList = [];
  if (minTPrice >= 0 && minDPrice >= 0) {
    priceInfoList.push({
      minDPrice,
      minTPrice,
    });
  }

  const vendorShowNum = Utils.getListVendorShowNum();
  let hasOptimize = false;
  let hasEasyLife = false;
  const vendorList = [];
  const logoUrlList = [];
  const vendorCodeList = [];
  const uniqVendorCodeList = [];
  let vendorListDesc = '';
  let allVendorPriceList = []; // 包含多年款时的总报价
  const vendorLen = vendorPriceList.length;
  let totalPriceLen = vendorLen;

  // 获取供应商logo+报价个数
  vendorPriceList.forEach((item, index) => {
    const vendorCode = lodashGet(item, 'reference.vendorCode');
    if (
      logoUrlList.length < vendorShowNum &&
      !Utils.isCtripIsd() &&
      !vendorCodeList.includes(vendorCode)
    ) {
      vendorCodeList.push(vendorCode);
      logoUrlList.push(item.vendorLogo);
    }
    if (!onlyReturnFirstVendor || index === 0) {
      const vendorItemData = getVendorItemData(
        { vendor: item, vendorIndex: index },
        vehicleInfo,
      );
      vendorList.push(vendorItemData);
    }

    if (!Utils.isCtripIsd() && !uniqVendorCodeList.includes(vendorCode)) {
      uniqVendorCodeList.push(vendorCode);
    }
  });

  let priceLen = 0;
  if (priceGroup.length > 1) {
    priceGroup.forEach(pItem => {
      const priceVendorPriceList =
        getValidVendorPriceList(pItem) ||
        getVendorPriceListByVehicleCode(
          pItem?.vehicleCode,
          pItem.productTopInfo,
        ) ||
        [];
      priceLen += priceVendorPriceList.length || 0;
      priceList.push(pItem.lowestPrice);
      if (pItem.minTPrice >= 0) {
        priceInfoList.push({
          minDPrice: pItem.minDPrice,
          minTPrice: pItem.minTPrice,
        });
      }
      if (priceVendorPriceList) {
        allVendorPriceList = allVendorPriceList.concat(priceVendorPriceList);
      }
    });
  } else {
    priceLen = vendorPriceList.length;
    allVendorPriceList = vendorPriceList;
  }
  totalPriceLen = priceLen;
  const prefix = uniqVendorCodeList.length > vendorShowNum ? '等' : '';
  vendorListDesc = `${prefix}${`${priceLen}个报价`}`;

  // 芝麻标签
  const hasZhiMaTagInfo = allVendorPriceList.find(f => getZhiMaTag(f.allTags));

  // 判断是否含有安心行产品
  const hasRestAssuredTagInfo = allVendorPriceList.find(f =>
    getRestAssuredTag(f.allTags),
  );

  let zhiMaTag = null;
  let restAssuredTag = null;
  if (hasZhiMaTagInfo) {
    zhiMaTag = getZhiMaTag(hasZhiMaTagInfo.allTags);
  }

  // 判断是否含有无忧租产品
  if (allVendorPriceList.find(f => lodashGet(f, 'easyLifeInfo.isEasyLife'))) {
    hasEasyLife = true;
  }

  // 判断是否含有优选产品(在没有无忧租的前提下)
  if (!hasEasyLife && allVendorPriceList.find(f => f.isSelect)) {
    hasOptimize = true;
  }

  if (hasRestAssuredTagInfo) {
    restAssuredTag = getRestAssuredTag(hasRestAssuredTagInfo.allTags);
  }

  const minTotalPriceOtherDesc =
    totalPriceLen > 1 ? Texts.recommendPriceTitle : '';

  const firstVendor = needReturnFirstVendor ? vendorPriceList[0] : null;

  // 获取最低价的vendorPrice
  const sortedVendorPriceList = [...vendorPriceList];
  sortedVendorPriceList.sort(
    (a, b) =>
      (a?.priceInfo?.currentDailyPrice ?? 0) -
      (b?.priceInfo?.currentDailyPrice ?? 0),
  );
  const minTPriceVendorPrice = sortedVendorPriceList?.[0];

  const depositFreeLabels = vendorPriceList[0]?.allTags?.filter(
    f => f.labelCode === ItemLabelCodeType.DEPOSITFREE,
  );

  // 最低价
  const { currentDailyPrice, currentOriginalDailyPrice, currentCurrencyCode } =
    minTPriceVendorPrice?.priceInfo || {};
  const minPrice = currentDailyPrice;
  const currency = currentCurrencyCode;
  const newOriginPrice =
    currentOriginalDailyPrice > 0
      ? {
          price: currentOriginalDailyPrice,
          currency: Utils.toRMB(currentCurrencyCode),
        }
      : null;

  // 获取天价描述
  let prefixDescText = '';
  let descText = '';
  // 2020-09-27 有总价时，日价不展示起
  if (priceLen > 1) {
    const priceCombileValue = `${currency}${minPrice}`;
    const priceDesc = `${priceCombileValue}/天起`;
    const priceDescList = priceDesc.split(priceCombileValue);
    prefixDescText = lodashGet(priceDescList, '[0]');
    descText = lodashGet(priceDescList, '[1]');
    descText = '起';
  } else {
    descText = '';
  }

  // 获取最低价的营销标签
  const discountLabels = minTPriceVendorPrice?.allTags?.filter(
    f => f.colorCode === LabelCodeType.DISCOUNT,
  );

  return {
    hasOptimize,
    hasEasyLife,
    price: minPrice,
    minTotalPriceDesc: Texts.total,
    minTotalPriceOtherDesc,
    currency,
    prefixDescText,
    descText,
    logoUrlList,
    vendorListDesc,
    tagInfo: zhiMaTag,
    hasMore: vendorLen > 1,
    totalPriceLen,
    vehicleCode,
    vendorList,
    allVendorPriceList,
    restAssuredTag,
    firstVendor,
    discountLabels,
    depositFreeLabels,
    newOriginPrice,
    pickUpDesc: '',
    productRef,
    type, // 产品类型 0 普通产品 1盲盒产品
  };
};

const getEnabledIndex = (index, maxIndex, minIndex, step) => {
  const vehGroupList = getVehGroupList(getAllProductGroups());
  let nextIndex = index;
  do {
    if (step > 0) {
      nextIndex = Math.min(nextIndex + step, maxIndex + 1);
    } else {
      nextIndex = Math.max(nextIndex + step, minIndex - 1);
    }
  } while (
    nextIndex <= maxIndex &&
    nextIndex >= minIndex &&
    vehGroupList[nextIndex].count === 0
  );

  return nextIndex;
};

/**
 * 获取筛选后每个车型对应的可用上个车型和下个车型
 */
export const getLastNextIndexObj = memoize(
  // eslint-disable-next-line
  (minIndex, maxIndex, selectedFilters) => {
    const res = {};
    for (let i = minIndex; i <= maxIndex - minIndex + 1; i += 1) {
      res[i] = {
        last: getEnabledIndex(i, maxIndex, minIndex, -1),
        next: getEnabledIndex(i, maxIndex, minIndex, 1),
      };
    }
    return res;
  },
  (newArgs, oldArgs) => lodashIsEqual(oldArgs, newArgs),
);

export const getGroupLength = () => {
  const vehGroupList = getVehGroupList(getAllProductGroups());
  return {
    minIndex: 0,
    maxIndex: vehGroupList.length - 1,
  };
};

export const getGroupNameByIndex = index => {
  const vehGroupList = getVehGroupList(getAllProductGroups());
  return (vehGroupList[index] || {}).title;
};

export const getGroupNameByKey = key => {
  const vehGroupList = getVehGroupList(getAllProductGroups()) || [];
  const cur = vehGroupList.find(item => item.gId === key) || {};
  const { title = '' } = cur;
  return title;
};

const getEnterTitleAndSub = contents => {
  const title = lodashGet(contents, '[0].stringObjs[0].content');
  const subTitle = lodashGet(contents, '[1].stringObjs[0].content');
  return {
    title,
    subTitle,
  };
};

const getEnterListData = (bitsFilters, selfServiceBannerInfo) => {
  const enterList = [];
  const yxEnterInfo = getYXEnterInfo();
  const easyLife2024PromptInfo = getEasyLife2024PromptInfo();
  const searchCarPromptInfo = getSearchCarPromptInfo();
  const encourageEnterList = getEncourageEnterInfo();
  const specialDateEnterInfo = getSpecialDateEnterInfo();
  const rentalStoreEnterInfo = getRentalStoreEnterInfo();
  const favoriteInfo = getFavoriteInfo();
  const qiangdanInfo = getQiangDanEnterInfo();
  const rentCenterEnter = getRentCenterEnter();
  const easyLifeEnterInfo = getEasyLifeEnter();
  const feedBackEnterInfo = getFeedBackEnter();
  const marketEnter = getMarketEnter();
  const yunnanEnter = getYunnanEnter();
  const secretBoxBanners = getSecretBoxInfo();
  const firstPageSelfServiceBannerInfo = getFirstPageSelfServiceBannerInfo();
  const selfServiceBannerData =
    bitsFilters?.length === 0 && firstPageSelfServiceBannerInfo
      ? firstPageSelfServiceBannerInfo
      : selfServiceBannerInfo;

  if (yxEnterInfo) {
    const yxContents = yxEnterInfo.contents || [];
    let yxDesc = '';
    yxContents.forEach(item => {
      const desc = lodashGet(item, 'stringObjs[0].content');
      if (desc) {
        yxDesc += `${desc}#`;
      }
    });
    yxEnterInfo.subTitle = yxDesc;
    enterList.push(yxEnterInfo);
  }
  if (encourageEnterList && encourageEnterList.length > 0) {
    encourageEnterList.forEach(encourageEnterInfo => {
      const { title } = getEnterTitleAndSub(encourageEnterInfo.contents);
      enterList.push({ ...encourageEnterInfo, subTitle: title });
    });
  }
  if (specialDateEnterInfo) {
    enterList.push(specialDateEnterInfo);
  }
  if (rentalStoreEnterInfo) {
    const { title, subTitle } = getEnterTitleAndSub(
      rentalStoreEnterInfo.contents,
    );
    enterList.push({ ...rentalStoreEnterInfo, title, subTitle });
  }
  if (favoriteInfo) {
    const filterItems = lodashGet(favoriteInfo, 'filterItems') || [];
    let isShow = true;
    filterItems.forEach(f => {
      if (bitsFilters.indexOf(f.itemCode) > -1) {
        isShow = false;
      }
    });

    if (isShow) {
      enterList.push({
        type: 'favoriteInfo', // 标记是推荐心仪车型筛选模块
        ...favoriteInfo,
      });
    }
  }

  if (qiangdanInfo) {
    const { title, subTitle } = getEnterTitleAndSub(qiangdanInfo.contents);
    enterList.push({ ...qiangdanInfo, title, subTitle });
  }

  if (easyLifeEnterInfo) {
    enterList.push(easyLifeEnterInfo);
  }
  if (rentCenterEnter) {
    enterList.push(rentCenterEnter);
  }
  if (feedBackEnterInfo) {
    enterList.push(feedBackEnterInfo);
  }
  if (marketEnter) {
    enterList.push(marketEnter);
  }
  if (selfServiceBannerData) {
    enterList.push(selfServiceBannerData);
  }
  if (yunnanEnter) {
    enterList.push(yunnanEnter);
  }

  if (secretBoxBanners) {
    secretBoxBanners.forEach(banner => {
      const { secretBoxInfo, product } = banner;
      enterList.push({
        ...secretBoxInfo,
        vehicleCode: product?.vehicleCode,
        minDPrice: product?.minDPrice,
        vendorPriceInfo: product?.vendorPriceList[0],
        productRef: product?.productRef,
      });
    });
  }

  if (easyLife2024PromptInfo) {
    enterList.push(easyLife2024PromptInfo);
  }

  if (searchCarPromptInfo) {
    enterList.push(searchCarPromptInfo);
  }

  return enterList;
};

// uid不同时更新缓存
export const getEnterList = createSelector(
  [
    getBitsFilter,
    getSelfServiceBannerInfo,
    AppContext.getUserId,
    getPromptInfos,
    getSecretBoxInfo,
    getFavoriteInfo,
  ],

  getEnterListData,
);

export const getQiangDanNoEnter = () => {
  let info = null;
  const qiangdanInfo = getQiangDanNoEnterInfo();
  if (qiangdanInfo) {
    const { title, subTitle } = getEnterTitleAndSub(qiangdanInfo.contents);
    info = { ...qiangdanInfo, title, subTitle };
  }

  return info;
};

export const getTitle = (obj, vehicleName?: string) => {
  const { title = '' } = obj || {};
  let { description = '' } = obj || {};
  if (!title && !description) return null;
  if (vehicleName) {
    description = description.replace('{0}', vehicleName);
  }
  return {
    headerText: title,
    items: description
      .split('\n')
      .filter(v => v)
      .map(v => ({ title: v })),
  };
};

export const getImgList = (imageList = []) =>
  imageList.map(item => {
    const res = {
      imageUrl: item,
    };
    return res;
  });

export const getPossibleVehicleList = (similarVehicleInfos = []) =>
  similarVehicleInfos.map(item => ({
    vendorLogo: item.vendorLogo,
    imgList: item.similarVehicleInfos.map(obj => ({
      imageUrl: obj.vehicleImageUrl,
      label: obj.vehicleName,
    })),
  }));

export const getSpecialVehicleList = (vehicleName, images = []) =>
  images.map(item => ({
    imgList: [
      {
        imageUrl: item,
        label: vehicleName,
      },
    ],
  }));

export const getVehPopData = memoize((activeGroupId, vehicleCode) => {
  const vehicleList = getVehicleList();
  const curVehInfo =
    lodashFind(vehicleList, {
      vehicleCode,
    }) || {};
  const { name, isHot, specializedImages, vehicleAccessoryImages } = curVehInfo;

  const { productGroups = [] } = getVehAndProductList();
  const curGroup = productGroups.find(f => f.groupCode === activeGroupId);
  const productList = lodashGet(curGroup, 'productList') || [];
  const curVeh = productList.find(p => p?.vehicleCode === vehicleCode) || {};

  const { isSpecialized, vendorSimilarVehicleInfos } = curVeh;

  const similarVehicleIntroduce = getSimilarVehicleIntroduce();
  const designatedVehicleIntroduce = getDesignatedVehicleIntroduce();
  const introduce =
    !Utils.isCtripIsd() &&
    (isSpecialized
      ? getTitle(designatedVehicleIntroduce, name)
      : getSimilarVehicleTitle(similarVehicleIntroduce));
  const carProtection =
    !Utils.isCtripIsd() &&
    !isSpecialized &&
    getTitle(similarVehicleIntroduce.carProtection);
  const baseInfo = getTitle({
    get title() {
      return '车型基本信息';
    },
    description: '',
  });
  let possibleVehicles = null;
  if (!Utils.isCtripIsd()) {
    /* eslint-disable max-len */
    possibleVehicles = getTitle({
      title: isSpecialized ? '车型图片参考' : '本次可能取到的车型',

      description: '',
    });
  }
  let tableTitleList = [];
  const tableDataList = [];
  if (
    !isSpecialized &&
    !similarVehicleIntroduce?.vedio &&
    similarVehicleIntroduce &&
    similarVehicleIntroduce.cases
  ) {
    similarVehicleIntroduce.cases.forEach(item => {
      const {
        vehicleGroupCode,
        vehicleGroupName,
        representativeVehicleName,
        vehicleGroupItems,
      } = item;

      const list = [];
      list.push(vehicleGroupName);
      list.push(representativeVehicleName);
      list.push(vehicleGroupItems);

      if (vehicleGroupCode === 'default') {
        tableTitleList = list;
      } else {
        tableDataList.push(list);
      }
    });
  }

  const similarVehicleTableProps = {
    title: tableTitleList || [],
    data: tableDataList,
  };

  // 获取推荐的报价
  const recommendVendorList = [];
  let recommendTip = '';
  if (productList) {
    const tip = lodashGet(curVeh, 'vehicleRecommendProduct.introduce');
    const vendorPriceList = lodashGet(curVeh, 'vendorPriceList');
    if (vendorPriceList && vendorPriceList.length) {
      recommendTip = tip;
      vendorPriceList.sort(
        (a, b) =>
          lodashGet(a, 'priceInfo.currentDailyPrice') -
          lodashGet(b, 'priceInfo.currentDailyPrice'),
      );
      const vendorInfo = getVendorItemData(
        {
          vendor: vendorPriceList[0],
          vendorIndex: 0,
        },
        curVehInfo,
      );
      recommendVendorList.push(vendorInfo);
    }
  }

  const vehicleLabelsHorizontal = getVehicleLabelsHorizontal(curVehInfo);
  const vehicleLabels = getVehicleLabels(curVehInfo);

  const allocationLables = getAllocationLables(curVehInfo);
  const energyBaseLabels = getEnergyBaseLabels(curVehInfo);
  const energyAllocationLabels = getEnergyAllocationLabels(curVehInfo);
  const data = {
    section: {
      introduce,
      carProtection,
      baseInfo,
      possibleVehicles,
    },
    similarVehicleTableProps,
    vehicleNameProps: {
      vehicleCode,
      name,
      type: Utils.isCtripIsd() ? 'none' : undefined,
      isSimilar: !isSpecialized,
      isHotLabel: isHot,
    },
    vehicleBaseInfoProps: {
      imgList: getImgList(vehicleAccessoryImages),
      vehicleLabels: [...vehicleLabelsHorizontal, ...vehicleLabels],
      allocationLables,
      energyBaseLabels,
      energyAllocationLabels,
      bootInfo: getTrunkInfo(),
    },
    possibleVehicleList: isSpecialized
      ? getSpecialVehicleList(name, specializedImages)
      : getPossibleVehicleList(vendorSimilarVehicleInfos),
    recommendTip,
    recommendVendorList,
  };

  return data;
});

const EMPTY_ARRAY = [];

const getGroupLowestPrice = productList => {
  const groupMinPriceList = productList.map(item => item?.lowestPrice);
  groupMinPriceList.sort((a, b) => a - b);
  return groupMinPriceList[0];
};

export const packageSections = productList => {
  if (!productList || productList.length <= 0) {
    return EMPTY_ARRAY;
  }
  const groupLowestPrice = getGroupLowestPrice(productList) || '';
  const sections = productList.map((item, pIndex) => {
    // SectionList数据结构限制，必须要有一个data节点
    const data = [item.vendorPriceList];
    const newItem = { ...item, data, vehicleIndex: pIndex, groupLowestPrice };
    return newItem;
  });

  return sections;
};

export const packageSectionsForListPage = memoize(
  (productList, activeGroupId) => {
    const isEasyLife2024 = activeGroupId === ApiResCode.EasyLife2024GroupCode;
    if (!productList || productList.length <= 0) {
      return EMPTY_ARRAY;
    }
    const sections = productList.map((item, pIndex) => {
      // SectionList数据结构限制，必须要有一个data节点
      const data = [item?.vehicleCode];
      const firstVendorPrice = item?.vendorPriceList?.[0] || {};
      const currentReference = firstVendorPrice?.reference || {};
      const isSelectedVendorTop1 = firstVendorPrice?.isSelect;
      // eslint-disable-next-line no-param-reassign
      item.extraLog = {
        vendorCode: currentReference?.vendorCode,
        pStoreCode: currentReference?.pStoreCode,
        rStoreCode: currentReference?.rStoreCode,
        currentTotalPrice: item.minTPrice,
        priceDifference: firstVendorPrice?.packageComparison?.diffPrice,
        smallGroupRank: 0, // 报价聚合组排序
        isSelectedVendorTop1: isSelectedVendorTop1 ? '1' : '0', // 是否是选中供应商第一位
        topSelectedVendorCarAge:
          (isSelectedVendorTop1 && firstVendorPrice?.carAge) || '',
      };
      if (isEasyLife2024) {
        // eslint-disable-next-line no-param-reassign
        item.isEasyLife2024 = isEasyLife2024;
      }
      let newItem = lodashOmit(item, 'vendorPriceList');

      if (newItem?.priceGroup?.length > 0) {
        // 多年款的处理
        const { recommendInfo, recommendType } = newItem || {};
        let groupRecommendInfo = recommendInfo;
        const newPriceGroup = newItem.priceGroup.map(priceGroupItem => {
          if (
            recommendType === RecommendType.PoiRange &&
            Number(priceGroupItem?.recommendInfo) < Number(groupRecommendInfo)
          ) {
            groupRecommendInfo = priceGroupItem?.recommendInfo;
          }
          return lodashOmit(priceGroupItem, 'vendorPriceList');
        });
        newItem.recommendInfo = groupRecommendInfo;
        newItem.priceGroup = newPriceGroup;
      }
      newItem = { ...newItem, data, vehicleIndex: pIndex };
      return newItem;
    });

    return sections;
  },
);

const getPickupAndDropOffLevel = (storeList, reference) => {
  const pickUpLevel = 0; // 送车上门的圈号
  let pickOffLevel = 0; // 上门取车的圈号
  if (storeList && reference) {
    const store = getVendorStore(storeList, reference);
    pickOffLevel = store?.pickUpLevel || 0;
    pickOffLevel = store?.pickOffLevel || 0;
  }

  return {
    pickUpLevel,
    pickOffLevel,
  };
};

export const hasFees = vendorPriceList => {
  if (!vendorPriceList) {
    return false;
  }
  const minTPriceVendor = vendorPriceList.find(f => f.isMinTPriceVendor);
  return minTPriceVendor?.fees?.length > 0;
};

// 获取总价说明弹层信息
export const getTotalPriceModalData = (
  vendorPriceList,
  storeList,
  type?,
  uniqueCode?,
  isISDShelves2B?,
) => {
  if (!vendorPriceList) {
    return null;
  }
  let data = null;
  const minTPriceVendor = vendorPriceList.find(f => {
    if (uniqueCode) {
      return f.uniqueCode === uniqueCode;
    }
    return f.isMinTPriceVendor;
  });

  const isEasyLife2024 =
    minTPriceVendor?.reference?.packageLevel === ApiResCode.EasyLife2024Code;
  let footer = '';
  switch (type) {
    case Enums.TotalPriceModalType.SecretBoxVendor:
      footer = Texts.bookRightNow;
      break;
    case Enums.TotalPriceModalType.SecretBox:
      footer = Texts.totalPriceSecretBoxNoteFooter;
      break;
    default:
      footer = isISDShelves2B ? '下一步' : Texts.totalPriceNoteFooter;
  }
  if (isEasyLife2024) {
    footer = Texts.bookRightNow;
  }
  if (minTPriceVendor) {
    data = {
      fees: minTPriceVendor?.fees,
      type: type || Enums.TotalPriceModalType.List,
      title: isEasyLife2024 ? '一口价说明' : Texts.totalPriceNoteTitle,
      footer,
      totalPriceName: Texts.totalPriceName,
      reference: minTPriceVendor?.reference,
      packageComparison: minTPriceVendor?.packageComparison,
      uniqueCode,
      ...getPickupAndDropOffLevel(storeList, minTPriceVendor?.reference),
    };
  }
  return data;
};

// 获取总价说明弹层信息--非价格一致性版本
export const getTotalPriceModalDataV2 = (
  minDPrice: number,
  minTPrice: number,
  type?: TotalPriceModalType,
  isISDShelves2B?: boolean,
) => {
  const { totalPriceNote } = Texts;
  const days = getTenancyDays();
  const defaultBtnText = isISDShelves2B ? '下一步' : Texts.totalPriceNoteFooter;
  return {
    type: type || Enums.TotalPriceModalType.List,
    content: totalPriceNote,
    title: Texts.totalPriceNoteTitle,
    footer:
      type === Enums.TotalPriceModalType.Vendor
        ? Texts.bookRightNow
        : defaultBtnText,
    dailyPriceUnit: Texts.dailyPriceUnit(days),
    dailyPriceName: Texts.dailyPriceName(days),
    totalPriceName: Texts.totalPriceName,
    dailyPriceContain: Texts.dailyPriceContain,
    totalPriceContain: Texts.totalPriceContain,
    dayPrice: minDPrice || 0,
    totalPrice: minTPrice || 0,
  };
};

/**
 * 校验车型是否售罄- 售罄的条件：车型售罄 || 车型下的所有供应商都售罄
 * @param {Array<string>} vendorSoldOutList 报价售罄列表
 * @param {Array<string>} vehicleSoldOutList 车型售罄列表
 * @param {Object} product 车型报价数据
 * @param {Object} activeGroupId 车型组code
 * @param {Object} recommendProducts 车型组列表
 * @return {boolean} 是否售罄
 */
const validateIsSoldOutSingleVehicle = (
  vendorSoldOutList,
  vehicleSoldOutList,
  product,
  activeGroupId = '',
  recommendProducts?,
) => {
  if (!product) {
    return false;
  }
  const { vehicleCode, productTopInfo, productRef } = product;
  if (vehicleSoldOutList.includes(vehicleCode)) {
    return true;
  }

  const vendorPriceList =
    getVendorPriceListByVehicleCode(
      vehicleCode,
      productTopInfo,
      activeGroupId,
      recommendProducts,
      productRef?.license,
    ) || [];
  const notSoldOutVendor = vendorPriceList.find(vendorItem => {
    const curKey = Utils.getProductKey(vendorItem?.reference);
    return !vendorSoldOutList.includes(curKey);
  });

  if (!vendorPriceList?.length) {
    return false;
  }

  if (!notSoldOutVendor) {
    return true;
  }

  return false;
};

export const validateIsSoldOut = (
  vendorSoldOutList,
  vehicleSoldOutList,
  product,
  isRecommend = false,
  activeGroupId = '',
  recommendProducts?,
) => {
  // 有无少结果推荐情况下，车型无售罄样式
  if (isRecommend) {
    return false;
  }
  // 多年款情况下是所有车型都售罄才算售罄
  if (product?.priceGroup?.length > 0) {
    const notSoldOut = product.priceGroup.find(
      item =>
        !validateIsSoldOutSingleVehicle(
          vendorSoldOutList,
          vehicleSoldOutList,
          item,
          activeGroupId,
          recommendProducts,
        ),
    );
    return !notSoldOut;
  }
  return validateIsSoldOutSingleVehicle(
    vendorSoldOutList,
    vehicleSoldOutList,
    product,
    activeGroupId,
    recommendProducts,
  );
};

export const mergeEasyLifeLabel = (tagsList, isEasyLife) => {
  let serviceTags = tagsList;
  // 无忧租集合标签
  if (serviceTags && serviceTags.length && isEasyLife) {
    const easyLifeSetLabelBase = serviceTags.filter(
      f => f.mergeId === Enums.LabelMergeType.EasyLife,
    )[0];
    if (easyLifeSetLabelBase) {
      const easyLifeSetLabel = { ...easyLifeSetLabelBase };
      let easyLifeSetLabelTitle = '';
      serviceTags.forEach(serviceTagItem => {
        if (serviceTagItem.mergeId === Enums.LabelMergeType.EasyLife) {
          if (!easyLifeSetLabelTitle) {
            easyLifeSetLabelTitle = serviceTagItem.title;
          } else {
            easyLifeSetLabelTitle += `·${serviceTagItem.title}`;
          }
        }
      });
      if (easyLifeSetLabelTitle) {
        easyLifeSetLabel.title = easyLifeSetLabelTitle;
        easyLifeSetLabel.colorCode = Enums.LabelColorCodeType.EasyLifeSet;
        const otherServiceTags =
          serviceTags.filter(
            f => f.mergeId !== Enums.LabelMergeType.EasyLife,
          ) || [];
        serviceTags = [{ ...easyLifeSetLabel }, ...otherServiceTags];
      }
    }
  }
  return serviceTags;
};

export const mergeVehicleYearLabel = (tagsList, sortBy) => {
  let vehicleTags = tagsList;
  // 车型标签合并
  if (vehicleTags?.length > 0) {
    const vehicleYearList = vehicleTags
      .filter(item => item.mergeId === Enums.LabelMergeType.VehicleYear)
      .sort((prev, next) => prev[sortBy] - next[sortBy]);
    if (vehicleYearList?.length > 1) {
      let vehicleYearTitle = '';
      const sortNum = vehicleYearList[0][sortBy];
      vehicleYearList.forEach(item => {
        if (!vehicleYearTitle) {
          vehicleYearTitle = item.title;
        } else {
          vehicleYearTitle += `·${item.title}`;
        }
      });
      if (vehicleYearTitle) {
        const filterTagList = [];
        vehicleTags.forEach(item => {
          const copyItem = { ...item };
          if (item.mergeId === Enums.LabelMergeType.VehicleYear) {
            if (vehicleYearTitle) {
              copyItem.title = vehicleYearTitle;
              copyItem[sortBy] = sortNum;
              vehicleYearTitle = '';
            } else {
              copyItem.isDelete = true;
            }
          }
          filterTagList.push(copyItem);
        });
        vehicleTags = filterTagList.filter(item => !item.isDelete);
      }
    }
  }
  return vehicleTags;
};

// 判断是否有信用租的产品
const getCreditRentTag = (allTags = []) => {
  return allTags.find(item =>
    CREDITRENT_LABELCODE_TYPES.includes(item.labelCode),
  );
};

// 组装新版供应商报价组件所需参数
export const packageVendorParam = (
  vendorPrice,
  isDiff: boolean,
  browVendorCode?: string,
  selectedFilters?: Array<string>,
  vehicleInfo?: VehicleInfoType,
  vehicleIndex?: number,
  vendorIndex?: number,
  isFit?: boolean,
  abVersion?: string,
  sortType?: number,
  priceSize?: number,
  belongTab?: string,
) => {
  if (!vendorPrice) {
    return null;
  }
  const {
    easyLifeInfo,
    urge,
    pStoreRouteDesc,
    rStoreRouteDesc,
    vendorName,
    isSelect,
    commentInfo,
    priceInfo,
    reference,
    allTags = [],
    uniqueCode,
    fees,
    newCar,
    lowestPrice,
  } = vendorPrice;
  const isEasyLife = easyLifeInfo?.isEasyLife;
  let vehicleTags = allTags.filter(
    f => f.groupId === Enums.LabelGroupType.Vehicle,
  );
  let serviceTags = allTags.filter(
    f => f.groupId === Enums.LabelGroupType.Service,
  );
  let marketTags = allTags.filter(
    f => f.groupId === Enums.LabelGroupType.Market,
  );
  let allLabels = [];
  const selfServiceLabel = allTags?.find(
    item => item?.labelCode === ILableCode.SelfService,
  );
  allLabels = allTags
    .filter(
      f =>
        (f.groupId === Enums.LabelGroupType.Vehicle ||
          f.groupId === Enums.LabelGroupType.Service ||
          f?.code === ILableCode.Limit) &&
        f?.code !== LabelCodeType.NATIONALCHNAIN &&
        f?.labelCode !== ILableCode.SelfService,
    )
    ?.sort((prev, next) => prev.sortBy - next.sortBy);

  // 车型标签合并
  vehicleTags = mergeVehicleYearLabel(vehicleTags, 'sortNum');

  // 无忧租集合标签
  serviceTags = mergeEasyLifeLabel(serviceTags, isEasyLife);

  // 是否自助取还
  const isSelfService = !!selfServiceLabel;

  if (marketTags.length > 1) {
    marketTags = [marketTags[0]];
  }

  const days = getTenancyDays();
  const commentCount = commentInfo?.commentCount;
  const vendorCode = reference?.vendorCode;
  const queryVid = getLogDataFromState()?.queryVid;
  const vendorPriceKey = Utils.getProductKey(reference);
  const isCouponedOrder = reference?.rCoup || 0;
  const couponIds = reference?.promtId ? `${reference.promtId}` : '';
  const logData = {
    isEasyLife,
    isOptimize: isSelect,
    isdropoffSource: Utils.getLogStrValue(vendorPrice?.reference?.rRc),
    isdropoffStation: getIsDropOffStation(),
    ispickupSource: Utils.getLogStrValue(vendorPrice?.reference?.pRc),
    ispickupStation: getIsPickupStation(),
    pStoreCode: reference?.pStoreCode,
    pickWayInfo: reference?.pickWayInfo,
    rStoreCode: reference?.rStoreCode,
    returnWayInfo: reference?.returnWayInfo,
    vehicleCode: vehicleInfo?.vehicleCode,
    vendorCode,
    vendorIndex,
    isCouponedOrder,
    couponIds,
    commentDesc: commentCount,
    score: commentInfo?.overallRating,
    currentDailyPrice: priceInfo?.currentDailyPrice,
    currentOriginalDailyPrice: priceInfo?.currentOriginalDailyPrice,
    currentTotalPrice: priceInfo?.currentTotalPrice,
    priceSize,
  };
  // 点击埋点数据
  const clickLogData = {
    bizVendorCode: vendorCode,
    groupId: vehicleInfo?.groupCode,
    groupName: vehicleInfo?.groupName,
    isCreditRent: !!getCreditRentTag(vendorPrice?.allTags || []),
    queryVid,
    selectedFilters,
    vehicleIndex,
    vehicleName: vehicleInfo?.name,
    vendorName: vendorPrice?.vendorName,
    sourceType: isFit ? '1' : '2',
    sortType,
    abVersion,
    skuId: reference?.skuId, // 车型skuid（仅卡拉比车型有）
    recommendType: reference?.gsDesc || '', // 货架推荐类型
    totalPrice: priceInfo?.currentTotalPrice, // 总价 (不包括加购的保险)
    dailyPrice: priceInfo?.currentDailyPrice, // 日均价
    allTags,
    ...logData,
  };
  const activeGroupId = getCurProductGroupId();
  // 曝光埋点数据
  const exposureLogData = {
    pageId: Channel.getPageId().VendorList.ID,
    info: {
      belongTab,
      totalPrice: priceInfo?.currentTotalPrice, // 总价 (不包括加购的保险)
      dailyPrice: priceInfo?.currentDailyPrice, // 日均价
      isLowestPrice: lowestPrice,
      virtualParent: activeGroupId,
      // allTags 里匹配免费取消、限时免费取消、有损取消这三种标签，如果有就返回
      cancelMethod:
        allTags?.find(tag =>
          ['免费取消', '限时免费取消', '有损取消'].includes(tag?.title),
        )?.title || '',
      isSelectedVendor: isSelect ? 1 : 0,
      vehicleCode: vehicleInfo?.vehicleCode,
      pStoreCode: reference?.pStoreCode,
      rStoreCode: reference?.rStoreCode,
      carAge: vendorPrice?.carAge, // 车型车龄
    },
    queryVid,
    groupId: vehicleInfo?.groupCode,
    selectedFilters,
    abVersion,
    sortType,
    sourceType: isFit ? '1' : '2',
    skuId: reference?.skuId, // 车型skuid（仅卡拉比车型有）
    recommendType: reference?.gsDesc || '', // 货架推荐类型
    totalPrice: priceInfo?.currentTotalPrice, // 总价 (不包括加购的保险)
    layerData: {
      ...logData,
      distance: vendorPrice?.distance,
      rDistance: vendorPrice?.rDistance,
      allTags,
      vendorCarAgeLevel: reference?.vehicleLevelInfo?.vLevel,
      platformCarAgeLevel: reference?.vehicleLevelInfo?.apiCode,
      isSelfService: isSelfService ? 1 : 0,
    },
  };
  const commentDesc = `${commentCount}评价`;
  return {
    isEasyLife,
    almostSoldOutLabel: urge,
    pickUpDesc: pStoreRouteDesc,
    dropOffDesc: rStoreRouteDesc,
    isPickUpRentCenter: reference?.pRc > 0, // 取车是否是租车中心
    isDropOffRentCenter: reference?.rRc > 0, // 还车是否是租车中心
    pickUpRentCenterName: ListResSelectors?.getRentCenter()?.name,
    dropOffRentCenterName: ListResSelectors?.getDropOffRentCenter()?.name,
    vehicleTags,
    allLabels,
    selfServiceLabel,
    serviceTags,
    marketTags,
    newCar, // 新车标识
    lowestPrice, // 低价标识
    hint: reference?.gsDesc, // 推荐语
    skuId: reference?.skuId, // 车型skuid（仅卡拉比车型有）
    priceDescProps: {
      dayPrice: priceInfo?.currentDailyPrice,
      originDailyPrice: priceInfo?.currentOriginalDailyPrice,
      dayText: Texts.dailyPriceUnit(days),
      totalText: Texts.totalPriceName,
      totalPrice: priceInfo?.currentTotalPrice,
      currentCurrencyCode: priceInfo?.currentCurrencyCode,
    },
    vendorName, // 供应商名称
    isOptimize: isSelect, // 是否优选
    nationalChainTagTitle: getNationalChainTag(allTags)?.title, // 全国连锁标签
    score: commentInfo?.hasComment === 0 ? 0 : commentInfo?.overallRating, // 点评分数，如果hasComment=0，则展示成“暂无评分”，反之展示对应的分数
    commentDesc: commentCount > 0 ? commentDesc : '', // 点评数量的描述
    uniqueCode,
    vendorCode,
    isShowAtTop: browVendorCode === vendorCode, // 是否是正在浏览的报价
    vendorPriceKey, // 售罄标识
    clickLogData, // 点击埋点数据
    exposureLogData, // 曝光埋点数据
    hasFees: fees?.length > 0,
    isCouponBook: reference?.rCoup === 1, // 是否是领券订产品
    isSelfService, // 是否自助取还
    belongTab, // 货架一期所在的Tab名称
  };
};

// 组装货架2.0楼层数据
export const packageShelvesFloorParam = (
  floor: Floor,
  selectedFilters?: Array<string>,
  vehicleInfo?: VehicleInfoType,
  vehicleIndex?: number,
  productIndex?: number,
  abVersion?: string,
  sortType?: number,
) => {
  if (!floor) {
    return null;
  }
  const {
    isSelect,
    packageList,
    alltags = [],
    lowestPrice,
    floorName,
    floorId,
    vendorName,
  } = floor;
  let carAge = '';
  if (floorName?.length > 0) {
    carAge = floorName[floorName.length - 1];
  }
  const { priceInfo, reference, code, name } = packageList?.[0] || {};
  const selfServiceLabel = alltags?.find(
    item => item?.labelCode === ILableCode.SelfService,
  );
  // 是否自助取还
  const isSelfService = !!selfServiceLabel;

  const vendorCode = reference?.vendorCode;
  const queryVid = getLogDataFromState()?.queryVid;
  const isCouponedOrder = reference?.rCoup || 0;
  const couponIds = reference?.promtId ? `${reference.promtId}` : '';
  const logData = {
    isOptimize: isSelect,
    isdropoffSource: Utils.getLogStrValue(reference?.rRc),
    isdropoffStation: getIsDropOffStation(),
    ispickupSource: Utils.getLogStrValue(reference?.pRc),
    ispickupStation: getIsPickupStation(),
    pStoreCode: reference?.pStoreCode,
    pickWayInfo: reference?.pickWayInfo,
    rStoreCode: reference?.rStoreCode,
    returnWayInfo: reference?.returnWayInfo,
    vehicleCode: vehicleInfo?.vehicleCode,
    vendorCode,
    isCouponedOrder,
    couponIds,
    currentDailyPrice: priceInfo?.currentDailyPrice,
    currentOriginalDailyPrice: priceInfo?.currentOriginalDailyPrice,
    currentTotalPrice: priceInfo?.currentTotalPrice,
    priceSize: packageList?.length,
    carAge,
    productIndex,
    productName: name,
    vendorName,
    floorId,
    skuId: reference?.skuId, // 车型skuid（仅卡拉比车型有）
    packageCode: code,
    dailyPrice: priceInfo?.currentDailyPrice,
    totalPrice: priceInfo?.currentTotalPrice,
    isSelfService: isSelfService ? 1 : 0,
    lowestPrice,
  };
  // 点击埋点数据
  const clickLogData = {
    bizVendorCode: vendorCode,
    groupId: vehicleInfo?.groupCode,
    groupName: vehicleInfo?.groupName,
    isCreditRent: !!getCreditRentTag(alltags || []),
    queryVid,
    selectedFilters,
    vehicleIndex,
    vehicleName: vehicleInfo?.name,
    sourceType: '1',
    sortType,
    abVersion,
    skuId: reference?.skuId, // 车型skuid（仅卡拉比车型有）
    recommendType: reference?.gsDesc || '', // 货架推荐类型
    totalPrice: priceInfo?.currentTotalPrice, // 总价 (不包括加购的保险)
    dailyPrice: priceInfo?.currentDailyPrice, // 日均价
    allTags: alltags,
    ...logData,
  };
  const activeGroupId = getCurProductGroupId();
  // 曝光埋点数据
  const exposureLogData = {
    pageId: Channel.getPageId().VendorList.ID,
    info: {
      totalPrice: priceInfo?.currentTotalPrice, // 总价 (不包括加购的保险)
      dailyPrice: priceInfo?.currentDailyPrice, // 日均价
      isLowestPrice: lowestPrice,
      virtualParent: activeGroupId,
      // allTags 里匹配免费取消、限时免费取消、有损取消这三种标签，如果有就返回
      cancelMethod:
        alltags?.find(tag =>
          ['免费取消', '限时免费取消', '有损取消'].includes(tag?.title),
        )?.title || '',
      isSelectedVendor: isSelect ? 1 : 0,
      vehicleCode: vehicleInfo?.vehicleCode,
      pStoreCode: reference?.pStoreCode,
      rStoreCode: reference?.rStoreCode,
      carAge,
    },
    queryVid,
    groupId: vehicleInfo?.groupCode,
    selectedFilters,
    abVersion,
    sortType,
    sourceType: '1',
    skuId: reference?.skuId, // 车型skuid（仅卡拉比车型有）
    recommendType: reference?.gsDesc || '', // 货架推荐类型
    totalPrice: priceInfo?.currentTotalPrice, // 总价 (不包括加购的保险)
    layerData: {
      ...logData,
      allTags: alltags,
      vendorCarAgeLevel: reference?.vehicleLevelInfo?.vLevel,
      platformCarAgeLevel: reference?.vehicleLevelInfo?.apiCode,
    },
  };
  return {
    isSelfService: isSelfService ? 1 : 0,
    clickLogData, // 点击埋点数据
    exposureLogData, // 曝光埋点数据
    ...floor,
  };
};

export const getSecretBoxItemTagsAndPrice = (
  vendorPrice: VendorPriceListType,
) => {
  if (!vendorPrice) {
    return null;
  }
  const { easyLifeInfo, allTags = [], priceInfo = {} } = vendorPrice;
  const isEasyLife = easyLifeInfo?.isEasyLife;
  const marketTags = allTags.find(
    f => f.groupId === Enums.LabelGroupType.Market,
  );
  let serviceTags = allTags.filter(
    f => f.groupId === Enums.LabelGroupType.Service,
  );
  serviceTags = mergeEasyLifeLabel(serviceTags, isEasyLife);
  const { currentDailyPrice, currentOriginalDailyPrice, currentTotalPrice } =
    priceInfo;
  const originPrice =
    currentOriginalDailyPrice > 0
      ? {
          price: currentOriginalDailyPrice,
        }
      : null;
  const tagList = [];
  if (marketTags) {
    tagList.push(marketTags);
  }
  return {
    serviceTags,
    marketTags: tagList,
    currentDailyPrice,
    currentTotalPrice,
    originPrice,
  };
};

// 组装车型报价入口曝光埋点数据
export const getProductEnterExposureData = (
  productItem: ProductListType,
  index,
) => {
  const vendorPriceList = getVendorPriceListByVehicleCode(
    productItem?.vehicleCode,
    productItem?.productTopInfo,
    null,
    null,
    productItem?.productRef?.license,
  );
  const isSelectedVendorTop1 = vendorPriceList?.[0]?.isSelect;
  return {
    isEasyLife: !!productItem.isEasy, // 是否含有无忧租
    isOptimize: !!productItem.isOptim, // 是否是优选
    isCreditRent: !!productItem.isCredit, // 是否含有信用租产品
    minTPrice: productItem?.minTPrice, // 最低总价
    minDPrice: productItem?.minDPrice, // 最低日价
    vehicleCode: productItem?.vehicleCode, // 车型code
    isHasCouponVehicle: productItem?.rCoup || 0, // 是否含有领券订产品
    license: productItem?.productRef?.license,
    vendorCode: vendorPriceList?.[0]?.reference?.vendorCode,
    pStoreCode: vendorPriceList?.[0]?.reference?.pStoreCode,
    rStoreCode: vendorPriceList?.[0]?.reference?.rStoreCode,
    isSelfService: productItem?.outTags?.find(
      item => item?.labelCode === CommonEnums.ILableCode.SelfService,
    )
      ? 1
      : 0,
    smallGroupRank: index, // 报价聚合组排序
    isSelectedVendorTop1: isSelectedVendorTop1 ? 1 : 0, // 是否是选中供应商第一位
    topSelectedVendorCarAge:
      (isSelectedVendorTop1 && vendorPriceList?.[0]?.carAge) || '',
  };
};

// 组装新详情页首屏渲染需要的数据
export const getVendorListFirstScreenParam = (
  vehicleCode,
  vehicleList?,
  productRef?,
) => {
  const vehicleInfo = getVehicleInfoByCode(
    vehicleCode,
    vehicleList,
    productRef,
  );
  return {
    vehicleInfo: lodashOmit(vehicleInfo, ['vehicleAccessoryImages']),
  };
};

// 判断车型渲染信息是否相等
export const validateRenderVehicleIsEqual = (preData, nextData) => {
  if (preData.renderUniqId !== nextData.renderUniqId) {
    return false;
  }

  const prePriceGroupLen = preData?.priceGroup?.length;
  const nextPriceGroupLen = nextData?.priceGroup?.length;
  if (prePriceGroupLen !== nextPriceGroupLen) {
    return false;
  }

  // 多年款的情况,需每个车型的uniqId都需相等
  if (prePriceGroupLen > 0 && nextPriceGroupLen > 0) {
    const prePriceGroupUniqIdList = [];
    const nextPriceGroupUniqIdList = [];
    preData?.priceGroup?.forEach(priceGroupItem => {
      prePriceGroupUniqIdList.push(priceGroupItem.uniqId);
    });
    nextData?.priceGroup?.forEach(priceGroupItem => {
      nextPriceGroupUniqIdList.push(priceGroupItem.uniqId);
    });
    if (
      prePriceGroupUniqIdList.toString() !== nextPriceGroupUniqIdList.toString()
    ) {
      return false;
    }
  }

  return true;
};

export const getVehicleLabelsGroupName = vehicle => {
  const { groupName } = vehicle;
  return !groupName
    ? []
    : [
        {
          text: groupName,
          icon: {
            iconContent: icon.car,
          },
        },
      ];
};

const getMergeVehicleExtendInfo = memoize((group: number) => {
  const infos = getMergeVehicleExtendInfos() || [];
  return infos?.find(item => item?.group === group);
});

export const getNewPriceGroup = memoize((group: number, priceGroup: any[]) => {
  const mergeVehicleExtendInfo = getMergeVehicleExtendInfo(group);
  if (mergeVehicleExtendInfo && priceGroup?.length) {
    const noNeedFoldGroup = [];
    const needFoldGroup = [];
    let needFold = false;
    priceGroup.forEach(item => {
      if (
        item?.showType === mergeVehicleExtendInfo.extendType &&
        item?.showType === LicenseShowType.fold
      ) {
        needFold = true;
        needFoldGroup.push(item);
      } else {
        noNeedFoldGroup.push(item);
      }
    });
    return needFold
      ? {
          priceGroup: [...noNeedFoldGroup, ...needFoldGroup],
          showNumber: needFoldGroup.length ? noNeedFoldGroup.length : 0,
          needFold: !!needFoldGroup.length,
          foldBtnContent:
            mergeVehicleExtendInfo.contents?.[0]?.stringObjs?.[0]?.content,
        }
      : {
          priceGroup,
        };
  }
  return {
    priceGroup,
  };
});
