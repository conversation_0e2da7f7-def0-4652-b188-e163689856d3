import {
  get as lodashGet,
  isEmpty as lodashIsEmpty,
  pick as lodashPick,
} from 'lodash-es';
import Loading from '@c2x/apis/Loading';
import Encrypt from '@c2x/apis/Encrypt';
import { xShowToast } from '@ctrip/xtaro';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { put, select, takeEvery, takeLatest } from 'redux-saga/effects';

import uuid from 'uuid';
import { InvokeFrom } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailType';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  QUERY_PRODUCT,
  QUERY_PRICE_INFO,
  SELECT_PACKAGE,
  CHANGE_EXTRAS_NUM,
  CHANGE_SELECT_INSURANCE,
  PRODUCT_PAGE_PRE_CACHE,
  VALIDATE_DOWNGRADE,
  QUERY_EQUIPMENT_INFO,
  QUERY_LICENSE_POLICY,
  QUERY_COUNTRYS_INFO,
} from './Types';
import {
  queryProductCallBack,
  setStatus,
  queryPriceInfo,
  undoChangePrice,
  showPriceConfirm,
  setPickupDownGradePopVisible,
  changeAllSelectInsurance,
  queryEquipmentInfo,
  queryEquipmentInfoCallBack,
  queryLicencePolicyCallBack,
  setCountrysInfo,
  setInitAddOnCodes,
} from './Actions';
import {
  getPointInfoParmas,
  getPickUpTime,
  getDropOffTime,
} from '../LocationAndDate/Selectors';
import {
  getProductState,
  getSelectedInsuranceId,
  getAddOnCodes,
  getCurInsPackageId,
  getIsFail,
} from './Selectors';
import {
  CarFetch,
  Utils,
  CarStorage,
  CarLog,
  User,
  Channel,
  AppContext,
  CarServerABTesting,
} from '../../Util/Index';
import {
  StorageKey,
  LogKey,
  ApiResCode,
  LogKeyDev,
} from '../../Constants/Index';
import {
  ProductReqAndResData,
  ProductSelectors,
} from '../../Global/Cache/Index';
import Texts from './Texts';
import notMatchTexts from '../../Pages/Product/Texts';
import { PriceAlertType } from '../../ComponentBusiness/Common/src/Enums';
import {
  getCurPackage,
  getCurPackageIsEasyLife,
  getProduct,
  getCurPayModeInfo,
  getCurDepositPayInfo,
  getCurPriceInfo,
  getDriveTimeReqParam,
} from './Mappers';
import { getAge } from '../DriverAgeAndNumber/Selectors';
import {
  getIsSelectedPackageValid,
  getSelectedCouponsCodes,
  getUniqueReference,
} from '../Booking/Selectors';
import { getUniqueCode } from '../VendorList/Selectors';
import {
  getPriceReqPassenger,
  getPriceReqDefaultPassengerData,
  getPassenger,
} from '../DriverList/Selectors';
import { createUserCache } from '../PreCache/Actions';
import {
  fetchApiDriverList,
  fetchApiIdCardList,
  selectDriver,
} from '../DriverList/Actions';
import { LogBookQueryPrice, LogProductInfo } from './UBTLog';
import {
  getRealRequestId,
  validateIsFromMemoryCache,
} from '../../Global/Cache/ListResSelectors';
import { initSesameAuthState } from '../Sesame/Actions';
import { setSupplierData } from '../SupplierData/Actions';
import { PriceTimer } from './Model';
import { ActionType } from '../../Types/ActionType';
import { PREVIOUS_STATE } from '../__Global/Types';
import { verifyPointInfo } from '../List/Method';
import { requestInfoType } from '../../Util/CarFetchHelper';
import { queryVehicleDetailInfo } from '../ProductConfirm/Actions';
import { QueryEquipmentInfoRes } from './FuntionTypes';
import {
  isValidRentalDate,
  ValidRentalDateLogType,
} from '../LocationAndDate/Util';
import { PAYMODE } from '../Booking/Types';
import { addSaleOutList, removeSaleOutList } from '../List/Actions';
import {
  getEmptyGuideInfo,
  getProductRequestReference,
  getPickupStoreInfo,
} from '../../Global/Cache/ProductSelectors';
import {
  setListStatusData,
  listStatusKeyList,
  setFromPage,
} from '../../Global/Cache/ListReqAndResData';
import { getSaleOutList } from '../List/Selectors';
import { getRebookParamsOsd } from '../ModifyOrder/CommonSelector';
import { IInsuranceCode } from '../../Types/Dto/QueryProductInfoType';

interface LogDataType {
  isError: boolean;
  param: Object;
  res: Object;
  taskQueryDriverSuccess?: boolean;
}

const logProductPageLoad = ({
  isError,
  param,
  res,
  taskQueryDriverSuccess,
}: LogDataType) => {
  const isSuccess = !isError && ProductSelectors.getProductResSuccess();
  const baseResponse = lodashGet(res, 'baseResponse') || {};
  const isSelected = !!lodashGet(res, 'isSelected');
  const gs = lodashGet(res, 'gs') || {};
  const { id = 0, title = '' } = gs;
  const isEasyLife = getCurPackageIsEasyLife();
  const info = {
    isOpenSuccess: isSuccess,
    eventResult: isSuccess,
    request: JSON.stringify(param),
    baseResponse: JSON.stringify(baseResponse),
    failRes: isSuccess ? null : Utils.composeError2String(res),
    resCode: baseResponse.code,
    errorCode: isError ? ApiResCode.TraceCode.E1001 : '',
    serverErrorCode: baseResponse.errorCode || '',
    serverErrorMsg: baseResponse.message || '',
    // @ts-ignore
    fetchCost: new Date() - param.now,
    // 默认驾驶员是否请求成功
    taskQueryDriverSuccess,
    expCode:
      baseResponse?.code ||
      (isError && Utils.getFrontEndExpCode(null, res)) ||
      '',
    expMsg:
      baseResponse?.returnMsg || (isError && Utils.getErrorMessage(res)) || '',
  };
  CarLog.LogTrace({
    key: LogKey.c_car_trace_product_page_load,
    info,
  });
  // 2022-8-2 预订流程度需同步一份dev埋点
  CarLog.LogTraceDev({
    key: LogKeyDev.c_car_dev_product_page_load,
    info,
  });
  if (Utils.isCtripIsd()) {
    // 货架信息产品详情页漏斗埋点
    CarLog.LogTrace({
      key: LogKey.vac_car_trace_product_goodsshelf,
      info: {
        gsId: id,
        gsName: title,
        isEasyLife,
        isOptimize: isSelected,
      },
    });
  }
};

export const logPriceLoad = ({ isError, success, param, res }) => {
  // 过滤超时 res 为 undefined 场景，去除接口超时重复上报
  let errorCode = '';
  if (isError && !res) {
    errorCode = ApiResCode.TraceCode.E1004;
  } else if (isError) {
    errorCode = ApiResCode.TraceCode.E1001;
  }
  const baseResponse = lodashGet(res, 'baseResponse') || {};
  const info = {
    eventResult: success,
    request: JSON.stringify(param),
    requestId: lodashGet(param, 'requestId', ''),
    baseResponse: JSON.stringify(baseResponse),
    failRes: success ? null : Utils.composeError2String(res),
    resCode: baseResponse.code,
    errorCode,
    serverErrorCode: baseResponse.errorCode || '',
    serverErrorMsg: baseResponse.message || '',
    expCode: baseResponse?.code || errorCode,
    expMsg:
      baseResponse?.returnMsg || (isError && Utils.getErrorMessage(res)) || '',
  };
  CarLog.LogTrace({
    key: LogKey.c_car_trace_product_price_load,
    info,
  });
  // 2022-8-1 预订流程度需同步一份dev埋点
  CarLog.LogTraceDev({
    key: LogKeyDev.c_car_dev_product_price_load,
    info,
  });
};

const getProductInfoParams = (state, action) => {
  const { pickupPointInfo, returnPointInfo } = getPointInfoParmas(true)(state);
  const verifyRequestParameters = () => {
    return verifyPointInfo(pickupPointInfo) && verifyPointInfo(returnPointInfo);
  };
  const verifyParametersCoincident = (resRequestInfo: requestInfoType) => {
    return Utils.isRequestParamCoincident(
      resRequestInfo,
      state?.LocationAndDate,
    );
  };
  const param = {
    pickupPointInfo,
    returnPointInfo,
    invokeFrom: InvokeFrom.Detail,
    ...action.data,
    now: new Date(),
    extraMaps: {
      listParentRequestId: getRealRequestId(),
      withCache: validateIsFromMemoryCache(),
    },
    appRequestMap: {
      verifyRequestParameters,
      verifyParametersCoincident,
    },
  };
  param.requestId = uuid();
  if (Utils.isCtripIsd()) {
    param.reference = getUniqueReference(state);
    // 使用QueryVehicleDetailList返回的uniqueCode作为缓存标志
    param.vcCacheKey = getUniqueCode(state);
  }
  return param;
};

const getQueryDriverParam = reference => {
  const vendorCode = reference?.vendorCode;
  const storeId = reference?.pStoreCode;
  const queryDriverParam: any = {
    vendorCode,
    storeId,
    reference,
  };
  try {
    const rebookDefaultPassenger = JSON.parse(
      decodeURIComponent(AppContext.UrlQuery?.passenger),
    );
    if (!lodashIsEmpty(rebookDefaultPassenger)) {
      queryDriverParam.passenger = rebookDefaultPassenger;
    }
  } catch (e) {
    /* eslint-disable no-empty-pattern */
  }
  return queryDriverParam;
};

function* handleFailAction() {
  const isFail = getIsFail(yield select());
  const saleOutList = getSaleOutList(yield select());
  const productKey = Utils.getProductKey(getProductRequestReference());
  if (!isFail) {
    if (saleOutList?.length && saleOutList.includes(productKey)) {
      yield put(removeSaleOutList({ saleOutProductKey: productKey }));
    }
    return;
  }
  const notMatchButtonInfos = getEmptyGuideInfo()?.buttonInfo;
  const hasType1 = notMatchButtonInfos?.some(v => v?.buttonType === 'Type1');
  const hasType5 = notMatchButtonInfos?.some(v => v?.buttonType === 'Type5');
  const hasType6 = notMatchButtonInfos?.some(v => v?.buttonType === 'Type6');

  if (hasType1) {
    yield put(
      addSaleOutList({
        priceSoldOutText: notMatchTexts.listCombineTempCannotBook,
        saleOutProductKey: productKey,
      }),
    );
    return;
  }
  if (hasType5) {
    setListStatusData(listStatusKeyList.refreshFromOSDProductPage, true);
    setFromPage(AppContext.PageInstance.getPageId());
    AppContext.setUserFetchCacheId({
      actionType: 'refreshFromOSDProductPage',
    });
    return;
  }
  if (hasType6) {
    yield put(
      addSaleOutList({
        priceSoldOutText: notMatchTexts.listCombineSoldOut,
        saleOutProductKey: productKey,
      }),
    );
  }
}

export function* queryProductInfo() {
  yield takeLatest(QUERY_PRODUCT, function* logic(action: ActionType) {
    const state = yield select();
    const {
      reset,
      isProductLoading = true,
      isLoginRefresh,
      isBooking,
      isStartPriceTimer,
      clearVcCacheKey,
    } = action.data;

    const param = getProductInfoParams(state, action);

    if (clearVcCacheKey) {
      // 填写页刷新需要清除vcCacheKey
      param.vcCacheKey = undefined;
    }

    yield put(
      setStatus({ isProductLoading, isPriceLoading: true, isFail: false }),
    );

    try {
      let taskQueryDriverSuccess = true;

      const queryDriverParam = getQueryDriverParam(param?.reference);

      if (Utils.isCtripIsd()) {
        // 国内请求默认驾驶员 + 初始化驾驶员列表
        yield put(
          fetchApiDriverList({
            setDefaultPassenger: true,
            queryDriverParam,
            callback: isSuccess => {
              taskQueryDriverSuccess = isSuccess;
            },
          }),
        );
      }
      const pTime = getPickUpTime(state);
      const rTime = getDropOffTime(state);
      if (
        // @ts-ignore
        // eslint-disable-next-line no-underscore-dangle
        !global.__TESTING_AUTOMATION__ &&
        Utils.isCtripOsd() &&
        !isValidRentalDate(
          pTime,
          rTime,
          true,
          false,
          ValidRentalDateLogType.ProductCarFetch,
        )
      ) {
        PriceTimer.alertChangeTime();
        return;
      }
      const res = yield CarFetch.queryProductInfo(param);
      const resStatus = { isError: false, param, res, reset, isLoginRefresh };
      yield put(queryProductCallBack(resStatus));

      if (res?.stockLevelPrompt) {
        // 车龄与详情页发生变化，出toast:已重新计算车辆租金及优惠金额
        xShowToast({ title: res?.stockLevelPrompt, duration: 3000 });
      }

      // 读取营业执照相关信息
      if (Utils.isCtripIsd()) {
        yield put(
          setSupplierData({
            licenseDesc: Texts.licenseDesc,
            companyName: res?.vendorInfo?.vendorFullName,
            licenseImgUrl: res?.vendorInfo?.vendorLicenseUrl,
            vendorId: res?.vendorInfo?.vendorCode,
          }),
        );
      }

      logProductPageLoad({ ...resStatus, taskQueryDriverSuccess });

      if (Utils.isCtripOsd()) {
        yield handleFailAction();
      }

      const isSuccess = ProductSelectors.getProductResSuccess();
      if (isSuccess) {
        const isISDShelves2B = CarServerABTesting.isISDShelves2B();
        const isRP = ProductSelectors.getIsRP();
        const { packageLevel } = param.reference || {};
        if (isISDShelves2B && isRP) {
          const addOnCode = IInsuranceCode[packageLevel];
          yield put(setInitAddOnCodes([addOnCode]));
        }
        const curInsPackageId = getCurInsPackageId(state);
        const { ageRestriction = {} } = getCurPriceInfo(curInsPackageId) || {};
        const { maxDriverAge, minDriverAge } = ageRestriction;
        const newQueryDriverParam = {
          ...queryDriverParam,
          minAge: minDriverAge,
          maxAge: maxDriverAge,
        };
        // 境外需要拿到详情页结果后，再去请求默认驾驶员
        if (Utils.isCtripOsd()) {
          // 查询产品详情页数据时，静默查询驾驶员信息
          yield put(
            fetchApiDriverList({
              setDefaultPassenger: true,
              isForceUpdate: true,
              queryDriverParam: newQueryDriverParam,
              callback: isSuccessNow => {
                taskQueryDriverSuccess = isSuccessNow;
              },
            }),
          );
          // 境外打开埋点
          LogProductInfo();
          // 境外详情页失败项目，拆分额外设备接口
          yield put(
            queryEquipmentInfo({
              productReqParams: param,
            }),
          );
        }
        // 【国内】防止在queryPriceInfo之前驾驶员接口未返回，重新请求一次.因为queryPriceInfo依赖驾驶员信息返回年龄限制话术，参考MR:4871
        const curState = yield select();
        let defaultPassenger = getPassenger(curState);
        if (!defaultPassenger?.passengerId) {
          const passengerRes =
            yield CarFetch.queryDriverList(newQueryDriverParam);
          defaultPassenger = passengerRes?.passengerList?.find(
            v => v.isDefault,
          );
          yield put(selectDriver(defaultPassenger));
        }

        yield put(
          queryPriceInfo({
            isInit: !!reset,
            // @ts-ignore
            customerInfo: getPriceReqDefaultPassengerData(
              defaultPassenger,
              res.idCardTypes,
            ),
            isStartPriceTimer,
          }),
        );

        if (Utils.isCtripIsd()) {
          const { vendorInsuranceDesc = {} } = res || {};
          CarStorage.saveIsd(
            StorageKey.CAR_ISD_VENDORINSURANCE,
            vendorInsuranceDesc,
            undefined,
            'rn_car_isd',
          );
        }

        const isLogin = User.isLoginSync();
        if (isLogin) {
          if (Utils.isCtripIsd()) {
            const vendorCode = param.reference?.vendorCode;
            yield put(fetchApiIdCardList({ vendorCode }));
            yield put(initSesameAuthState({ isInitial: true, isBooking }));
          }
        }
      }
    } catch (error) {
      if (error?.cancelTimeout) {
        return;
      }
      const resStatus = {
        isError: true,
        param,
        res: error,
        reset,
        isLoginRefresh,
      };
      yield put(queryProductCallBack(resStatus));
      logProductPageLoad(resStatus);
    }
  });
}

const getSelectedInsuranceIdParam = state => {
  if (Utils.isCtripIsd()) {
    const curInsPackageId = getSelectedInsuranceId(state);
    return curInsPackageId.map(id => +id);
  }
  const { curInsPackageId } = getProductState(state);
  const { ctripInsuranceIds } = getProduct(curInsPackageId);
  return ctripInsuranceIds;
};

export const getPirceReqParam = (state, data) => {
  const requestId = ProductSelectors.getProductRequestId();
  const {
    curInsPackageId,
    curPackageId,
    curBomCode,
    selectedExtras = [],
    payMode,
    showPayMode,
    depositPayType,
    nextDepositPayType,
  } = getProductState(state);
  const rebookParamsOsd = getRebookParamsOsd(state);
  const currentPackageId = data?.curPackageId || curPackageId;
  const { packageName, subType } = getCurPackage(curInsPackageId);
  const isEasyLife = getCurPackageIsEasyLife();
  const equipments = selectedExtras.map(extra => ({
    ...extra,
    localTotalPrice: extra.localTotalPrice?.toString(), // 价格转字符串解决精度问题
    localDailyPrice: extra.localDailyPrice?.toString(),
    currentDailyPrice: extra.currentDailyPrice?.toString(),
    currentTotalPrice: extra.currentTotalPrice?.toString(),
    quantity: extra.currentNum,
  }));
  const reference = ProductSelectors.getProductRequestReference();

  const currentPayMode = Utils.isCtripIsd() ? showPayMode : payMode;

  const verifyRequestParameters = () => {
    return (
      Utils.isValid(currentPackageId) &&
      Utils.isValid(currentPayMode) &&
      Utils.isValid(depositPayType)
    );
  };

  const param = {
    uuid: uuid(),
    requestId,
    depositPayType,
    nextDepositPayType,
    currentPayMode,
    currentBomCode: curBomCode,
    currentPackageName: packageName,
    currentPackageId,
    insPackageId: curInsPackageId,
    equipments: equipments || [],
    selectedCouponsCodes: getSelectedCouponsCodes(state),
    // trip 不可点击活动
    selectedActiveCode: '',
    // 区分基础套餐跟全面无忧租套餐
    subType: subType || 0,
    // 自营险ID
    selectedInsuranceId: getSelectedInsuranceIdParam(state),
    addOnCodes: getAddOnCodes(state),
    age: parseInt(getAge(state), 10),
    customerInfo: getPriceReqPassenger(state),
    // 砍价下线
    // "packageId4CutPrice": 0,
    isEasyLife,
    ...lodashPick(data, [
      'isInit',
      'customerInfo',
      'selectedInsuranceId',
      'serviceExtras',
    ]),
    // 埋点需求添加
    // http://iwork.ctripcorp.com/sync/opencard/2567/4904/13177/519785
    priceVersion: reference.priceVersion,
    originalCouponCode: AppContext.originalCouponCode,
    originalActivityId: AppContext.originalActivityId,
    originalActivityName: AppContext.originalActivityName,
    appRequestMap: {
      verifyRequestParameters,
    },
    originalOrderId: rebookParamsOsd?.ctripOrderId,
  };

  return param;
};

function* getPriceInfo() {
  yield takeLatest(
    [
      QUERY_PRICE_INFO,
      SELECT_PACKAGE,
      CHANGE_EXTRAS_NUM,
      CHANGE_SELECT_INSURANCE,
      PAYMODE,
    ],

    function* logic(action: ActionType) {
      // 产品详情接口还没返回，但是已经回退的情况
      const requestId = ProductSelectors.getProductRequestId();
      if (!requestId) {
        return;
      }
      const state = yield select();

      if (!getIsSelectedPackageValid(state)) {
        CarLog.LogDevError({
          expPoint: 'osd extra error',
          expMsg: '当前packageId对应的equipments没有数据',
          requestId: ProductSelectors.getProductRequestId(),
        });
      }
      // 原逻辑：transform 中获取state后保存在全局对像中
      // 可以直接在此处获取，执行到这里时state未变更
      const previousState = action[PREVIOUS_STATE];

      const param = getPirceReqParam(state, action.data);
      const { isStartPriceTimer } = action.data || {};
      yield put(
        setStatus({
          isPriceFail: false,
          isPriceLoading: true,
          isPriceTimerLoading: isStartPriceTimer,
        }),
      );

      const undoChangePriceFn = undoChangePrice({
        ...getProductState(previousState),
        isPriceLoading: false,
        isPriceFail: true,
        isPriceTimerLoading: false,
      });

      let isError = false;
      const res = yield CarFetch.queryPriceInfo(param, {
        latest: true,
      }).catch(error => {
        if (error?.cancelTimeout) {
          return;
        }
        isError = true;
      });

      if (res?.canceled) {
        return;
      }

      const success = lodashGet(res, 'baseResponse.isSuccess') === true;
      logPriceLoad({
        isError,
        success,
        param,
        res,
      });

      if (!success) {
        PriceTimer.setAlertData(PriceAlertType.PriceCacheError);
        yield put(undoChangePriceFn);
        yield put(showPriceConfirm(true));
        return;
      }

      ProductReqAndResData.setData(
        ProductReqAndResData.keyList.priceReq,
        param,
      );
      ProductReqAndResData.setData(ProductReqAndResData.keyList.priceRes, res);

      // 填写页埋点
      if (
        AppContext.PageInstance.getPageId() === Channel.getPageId().Book.ID ||
        AppContext.PageInstance.getPageId() === Channel.getPageId().Product.ID
      ) {
        LogBookQueryPrice();
      }

      const {
        packageId,
        showPayMode: nextShowPayMode,
        payMode: nextPayMode,
      } = getCurPayModeInfo();
      const { depositPayType: fixDepositPayType } = getCurDepositPayInfo();
      if (
        Utils.isCtripIsd() &&
        res?.insuranceAvailable?.clickable === false &&
        getSelectedInsuranceId(yield select())?.length > 0
      ) {
        // 不能加购自营险
        yield put(changeAllSelectInsurance([]));
      }
      yield put(
        setStatus({
          isPriceLoading: false,
          isPriceTimerLoading: false,
          curPackageId: packageId,
          showPayMode: nextShowPayMode,
          payMode: nextPayMode,
          depositPayType: fixDepositPayType,
        }),
      );
      const reference = ProductSelectors.getProductRequestReference();

      const isInit = lodashGet(action, 'data.isInit');
      if (isInit) {
        yield put(createUserCache({ type: PRODUCT_PAGE_PRE_CACHE }));
        if (Utils.isCtripIsd()) {
          // 初始化门店弹层数据
          yield put(queryVehicleDetailInfo({ reference }));
        }
      }
    },
  );
}

const getDonwGradeReqParam = async state => {
  const { storePointInfo, meetingPointInfo } = getDriveTimeReqParam();
  const drivingTimeEncrypt = await Utils.promisable(Encrypt.encrypt)(
    String(AppContext.driveTime),
    '1',
    'callback',
  );
  return {
    storePointInfo,
    meetingPointInfo,
    pickupDate: dayjs(getPickUpTime(state)).format('YYYY-MM-DD HH:mm:ss'),
    reference: ProductSelectors.getProductRequestReference(),
    timeout: 5,
    drivingTimeEncrypt,
  };
};

const logDownGrade = ({ isError, isSuccess, param, res }) => {
  const baseResponse = lodashGet(res, 'baseResponse') || {};
  const downGradeInfo = lodashGet(res, 'downGradeInfo') || {};
  CarLog.LogTraceDev({
    key: LogKeyDev.c_car_dev_trace_down_grade,
    info: {
      eventResult: isSuccess,
      request: JSON.stringify(param),
      baseResponse: JSON.stringify(baseResponse),
      resCode: baseResponse.code,
      error: isError ? JSON.stringify(res) : '',
      errorCode: isError ? ApiResCode.TraceCode.E1001 : '',
      downGradeInfo: JSON.stringify(downGradeInfo),
    },
  });
};

const handleDownGradeFail = (isFailToContinue, callbackFun) => {
  if (isFailToContinue) {
    BbkUtils.ensureFunctionCall(callbackFun());
    return;
  }
  xShowToast({ title: '加载失败，请重试', duration: 3000 });
};

function* validateIsDownGrade() {
  yield takeEvery(VALIDATE_DOWNGRADE, function* logic(action: ActionType) {
    Loading.showMaskLoading({
      cancelable: false,
    });
    const state = yield select();
    const { callbackFun, isFailToContinue } = action.data;
    const param = yield getDonwGradeReqParam(state);
    let isError = false;
    let errorInfo = null;
    const res = yield CarFetch.queryDownGradeInfo(param).catch(error => {
      isError = true;
      errorInfo = error;
    });
    Loading.hideMaskLoading();
    ProductReqAndResData.setData(
      ProductReqAndResData.keyList.downGradeReq,
      param,
    );
    ProductReqAndResData.setData(
      ProductReqAndResData.keyList.downGradeRes,
      res,
    );
    const isSuccess = !!lodashGet(res, 'baseResponse.isSuccess');
    const downGradeInfo = lodashGet(res, 'downGradeInfo');
    logDownGrade({
      isError,
      isSuccess,
      param,
      res: isError ? errorInfo : res,
    });
    if (downGradeInfo) {
      yield put(setPickupDownGradePopVisible(true));
      return;
    }

    if (!isSuccess) {
      handleDownGradeFail(isFailToContinue, callbackFun);
      return;
    }

    callbackFun();
  });
}

export function* queryEquipmentInfoLogic() {
  yield takeLatest(QUERY_EQUIPMENT_INFO, function* logic(action: ActionType) {
    const { productReqParams } = action.data;
    const { response }: QueryEquipmentInfoRes =
      yield CarFetch.queryEquipmentInfo(productReqParams);
    if (response?.equipmentInfos) {
      yield put(
        queryEquipmentInfoCallBack({
          equipmentInfos: response?.equipmentInfos,
          areaDesc: response?.areaDesc,
        }),
      );
    }
  });
}

export function* queryLicencePolicyLogic() {
  yield takeLatest(QUERY_LICENSE_POLICY, function* logic(action: ActionType) {
    const { requestId, nationalityCode } = action.data;
    const productInfoParam = ProductReqAndResData.getData(
      ProductReqAndResData.keyList.productReq,
    );
    const parentRequestId = requestId || productInfoParam?.requestId;
    if (!nationalityCode || !parentRequestId) return;
    const params = {
      parentRequestId,
      nationalityCode,
    };
    const response = yield CarFetch.queryLicencePolicy(params).catch(
      () => {},
    ) || {};
    if (response?.driverLicenseItems || response?.placeHoldTips) {
      const curDriverLicense = response?.driverLicenseItems?.find(
        item => !!item.selected,
      );
      yield put(
        queryLicencePolicyCallBack({
          driverLicenseItems: response?.driverLicenseItems,
          placeHoldTips: response?.placeHoldTips,
          curDriverLicense,
        }),
      );
      // 驾照政策接口返回后，更新当前驾驶员的驾照政策信息，防止默认驾驶员接口于驾照政策接口不同步造成驾照默认代入不展示
      const curState = yield select();
      const defaultPassenger = getPassenger(curState);
      yield put(selectDriver(defaultPassenger));
    }
  });
}

export function* queryCountriesInfoLogic() {
  yield takeLatest(QUERY_COUNTRYS_INFO, function* logic() {
    const { countryId } = getPickupStoreInfo();
    const param = {
      showGAT: true, // 返回港澳台
      dataSource: 'OCH', // 固定用车数据源
      extraTags: { withCountrySuffix: '1' },
    };

    const res = yield CarFetch.queryAppCountryIdList(param).catch(() => {});

    const countryInfo = res?.countries?.find(
      item => item?.countryId === countryId,
    );
    yield put(setCountrysInfo(countryInfo));
  });
}

export default [
  queryProductInfo(),
  getPriceInfo(),
  validateIsDownGrade(),
  queryEquipmentInfoLogic(),
  queryLicencePolicyLogic(),
  queryCountriesInfoLogic(),
];
