import { get as lodashGet } from 'lodash-es';
import { put, select, takeLatest } from 'redux-saga/effects';

import {
  FETCH_FLIGHTREC_PRODUCTS,
  PRE_FETCH_FLIGHTREC_PRODUCTS,
  GO_BOOKING,
} from './Types';
import {
  fetchFlightRecProductsCallback,
  fetchFlightRecProductsLoading,
  fetchFlightRecProducts,
  setSearchTipPopVisible,
} from './Actions';
import { ActionType } from '../../Types/ActionType';
import { ApiResCode } from '../../Constants/Index';
import {
  getPointInfoParmas,
  getFormatLocationAndDate,
} from '../LocationAndDate/Selectors';
import {
  AppContext,
  CarFetch,
  CarFetchHelper,
  CarServerABTesting,
  Channel,
  GetABCache,
} from '../../Util/Index';
import { getProducts, getIsNeedReSearch } from './Selectors';
import {
  getQueryVehicleDetailInfoParams,
  getReviewRequestParams,
} from './Mapper';
import { getVendorItemData } from '../List/VehicleListMappers';
import { queryVehicleDetailInfo } from '../ProductConfirm/Actions';
import {
  setUniqueCode,
  setVehicleListRequestAndResponse,
} from '../VendorList/Actions';

const verifyResponseIsValid = response => {
  const isSuccess = lodashGet(response, 'baseResponse.isSuccess');
  const resCode = lodashGet(response, 'baseResponse.code');
  return isSuccess && resCode === ApiResCode.ListResCode.C200;
};

export function* apiFlightRecProducts() {
  yield takeLatest(
    FETCH_FLIGHTREC_PRODUCTS,
    function* logic(action: ActionType) {
      const state = yield select();
      const { isNeedCallBack = true } = action?.data || {};
      const { pickupPointInfo, returnPointInfo } = getPointInfoParmas()(state);
      const brforeLocationAndDate = getFormatLocationAndDate(state);
      const requestBody = {
        pickupPointInfo,
        returnPointInfo,
      };
      try {
        if (isNeedCallBack) {
          yield put(fetchFlightRecProductsLoading());
        }
        const parameter = CarFetchHelper.parameterBuilder({
          param: requestBody,
          cachePolicy: {
            enableCache: true,
            isFromStorage: true,
            isLoadStorageThenRemove: true,
            cacheKey: JSON.stringify(requestBody),
          },
          verifyResponseIsValid,
          verifyResponseIsSuccess: verifyResponseIsValid,
        });
        const res = yield CarFetch.queryRecommendProducts(parameter).catch(
          () => {
            if (isNeedCallBack) {
              put(fetchFlightRecProductsCallback({ isNetWorkError: true }));
            }
          },
        );
        if (isNeedCallBack) {
          const { products, promptInfos } = res || {};
          // 查询成功后，记录查询的请求参数，校验是否更改了时间地点
          yield put(
            fetchFlightRecProductsCallback({
              products,
              promptInfos,
              brforeLocationAndDate,
            }),
          );
        }
      } catch (e) {
        if (isNeedCallBack) {
          yield put(fetchFlightRecProductsCallback({ isNetWorkError: true }));
        }
      }
    },
  );
}

// 提前请求推荐车型数据
export function* preFetchFlightRecProducts() {
  yield takeLatest(PRE_FETCH_FLIGHTREC_PRODUCTS, function* logic() {
    yield put(
      fetchFlightRecProducts({
        isNeedCallBack: false,
      }),
    );
  });
}

// 跳转填写页
export function* goBooking() {
  yield takeLatest(GO_BOOKING, function* logic(action: ActionType) {
    const state = yield select();
    const { vehicleIndex } = action.data;
    const isNeedReSearch = getIsNeedReSearch(state);
    // 用户点击修改时间/地址后未点击【重新查询】
    if (isNeedReSearch) {
      yield put(
        setSearchTipPopVisible({
          visible: true,
        }),
      );
      return;
    }

    const { product, vehicleInfo } = getProducts(state)?.[vehicleIndex] || {};
    const vendor = product?.vendorPriceList[0];
    const reference = getVendorItemData(
      { vendor, vendorIndex: 0 },
      vehicleInfo,
    )?.reference;

    const firstScreenParam = {
      vehicleInfo,
      vendorInfo: {
        vendorName: vendor?.vendorName,
      },
      isSelect: vendor?.isSelect,
      isEasyLife: vendor?.easyLifeInfo?.isEasyLife,
      allTags: {
        vehicleTagList: vendor?.allTags?.filter(flex => flex.groupId === 2),
      },
    };
    const isdParam = {
      vpid: vehicleInfo?.vehicleCode,
      vehicleName: firstScreenParam?.vehicleInfo?.name,
      vendorid: reference?.vendorCode,
      csname: vendor?.vendorName,
      psid: reference?.pStoreCode,
      rsid: reference?.rStoreCode,
    };
    const uniqueCode = `RecommendVehicle${vehicleIndex}`;
    const vehicleListResponse = {
      specificProductGroups: {
        vendorPriceList: [{ ...vendor, uniqueCode }],
      },
      vehicleInfo,
    };
    yield put(
      setVehicleListRequestAndResponse({
        response: vehicleListResponse,
        vehicleIndex,
      }),
    );
    yield put(setUniqueCode(uniqueCode));

    // 预请求车型弹层数据
    yield put(
      queryVehicleDetailInfo({
        requestParams: getQueryVehicleDetailInfoParams(state, reference),
        reviewRequestParams: getReviewRequestParams(reference),
      }),
    );

    // 跳转到填写页
    const isISDShelves2B = CarServerABTesting.isISDShelves2B();
    const isISDShelves3 = GetABCache.isISDShelves3();
    const pageToNavigate =
      isISDShelves2B && isISDShelves3 ? 'BookingIsd' : 'Booking';

    AppContext.PageInstance.push(pageToNavigate, {
      firstScreenParam,
      reference: { ...reference, isdParam },
      vendorPriceInfo: vendor,
      fromPage: Channel.getPageId().RecommendVehicle.EN,
    });
  });
}

export default [
  apiFlightRecProducts(),
  preFetchFlightRecProducts(),
  goBooking(),
];
