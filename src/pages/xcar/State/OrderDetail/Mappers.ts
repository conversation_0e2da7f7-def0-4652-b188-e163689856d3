import memoize from 'memoize-one';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { createSelector } from 'reselect';
import { PhoneNumberType } from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/QueryOrderNumberType';
import { getStore } from '../StoreRef';
import { Utils, CarABTesting, GetAB } from '../../Util/Index';
import { getAuthStatus, getSesameUserName } from '../Sesame/Selectors';
import { OrderDetailTexts as texts } from '../../Constants/TextIndex';
import { CarPayParams } from '../../Types/PaymentType';
import {
  getLimitRuleSuccessCont,
  getPickupStore,
  getReturnStore,
  getProductDetails,
  getCtripInsuranceInfos,
  getCancelReasons,
  getIsdRentCenter,
  getVendorInfo,
  getFreeDeposit,
  getPhoneModalType,
  selectViolationList,
  selectVehicleDamageList,
  getOrderBaseInfo,
  getOrderId,
  getLocationType,
  getDepositPayOnlineAmount,
  getExtendedInfo,
  getOrderStatus,
  getOrderCancelInfo,
  getMiddlePayVendorId,
  getPackageInfos,
  getVehicleInfo,
  getOrderDetailResponse,
  isKlbVersion,
  getClaimProcessModalData,
  getIsHasAccidentOSD,
  getFlightDelayRule,
  getIsSelfService,
  getVehicleUseNotesModalData,
} from './Selectors';
import { mappingLimitInfo } from '../Common/Mapper';
import {
  DepositStatus,
  CustomerPhoneModalType,
} from '../../Constants/OrderDetail';
import { Platform } from '../../Constants/Index';
import { DepositLabelType } from '../../ComponentBusiness/Common/src/Enums';
import {
  ZhimaWarnType,
  ZhimaResultMap,
  IChangeReminder,
  IPolicyTipType,
} from '../../Pages/OrderDetail/Types';
import { VendorId } from './Types';
import VehicleDamageTexts from '../../Pages/DamageDetail/Texts';
import {
  PayBusinessType,
  PayCurrencyStr,
  PayType,
  PAY_TITLE_TYPE,
} from '../../Constants/PayEnums';
import { Enums, WarningListResponseType } from '../../ComponentBusiness/Common';
import { PolicyPressType, PolicySubType } from '../Product/Enums';
import Texts from '../Product/Texts';
import OrderTexts from '../../Pages/OrderDetail/Texts';

const { uuid } = BbkUtils;

const { vialationProcesss, insSeviceDetail } = texts;

const getStartInfo = info => {
  const {
    location: {
      locationName = '',
      poiInfo: { latitude = 0, longitude = 0 },
    } = {
      locationName: '',
      poiInfo: {},
    },
    userLatitude,
    userLongitude,
    userAddress,
  } = info;
  return {
    lat: Utils.isCtripIsd() ? userLatitude : latitude,
    lng: Utils.isCtripIsd() ? userLongitude : longitude,
    addr: Utils.isCtripIsd() ? userAddress : locationName,
  };
};

export const getGuidePageParam = (guideTabId, isHidePhone = false) => {
  const state = getStore().getState();
  const pickupStore = getPickupStore(state);
  const returnStore = getReturnStore(state);
  const rentCenter = getIsdRentCenter(state);
  const pStoreNav = ''; // todo
  const rStoreNav = ''; // todo
  const locationType = getLocationType(state);

  return {
    pickupStart: getStartInfo(pickupStore),
    dropoffStart: getStartInfo(returnStore),
    pickupStoreId: Utils.isCtripIsd()
      ? pickupStore.storeID
      : pickupStore.storeCode,
    dropoffStoreId: Utils.isCtripIsd()
      ? returnStore.storeID
      : returnStore.storeCode,
    selectedId: guideTabId,
    rentCenterId: rentCenter.id ? rentCenter.id : 0,
    pickupServiceType: Utils.isCtripIsd() ? pickupStore.serviceType : 0, // 是否是送车上门
    dropoffServiceType: Utils.isCtripIsd() ? returnStore.serviceType : 0, // 是否是上门取车
    pStoreWay: pStoreNav,
    rStoreWay: rStoreNav,
    fixMap: true,
    isHidePhone,
    orderId: Utils.isCtripIsd()
      ? !!locationType && getOrderId(state)
      : getOrderId(state),
    orderStatus: getOrderStatus(state),
    pickupStoreGuide: Utils.isCtripOsd() ? pickupStore?.storeGuide : '', // 2023-3-21 境外售后地图指引页中的取还车指引取OSDQueryOrder接口返回的
    returnStoreGuide: Utils.isCtripOsd() ? returnStore?.storeGuide : '',
  };
};

export const getGurProductDetails = insPackageId => {
  const state = getStore().getState();
  const productDetails = getProductDetails(state);
  const ctripInsuranceInfos = getCtripInsuranceInfos(state);
  let curProductDetails: any;
  productDetails.forEach(element => {
    if (element.insPackageId === insPackageId) curProductDetails = element;
  });
  if (!!curProductDetails && ctripInsuranceInfos.length > 0) {
    curProductDetails.insuranceItems.forEach(e => {
      ctripInsuranceInfos.forEach(e2 => {
        if (e2.productId === e.productId) {
          e.insuranceInfos = e2;
        }
      });
    });
  }
  return curProductDetails;
};

export const getLimitData = createSelector([getLimitRuleSuccessCont], res =>
  mappingLimitInfo(res),
);

export const getLimitTipInfo = createSelector(
  [getVehicleInfo, getLimitData],
  (vehicleInfo, limitDesc) => {
    if (!vehicleInfo) {
      return {
        isShowTip: false,
        isLimit: true,
        tipText: '',
      };
    }
    const tipText = vehicleInfo?.licenseLimitDesc || limitDesc?.title;
    return {
      isShowTip: limitDesc?.title || (vehicleInfo && !!tipText),
      isLimit: !vehicleInfo?.licenseLimitDesc,
      tipText,
    };
  },
);

export const getCancelReasonLists = createSelector(
  [getCancelReasons, getOrderCancelInfo, isKlbVersion],
  (cancelReasons, newCancelReasons, isKlb) => {
    if (Utils.isCtripIsd() || GetAB.isOSDNewOrderCancel(isKlb)) {
      return newCancelReasons?.cancelReasonList;
    }
    const nReasons = [];
    cancelReasons?.map(item =>
      nReasons.push({
        isSelect: false,
        title: item,
        code: item?.code,
      }),
    );
    return nReasons;
  },
);

// 从订单信息中获取首页的参数
export const getHomeParamFromOrder = createSelector(
  [getVendorInfo, getPickupStore, getReturnStore],
  (vendorInfo = {}, pickupStore = {}, returnStore = {}) => {
    const { vendorID } = vendorInfo;
    /**
     * 1.有些VBK默认支持上门取还`serviceType`为2，即使`issendcar`和`ispickupcar`传值为2
     * 2.一嗨则需要用户在首页手动选择是否需要上门取还服务
     * 3.以下逻辑为了避免当用户在首页没选上门取还但是下单了默认支持上门取还的vbk门店，
     * 用户点击修改订单时，首页错误缓存了issendcar=1，ispickupcar=1，导致后续只能搜索出一嗨门店
     */
    let issendcar;
    let ispickupcar;
    if (vendorID === 9787) {
      issendcar = pickupStore.serviceType === 2 ? 1 : 2;
      ispickupcar = returnStore.serviceType === 2 ? 1 : 2;
    } else {
      issendcar = 2;
      ispickupcar = 2;
    }
    const ptime = pickupStore?.localDateTime?.replace(/-/g, '/');
    const rtime = returnStore?.localDateTime?.replace(/-/g, '/');
    return {
      issendcar,
      ispickupcar,
      pcid: `${pickupStore.cityId}`,
      rcid: `${returnStore.cityId}`,
      pcname: pickupStore.cityName,
      rcname: returnStore.cityName,
      poiinfo: {
        addr: pickupStore.userAddress,
        lat: `${pickupStore.userLatitude}`,
        lng: `${pickupStore.userLongitude}`,
      },
      rpoiinfo: {
        addr: returnStore.userAddress,
        lat: `${returnStore.userLatitude}`,
        lng: `${returnStore.userLongitude}`,
      },
      ptime,
      rtime,
    };
  },
);

export const getIsEhi = createSelector(
  [getVendorInfo],
  vendorInfo => String(vendorInfo?.vendorID) === VendorId.ehi,
);

export const getOrderDetailDepositInfo = createSelector(
  [getAuthStatus, getFreeDeposit, getSesameUserName, getOrderBaseInfo],
  (authStatus, freeDepositData, userName = '', orderbaseInfo = null) => {
    const {
      depositItems = [],
      payMethodExplain,
      deductionTime,
      freeDepositType,
      depositStatus,
      showDepositType,
      tip = {},
      realPayItems = [],
      tipsExplainDesc,
      freeDepositBtn,
    } = freeDepositData || {};

    const { vendorPreAuthInfo = {} } = orderbaseInfo || {};

    const serverRuleURL = Platform.CAR_CROSS_URL.ServerRule.ISD;

    let zhimaResult: ZhimaResultMap = {};

    if (showDepositType === 1) {
      // 程信分
      zhimaResult = {
        warnTip: '',
        texts: [
          {
            get title() {
              return '确认授权视为理解并同意';
            },
            link: {
              get text() {
                return '服务协议';
              },
              url: serverRuleURL,
            },
          },
        ],

        btnType: 1,
      };
    } else if (showDepositType === 2) {
      // 芝麻验证
      zhimaResult = tip;
    }

    const zhimaBtnTexts = ['确认信用免押', '去授权', '实名认证'];

    const zhimaBtnText = zhimaResult.btnType
      ? zhimaBtnTexts[zhimaResult.btnType - 1]
      : '';

    const depositDescTable = {
      items: [],
      notices: null,
    };

    if (depositItems) {
      depositDescTable.items = depositItems.map(item => ({
        retractable: item.deposit > 0,
        title: item.depositTitle,
        currencyCode: '¥',
        showFree: item.depositStatus === 1,
        currentTotalPrice: item.deposit,
        description: item.explain,
      }));

      if (payMethodExplain) {
        depositDescTable.notices = [payMethodExplain];
      }
    }

    const creditText = CarABTesting.isCreditRent() ? `${'信用租'}·` : '';

    const depositTableTitleMap = {
      [DepositStatus.Unsupport]: Utils.isCtripIsd()
        ? '取车前支付如下押金'
        : '到店支付押金',
      [DepositStatus.PreAuth]: '您已在线预授权押金',
      // app 无单免
      [DepositStatus.Zhima]: '您已享押金双免',
      // app 无单免
      [DepositStatus.CreditRent]: '您已享押金双免',
      [DepositStatus.PayOnline]: '您已选择在线支付押金',
    };
    let depositTableTitle = depositTableTitleMap[depositStatus] || '';

    if (
      [DepositStatus.Zhima, DepositStatus.CreditRent].includes(depositStatus)
    ) {
      const freeUsingTxt = '本单已享';
      switch (freeDepositType) {
        case 20:
          depositTableTitle = `${freeUsingTxt}${'免租车押金'}`;

          break;
        case 30:
          depositTableTitle = `${freeUsingTxt}${'免违章押金'}`;

          break;
        default:
          break;
      }
    }

    const creaditRentTitleLabelMap = {
      get 0() {
        return '押金双免';
      },
      get 10() {
        return '押金双免';
      },
      get 20() {
        return '免租车押金';
      },
      get 30() {
        return '免违章押金';
      },
    };

    const creaditRentDescMap = {
      get 0() {
        return '无需支付租车押金和违章押金';
      },
      get 10() {
        return '无需支付租车押金和违章押金';
      },
      get 20() {
        return '无需支付租车押金';
      },
      get 30() {
        return '无需支付违章押金';
      },
    };

    const orderDepositFreeTxt = '免收';

    const creaditRentTagMap = {
      0: `${orderDepositFreeTxt}`,
      10: `${orderDepositFreeTxt}`,
      20: `${orderDepositFreeTxt}${'租车押金'}`,
      30: `${orderDepositFreeTxt}${'违章押金'}`,
      100: `${orderDepositFreeTxt}`,
    };

    const creditEntryTitle = `${creditText}${creaditRentTitleLabelMap[freeDepositType]}`;
    const depositCreditSubTitle = creaditRentTitleLabelMap[freeDepositType];
    const creaditRentOutDesc = creaditRentDescMap[freeDepositType];
    const creaditRentTag = creaditRentTagMap[freeDepositType];

    const logoTypeMapper = {
      [DepositStatus.CreditRent]: DepositLabelType.CtripCreditRent,
      [DepositStatus.Zhima]: DepositLabelType.Zhima,
    };

    const isMax =
      zhimaResult.warnType === ZhimaWarnType.yihai ||
      zhimaResult.warnType === ZhimaWarnType.normal;

    return {
      ...freeDepositData,
      isHasFreeDepositData: !!freeDepositData,
      creditEntryTitle,
      get depositCreditTitle() {
        return '更多押金方式';
      },
      depositCreditSubTitle,
      creaditRentTag,
      showDepositType,
      authStatus,
      userName,
      depositTableTitle,
      creaditRentOutDesc,
      depositStatus,
      freeDepositType,
      showCreditRent: showDepositType === 1 || showDepositType === 2,
      showPreAuthRent:
        showDepositType !== 4 && vendorPreAuthInfo.preAuthDisplay === 1,
      depositDescTable,
      realPayItems,
      zhimaResult,
      zhimaBtnText,
      preAuthDescTime: `需${deductionTime}后申请`,
      get preAuthDescFeature() {
        return '提前支付租车押金，取车手续更便捷';
      },
      preAuthDescTips: [
        {
          title: `使用信用卡预授权押金，取车前72小时（${deductionTime}）将采用冻结你的信用卡额度的方法开始扣款。若扣款失败则仍需到店支付押金`,
        },
        {
          get title() {
            return '使用微信、支付宝等其他支付方式支付，将以消费的方式进行扣款';
          },
        },
        {
          get title() {
            return '支付预授权视为理解并同意';
          },
          get btnText() {
            return '服务协议';
          },
          url: serverRuleURL,
        },
      ],

      hasCreditFreeLabelType: logoTypeMapper[depositStatus],
      isMax,
      tipsExplainDesc,
      btnColorIsGray: !freeDepositBtn?.statusType,
    };
  },
);

export const getPhoneMenus = createSelector(
  [getPickupStore, getReturnStore, getPhoneModalType],
  (pickupStore, returnStore, phoneModalType) => {
    const { storeTel: pickUpStoreTel = '' } = pickupStore;
    const { storeTel: returnStoreTel = '' } = returnStore;

    const pickupStoreTels = Utils.getPhoneList(pickUpStoreTel);
    const returnStoreTels = Utils.getPhoneList(returnStoreTel);

    let pickStoreTitle: string;
    let returnStoreTitle: string;

    if (phoneModalType === CustomerPhoneModalType.Customer) {
      pickStoreTitle = `${'取车'}${'门店电话'}`;
      returnStoreTitle = `${'还车'}${'门店电话'}`;
    } else if (phoneModalType === CustomerPhoneModalType.Phone) {
      pickStoreTitle = '取车门店';
      returnStoreTitle = '还车门店';
    } else {
      pickStoreTitle = '取车门店';
      returnStoreTitle = '还车门店';
    }

    return JSON.stringify(pickUpStoreTel) === JSON.stringify(returnStoreTel)
      ? [
          {
            get name() {
              return '门店电话';
            },
            tels: pickupStoreTels,
          },
        ]
      : [
          {
            name: pickStoreTitle,
            tels: pickupStoreTels,
          },
          {
            name: returnStoreTitle,
            tels: returnStoreTels,
          },
        ];
  },
);

export const getPersonPhoneMenus = createSelector(
  [getExtendedInfo],
  extendedInfo => {
    let tels = [];
    if (extendedInfo?.storeAttendant?.actionUrl) {
      tels = [`${extendedInfo?.storeAttendant?.actionUrl}`];
    }

    return [
      {
        tels,
      },
    ];
  },
);

export const mapCarRentalMustReadToViolationRules = carRentalMustReadData => {
  const violations = carRentalMustReadData?.find(
    item => item.type === PolicyPressType.ViolationDepositRule,
  );
  const rule = violations?.subObject?.find(
    item => item.type === PolicySubType.ViolationPlenty,
  );
  return rule?.content;
};

export const mapVioLationAndDamageDesc = createSelector(
  [selectViolationList, selectVehicleDamageList],
  (violations, damages) => {
    let desc = null;
    const isViolationEmpty = violations?.length === 0;
    const isDamageEmpty = damages?.length === 0;
    if (Utils.isCtripIsd()) {
      if (isViolationEmpty) {
        desc = texts.violationEntryTipV2;
      }
    } else if (isViolationEmpty && isDamageEmpty) {
      desc = `若有违章，车行一般会在还车后的${15}个工作日左右收到未处理的违章信息，请持续关注；暂无车损记录。`;
    } else {
      const violationDesc = isViolationEmpty
        ? '暂无违章记录。'
        : `已查询到${violations?.length}条违章记录。`;
      const damegeDesc = isDamageEmpty
        ? '暂无车损记录。'
        : `已查询到${damages?.length}条车损记录。`;
      desc = isViolationEmpty
        ? `${damegeDesc}\n${violationDesc}`
        : `${violationDesc}\n${damegeDesc}`;
    }
    return desc;
  },
);

export const getStorePoiInfoMaper = (pickupStore, returnStore) => {
  return {
    poiinfo: {
      lat: pickupStore.userLatitude,
      lng: pickupStore.userLongitude,
      addr: pickupStore.userAddress,
    },
    rpoiinfo: {
      lat: returnStore.userLatitude,
      lng: returnStore.userLongitude,
      addr: returnStore.userAddress,
    },
    pStore: {
      lat: pickupStore.latitude,
      lng: pickupStore.longitude,
      onDoor: pickupStore.serviceType === '2', // serviceType 0 门店取车；1 接您至门店取车；2 送车上门；
    },
    rStore: {
      lat: returnStore.latitude,
      lng: returnStore.longitude,
      onDoor: returnStore.serviceType === '2',
    },
  } as any;
};

export const mapInsuranceItemToServiceData = (item: any = {}) => {
  const accidentContents = item.accident.map(accItem => ({
    type: 3,
    title: accItem.title,
    content: accItem.desc,
  }));

  return {
    description: item.description,
    allTags: item.targetTag,
    localCurrencyCode: item.localCurrencyCode,
    currentTotalPrice: item.localTotalPrice,
    localDailyPrice: item.localDailyPrice,
    currentCurrencyCode: item.currentCurrencyCode,
    localTotalPrice: item.localTotalPrice,
    uniqueCode: item.code,
    currentDailyPrice: item.currentDailyPrice,
    name: item.name,
    footDescription: item.insServiceBottomDesc,
    insBottomDesc: {
      title: item.insBottomDesc?.title,
      desclist: item.insBottomDesc?.desc,
    },
    insuranceDetailDescription: {
      items: [
        {
          title: insSeviceDetail,
        },
        {
          title: vialationProcesss,
          contents: accidentContents,
        },
      ],
    },
  };
};

export const getRenewTipLogData = createSelector(
  [getOrderBaseInfo],
  orderBaseInfo => {
    const { orderId, orderStatusDesc } = orderBaseInfo || {};
    return {
      orderId: String(orderId),
      orderStatus: orderStatusDesc,
    };
  },
);

export const getDepositPayOnlineParams = createSelector(
  [getOrderId, getDepositPayOnlineAmount, getMiddlePayVendorId],
  (orderId, amount, vendorId): CarPayParams => {
    const title = '租车押金';
    const currency = PayCurrencyStr.CNY;
    return {
      businessType: PayBusinessType.DepositPayOnline,
      orderId: Number(orderId),
      businessId: orderId,
      title,
      titletype: PAY_TITLE_TYPE.Normal,
      currency,
      amount,
      chargesInfos: [
        {
          title,
          currencyCode: currency,
          currentTotalPrice: amount,
        },
      ],

      hideOrderPaySummary: true,
      hidePayRemind: true,
      requestId: uuid(),
      vendorId,
      payType: PayType.RegularPay,
    };
  },
);

// 获取车损证明渲染数据
export const getDamageProveRenderData = memoize(damageImgList => {
  const combineUrls = [];
  const pureImageList = [];
  if (damageImgList?.length > 0) {
    damageImgList.forEach(item => {
      const urlItems = [
        ...(item?.vedioUrl || []),
        ...(item?.imgUrl?.map(img => ({ imageUrl: img })) || []),
      ];

      const commitTime = dayjs(item.commitTime).format(
        VehicleDamageTexts.YYYYYearMMMonthDDDayHourMinute,
      );
      combineUrls.unshift({
        title: item.firstCommit
          ? `${commitTime} ${VehicleDamageTexts.recordText}`
          : `${commitTime} ${VehicleDamageTexts.supplementText}`,
        urlItems,
      });
    });
  }

  if (combineUrls?.length > 0) {
    combineUrls.forEach(combineUrl => {
      combineUrl?.urlItems?.forEach(urlItem => {
        if (urlItem.imageUrl) {
          pureImageList.push(urlItem);
        }
      });
    });
  }

  return {
    combineUrls,
    pureImageList,
  };
});

export const isPickPointFn = (storeInfo, isPick) =>
  isPick && storeInfo?.storeType === Enums.StoreType.PickPoint;

export const mapChangeReminderToWaringInfo = (
  changeReminder: IChangeReminder,
): WarningListResponseType => {
  return {
    prefixTitle: changeReminder?.tipText?.title,
    warningDtos: [
      {
        warningTitle: changeReminder?.tipText?.description,
      },
    ],
  };
};

export const setContactData = (
  phoneNumberData,
  type,
  storeAttendant,
  icon,
  IStoreAttendantType,
  PhoneNumberRole,
  vendorImUrl,
  phoneModalFromWhere,
  PhoneModalFromWhere,
  tourImJumpUrl,
) => {
  // const isCollector = storeAttendant?.type === IStoreAttendantType.DropOff; // 有取车员状态
  // const isDeliverer = storeAttendant?.type === IStoreAttendantType.PickUp; // 有送车员状态
  const { phoneNumberList } = phoneNumberData || {};
  const contacts = [];
  const showCustomerAndPhone = [
    CustomerPhoneModalType.Customer,
    CustomerPhoneModalType.ViolationPhone,
    CustomerPhoneModalType.FooterBar,
    CustomerPhoneModalType.HomeOrderCard,
    CustomerPhoneModalType.Penalty,
  ].includes(type);
  const contactLists = [];
  if (phoneNumberList?.length) {
    let isVirtual = false;
    phoneNumberList.forEach(item => {
      const collector = item.role === PhoneNumberRole.COLLECTOR;
      const deliverer = item.role === PhoneNumberRole.DELIVERER;
      if (showCustomerAndPhone && (collector || deliverer)) {
        isVirtual = item.type === PhoneNumberType.virtual;
        contactLists.push({
          title: collector ? texts.pickUPCall : texts.deliveryCall,
          virtualNumber: item.number,
          isRecommend: true,
          rightIcon: icon.phone,
        });
      }
    });
    if (contactLists.length) {
      contacts.push({
        secAndPrivacyTips: isVirtual ? texts.privacyNotice : '',
        titleLineInfo: {
          title: texts.deliveryPick,
          subTitle: texts.deliveryTip,
        },
        contactLists,
      });
    }
    const storePhones = phoneNumberList.filter(item =>
      [PhoneNumberRole.PICKUP_STORE, PhoneNumberRole.RETURN_STORE].includes(
        item.role,
      ),
    );
    const storeClickLog = {
      name: '点击_订详_联系门店弹层页面_取车门店/还车门店手机虚拟号',
      info: {
        onPressFrom: type,
      },
    };
    if (storePhones.length > 0) {
      if (type === CustomerPhoneModalType.ViolationPhone) {
        storeClickLog.name =
          phoneModalFromWhere === PhoneModalFromWhere.hasViolation
            ? '点击_订单详情页-有违章详情-联系门店_手机虚拟号'
            : '点击_订单详情页-无违章详情-联系门店_手机虚拟号';
      } else if (phoneModalFromWhere === PhoneModalFromWhere.faqPage) {
        storeClickLog.name = '点击_订详常见问题_联系门店_手机虚拟号';
      } else if (type === CustomerPhoneModalType.HomeOrderCard) {
        storeClickLog.name =
          '点击_国内租车首页_租车订单卡片_联系门店_手机虚拟号';
      }
    }
    // 是否有供应商IM
    const hasStoreIm = showCustomerAndPhone && vendorImUrl;
    if (hasStoreIm) {
      contacts.push({
        secAndPrivacyTips: texts.vendorImTip,
        titleLineInfo: {
          title: texts.orderStore,
          subTitle: texts.storeMessage,
        },
        contactLists: [
          {
            title: texts.vendorOnlineService,
            vendorImUrl,
            isRecommend: true,
            rightIcon: icon.customerChat,
          },
        ],
      });
    }
    // 判断同门店取还就展示 “门店电话”
    if (storePhones.length === 1) {
      contacts.push({
        secAndPrivacyTips: texts.privacyNotice,
        titleLineInfo: !hasStoreIm
          ? {
              title: texts.orderStore,
              subTitle: texts.storeMessage,
            }
          : undefined,
        contactLists: [
          {
            title: texts.phone,
            virtualNumber: storePhones[0].number,
            rightIcon: icon.phone,
            clickLog: storeClickLog,
          },
        ],
      });
    } else if (storePhones.length === 2) {
      const contactListsScope = [];
      let isVirtualScope = false;
      storePhones.forEach(info => {
        const title =
          info.role === PhoneNumberRole.RETURN_STORE
            ? texts.returnShopCall
            : texts.pickUpShopCall;
        contactListsScope.push({
          title,
          virtualNumber: info.number,
          rightIcon: icon.phone,
          clickLog: storeClickLog,
        });
        isVirtualScope = info.type === PhoneNumberType.virtual;
      });
      contacts.push({
        secAndPrivacyTips: isVirtualScope ? texts.privacyNotice : '',
        titleLineInfo: !hasStoreIm
          ? {
              title: texts.orderStore,
              subTitle: texts.storeMessage,
            }
          : undefined,
        contactLists: contactListsScope,
      });
    }

    if (type === CustomerPhoneModalType.Customer) {
      // 携程客服
      contacts.push({
        titleLineInfo: {
          title: texts.ctrip,
          subTitle: texts.orderFaqTip,
        },
        contactLists: [
          {
            title: texts.orderCtripOnlineCustomer,
            vendorImUrl: tourImJumpUrl,
            rightIcon: icon.service,
          },
        ],
      });
    }
  }
  return contacts;
};

export const mapIsuranceBox = createSelector(
  [getPackageInfos, getVendorInfo, getVehicleInfo, getOrderDetailResponse],
  (packageItem, vendorInfo, vehicleInfo, orderDetailResponse) => {
    const vendorCode = vendorInfo?.bizVendorCode;
    const groupName = vehicleInfo?.vehicleGroupName;
    const newPackageInfos = [];
    if (packageItem) {
      const curInsuranceItems = getGurProductDetails(
        packageItem.insPackageId,
      )?.insuranceItems;
      const insNameAndExcess = [];
      const includeInsCode = [];
      // 不包含的保险
      const unIncludeInsNameAndExcess = [];
      // 所有的保险
      const allInsNameAndExcess = [];

      // 展示保险说明的数量
      let showHasExcessCount = 1;
      curInsuranceItems?.forEach(insItem => {
        let curInsItem = null;
        // 如果是海外自营险，则取第一条保险，因为保险可以属于多个套餐，服务端不好增加packageId
        if (Utils.isCtripOsd() && insItem.isFromCtrip) {
          curInsItem = insItem?.insuranceDetail?.[0];
        } else {
          curInsItem = insItem?.insuranceDetail?.find(
            f => f.packageId === packageItem.defaultPackageId,
          );
        }
        const {
          minExcess,
          maxExcess,
          excessShortDesc,
          coverageWithoutPlatformInsurance,
        } = curInsItem || {};
        const hasExcess = minExcess >= 0 || maxExcess >= 0;
        // 有起赔额
        if (hasExcess) {
          showHasExcessCount -= 1;
        }

        const curNameAndExcess = {
          code: insItem.code,
          name: insItem.name,
          groupCode: insItem.groupCode,
          excessShortDesc,
          // 新版保险字段
          isHasExcess: showHasExcessCount >= 0 && hasExcess,
          isInclude: true, // 订单详情页的保险都属于包含项
          // 新版本用与展示起赔额
          excessShortDescNew:
            minExcess === 0 ? Texts.zeroExcessDesc : excessShortDesc,
          // 是否是0起赔额
          isZeroExcess: minExcess === 0,
          // 携程自营险展示灰色标签 （用于非弹窗展示）
          label: insItem.isFromCtrip ? coverageWithoutPlatformInsurance : '',
          // 详情弹窗灰色标签
          modalLabel: [
            !insItem.isFromCtrip && Texts.includedByCar,
            coverageWithoutPlatformInsurance,
          ],

          isFromCtrip: insItem.isFromCtrip,
          giveUp: insItem.giveUp,
          itemUrl: insItem.itemUrl,
          insuranceStatus: insItem.insuranceStatus,
          insuranceStatusName: insItem.insuranceStatusName,
          // 携程自营险展示高亮描述
          description: insItem.description,
        };
        if (insItem.isInclude) {
          insNameAndExcess.push(curNameAndExcess);
          includeInsCode.push(insItem.code);
        } else {
          unIncludeInsNameAndExcess.push(curNameAndExcess);
        }
        allInsNameAndExcess.push(curNameAndExcess);
      });

      const logInfo = {
        guaranteePkgName: packageItem.packageName,
        insuranceId: includeInsCode,
        vendorCode,
        groupName,
      };

      // 将所有保险按groupCode进行分组
      const groupInsNameAndExcess = {};
      allInsNameAndExcess.forEach(ins => {
        if (!groupInsNameAndExcess[ins.groupCode]) {
          groupInsNameAndExcess[ins.groupCode] = [];
        }
        groupInsNameAndExcess[ins.groupCode].push(ins);
      });
      newPackageInfos.push({
        ...packageItem,
        allInsNameAndExcess,
        groupInsNameAndExcess,
        insNameAndExcess,
        unIncludeInsNameAndExcess,
        logInfo,
      });
    }
    return {
      packageInfos: newPackageInfos,
      insuranceAndXProductDesc: orderDetailResponse?.insuranceAndXProductDesc,
      platformInsuranceReminder: orderDetailResponse?.platformInsuranceReminder,
    };
  },
);

export const mapIsuranceBoxOsd = createSelector(
  [getPackageInfos, getVendorInfo, getVehicleInfo, getOrderDetailResponse],
  (packageItem, vendorInfo, vehicleInfo, orderDetailResponse) => {
    const vendorCode = vendorInfo?.bizVendorCode;
    const groupName = vehicleInfo?.vehicleGroupName;
    const newPackageInfos = [];
    if (packageItem) {
      const curProduct = getGurProductDetails(packageItem.insPackageId);
      const { insuranceItems: curInsuranceItems, briefInsuranceItems } =
        curProduct || {};
      const insNameAndExcess = [];
      const includeInsCode = [];
      // 不包含的保险
      const unIncludeInsNameAndExcess = [];
      // 所有的保险
      const allInsNameAndExcess = [];
      // 出境自营险一级页面的保险
      const summaryInsNameAndExcess = [];

      // 一级页面节点
      briefInsuranceItems?.forEach(briefItem => {
        const curInsItem = briefItem.insuranceDetail?.[0];
        const {
          minExcess,
          maxExcess,
          coverageWithPlatformInsuranceV2,
          coverageWithoutPlatformInsuranceV2,
        } = curInsItem || {};
        // 是否是0起赔额
        const isZeroExcess = minExcess === 0 && maxExcess === 0;
        // 起赔额拼接文案
        const excessLabel = coverageWithoutPlatformInsuranceV2
          ? ` ${coverageWithoutPlatformInsuranceV2}`
          : '';
        // 保险名称拼接起赔额
        const insuranceName = `${briefItem.name}${
          isZeroExcess ? '' : excessLabel
        }`;

        const curNameAndExcess = {
          code: briefItem.code,
          // 非自营险时，拼接起赔额文案
          name: briefItem.isFromCtrip ? briefItem.name : insuranceName,
          isInclude: briefItem.isInclude,
          // 0起赔额绿色文案
          excessShortDescNew: isZeroExcess ? Texts.zeroExcessDesc : '',
          // 是否是0起赔额
          isZeroExcess,
          // 自营险展示灰色标签
          label: briefItem.isFromCtrip ? coverageWithPlatformInsuranceV2 : '',
          // 自营险展示灰色标签
          labelDescription: briefItem.isFromCtrip
            ? coverageWithoutPlatformInsuranceV2
            : '',
          isFromCtrip: briefItem.isFromCtrip,
          itemUrl: briefItem.itemUrl,
          insuranceStatus: briefItem.insuranceStatus,
          insuranceStatusName: briefItem.insuranceStatusName,
          insuranceStatusDesc: briefItem.insuranceStatusDesc,
          // 自营险展示的详细描述
          description: briefItem.description,
        };
        summaryInsNameAndExcess.push(curNameAndExcess);
      });

      // 是否有起赔额
      let isHasExcess = 0;
      curInsuranceItems?.forEach(insItem => {
        let curInsItem = null;
        // 如果是海外自营险，则取第一条保险，因为保险可以属于多个套餐，服务端不好增加packageId
        if (Utils.isCtripOsd() && insItem.isFromCtrip) {
          curInsItem = insItem?.insuranceDetail?.[0];
        } else {
          curInsItem = insItem?.insuranceDetail?.find(
            f => f.packageId === packageItem.defaultPackageId,
          );
        }
        const {
          minExcess,
          maxExcess,
          excessShortDesc,
          coverageWithoutPlatformInsuranceV2 = '',
          coverageWithPlatformInsuranceV2 = '',
        } = curInsItem || {};
        const isZeroExcess = minExcess === 0 && maxExcess === 0;
        const hasExcess = minExcess >= 0 || maxExcess >= 0;
        // 有起赔额
        if (hasExcess) {
          isHasExcess += 1;
        }

        // 详情描述
        const modalLabel = [];
        // 拼接保险弹窗描述
        let labelContent = insItem.isFromCtrip
          ? Texts.insuranceCompanyByCar
          : Texts.includedByCar;
        // 拼接短描述
        if (insItem.description) {
          labelContent += `：${insItem.description}`;
        }
        // 自营险拼接起赔额(起赔额为0不拼接起赔额)
        const isAppendModalLabel =
          insItem.isAddFulCoverage && !insItem.isFromCtrip;
        if (isAppendModalLabel && coverageWithoutPlatformInsuranceV2) {
          labelContent += `${insItem.description ? '，' : '：'}${
            coverageWithoutPlatformInsuranceV2
          }`;
        }
        modalLabel.push(labelContent);
        if (isAppendModalLabel && coverageWithPlatformInsuranceV2) {
          modalLabel.push(coverageWithPlatformInsuranceV2);
        }
        // 起赔额拼接文案
        const excessLabel = coverageWithoutPlatformInsuranceV2
          ? ` ${coverageWithoutPlatformInsuranceV2}`
          : '';
        // 保险名称拼接起赔额
        const insuranceName = `${insItem.name}${
          insItem.isFromCtrip || isZeroExcess ? '' : excessLabel
        }`;

        const curNameAndExcess = {
          code: insItem.code,
          name: insuranceName,
          groupCode: insItem.groupCode,
          excessShortDesc,
          isInclude: insItem.isInclude, // 订单详情页的保险都属于包含项
          // 0起赔额绿色文案
          excessShortDescNew: minExcess === 0 ? Texts.zeroExcessDesc : '',
          // 是否是0起赔额
          isZeroExcess,
          // 自营险展示灰色标签
          label: insItem.isFromCtrip ? coverageWithPlatformInsuranceV2 : '',
          // 自营险展示灰色标签
          labelDescription: insItem.isFromCtrip
            ? coverageWithoutPlatformInsuranceV2
            : '',
          // 详情弹窗灰色标签
          modalLabel,
          isFromCtrip: insItem.isFromCtrip,
          itemUrl: insItem.itemUrl,
          insuranceStatus: insItem.insuranceStatus,
          insuranceStatusName: insItem.insuranceStatusName,
          insuranceStatusDesc: insItem.insuranceStatusDesc,
        };
        if (insItem.isInclude) {
          insNameAndExcess.push(curNameAndExcess);
          includeInsCode.push(insItem.code);
        } else {
          unIncludeInsNameAndExcess.push(curNameAndExcess);
        }
        allInsNameAndExcess.push(curNameAndExcess);
      });

      const logInfo = {
        guaranteePkgName: packageItem.packageName,
        insuranceId: includeInsCode,
        vendorCode,
        groupName,
      };

      // 将所有保险按groupCode进行分组
      const groupInsNameAndExcess = {};
      allInsNameAndExcess.forEach(ins => {
        if (!groupInsNameAndExcess[ins.groupCode]) {
          groupInsNameAndExcess[ins.groupCode] = [];
        }
        groupInsNameAndExcess[ins.groupCode].push(ins);
      });
      newPackageInfos.push({
        ...packageItem,
        allInsNameAndExcess,
        summaryInsNameAndExcess,
        groupInsNameAndExcess,
        insNameAndExcess,
        unIncludeInsNameAndExcess,
        isHasExcess,
        logInfo,
      });
    }
    return {
      packageInfos: newPackageInfos,
      insuranceAndXProductDesc: orderDetailResponse?.insuranceAndXProductDesc,
      platformInsuranceReminder: orderDetailResponse?.platformInsuranceReminder,
    };
  },
);

export const getpolicyList = createSelector(
  [
    getLimitData,
    getIsSelfService,
    getVehicleUseNotesModalData,
    getClaimProcessModalData,
    getIsHasAccidentOSD,
    getFlightDelayRule,
  ],

  (
    limitRuleCont,
    isSelfService,
    isHasVehicleUseNotesModalData,
    isHasClaimProcessModalData,
    isHasAccidentOSD,
    IsHasFlightDelayRule,
  ) => {
    const policies = [];
    if (Utils.isCtripIsd()) {
      const isdPolicies = [
        {
          text: OrderTexts.limitRulesText,
          type: IPolicyTipType.limit,
          show: !!limitRuleCont,
        },
        {
          text: OrderTexts.mileageProhibitionText,
          type: IPolicyTipType.mileage,
          show: true,
        },
        {
          text: OrderTexts.selfServiceText,
          type: IPolicyTipType.selfService,
          show: !!isSelfService && !!isHasVehicleUseNotesModalData,
        },
        {
          text: OrderTexts.accidentHandleText,
          type: IPolicyTipType.accident,
          show: !!isHasClaimProcessModalData,
        },
        {
          text: OrderTexts.storePolicyText,
          type: IPolicyTipType.store,
          show: true,
        },
      ];

      policies.push(...isdPolicies.filter(p => p.show));
    } else {
      const osdPolicies = [
        {
          text: OrderTexts.flightDelayText,
          type: IPolicyTipType.flightDelay,
          show: !!IsHasFlightDelayRule,
        },
        {
          text: OrderTexts.accidentHandleOSDText,
          type: IPolicyTipType.accidentOsd,
          show: !!isHasAccidentOSD,
        },
        {
          text: OrderTexts.storePolicyText,
          type: IPolicyTipType.store,
          show: true,
        },
      ];

      policies.push(...osdPolicies.filter(p => p.show));
    }
    return policies;
  },
);

export const getOrderFuelDesc = createSelector(
  [getVehicleInfo],
  vehicleInfo => {
    return {
      // 燃油提示
      fuelNote: vehicleInfo?.fuelNote,
      // 燃油提示标题
      fuelNoteTitle: vehicleInfo?.fuelNoteTitle,
    };
  },
);
