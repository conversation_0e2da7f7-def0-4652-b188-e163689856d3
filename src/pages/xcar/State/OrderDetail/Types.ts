export const GET_ORDER_DATA = 'ORDER/GET_ORDER_DATA';
export const QUERY_ORDER_SUCCESS = 'ORDER/QUERY_ORDER_SUCCESS';

export const FETCH_ORDER_CALLBACK = 'ORDER/FETCH_DATA_CALLBACK';

export const FETCH_QUERYCANCELFEE = 'ORDER/FETCH_QUERYCANCELFEE';

export const FETCH_QUERY_CANCEL_INFO = 'ORDER/FETCH_QUERY_CANCEL_INFO';

export const FETCH_QUERYCANCELFEE_CALLBACK =
  'ORDER/FETCH_QUERYCANCELFEE_CALLBACK';

export const FETCH_TOCANCELBOOK = 'ORDER/FETCH_TOCANCELBOOK';

export const TO_CANCEL_CALLBACK = 'ORDER/TO_CANCEL_CALLBACK';

export const SET_PRICEMODALVISIBLE = 'ORDER/SET_PRICEMODALVISIBLE';

export const FETCH_CREATECOMMENT = 'ORDER/FETCH_CREATECOMMENT';

export const SHOW_REVIEWSUCCESSMODAL = 'ORDER/SHOW_REVIEWSUCCESSMODAL';

export const SHOW_REVIEWMODAL = 'ORDER/SHOW_REVIEWMODAL';

export const SET_REFUNDMODALVISIBLE = 'ORDER/SET_REFUNDMODALVISIBLE';

export const SET_FULFILLMENT_MODAL_VISIBLE =
  'ORDER/SET_FULFILLMENT_MODAL_VISIBLE';

export const SET_CHANGEORDERMODALVISIBLE = 'ORDER/SET_ORDERCHANGEVISIBLE';

export const SUBMIT_SCOREANDSUGGESTIONS = 'ORDER/SUBMIT_SCOREANDSUGGESTIONS';

export const SUBMIT_SCOREANDSUGGESTIONSCALLBACK =
  'ORDER/SUBMIT_SCOREANDSUGGESTIONSCALLBACK';

export const QUERY_SCOREANDSUGGESTIONS = 'ORDER/QUERY_SCOREANDSUGGESTIONS';

export const QUERY_SCOREANDSUGGESTIONSCALLBACK =
  'ORDER/QUERY_SCOREANDSUGGESTIONSCALLBACK';

export const FETCH_MODIFYORDERCALLBACK = 'ORDER/FETCH_MODIFYORDERCALLBACK';

export const FETCH_CARASSISTANTCALLBACK = 'ORDER/FETCH_CARASSISTANTCALLBACK';

export const SET_ISDCHANGEORDERMODALVISIBLE = 'ORDER/SET_ISDORDERCHANGEVISIBLE';

export const SET_FEEDEDUCTIONVISIBLE = 'ORDER/SET_FEEDEDUCTIONVISIBLE';

export const SET_INSDETAILMODALVISIBLE = 'ORDER/SET_INSDETAILMODALVISIBLE';

export const FETCH_ISDBUYINSURANCE = 'ORDER/FETCH_ISDBUYINSURANCE';

export const SET_CLMODALVISIBLE = 'ORDER/SET_CLMODALVISIBLE';

export const SET_CDMODALVISIBLE = 'ORDER/SET_CDMODALVISIBLE';

export const QUERY_PMSINFOCALLBACK = 'ORDER/QUERY_PMSINFOCALLBACK';

export const GET_LIMIT_CONTENT_SUCCESS = 'ORDER/GET_LIMIT_CONTENT_SUCCESS';

export const GET_LIMIT_CONTENT_DATA = 'ORDER/GET_LIMIT_CONTENT_DATA';

export const LIMITRULE_POP_VISIBLE = 'ORDER/LIMITRULE_POP_VISIBLE';

export const CREATE_ORDER_PAYMENT = 'ORDER/CREATE_ORDER_PAYMENT';

export const CREATE_ORDER_PAYMENT_PARAMS = 'ORDER/CREATE_ORDER_PAYMENT_PARAMS';

export const GET_CREATE_PAYMENT_SUCCESS = 'ORDER/GET_CREATE_PAYMENT_SUCCESS';

export const SET_HISTORY_MODAL_VISIBLE = 'ORDER/SET_HISTORY_MODAL_VISIBLE';

export const SET_DEPOSIT_DEATIL_MODAL_VISIBLE =
  'SET_DEPOSIT_DEATIL_MODAL_VISIBLE';

export const QUERY_SIMILAR_VEHICLE = 'ORDER/QUERY_SIMILAR_VEHICLE';

export const QUERY_ORDER_PRICE_INFO = 'ORDER/QUERY_ORDER_PRICE_INFO';

export const QUERY_ORDER_PRICE_SUCCESS = 'ORDER/QUERY_ORDER_PRICE_SUCCESS';

export const FETCH_CREDIT_NOTIFICATION = 'ORDER/CREDIT_NOTIFICATION';

export const SET_CREDIT_NOTIFICATION = 'ORDER/SET_CREDIT_NOTIFICATION';

export const FETCH_UPDATE_PAYMENT = 'ORDER/FETCH_UPDATE_PAYMENT';

export const UPDATE_PAYMENT_CALLBACK = 'ORDER/UPDATE_PAYMENT_CALLBACK';

export const QUERY_CUSTOMERSERVICEUEL = 'ORDER/QUERY_CUSTOMERSERVICEUEL';

export const QUERY_CUSTOMERSERVICEUEL_SUCCESS =
  'ORDER/QUERY_CUSTOMERSERVICEUEL_SUCCESS';

export const SET_MODIFYFLIGHTNOMODALVISIBLE =
  'ORDER/SET_MODIFYFLIGHTNOMODALVISIBLE';

export const SET_PHONE_MODAL_VISIBLE = 'ORDER/SET_PHONE_MODAL_VISIBLE';

export const SET_PERSON_PHONE_MODAL_VISIBLE =
  'ORDER/SET_PERSON_PHONE_MODAL_VISIBLE';

export const QUERYCCTRIPCONTINUEPAY = 'ORDER/QUERYCCTRIPCONTINUEPAY';

export const CONTINUEPAYMENT = 'ORDER/CONTINUEPAYMENT';

export const CREDIT_RENT_AUTH = 'ORDER/CREDIT_RENT_AUTH';

export const QUERYADDITIONPAYMENT = 'ORDER/QUERYADDITIONPAYMENT';

export const GETRENTALMUSTREAD = 'ORDER/GETRENTALMUSTREAD';

export const SET_FETCH_DONE = 'ORDER/SET_FETCH_DONE';

export const UPDATA_FREE_DEPOSITINFO = 'ORDER/UPDATA_FREE_DEPOSITINFO';
export const RESET = 'ORDER/RESET';

export const RERENT_RETRY = 'ORDER/RERENT_RETRY';

export const QUERY_ORDER_DETAIL = 'QUERY_ORDER_DETAIL';

export const GET_SUPPLEMENT_LIST = 'ORDER/GET_SUPPLEMENT_LIST';
export const SET_QUERYDEDUCTION_DATA = 'ORDER/SET_QUERYDEDUCTION_DATA';

export const GET_SUPPLEMENT_LIST_CALLBACK =
  'ORDER/GET_SUPPLEMENT_LIST_CALLBACK';

export const SET_SUPPLEMENT_LIST_NEW = 'ORDER/SET_SUPPLEMENT_LIST_NEW';

export const SET_VEHICLE_DAMAGE_ID = 'ORDER/SET_VEHICLE_DAMAGE_ID';

export const ISDINSURANCEPAYMENT = 'ORDER/ISDINSURANCEPAYMENT';

export const SETMODALSVISIBLE = 'ORDER/SETMODALSVISIBLE';

export const SET_RENEW_STATUS_BYSTORAGE = 'ORDER/SET_RENEW_STATUS_BYSTORAGE';

export const SET_SELECTOR_DATA = 'ORDER/SET_SELECTOR_DATA';

export const SAVE_RENEWALORDERSTATUS = 'ORDER/SAVE_RENEWALORDERSTATUS';

export const QUERY_ORDER_STATUS = 'ORDER/QUERY_ORDER_STATUS';

export const SET_ORDER_STATUS_SIGN = 'ORDER/SET_ORDER_STATUS_SIGN';

export const PAYCOUNTDOWNTIMEOUT = 'ORDER/PAYCOUNTDOWNTIMEOUT';

export const SET_EASYLIFETAGMODALVISIBLE = 'ORDER/SET_EASYLIFETAGMODALVISIBLE';

export const FETCH_EASYLIFETAG = 'ORDER/FETCH_EASYLIFETAG';

export const FETCH_EASYLIFETAG_CALLBACK = 'ORDER/FETCH_EASYLIFETAG_CALLBACK';

export const SET_TIPPOP_DATA = 'ORDER/SET_TIPPOP_DATA';

export const QUERY_CAR_ASSISTANT_V2 = 'ORDER/QUERY_CAR_ASSISTANT_V2';

export const QUERY_CAR_ASSISTANT_V2_CALLBACK =
  'ORDER/QUERY_CAR_ASSISTANT_V2_CALLBACK';

export const SET_STORAGE_CARDS_TITLE = 'ORDER/SET_STORAGE_CARDS_TITLE';

export const UPDATE_FREE_DEPOSITINFO_BY_CONTINUE_PAY =
  'ORDER/UPDATE_FREE_DEPOSITINFO_BY_CONTINUE_PAY';

export const SET_PRICE_DETAIL_MODAL_VISIBLE =
  'ORDER/SET_PRICE_DETAIL_MODAL_VISIBLE';

export const DEPOSIT_PAY_ONLINE = 'ORDER/DEPOSIT_PAY_ONLINE';

export const SET_ORDER_DETAIL_CONFIRM_MODAL_VISIBLE =
  'ORDER/SET_ORDER_DETAIL_CONFIRM_MODAL_VISIBLE';

export const QUERY_ORDERINSUANDXPRODUCT = 'ORDER_QUERY_ORDERINSUANDXPRODUCT';

export const QUERY_ORDERINSUANDXPRODUCT_CALLBACK =
  'QUERY_ORDERINSUANDXPRODUCT_CALLBACK';
export const SET_SCANNED_IMAGES = 'ORDER/SET_SCANNED_IMAGES';

export const ORDER_CROSS_PARAMS = 'ORDER_CROSS_PARAMS';

export enum IBizScene {
  modifyOrderAdditionalPay = 4, // 修改订单补款
}

export enum IPayStatus {
  waitingPayment = 0,
  // 0-待支付 1-支付中 2-支付成功 3-支付失败 4-部分退款 5-全额退款
}

export enum VendorId {
  ehi = '9787',
}

export enum IStageType {
  PickUp = 'before',
  InUse = 'driving',
  DropOff = 'after',
  Precautions = 'precautions',
}

export const QUERY_ORDERCANCELINFO_CALLBACK =
  'ORDER/QUERY_ORDERCANCELINFO_CALLBACK';

export const QUERYORDERCOUPON_CALLBACK = 'ORDER/QUERYORDERCOUPON_CALLBACK';

export const FETCH_CASHBACK_CALLBACK = 'ORDER/FETCH_CASHBACK_CALLBACK';

export const SET_CONTINUE_PAY_FAIL_DIALOG_INFO =
  'ORDER/SET_CONTINUE_PAY_FAIL_DIALOG_INFO';

export const HANDLE_CONTINUE_PAY_FAIL_DIALOG_CANCEL =
  'ORDER/HANDLE_CONTINUE_PAY_FAIL_DIALOG_CANCEL';

export const SET_FINAL_QUERY_IS_FINISH = 'ORDER/SET_FINAL_QUERY_IS_FINISH';

export const QUERY_FIRST_SCREEN_DATA = 'ORDER/QUERY_FIRST_SCREEN_DATA';

export const FETCH_ORDER_DATA_BY_GRAPHQL = 'ORDER/FETCH_ORDER_DATA_BY_GRAPHQL';

export const SET_CONTINUE_PAY_INTERCEPTION_DATA =
  'ORDER/SET_CONTINUE_PAY_INTERCEPTION_DATA';

export const SET_TRAVEL_LIMIT_SELECTED_RESULT =
  'ORDER/SET_TRAVEL_LIMIT_SELECTED_RESULT';

export const FETCH_MODIFY_CROSS_LOCATION = 'ORDER/FETCH_MODIFY_CROSS_LOCATION';

export const SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE =
  'ORDER/SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE';

export const QUERY_DRIVER_LICENSE_ORDERS = 'ORDER/QUERY_DRIVER_LICENSE_ORDERS';

export const QUERY_DRIVER_LICENSE_ORDERS_CALLBACK =
  'ORDER/QUERY_DRIVER_LICENSE_ORDERS_CALLBACK';

export const QUERY_EXTRA_INSRUANCE = 'ORDER/QUERY_EXTRA_INSRUANCE';

export const SET_IS_QUERY_ORDER_LOADING = 'ORDER/SET_IS_QUERY_ORDER_LOADING';

// 自助取还用车助手获取车机状态
export const QUERY_VEHICLE_STATUS = 'ORDER/QUERY_VEHICLE_STATUS';

export const QUERY_VEHICLE_STATUS_CALLBACK =
  'ORDER/QUERY_VEHICLE_STATUS_CALLBACK';

// 自助取还用车助手操作车机
export const SELF_SERVICE_OPERATION = 'ORDER/SELF_SERVICE_OPERATION';

// 设置取消订单submitId
export const SET_CANCEL_ORDER_SUBMIT_ID = 'ORDER/SET_CANCEL_ORDER_SUBMIT_ID';

export const ORDERDETAIL_PAGE_PRE_CACHE = 'ORDER/ORDERDETAIL_PAGE_PRE_CACHE';

export const SET_FULFILMENT_DATA = 'ORDER/SET_FULFILMENT_DATA';

export const CHECK_SUBMIT_RETURN_CAR = 'ORDER/CHECK_SUBMIT_RETURN_CAR';

export const SET_DISTANCE = 'ORDER/SET_DISTANCE';

export const QUERY_ORDER_NOTICE_FROM_DID = 'ORDER/QUERY_ORDER_NOTICE_FROM_DID';

export const SET_ORDER_NOTICE_FROM_DID = 'ORDER/SET_ORDER_NOTICE_FROM_DID';

export const SET_LOCATIONDATEPOP_VISIBLE = 'ORDER/SET_LOCATIONDATEPOP_VISIBLE';

export const QUERY_OSD_MODIFY_ORDER_NOTE = 'ORDER/QUERY_OSD_MODIFY_ORDER_NOTE';

export const SET_OSD_MODIFY_ORDER_NOTE = 'ORDER/SET_OSD_MODIFY_ORDER_NOTE';

export const OSD_MODIFY_ORDER_INIT = 'ORDER/OSD_MODIFY_ORDER_INIT';
export const QUERY_COUNTRYS_INFO = 'ORDER/QUERY_COUNTRYS_INFO';

export const SET_COUNTRYS_INFO = 'ORDER/SET_COUNTRYS_INFO';

export const QUERY_ORDER_INFO = 'ORDER/QUERY_ORDER_INFO';

export const QUERY_ORDER_INFO_FLAG2 = 'ORDER/QUERY_ORDER_INFO_FLAG2';

export const FULFILLMENT_MODIFY_CONFIRM = 'ORDER/FULFILLMENT_MODIFY_CONFIRM';

export const QUERY_FULFILLMENT = 'ORDER/QUERY_FULFILLMENT';

export const QUERY_ORDER_WARNINGINFO = 'ORDER/QUERY_ORDER_WARNINGINFO';

export const SET_ORDER_FULFILLMENT_MODIFY_INFO =
  'ORDER/SET_ORDER_FULFILLMENT_MODIFY_INFO';

export interface ThirdClientInfo {
  /**
   * 必须有，来源 1 去哪儿访问
   */
  sourceFrom?: number | null;
  /**
   * 用户名
   */
  userName?: string | null;
  /**
   * 非必填 打开失败后跳转的链接
   */
  failUrl?: string | null;
  /**
   * 账户信息
   */
  headInfo?: any;
}
export interface QueryManOrderRequestType {
  /**
   * 订单号
   */
  orderId: number | 0;
  /**
   * 供应商单号
   */
  orderCode?: string | null;
  /**
   * 供应商ID
   */
  vendorId?: number | null;
  /**
   * 供应商编号
   */
  vendorCode?: string | null;
  /**
   * 门店编号
   */
  storeCode?: string | null;
  /**
   * 国家ID
   */
  countryId?: number | null;
  /**
   * 是否历史单
   */
  isHistory?: boolean | null;
  /**
   * ticket
   */
  ticket?: string | null;
  /**
   * translateVersion
   */
  translateVersion?: string | null;
  /**
   * 是否无忧租
   */
  isEasyLife?: boolean | null;
  /**
   * 第三方信息 外网接入时需要 例如去哪儿访问)
   */
  thirdClientInfo?: ThirdClientInfo | null;
  /**
   * 渠道（履约可视化）
   */
  channel?: string | null;
  /**
   * 是否，（履约可视化）
   */
  blnCanRenew?: number | null;
}

export enum ModifyConfirmStatus {
  Agree = 2,
  Disagree = 3,
}
