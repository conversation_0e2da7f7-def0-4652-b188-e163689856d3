import {
  SET_ORDER_AUTH_INFO,
  SET_LOADING,
  SET_MODAL,
  SET_ISRETRY_CARDFACE,
  UPDATE_PERSONAL_AUTHORIZE,
  SET_FACE_REC_MODAL_VISIBLE,
  SET_IS_SELF_SERVICE_FROM_URL,
  ONLINEAUTH_CROSS_PARAMS,
} from './Types';
import { ModalTypes } from '../../Constants/OrderOnlineAuth';
import { LisenceCardFaceType } from './FuntionTypes';

interface OnlineAuthStateType {
  orderAuthInfo: any;
  isLoading: boolean;
  retryingCardFace: any;
  modals: any;
  isPersonalAuthorized: boolean;
  faceRecModalVisible: boolean;
  isSelfServiceFromUrl: boolean;
}

export const getInitalState = (): OnlineAuthStateType => ({
  isLoading: false,
  orderAuthInfo: {},
  retryingCardFace: {
    [LisenceCardFaceType.IdCardAType]: false,
    [LisenceCardFaceType.IdCardBType]: false,
    [LisenceCardFaceType.DriverAType]: false,
    [LisenceCardFaceType.DriverBType]: false,
  },
  modals: {
    [ModalTypes.CONFIRM]: {},
    [ModalTypes.AUTH_SWITCH]: {},
    [ModalTypes.OCR_TIP]: {},
  },
  isPersonalAuthorized: false,
  faceRecModalVisible: false,
  isSelfServiceFromUrl: false,
});

const initalState = getInitalState();

// eslint-disable-next-line @typescript-eslint/default-param-last
export default (state = initalState, action) => {
  const { orderAuthInfo = {}, modals, retryingCardFace } = state;
  switch (action.type) {
    case SET_LOADING:
      return { ...state, isLoading: action.data };
    case SET_ORDER_AUTH_INFO:
      return {
        ...state,
        orderAuthInfo: { ...orderAuthInfo, ...action.data },
      };
    case SET_MODAL:
      return {
        ...state,
        modals: { ...modals, [action.name]: action.data },
      };
    case SET_ISRETRY_CARDFACE:
      return {
        ...state,
        retryingCardFace: { ...retryingCardFace, ...action.data },
      };
    case UPDATE_PERSONAL_AUTHORIZE:
      return {
        ...state,
        isPersonalAuthorized: action.data?.isPersonalAuthorized,
      };
    case SET_FACE_REC_MODAL_VISIBLE:
      return {
        ...state,
        faceRecModalVisible: action.data?.faceRecModalVisible,
      };
    case SET_IS_SELF_SERVICE_FROM_URL:
      return {
        ...state,
        isSelfServiceFromUrl: action.data,
      };
    case ONLINEAUTH_CROSS_PARAMS:
      return {
        ...state,
        ...action.data,
      };
    default:
      return state;
  }
};
