import { pull as lodashPull } from 'lodash-es';

import {
  CHANGE_SELECT_INSURANCE,
  UPDATE_FEE_INFO,
  UNDO_CHAGE_PRICE,
  SET_STATUS,
  RESET_CONFIRM_DATA,
  SET_CONFIRM_MODAL_VISIBLE,
  SET_CONFIRM_MODAL_PROPS,
  SET_CREATE_INS_MODAL_VISIBLE,
  SET_INS_FAILED_MODAL_VISIBLE,
  SET_MODIFY_ORDER_MODAL_PROPS,
  CHANGE_COUPON,
  MODIFY_TO_PAY_SET_LOADING,
  SET_PAY_TIME_OUT_MODAL_VISIBLE,
  SET_MODIFY_DISCOUNTINFO,
  SET_MODIFY_ORDER_CONFIRM_INTERCEPTION_DATA,
  MODIFY_ORDER_CONFIRM_CROSS_PARAMS,
} from './Types';

export const initalState = {
  isLoading: false,
  isPriceLoading: false,
  isSubmitting: false,
  selectedInsuranceId: [],
  selectedCouponsCodes: '',
  feeInfo: null,
  confirmModalVisible: false,
  confirmModalProps: null,
  createInsModalVisible: false,
  insFailedModalVisible: false,
  modifyOrderModalProps: {
    visible: false,
    title: '',
    contentText: '',
    titleHeightlightStyle: '',
  },
  discountInfo: {
    explain: '',
    activityDetail: {},
    couponList: {},
  },
  enablePressCoupon: true,
  payTimeOutModalVisible: false,
  modifyOrderConfirmInterceptionData: null,
};

export const updateSelectedInsurance = (state, data) => {
  const { selectedInsuranceId } = state;
  const { uniqueCode } = data;
  const newSelectedInsuranceId = [...selectedInsuranceId];
  if (uniqueCode) {
    const deleteInsuranceId = newSelectedInsuranceId.find(
      id => id === uniqueCode,
    );
    if (deleteInsuranceId) {
      lodashPull(newSelectedInsuranceId, deleteInsuranceId);
    } else {
      newSelectedInsuranceId.push(uniqueCode);
    }
  }
  return {
    ...state,
    selectedInsuranceId: newSelectedInsuranceId,
  };
};

export const undoChangePrice = (state, data: {}) => ({
  ...state,
  ...data,
});

export default (state = initalState, action) => {
  switch (action.type) {
    case MODIFY_TO_PAY_SET_LOADING:
      return { ...state, isLoading: action.data };
    case CHANGE_SELECT_INSURANCE:
      return updateSelectedInsurance(state, action.data);
    case UPDATE_FEE_INFO:
      return { ...state, feeInfo: action.data };
    case UNDO_CHAGE_PRICE:
      return undoChangePrice(state, action.data);
    case SET_STATUS:
      return { ...state, ...action.data };
    case RESET_CONFIRM_DATA:
      return { ...initalState };
    case SET_CONFIRM_MODAL_VISIBLE:
      return { ...state, confirmModalVisible: action.data };
    case SET_CONFIRM_MODAL_PROPS:
      return { ...state, confirmModalProps: action.data };
    case SET_CREATE_INS_MODAL_VISIBLE:
      return { ...state, createInsModalVisible: action.data };
    case SET_INS_FAILED_MODAL_VISIBLE:
      return { ...state, insFailedModalVisible: action.data };
    case CHANGE_COUPON:
      return { ...state, selectedCouponsCodes: action.data.couponCode };
    case SET_MODIFY_DISCOUNTINFO:
      return { ...state, ...action.data };
    case SET_MODIFY_ORDER_MODAL_PROPS:
      return {
        ...state,
        modifyOrderModalProps: {
          ...state.modifyOrderModalProps,
          ...action.data,
        },
      };
    case SET_PAY_TIME_OUT_MODAL_VISIBLE:
      return { ...state, payTimeOutModalVisible: action.data };
    case SET_MODIFY_ORDER_CONFIRM_INTERCEPTION_DATA:
      return { ...state, modifyOrderConfirmInterceptionData: action.data };
    case MODIFY_ORDER_CONFIRM_CROSS_PARAMS:
      return {
        ...state,
        ...action.data,
      };
    default:
      return state;
  }
};
