import { TitleHightlightType } from '../../ComponentBusiness/OrderConfirmModal/Index';

export const CHANGE_SELECT_INSURANCE =
  'ModifyOrderConfirm/CHANGE_SELECT_INSURANCE';

export const UPDATE_FEE_INFO = 'ModifyOrderConfirm/UPDATE_FEE_INFO';

export const UNDO_CHAGE_PRICE = 'ModifyOrderConfirm/UNDO_CHAGE_PRICE';

export const SET_STATUS = 'ModifyOrderConfirm/SET_STATUS';

export const RESET_CONFIRM_DATA = 'ModifyOrderConfirm/RESET_CONFIRM_DATA';

export const ON_PRESS_SUBMIT = 'ModifyOrderConfirm/ON_PRESS_SUBMIT';

export const SET_CONFIRM_MODAL_VISIBLE =
  'ModifyOrderConfirm/SET_CONFIRM_MODAL_VISIBLE';

export const SET_CONFIRM_MODAL_PROPS =
  'ModifyOrderConfirm/SET_CONFIRM_MODAL_PROPS';

export const SET_CREATE_INS_MODAL_VISIBLE =
  'ModifyOrderConfirm/SET_CREATE_INS_MODAL_VISIBLE';

export const SET_INS_FAILED_MODAL_VISIBLE =
  'ModifyOrderConfirm/SET_INS_FAILED_MODAL_VISIBLE';

export const SET_MODIFY_ORDER_MODAL_PROPS =
  'ModifyOrderConfirm/SET_MODIFY_ORDER_MODAL_PROPS';

export const CHANGE_COUPON = 'ModifyOrderConfirm/CHANGE_COUPON';

export const SET_MODIFY_DISCOUNTINFO =
  'ModifyOrderConfirm/SET_MODIFY_DISCOUNTINFO';

export const MODIFY_TO_PAY_SET_LOADING =
  'ModifyOrderConfirm/MODIFY_TO_PAY_SET_LOADING';

export const MODIFY_TO_PAY = 'ModifyOrderConfirm/MODIFY_TO_PAY'; // 修改订单补款-订单确认信息

export const SET_PAY_TIME_OUT_MODAL_VISIBLE =
  'ModifyOrderConfirm/SET_PAY_TIME_OUT_MODAL_VISIBLE';

export const SET_MODIFY_ORDER_CONFIRM_INTERCEPTION_DATA =
  'ModifyOrderConfirm/SET_MODIFY_ORDER_CONFIRM_INTERCEPTION_DATA';

export const MODIFY_ORDER_CONFIRM_CROSS_PARAMS =
  'MODIFY_ORDER_CONFIRM_CROSS_PARAMS';

export interface setStatusData {
  isPriceLoading?: boolean;
  isSubmitting?: boolean;
}

export interface IModifyOrderModalPropsType {
  visible: boolean;
  title?: string;
  contentText?: string;
  titleHeightlightStyle?: TitleHightlightType;
}

export interface IConfirmModalPropsType {
  title?: string;
  btnName?: string;
  isReSearch?: boolean;
}

export enum IFooterContentsStyleType {
  boldAndOrange = 'bold',
  normalAndGray = 'gray',
}

export enum IModifyType {
  modifyDriver = 1,
  modifyDate = 2,
}

export enum IPriceChangeType {
  priceUp = 1,
  priceDown = 2,
}

export enum IFeeInfoContentsType {
  tip = 0,
}

export enum IModifyConfirmErrorMsgType {
  stayTooLong = 1,
  repeatOrder = 2,
  priceChanged = 3,
  noInventory = 4,
}

export enum IFeeDetailItemStyleType {
  bold = 'b',
}

export enum IInsuranceStatus {
  invalid = 4,
}

export enum IFeeDetailItemType {
  // type 0 是费用项，如租车费等，type 1是活动的，type 2 是优惠券的
  normal = 0,
  activity = 1,
  coupon = 2,
}
