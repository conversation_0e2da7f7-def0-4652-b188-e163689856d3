import { difference as lodashDifference } from 'lodash-es';
import { xShowToast } from '@ctrip/xtaro';
import { takeLatest, put, select, delay } from 'redux-saga/effects';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  CarFetch,
  CarLog,
  User,
  AppContext,
  CarStorage,
  Utils,
  CarFetchHelper,
  GetABCache,
  CarServerABTesting,
} from '../../Util/Index';
import {
  ApiResCode,
  LogKeyDev,
  StorageKey,
  LogKey,
} from '../../Constants/Index';
import {
  getServerRequestId,
  getListRequestId,
  setListStatusData,
  listStatusKeyList,
} from '../../Global/Cache/ListReqAndResData';
import {
  QUERY_VEHICLE_DETAIL_LIST,
  GO_TO_BOOKING,
  QUERY_MULTIMEDIA_ALBUM,
  SET_CURRENTPAGEPARAMS,
  QUERY_INSURANCE_DETAILS,
  QUERY_SHELVES_COMMENT_SUMMARY,
  QUERY_VENDORLIST_IPOLL_CONFIG,
} from './Types';
import {
  setIsCouponBookAtBookingPage,
  setIsLoginAtBookingPage,
  setModalStatus,
  setStatus,
  setTimeOutPopData,
  setVehicleListRequestAndResponse,
  setFristScreenParam,
  setInsuranceDetail,
  queryMultimediaAlbumCallBack,
  setShelvesCommentSummary,
  fetchShelvesCommentSummary,
  setShelvesCommentSummaryLoading,
  setVendorIpollConfig,
} from './Actions';
import { addVehicleSaleOutList } from '../List/Actions';
import { getPickUpCityId } from '../LocationAndDate/Selectors';
import { clear as clearProductConfirm } from '../ProductConfirm/Actions';
import {
  packageQueryVehicleDetailListParams,
  formatMultimediaAlbum,
} from './Mappers';
import { getIsFilteredRecommend } from '../../Global/Cache/ListResSelectors';
import { validateTimeIsOutIsd } from '../../Helpers/validateTimeIsOut';

import {
  getFirstScreenParamToBooking,
  getCouponBookInfoByUniqueCode,
  getVehicleDetailListResponse,
  getVendorInfoByUniqueCode,
  getUniqueCode,
  getFloorId,
  getPackageCode,
  getUniquePriceInfo,
} from './Selectors';
import { ActionType } from '../../Types/ActionType';
import { setRebookParams } from '../ModifyOrder/Actions';
import {
  getSpecificVendorPriceList,
  getFilteredVendorPriceList,
  generateFloorId,
} from './Method';
import { VendorPriceListType } from '../../Types/Dto/QueryVehicleDetailListResponseType';

export function* queryVehicleDetailList() {
  yield takeLatest(QUERY_VEHICLE_DETAIL_LIST, function* logic(action) {
    // 初始状态重置
    yield put(
      setStatus({
        isLoading: true,
        isError: false,
      }),
    );
    const state = yield select();
    // @ts-ignore
    const { reqParam, callbackFun, noShowLoadTip } = action.data;
    const request = packageQueryVehicleDetailListParams(state, reqParam);
    yield put(setVehicleListRequestAndResponse({ request }));
    let isError = false;
    let errorInfo = null;

    const fetchFn = CarFetch.queryVehicleDetailList;
    let response = yield fetchFn(request).catch(error => {
      isError = true;
      errorInfo = error;
    }) || {};

    // 处理服务端返回的floorId重复问题，根据出现的次数生成新的floorId
    if (response?.floor?.length > 0) {
      response.floor = generateFloorId(response.floor);
    }
    const cacheInvalid =
      !response?.baseResponse?.isSuccess &&
      [
        ApiResCode.VehicleDetailListResCode.CACHE_INVALID,
        ApiResCode.VehicleDetailListResCode.SEARCH_NO_RESULT,
        ApiResCode.VehicleDetailListResCode.FILTER_NO_RESULT,
      ].includes(response?.baseResponse?.code);
    const isNoVehicle =
      !response?.baseResponse?.isSuccess &&
      response?.baseResponse?.code ===
        ApiResCode.VehicleDetailListResCode.NO_VEHICLE;
    const isNoVendorPrice =
      response?.baseResponse?.isSuccess &&
      !getSpecificVendorPriceList(response)?.length &&
      !response?.floor?.length;
    // 如果返回无结果且code不是已约定的code也无recommendInfo则前端展示兜底提示
    if (
      !response?.baseResponse?.isSuccess &&
      !cacheInvalid &&
      !response?.recommendInfo?.reason
    ) {
      if (response?.baseResponse?.isSuccess !== false) {
        response = {};
        response.recommendInfo = {
          get reason() {
            return '网络不给力';
          },
          get recommend() {
            return '请检查网络设置后再试';
          },
        };
      } else {
        response.recommendInfo = {
          get reason() {
            return '暂无符合要求的报价哦';
          },
          get recommend() {
            return '建议您修改取还车条件';
          },
        };
      }
    }
    AppContext.setEasyLifeSwitch(response?.extras?.packageLevelSwitch);
    AppContext.setGoodsShelvesTwoSwitch(
      response?.extras?.goodsShelvesTwoSwitch,
    );
    AppContext.setGoodsShelvesTwoABVersion(
      response?.extras?.goodsShelvesTwoABVersion,
    );
    yield put(
      setVehicleListRequestAndResponse({
        response,
      }),
    );
    // 重搜后无资源，首屏展示同上一次
    if ((cacheInvalid || isNoVehicle) && state.VendorList?.firstScreenParam) {
      yield put(
        setFristScreenParam({
          isUsed: false,
          vehicleInfo: {
            ...state.VendorList?.firstScreenParam?.vehicleInfo,
            license: '', // 重搜无结果统一屏蔽拍牌照信息
          },
        }),
      );
    }
    yield put(
      setStatus({
        isLoading: false,
        isError,
        errorInfo: Utils.composeError2String(errorInfo),
      }),
    );
    BbkUtils.ensureFunctionCall(callbackFun);
    // 实时查询提示
    if (response?.isFromSearch && response?.uniqSign) {
      if (!noShowLoadTip)
        xShowToast({
          title: '库存、报价可能发生变化，已重新加载',
          duration: 3000,
        });
      setListStatusData(
        listStatusKeyList.cacheExpireFromVendorListPage,
        response.uniqSign,
      );
    }
    // 接口返回的符合Section为空数组时，判定该车型已售罄
    // 2022-8-23 对于车型已下线的情况，也添加售罄标记
    // 2023-01-17 对于筛选无结果推荐，无符合筛选车辆时，不标记筛选推荐车辆为已售罄
    const isFilteredRecommend = getIsFilteredRecommend();
    if (!isFilteredRecommend && (isNoVendorPrice || isNoVehicle)) {
      yield put(addVehicleSaleOutList(reqParam?.ctripVehicleId));
    }

    if (cacheInvalid) {
      setListStatusData(listStatusKeyList.refreshFromVendorListPage, true);
    }

    // 如果该车型下有任意一个报价是领券订产品，且该券id+uid没有提示过，则增加提示
    if (response?.promotMap) {
      const curRecordIds =
        (yield CarStorage.loadAsync(
          StorageKey.CAR_COUPON_BOOK_IDS,
          true,
          true,
        )) || '';
      const curRecordIdList = curRecordIds ? curRecordIds.split(',') : [];
      const differenceVal = lodashDifference(
        Object.keys(response.promotMap),
        curRecordIdList,
      );
      if (differenceVal.length > 0) {
        xShowToast({
          title: '已自动为您计算券后价格',
          duration: 3000,
        });
        CarStorage.save(
          StorageKey.CAR_COUPON_BOOK_IDS,
          curRecordIdList.concat(differenceVal).toString(),
          '30d',
          true,
        );
      }
    }

    // 请求结果埋点
    const { baseResponse = {} } = response || {};
    if (response.extras?.goodsShelvesTwoSwitch === '1') {
      yield put(
        fetchShelvesCommentSummary({
          pageParam: reqParam,
        }),
      );
    }

    const hasFilteredVendor = getFilteredVendorPriceList(response)?.length > 0; // 不符合当前筛选条件的
    const hasSpecificVendor = getSpecificVendorPriceList(response)?.length > 0; // 符合当前筛选条件的
    const hasResult = isError
      ? false
      : hasFilteredVendor || hasSpecificVendor || response?.floor?.length > 0;
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_query_vehicle_detail_list_result,
      info: {
        eventResult: hasResult,
        baseResponse: JSON.stringify(baseResponse),
        resCode: baseResponse?.code,
        error: isError ? Utils.composeError2String(errorInfo) : '',
        errorCode: isError ? ApiResCode.TraceCode.E1001 : '',
        expCode:
          response?.baseResponse?.code ||
          (isError && Utils.getFrontEndExpCode(response, errorInfo)) ||
          '',
        expMsg:
          response?.baseResponse?.returnMsg ||
          (isError && Utils.getErrorMessage(errorInfo)) ||
          '',
      },
    });
  });
}

// 获取评价
export function* queryShelvesCommentSummary() {
  yield takeLatest(QUERY_SHELVES_COMMENT_SUMMARY, function* logic(action) {
    // @ts-ignore
    const { pageParam } = action.data;
    const state = yield select();
    // 设置加载状态为true
    yield put(setShelvesCommentSummaryLoading(true));
    const request = {
      pickCityId: getPickUpCityId(state),
      calabiVehicleId: pageParam.ctripVehicleId,
      klbVersion: 1,
    };
    const res = yield CarFetch.getCommentSummary(request).catch(() => {});
    yield put(setShelvesCommentSummary(res?.commentAggregation || {}));
    yield put(setShelvesCommentSummaryLoading(false));
  });
}

// 打开保险弹窗
export function* queryInsuranceDetails() {
  yield takeLatest(QUERY_INSURANCE_DETAILS, function* logic(action) {
    const state = yield select();
    // @ts-ignore
    const { pageParam, reference, allPackageLevel } = action.data;
    const reqParam = packageQueryVehicleDetailListParams(state, pageParam);
    const request = {
      pickupPointInfo: reqParam.pickupPointInfo,
      returnPointInfo: reqParam.returnPointInfo,
      reference,
      packageLevelInfos: allPackageLevel,
    };
    const response = yield CarFetch.getVendorListInsuranceDetails(request);
    yield put(setInsuranceDetail(response?.rentalGuarantee));
  });
}

// 处理跳转填写页
export function* handleGoToBooking() {
  yield takeLatest(GO_TO_BOOKING, function* logic(action: ActionType) {
    const {
      pTime,
      uniqueCode = '',
      floorId = '',
      packageCode = '',
      logData,
    } = action.data;
    const initState = yield select();
    const curUniqueCode = getUniqueCode(initState);
    const curFloorId = getFloorId(initState);
    const curPackageCode = getPackageCode(initState);
    if (
      uniqueCode !== curUniqueCode ||
      floorId !== curFloorId ||
      packageCode !== curPackageCode
    ) {
      // 切换产品，置空信息弹层数据
      yield put(clearProductConfirm());
    }
    // 跳转填写前设置 uniqueCode,请求VehicleInfo信息供填写页预展示参数的拼装firstScreenParam
    yield put(setModalStatus({ uniqueCode, floorId, packageCode }));
    if (validateTimeIsOutIsd(pTime)) {
      yield put(
        setTimeOutPopData({
          visible: true,
        }),
      );
      return;
    }
    const isLogin = User.isLoginSync();
    if (!isLogin) {
      const isLoginNow = yield User.toLogin();
      if (!isLoginNow) {
        return;
      }
      yield put(setIsLoginAtBookingPage(true));
      setListStatusData(listStatusKeyList.refreshFromVendorListPage, true);
    }

    const state = yield select();
    // 如果是领券订产品，先领券再跳转
    const info = getCouponBookInfoByUniqueCode(
      getVehicleDetailListResponse(state),
      uniqueCode,
    );
    if (info.isCouponBook && info.promotionId && info.promotionSecretId) {
      let toastMsg = '';
      const receiveRes = yield CarFetch.receivePromotion({
        promotionId: info.promotionId,
        promotionSecretId: info.promotionSecretId,
        scenes: '998',
      }).catch(() => {
        toastMsg = '系统异常，请刷新页面后重试';
      });
      if (receiveRes?.receivePromotionInfo) {
        toastMsg = '已为您领取优惠券，您已享受最优价格';
      } else if (
        !receiveRes?.baseResponse?.isSuccess &&
        receiveRes?.baseResponse?.showMessage
      ) {
        toastMsg = receiveRes?.baseResponse?.showMessage;
      }
      if (toastMsg) {
        xShowToast({ title: toastMsg, duration: 3000 });
      }
      yield put(setIsCouponBookAtBookingPage(true));
      setListStatusData(listStatusKeyList.couponBookFromVendorListPage, true);
    }

    const curState = yield select();
    const firstScreenParam = getFirstScreenParamToBooking(curState);
    let curVendorInfo: VendorPriceListType = null;
    if (floorId) {
      curVendorInfo = getUniquePriceInfo(curState);
    } else {
      curVendorInfo = getVendorInfoByUniqueCode(
        getVehicleDetailListResponse(state),
        uniqueCode,
      );
    }
    const reference = curVendorInfo?.reference;
    const isdParam = {
      vpid: firstScreenParam?.vehicleInfo?.vehicleCode,
      vehicleName: firstScreenParam?.vehicleInfo?.name,
      vendorid: reference?.vendorCode,
      csname: curVendorInfo?.vendorName,
      psid: reference?.pStoreCode,
      rsid: reference?.rStoreCode,
    };
    const newReference = { ...reference, isdParam };
    // 跳转到填写页
    const isISDShelves2B = CarServerABTesting.isISDShelves2B();
    const isISDShelves3 = GetABCache.isISDShelves3();
    const pageToNavigate =
      isISDShelves2B && isISDShelves3 ? 'BookingIsd' : 'Booking';

    AppContext.PageInstance.push(pageToNavigate, {
      firstScreenParam,
      reference: newReference,
      vendorPriceInfo: curVendorInfo,
      isHasEtcIntroModal: true, // 货架页面跳转填写页时，ETC弹窗使用货架页的ETCModal展示
      logData,
    });

    yield delay(500);
    yield put(
      setModalStatus({
        productConfirmModalVisible: false,
        priceDetailModalVisible: false,
        totalPriceModalVisible: false,
      }),
    );
    CarLog.LogCode({ name: '点击_立即预订' });
    CarLog.LogTrace({
      key: LogKey.cm_car_app_click_airanking,
      info: {
        name: '点击_立即预订',
        newMergeId: AppContext.currentNewMergeId,
        vehicleId: firstScreenParam?.vehicleInfo?.vehicleCode,
        serverRequestId: getServerRequestId(),
        requestId: getListRequestId(),
      },
    });
  });
}

export function* queryMultimediaAlbum() {
  yield takeLatest(QUERY_MULTIMEDIA_ALBUM, function* logic(action) {
    try {
      // @ts-ignore
      const { data } = action;
      const { storeId, vehicleId, skuId, albumType, isPreFetch } = data;
      if (!(Number(vehicleId) > 0)) {
        return;
      }
      const param = {
        storeId,
        vehicleId: Number(vehicleId),
        skuId,
        albumType,
      };
      const parameter = CarFetchHelper.parameterBuilder({
        param,
        cachePolicy: { enableCache: true },
      });
      const res = yield CarFetch.queryMultimediaAlbum(parameter);
      if (res?.baseResponse?.isSuccess && !isPreFetch) {
        const { multimediaAlbum, totalPhotos } = formatMultimediaAlbum(
          res?.multimediaAlbum,
        );
        yield put(
          queryMultimediaAlbumCallBack({
            multimediaAlbum,
            totalPhotos,
          }),
        );
      }
    } catch {
      yield put(queryMultimediaAlbumCallBack({}));
    }
  });
}

// 处理看了又看传入参数副作用
export function* handlePageParams() {
  yield takeLatest(SET_CURRENTPAGEPARAMS, function* logic(action: ActionType) {
    // @ts-ignore
    const { data } = action || {};
    // 更新 appContext
    if (data?.appContext) {
      AppContext.updateAppContextValueFromProps(data?.appContext);
    }
    // 透传修改订单参数
    if (data?.rebookParams) {
      yield put(setRebookParams(data?.rebookParams));
    }
  });
}

export function* queryVendorIpollConfig() {
  yield takeLatest(
    QUERY_VENDORLIST_IPOLL_CONFIG,
    function* logic(action: ActionType) {
      const { pageType } = action.data;
      const params = {
        pageType,
      };
      const response = yield CarFetch.getIpollConfig(params).catch(() => {});
      if (response?.baseResponse?.isSuccess) {
        if (response?.allConfigs) {
          yield put(setVendorIpollConfig(response?.allConfigs));
        }
      }
    },
  );
}

export default [
  queryVehicleDetailList(),
  handleGoToBooking(),
  queryMultimediaAlbum(),
  handlePageParams(),
  queryInsuranceDetails(),
  queryShelvesCommentSummary(),
  queryVendorIpollConfig(),
];
