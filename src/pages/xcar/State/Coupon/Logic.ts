import { xShowToast } from '@ctrip/xtaro';

import { takeEvery, put } from 'redux-saga/effects';
import {
  FETCH_RECEIVE_PROMOTION,
  FETCH_RECEIVE_PROMOTION_CALLBACK_TASK,
  RECEIVE_ALL_PROMOTION,
  RECEIVE_PROMOTION,
} from './Types';

import {
  fetchReceivePromotionCallback,
  fetchReceivePromotionCallbackTask,
  receiveAllPromotionCallback,
  receivePromotionCallback,
  setIsListReceiveSuccess,
} from './Actions';
import { CarFetch, CarLog, AppContext, Utils } from '../../Util/Index';
import { LogKey } from '../../Constants/Index';
import Channel from '../../Util/Channel';
import { ActionType } from '../../Types/ActionType';
import { getApiFetchReceivePromotionParams } from './Requests';
import { texts } from '../../ComponentBusiness/Coupon/src/Texts';
import { queryTripRecommend } from '../Home/Actions';

// 处理获取优惠券列表后的回调
export function* apiFetchReceivePromotionCallBackTask() {
  yield takeEvery(
    FETCH_RECEIVE_PROMOTION_CALLBACK_TASK,
    function* logic(action: ActionType) {
      // @ts-ignore
      const { res, scenes, listFetchSuccess } = action?.data || {};
      if (res?.baseResponse?.isSuccess) {
        const {
          title = '',
          promotions = [],
          marketCouponStatus,
          marketPromotions = [],
        } = res || {};
        let mergedPromotions = [];
        if (Utils.isCtripIsd()) {
          // 只在境内做合并新节点 marketPromotions 数据
          mergedPromotions = [...marketPromotions, ...promotions];
        } else {
          mergedPromotions = promotions;
        }
        yield put(
          fetchReceivePromotionCallback({
            title,
            promotions:
              mergedPromotions?.length > 0
                ? mergedPromotions
                : Utils.EmptyArray,
            scenes,
            marketCouponStatus,
            listFetchSuccess,
          }),
        );
      } else {
        yield put(
          fetchReceivePromotionCallback({
            title: '',
            promotions: Utils.EmptyArray,
            scenes,
            listFetchSuccess,
          }),
        );
      }
    },
  );
}

export function* apiFetchReceivePromotion() {
  yield takeEvery(FETCH_RECEIVE_PROMOTION, function* logic(action: ActionType) {
    const param = getApiFetchReceivePromotionParams(action);
    const res = yield CarFetch.getReceivePromotion(param).catch(() => {});
    yield put(
      fetchReceivePromotionCallbackTask({
        res,
        scenes: param?.scenes,
        listFetchSuccess: action?.data.listFetchSuccess,
      }),
    );
  });
}

export function* apiReceivePromotion() {
  yield takeEvery(RECEIVE_PROMOTION, function* logic(action: ActionType) {
    const {
      promotionSecretId,
      promotionId,
      isHomeCouponItem,
      isList,
      isBirth,
      vehicleCode,
      isVendorList,
    } = action?.data || {};
    let toastMsg = '';
    const param = { promotionSecretId, promotionId };
    const res = yield CarFetch.receivePromotion(param).catch(() => {
      toastMsg = '系统异常，请刷新页面后重试';
    });
    if (res && res.receivePromotionInfo) {
      // 清空列表页缓存 领取成功后
      AppContext.setUserFetchCacheId({
        actionType: 'receivePromotionInfo',
      });
      const { receivePromotionInfo } = res;
      yield put(receivePromotionCallback(receivePromotionInfo));
      // 领取成功后，刷新信息流数据
      yield put(queryTripRecommend({ isInit: true }));
    }

    if (res?.baseResponse?.isSuccess && isList) {
      yield put(setIsListReceiveSuccess(true));
    }

    if (!res?.baseResponse?.isSuccess && res?.baseResponse?.showMessage) {
      toastMsg = res?.baseResponse?.showMessage;
    }

    if (toastMsg) {
      xShowToast({ title: toastMsg, duration: 3000 });
    }
    CarLog.LogCode({
      key: LogKey.CLICK_KEY,
      // eslint-disable-next-line no-nested-ternary
      name: isBirth
        ? '点击_生日福利_领券'
        : // eslint-disable-next-line no-nested-ternary
          isHomeCouponItem
          ? '点击_首页_领券卡片_领券'
          : isVendorList
            ? '点击_产品详情页_券包弹层_领取'
            : '点击_领券弹层_领取',

      // eslint-disable-next-line no-nested-ternary
      pageId: isBirth
        ? Channel.getPageId().MemberBirth.ID // eslint-disable-next-line no-nested-ternary
        : isVendorList
          ? Channel.getPageId().VendorList.ID
          : isList
            ? Channel.getPageId().List.ID
            : Channel.getPageId().Home.ID,
      couponId: promotionId,
      couponRecieveStatus: res?.baseResponse?.isSuccess ? 1 : 2,
      vehicleCode,
    });
  });
}

export function* apiReceiveAllPromotion() {
  yield takeEvery(RECEIVE_ALL_PROMOTION, function* logic(action: ActionType) {
    try {
      const {
        promotionSecretIds,
        promotionIds,
        isList,
        callback,
        isNewBooking,
      } = action?.data || {};
      const param = { promotionSecretIds, promotionIds };
      const res = yield CarFetch.receivePromotion(param);
      const { baseResponse, receivePromotionInfos } = res || {};
      const { isSuccess, showMessage } = baseResponse || {};
      if (isSuccess && receivePromotionInfos?.length > 0) {
        // 清空列表页缓存 领取成功后
        AppContext.setUserFetchCacheId({
          actionType: 'receivePromotionInfo',
        });
        yield put(receiveAllPromotionCallback(receivePromotionInfos));
        // 领取成功
        xShowToast({ title: texts.receiveSuccess, duration: 3000 });
        if (isList) {
          yield put(setIsListReceiveSuccess(true));
        }
        if (isNewBooking && callback) {
          callback();
        }
      } else if (showMessage) {
        xShowToast({ title: showMessage, duration: 3000 });
      }
    } catch (err) {
      yield put(receiveAllPromotionCallback([]));
      xShowToast({ title: texts.receiveError, duration: 3000 });
    }
  });
}

export default [
  apiFetchReceivePromotion(),
  apiFetchReceivePromotionCallBackTask(),
  apiReceivePromotion(),
  apiReceiveAllPromotion(),
];
