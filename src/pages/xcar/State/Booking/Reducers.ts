/* eslint-disable @typescript-eslint/default-param-last */
import { DriverItem } from '../../ComponentBusiness/BookForm';
import * as TYPES from './Types';
import { ActionType } from '../../Types/ActionType';
import Utils from '../../Util/Utils';
import { IInputType } from '../../ComponentBusiness/BookForm/src/Type';
import { getOptionalContactMethods } from '../../Global/Cache/ProductSelectors';

const defaultCancelFee = {
  amount: 0,
  currencyCode: 0,
  canRefund: false,
  auditTime: '',
  orderAmount: 0,
};

export const initalState = {
  isMaskLoading: false,
  driverInfo: [
    {
      type: 'areaCode',
      value: '',
      error: false,
    },
  ],
  passengerError: false,
  uniqueOrderId: 0,
  flightErrorCode: '',
  flightErrorTimes: 0,
  orderId: 0,
  orderData: {},
  isNameReverse: false,
  createOrderFailModalVisible: false,
  uniqueOrderModalVisible: false,
  flightErrorModalVisible: false,
  selectedCouponsCodes: undefined,
  baseResponse: {},
  preLicensData: null, // 验单返回数据
  isSnapShotFinish: false,
  supplierData: null, // 国内供应商信息
  supplierModalVisible: false,
  depositIntroduceModalVisible: false,
  ctripCreditFModalVisible: false,
  priceChangePopVisible: false,
  insRemindPopVisible: false,
  createInsLoadingPopVisible: false,
  createInsFailPopVisible: false,
  ehiFreeDepositModalVisible: false,
  freeDepositModalVisible: false,
  isEasyLifeModalVisible: false,
  rebookPenalty: '', // 取消重订下单的申请退违约金金额
  resCancelFeeRebook: defaultCancelFee,
  logInfo: null,
  vendorPriceInfo: null, // 填写页缓存供应商价格数据
  strongSubmitInfoReqParams: null, // 重复下单弱拦截弹窗“继续下单”按钮请求createOrder需要传入的参数
  checkFlightNoLoading: false, // 是否正在校验航班号
  flightErrorTip: '', // 航班号校验失败的提示文案
  depositRateDescriptionModalVisible: false,
  flightDelayRulesModalVisible: false,
  isBusinessLicenseModalVisible: false, // 底部资质弹层
  osdModifyOrderNote: null,
  localContactsData: [],
  hasReceivedCouponAtBooking: false,
};

const getLocalContactsData = type => {
  const optionalContactMethods = getOptionalContactMethods();
  if (type === IInputType.localContacts) {
    if (optionalContactMethods?.length > 0) {
      return {
        type,
        value: '',
        error: false,
        contactType: optionalContactMethods[0].contactType,
        contactTypeName: optionalContactMethods[0].title,
      };
    }
    return {
      type: '',
    };
  }
  return {};
};

const changeFormData = (state, action) => {
  let { driverInfo } = state;
  const { data = [] } = action;
  const sequence = Utils.isCtripIsd() ? TYPES.ISD_SEQUENCE : TYPES.OSD_SEQUENCE;
  // 是否需要更新表单数据中的驾驶员信息，判断依据为传入的参数与更新的参数有所不同
  let isNeedUpdate = false;
  driverInfo = sequence
    .map(type => ({
      type,
      value: '',
      error: false,
      ...getLocalContactsData(type),
    }))
    // @ts-ignore
    .concat(initalState.driverInfo)
    .map((item: DriverItem) => {
      const driverInfoItem = driverInfo.find(
        (v: DriverItem) => v.type === item.type,
      );
      const inputItem = data.find((v: DriverItem) => v.type === item.type);
      // 有输入内容且与reducer中的内容不一致才需要更新
      if (
        !isNeedUpdate &&
        !!inputItem &&
        JSON.stringify(driverInfoItem) !== JSON.stringify(inputItem)
      ) {
        isNeedUpdate = true;
      }
      return {
        ...item,
        ...driverInfoItem,
        ...inputItem,
      };
    })
    .filter(item => !!item.type);
  const localContactsInput = data.find(
    (v: DriverItem) => v.type === IInputType.localContacts,
  );
  const { localContactsData } = localContactsInput || {};
  if (isNeedUpdate) {
    return {
      ...state,
      driverInfo,
      localContactsData: localContactsData || state.localContactsData,
    };
  }
  return state;
};
const setFlightNo = (state, action) => {
  let flightErrorModalVisible = false;
  const { flightErrorTimes } = state;
  if (flightErrorTimes > 0) {
    flightErrorModalVisible = true;
  }
  return {
    ...state,
    flightErrorCode: action.data,
    flightErrorModalVisible,
    flightErrorTimes: flightErrorTimes + 1,
  };
};
const setLoading = (state, action) => {
  const { option, data: isMaskLoading } = action;
  if (option && option.clearOrder) {
    return {
      ...state,
      uniqueOrderId: 0,
      flightErrorCode: '',
      orderId: 0,
      orderData: {},
      baseResponse: {},
      isMaskLoading,
      createOrderFailModalVisible: false,
      uniqueOrderModalVisible: false,
      flightErrorModalVisible: false,
      ehiFreeDepositModalVisible: false,
      preLicensData: null,
    };
  }
  return { ...state, isMaskLoading };
};

export default (state = initalState, action: ActionType) => {
  const actionType = action.type;
  switch (actionType) {
    case TYPES.CHANGE_FORM:
      return changeFormData(state, action);
    case TYPES.LOADING:
      return setLoading(state, action);
    case TYPES.UNIQUEORDER:
      return {
        ...state,
        uniqueOrderId: action.data,
        uniqueOrderModalVisible: true,
      };
    case TYPES.CREATEORDERSUCCESS:
      return { ...state, orderData: action.data, orderId: action.data.orderId };
    case TYPES.CREATEFAIL:
      return { ...state, createOrderFailModalVisible: true };
    case TYPES.FLIGHTNO:
      return setFlightNo(state, action);
    case TYPES.NAMEREVERSE:
      return { ...state, isNameReverse: action.data };
    case TYPES.CHANGE_MODALE:
      return { ...state, ...action.data };
    case TYPES.CHANGE_COUPON:
      return { ...state, selectedCouponsCodes: action.data };
    case TYPES.CLEAR:
      return {
        ...initalState,
        driverInfo: state.driverInfo,
        osdModifyOrderNote: state.osdModifyOrderNote, // 修改订单提示不需要重置
        hasReceivedCouponAtBooking: state.hasReceivedCouponAtBooking, // 填写页是否领券不需要重置
      };
    case TYPES.CLEAR_WITHOUT_VENDOR_INFO:
      return {
        ...initalState,
        driverInfo: state.driverInfo,
        vendorPriceInfo: state.vendorPriceInfo,
      };
    case TYPES.ERRORINFO:
      return { ...state, baseResponse: action.data };
    case TYPES.SET_PRELICENDING:
      return { ...state, preLicensData: action.data };
    case TYPES.SET_SNAPSHOT_FINISH:
      return { ...state, isSnapShotFinish: action.data };
    case TYPES.SET_PRICECHANGE_POP_VISIBLE:
      return { ...state, priceChangePopVisible: action.data };
    case TYPES.SET_INSREMIND_POP_VISIBLE:
      return { ...state, insRemindPopVisible: action.data };
    case TYPES.SET_CREATEINS_LOADING_POP_VISIBLE:
      return { ...state, createInsLoadingPopVisible: action.data };
    case TYPES.SET_EASYLIFE_POP_VISIBLE:
      return { ...state, isEasyLifeModalVisible: action.visible };
    case TYPES.SET_CREATEINS_FAIL_POP_VISIBLE:
      return { ...state, createInsFailPopVisible: action.data };
    case TYPES.SET_PASSENGER_ERROR:
      return { ...state, passengerError: action.data };
    case TYPES.SET_EHI_FREEDEPOSIT_MODAL_VISIBLE:
      return { ...state, ehiFreeDepositModalVisible: action.data };
    case TYPES.SET_FREE_DEPOSIT_MODAL_VISIBLE:
      return { ...state, freeDepositModalVisible: action.data };
    case TYPES.SET_REBOOK_PENALTY:
      return { ...state, rebookPenalty: action.data?.rebookPenalty };
    case TYPES.RESET_CANCELFEE_REBOOK:
      return { ...state, resCancelFeeRebook: defaultCancelFee };
    case TYPES.SET_RECEIVED_BOOKING_COUPON:
      return { ...state, hasReceivedCouponAtBooking: action.data };
    case TYPES.FETCH_CANCELFEE_REBOOK_CALLBACK:
      return {
        ...state,
        resCancelFeeRebook: action.data.resCancelFee,
        rebookPenalty: '',
      };
    case TYPES.SET_LOG_INFO:
      return { ...state, logInfo: action.data };
    case TYPES.SET_VENDOR_PRICE_DATA:
      return { ...state, vendorPriceInfo: action.data };
    case TYPES.SET_STRONG_SUBMIT_INFO_REQ_PARAMS:
      return { ...state, strongSubmitInfoReqParams: action.data };
    case TYPES.SET_CHECK_FLIGHT_NO_LOADING:
      return { ...state, checkFlightNoLoading: action.data };
    case TYPES.SET_FLIGHT_ERROR_TIP:
      return { ...state, flightErrorTip: action.data };
    case TYPES.SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE:
      return {
        ...state,
        depositRateDescriptionModalVisible: action.data,
      };
    case TYPES.SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE:
      return {
        ...state,
        flightDelayRulesModalVisible: action.data,
      };
    case TYPES.SET_BUSINESS_LICENSE_VISIBLE:
      return {
        ...state,
        isBusinessLicenseModalVisible: action.data,
      };
    case TYPES.SET_OSD_MODIFY_ORDER_NOTE:
      return {
        ...state,
        osdModifyOrderNote: action.data?.osdModifyOrderNote,
      };
    case TYPES.SET_LOCALCONTACT_DATA:
      return {
        ...state,
        localContactsData: action.data,
      };
    default:
      return state;
  }
};
