import { DriverItem } from '../../ComponentBusiness/BookForm';
import {
  CHANGE_FORM,
  <PERSON>LE<PERSON>,
  <PERSON><PERSON><PERSON>_WITHOUT_VENDOR_INFO,
  CREATEORDER,
  UNIQUEORDER,
  CREATEORDERSUCCESS,
  NAMEREVERS<PERSON>,
  <PERSON>EA<PERSON><PERSON><PERSON>,
  LOADING,
  SNAPSHOT,
  <PERSON><PERSON><PERSON><PERSON>,
  CHANGE_COUPON,
  CHANGE_MODALE,
  <PERSON><PERSON>RINFO,
  SET_PRELICENDING,
  SET_SNAPSHOT_FINISH,
  SET_PRICECHANGE_POP_VISIBLE,
  SET_INSREMIND_POP_VISIBLE,
  SET_CREATEINS_LOADING_POP_VISIBLE,
  SET_CREATEINS_FAIL_POP_VISIBLE,
  SET_EASYLIFE_POP_VISIBLE,
  INSCONFIRM_CALLBACK,
  CLOSE_INSURANCE_LOADING_VIEW,
  SET_EHI_FREEDEPOSIT_MODAL_VISIBLE,
  SET_SELECTED_LOAN_PAY_STAGE_COUNT,
  SET_PASSENGER_ERROR,
  SET_<PERSON><PERSON><PERSON><PERSON>_PENALTY,
  RESET_CANCELFEE_REBOOK,
  <PERSON><PERSON><PERSON>_CANCELFEE_REBOOK,
  FETCH_CANCELFEE_REBOOK_CALLBACK,
  SET_LOG_INFO,
  SET_VENDOR_PRICE_DATA,
  SET_STRONG_SUBMIT_INFO_REQ_PARAMS,
  VALIDATE_FLIGHT_NO,
  SET_CHECK_FLIGHT_NO_LOADING,
  SET_FLIGHT_ERROR_TIP,
  SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
  SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE,
  REFRESH,
  SET_BUSINESS_LICENSE_VISIBLE,
  SET_FUEL_DESCRIPTION_MODAL_VISIBLE,
  SET_FREE_DEPOSIT_MODAL_VISIBLE,
  QUERY_OSD_MODIFY_ORDER_NOTE,
  SET_OSD_MODIFY_ORDER_NOTE,
  SET_LOCALCONTACT_DATA,
  SET_RECEIVED_BOOKING_COUPON,
} from './Types';

export const changeFormData = (data: Array<DriverItem>) => ({
  type: CHANGE_FORM,
  data,
});

export const changeLocalContactsData = data => ({
  type: SET_LOCALCONTACT_DATA,
  data,
});

export const clear = () => ({
  type: CLEAR,
});

export const setPassengerError = (isError: boolean) => ({
  type: SET_PASSENGER_ERROR,
  data: isError,
});

export const createOrder = (data?: any) => ({
  type: CREATEORDER,
  data,
});

export const setUniqueOrder = data => ({
  type: UNIQUEORDER,
  data,
});

export const setBaseResInfo = data => ({
  type: ERRORINFO,
  data,
});

export const setNameReverse = data => ({
  type: NAMEREVERSE,
  data,
});

export const setOrderInfo = data => ({
  type: CREATEORDERSUCCESS,
  data,
});

export const createOrderFail = () => ({
  type: CREATEFAIL,
});

export const resetLoading = (data: boolean, option?: any) => ({
  type: LOADING,
  data,
  option,
});

export const saveSnapshot = data => ({
  type: SNAPSHOT,
  data,
});

export const changePayMode = data => ({
  type: PAYMODE,
  data,
});

export const changeCoupon = data => ({
  type: CHANGE_COUPON,
  data,
});

export const changeModalStatus = data => ({
  type: CHANGE_MODALE,
  data,
});

export const setPreLicensing = data => ({
  type: SET_PRELICENDING,
  data,
});

export const setSnapshotFinish = data => ({
  type: SET_SNAPSHOT_FINISH,
  data,
});

export const setPriceChangePopIsShow = data => ({
  type: SET_PRICECHANGE_POP_VISIBLE,
  data,
});

export const setInsRemindPopIsShow = data => ({
  type: SET_INSREMIND_POP_VISIBLE,
  data,
});

export const setEasyLifePopIsShow = (visible: boolean) => ({
  type: SET_EASYLIFE_POP_VISIBLE,
  visible,
});

export const setCreateInsLoadingPopIsShow = data => ({
  type: SET_CREATEINS_LOADING_POP_VISIBLE,
  data,
});

export const closeInsuranceLoadingView = data => ({
  type: CLOSE_INSURANCE_LOADING_VIEW,
  data,
});

export const setCreateInsFailPopIsShow = data => ({
  type: SET_CREATEINS_FAIL_POP_VISIBLE,
  data,
});

export const insConfirmCallBack = data => ({
  type: INSCONFIRM_CALLBACK,
  data,
});

export const setEhiFreeDepositModalVisible = data => ({
  type: SET_EHI_FREEDEPOSIT_MODAL_VISIBLE,
  data,
});

export const setFreeDepositModalVisible = data => ({
  type: SET_FREE_DEPOSIT_MODAL_VISIBLE,
  data,
});

export const setSelectedLoanPayStageCount = data => ({
  type: SET_SELECTED_LOAN_PAY_STAGE_COUNT,
  data,
});

export const setRebookPenalty = data => ({
  type: SET_REBOOK_PENALTY,
  data,
});

export const resetCancelFeeRebook = () => ({
  type: RESET_CANCELFEE_REBOOK,
});

export const fetchQueryCancelFeeRebook = data => ({
  type: FETCH_CANCELFEE_REBOOK,
  data,
});

export const fetchQueryCancelFeeRebookCallBack = data => ({
  type: FETCH_CANCELFEE_REBOOK_CALLBACK,
  data,
});

export const setLogInfo = data => ({
  type: SET_LOG_INFO,
  data,
});

export const setVendorPriceData = data => ({
  type: SET_VENDOR_PRICE_DATA,
  data,
});

export const setStrongSubmitInfoReqParams = data => ({
  type: SET_STRONG_SUBMIT_INFO_REQ_PARAMS,
  data,
});

export const validateFlightNo = data => ({
  type: VALIDATE_FLIGHT_NO,
  data,
});

export const setCheckFlightNoLoading = data => ({
  type: SET_CHECK_FLIGHT_NO_LOADING,
  data,
});

export const setFlightErrorTip = (data: string) => ({
  type: SET_FLIGHT_ERROR_TIP,
  data,
});

export const setDepositRateDescriptionModalVisible = (data: boolean) => ({
  type: SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
  data,
});

export const setFlightDelayRulesModalVisible = (data: boolean) => ({
  type: SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE,
  data,
});

export const clearWithoutVendorInfo = () => ({
  type: CLEAR_WITHOUT_VENDOR_INFO,
});

export const refresh = () => ({
  type: REFRESH,
});

export const setBusinessLicenseVisible = data => ({
  type: SET_BUSINESS_LICENSE_VISIBLE,
  data,
});

export const setFuelDescriptionModalVisible = (data: boolean) => ({
  type: SET_FUEL_DESCRIPTION_MODAL_VISIBLE,
  data,
});

export const queryOsdModifyOrderNote = () => ({
  type: QUERY_OSD_MODIFY_ORDER_NOTE,
});

export const setOsdModifyOrderNote = data => ({
  type: SET_OSD_MODIFY_ORDER_NOTE,
  data,
});

export const setHasReceivedCouponAtBooking = data => ({
  type: SET_RECEIVED_BOOKING_COUPON,
  data,
});
