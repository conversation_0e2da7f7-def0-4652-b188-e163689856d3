import { IInputType } from '../../ComponentBusiness/BookForm/src/Type';

export const CHANGE_FORM = 'Booking/CHANGE_FORM';
export const CLEAR = 'Booking/CLEAR';
export const CLEAR_WITHOUT_VENDOR_INFO = 'Booking/CLEAR_WITHOUT_VENDOR_INFO';
export const CREATEORDER = 'Booking/CREATEORDER';
export const CREATEFAIL = 'Booking/CREATEFAIL';
export const LOADING = 'Booking/LOADING';
export const SNAPSHOT = 'Booking/SNAPSHOT';
export const PAYMODE = 'Booking/PAYMODE';
export const CHANGE_COUPON = 'Booking/CHANGE_COUPON';
export const UNIQUEORDER = 'Booking/UNIQUEORDER';
export const FLIGHTNO = 'Booking/FLIGHTNO';
export const CREATEORDERSUCCESS = 'Booking/CREATEORDERSUCCESS';
export const NAMEREVERSE = 'Booking/NAMEREVERSE';
export const CREATEORDERFAIL = 'Booking/CREATEORDERFAIL';
export const CHANGE_MODALE = 'Booking/CHANGE_MODALE';
export const ERRORINFO = 'Booking/ERRORINFO';
export const SET_PRELICENDING = 'Booking/SET_PRELICENDING';
export const SET_SNAPSHOT_FINISH = 'Booking/SET_SNAPSHOT_FINISH';
export const SET_REBOOK_PENALTY = 'Booking/SET_REBOOK_PENALTY';
export const RESET_CANCELFEE_REBOOK = 'Booking/RESET_CANCELFEE_REBOOK';
export const FETCH_CANCELFEE_REBOOK = 'Booking/FETCH_CANCELFEE_REBOOK';
export const FETCH_CANCELFEE_REBOOK_CALLBACK =
  'Booking/FETCH_CANCELFEE_REBOOK_CALLBACK';
export const SET_PRICECHANGE_POP_VISIBLE =
  'Booking/SET_PRICECHANGE_POP_VISIBLE';
export const SET_INSREMIND_POP_VISIBLE = 'Booking/SET_INSREMIND_POP_VISIBLE';
export const SET_CREATEINS_LOADING_POP_VISIBLE =
  'Booking/SET_CREATE_INSLOADING_POP_VISIBLE';
export const CLOSE_INSURANCE_LOADING_VIEW =
  'Booking/CLOSE_INSURANCE_LOADING_VIEW';
export const SET_CREATEINS_FAIL_POP_VISIBLE =
  'Booking/SET_CREATEINS_FAIL_POP_VISIBLE';
export const INSCONFIRM_CALLBACK = 'Booking/INSCONFIRM_CALLBACK';
export const SET_EHI_FREEDEPOSIT_MODAL_VISIBLE =
  'Booking/SET_EHI_FREEDEPOSIT_MODAL_VISIBLE';
export const SET_FREE_DEPOSIT_MODAL_VISIBLE =
  'Booking/SET_FREE_DEPOSIT_MODAL_VISIBLE';
export const SET_IOUS_INFO = 'Booking/SET_IOUS_INFO';
export const SET_SELECTED_LOAN_PAY_STAGE_COUNT =
  'Booking/SET_SELECTED_LOAN_PAY_STAGE_COUNT';
export const SET_PASSENGER_ERROR = 'Booking/SET_PASSENGER_ERROR';
export const SET_EASYLIFE_POP_VISIBLE = 'Booking/SET_EASYLIFE_POP_VISIBLE';
export const SET_LOG_INFO = 'Booking/SET_LOG_INFO';
export const SET_VENDOR_PRICE_DATA = 'Booking/SET_VENDOR_PRICE_DATA';
export const SET_STRONG_SUBMIT_INFO_REQ_PARAMS =
  'Booking/SET_STRONG_SUBMIT_INFO_REQ_PARAMS';
export const VALIDATE_FLIGHT_NO = 'Booking/VALIDATE_FLIGHT_NO';
export const REFRESH = 'Booking/REFRESH';

export const SET_CHECK_FLIGHT_NO_LOADING =
  'Booking/SET_CHECK_FLIGHT_NO_LOADING';

export const SET_FLIGHT_ERROR_TIP = 'Booking/SET_FLIGHT_ERROR_TIP';

export const SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE =
  'Booking/SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE';

export const SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE =
  'Booking/SET_FLIGHT_DELAY_RULES_MODAL_VISIBLE';

export const SET_BUSINESS_LICENSE_VISIBLE =
  'Booking/SET_BUSINESS_LICENSE_VISIBLE';

export const QUERY_OSD_MODIFY_ORDER_NOTE =
  'Booking/QUERY_OSD_MODIFY_ORDER_NOTE';

export const SET_OSD_MODIFY_ORDER_NOTE = 'Booking/SET_OSD_MODIFY_ORDER_NOTE';
export const SET_LOCALCONTACT_DATA = 'Booking/SET_LOCALCONTACT_DATA';

export const SET_FUEL_DESCRIPTION_MODAL_VISIBLE =
  'Booking/SET_FUEL_DESCRIPTION_MODAL_VISIBLE';

export const SET_RECEIVED_BOOKING_COUPON =
  'BookingISD/SET_RECEIVED_BOOKING_COUPON';
export const OSD_SEQUENCE = [
  IInputType.firstName,
  IInputType.lastName,
  IInputType.mobilePhone,
  IInputType.email,
  IInputType.wechat,
  IInputType.localContacts,
  IInputType.flightNumber,
  IInputType.driverLicense,
  IInputType.driverLicenseName,
];
export const OSD_NORMAL_SEQUENCE = [
  IInputType.firstName,
  IInputType.lastName,
  IInputType.mobilePhone,
  IInputType.email,
  IInputType.flightNumber,
];
export const OSD_LICENSE_APPROVE_SEQUENCE = [
  IInputType.firstName,
  IInputType.lastName,
  IInputType.mobilePhone,
  IInputType.email,
  IInputType.flightNumber,
  IInputType.driverLicense,
  IInputType.driverLicenseName,
];
export const ISD_SEQUENCE = [IInputType.mobilePhone, IInputType.flightNumber];
export const IBU_SEQUENCE = [
  IInputType.firstName,
  IInputType.lastName,
  IInputType.mobilePhone,
  IInputType.email,
  IInputType.flightNumber,
];
