import {
  FETCH_QUERY_SERVICEPROGRESS_CALLBACK,
  URGE_SERVICEPROGRESS_CALLBACK,
  SERVICE_CROSS_PARAMS,
} from './Types';

export const getInitalState = () => ({
  serviceProgressList: [],
  serviceStatus: '0',
  serviceTitle: '',
  serviceDesc: '',
  serviceLatestTime: '',
  urgeServiceIds: [],
  serviceIds: '',
  serviceCardHistory: false,
});

const initalState = getInitalState();

export const getServiceIdsStr = (serviceProgressDTO = []) => {
  const serviceIds = serviceProgressDTO.map(item => item?.serviceId);
  return serviceIds.join(',');
};

export default (state = initalState, action) => {
  const { data } = action || {};
  const {
    serviceProgressDTO = [],
    status,
    title,
    desc,
    latestTime,
    urgeServiceIds,
    attrDto,
  } = data || {};
  const serviceIds = getServiceIdsStr(serviceProgressDTO);
  switch (action.type) {
    case FETCH_QUERY_SERVICEPROGRESS_CALLBACK:
      return {
        ...state,
        serviceProgressList: serviceProgressDTO,
        serviceStatus: status,
        serviceTitle: title,
        serviceDesc: desc,
        serviceLatestTime: latestTime,
        urgeServiceIds,
        serviceIds,
        serviceCardHistory: attrDto?.history,
      };
    case URGE_SERVICEPROGRESS_CALLBACK:
      return {
        ...state,
        urgeServiceIds,
      };
    case SERVICE_CROSS_PARAMS:
      return {
        ...state,
        ...action.data,
      };
    default:
      return state;
  }
};
