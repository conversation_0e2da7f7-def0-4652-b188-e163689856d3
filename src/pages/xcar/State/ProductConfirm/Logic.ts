import { takeLatest, put, select } from 'redux-saga/effects';
import { cloneDeep, uuid } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { xUtil } from '@ctrip/xtaro';
import * as Types from './Types';
import {
  CarFetch,
  CarFetchHelper,
  CarLog,
  Utils,
  CarServerABTesting,
} from '../../Util/Index';
import {
  setLoading,
  setFail,
  setRequestAndResponse,
  setReviewLoading,
  setReviewFail,
  setReviewRequestAndResponse,
  queryCommentSummary as queryCommentAction,
  queryVirtualNumber as queryVirtualNumberAction,
  setDepositLoading,
  setDepositFail,
  setDepositInfo,
  queryDepositInfo as queryDepositInfoAction,
  clear,
  setVirtualNumberLoading,
  setVirtualNumberInfo,
  setIconVisible,
  setDepositSaleOut,
} from './Actions';
import { queryPolicy } from '../Policy/Actions';
import { setSupplierData } from '../SupplierData/Actions';
import {
  getProductConfirmRequest,
  getRequestParams,
  getReviewRequestParams,
  getProductConfirmResponse,
  getFail,
} from './Selectors';
import {
  getPickUpStoreId,
  getExtendedInfo,
  getVehicleInfo,
  getOrderDetailIsISDShelves2B,
  getVendorInfo,
} from '../OrderDetail/Selectors';
import { GetCommentSummaryRequestType } from '../../Types/Dto/GetCommentSummaryRequestType';
import {
  getFloorId,
  getPackageCode,
  getUniqueCode,
  getUniqueReference,
} from '../VendorList/Selectors';
import {
  BindStatusTypeEnum,
  IconShowTypeEnum,
} from '../../Types/Dto/QueryStoreVNumberResponseType';
import Texts from '../Product/Texts';
import { verifyPointInfo } from '../List/Method';
import { LogKeyDev } from '../../Constants/Index';
import { getPickUpCityId } from '../LocationAndDate/Selectors';

export function* queryVehicleDetailInfo() {
  yield takeLatest(Types.QUERY, function* logic(action: any) {
    yield put(clear());
    const { isOrderDetail, requestParams, reviewRequestParams, reference } =
      action?.data || {};
    const state = yield select();
    let request = requestParams || getRequestParams(state);
    if (!request?.reference) {
      request.reference = reference;
    }
    try {
      const parentRequestId = uuid();
      const verifyRequestParameters = () => {
        const { pickupPointInfo, returnPointInfo } = request || {};
        return (
          verifyPointInfo(pickupPointInfo) && verifyPointInfo(returnPointInfo)
        );
      };
      const isISDShelves2B = isOrderDetail
        ? getOrderDetailIsISDShelves2B(state)
        : CarServerABTesting.isISDShelves2B();
      const isFail = getFail(state);
      const randomStr = (isFail && uuid()) || ''; // 如果接口失败则刷新缓存
      const cacheKey = `queryVehicleDetailInfo_${xUtil.hashCode(JSON.stringify(request))}_${randomStr}`;
      request.parentRequestId = parentRequestId;
      request = CarFetchHelper.parameterBuilder({
        param: request,
        verifyRequestParameters,
        cachePolicy: isISDShelves2B && {
          enableCache: true,
          cacheExpireTime: 5 * 60,
          cacheKey,
        },
      });
      yield put(setLoading(true));
      // 弹窗查询门店政策, 订单详情页不需要查询，已有接口查询
      if (!isOrderDetail) {
        yield put(queryPolicy());
      }
      yield put(queryVirtualNumberAction());
      yield put(queryDepositInfoAction({ parentRequestId, requestParams }));
      yield put(
        queryCommentAction({
          isOrderDetail,
          reviewRequestParams,
        }),
      );
      const response = yield CarFetch.queryVehicleDetailInfo(request, {
        latest: false,
      });
      // 售前读取营业执照相关信息
      if (!isOrderDetail) {
        yield put(
          setSupplierData({
            licenseDesc: Texts.licenseDesc,
            companyName: response?.vendorInfo?.vendorFullName,
            licenseImgUrl: response?.vendorInfo?.vendorLicenseUrl,
            vendorId: response?.vendorInfo?.vendorCode,
          }),
        );
      }
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_dev_prodcut_confirm,
        info: {
          eventResult: response?.baseResponse?.isSuccess,
          expMsg: response?.baseResponse?.returnMsg,
          expCode:
            response?.baseResponse?.code ||
            Utils.getFrontEndExpCode(response, null),
        },
      });
      yield put(setLoading(false));
      yield put(setFail(!response?.baseResponse?.isSuccess));
      yield put(setRequestAndResponse({ request, response }));
    } catch (e) {
      yield put(setLoading(false));
      yield put(setFail(true));
      yield put(setRequestAndResponse({ request, response: null }));
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_dev_prodcut_confirm,
        info: {
          eventResult: false,
          error: e,
          expMsg: e?.message,
          expCode: Utils.getFrontEndExpCode(null, e),
        },
      });
    }
  });
}

export function* queryDepositInfo() {
  yield takeLatest(Types.QUERY_DEPOSIT_INFO, function* logic(action: any) {
    const state = yield select();
    const data: Types.QueryDepositInfoActionType = action?.data;
    const baseRequest = data?.requestParams || getRequestParams(state);
    const parentRequestId =
      data?.parentRequestId || getProductConfirmRequest(state)?.parentRequestId;
    const request = {
      ...baseRequest,
      withDepositInfo: true,
      parentRequestId,
    };
    try {
      yield put(setDepositLoading(true));
      const response = yield CarFetch.queryVehicleDetailInfo(request, {
        latest: false,
      });
      yield put(setDepositLoading(false));
      yield put(setDepositFail(!response?.depositInfo));
      yield put(setDepositInfo(response?.depositInfo));
      if (response?.baseResponse?.code === '40005') {
        yield put(setDepositSaleOut(true));
      }
    } catch (e) {
      yield put(setDepositLoading(false));
      yield put(setDepositFail(true));
      yield put(setDepositInfo(null));
    }
  });
}

export function* queryCommentSummary() {
  yield takeLatest(Types.QUERY_REVIEWS, function* logic(action: any) {
    const { isOrderDetail, reviewRequestParams } = action?.data || {};
    const state = yield select();
    const reviewRequest: GetCommentSummaryRequestType =
      reviewRequestParams || getReviewRequestParams(state);
    const request = cloneDeep(reviewRequest);
    request.invokeFrom = isOrderDetail ? 4 : 3; // 增加售后售前调用区分
    const isISDShelves2B = isOrderDetail
      ? getOrderDetailIsISDShelves2B(state)
      : CarServerABTesting.isISDShelves2B();
    const vendorInfo = getVendorInfo(state); // 订详供应商信息
    const { isSelf } = vendorInfo || {};
    if (isISDShelves2B) {
      request.pickCityId = getPickUpCityId(state);
      request.isProductDetail = true;
      request.self = isOrderDetail ? isSelf : request.self;
      request.appRequestMap = {
        cachePolicy: { enableCache: true, cacheExpireTime: 5 * 60 },
      };
    }
    if (isOrderDetail) {
      const extendedInfo = getExtendedInfo(state);
      request.storeId = getPickUpStoreId(state);
      request.klbVersion = extendedInfo?.klbVersion;
      request.calabiVehicleId = getVehicleInfo(state)?.ctripVehicleID;
    }

    try {
      yield put(setReviewLoading(true));
      const res = yield CarFetch.getCommentSummary(request);
      const {
        content,
        userAlbum,
        userAlbumUrl,
        userAlbumTotalCount,
        selfComment,
      } = res;
      const response = JSON.parse(content);
      yield put(
        setReviewRequestAndResponse({
          request,
          response,
          userAlbum,
          userAlbumUrl,
          userAlbumTotalCount,
          selfComment,
        }),
      );
      yield put(setReviewLoading(false));
      yield put(setReviewFail(false));
    } catch (e) {
      yield put(
        setReviewRequestAndResponse({
          request,
          response: null,
          userAlbum: null,
          userAlbumUrl: '',
          userAlbumTotalCount: 0,
          selfComment: null,
        }),
      );
      yield put(setReviewLoading(false));
      yield put(setReviewFail(true));
    }
  });
}

export function* queryVirtualNumber() {
  yield takeLatest(Types.QUERY_VIRTUAL_NUMBER, function* logic() {
    const state = yield select();
    const reference = getUniqueReference(state);
    const storeCode = reference?.pStoreCode;
    if (!reference) {
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_dev_prodcut_confirm,
        info: {
          eventResult: false,
          expCode: 'no vendorList reference',
        },
      });
    }
    yield put(setVirtualNumberLoading(true));
    const { response } = yield CarFetch.queryVirtualNumber(
      storeCode,
      reference,
    );
    yield put(setVirtualNumberLoading(false));
    yield put(setIconVisible(response?.iconShow === IconShowTypeEnum.yes));
    const info = {
      vNumber:
        (response?.bindStatus === BindStatusTypeEnum.success &&
          response?.vNumber) ||
        '',
      secAndPrivacyTips: response?.secAndPrivacyTips,
      nonBusinessHoursTips: response?.nonBusinessHoursTips,
      vendorVNumber: response?.vendorVNumber,
    };
    yield put(setVirtualNumberInfo(info));
  });
}

export default [
  queryVehicleDetailInfo(),
  queryVirtualNumber(),
  queryDepositInfo(),
  queryCommentSummary(),
];
