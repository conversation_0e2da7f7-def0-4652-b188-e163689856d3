@import '../../Common/src/Tokens/tokens/color.scss';

.wrap {
  flex-direction: row;
  align-items: center;
  z-index: 10;
  margin-top: 10px;
}
.mt30 {
  margin-bottom: 16px;
}
.pTime {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.small {
  font-size: 23px;
}
.durationWrap {
  background-color: $rentalDateDurationBg;
  margin-left: 10px;
  margin-right: 10px;
  padding-left: 8px;
  padding-right: 8px;
  padding-top: 1px;
  padding-bottom: 1px;
  border-radius: 4px;
  min-width: 56px;
  align-items: center;
  justify-content: center;
}
.duration {
  font-size: 20px;
  line-height: 28px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.duration24 {
  font-size: 24px;
  line-height: 32px;
}
.durantion34 {
  line-height: 34px;
}
.rTime {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.durationLineWrap {
  border: 1px solid rgba($C_0040AC, 0.192);
  border-radius: 18px;
  padding: 0 12px;
  margin: 0 8px;
}
.timePng {
  width: 28px;
  height: 28px;
  margin-right: 13px;
}
