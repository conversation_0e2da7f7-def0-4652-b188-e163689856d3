import React, { useState, useMemo, memo, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { throttle as lodashThrottle } from 'lodash-es';
import {
  XView as View,
  XImage as Image,
  XViewExposure,
  xClassNames,
  XLinearGradient as LinearGradient,
  xMergeStyles,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { layout, icon, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import CScrollView from '@c2x/components/CScrollView';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { getPixel } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { Collapse } from './Collapse';
import { TableBoxNew } from '../../ComponentBusiness/TableBox';
import {
  getPackagedLocationInfo,
  getVehicleTags,
  getProductConfirmResponse,
  getPickUpStoreSelfServiceInfo,
  getReturnStoreSelfServiceInfo,
  getImageList,
  getStoreAlbumPageParams,
  getSelfComment,
  getReviewFail,
  getReviewLoading,
} from '../../State/ProductConfirm/Selectors';
import { getNationalChainTag } from '../../State/List/VehicleListMappers';
import {
  getProductComfrimLocation,
  getCarLabelsInfo,
  getOrderDetailConfirmModalData,
  getVendorInfo,
  getNationalChainTag as getOrderDetailNationalChainTag,
  getCarServiceData,
} from '../../State/OrderDetail/Selectors';
import BbkImageList from '../../ComponentBusiness/ImageList';
import { CarLog } from '../../Util/Index';
import styles from './VehicleDetailIsdShelves2B.module.scss';
import MultiColumn from './MultiColumn';
import SelfServiceSlogan from '../../ComponentBusiness/SelfServiceSlogan';
import {
  getUniqueReference,
  getVehicleDetailModalLogInfo,
  getCurFloor,
} from '../../State/VendorList/Selectors';
import {
  getPickUpGuideInfo,
  getDropOffGuideInfo,
} from '../../State/Guide/Selectors';
import { GuideTabType } from '../../State/Product/Enums';
import {
  ILocationInfo,
  ILookMore,
  IPickUpGuide,
  IPickAndDropWay,
  IVehicleDetailNewBVer,
  ICancelInfo,
  IAssurance,
  IServiceProvider,
} from './Types';
import {
  IGuidInfoType,
  PromptInfosCodeEnum,
} from '../../Types/Dto/QueryVehicleDetailInfoResponseType';
import SelectedReviews from './SelectedReviews';
import { ImagesNew } from '../../ComponentBusiness/ProductConfirmModal/components/VehicleDetailNew';
import {
  queryMultimediaAlbum,
  setBusinessLicenseVisible as setVendorListBusinessLicenseVisible,
} from '../../State/VendorList/Actions';
import { getSupplierData } from '../../State/SupplierData/Selector';
import { setBusinessLicenseVisible as setBookingBusinessLicenseVisible } from '../../State/Booking/Actions';
import { setOrderModalsVisible } from '../../State/OrderDetail/Actions';
import { ImageUrl } from '../../Constants/Index';
import { LayoutPartEnum } from '../../ComponentBusiness/ProductConfirmModal/Type';
import { IInsuranceCode } from '../../Types/Dto/QueryProductInfoType';

const noop = () => {};
const useGetProductConfirmResponse = isOrderDetail => {
  const orderDetailConfirmResponse = useSelector(
    getOrderDetailConfirmModalData,
  );
  const productConfirmResponse = useSelector(getProductConfirmResponse);
  return isOrderDetail ? orderDetailConfirmResponse : productConfirmResponse;
};

function useGetIsSelect(isOrderDetail) {
  const response = useSelector(getProductConfirmResponse); // 售前取还车信息
  const vendorInfo = useSelector(getVendorInfo); // 订详供应商信息
  const { isSelf } = vendorInfo || {};
  return isOrderDetail ? isSelf : response?.isSelected;
}

const { useMemoizedFn, isIos } = BbkUtils;

const LocationInfo = memo(
  ({
    isSelfService,
    locationInfo,
    storeGuidInfo,
    isOrderDetail,
  }: ILocationInfo) => {
    return (
      <View>
        <View className={styles.pickupWay}>
          {isSelfService && (
            <SelfServiceSlogan
              text="快捷取车·无需排队"
              style={{ marginBottom: 7 }}
            />
          )}
          <Text className={styles.pickupWayText}>
            {isOrderDetail ? locationInfo.way : storeGuidInfo?.storeGuid}
          </Text>
        </View>
        <Text className={styles.pickupDropAdress}>{locationInfo?.address}</Text>
      </View>
    );
  },
);

const LookMore: React.FC<ILookMore> = ({ onPressLocation }) => {
  const onPressLookMore = useMemoizedFn(() => {
    onPressLocation(GuideTabType.Pickup);
  });
  return (
    <BbkTouchable className={styles.showMoreImage} onPress={onPressLookMore}>
      <Image
        src={`${ImageUrl.DIMG04_PATH}1tg4712000cj49w9h5802.png`}
        className={styles.moreIcon}
      />

      <View className={styles.showMoreTextWrapper}>
        <Text className={styles.showMoreImageTxt}>详情</Text>
      </View>
    </BbkTouchable>
  );
};

const PickUpGuide: React.FC<IPickUpGuide> = ({
  onPressLocation,
  outShowGuideImgList,
  wholeImgList,
}) => {
  const vehicleDetailModalLogInfo = useSelector(getVehicleDetailModalLogInfo);
  const onPressImg = useMemoizedFn(() => {
    CarLog.LogCode({
      name: '点击_产品详情页_门店弹层_取还车指引图片',
      info: vehicleDetailModalLogInfo,
    });
  });
  if (!outShowGuideImgList?.length) return null;
  return (
    <XViewExposure
      className={styles.imgScrollWrap}
      testID={CarLog.LogExposure({
        name: '曝光_产品详情页_门店弹层_取还车指引图片',
        info: vehicleDetailModalLogInfo,
      })}
    >
      <CScrollView scrollEventThrottle={10} horizontal={true}>
        <BbkImageList
          listWrapStyle={layout.flexRow}
          imgList={outShowGuideImgList}
          imgStyle={{ width: 200, height: 133, borderRadius: 4 }}
          cornerText="如何到达"
          onPress={onPressImg}
          wholeImgList={wholeImgList}
        />
        <LookMore onPressLocation={onPressLocation} />
      </CScrollView>
    </XViewExposure>
  );
};

const PickAndDropWay = memo(
  ({
    onPressLocation,
    isOrderDetail,
    onLayoutPickupAndDrop,
  }: IPickAndDropWay) => {
    const [wrapHeight, setWrapHeight] = useState(0);
    const response = useGetProductConfirmResponse(isOrderDetail);
    const storeGuidInfos = response?.storeGuidInfos;
    const [isLayoutDone, setIsLayoutDone] = useState(false);
    const pickUpStoreSelfServiceInfo = useSelector(
      getPickUpStoreSelfServiceInfo,
    );
    const returnStoreSelfServiceInfo = useSelector(
      getReturnStoreSelfServiceInfo,
    );
    const productLocation = useSelector(getPackagedLocationInfo); // 售前取还车信息
    const vehicleDetailModalLogInfo = useSelector(getVehicleDetailModalLogInfo); // 售前取还车信息

    const pickUpGuideInfo = useSelector(getPickUpGuideInfo);
    const dropOffGuideInfo = useSelector(getDropOffGuideInfo);
    const guideImgList = useMemo(() => {
      return (
        pickUpGuideInfo?.guideStep?.map?.((item, index) => {
          return {
            imageUrl: item?.image,
            imageDescription: item?.content?.replace?.(/\s+/g, ''),
            imageIndex: index,
          };
        }) || []
      );
    }, [pickUpGuideInfo?.guideStep]);
    const outShowGuideImgList =
      guideImgList?.length > 5 ? guideImgList?.slice(0, 5) : guideImgList;
    const hasPickUpGuide = !!outShowGuideImgList?.length;
    const orderDetailLocation = useSelector(getProductComfrimLocation); // 售后取还车信息
    const location = productLocation.pickup
      ? productLocation
      : orderDetailLocation;
    const pickUpInfo = location?.pickup;
    const dropInfo = location?.return;

    const pickupStoreGuidInfo = storeGuidInfos?.find?.(
      item => item.type === IGuidInfoType.PickUp,
    );
    const dropoffStoreGuidInfo = storeGuidInfos?.find?.(
      item => item.type === IGuidInfoType.DropOff,
    );
    const needMerge = isOrderDetail
      ? pickUpInfo?.pickupServiceType === dropInfo?.dropoffServiceType &&
        pickUpInfo?.address === dropInfo?.address
      : pickUpGuideInfo?.wayInfo === dropOffGuideInfo?.wayInfo &&
        pickUpInfo?.address === dropInfo?.address; // 取还车信息是否需要合并
    const onPressPickupAndDropIcon = useMemoizedFn(() => {
      CarLog.LogCode({
        name: '点击_产品详情页_门店弹层_取还车指引',
        info: {
          pickWayInfo: pickUpInfo?.way,
          returnWayInfo: dropInfo?.way,
          ...vehicleDetailModalLogInfo,
        },
      });
      onPressLocation(GuideTabType.Pickup);
    });
    const wrapOnLayout = lodashThrottle(
      event => {
        setWrapHeight(event.nativeEvent.layout.height - 10);
        setIsLayoutDone(true);
      },
      300,
      { trailing: true },
    );
    return (
      <View
        onLayout={e => {
          onLayoutPickupAndDrop(e, LayoutPartEnum.PickupAndDrop);
        }}
        className={styles.blockSection}
      >
        <View className={styles.blockLeft}>
          <View className={styles.blockTitleIconWrap}>
            <Image
              className={styles.blockTitleIcon}
              src={`${ImageUrl.DIMG04_PATH}1tg2412000j6vy0tu4005.png`}
            />
          </View>
          {!needMerge && (
            <View
              style={{
                opacity: isLayoutDone ? 1 : 0,
              }}
              className={styles.pickAndDropWayInnerLeft}
            >
              <View className={styles.lineWrap}>
                <View className={styles.pickDropWayLabelWrap}>
                  <Text className={styles.pickDropWayLabel} fontWeight="medium">
                    取
                  </Text>
                </View>
                <View
                  className={styles.verticalLine}
                  style={{ height: wrapHeight }}
                />
              </View>
              <View className={styles.pickDropWayLabelWrap}>
                <Text className={styles.pickDropWayLabel} fontWeight="medium">
                  还
                </Text>
              </View>
            </View>
          )}
        </View>
        <View className={styles.cancelBlockRight}>
          <Text className={styles.blockTitle} fontWeight="medium">
            取还方式
          </Text>
          <View className={styles.pickAndDropWayInner}>
            <View className={styles.pickAndDropWayInnerRight}>
              <View onLayout={wrapOnLayout}>
                <LocationInfo
                  isSelfService={pickUpStoreSelfServiceInfo?.isSelfService}
                  locationInfo={pickUpInfo}
                  storeGuidInfo={pickupStoreGuidInfo}
                  isOrderDetail={isOrderDetail}
                />
                {hasPickUpGuide && (
                  <PickUpGuide
                    outShowGuideImgList={outShowGuideImgList}
                    wholeImgList={guideImgList}
                    onPressLocation={onPressLocation}
                  />
                )}
              </View>
              {!needMerge && (
                <View className={styles.dropWayWrap}>
                  <LocationInfo
                    isSelfService={returnStoreSelfServiceInfo?.isSelfService}
                    locationInfo={dropInfo}
                    storeGuidInfo={dropoffStoreGuidInfo}
                    isOrderDetail={isOrderDetail}
                  />
                </View>
              )}
            </View>
            <Image
              className={
                hasPickUpGuide || needMerge
                  ? styles.guideIconBlockBackgroundImage
                  : styles.noGuideStepGuideIconBlockBackgroundImage
              }
              mode="aspectFit"
              src={`${ImageUrl.DIMG04_PATH}1tg2812000j7xrmx3264F.png`}
            />
            <BbkTouchable
              className={
                hasPickUpGuide
                  ? styles.guideIconBlockBackgroundImageBtn
                  : styles.noGuideStepGuideIconBlockBackgroundImageBtn
              }
              onPress={onPressPickupAndDropIcon}
            />
          </View>
        </View>
      </View>
    );
  },
);

const CancelInfo = memo(
  ({ isOrderDetail, onLayoutCancelInfo }: ICancelInfo) => {
    const response = useGetProductConfirmResponse(isOrderDetail);
    const cancelRuleInfo = response?.cancelRuleInfo;
    return (
      <View
        onLayout={e => {
          onLayoutCancelInfo(e, LayoutPartEnum.CancelInfo);
        }}
      >
        <MultiColumn
          title="取消政策"
          blockIcon={`${ImageUrl.DIMG04_PATH}1tg6j12000j6vxozw9562.png`}
        >
          <Collapse
            expand={!!cancelRuleInfo?.cancelType}
            title={cancelRuleInfo?.subTitle}
            // eslint-disable-next-line react-native/no-color-literals
            titleStyle={{
              fontSize: getPixel(26),
              lineHeight: 18,
              fontWeight: 'normal',
              color: color.C_333333,
            }}
            headStyle={{
              paddingTop: 6,
              paddingBottom: 0,
              justifyContent: 'flex-start',
            }}
            // eslint-disable-next-line react-native/no-color-literals
            iconStyle={{
              color: color.C_000,
              fontSize: 12,
              marginTop: 6,
            }}
          >
            {!!cancelRuleInfo && (
              <XViewExposure
                className={styles.cancelRuleInfoWrap}
                testID={CarLog.LogExposure({
                  name: '曝光_门店弹层_取消政策',
                })}
              >
                {Object.values(cancelRuleInfo).length > 0 && (
                  <TableBoxNew
                    style={{ marginTop: 17, left: -10 }}
                    itemTitleStyle={styles.itemTitleStyle}
                    itemDescriptionStyle={styles.itemTitleStyle}
                    title={cancelRuleInfo?.tableTitle?.split('|')}
                    items={cancelRuleInfo.items}
                    isISDShelves2B={true}
                  />
                )}
                {!!cancelRuleInfo?.notices && (
                  <Text className={styles.cancelRuleNotice}>
                    {cancelRuleInfo?.notices}
                  </Text>
                )}
              </XViewExposure>
            )}
          </Collapse>
        </MultiColumn>
      </View>
    );
  },
);

const Services = memo(() => {
  const productVehicleTags = useSelector(getVehicleTags); // 售前取还车信息
  const orderVehicleTags = useSelector(getCarLabelsInfo); // 售后标签
  const vehicleTags =
    productVehicleTags?.length > 0 ? productVehicleTags : orderVehicleTags;
  if (!vehicleTags?.length) return null;
  return (
    <MultiColumn
      title="服务与配置"
      blockIcon={`${ImageUrl.DIMG04_PATH}1tg2612000j6vxnt769CF.png`}
    >
      <View className={xClassNames(styles.lablesWrap, styles.negativeML8)}>
        {vehicleTags?.map?.(item => {
          return (
            <View className={styles.lablesItem}>
              <Text className={styles.lablesItemText}>{item.title}</Text>
            </View>
          );
        })}
      </View>
    </MultiColumn>
  );
});

const Assurance = ({
  isOrderDetail,
  showCarServiceDetail,
  showCarServiceDetailBooking,
  pageName,
  onPressCarServiceDetail,
}: IAssurance) => {
  const response = useGetProductConfirmResponse(isOrderDetail);
  const reference = useSelector(getUniqueReference);
  const curFloor = useSelector(getCurFloor);
  const insurance = response?.insuranceIntroduction;
  const allPackageLevel = curFloor?.packageList?.map(
    item => item?.reference?.packageLevel,
  );
  const carServiceData = useSelector(getCarServiceData);
  const { rentalGuaranteeV2 } = carServiceData || {};
  const { packageDetailList, purchasingNotice, purchased } =
    rentalGuaranteeV2 || {};
  const handleAssuranceClick = useCallback(() => {
    if (pageName === 'Booking') {
      showCarServiceDetailBooking(IInsuranceCode[reference?.packageLevel]);
      return;
    }
    if (pageName === 'OrderDetail') {
      onPressCarServiceDetail({
        data: packageDetailList,
        purchasingNotice,
      });
      return;
    }
    showCarServiceDetail(
      reference,
      IInsuranceCode[reference?.packageLevel],
      allPackageLevel,
    );
  }, [
    showCarServiceDetailBooking,
    showCarServiceDetail,
    reference,
    allPackageLevel,
    pageName,
    onPressCarServiceDetail,
    packageDetailList,
    purchasingNotice,
  ]);
  return (
    <BbkTouchable onClick={handleAssuranceClick}>
      <MultiColumn
        title={insurance?.insuranceTitle || '基础保障'}
        blockIcon={`${ImageUrl.DIMG04_PATH}1tg5s12000j6vxcno95FC.png`}
      >
        <View className={styles.serviceWrap}>
          {insurance?.insuranceDescList?.map?.((item, idx) => {
            return (
              <View className={styles.serviceItemWrap} key={item}>
                <View className={styles.serviceItem}>
                  <Text className={styles.serviceItemText}>{item}</Text>
                </View>
                {idx !== insurance.insuranceDescList.length - 1 && (
                  <View className={styles.serviceItemSplit} />
                )}
              </View>
            );
          })}
        </View>
        <Text className={styles.assArrowRight} type="icon">
          {icon.arrowRight}
        </Text>
      </MultiColumn>
    </BbkTouchable>
  );
};

const ServiceProvider = memo(
  ({ isOrderDetail, pageName }: IServiceProvider) => {
    const response = useGetProductConfirmResponse(isOrderDetail);
    const hasSupplierData = !!useSelector(getSupplierData)?.licenseImgUrl;
    const dispatch = useDispatch();
    const vendorName = response?.vendorInfo?.vendorName;
    const nationalChainTag = getNationalChainTag(response?.vehicleTags);
    const orderDetailNationalChainTitle = useSelector(
      getOrderDetailNationalChainTag,
    )?.title;
    const isNationalChain = isOrderDetail
      ? orderDetailNationalChainTitle
      : nationalChainTag;
    const handelBusinessLicense = useCallback(() => {
      const setBusinessLicenseVisible = data => {
        if (pageName === 'Booking') {
          dispatch(setBookingBusinessLicenseVisible(data));
          return;
        }
        dispatch(
          isOrderDetail
            ? setOrderModalsVisible({
                businessLicenseModal: {
                  visible: data,
                },
              })
            : setVendorListBusinessLicenseVisible(data),
        );
      };
      setBusinessLicenseVisible(true);
    }, [dispatch, isOrderDetail, pageName]);
    return (
      <MultiColumn
        title="服务提供方"
        blockIcon={`${ImageUrl.DIMG04_PATH}1tg5w12000j6vxi1a3578.png`}
      >
        <BbkTouchable
          className={styles.busLicenseWrap}
          debounce={true}
          onPress={hasSupplierData ? handelBusinessLicense : noop}
        >
          <View
            className={xClassNames(
              styles.lablesWrap,
              !!isNationalChain && styles.negativeML8,
            )}
          >
            {!!isNationalChain && (
              <View className={styles.nationalChainTag}>
                <Text className={styles.nationalChainTagText}>全国连锁</Text>
              </View>
            )}
            <Text className={styles.vendorName}>{vendorName}</Text>
            <View>
              <Image
                className={xClassNames(
                  styles.blockTitleIcon,
                  !isIos && styles.negaMb2,
                )}
                src={`${ImageUrl.DIMG04_PATH}1tg1q12000j6vxmgfFD8C.png`}
              />
            </View>
          </View>
          {hasSupplierData && (
            <Text className={styles.arrowRight} type="icon">
              {icon.arrowRight}
            </Text>
          )}
        </BbkTouchable>
      </MultiColumn>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.isOrderDetail === nextProps.isOrderDetail;
  },
);

type TCtripSelectedProps = {
  hasSelfComment: boolean;
  isReviewLoading: boolean;
  isReviewFail: boolean;
};

const CtripSelected = memo(
  ({ hasSelfComment, isReviewLoading, isReviewFail }: TCtripSelectedProps) => {
    const [visible, setVisible] = useState(true); // 乐观渲染，初始可见
    const selfComment = useSelector(getSelfComment);
    const CtripSelectedInfoConfig = useMemo(() => {
      return [
        {
          title: '用户评价',
          ctripSelectedItemSubtitle: '高',
          ctripSelectedItemContent: `超过${selfComment?.positiveCommentCount || '2万'}条满分好评`,
          ctripSelectedItemIcon: `${ImageUrl.DIMG04_PATH}1tg4p12000k8fu5s52DC2.png`,
        },
        {
          title: '门店服务',
          ctripSelectedItemSubtitle: '高',
          ctripSelectedItemContent: `超过${selfComment?.storeExceedPercentage || '95%'}的当地门店`,
          ctripSelectedItemIcon: `${ImageUrl.DIMG04_PATH}1tg3u12000k8fx5k09FF6.png`,
        },
      ];
    }, [selfComment]);
    useEffect(() => {
      if (!isReviewLoading) {
        if (isReviewFail || !hasSelfComment) {
          setVisible(false);
          return;
        }
      }
      setVisible(true);
    }, [isReviewLoading, isReviewFail, hasSelfComment]);
    if (!visible) return null;
    return (
      <LinearGradient
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.8 }}
        locations={[0, 1]}
        colors={[color.C_006FF6, color.C_0019C3]}
        className={styles.cSCard}
      >
        <View className={styles.cSLogoWrap}>
          <Image
            className={styles.cSLogo}
            src={`${ImageUrl.DIMG04_PATH}1tg2912000k8fwmb12219.png`}
          />
        </View>
        {CtripSelectedInfoConfig?.map?.(item => {
          return (
            <View className={styles.cSItemWrap}>
              <Image
                className={styles.cSItemIcon}
                src={item.ctripSelectedItemIcon}
              />
              <Text fontWeight="medium" className={styles.cSItemTitle}>
                {item.title}
              </Text>
              <Image
                className={styles.cSProgressBar}
                src={`${ImageUrl.DIMG04_PATH}1tg4b12000k8fu4807049.png`}
              />
              <Text fontWeight="medium" className={styles.cSItemSubtitle}>
                {item.ctripSelectedItemSubtitle}
              </Text>
              <Text fontWeight="400" className={styles.cSItemContent}>
                {item.ctripSelectedItemContent}
              </Text>
            </View>
          );
        })}
      </LinearGradient>
    );
  },
);

type TypeCarAgeInfo = {
  vehicleInfoLog: any;
  onPressMore: () => void;
  vehicleDetailModalTitle: [];
  isOrderDetail?: boolean;
};

const CarAgeInfo = memo(
  ({
    vehicleInfoLog,
    onPressMore,
    vehicleDetailModalTitle,
    isOrderDetail,
  }: TypeCarAgeInfo) => {
    const images = useSelector(getImageList);
    const storeAlbumPageParams = useSelector(getStoreAlbumPageParams);
    const response = useGetProductConfirmResponse(isOrderDetail);
    const carAgeDesc = response?.carAgeTitle?.desc;
    const dispatch = useDispatch();
    const queryAlbum = useCallback(
      data => {
        dispatch(queryMultimediaAlbum(data));
      },
      [queryMultimediaAlbum],
    );
    const vehicleDetailModalTitleFilted = vehicleDetailModalTitle?.filter?.(
      v => !!v,
    );
    if (!carAgeDesc) return null;
    return (
      <MultiColumn
        title=""
        blockIcon={`${ImageUrl.DIMG04_PATH}1tg2412000j6vy0tu4005.png`}
        header={
          <View style={xMergeStyles([layout.flex1, layout.flexRow])}>
            {vehicleDetailModalTitleFilted?.map?.((item, idx) => {
              return (
                <View style={layout.flexRow} key={item}>
                  <Text className={styles.title} fontWeight="medium">
                    {item}
                  </Text>
                  {idx !== vehicleDetailModalTitleFilted.length - 1 && (
                    <View className={styles.verticalLineGap} />
                  )}
                </View>
              );
            })}
          </View>
        }
        style={{ marginBottom: getPixel(4) }}
      >
        <Text className={styles.carAgeDescription}>{carAgeDesc}</Text>
        {images?.imageList?.length > 0 && (
          <ImagesNew
            imagesTotalNumber={images.imagesTotalNumber}
            albumName={images.albumName}
            imageList={images.imageList}
            video={images.video}
            videoCover={images.videoCover}
            vehicleInfoLog={vehicleInfoLog}
            onPressMore={onPressMore}
            queryMultimediaAlbum={queryAlbum}
            storeAlbumPageParams={storeAlbumPageParams}
            testID={CarLog.LogExposure({ name: '曝光_门店弹层_实拍图' })}
            isISDShelves2B={true}
          />
        )}
      </MultiColumn>
    );
  },
);

interface PromptInfosPropsType {
  promptInfos: any[];
  onLayout?: (e) => void;
  style?: any;
}

const PromptInfosNew: React.FC<PromptInfosPropsType> = ({
  promptInfos,
  onLayout,
  style,
}) => {
  if (!promptInfos?.length) return null;
  return (
    <View onLayout={onLayout} style={style}>
      {promptInfos.map(v => (
        <MultiColumn key={v.title} title={v.title} blockIcon={v?.iconUrl}>
          <View className={styles.promptInfoItemNew}>
            {v.content?.length > 0 &&
              v.content.map(s => (
                <View className={styles.row} key={s}>
                  <Text className={styles.promptInfoTextNew}>{s}</Text>
                </View>
              ))}
            {!!v.subTitle && (
              <Text className={styles.promptInfoTip}>{v.subTitle}</Text>
            )}
          </View>
        </MultiColumn>
      ))}
    </View>
  );
};

const VehicleDetailNewBVer: React.FC<IVehicleDetailNewBVer> = ({
  onPressLocation,
  isOrderDetail,
  vehicleInfoLog,
  onPressMore,
  vehicleDetailModalTitle,
  pageName,
  onLayoutPickupAndDrop,
  onLayoutCancelInfo,
  showCarServiceDetail,
  showCarServiceDetailBooking,
  onPressCarServiceDetail,
  isShelvesReview,
}) => {
  const isSelect = useGetIsSelect(isOrderDetail);
  const selfComment = useSelector(getSelfComment);
  const isReviewFail = useSelector(getReviewFail);
  const isReviewLoading = useSelector(getReviewLoading);
  const hasSelfComment = useMemo(() => {
    return (
      selfComment?.positiveCommentCount && selfComment?.storeExceedPercentage
    );
  }, [selfComment]);
  const response = useGetProductConfirmResponse(isOrderDetail);
  const promptInfos = response?.promptInfos;
  const promptInfoDelay = useMemo(() => {
    return promptInfos?.filter(v =>
      [PromptInfosCodeEnum.delay].includes(v.code),
    );
  }, [promptInfos]);
  const isReviewSuccess = !isReviewLoading && !isReviewFail;
  return (
    <View
      className={xClassNames(
        styles.vehicleDetailNewBVerBlockWrap,
        !isSelect && styles.pt24,
      )}
    >
      {/* 优选背景 card */}
      {isSelect && (
        <CtripSelected
          hasSelfComment={hasSelfComment}
          isReviewLoading={isReviewLoading}
          isReviewFail={isReviewFail}
        />
      )}
      <View
        className={styles.vehicleDetailNewBVerBlockInnerWrap}
        onLayout={e => {
          onLayoutPickupAndDrop(e, LayoutPartEnum.VehicleDetailInner);
        }}
      >
        {/* 精选点评 */}
        {isSelect && !isShelvesReview && (
          <SelectedReviews isReviewSuccess={isReviewSuccess} />
        )}
        {/* 车龄信息 */}
        <CarAgeInfo
          vehicleInfoLog={vehicleInfoLog}
          onPressMore={onPressMore}
          vehicleDetailModalTitle={vehicleDetailModalTitle}
          isOrderDetail={isOrderDetail}
        />
        {/* 取还信息 */}
        <PickAndDropWay
          onPressLocation={onPressLocation}
          isOrderDetail={isOrderDetail}
          onLayoutPickupAndDrop={onLayoutPickupAndDrop}
        />
        {/* 取消政策 */}
        <CancelInfo
          onLayoutCancelInfo={onLayoutCancelInfo}
          isOrderDetail={isOrderDetail}
        />
        {/* 延误时免费留车说明挪 */}
        <PromptInfosNew promptInfos={promptInfoDelay} />
        {/* 服务与配置 */}
        <Services />
        {/* 基础服务 */}
        <Assurance
          isOrderDetail={isOrderDetail}
          showCarServiceDetail={showCarServiceDetail}
          showCarServiceDetailBooking={showCarServiceDetailBooking}
          pageName={pageName}
          onPressCarServiceDetail={onPressCarServiceDetail}
        />
        {/* 服务提供方 */}
        <ServiceProvider isOrderDetail={isOrderDetail} pageName={pageName} />
      </View>
    </View>
  );
};

export default memo(VehicleDetailNewBVer);
