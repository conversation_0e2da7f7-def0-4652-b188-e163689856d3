import { XView as View, xClassNames } from '@ctrip/xtaro';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import React from 'react';
/* eslint-disable */
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './DepositPaymentBlockISDC2xStyles.module.scss';
import {
  DepositPayInfosType,
  TrackInfoType,
} from '../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';
import DepositSimpleDesc from '../../ComponentBusiness/DepositSimpleDesc/src/DepositSimpleDesc';
import { ApiResCode } from '../../Constants/Index';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';

export interface IPaymentComponent {
  depositPayInfos?: any;
  depositInfo?: any;
  bookPriceTrackInfo?: TrackInfoType;
  setEhiFreeDepositModalVisible?: (data: boolean) => void;
  onPressCtripCreditF?: () => void;
}

const DepositPaymentBlockISD: React.FC<IPaymentComponent> = ({
  depositPayInfos,
  depositInfo,
  bookPriceTrackInfo,
  setEhiFreeDepositModalVisible,
  onPressCtripCreditF,
}) => {
  if (!depositPayInfos?.length) return;
  const { isAndroid } = BbkUtils;
  const { depositTypeInfo } = depositPayInfos?.[0] || {};
  const { title, desc, note } = depositTypeInfo || {};
  const { depositSimpleDescs } = depositInfo || {};
  return (
    <View className={c2xStyles.block}>
      <View className={c2xStyles.header}>
        <BbkText className={c2xStyles.title} fontWeight="bold">
          {bookPriceTrackInfo?.depositFreeType ===
            ApiResCode.DepositFreeType.noSupport ||
          bookPriceTrackInfo?.depositType === 0
            ? '押金'
            : title?.stringObjs?.[0]?.content}
        </BbkText>
        {!!note?.stringObjs?.[0]?.content && (
          <BbkTouchable
            className={c2xStyles.rightWrap}
            onPress={onPressCtripCreditF}
          >
            <BbkText className={c2xStyles.descTitle}>
              {note?.stringObjs?.[0]?.content}
            </BbkText>
            <BbkText
              type="icon"
              className={xClassNames(
                c2xStyles.descIcon,
                isAndroid && c2xStyles.top1,
              )}
            >
              {icon.help}
            </BbkText>
          </BbkTouchable>
        )}
      </View>
      {!!desc?.[0]?.stringObjs?.[0]?.content && (
        <View className={c2xStyles.descWrap}>
          <BbkText className={c2xStyles.descTitle}>
            {desc?.[0]?.stringObjs?.[0]?.content}
          </BbkText>
        </View>
      )}
      {!!depositSimpleDescs?.length && (
        <DepositSimpleDesc
          depositSimpleDescs={depositSimpleDescs}
          depositInfo={depositInfo}
          showExtra={false}
          isShowCredit={false}
          noBg
          style={c2xStyles.simpleWrap}
        />
      )}
    </View>
  );
};

export default withTheme(DepositPaymentBlockISD);
