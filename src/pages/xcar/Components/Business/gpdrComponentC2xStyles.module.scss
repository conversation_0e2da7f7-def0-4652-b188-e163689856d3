@import '../../Common/src/Tokens/tokens/color.scss';

.checkTerms {
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 40px;
  background-color: $C_EDF2F8;
}
.generalDescWrap {
  margin-bottom: 8px;
}
.generalDescText {
  font-size: 24px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}

.generalDescTextGs3 {
  font-size: 26px;
  line-height: 44px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontGrayBlue;
}
.secretBoxRuleWrap {
  margin-top: 20px;
  flex-wrap: wrap;
}
.textStyle {
  font-size: 26px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontBlueDark;
}
.businessLicenseWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 4px;
}
.licienseTextStyle {
  font-size: 24px;
  line-height: 30px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontBlueDark;
}
.checkTermsText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
}
.bookingOptimizationCheckTermsIcon {
  font-size: 24px;
  color: $fontBlueDark;
  margin-left: 5px;
}
.checkTermsIcon {
  font-size: 24px;
  color: $fontSecondary;
  margin-left: 5px;
}
.bookingOptimizationTermsWrap {
  margin-top: 20px;
}
.licienseTextStyleGs3 {
  color: $fontGrayBlue;
  font-size: 26px;
  line-height: 44px;
}