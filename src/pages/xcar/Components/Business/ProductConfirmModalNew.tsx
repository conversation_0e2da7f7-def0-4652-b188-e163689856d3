import { xMergeStyles, XView as View, XBoxShadow } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import Loading from '@c2x/apis/Loading';
import { useSelector } from 'react-redux';
import LoadingView from '@c2x/components/LoadingView';
import React, {
  useCallback,
  useRef,
  useState,
  useEffect,
  useMemo,
  memo,
} from 'react';
import Dimensions from '@c2x/apis/Dimensions';
import ScrollView from '@c2x/components/CScrollView';
import OScrollView from '@c2x/components/ScrollView';
import LayoutChangeEvent from '@c2x/apis/LayoutChangeEvent';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, layout, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './productConfirmModalNewC2xStyles.module.scss';
import BbkHalfPageModal from '../../ComponentBusiness/HalfPageModal';
import EmptyComponent, {
  ImgType,
} from '../../ComponentBusiness/EmptyComponent/Index';
import { Tabs } from '../../ComponentBusiness/ProductConfirmModal/components/Tabs';
import VehicleDetail from '../../Containers/ProductConfirmVehicleDetailContainerNew';
import VehicleDetailIsdShelves2B from './VehicleDetailIsdShelves2B';
import Attention from '../../Containers/ProductConfirmAttentionContainerNew';
import AttentionIsdShelves2B from '../../Containers/ProductConfirmAttentionContainerIsdShelves2B';
import Reviews from '../../Containers/ProductConfirmReviewsContainerNew';
import Distance from '../../Containers/ProductConfirmDistanceContainer';
import FeeDetail from '../../ComponentBusiness/ProductConfirmModal/components/FeeDetail';
import {
  Header,
  Footer,
} from '../../ComponentBusiness/ProductConfirmModal/components/MiniComponent';
import { texts } from '../../ComponentBusiness/ProductConfirmModal/Texts';
import {
  LayoutPartEnum,
  FooterBarDataType,
  StoreAlbumPageParamsType,
  VehicleInfoLogDataType,
} from '../../ComponentBusiness/ProductConfirmModal/Type';
import CalendarPriceModal from '../../ComponentBusiness/CalendarPrice/CalendarPriceModal';
import { CalendarPriceModalContextProvider } from '../../ComponentBusiness/CalendarPrice/CalendarPriceContext';
import {
  Utils,
  CarLog,
  Channel,
  CarServerABTesting,
  AppContext,
} from '../../Util/Index';
import { Enums } from '../../ComponentBusiness/Common';
import { TotalPriceModalType } from '../../ComponentBusiness/Common/src/Enums';
import { PolicyPressType, GuideTabType } from '../../State/Product/Enums';

import { VendorListTexts } from '../../Constants/TextIndex';
import { PageParamType } from '../../Types/Dto/QueryVehicleDetailListRequestType';
import { ReqInfoType } from '../../Types/Dto/RentalMustReadResponseType';
import { UITestID } from '../../Constants/Index';
import { SCENES } from '../../Constants/Guide';
import { getGuidePageParam } from '../../State/OrderDetail/Mappers';
import IsdShelves2BVirtualNumberIcon from '../../ComponentBusiness/ProductConfirmModal/components/IsdShelves2BVirtualNumberIcon';

import { getOrderDetailIsISDShelves2B } from '../../State/OrderDetail/Selectors';

const { getPixel, vh, useMemoizedFn } = BbkUtils;
const noop = () => {};

const styles = StyleSheet.create({
  container: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 0,
    paddingBottom: 0,
    backgroundColor: color.transparent,
  },
  contentLoading: {
    backgroundColor: color.white,
    alignItems: 'center',
  },
  contentFail: {
    backgroundColor: color.white,
    paddingTop: getPixel(260),
  },
  loading: {
    top: getPixel(-200),
  },
});

const fixSelectedId = (anchor: LayoutPartEnum) => {
  if (
    anchor === LayoutPartEnum.Mileage ||
    anchor === LayoutPartEnum.PickUpMaterials
  ) {
    return LayoutPartEnum.Attention;
  }
  if (anchor === LayoutPartEnum.VehicleConfig) {
    return LayoutPartEnum.VehicleDetail;
  }
  return anchor;
};

type ISDShelves2BHeaderPropsType = {
  vehicleDetailModalTitle: [string];
  onClose: () => void;
};

const ISDShelves2BHeader = memo(
  ({ vehicleDetailModalTitle, onClose }: ISDShelves2BHeaderPropsType) => {
    const vehicleDetailModalTitleFilted = vehicleDetailModalTitle?.filter?.(
      v => !!v,
    );
    return (
      <BbkHeader
        leftIconStyle={{ fontSize: getPixel(42) }}
        leftIconColor={color.fontSecondary}
        isLeftIconCross={true}
        style={{ paddingTop: 0 }}
        styleInner={{
          minHeight: getPixel(88),
        }}
        onPressLeft={onClose}
        isBottomBorder={false}
        renderContent={
          <View
            style={xMergeStyles([
              layout.flexCenter,
              layout.flex1,
              layout.flexRow,
            ])}
          >
            {vehicleDetailModalTitleFilted?.map?.((item, idx) => {
              return (
                <View style={layout.flexRow} key={item}>
                  <Text className={c2xStyles.title} fontWeight="medium">
                    {item}
                  </Text>
                  {idx !== vehicleDetailModalTitleFilted.length - 1 && (
                    <View className={c2xStyles.verticalLine} />
                  )}
                </View>
              );
            })}
          </View>
        }
      />
    );
  },
);

export interface ProductConfirmModalProps {
  footerBarData?: FooterBarDataType;
  visible: boolean;
  vendorName?: string;
  isSelected: boolean;
  isVirtualNumberIconVisible?: boolean;
  isLoading?: boolean;
  isFail?: boolean;
  isVirtualNumberLoading?: boolean;
  page?: any; // 当前页面
  pageParam?: PageParamType; // 当前页面的参数
  uniqueCode?: string;
  storeAlbumPageParams?: StoreAlbumPageParamsType;
  vehicleInfoLog?: VehicleInfoLogDataType;
  guidePageParams?: any;
  hasFees?: boolean; // 当前的报价是否有价格一致性费用节点
  commentCount?: number;
  anchor?: LayoutPartEnum;
  showFooter?: boolean;
  useModal?: boolean;
  nationalChainTagTitle?: string;
  isHeightFix?: boolean; // 是否固定高度，正常固定高度，快照截图不固定高度
  queryVehicleDetailInfo?: () => void;
  onPressEasyLife?: () => void;
  onPressFooter?: (
    type: TotalPriceModalType,
    uniqueCode: string,
    hasFees: boolean,
  ) => void;
  showPriceDetailModal?: boolean; // 是否展示价格详情弹层
  onContinue?: () => void;
  onClose?: () => void;
  onPressOptimize?: () => void;
  isOrderDetail?: boolean;
  gotoPolicy?: (_?: any, __?: any) => void;
  showPhoneDialog?: () => void;
  isCouponBook?: boolean; // 是否是领券订产品
  virtualNumber?: string;
  vendorVNumber?: string;
  vendorImUrl?: string;
  showOptimizationStrengthenModal?: () => void;
  optimizationStrengthenHeaderExposureLogData?: any;
  pickUpDistanceDesc?: string;
  policyQueryParams?: ReqInfoType;
  showVirtualNumberStoreModal?: () => void;
  isSecretBox?: boolean;
  isSelfService?: boolean;
  pageName?: string;
  isSnapShot?: boolean;
  fetchApiGuide?: (data: any) => void;
  vehicleDetailModalTitle?: [];
  vehicleDetailModalLogInfo?: any;
  setPriceDetailModalVisible?: (data: boolean) => void;
  reference?: any;
  curFloor?: any;
  showCarServiceDetail?: (
    reference: any,
    code: string,
    allPackageLevel?: string[],
  ) => void;
  showCarServiceDetailBooking?: (code: string) => void;
  onPressShowNoLimitModal?: (data) => void;
  onPressCarServiceDetail?: (data) => void;
}

/**
 * 此弹窗在填写页会包含在交易快照中，且平铺展示
 * 改动请注意回归测试填写页的交易快照展示及性能
 */
const ProductConfirmModal: React.FC<ProductConfirmModalProps> = ({
  isHeightFix = true,
  visible,
  isVirtualNumberLoading,
  isVirtualNumberIconVisible,
  vendorName,
  isSelected,
  isLoading,
  commentCount,
  isFail,
  anchor,
  showFooter = true,
  useModal = true,
  footerBarData,
  page,
  uniqueCode,
  nationalChainTagTitle,
  guidePageParams,
  storeAlbumPageParams,
  vehicleInfoLog,
  onClose = noop,
  onPressOptimize = noop,
  onContinue,
  onPressFooter,
  onPressEasyLife = noop,
  showPhoneDialog = noop,
  queryVehicleDetailInfo,
  hasFees,
  isOrderDetail = false,
  gotoPolicy,
  isCouponBook,
  virtualNumber,
  vendorVNumber,
  vendorImUrl,
  showOptimizationStrengthenModal = noop,
  optimizationStrengthenHeaderExposureLogData,
  pickUpDistanceDesc,
  pageParam,
  showVirtualNumberStoreModal,
  policyQueryParams,
  isSecretBox,
  isSelfService,
  pageName,
  showPriceDetailModal = false,
  isSnapShot,
  fetchApiGuide,
  vehicleDetailModalTitle,
  vehicleDetailModalLogInfo,
  setPriceDetailModalVisible,
  showCarServiceDetail,
  showCarServiceDetailBooking,
  onPressCarServiceDetail,
  onPressShowNoLimitModal,
}) => {
  const orderDetailIsISDShelves2B = useSelector(getOrderDetailIsISDShelves2B);
  const isISDShelves2B = isOrderDetail
    ? orderDetailIsISDShelves2B
    : CarServerABTesting.isISDShelves2B();
  const isShelvesReview =
    isISDShelves2B && commentCount !== null && commentCount !== undefined;
  // const [selectedId, setSelectedId] = useState(LayoutPartEnum.VehicleDetail);
  const scrollRef = useRef(null);
  const tabsRef = useRef(null);
  const isOperated = useRef(false);
  const [scrollHeight, setScrollHeight] = useState(0);
  const [lastTabHeight, setLastTabHeight] = useState(0); // 最后一个tab栏的高度
  const [tabScrollToHeight, setTabScrollToHeight] = useState(-1);
  const limitHeight = useRef(0);
  const [positions, setPositions] = useState(
    Object.values(LayoutPartEnum).map(part => ({ type: part, positionY: 0 })),
  );
  // eslint-disable-next-line  @typescript-eslint/no-unused-vars
  const [pickupDistance, setPickupDistance] = useState<string>('');
  const [returnDistance, setReturnDistance] = useState<string>('');
  const isFetchingVNumber = useRef<boolean>(false);
  const tabs = useMemo(() => {
    const items = [];
    items.push({
      type: LayoutPartEnum.VehicleDetail,
      title: texts.vehicleDetail,
      clickKey: '点击_门店弹层_详情tab',
    });
    if (isOrderDetail) {
      if (isISDShelves2B && commentCount > 0) {
        items.push({
          type: LayoutPartEnum.Reviews,
          title: '评价',
          clickKey: '点击_门店弹层_评价',
        });
      }
      items.push({
        type: LayoutPartEnum.Attention,
        title: texts.carRentalMustRead,
        clickKey: '点击_门店弹层_订前必读tab',
      });
    } else {
      if (
        AppContext.goodsShelvesTwoABVersion === 'C' &&
        (isLoading || commentCount > 0)
      ) {
        items.push({
          type: LayoutPartEnum.Reviews,
          title: '评价',
          clickKey: '点击_门店弹层_评价',
        });
      }
      items.push({
        type: LayoutPartEnum.Attention,
        title: texts.new_attention,
        clickKey: '点击_门店弹层_订前必读tab',
      });
      items.push({
        type: LayoutPartEnum.FeeDetail,
        title: isISDShelves2B ? texts.fee_shelves2 : texts.new_fee,
        clickKey: '点击_门店弹层_费用tab',
      });
    }
    return items;
  }, [isOrderDetail, isISDShelves2B, commentCount, isLoading]);

  const lastTabEnum = tabs[tabs.length - 1]?.type;

  const onPartLayout = useMemoizedFn(
    (e: LayoutChangeEvent, type: LayoutPartEnum) => {
      const { nativeEvent } = e;
      const positionY = Math.ceil(nativeEvent.layout.y);
      const elHeight = nativeEvent.layout.height;
      // harmony为同步渲染 将批处理转换为回调函数赋值
      const positionsCurrent = (x: any) => {
        return x.map(v => {
          if (type === v.type) return { ...v, positionY };
          return v;
        });
      };
      if (type === LayoutPartEnum.Mileage) {
        limitHeight.current = elHeight;
      }
      if (type === lastTabEnum) {
        setLastTabHeight(elHeight);
      }
      setPositions(pos => {
        return positionsCurrent(pos);
      });
    },
  );

  const onScrollViewLayout = useMemoizedFn((e: LayoutChangeEvent) => {
    const { nativeEvent } = e;
    setScrollHeight(nativeEvent.layout.height);
  });

  const scrollTo = useMemoizedFn(y =>
    setTimeout(() => {
      if (scrollRef?.current?.scrollTo) {
        scrollRef.current?.scrollTo({
          x: 0,
          y,
          animated: false,
        });
      }
    }),
  );

  useEffect(() => {
    if (!visible) {
      isOperated.current = false;
    }
  }, [visible]);

  const onPressTab = useCallback(
    (type: LayoutPartEnum, clickKey) => {
      // 如果弹窗内容未加载完成，则点击tab切换不响应
      const isLoadComplete = positions.find(item => item?.positionY > 0);
      if (!isLoadComplete) {
        return;
      }
      isOperated.current = true;
      const y = positions.find(v => v.type === type)?.positionY || 0;
      tabsRef.current?.setSelectedId(fixSelectedId(type));
      // setSelectedId(fixSelectedId(type));
      setTabScrollToHeight(y);
      scrollTo(y);
      CarLog.LogCode({
        name: clickKey,
        info: {
          tabName: clickKey,
          ...vehicleDetailModalLogInfo,
        },
      });
    },
    [scrollTo, positions, vehicleDetailModalLogInfo],
  );

  useEffect(() => {
    if (visible && isFail) {
      queryVehicleDetailInfo();
    }
  }, [visible, isFail, queryVehicleDetailInfo]);

  const pageModalProps = useMemo(
    () => ({
      visible,
      onMaskPress: onClose,
    }),
    [visible, onClose],
  );

  useEffect(() => {
    if (isOperated.current) {
      return;
    }
    tabsRef.current?.setSelectedId(fixSelectedId(anchor));
    // setSelectedId(fixSelectedId(anchor));
    if (anchor === LayoutPartEnum.Mileage) {
      const y =
        positions.find(v => v.type === LayoutPartEnum.Attention)?.positionY ||
        0;
      const mileageY =
        positions.find(v => v.type === LayoutPartEnum.Mileage)?.positionY || 0;
      if (y === 0 && commentCount > 0) {
        return;
      }
      if (y > 0) {
        scrollTo(y + mileageY);
      } else {
        setTimeout(() => {
          if (scrollRef?.current?.scrollToEnd) {
            scrollRef.current.scrollToEnd();
          }
        });
      }
      return;
    }
    if (anchor === LayoutPartEnum.PickupAndDrop) {
      const vehicleDetailY =
        positions.find(v => v.type === LayoutPartEnum.VehicleDetail)
          ?.positionY || 0;
      const vehicleDetailInnerY =
        positions.find(v => v.type === LayoutPartEnum.VehicleDetailInner)
          ?.positionY || 0;
      const pickupAndDropY =
        positions.find(v => v.type === LayoutPartEnum.PickupAndDrop)
          ?.positionY || 0;
      scrollTo(vehicleDetailY + vehicleDetailInnerY + pickupAndDropY);
      return;
    }

    if (anchor === LayoutPartEnum.CancelInfo) {
      const vehicleDetailY =
        positions.find(v => v.type === LayoutPartEnum.VehicleDetail)
          ?.positionY || 0;
      const vehicleDetailInnerY =
        positions.find(v => v.type === LayoutPartEnum.VehicleDetailInner)
          ?.positionY || 0;
      const cancelInfoY =
        positions.find(v => v.type === LayoutPartEnum.CancelInfo)?.positionY ||
        0;
      scrollTo(vehicleDetailY + vehicleDetailInnerY + cancelInfoY);
      return;
    }

    if (anchor === LayoutPartEnum.PickUpMaterials) {
      const attentionY =
        positions.find(v => v.type === LayoutPartEnum.Attention)?.positionY ||
        0;
      const pickUpMaterialsY =
        positions.find(v => v.type === LayoutPartEnum.PickUpMaterials)
          ?.positionY || 0;
      scrollTo(attentionY + pickUpMaterialsY);
      return;
    }

    if (anchor) {
      const y = positions.find(v => v.type === anchor)?.positionY || 0;
      /**
       * @y打底为1的原因：
       * 当前scrollView本身无滚动，位置是0，
       * scrollTo({ x: 0, y: 0, animated: false })不会滚动，
       * 所以不会触发onScroll事件，因而不会更新selectedId
       * */
      scrollTo(y);
    }
  }, [anchor, scrollTo, commentCount, positions]);

  const onPressLocation = useCallback(
    guideTabId => {
      if (isOrderDetail) {
        const param = getGuidePageParam(guideTabId);
        CarLog.LogCode({
          name: '点击_产品详情页_门店弹层_地图及指引',

          info: param,
        });
        page.push(Channel.getPageId().Guide.EN, {
          pageParam: {
            ...param,
            isFromOrderDetail: true,
            scene: SCENES.OrderDetail,
            isISDShelves2B,
          },
        });
      } else {
        CarLog.LogCode({
          name: '点击_产品详情页_门店弹层_地图及指引',

          info: vehicleInfoLog,
        });
        page.push(Channel.getPageId().Guide.EN, {
          pageParam: {
            ...guidePageParams,
            selectedId: guideTabId,
            isISDShelves2B,
          },
        });
      }
    },
    [vehicleInfoLog, isOrderDetail, page, guidePageParams, isISDShelves2B],
  );

  useEffect(() => {
    let guideApiParam = {};
    if (visible) {
      if (isOrderDetail) {
        const param = getGuidePageParam(GuideTabType.Pickup);
        guideApiParam = {
          ...param,
          isFromOrderDetail: true,
          scene: SCENES.OrderDetail,
          noLoading: true,
        };
      } else {
        guideApiParam = {
          ...guidePageParams,
          selectedId: GuideTabType.Pickup,
          noLoading: true,
        };
      }
      fetchApiGuide(guideApiParam);
    }
  }, [fetchApiGuide, isOrderDetail, guidePageParams, visible]);

  const onPressAlbumMore = useCallback(() => {
    page.push(Channel.getPageId().Album.EN, storeAlbumPageParams);
  }, [page, storeAlbumPageParams]);

  const onPressPolice = useCallback(
    (policyType = PolicyPressType.All) => {
      if (isOrderDetail) {
        gotoPolicy(policyType);
      } else {
        CarLog.LogCode({
          name: '点击_产品详情页_门店弹层_查看更多门店政策',

          info: vehicleInfoLog,
        });
        page.push(Channel.getPageId().Policy.EN, {
          fetchSelf: true,
          policySelectedId: policyType,
          params: policyQueryParams,
        });
      }
    },
    [isOrderDetail, gotoPolicy, vehicleInfoLog, page, policyQueryParams],
  );

  const onPressReviewsLog = useCallback(
    (tag?: string) => {
      if (tag) {
        CarLog.LogCode({
          name: '点击_产品详情页_门店弹层_用户评价标签',

          info: {
            ...vehicleInfoLog,
            commentLabelName: tag,
          },
        });
      } else {
        CarLog.LogCode({
          name: '点击_产品详情页_门店弹层_查看全部评价',

          info: vehicleInfoLog,
        });
      }
    },
    [vehicleInfoLog],
  );

  const handleOnScrollBeginDrag = useMemoizedFn(() => {
    isOperated.current = true;
  });

  const handlePhonePress = useMemoizedFn(() => {
    CarLog.LogCode({
      name: '点击_产品详情页_门店信息弹层_供应商电话',

      info: vehicleInfoLog,
    });
    if (isVirtualNumberLoading) {
      isFetchingVNumber.current = true;
      Loading.showMaskLoading();
    } else {
      showPhoneDialog();
    }
  });

  const handlePressEasyLife = useMemoizedFn(() => {
    CarLog.LogCode({
      name: '点击_产品详情页_门店弹层_查看无忧租',

      info: vehicleInfoLog,
    });
    onPressEasyLife();
  });

  useEffect(() => {
    if (!isVirtualNumberLoading && isFetchingVNumber.current) {
      isFetchingVNumber.current = false;
      Loading.hideMaskLoading();
      showPhoneDialog();
    }
  }, [isVirtualNumberLoading, showPhoneDialog]);

  const handleOnScroll = useCallback(
    (event: any) => {
      if (isOrderDetail && tabs?.length === 1) return;
      const { nativeEvent } = event;
      // 模块位置信息是否初始化完成，通过判断Y轴>0的个数来判断, 大于1说明已经有模块初始化完成
      const layoutCount = positions?.filter(pos => pos.positionY > 0)?.length;
      const scrollY = Math.ceil(nativeEvent.contentOffset.y);
      let reversedIndex: undefined | number;
      let hasPopTab = true;
      tabs
        .map(v => positions.find(s => s.type === v.type).positionY)
        .reverse()
        .forEach((limit, i) => {
          if (tabScrollToHeight === limit) {
            hasPopTab = false;
            setTabScrollToHeight(-1);
          } else if (
            reversedIndex === undefined &&
            scrollY > 0 &&
            scrollY >= limit
          ) {
            reversedIndex = i;
          }
        });
      const popTab = tabs[tabs.length - 1 - reversedIndex] || tabs[0];
      if (hasPopTab && layoutCount > 0) {
        tabsRef.current?.setSelectedId(popTab.type);
      }
    },
    [isOrderDetail, tabs, positions, tabScrollToHeight],
  );

  const handleOnPressBar = useCallback(() => {
    onPressFooter(
      Enums.TotalPriceModalType.VendorListModal,
      uniqueCode,
      hasFees,
    );
  }, [uniqueCode, hasFees, onPressFooter]);

  const isSuccess = !isLoading && !isFail;
  const Wrapper = useModal ? BbkHalfPageModal : View;

  // 是否能展示拨打电话icon
  const isShowPhone = Utils.isCtripIsd() && isVirtualNumberIconVisible;
  const isShowVirtualNumberEntry = useMemo(() => {
    return (
      isISDShelves2B && ((Utils.isCtripIsd() && !!vendorImUrl) || isShowPhone)
    );
  }, [isISDShelves2B, vendorImUrl, isShowPhone]);
  const hasFeeDetailEmpty =
    scrollHeight > 0 && lastTabHeight > 0 && lastTabHeight < scrollHeight;
  const ScrollViewWrap = isSnapShot ? OScrollView : ScrollView;
  return (
    <CalendarPriceModalContextProvider>
      <Wrapper
        pageModalProps={pageModalProps}
        headerDom={<View />}
        contentStyle={styles.container}
        style={xMergeStyles([
          styles.container,
          isHeightFix && {
            height: isISDShelves2B
              ? Dimensions.get('window').height * 0.9
              : vh(85),
          },
        ])}
        closeModalBtnTestID={
          UITestID.car_testid_page_vendorlist_product_modal_close_mask
        }
        testID={UITestID.car_testid_page_vendorlist_productconfirm_modal}
      >
        <Tabs
          tab={tabs}
          // @ts-ignore
          ref={tabsRef}
          renderTop={
            isISDShelves2B ? (
              <ISDShelves2BHeader
                vehicleDetailModalTitle={['商品详情']}
                onClose={onClose}
              />
            ) : (
              <Header
                title={vendorName}
                isOptimize={isSelected}
                nationalChainTagTitle={nationalChainTagTitle}
                onPressClose={onClose}
                onPressOptimize={onPressOptimize}
                onPressOptimizationStrengthenTitle={
                  showOptimizationStrengthenModal
                }
                optimizationStrengthenHeaderExposureLogData={
                  optimizationStrengthenHeaderExposureLogData
                }
                showVirtualNumberStoreModal={showVirtualNumberStoreModal}
                isShowPhone={isShowPhone}
                virtualNumber={virtualNumber}
                vendorVNumber={vendorVNumber}
                vendorImUrl={vendorImUrl}
                virtualNumberLog={vehicleInfoLog}
                showPhoneDialog={showPhoneDialog}
              />
            )
          }
          onPressTab={onPressTab}
        />

        {!isSuccess && (
          <XBoxShadow
            coordinate={{ x: 0, y: getPixel(-8) }}
            color={
              isLoading
                ? setOpacity(color.black, 0.03)
                : setOpacity(color.black, 0)
            }
            opacity={1}
            blurRadius={getPixel(12)}
            className={c2xStyles.content}
            style={xMergeStyles([
              isLoading ? styles.contentLoading : styles.contentFail,
              isISDShelves2B && layout.flex1,
            ])}
          >
            {isLoading && (
              <LoadingView
                loadingText={texts.loadingText}
                clearBgColor={true}
                // @ts-ignore 升级072
                style={styles.loading}
              />
            )}
            {!isLoading && isFail && (
              <EmptyComponent
                imgType={ImgType.No_Response}
                showButton={true}
                onButtonPress={queryVehicleDetailInfo}
              />
            )}
          </XBoxShadow>
        )}
        {isSuccess && (
          <View
            testID={UITestID.car_testid_comp_booking_vehicle_modal}
            className={c2xStyles.content}
          >
            <ScrollViewWrap
              ref={scrollRef}
              // @ts-ignore
              scrollEventThrottle={10}
              onLayout={useModal && onScrollViewLayout}
              onScroll={handleOnScroll}
              onScrollBeginDrag={handleOnScrollBeginDrag}
              style={layout.flex1}
            >
              {isISDShelves2B ? (
                <VehicleDetailIsdShelves2B
                  vehicleInfoLog={vehicleInfoLog}
                  onPressMore={onPressAlbumMore}
                  isOrderDetail={isOrderDetail}
                  onPressLocation={onPressLocation}
                  isShelvesReview={isShelvesReview}
                  vehicleDetailModalTitle={vehicleDetailModalTitle}
                  pageName={pageName}
                  onLayoutPickupAndDrop={onPartLayout}
                  onLayoutCancelInfo={onPartLayout}
                  showCarServiceDetail={showCarServiceDetail}
                  showCarServiceDetailBooking={showCarServiceDetailBooking}
                  onPressCarServiceDetail={onPressCarServiceDetail}
                />
              ) : (
                <VehicleDetail
                  vehicleInfoLog={vehicleInfoLog}
                  pickupDistance={pickUpDistanceDesc}
                  returnDistance={returnDistance}
                  onLayoutWrapper={onPartLayout}
                  onLayoutVehicleConfig={onPartLayout}
                  onPressLocation={onPressLocation}
                  onPressEasyLife={handlePressEasyLife}
                  onPressMore={onPressAlbumMore}
                  isOrderDetail={isOrderDetail}
                  isSelfService={isSelfService}
                />
              )}

              <Reviews
                vehicleInfoLog={vehicleInfoLog}
                onLayoutWrapper={onPartLayout}
                onPressReviewsLog={onPressReviewsLog}
                isOrderDetail={isOrderDetail}
                isShelvesReview={isShelvesReview}
              />
              {isISDShelves2B ? (
                <AttentionIsdShelves2B
                  vehicleInfoLog={vehicleInfoLog}
                  onLayoutWrapper={onPartLayout}
                  onLayoutMileage={onPartLayout}
                  onLayoutMaterials={onPartLayout}
                  onPressPolice={onPressPolice}
                  isOrderDetail={isOrderDetail}
                  pageParam={pageParam}
                  pageName={pageName}
                  onPressShowNoLimitModal={onPressShowNoLimitModal}
                />
              ) : (
                <Attention
                  vehicleInfoLog={vehicleInfoLog}
                  onLayoutWrapper={onPartLayout}
                  onLayoutMileage={onPartLayout}
                  onPressPolice={onPressPolice}
                  isOrderDetail={isOrderDetail}
                  pageParam={pageParam}
                  pageName={pageName}
                />
              )}

              {!isOrderDetail && (
                <FeeDetail
                  vehicleInfoLog={vehicleInfoLog}
                  onLayoutWrapper={onPartLayout}
                />
              )}
              {hasFeeDetailEmpty && (
                <View style={{ height: scrollHeight - lastTabHeight }} />
              )}
              {!isOrderDetail && (
                <Distance
                  onPickupDistanceChange={setPickupDistance}
                  onReturnDistanceChange={setReturnDistance}
                />
              )}
            </ScrollViewWrap>
          </View>
        )}
        {showFooter && isSuccess && (
          <Footer
            isShowVirtualNumberEntry={isShowVirtualNumberEntry}
            isISDShelves2B={isISDShelves2B}
            isShowTotalAmount={true}
            isLoading={isLoading}
            isNewBooking={true}
            onPressBtn={onContinue}
            onPressBar={handleOnPressBar}
            isShowPhone={isShowPhone}
            showPriceDetailModal={showPriceDetailModal}
            onPressPhone={handlePhonePress}
            buttonTestID={
              UITestID.car_testid_page_vendorlist_product_modal_footer_button
            }
            phoneTestID={
              UITestID.car_testid_page_vendorlist_product_modal_footer_phone
            }
            barTestID={
              UITestID.car_testid_page_vendorlist_product_modal_footer_bar
            }
            virtualNumber={virtualNumber}
            vendorImUrl={vendorImUrl}
            data={footerBarData}
            virtualNumberLog={vehicleInfoLog}
            buttonName={
              isISDShelves2B && isShowVirtualNumberEntry
                ? '预订'
                : isCouponBook && VendorListTexts.couponBookNow
            }
            virtualNumberEntry={
              <IsdShelves2BVirtualNumberIcon
                showVirtualNumberStoreModal={showVirtualNumberStoreModal}
                isShowPhone={isShowPhone}
                vendorImUrl={vendorImUrl}
                virtualNumberLog={vehicleInfoLog}
                hasVirtualNumber={!!(virtualNumber || vendorVNumber)}
                showPhoneDialog={showPhoneDialog}
                vehicleDetailModalLogInfo={vehicleDetailModalLogInfo}
                setPriceDetailModalVisible={setPriceDetailModalVisible}
              />
            }
          />
        )}
      </Wrapper>
      <CalendarPriceModal />
    </CalendarPriceModalContextProvider>
  );
};

export default ProductConfirmModal;
