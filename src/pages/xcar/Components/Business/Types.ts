import { CSSProperties } from 'react';

import {
  Passenger,
  Certificate,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import { DriverItem } from '../../ComponentBusiness/BookForm';
import {
  ComplexSubTitleType,
  LabelsType,
} from '../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';
import { OptionalContactMethods } from '../../Types/Dto/QueryProductInfoType';
import { LocalContactInfoType } from '../../Constants/LocalContactsData';

export interface IGPDRComponent {
  isChecked?: boolean;
  hasCheckBox?: boolean;
  buttonName?: string;
  supplierData?: any;
  showPayMode?: number;
  showCtripDepositFee?: boolean;
  generalDescList?: string[];
  vendorId?: string;
  style?: CSSProperties;
  isSecretBox?: boolean;
  secretBoxRuleUrl?: string;
  onPressCtripDepositFee?: () => void;
  onPressTandC?: () => void;
  querySupplierData?: ({ vendorId }: { vendorId: string }) => void;
  onPressSupplier?: () => void;
  onPressExplain?: () => void;
  showPersonalAuthCheck?: boolean;
  isShowSelfServiceInstruction?: boolean;
  onPressSelfServiceInstruction?: () => void;
  setBusinessLicenseVisible?: (data) => void;
  isBookIsdGS3?: boolean;
}

export interface IBookFrom {
  passenger: Passenger;
  recommendedPassengers: Passenger[];
  driverInfo: DriverItem[];
  age: string;
  flightErrorTip: string;
  isAirportStore: boolean;
  changeFormData: (data: DriverItem[]) => void;
  setFlightErrorTip: (data: string) => void;
  validateFlightNo: (data: { type: string; flightNo: string }) => void;
  setAge: (age: string) => void;
  onPressDriver: () => void;
  selectDriver: (data: Passenger) => void;
  onAddInstructPress?: () => void;
  onPressFlightDelayRules: () => void;
  onPresssMore: () => void;
  maxDriverAge?: number;
  minDriverAge?: number;
  driverFeeDesc?: string;
  needFlightNo?: boolean;
  showFlightNo?: boolean;
  isEasyLife?: boolean;
  flightInfoTip?: string;
  isPressFlightInfo?: boolean;
  isSesameUserTip?: boolean;
  passengerIDCard?: Certificate;
  driverTips?: ComplexSubTitleType;
  depositLabel?: LabelsType;
  testID?: any;
  noAnyPassenger?: boolean;
  driverTitle?: string;
  passengerError: boolean;
  driverPickUpMaterialDesc: string[];
  drivingAgeLimit?: string;
  isShowLoading?: boolean;
  onPressQuestion?: () => void;
  onPressDriverLicense?: () => void;
  productRes?: any;
  pickUpRequirement?: any;
  curDriverLicense?: any;
  isHasTips?: boolean;
  onPressLocalContacts?: () => void;
  hasLocalContacts?: boolean;
  localContactsInputFocus?: (isfocus: boolean) => void;
  optionalContactMethods?: OptionalContactMethods;
  localContactsData?: LocalContactInfoType[];
  changeLocalContactsData?: (data: LocalContactInfoType[]) => void;
  topChildren?: React.ReactNode;
  onPressPickUpMaterials?: () => void;
}

export interface IBookingFormTip {
  style?: CSSProperties;
  extraCharge?: string;
  requirements?: Array<string>;
  driverYear?: string;
  testID?: string;
}

export interface IBookFooter {
  isPriceLoading: boolean;
  noExtraInfo?: boolean;
  priceInfo?: any;
  isPriceFail?: boolean;
  onPressBtn?: () => void;
  onPressBar?: () => void;
  isRebook: boolean;
  resCancelFeeRebook?: any;
  isLogin?: boolean;
  onCheckBarCheck?: (check) => void;
  onCheckBarPress?: () => void;
  showPersonalAuthCheck?: boolean;
  personalInfoCheck?: boolean;
  setBookBarHeight?: (height: number) => void;
  osdPriceDetailModalVisible?: boolean;
}

export interface ISDCreditRentUserTipProps {
  driverTips?: ComplexSubTitleType;
  onPress?: () => void;
}

export interface ITitle {
  name?: string;
}

export interface ILocationInfo {
  isSelfService: boolean;
  locationInfo: any;
  storeGuidInfo: any;
  isOrderDetail: boolean;
}

export interface ILookMore {
  onPressLocation: (tabid: any) => void;
}

export interface IPickUpGuide {
  onPressLocation: (tabid: any) => void;
  outShowGuideImgList: [];
  wholeImgList: [];
}

export interface IPickAndDropWay {
  onPressLocation: (tabid: any) => void;
  isOrderDetail: boolean;
  onLayoutPickupAndDrop: (e, type) => void;
}

export interface IVehicleDetailNewBVer {
  onPressLocation: (tabid: any) => void;
  isOrderDetail: boolean;
  isShelvesReview: boolean;
  vehicleInfoLog: any;
  onPressMore: () => void;
  vehicleDetailModalTitle?: [];
  pageName?: string;
  onLayoutPickupAndDrop: (e, type) => void;
  onLayoutCancelInfo: (e, type) => void;
  showCarServiceDetail?: (reference: any,
    code: string,
    allPackageLevel: string[],
  ) => void;
  showCarServiceDetailBooking?: (code: string,) => void;
  onPressCarServiceDetail?: (data: any) => void;
}

export interface ICancelInfo {
  isOrderDetail: boolean;
  onLayoutCancelInfo: (e, type) => void;
}

export interface IAssurance {
  isOrderDetail: boolean;
  showCarServiceDetail?: (reference: any,
    code: string,
    allPackageLevel: string[],
  ) => void;
  showCarServiceDetailBooking?: (code: string) => void;
  pageName: string;
  onPressCarServiceDetail?: (data: any) => void;
}

export interface IServiceProvider {
  isOrderDetail: boolean;
  pageName: string;
}
