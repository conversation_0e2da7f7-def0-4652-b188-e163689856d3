import React from 'react';
import { XView as View, XImage as Image } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import multiColumnStyles from './multiColumn.module.scss';

const MultiColumn: React.FC<{
  title: string;
  children: any;
  blockIcon: string;
  header?: any;
  style?: any;
  onLayout?: any;
}> = ({ title, children, blockIcon, header, style, onLayout }) => {
  return (
    <View
      onLayout={onLayout}
      className={multiColumnStyles.blockSection}
      style={style}
    >
      <View className={multiColumnStyles.blockLeft}>
        <View className={multiColumnStyles.blockTitleIconWrap}>
          <Image
            className={multiColumnStyles.blockTitleIcon}
            src={
              blockIcon ||
              'https://dimg04.c-ctrip.com/images/1tg6m12000jbxb9wfE1C0.png' // 兜底图标
            }
          />
        </View>
      </View>
      <View className={multiColumnStyles.blockRight}>
        {header || (
          <Text className={multiColumnStyles.blockTitle} fontWeight="medium">
            {title}
          </Text>
        )}
        <View className={multiColumnStyles.blockRightContent}>{children}</View>
      </View>
    </View>
  );
};

export default MultiColumn;
