import React, { memo, useCallback } from 'react';
import {
  XView as View,
  xClassNames,
  XImage as Image,
  XViewExposure,
} from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import CarLog from '../../Util/CarLog';
import c2xStyles from './rentalLocationInfo.module.scss';
import { IGuidInfoType, IRentalLocation } from '../../Pages/Booking/Types';
import { CommonEnums, ImageUrl, UITestID } from '../../Constants/Index';

const { isAndroid } = BbkUtils;
interface ILocationItem {
  typeDesc: string;
  address: string;
  showCarIcon?: boolean;
}
const LocationItem: React.FC<ILocationItem> = memo(
  ({ typeDesc, address, showCarIcon }: ILocationItem) => {
    return (
      <View className={c2xStyles.flexStart}>
        {!!showCarIcon && (
          <Image
            src={`${ImageUrl.DIMG04_PATH}1tg2512000jpown6j95F3.png`}
            className={c2xStyles.carIcon}
          />
        )}
        <View className={c2xStyles.locationText}>
          <Text className={c2xStyles.textDesc} numberOfLines={1}>
            {typeDesc}
          </Text>
          <Text className={c2xStyles.textDesc} numberOfLines={1}>
            {address}
          </Text>
        </View>
      </View>
    );
  },
);
interface ISelfServiceBlock {
  isSelfService: boolean;
}
const SelfServiceBlock = memo(({ isSelfService }: ISelfServiceBlock) => {
  if (!isSelfService) return null;
  return (
    <View className={c2xStyles.wrap}>
      <Image
        src={`${ImageUrl.DIMG04_PATH}1tg1612000j7wvouo2E28.png`}
        className={xClassNames(
          c2xStyles.carIcon,
          c2xStyles.carIconInfo,
          isAndroid && c2xStyles.carIconInfoAndroid,
        )}
      />
      <Text className={c2xStyles.textDesc} fontWeight="medium">
        自助取还需预订人现场操作
      </Text>
      <Text className={c2xStyles.textDesc}> 否则需联系门店员工处理</Text>
    </View>
  );
});

const RentalLocationInfo: React.FC<IRentalLocation> = memo(
  ({
    storeGuidInfos,
    onPress,
    isPickupCenter,
    isReturnCenter,
    pickUpStoreSelfServiceInfo,
    showCarIcon = true,
  }: IRentalLocation) => {
    const { GuideTabType } = CommonEnums;
    const handlePress = useCallback(() => {
      onPress(GuideTabType.Pickup);
    }, [onPress, GuideTabType.Pickup]);

    if (!storeGuidInfos?.length) return null;
    const mergeInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.Merge,
    );
    const mapIconUrl = `${ImageUrl.DIMG04_PATH}1tg2812000j7xrmx3264F.png`;
    const isSelfService = pickUpStoreSelfServiceInfo?.isSelfService;

    if (mergeInfo) {
      const { storeGuid, address } = mergeInfo;
      return (
        <>
          <BbkTouchable
            onPress={handlePress}
            testID={UITestID.car_testid_comp_vendor_modal_location_map}
            className={c2xStyles.wrapper}
          >
            <XViewExposure
              className={c2xStyles.locationTextContent}
              testID={CarLog.LogExposure({
                name: '曝光-填写页-取还车信息-门店位置',
                info: {
                  pickupway: isPickupCenter
                    ? storeGuid?.split(' ')?.[0]
                    : storeGuid,
                  returnway: isPickupCenter
                    ? storeGuid?.split(' ')?.[0]
                    : storeGuid,
                },
              })}
            >
              <LocationItem
                typeDesc={
                  isPickupCenter ? storeGuid?.split(' ')?.[0] : storeGuid
                }
                address={address}
                showCarIcon={showCarIcon}
              />
            </XViewExposure>
            <Image
              src={mapIconUrl}
              mode="aspectFill"
              className={xClassNames(
                c2xStyles.mapImageStyle,
                c2xStyles.mapImageAbsStyle,
              )}
            />
          </BbkTouchable>
          <SelfServiceBlock isSelfService={isSelfService} />
        </>
      );
    }
    const pickUpInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.PickUp,
    );
    const dropOffInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.DropOff,
    );
    if (pickUpInfo && dropOffInfo) {
      return (
        <>
          <BbkTouchable
            onPress={handlePress}
            className={c2xStyles.wrapper}
            testID={UITestID.car_testid_comp_vendor_modal_location_map}
          >
            <XViewExposure
              className={c2xStyles.locationTextContent}
              testID={CarLog.LogExposure({
                name: '曝光-填写页-取还车信息-门店位置',
                info: {
                  pickupway: isPickupCenter
                    ? pickUpInfo?.storeGuid?.split(' ')?.[0]
                    : pickUpInfo?.storeGuid,
                  returnway: isReturnCenter
                    ? dropOffInfo?.storeGuid?.split(' ')?.[0]
                    : dropOffInfo?.storeGuid,
                },
              })}
            >
              <LocationItem
                typeDesc={`取-${
                  isPickupCenter
                    ? pickUpInfo?.storeGuid?.split(' ')?.[0]
                    : pickUpInfo?.storeGuid
                }`}
                address={pickUpInfo?.address}
                showCarIcon={showCarIcon}
              />
              {!!showCarIcon && <View className={c2xStyles.locationBarLine} />}
              <LocationItem
                typeDesc={`还-${
                  isReturnCenter
                    ? dropOffInfo?.storeGuid?.split(' ')?.[0]
                    : dropOffInfo?.storeGuid
                }`}
                address={dropOffInfo?.address}
                showCarIcon={showCarIcon}
              />
            </XViewExposure>
            <Image
              src={mapIconUrl}
              mode="aspectFill"
              className={xClassNames(
                c2xStyles.mapImageStyle,
                c2xStyles.mapImageAbsStyle2,
              )}
            />
          </BbkTouchable>
          <SelfServiceBlock isSelfService={isSelfService} />
        </>
      );
    }
    return null;
  },
);
export default RentalLocationInfo;
