import { debounce as lodashDebounce } from 'lodash-es';
import {
  xMergeStyles,
  XView as View,
  xRouter,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useCallback } from 'react';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import TermsBar from '@ctrip/rn_com_car/dist/src/Components/Basic/TermsBar';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { font, color, icon, space } from '@ctrip/rn_com_car/dist/src/Tokens';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './gpdrComponentC2xStyles.module.scss';
import { AppContext, CarLog, Utils } from '../../Util/Index';
import Channel from '../../Util/Channel';
import { Platform, UITestID } from '../../Constants/Index';
import { openURL } from '../../Helpers/WebHelper';
import { ShowPayModeType } from '../../State/Product/Enums';
import texts from './Texts';
import { IGPDRComponent } from './Types';
import c2xCommonStyles from '../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, autoProtocol, selector } = BbkUtils;
const styles = StyleSheet.create({
  checkTerms: {
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    paddingTop: getPixel(40),
    backgroundColor: color.C_EDF2F8,
  },
  checkTermsText: { ...font.caption1LightStyle, color: color.fontSecondary },
  checkTermsBg: { backgroundColor: color.newBookingGrayBg },
  termsGap: { marginBottom: space.spaceXS },
  highLightStyle: {
    ...font.body3LightFlatStyle,
    color: color.bookingOptimizationBlue,
    textDecorationLine: 'none',
  },
  highLightStyleGs3: {
    ...font.body3LightFlatStyle,
    color: color.bookingOptimizationBlue,
    textDecorationLine: 'none',
    lineHeight: getPixel(44),
  },
  textStyle: { ...font.body3LightFlatStyle, color: color.fontBlueDark },
  bookingOptimizationCheckTerms: {
    paddingLeft: getPixel(56),
    paddingRight: getPixel(56),
  },
  bookingOptimizationCheckTermsBg: { backgroundColor: color.C_EDF2F8 },
  textStyleGS3: {
    ...font.body3LightFlatStyle,
    color: color.fontGrayBlue,
    lineHeight: getPixel(44),
  },
});

const GPDRComponent: React.FC<IGPDRComponent> = ({
  supplierData,
  showCtripDepositFee,
  onPressCtripDepositFee,
  onPressTandC,
  onPressSupplier,
  onPressExplain,
  generalDescList = [],
  showPayMode,
  isSecretBox,
  secretBoxRuleUrl,
  showPersonalAuthCheck,
  isShowSelfServiceInstruction,
  onPressSelfServiceInstruction,
  setBusinessLicenseVisible,
  isBookIsdGS3,
}) => {
  const {
    bookingPolicyTip,
    storePolicy,
    bookingTerms,
    authorizationStatement,
    chengXinFenServiceAgreement,
    secretBoxRule,
    selfServiceInstruction,
  } = texts;
  const tipWithPayMode = {
    [ShowPayModeType.Store]: '预订',
    [ShowPayModeType.Online]: '支付',
    [ShowPayModeType.PreOnline]: '预付订金',
  };
  let termsText = '';
  // 请您在支付前仔细阅读预订条款
  const CNContent = `请您在${tipWithPayMode[showPayMode]}前仔细阅读${
    bookingTerms
  }`;

  const termsArr = [CNContent];
  // 个人身份信息授权声明
  if (!showPersonalAuthCheck) {
    termsArr.push(authorizationStatement);
  }
  // 程信分免押服务协议
  if (showCtripDepositFee) {
    termsArr.push(chengXinFenServiceAgreement);
  }
  // 盲盒预订规则说明
  if (isSecretBox) {
    termsArr.push(secretBoxRule);
  }
  // 自助取还说明
  if (isShowSelfServiceInstruction) {
    termsArr.push(selfServiceInstruction);
  }
  // 连接逻辑：一个直接展示；两个用“以及”连接展示；三个及以上后两个用“以及”连接，其他用“、”连接
  if (termsArr.length > 1) {
    termsText = `${[...termsArr]
      .splice(0, termsArr.length - 1)
      .join('、')}${texts.Business_oneRule(termsArr[termsArr.length - 1])}`;
  } else {
    [termsText] = termsArr;
  }

  const onPressBusLicense = () => {
    if (Utils.isCtripIsd()) {
      setBusinessLicenseVisible(true);
    } else {
      onPressSupplier();
    }
  };

  const handlePressPrecondition = useCallback(
    lodashDebounce(
      () => {
        CarLog.LogCode({ name: '点击_填写页_预订条款' });

        if (Utils.isCtripIsd()) {
          AppContext.PageInstance.push(Channel.getPageId().IsdAgreement.EN);
          return;
        }

        const { Agreement } = Platform.CAR_CROSS_URL;
        const forwardurl = autoProtocol(
          Utils.isCtripIsd() ? Agreement.ISD : Agreement.OSD,
        );
        openURL(forwardurl);
      },
      100,
      {
        leading: true,
        trailing: false,
      },
    ),
    [],
  );
  const onPressSecretBoxRule = useMemoizedFn(() => {
    xRouter.navigateTo({ url: secretBoxRuleUrl });
  });

  return (
    <View
      className={c2xStyles.checkTerms}
      style={xMergeStyles([
        Utils.isCtripIsd() && styles.checkTermsBg,
        Utils.isCtripIsd() && styles.bookingOptimizationCheckTerms,
        Utils.isCtripIsd() && styles.bookingOptimizationCheckTermsBg,
      ])}
    >
      {selector(
        generalDescList.length > 0,
        <View className={!isBookIsdGS3 && c2xStyles.generalDescWrap}>
          {generalDescList.map(desc => (
            <Text
              key={desc}
              className={classNames(
                isBookIsdGS3
                  ? c2xStyles.generalDescTextGs3
                  : c2xStyles.generalDescText,
              )}
            >
              {desc}
            </Text>
          ))}
        </View>,
      )}
      {
        // 展示文案：门店费用、违章及车损处理流程等请查看门店政策
        Utils.isCtripIsd() && (
          <TermsBar
            hasCheckbox={false}
            text={[bookingPolicyTip(storePolicy)]}
            keywords={[storePolicy]}
            funcObj={{ [storePolicy]: onPressTandC }}
            highLightStyle={
              isBookIsdGS3 ? styles.highLightStyleGs3 : styles.highLightStyle
            }
            textStyle={isBookIsdGS3 ? styles.textStyleGS3 : styles.textStyle}
            style={!isBookIsdGS3 && styles.termsGap}
          />
        )
      }
      <View
        className={
          Utils.isCtripIsd() &&
          !isBookIsdGS3 &&
          c2xStyles.bookingOptimizationTermsWrap
        }
      >
        {!!termsText && (
          <TermsBar
            text={[termsText]}
            keywords={[
              bookingTerms,
              authorizationStatement,
              chengXinFenServiceAgreement,
              secretBoxRule,
              selfServiceInstruction,
            ]}
            funcObj={{
              [bookingTerms]: handlePressPrecondition,
              [authorizationStatement]: onPressExplain,
              [chengXinFenServiceAgreement]: onPressCtripDepositFee,
              [secretBoxRule]: onPressSecretBoxRule,
              [selfServiceInstruction]: onPressSelfServiceInstruction,
            }}
            highLightStyle={
              Utils.isCtripIsd()
                ? isBookIsdGS3
                  ? styles.highLightStyleGs3
                  : styles.highLightStyle
                : font.caption1LightStyle
            }
            textStyle={
              Utils.isCtripIsd()
                ? isBookIsdGS3
                  ? styles.textStyleGS3
                  : styles.textStyle
                : styles.checkTermsText
            }
            style={styles.checkTerms}
          />
        )}
      </View>
      {!!supplierData && Utils.isCtripIsd() && !isSecretBox && (
        <BusinessLicense
          licenseDesc={supplierData.licenseDesc}
          supplierName={supplierData.companyName}
          supplierUrl={supplierData.licenseImgUrl}
          onPress={onPressBusLicense}
          isBookIsdGS3={isBookIsdGS3}
        />
      )}
      {isSecretBox && (
        <View className={c2xStyles.secretBoxRuleWrap}>
          <Text className={c2xStyles.textStyle}>{texts.secretBoxRenewTip}</Text>
        </View>
      )}
    </View>
  );
};

type TBusinessLicense = {
  licenseDesc?: string;
  supplierName?: string;
  supplierUrl?: string;
  onPress?: (data) => void;
  isBookIsdGS3?: boolean;
};

export const BusinessLicense = ({
  licenseDesc,
  supplierName,
  supplierUrl,
  onPress,
  isBookIsdGS3,
}: TBusinessLicense) => {
  if (!supplierName || !supplierUrl) return null;
  return (
    <Touchable
      onPress={() => {
        CarLog.LogCode({ name: '点击_填写页_供应商营业执照' });
        onPress(true);
      }}
      testID={UITestID.car_testid_gpdr_license}
      className={c2xStyles.businessLicenseWrap}
    >
      <Text
        className={classNames(
          c2xCommonStyles.c2xTextDefaultCss,

          Utils.isCtripIsd()
            ? c2xStyles.licienseTextStyle
            : c2xStyles.checkTermsText,
          isBookIsdGS3 && c2xStyles.licienseTextStyleGs3,
        )}
      >
        {`${licenseDesc}：${supplierName}`}
      </Text>
      <Text
        type="icon"
        className={classNames(
          c2xCommonStyles.c2xTextDefaultColor,

          Utils.isCtripIsd()
            ? c2xStyles.bookingOptimizationCheckTermsIcon
            : c2xStyles.checkTermsIcon,
          isBookIsdGS3 && c2xStyles.licienseTextStyleGs3,
        )}
      >
        {icon.circleQuestion}
      </Text>
    </Touchable>
  );
};

export default withTheme(GPDRComponent);
