import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { XView as View, xClassNames, XImage } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import color from '@ctrip/rn_com_car/dist/src/Tokens/tokens/color';
import { getPixel, isIos } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import * as ImageUrl from '../../Constants/ImageUrl';
import c2xStyles from './rentalDateC2xStyles.module.scss';
import SkeletonLoading, {
  PageType,
} from '../../ComponentBusiness/SkeletonLoading';

interface IRentalDate {
  pTime: string;
  rTime: string;
  duration: string;
  isShelves2?: boolean;
  showTimeIcon?: boolean;
  isShowLoading?: boolean;
  isShelves3?: boolean;
}
const styles = StyleSheet.create({
  loadingBg: {
    backgroundColor: color.white,
    marginBottom: getPixel(20),
  },
});

const RentalDate: React.FC<IRentalDate> = memo(
  ({
    pTime,
    rTime,
    duration,
    isShelves2,
    showTimeIcon = true,
    isShowLoading,
    isShelves3 = false,
  }: IRentalDate) => {
    if (!pTime || !rTime || !duration) return null;
    if (isShowLoading) {
      return (
        <SkeletonLoading
          visible={true}
          style={styles.loadingBg}
          pageName={PageType.BookTimeISD}
        />
      );
    }
    const hasYear = pTime.includes('年') && rTime.includes('年') && isShelves3;
    return (
      <View
        className={xClassNames(c2xStyles.wrap, isShelves2 && c2xStyles.mt30)}
      >
        {!!isShelves2 && !!showTimeIcon && (
          <XImage
            src={`${ImageUrl.DIMG04_PATH}1tg4d12000jpowpcc4802.png`}
            className={c2xStyles.timePng}
          />
        )}
        <Text
          className={xClassNames(c2xStyles.pTime, hasYear && c2xStyles.small)}
        >
          {pTime}
        </Text>
        <View
          className={
            isShelves2 ? c2xStyles.durationLineWrap : c2xStyles.durationWrap
          }
        >
          <Text
            className={xClassNames(
              c2xStyles.duration,
              isShelves2 && c2xStyles.duration24,
              isIos && c2xStyles.durantion34,
              hasYear && c2xStyles.small,
            )}
          >
            {duration}
          </Text>
        </View>
        <Text
          className={xClassNames(c2xStyles.rTime, hasYear && c2xStyles.small)}
        >
          {rTime}
        </Text>
      </View>
    );
  },
);

export default RentalDate;
