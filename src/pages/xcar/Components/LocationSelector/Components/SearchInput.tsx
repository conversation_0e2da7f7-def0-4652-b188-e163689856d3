import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { xClassNames as classNames, XView as View } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkInput from '@ctrip/rn_com_car/dist/src/Components/Basic/Input';
import { icon, font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './searchInputC2xStyles.module.scss';
import { CarLog, Utils } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import texts from '../Texts';
import { AppType, PageType } from '../Types';
import { CityInfo } from '../../../Types/Dto/GetCityListType';

const { getPixel, useMemoizedFn, getLineHeight } = BbkUtils;

const DelayTime = 380;
const styles = StyleSheet.create({
  inputWrapper: {
    marginTop: getPixel(0),
    paddingTop: getPixel(14),
    paddingBottom: getPixel(14),
  },
  input: {
    ...font.areaLabel,
    lineHeight: getLineHeight(40),
    color: color.grayBase,
  },
  coveredInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 0,
    marginTop: getPixel(0),
    paddingBottom: getPixel(0),
    borderBottomColor: color.transparent,
    borderBottomWidth: 0,
  },
});

export interface ISearchInput {
  pageType: PageType;
  initCity?: CityInfo;
  selectedCity?: CityInfo;
  onChange?: (value: string) => void;
  handleCityPress: (city: CityInfo) => void;
  changePageType: (pageType: PageType) => void;
  isSearchInputFocus?: boolean;
  mainPageSearchFrom?: string;
}

const SearchInput: React.FC<ISearchInput> = ({
  pageType,
  initCity,
  selectedCity,
  onChange,
  handleCityPress,
  changePageType,
  isSearchInputFocus,
  mainPageSearchFrom,
}) => {
  const [isCitySelector, setIsCitySelector] = useState(
    pageType === PageType.City,
  );
  const [isShowCancel, setIsShowCancel] = useState(false);
  const [isFocus, setIsFocus] = useState(false);
  const [keyword, setKeyword] = useState('');
  const timeout = useRef(null);
  const searchInput = useRef(null);
  const focusTimer = useRef(null);

  const init = useMemoizedFn(() => {
    Keyboard.dismiss();
    setIsFocus(false);
    setKeyword('');
    setIsShowCancel(false);
  });

  const goChooseCity = useMemoizedFn(() => {
    init();
    setIsCitySelector(true);
    changePageType(PageType.City);
    CarLog.LogCode({
      name: '点击_城市区域页_修改城市',
      info: {
        cityPageType: pageType,
        mainPageSearchFrom,
        cityPageTabFrom: Utils.isCtripIsd() ? AppType.ISD : AppType.OSD,
      },
    });
  });
  const goChooseArea = useMemoizedFn(() => {
    setIsCitySelector(false);
    handleCityPress(initCity);
    CarLog.LogCode({
      name: '点击_城市区域页_选择地点',
      info: {
        cityPageType: pageType,
        mainPageSearchFrom,
        cityPageTabFrom: Utils.isCtripIsd() ? AppType.ISD : AppType.OSD,
      },
    });
  });
  const onBlur = useMemoizedFn(() => {
    Keyboard.dismiss();
    setIsFocus(false);
    if (!keyword) {
      setIsShowCancel(false);
    }
  });
  const onFocus = useMemoizedFn(() => {
    setIsShowCancel(true);
    setIsFocus(true);
    CarLog.LogCode({
      name: isCitySelector
        ? '点击_城市区域页_混搜搜索框'
        : '点击_城市区域页_地点搜索框',
      info: {
        cityPageType: pageType,
        mainPageSearchFrom,
        cityPageTabFrom: Utils.isCtripIsd() ? AppType.ISD : AppType.OSD,
      },
    });
  });
  const onChangeText = useMemoizedFn((value: string) => {
    setKeyword(value);

    // TA: fireEvent.changeText 改变value时不会触发onChange事件，这里需要手动触发
    // @ts-ignore
    // eslint-disable-next-line no-underscore-dangle
    if (global.__TESTING_AUTOMATION__) {
      onChange(value);
    }
  });
  const onClearText = useMemoizedFn(() => {
    setKeyword('');
    onChange('');
  });
  const onCancelInput = useMemoizedFn(() => {
    Keyboard.dismiss();
    setIsShowCancel(false);
    setIsFocus(false);
    onClearText();
    if (isCitySelector) {
      CarLog.LogCode({ name: '点击_城市区域页_搜索框_取消输入' });
    }
  });
  const handleChange = useMemoizedFn((event: any) => {
    event.persist();
    if (timeout.current) {
      clearTimeout(timeout.current);
    }
    timeout.current = setTimeout(() => {
      onChange(event?.nativeEvent?.text);
    }, DelayTime);
  });
  const searchIconViewStyle = useMemo(() => {
    return { marginLeft: getPixel(isCitySelector ? 35 : 19) };
  }, [isCitySelector]);

  useEffect(() => {
    init();
    setIsCitySelector(pageType === PageType.City);
  }, [init, pageType]);

  const inputRefHandler = ref => {
    searchInput.current = ref;
  };

  useEffect(() => {
    clearTimeout(focusTimer?.current);
    focusTimer.current = setTimeout(() => {
      if (pageType === PageType.Area) {
        if (isSearchInputFocus) {
          searchInput?.current?.focus?.();
        } else {
          searchInput?.current?.blur?.();
        }
      }
    }, 100);
  }, [pageType, isSearchInputFocus, selectedCity]);

  return (
    <View className={classNames(c2xStyles.contain, c2xStyles.containPad)}>
      <View className={c2xStyles.inputBackground}>
        {!isCitySelector && (
          <BbkTouchable
            className={c2xStyles.selectedCity}
            testID={UITestID.car_testid_page_location_go_city}
            onPress={goChooseCity}
          >
            <BbkText className={c2xStyles.selectedCityText} numberOfLines={1}>
              {selectedCity.name}
            </BbkText>
            <BbkText type="icon" className={c2xStyles.arrowDownIcon}>
              {icon.arrowDown}
            </BbkText>
            <BbkText className={c2xStyles.divider} />
          </BbkTouchable>
        )}
        <View className={c2xStyles.inputContain}>
          <BbkInput
            style={styles.inputWrapper}
            testID={UITestID.car_testid_page_location_searchinput_input}
            inputStyle={styles.input}
            value={keyword}
            title={
              isCitySelector
                ? texts.newSearchCityPlaceholder
                : texts.newSearchAreaPlaceholder
            }
            placeholder={
              isCitySelector
                ? texts.newSearchCityPlaceholder
                : texts.newSearchAreaPlaceholder
            }
            isShowTitle={false}
            placeholderTextColor={color.grayBase}
            inputWrapperStyle={styles.coveredInputWrapper}
            showClearWhileEditing={false}
            onChangeText={onChangeText}
            onChange={handleChange}
            onInputFocus={onFocus}
            onInputBlur={onBlur}
            returnKeyType="search"
            returnKeyLabel="search"
            leftChildren={
              <BbkText
                className={c2xStyles.searchIcon}
                style={searchIconViewStyle}
                type="icon"
              >
                {icon.search}
              </BbkText>
            }
            rightChildren={
              !!keyword &&
              isFocus && (
                <BbkTouchable
                  testID={UITestID.car_testid_page_location_searchinput_clear}
                  onPress={onClearText}
                >
                  <BbkText type="icon" className={c2xStyles.deleteAllIcon}>
                    {icon.circleCrossFilled}
                  </BbkText>
                </BbkTouchable>
              )
            }
            inputRefHandler={inputRefHandler}
          />
        </View>
        {!!isCitySelector && !isShowCancel && (
          <BbkTouchable
            testID={UITestID.car_testid_page_location_searchinput_go_area}
            className={c2xStyles.chooseArea}
            onPress={goChooseArea}
          >
            <BbkText className={c2xStyles.divider} />
            <BbkText className={c2xStyles.chooseAreaText}>
              {texts.chooseArea}
            </BbkText>
          </BbkTouchable>
        )}
      </View>
      {isShowCancel && (
        <BbkTouchable
          testID={UITestID.car_testid_page_location_searchinput_cancel}
          onPress={onCancelInput}
        >
          <BbkText className={c2xStyles.cancelText}>
            {texts.searchCancle}
          </BbkText>
        </BbkTouchable>
      )}
    </View>
  );
};

export default memo(SearchInput);
