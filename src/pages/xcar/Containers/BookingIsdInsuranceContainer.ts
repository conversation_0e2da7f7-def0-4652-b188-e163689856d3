import { connect } from 'react-redux';
import Service from '../Pages/BookingIsd/Component/ISD/Service';

import {
  getCurInsPackageId,
  getAddOnCodes,
  getShowPayMode,
  getSelectedInsuranceId,
} from '../State/Product/Selectors';
import {
  getPriceLoading,
  getPrice,
  getIsEasyLife2024,
} from '../State/Booking/Selectors';
import { changeSelectInsurance } from '../State/Product/Actions';
import { getQConfig } from '../State/Common/Selectors';
import { getVendorInfo, getIsRP } from '../Global/Cache/ProductSelectors';
import { ApiResCode } from '../Constants/Index';

const mapStateToProps = state => ({
  insuranceFlag: getQConfig(state)?.insuranceFlag,
  moreServiceTip: getQConfig(state)?.moreServiceTip,
  addOnCodes: getAddOnCodes(state),
  isPriceLoading: getPriceLoading(state),
  curInsPackageId: getCurInsPackageId(state),
  showPayMode: getShowPayMode(state),
  selectedInsuranceId: getSelectedInsuranceId(state),
  insuranceAvailable: getPrice()?.insuranceAvailable,
  isEhai: getVendorInfo()?.vendorCode === ApiResCode.ehaiVendorCode,
  isEasyLife2024: getIsEasyLife2024(state),
  isRP: getIsRP(),
});

const mapDispatchToProps = dispatch => ({
  changeSelectInsurance: data => {
    dispatch(changeSelectInsurance(data));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(Service);
