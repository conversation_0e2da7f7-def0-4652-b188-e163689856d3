import connect from '../WithRedux';
import BookingContainer from '../Pages/BookingIsd/Index';
import texts from '../Pages/BookingIsd/Texts';
import {
  getDriverInfo,
  getCurrentPriceInfo,
  getPrice,
  getPriceLoading,
  getPaymentParams,
  getOrderData,
  getOrderPrice,
  getPreLicensData,
  getPayRequestId,
  getIsSnapShotFinish,
  getSupplierModalVisible,
  getDepositIntroduceModalVisible,
  getInsConfirmReqParam,
  getOrderParams,
  getCreateInsLoadingPopVisible,
  getDriversMap,
  getNeedValidateOrder,
  getCtripRentNeedValidateOrder,
  getEhiFreeDepositModalVisible,
  getIousInfo,
  getSelectedLoanPayStageCount,
  isNaquhuaValidated,
  getEasyLifePopVisible,
  getHasVehicleConfig,
  getActivityDetail,
  getCreateOrderFailModalVisible,
  getUniqueOrderModalVisible,
  getFlightErrorModalVisible,
  getPriceTimerLoading,
  getRebookPenalty,
  getResCancelFeeRebook,
  validateIsMergeDeposit,
  getCheckFlightNoLoading,
  getDepositRateDescriptionModalVisible,
  getIsBusinessLicenseModalVisible,
  getAdditionalDriverDesc,
  getIsEasyLife2024,
  getFuelDescriptionModalVisible,
  getOsdModifyOrderNote,
  getLocalContactsData,
  getBookingStoreGuidInfosGS3,
} from '../State/Booking/Selectors';
import { setEtcIntroModal } from '../State/VendorList/Actions';
import {
  getIsEtcIntroModalVisible,
  getCurFloor,
  getVehicleIndex,
} from '../State/VendorList/Selectors';
import {
  getSelectedInsuranceId,
  getPayMode,
  getIsFail,
  getCurInsPackageId,
  getDepositPayType,
  getIsPriceFail,
  getIsShowPriceConfirm,
  getIsLoading,
  getAddOnCodes,
  getDriverLicenseItems,
  getCurDriverLicense,
  getPickUpAreaCode,
} from '../State/Product/Selectors';
import {
  createOrder,
  changeFormData,
  saveSnapshot,
  clear,
  changeModalStatus,
  resetLoading,
  setPriceChangePopIsShow,
  setInsRemindPopIsShow,
  setCreateInsLoadingPopIsShow,
  setCreateInsFailPopIsShow,
  insConfirmCallBack,
  setSelectedLoanPayStageCount,
  setPassengerError,
  setEasyLifePopIsShow,
  setRebookPenalty,
  fetchQueryCancelFeeRebook,
  resetCancelFeeRebook,
  setLogInfo,
  setVendorPriceData,
  changeCoupon,
  setFlightDelayRulesModalVisible,
  setDepositRateDescriptionModalVisible,
  setBusinessLicenseVisible,
  setEhiFreeDepositModalVisible,
  setFuelDescriptionModalVisible,
  changeLocalContactsData,
} from '../State/Booking/Actions';
import { setRebookParamsOsd } from '../State/ModifyOrder/Actions';
import { setSupplierData } from '../State/SupplierData/Actions';
import {
  fetchApiDriverList,
  selectDriver,
  maskerDriver,
  unMaskerDriver,
  clear as clearDriverList,
} from '../State/DriverList/Actions';
import {
  queryPriceInfo,
  queryProduct,
  reset as resetProduct,
  showPriceConfirm,
  validateIsDownGrade,
  changeSelectInsurance,
  selectCurDriverLicense,
  queryLicencePolicy,
  queryEquipmentInfo,
  queryCountrysInfo,
} from '../State/Product/Actions';
import { setCouponPreValidationModalVisible } from '../State/Coupon/Actions';
import {
  getAuthenStatusTicket,
  getIsLogin,
  getCancleZhimaModalData,
} from '../State/Sesame/Selectors';
import { isCtripCreditRent } from '../State/Sesame/Mappers';
import {
  getPassenger,
  getIsOnMask,
  getPassengerList,
  getPassengerIDCard,
} from '../State/DriverList/Selectors';
import {
  getCurPackageIsEasyLife,
  getFlightDelayRules,
  getCurDepositPayInfo,
  getCurCtripInsuranceIds,
  getCurPriceInfo,
  getCurProductInfo,
  getIsKlb,
  hasExtrasProducts,
} from '../State/Product/Mappers';

import { isDebugMode } from '../State/Debug/Selectors';
import {
  getProductRentalLocationInfo,
  getPickUpTime,
  getProductDropOffTime,
} from '../State/LocationAndDate/Selectors';
import {
  getRebookParams,
  getRebookParamsOsd,
} from '../State/ModifyOrder/CommonSelector';
import { initSesameAuthState } from '../State/Sesame/Actions';
import {
  getProductReq,
  getPriceVersion,
  getPriceChangeCode,
  getBookPriceTrackInfo,
  getProductRes,
  getDownGradeInfo,
  getNeedDownGrade,
  getNewRentCenterId,
  getNewDropOffRentCenterId,
  getModifyOrderDesc,
  getBookingConfirmData,
  getOptionalContactMethods,
} from '../Global/Cache/ProductSelectors';
import {
  setVendorListModalData,
  addSaleOutList,
  addVehicleSaleOutList,
  setLicenseModalData,
} from '../State/List/Actions';
import { addRecommendSaleOutList } from '../State/RecommendVehicle/Actions';
import {
  setListStatusData,
  listStatusKeyList,
} from '../Global/Cache/ListReqAndResData';
import { AppContext, Channel, Utils } from '../Util/Index';
import { ProductReqAndResData } from '../Global/Cache/Index';
import { getQConfig } from '../State/Common/Selectors';
import { getImAddressWithQconfig } from '../State/Common/Mapper';
import { getIsVehicle2 } from '../Global/Cache/ListResSelectors';
import { getProductTraceData } from '../State/Product/Method';
import {
  getNewRecommendType,
  getSaleOutList,
  getActiveGroupId,
} from '../State/List/Selectors';

const mapStateToProps = state => {
  const rebookParams = getRebookParams(state);
  const rebookParamsOsd = getRebookParamsOsd(state);
  const prices = getPrice();
  const priceInfo = getCurrentPriceInfo(state);
  const orderPriceInfo = getOrderPrice(state);
  const curInsPackageId = getCurInsPackageId(state);
  const productInfo = getCurProductInfo(curInsPackageId) || {};
  const productPriceInfo = getCurPriceInfo(curInsPackageId) || {};
  const { ageRestriction = {}, cancelRule = {} } = productPriceInfo;
  const { maxDriverAge, minDriverAge, youngDriverAge, oldDriverAge } =
    ageRestriction;
  const { cancelEncourage } = cancelRule;
  return {
    addInstructData: getAdditionalDriverDesc(state),
    hasBookingConfirmInfo: !!getBookingConfirmData(),
    isRebook: !!rebookParams?.vehicleId,
    ctripOrderId: Utils.isCtripOsd()
      ? rebookParamsOsd?.ctripOrderId
      : rebookParams?.ctripOrderId,
    rebookPenalty: getRebookPenalty(state),
    resCancelFeeRebook: getResCancelFeeRebook(state),
    config: getQConfig(state),
    oldAge: Utils.isCtripIsd() ? oldDriverAge : maxDriverAge,
    yongAge: Utils.isCtripIsd() ? youngDriverAge : minDriverAge,
    isFail: getIsFail(state),
    isChecked: true,
    driverInfo: getDriverInfo(state),
    needFlightNo: productInfo?.needFlightNo,
    positivePolicies: prices && prices.positivePolicies,
    activityDetail: getActivityDetail(state),
    adjustPriceDetail: prices && prices.adjustPriceDetail,
    cancelRuleInfo: prices && prices.cancelRuleInfo,
    invoiceInfo: prices && prices.invoiceInfo,
    confirmInfo: prices && prices.confirmInfo,
    curDepositPayInfo: getCurDepositPayInfo(),
    isPriceLoading: getPriceLoading(state),
    isPriceTimerLoading: getPriceTimerLoading(state),
    isProductLoading: getIsLoading(state),
    isMaskLoading: state.Booking.isMaskLoading,
    orderData: getOrderData(state),
    orderId: state.Booking.orderId,
    payParams: getPaymentParams(state),
    payAmount: orderPriceInfo && orderPriceInfo.payAmount,
    payMode: getPayMode(state),
    payRequestId: getPayRequestId(state),
    couponList: prices && prices.couponList,
    preferentialTips: prices && prices.preferentialTips,
    passenger: getPassenger(state),
    passengerIDCard: getPassengerIDCard(state),
    isEasyLife: getCurPackageIsEasyLife(),
    authenStatusTicket: getAuthenStatusTicket(state),
    isLogin: getIsLogin(state),
    showPayMode: priceInfo && priceInfo.showPayMode,
    isOnlyCreditCard: priceInfo && priceInfo.payType && priceInfo.payType === 4,
    preLicensData: getPreLicensData(state),
    flightDelayRules: getFlightDelayRules(),
    isDebugMode: isDebugMode(state),
    isSnapShotFinish: getIsSnapShotFinish(state),
    isOnMask: getIsOnMask(state),
    passengerList: getPassengerList(state),
    depositPayType: getDepositPayType(state),
    isCtripCreditRent: isCtripCreditRent(state),
    productReq: getProductReq(),
    productRes: getProductRes(),
    curInsPackageId,
    supplierModalVisible: getSupplierModalVisible(state),
    depositIntroduceModalVisible: getDepositIntroduceModalVisible(state),
    productRentalLocationInfo: getProductRentalLocationInfo(state),
    selectedInsuranceId: getSelectedInsuranceId(state),
    bookPriceTrackInfo: getBookPriceTrackInfo(),
    curCtripInsuranceIds: getCurCtripInsuranceIds(curInsPackageId),
    insConfirmReqParam: getInsConfirmReqParam(state),
    orderParams: getOrderParams(state),
    createInsLoadingPopVisible: getCreateInsLoadingPopVisible(state),
    priceVersion: getPriceVersion(),
    priceChangeCode: getPriceChangeCode(),
    driversMap: getDriversMap(state),
    needValidateOrder: getNeedValidateOrder(state),
    ctripRentNeedValidateOrder: getCtripRentNeedValidateOrder(state),
    isPriceFail: getIsPriceFail(state),
    ehiFreeDepositModalVisible: getEhiFreeDepositModalVisible(state),
    downGradeInfo: getDownGradeInfo(),
    needDownGrade: getNeedDownGrade(),
    cancelModalData: getCancleZhimaModalData(state),
    iousInfo: getIousInfo(state),
    selectedLoanPayStageCount: getSelectedLoanPayStageCount(state),
    isNaquhuaValidated: isNaquhuaValidated(state),
    rentCenterId: getNewRentCenterId(),
    dropOffRentCenterId: getNewDropOffRentCenterId(),
    membershipPerception: prices && prices.membershipPerception,
    isShowPriceConfirm: getIsShowPriceConfirm(state),
    easyLifePopVisible: getEasyLifePopVisible(state),
    hasVehicleConfig: getHasVehicleConfig(state),
    createOrderFailModalVisible: getCreateOrderFailModalVisible(state),
    uniqueOrderModalVisible: getUniqueOrderModalVisible(state),
    flightErrorModalVisible: getFlightErrorModalVisible(state),
    ptime: getPickUpTime(state),
    rtime: getProductDropOffTime(state),
    addOnCodes: getAddOnCodes(state),
    modifyOrderDesc: getModifyOrderDesc(),
    isMergeDeposit: validateIsMergeDeposit(state),
    checkFlightNoLoading: getCheckFlightNoLoading(state),
    toolBoxCustomerJumpUrl: getImAddressWithQconfig(state, {
      pageId: Channel.getPageId().Book.ID,
      isPreSale: 1,
    }),
    agreeSubmitName:
      (priceInfo && priceInfo.agreeSubmitName) || texts.agreeAndBook,
    isKlb: getIsKlb(state),
    depositRateDescriptionModalVisible:
      getDepositRateDescriptionModalVisible(state),
    currenctTotalPrice: priceInfo?.currenctPriceInfo?.totalPrice,
    driverLicenseItems: getDriverLicenseItems(state),
    curDriverLicense: getCurDriverLicense(state),
    isBusinessLicenseModalVisible: getIsBusinessLicenseModalVisible(state),
    isRefactor: getIsVehicle2(),
    isEasyLife2024: getIsEasyLife2024(state),
    hasExtrasProducts: hasExtrasProducts(state),
    isFuelDescriptionModalVisible: getFuelDescriptionModalVisible(state),
    isRebookOsd: !!getRebookParamsOsd(state)?.ctripOrderId,
    osdModifyOrderNote: getOsdModifyOrderNote(state),
    pickUpAreaCode: getPickUpAreaCode(state),
    optionalContactMethods: getOptionalContactMethods(),
    localContactsData: getLocalContactsData(state),
    logBaseInfo: getProductTraceData(),
    newRecommendType: getNewRecommendType(state),
    isEtcIntroModalVisible: getIsEtcIntroModalVisible(state),
    curFloor: getCurFloor(state),
    saleOutList: getSaleOutList(state),
    activeGroupId: getActiveGroupId(state),
    vehicleIndex: getVehicleIndex(state),
    cancelEncourage,
    storeGuidInfos: getBookingStoreGuidInfosGS3(state),
  };
};

const mapDispatchToProps = dispatch => ({
  createOrder: (data?: any) => dispatch(createOrder(data)),
  setRebookPenalty: data => dispatch(setRebookPenalty(data)),
  fetchQueryCancelFeeRebook: data => dispatch(fetchQueryCancelFeeRebook(data)),
  resetCancelFeeRebook: () => dispatch(resetCancelFeeRebook()),
  changeFormData: data => dispatch(changeFormData(data)),
  saveSnapshot: data => dispatch(saveSnapshot(data)),
  fetchApiDriverList: () => dispatch(fetchApiDriverList({})),
  queryPriceInfo: () => dispatch(queryPriceInfo()),
  queryProduct: data => dispatch(queryProduct({ ...data, isBooking: true })),
  initSesameAuthState: () =>
    dispatch(initSesameAuthState({ isInitial: true, isBooking: true })),
  clear: () => {
    if (Utils.isCtripIsd()) {
      ProductReqAndResData.removeData();
      dispatch(resetProduct());
      dispatch(clearDriverList());
    }
    dispatch(setSupplierData(null));
    dispatch(clear());
  },
  changeModalStatus: data =>
    setTimeout(() => dispatch(changeModalStatus(data)), 100),
  selectDriver: data => dispatch(selectDriver(data)),
  showPriceConfirm: () => dispatch(showPriceConfirm(true)),
  resetLoading: (data: boolean, option?: any) =>
    dispatch(resetLoading(data, option)),
  maskerDriver: () => dispatch(maskerDriver({})),
  unMaskerDriver: () => dispatch(unMaskerDriver({})),
  setPriceChangePopIsShow: data => dispatch(setPriceChangePopIsShow(data)),
  setInsRemindPopIsShow: data => dispatch(setInsRemindPopIsShow(data)),
  setCreateInsLoadingIsShow: data =>
    dispatch(setCreateInsLoadingPopIsShow(data)),
  setCreateInsFailPopIsShow: data => dispatch(setCreateInsFailPopIsShow(data)),
  setPassengerError: data => dispatch(setPassengerError(data)),
  refreshList: () => {
    // 清空列表页缓存
    AppContext.setUserFetchCacheId({
      actionType: 'bookingRefreshList',
    });
    // 添加从填写页变价弹层回来的标记
    setListStatusData(listStatusKeyList.priceChangeFromBook, true);
  },
  insConfirmCallBack: data => dispatch(insConfirmCallBack(data)),
  validateIsDownGrade: data => dispatch(validateIsDownGrade(data)),
  setVendorListModalData: data => dispatch(setVendorListModalData(data)),
  setSelectedLoanPayStageCount: data =>
    dispatch(setSelectedLoanPayStageCount(data)),
  setCouponPreValidationModalVisible: (visible, content) =>
    dispatch(setCouponPreValidationModalVisible(visible, content)),
  addSaleOutList: data => {
    dispatch(addSaleOutList(data));
  },
  addRecommendSaleOutList: data => {
    dispatch(addRecommendSaleOutList(data));
  },
  onPressEasyLife: visible => dispatch(setEasyLifePopIsShow(visible)),
  changeSelectInsurance: data => dispatch(changeSelectInsurance(data)),
  setLogInfo: data => dispatch(setLogInfo(data)),
  setVendorPriceData: data => dispatch(setVendorPriceData(data)),
  changeCoupon: coupon => {
    dispatch(changeCoupon(coupon));
    dispatch(queryPriceInfo());
  },
  setFlightDelayRulesModalVisible: (data: boolean) =>
    dispatch(setFlightDelayRulesModalVisible(data)),
  setLicenseModalData: data => dispatch(setLicenseModalData(data)),
  setDepositRateDescriptionModalVisible: (data: boolean) =>
    dispatch(setDepositRateDescriptionModalVisible(data)),
  selectCurDriverLicense: data => dispatch(selectCurDriverLicense(data)),
  queryLicencePolicy: data => dispatch(queryLicencePolicy(data)),
  setBusinessLicenseModalVisible: data =>
    dispatch(setBusinessLicenseVisible(data)),
  setEhiFreeDepositModalVisible: (data: boolean) => {
    dispatch(setEhiFreeDepositModalVisible(data));
  },
  setFuelDescriptionModalVisible: data => {
    dispatch(setFuelDescriptionModalVisible(data));
  },
  queryEquipmentInfo: data =>
    dispatch(queryEquipmentInfo({ productReqParams: data })),
  setRebookParamsOsd: data => dispatch(setRebookParamsOsd(data)),
  queryCountrysInfo: data => {
    dispatch(queryCountrysInfo(data));
  },
  changeLocalContactsData: data => {
    dispatch(changeLocalContactsData(data));
  },
  setEtcIntroModal: data => dispatch(setEtcIntroModal(data)),
  addVehicleSaleOutList: data => {
    dispatch(addVehicleSaleOutList(data));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(BookingContainer);
