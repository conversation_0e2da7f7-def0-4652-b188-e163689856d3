import { connect } from 'react-redux';

import {
  getDriverFeeDesc,
  getFlightErrorCode,
  getPassengerError,
  getFlightErrorTip,
  getLocalContactsData,
} from '../State/Booking/Selectors';
import {
  changeFormData,
  setNameReverse,
  validateFlightNo,
  setFlightErrorTip,
  changeLocalContactsData,
} from '../State/Booking/Actions';
import { getAge } from '../State/DriverAgeAndNumber/Selectors';
import BookingForm from '../Components/Business/BookingFormNew';
import { setAge } from '../State/DriverAgeAndNumber/Actions';
import { queryPriceInfo } from '../State/Product/Actions';
import {
  getCurCertificates,
  getAvailableCertificates,
  getPassengerMasker,
  getDriverMaskInfo,
  getPassengerList,
  getRecommendedPassengers,
  getPassengerIDCard,
} from '../State/DriverList/Selectors';
import { getSesameUserTip, isCreditQualified } from '../State/Sesame/Mappers';
import { getCurInsPackageId } from '../State/Product/Selectors';
import {
  getCurPriceInfo,
  getCurProductInfo,
  getCurPackageIsEasyLife,
  getFlightRuleTips,
  getFlightDelayRules,
  getPriceRes,
  getIsKlb,
} from '../State/Product/Mappers';
import Utils from '../Util/Utils';
import { selectDriver } from '../State/DriverList/Actions';
import { getQConfig } from '../State/Common/Selectors';
import {
  getPickupStoreInfo,
  getOptionalContactMethods,
} from '../Global/Cache/ProductSelectors';

const mapStateToProps = state => {
  const driverInfo = getDriverMaskInfo(state);
  const passenger = getPassengerMasker(state);
  const age = getAge(state);
  const curInsPackageId = getCurInsPackageId(state);
  const productInfo = getCurProductInfo(curInsPackageId) || {};
  const priceInfo = getCurPriceInfo(curInsPackageId) || {};
  const { ageRestriction = {} } = priceInfo;
  // minDriverAge,maxDriverAge: 最大最小驾龄, 怀疑海外误用为驾驶员限制最大最小年龄
  // youngDriverAge, oldDriverAge: 驾驶员限制最大最小年龄，国内使用
  const { maxDriverAge, minDriverAge, youngDriverAge, oldDriverAge } =
    ageRestriction;
  const driverFeeDesc = getDriverFeeDesc(state);
  const {
    driverTips,
    depositLabel,
    driverPickUpMaterialDesc,
    drivingAgeLimit,
  } = getPriceRes();
  return {
    driverTitle: getQConfig(state)?.addInstructData?.title,
    driverInfo,
    age,
    flightErrorCode: getFlightErrorCode(state),
    needFlightNo: productInfo.needFlightNo,
    isAirportStore: getPickupStoreInfo()?.isAirportStore,
    passenger,
    passengerError: getPassengerError(state),
    passengerIDCard: getPassengerIDCard(state),
    maxDriverAge: Utils.isCtripIsd() ? oldDriverAge : maxDriverAge,
    minDriverAge: Utils.isCtripIsd() ? youngDriverAge : minDriverAge,
    driverFeeDesc,
    isEasyLife: getCurPackageIsEasyLife(),
    flightInfoTip: getFlightRuleTips(),
    isPressFlightInfo: getIsKlb(state)
      ? getFlightDelayRules()?.rules?.length > 0
      : !!(getFlightDelayRules() && getFlightDelayRules().content),
    isSesameUserTip: getSesameUserTip(state),
    curCertificates: getCurCertificates(state),
    availableCertificates: getAvailableCertificates(state),
    isCreditQualified: isCreditQualified(state),
    driverTips,
    depositLabel,
    recommendedPassengers: getRecommendedPassengers(state),
    noAnyPassenger: !getPassengerList(state)?.length,
    driverPickUpMaterialDesc,
    drivingAgeLimit,
    flightErrorTip: getFlightErrorTip(state),
    localContactsData: getLocalContactsData(state),
    hasLocalContacts: getOptionalContactMethods().length > 0,
    showFlightNo: productInfo.showFlightNo,
  };
};

const mapDispatchToProps = dispatch => ({
  changeFormData: data => dispatch(changeFormData(data)),
  setFlightErrorTip: data => dispatch(setFlightErrorTip(data)),
  setAge: (age: string) => {
    dispatch(setAge({ age }));
    dispatch(queryPriceInfo());
  },
  selectDriver: data => {
    dispatch(
      selectDriver({
        ...data,
        queryPriceInfo: true,
      }),
    );
  },
  setNameReverse: (nameReverse: boolean) =>
    dispatch(setNameReverse(nameReverse)),
  validateFlightNo: data => dispatch(validateFlightNo(data)),
  changeLocalContactsData: data => dispatch(changeLocalContactsData(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(BookingForm);
