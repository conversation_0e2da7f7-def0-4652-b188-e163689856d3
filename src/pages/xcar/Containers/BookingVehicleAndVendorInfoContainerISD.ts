import { connect } from 'react-redux';
import {
  getBaseResData,
  getVehicleInfoV2,
  getSelfServiceTipInfo,
  getCommodityDescsGS3,
} from '../Global/Cache/ProductSelectors';
import {
  getPickUpTime,
  getProductDropOffTime,
} from '../State/LocationAndDate/Selectors';
import {
  getNewBookingVendorInfo,
  getIsEasyLife2024,
  getResData,
} from '../State/Booking/Selectors';
import VehicleAndVendorInfoVC from '../Pages/BookingIsd/VehicleAndVendorInfoVC';

const mapStateToProps = state => {
  const baseResData = getBaseResData();
  const vehicleInfo = getVehicleInfoV2();
  const vendorInfo = getNewBookingVendorInfo(state);
  const { pickupStoreInfo, returnStoreInfo } = getResData() || {};

  return {
    vehicleImage: vehicleInfo?.imageUrl,
    vehicleName: vehicleInfo?.name,
    licenseTag: vehicleInfo?.license,
    licenseStyle: vehicleInfo?.licenseStyle,
    rentalMustReadTitle: baseResData?.rentalMustReadTitle,
    ptime: getPickUpTime(state),
    rtime: getProductDropOffTime(state),
    vendorName: vendorInfo.vendorName,
    isOptimize: vendorInfo.isOptimize,
    nationalChainTagTitle: vendorInfo.nationalChainTagTitle,
    isEasyLife: vendorInfo.isEasyLife,
    isEasyLife2024: getIsEasyLife2024(state),
    allTagsInfo: vendorInfo.allTagsInfo,
    isNewEnergy: vehicleInfo?.esgInfo?.reducedCarbonEmissionRatio > 0,
    isSelfService: vendorInfo?.isSelfService,
    selfServiceTipInfo: getSelfServiceTipInfo(),
    commodityDescList: getCommodityDescsGS3(),
    vehicleCode: vehicleInfo?.vehicleCode,
    pWayInfo: pickupStoreInfo?.wayInfo,
    rWayInfo: returnStoreInfo?.wayInfo,
  };
};

export default connect(mapStateToProps)(VehicleAndVendorInfoVC);
