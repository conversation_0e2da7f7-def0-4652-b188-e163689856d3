import { connect } from 'react-redux';
import NewCouponAndDeposit from '../Pages/Booking/Component/NewCouponAndDeposit';
import { Utils } from '../Util/Index';
import {
  getNewCouponTip,
  getPrice,
  getNewDepositData,
  validateIsMergeDeposit,
} from '../State/Booking/Selectors';
import {
  changePayMode,
  setEhiFreeDepositModalVisible,
} from '../State/Booking/Actions';
import { changeAllSelectInsurance } from '../State/Product/Actions';

const mapStateToProps = state => {
  const priceInfo = getPrice();
  return {
    couponTip: getNewCouponTip(state),
    depositInfo: priceInfo?.depositInfo,
    trackInfo: priceInfo?.trackInfo,
    isMergeDeposit: validateIsMergeDeposit(state),
    ...getNewDepositData(state),
  };
};

const mapDispatchToProps = dispatch => ({
  changePayMode: data => {
    dispatch(changeAllSelectInsurance([]));
    dispatch(changePayMode(data));
  },
  showEhiFreeDepositModalVisible: () => {
    dispatch(setEhiFreeDepositModalVisible(true));
  },
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(NewCouponAndDeposit);
