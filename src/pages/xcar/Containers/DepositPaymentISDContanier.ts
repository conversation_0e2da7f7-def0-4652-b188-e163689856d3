import { keys as lodashKeys } from 'lodash-es';
import { connect } from 'react-redux';
import DepositPaymentBlockISD from '../Components/Business/DepositPaymentBlockISD';
import type { IPaymentComponent } from '../Components/Business/DepositPaymentBlock';
import {
  getDepositPayInfos,
  getPrice,
  isLateDepositVerVendorBlackList,
} from '../State/Booking/Selectors';
import {
  changePayMode,
  changeModalStatus,
  setEhiFreeDepositModalVisible,
} from '../State/Booking/Actions';
import {
  changeAllSelectInsurance,
  queryPriceInfo,
} from '../State/Product/Actions';
import { getDepositPayType } from '../State/Product/Selectors';
import { getPassenger } from '../State/DriverList/Selectors';
import {
  SesamePageLocation,
  OnAuthenticationDateType,
} from '../State/Sesame/Types';
import {
  getSesameBarTexts,
  isCtripCreditRent,
  getDepositLabelType,
  getSesameOrderId,
  geZhimaNoteInfo,
  getZhimaComplementaryAmount,
} from '../State/Sesame/Mappers';
import {
  onAuthentication,
  onCtripAuthentication,
  onAuthConfirmPress,
} from '../State/Sesame/Actions';
import { getAuthStatus } from '../State/Sesame/Selectors';
import {
  getBookPriceTrackInfo,
  getFreezeDepositContent,
} from '../Global/Cache/ProductSelectors';
import { getVehicleInfoLogData } from '../State/ProductConfirm/Selectors';
import { GetAB, Utils } from '../Util/Index';

const mapStateToProps = state => {
  const price = getPrice();
  return {
    depositPayInfos: getDepositPayInfos(state),
    depositInfo: price && price.depositInfo,
    depositPayType: getDepositPayType(state),
    passenger: getPassenger(state),
    sesameBarTexts: getSesameBarTexts(SesamePageLocation.creditRent),
    authStatus: getAuthStatus(state),
    isCtripCreditRent: isCtripCreditRent(state),
    depositLabelType: getDepositLabelType(state),
    bookPriceTrackInfo: getBookPriceTrackInfo(),
    sesameOrderId: getSesameOrderId(),
    freezeDepositContent: getFreezeDepositContent(),
    vehicleInfoLog: getVehicleInfoLogData(state),
    complementaryAmount: getZhimaComplementaryAmount(),
    isEhiFreeDeposit:
      price?.zhimaInfo?.isSupportZhima && geZhimaNoteInfo()?.title,
    isLateDepositVerVendorBlackList: isLateDepositVerVendorBlackList(state),
  };
};

const mapDispatchToProps = dispatch => ({
  changePayMode: data => {
    dispatch(changePayMode(data));
    dispatch(changeAllSelectInsurance([]));
    if (data.nextDepositPayType && lodashKeys(data).length === 1) {
      return;
    }
    dispatch(queryPriceInfo());
  },
  onPressDepositIntroduce: () =>
    dispatch(changeModalStatus({ depositIntroduceModalVisible: true })),
  onAuthentication: (data: OnAuthenticationDateType) =>
    dispatch(onAuthentication(data)),
  onAuthConfirmPress: (data: { isVisible: boolean }) =>
    dispatch(onAuthConfirmPress(data)),
  onPressCtripCreditF: () =>
    dispatch(changeModalStatus({ ctripCreditFModalVisible: true })),
  onCtripAuthentication: () => dispatch(onCtripAuthentication()),
  setEhiFreeDepositModalVisible: (data: boolean) => {
    dispatch(setEhiFreeDepositModalVisible(data));
  },
});

export default connect<{}, {}, IPaymentComponent>(
  mapStateToProps,
  mapDispatchToProps,
)(DepositPaymentBlockISD);
