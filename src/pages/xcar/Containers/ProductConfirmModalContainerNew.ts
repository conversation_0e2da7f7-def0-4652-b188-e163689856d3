import { connect } from 'react-redux';
import ProductConfirmModalNew from '../Components/Business/ProductConfirmModalNew';
import {
  getProductConfirmResponse,
  getLoading,
  getFail,
  getFooterBarData,
  getImagesPageParams,
  getStoreAlbumPageParams,
  getGuidePageParams,
  getVirtualNumberLoading,
  getVehicleInfoLogData,
  getIsVirtualNumberIconVisible,
  getVirtualNumber,
  getVendorVNumber,
  getVendorImUrl,
  getPickUpDistanceDesc,
} from '../State/ProductConfirm/Selectors';
import { queryVehicleDetailInfo } from '../State/ProductConfirm/Actions';
import {
  setVirtualNumberDialogVisible,
  setVirtualNumberModalVisible,
  setPriceDetailModalVisible,
} from '../State/VendorList/Actions';
import { fetchApiGuide } from '../State/Guide/Actions';
import {
  getUniqueCode,
  getUniquePriceInfo,
  getIsCouponBook,
  getVehicleDetailModalLogInfo,
  getUniqueReference,
  getCurFloor,
} from '../State/VendorList/Selectors';
import {
  getVendorPriceInfo,
  getPolicyQueryParams,
} from '../State/Booking/Selectors';
import { getNationalChainTag } from '../State/List/VehicleListMappers';
import { VendorPriceListType } from '../Types/Dto/QueryVehicleDetailListResponseType';
import Channel from '../Util/Channel';

const mapStateToProps = (state, props) => {
  const response = getProductConfirmResponse(state);
  // uniquePriceInfo 在Booking页直接从当前页Store获取
  const isBook = props?.page?.pageId === Channel.getPageId().Book.ID;
  const uniquePriceInfo: VendorPriceListType = isBook
    ? getVendorPriceInfo(state)
    : getUniquePriceInfo(state);
  const nationalChainTag = getNationalChainTag(response?.vehicleTags);
  return {
    guidePageParams: getGuidePageParams(state),
    imagesPageParams: getImagesPageParams(state),
    storeAlbumPageParams: getStoreAlbumPageParams(state),
    commentCount: response?.commentInfo?.commentCount,
    vendorName: uniquePriceInfo?.vendorName,
    isSelected: uniquePriceInfo?.isSelect,
    nationalChainTagTitle: nationalChainTag?.title,
    isLoading: getLoading(state),
    isFail: getFail(state),
    footerBarData: getFooterBarData(state),
    hasFees: uniquePriceInfo?.fees?.length > 0,
    uniqueCode: getUniqueCode(state),
    isVirtualNumberLoading: getVirtualNumberLoading(state),
    isVirtualNumberIconVisible: getIsVirtualNumberIconVisible(state),
    vehicleInfoLog: getVehicleInfoLogData(state),
    isCouponBook: getIsCouponBook(state),
    virtualNumber: getVirtualNumber(state),
    vendorVNumber: getVendorVNumber(state),
    vendorImUrl: getVendorImUrl(state),
    pickUpDistanceDesc: getPickUpDistanceDesc(state),
    policyQueryParams: isBook && getPolicyQueryParams(state),
    vehicleDetailModalTitle: response?.title,
    vehicleDetailModalLogInfo: getVehicleDetailModalLogInfo(state),
    reference: getUniqueReference(state),
    curFloor: getCurFloor(state),
  };
};

const mapDispatchToProps = dispatch => ({
  queryVehicleDetailInfo: () => {
    dispatch(queryVehicleDetailInfo());
  },
  showPhoneDialog: () => dispatch(setVirtualNumberDialogVisible(true)),
  showVirtualNumberStoreModal: () =>
    dispatch(setVirtualNumberModalVisible(true)),
  fetchApiGuide: data => dispatch(fetchApiGuide(data)),
  setPriceDetailModalVisible: data =>
    dispatch(setPriceDetailModalVisible(data)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ProductConfirmModalNew);
