import { connect } from 'react-redux';
import VehicleListNew from '../Pages/List/Components/VehicleListNew';
import {
  getActiveGroupIndex,
  getScrollViewHeight,
  getActiveGroupId,
  getFilterNoResult,
  getIsNextPageFail,
  getIsNextPageLoading,
  getSiblingsGroups,
  getCurSearchWordObj,
} from '../State/List/Selectors';
import {
  setActiveGroupId,
  setVehPopData,
  setVendorListModalData,
  fetchListPage,
  setSecretBoxModalData,
  setEasyLifePopVisible,
  fetchEasyLife2024Tags,
} from '../State/List/Actions';
import {
  getGroupNameByKey,
  packageSectionsForListPage,
} from '../State/List/VehicleListMappers';
import {
  getProductListByGroupId,
  getBaseAllVendorPriceCount,
  getIsNoTopProduct,
  getIsLastPage,
  getNetworkCost,
  getIsFromSearch,
  getIsFromCache,
  getHasRetry,
  getIsNewSearchNoResult,
  getRecommendEventResult,
  getIsFilteredRecommend,
  getIsEasyLife2024NoResult,
  getBaseResData,
} from '../Global/Cache/ListResSelectors';
import ListPerformanceLog from '../Pages/List/ListPerformanceLog';
import { CarABTesting, CarServerABTesting } from '../Util/Index';
import { ApiResCode } from '../Constants/Index';

const EMPTY_ARRAY = [];

const mapStateToProps = state => {
  ListPerformanceLog.start(ListPerformanceLog.keyList.packageVehList);
  const activeGroupId = getActiveGroupId(state);
  const activeGroupName = getGroupNameByKey(activeGroupId);
  const curProductList = getProductListByGroupId(activeGroupId);
  const filterNoResult = getFilterNoResult(state);
  const isFromSearch = getIsFromSearch() && !getIsFromCache();

  const sections = filterNoResult
    ? EMPTY_ARRAY
    : packageSectionsForListPage(curProductList, activeGroupId);
  const isNewSearchNoResult = getIsNewSearchNoResult();
  // 非推荐车型数
  const curVehicleCount = sections.filter(item => !item.recommendType).length;
  const priceCount = getBaseAllVendorPriceCount();
  const recommendEventResult = getRecommendEventResult();
  const propsData = {
    isEasyLife2024NoResult: getIsEasyLife2024NoResult(activeGroupId),
    index: getActiveGroupIndex(state),
    scrollViewHeight: getScrollViewHeight(state),
    activeGroupId,
    activeGroupName,
    filterNoResult,
    priceCount,
    curVehicleCount,
    sections,
    unUrlProduct: getIsNoTopProduct(),
    isLoading: getIsNextPageLoading(state),
    isLoadingFail: getIsNextPageFail(state),
    isLastPage: getIsLastPage(),
    networkCost: getNetworkCost(),
    isFromSearch,
    hasRetry: getHasRetry(),
    abVersion: CarABTesting.packageAbversion(),
    recommendEventResult,
    isNewSearchNoResult,
    isFilteredRecommend: getIsFilteredRecommend(),
    isFilteredRecommendAb: CarServerABTesting.isFilteredRecommendAb(),
    siblingsGroups: getSiblingsGroups(state),
    isGroupLoading: state.List.isGroupLoading,
    isFilterFilterNoResult:
      getBaseResData()?.baseResponse?.errorCode ===
      ApiResCode.ListResErrorCode.E1000002,
    searchKeyWord: getCurSearchWordObj(state)?.word,
  };
  ListPerformanceLog.end(ListPerformanceLog.keyList.packageVehList);
  return propsData;
};

const mapDispatchToProps = dispatch => ({
  setActiveGroupId: data => {
    dispatch(setActiveGroupId(data));
  },
  setVehPopData: data => {
    dispatch(setVehPopData(data));
  },
  setVendorListModalData: data => dispatch(setVendorListModalData(data)),
  fetchListPage: data => dispatch(fetchListPage(data)),
  setSecretBoxModalData: data => dispatch(setSecretBoxModalData(data)),
  setEasyLifePopVisible: () => {
    dispatch(setEasyLifePopVisible({ visible: true }));
    dispatch(fetchEasyLife2024Tags());
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(VehicleListNew);
