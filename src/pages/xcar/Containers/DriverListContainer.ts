import connect from '../WithRedux';
import DriverList from '../Pages/DriverList/Index';
import { SesameState } from '../State/Sesame/Types';
import {
  getIsLoading,
  getIsFail,
  getPassengerList,
  getPassenger,
  getIsForceUpdate,
  getAvailableCertificates,
  getCurCertificates,
  getIsLoadingSuccess,
  getMaxAge,
  getMinAge,
} from '../State/DriverList/Selectors';
import { getSesameInfo } from '../Global/Cache/ProductSelectors';
import { getAuthStatus, getSesameUserName } from '../State/Sesame/Selectors';
import { isCtripCreditRent } from '../State/Sesame/Mappers';
import { getCurPriceInfo } from '../State/Product/Mappers';
import {
  fetchApiDriverList,
  deleteDriver,
  selectDriver,
  fetchApiIdCardList,
} from '../State/DriverList/Actions';
import { Utils, CarABTesting } from '../Util/Index';
import { getCurInsPackageId } from '../State/Product/Selectors';
import { getAdditionalDriverDesc } from '../State/Booking/Selectors';

const mapStateToProps = state => {
  const curInsPackageId = getCurInsPackageId(state);
  const priceInfo = getCurPriceInfo(curInsPackageId) || {};
  const { ageRestriction = {} } = priceInfo;
  const { maxDriverAge, minDriverAge, youngDriverAge, oldDriverAge } =
    ageRestriction;
  const yongAge = Utils.isCtripIsd()
    ? youngDriverAge || getMinAge(state)
    : minDriverAge;
  const oldAge = Utils.isCtripIsd()
    ? oldDriverAge || getMaxAge(state)
    : maxDriverAge;
  const { isSupportZhima = false } = getSesameInfo();
  const isAuthorized = getAuthStatus(state) === SesameState.authorized;
  const userName = getSesameUserName(state);
  return {
    isLoading: getIsLoading(state),
    isLoadingSuccess: getIsLoadingSuccess(state),
    isFail: getIsFail(state),
    passengerList: getPassengerList(state),
    passenger: getPassenger(state),
    isForceUpdate: getIsForceUpdate(state),
    availableCertificates: getAvailableCertificates(state),
    curCertificates: getCurCertificates(state),
    yongAge,
    oldAge,
    isSupportZhima,
    isAuthorized,
    userName,
    isCtripCreditRent: isCtripCreditRent(state),
    isCreditRent: CarABTesting.isCreditRent(),
    addInstructData: getAdditionalDriverDesc(state),
  };
};

const mapDispatchToProps = dispatch => ({
  fetchApiDriverList: data => {
    setTimeout(() => {
      dispatch(fetchApiDriverList(data));
    });
  },
  deleteDriver: (data, callback) => dispatch(deleteDriver(data, callback)),
  selectDriver: data => {
    dispatch(
      selectDriver({
        ...data,
        queryPriceInfo: true,
      }),
    );
  },
  fetchApiIdCardList: data => dispatch(fetchApiIdCardList(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(DriverList);
