import { connect } from 'react-redux';
import NewCouponAndDepositfromISD from '../Pages/BookingIsd/Component/NewCouponAndDeposit';
import {
  getNewCouponTip,
  getPrice,
  getNewDepositData,
  validateIsMergeDeposit,
} from '../State/Booking/Selectors';
import {
  changePayMode,
  setEhiFreeDepositModalVisible,
  setHasReceivedCouponAtBooking,
} from '../State/Booking/Actions';
import { changeAllSelectInsurance } from '../State/Product/Actions';
import { receiveAllPromotion } from '../State/Coupon/Actions';

const mapStateToProps = state => {
  const priceInfo = getPrice();
  return {
    couponTip: getNewCouponTip(state),
    depositInfo: priceInfo?.depositInfo,
    trackInfo: priceInfo?.trackInfo,
    isMergeDeposit: validateIsMergeDeposit(state),
    depositSimpleDescs: priceInfo?.depositInfo?.depositSimpleDescs,
    preferentialTips: priceInfo?.preferentialTips,
    isNewUser: priceInfo?.isdNewUser,
    ...getNewDepositData(state),
  };
};

const mapDispatchToProps = dispatch => ({
  changePayMode: data => {
    dispatch(changeAllSelectInsurance([]));
    dispatch(changePayMode(data));
  },
  showEhiFreeDepositModalVisible: () => {
    dispatch(setEhiFreeDepositModalVisible(true));
  },
  onReceive: data => dispatch(receiveAllPromotion(data)),
  setHasReceivedCouponAtBooking: data =>
    dispatch(setHasReceivedCouponAtBooking(data)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(NewCouponAndDepositfromISD);
