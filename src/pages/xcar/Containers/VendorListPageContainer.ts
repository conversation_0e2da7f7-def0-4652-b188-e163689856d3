import { omit as lodashOmit } from 'lodash-es';
import { xShowToast } from '@ctrip/xtaro';

import connect from '../WithRedux';

import {
  Utils,
  Channel,
  CarLog,
  CarStorage,
  AppContext,
  GetABCache,
} from '../Util/Index';
import ListTexts from '../Pages/List/Texts';
import VendorList from '../Pages/VendorList/Index';
import { isDebugMode } from '../State/Debug/Selectors';
import {
  setTimeOutPopData,
  queryVehicleDetailList,
  setVehicleModalVisible,
  setCouponModalVisible,
  setModalStatus,
  setProductConfirmModalVisible,
  setPriceDetailModalVisible,
  setIsLoginAtBookingPage,
  reset,
  setVehicleIndex,
  setFristScreenParam,
  closeVendorListModal,
  goToBooking,
  setIsCouponBookAtBookingPage,
  setOptimizationStrengthenModalVisible,
  setCarServiceDetailVisible,
  setCurrentPageParams,
  queryMultimediaAlbum,
  setBusinessLicenseVisible,
  setLocationAndDatePopIsShow,
  setEtcIntroModal,
  queryInsuranceDetails,
  setInsuranceDetail,
  queryVendorIpollConfig,
} from '../State/VendorList/Actions';
import {
  fetchReceivePromotion,
  setIsListReceiveSuccess,
} from '../State/Coupon/Actions';
import {
  getProductCarHeaderData,
  getPickUpTime,
  getPickUpCityId,
  getPickUpCityName,
} from '../State/LocationAndDate/Selectors';
import { setLocationDateInfo } from '../State/LocationAndDate/Actions';
import {
  getBbkVehicleNameProps,
  getIsShowRestAssured,
  getMarketingAtmosphere,
  getVehicleDescList,
  getProductConfirmModalVisible,
  getPriceDetailModalVisible,
  getIsLoading,
  getIsError,
  getAppResponseMap,
  getIsLoginAtBookingPage,
  getProductConfirmAnchor,
  getUniqueCode,
  getVendorListShowModalKey,
  getTangramEntranceInfos,
  getVehicleCode,
  getIsCouponBookAtBookingPage,
  getHasLaborDayLabel,
  getVendorListMarketTheme,
  getIsShowMarketTheme,
  getYunnanBannerInfo,
  getOptimizationStrengthenModalVisible,
  getCarServiceDetailVisible,
  getMinTPriceVendor,
  getMinTotalPriceOtherDesc,
  getLocationDatePopVisible,
  getVehicleVR,
  getNoMatchData,
  getNoMatchDataNew,
  getCurrentPageParams,
  getIsBusinessLicenseModalVisible,
  getIsEtcIntroModalVisible,
  getVehicleDetailListRequest,
  getVehicleDetailListResponse,
  getShareVehicleInfo,
  getReducedCarbonEmissionRatio,
  getFloorVendorList,
  getFloorId,
  getPackageCode,
  getIsInsuranceDetails,
  getVehicleDetailModalLogInfo,
  getIpollConfigData,
  getIpollLogData,
} from '../State/VendorList/Selectors';
import { getRebookParams } from '../State/ModifyOrder/CommonSelector';
import { getQConfig } from '../State/Common/Selectors';
import {
  getIsListReceiveSuccess,
  getListCouponEntryData,
} from '../State/Coupon/Selectors';
import { getVehicleInfoLogData } from '../State/ProductConfirm/Selectors';
import ListReqAndResData, {
  listStatusKeyList,
  setListStatusData,
} from '../Global/Cache/ListReqAndResData';
import Texts from '../Pages/VendorList/Texts';
import { StorageKey } from '../Constants/Index';
import { queryVehicleDetailInfo, clear } from '../State/ProductConfirm/Actions';
import { getMarketTheme } from '../State/Home/Actions';
import { Enums } from '../ComponentBusiness/Common';
import { getSelectedFilterString, getTenancyDays } from '../State/List/Mappers';
import { fetchLimitContent } from '../State/List/Actions';
import { getImAddressWithQconfig } from '../State/Common/Mapper';
import { getSelectedFilters } from '../State/List/Selectors';
import { getHasReceivedCouponAtBooking } from '../State/Booking/Selectors';
import { setHasReceivedCouponAtBooking } from '../State/Booking/Actions';

const TotalPriceEnName = {
  [Enums.TotalPriceModalType.Vendor]: '点击_供应商总价提示',
  [Enums.TotalPriceModalType.VendorListModal]: '点击_费用明细',
};

const setVendorListCurrentPageParams = state => {
  const pageUniqueId = Utils.openUrlVendorListPageUniqueId();
  if (pageUniqueId) {
    /**
     * openUrl打开的方式首先会去state中拿首屏参数，
     * 没有的话就是首次打开会在localStorage中获取，获取后会remove
     * 页面会在constructor中将获取的currentPageParams存储到state中，之后每次都能通过getCurrentPageParams获取
     * todo-lxj 是否需要迁移到页面的constructor中处理
     */
    const currentPageParams = getCurrentPageParams(state);
    if (currentPageParams.pageParam) return currentPageParams;
    const storageKey = `${StorageKey.CAR_VENDORLISTPAGE_PAGEPARAMSINFO}_${pageUniqueId}`;
    let pageParams = CarStorage.privateLoadSync(storageKey);
    if (pageParams) {
      pageParams = JSON.parse(pageParams);
      AppContext.setGoodsShelvesTwoSwitch(pageParams?.goodsShelvesTwoSwitch);
      AppContext.setGoodsShelvesTwoABVersion(
        pageParams?.goodsShelvesTwoABVersion,
      );
      AppContext.setSelfServiceSwitch(pageParams?.selfServiceSwitch); // 自助取还开关
      // 设置限行政策缓存数据
      if (pageParams?.limitRuleData) {
        const curCityId = getPickUpCityId(state);
        const curCityName = getPickUpCityName(state);
        const resCityId = pageParams?.limitRuleData?.cityId;
        const resCityName = pageParams?.limitRuleData?.cityName;
        if (curCityId === resCityId && curCityName === resCityName) {
          ListReqAndResData.setData(
            ListReqAndResData.keyList.listLimitRuleRes,
            pageParams?.limitRuleData,
          );
        }
      }
      if (pageParams?.appContext?.nonJumpFlow) {
        const newQuery = {
          ...(AppContext.UrlQuery || {}),
          nonJumpFlow: pageParams?.appContext?.nonJumpFlow,
        };
        AppContext.setUrlQuery(newQuery);
      }
      CarStorage.remove(storageKey);
      return pageParams;
    }
  }
  return {};
};

const mapStateToProps = state => {
  const rebookParams = getRebookParams(state);
  const minPriceInfo = getMinTPriceVendor(state)?.priceInfo;
  const minTotalPriceOtherDesc = getMinTotalPriceOtherDesc(state);
  const vendorListCurrentPageParams = setVendorListCurrentPageParams(state);
  const ipollConfig = getIpollConfigData(state);
  const positionNum = ipollConfig?.find(
    item => item?.pageType === 2 && !!item?.positionNum,
  )?.positionNum;
  const ipollPositionType = ipollConfig?.find(
    item => item?.pageType === 2 && !!item?.positionType,
  )?.positionType;
  const sceneid = ipollConfig?.find(item => item?.pageType === 2)?.sceneid;
  return {
    isDebugMode: isDebugMode(state),
    ctripOrderId: rebookParams?.ctripOrderId,
    config: getQConfig(state),
    // 头部组件参数
    headerData: getProductCarHeaderData(state),
    bizType: Utils.getSideToolBizType(),
    toolBoxCustomerJumpUrl: getImAddressWithQconfig(state, {
      pageId: Channel.getPageId().Product.ID,
      isPreSale: 1,
    }),
    isLoginAtBookingPage: getIsLoginAtBookingPage(state),
    bbkVehicleNameProps: getBbkVehicleNameProps(state),
    isShowRestAssured: getIsShowRestAssured(state),
    marketingAtmosphere: getMarketingAtmosphere(state),
    vehicleDescList: getVehicleDescList(state),
    isListReceiveSuccess: getIsListReceiveSuccess(state),
    productConfirmModalVisible: getProductConfirmModalVisible(state),
    priceDetailModalVisible: getPriceDetailModalVisible(state),
    isLoading: getIsLoading(state),
    isError: getIsError(state),
    appResponseMap: getAppResponseMap(state),
    productConfirmAnchor: getProductConfirmAnchor(state),
    pTime: getPickUpTime(state),
    uniqueCode: getUniqueCode(state),
    showModalKey: getVendorListShowModalKey(state),
    tangramEntranceInfos: getTangramEntranceInfos(state),
    vehicleCode: getVehicleCode(state),
    isCouponBookAtBookingPage: getIsCouponBookAtBookingPage(state),
    hasLaborDayLabel: getHasLaborDayLabel(state),
    vendorListMarketTheme: getVendorListMarketTheme(state),
    isShowMarketTheme: getIsShowMarketTheme(state),
    yunnanBannerInfo: getYunnanBannerInfo(state),
    optimizationStrengthenModalVisible:
      getOptimizationStrengthenModalVisible(state),
    carServiceDetailVisible: getCarServiceDetailVisible(state),
    vehicleInfoLogData: getVehicleInfoLogData(state),
    descText: ListTexts.dailyPriceUnit(getTenancyDays()),
    price: minPriceInfo?.currentDailyPrice,
    minTotalPrice: minPriceInfo?.currentTotalPrice,
    minTotalPriceDesc: ListTexts.total,
    minTotalPriceOtherDesc,
    locationDatePopVisible: getLocationDatePopVisible(state),
    vrUrl: getVehicleVR(state),
    isNomatch: getNoMatchDataNew(state)?.isNomatch,
    vendorListCurrentPageParams,
    isBusinessLicenseModalVisible: getIsBusinessLicenseModalVisible(state),
    isEtcIntroModalVisible: getIsEtcIntroModalVisible(state),
    vehicleDetailListReq: getVehicleDetailListRequest(state),
    vehicleDetailListRes: getVehicleDetailListResponse(state),
    shareVehicleInfo: getShareVehicleInfo(state),
    reducedCarbonEmissionRatio: getReducedCarbonEmissionRatio(state),
    shelvesFloor: getFloorVendorList(state),
    floorId: getFloorId(state),
    packageCode: getPackageCode(state),
    isCouponEntry: !!(getListCouponEntryData(state)?.length > 0),
    insuranceDetails: getIsInsuranceDetails(state),
    ...vendorListCurrentPageParams,
    vehicleDetailModalLogInfo: getVehicleDetailModalLogInfo(state),
    ipollPositionNum: positionNum,
    ipollPositionType,
    sceneid,
    ipollLogData: getIpollLogData(state),
    filterInfo: getSelectedFilterString(getSelectedFilters(state)),
    hasReceivedCouponAtBooking: getHasReceivedCouponAtBooking(state),
  };
};

const mapDispatchToProps = dispatch => ({
  setTimeOutPopData: data => {
    dispatch(setTimeOutPopData(data));
  },
  queryVehicleDetailList: data => {
    dispatch(queryVehicleDetailList(data));
  },
  queryVehicleDetailInfo: () => {
    dispatch(queryVehicleDetailInfo());
  },
  fetchReceivePromotion: () => dispatch(fetchReceivePromotion()),
  setCouponModalVisible: visible => dispatch(setCouponModalVisible(visible)),
  setIsListReceiveSuccess: data => dispatch(setIsListReceiveSuccess(data)),
  handleCloseCouponModal: param => {
    const {
      pageParam,
      isListReceiveSuccess,
      isLoginChanged,
      setIsLoginChanged,
    } = param;
    dispatch(setCouponModalVisible(false));
    // 若在弹层中有进行领券或登录操作，关闭弹层时，需要刷新页面重新请求
    if (isLoginChanged || isListReceiveSuccess) {
      dispatch(
        queryVehicleDetailList({
          reqParam: lodashOmit(pageParam, 'uniqSign'),
          noShowLoadTip: isListReceiveSuccess,
        }),
      );
      if (isListReceiveSuccess) {
        xShowToast({ title: Texts.listReceivePromotionToast, duration: 3000 });
        dispatch(setIsListReceiveSuccess(false));
      }
      if (isLoginChanged) {
        setIsLoginChanged(false);
      }
      // 记录在报价列表页重新刷新页面
      setListStatusData(listStatusKeyList.refreshFromVendorListPage, true);
    }
  },
  // 点击预订
  goToBooking: async data => {
    dispatch(goToBooking(data));
  },
  // 打开车型信息弹层
  openVehicleModal: () => {
    dispatch(setVehicleModalVisible(true));
  },
  // 关闭车型信息弹层
  closeVehicleModal: () => {
    dispatch(setVehicleModalVisible(false));
  },
  // 打开或关闭费用明细弹层
  setPriceDetailModal: data => {
    const {
      type,
      uniqueCode,
      hasFees,
      floorId,
      packageCode,
      curPriceDetailModalVisible,
      curTotalPriceModalVisible,
    } = data;
    // 如果接口有返回价格一致性的节点，则设置价格一致性总价说明，反之控制非价格一致性版本的总价说明
    if (hasFees || (floorId && packageCode)) {
      dispatch(
        setModalStatus({
          priceDetailModalVisible: !curPriceDetailModalVisible,
          uniqueCode,
          // 货架2.0的费用明细的组合标识
          floorId,
          packageCode,
        }),
      );
    } else {
      dispatch(
        setModalStatus({
          totalPriceModalVisible: !curTotalPriceModalVisible,
          uniqueCode,
          // 货架2.0的费用明细的组合标识
          floorId,
          packageCode,
        }),
      );
    }
    CarLog.LogCode({
      name: TotalPriceEnName[type],
    });
  },
  // 关闭费用明细弹层
  closePriceDetailModal: () => {
    dispatch(setPriceDetailModalVisible(false));
    CarLog.LogCode({ name: '点击_列表页_供应商总价提示弹层_关闭' });
  },
  // 关闭信息确认弹层
  closeProductConfirmModal: () => {
    dispatch(setProductConfirmModalVisible(false));
  },
  setIsLoginAtBookingPage: (data: boolean) => {
    dispatch(setIsLoginAtBookingPage(data));
  },
  setLocationAndDatePopIsShow: data =>
    dispatch(setLocationAndDatePopIsShow(data)),
  reset: () => {
    // 清空信息确认弹层数据
    dispatch(clear());
    // 清空VendorList数据
    dispatch(reset());
  },
  setVehicleIndex: vehicleIndex => dispatch(setVehicleIndex(vehicleIndex)),
  setFirstScreenParam: data => dispatch(setFristScreenParam(data)),
  setCurrentPageParams: data => dispatch(setCurrentPageParams(data)),
  closeVendorListModal: data => dispatch(closeVendorListModal(data)),
  onPressEasyLife: () =>
    dispatch(
      setModalStatus({
        isEasyLifeModalVisible: true,
      }),
    ),
  setIsCouponBookAtBookingPage: data =>
    dispatch(setIsCouponBookAtBookingPage(data)),
  getMarketTheme: () => dispatch(getMarketTheme()),
  showOptimizationStrengthenModal: () =>
    dispatch(setOptimizationStrengthenModalVisible(true)),
  hideOptimizationStrengthenModal: () =>
    dispatch(setOptimizationStrengthenModalVisible(false)),
  showCarServiceDetailModal: () => dispatch(setCarServiceDetailVisible(true)),
  hideCarServiceDetailModal: () => dispatch(setCarServiceDetailVisible(false)),
  setLocationDateInfo: data => dispatch(setLocationDateInfo(data)),
  fetchLimitContent: data => dispatch(fetchLimitContent(data)),
  queryMultimediaAlbum: data => dispatch(queryMultimediaAlbum(data)),
  setBusinessLicenseModal: data => dispatch(setBusinessLicenseVisible(data)),
  setEtcIntroModal: data => dispatch(setEtcIntroModal(data)),
  queryInsuranceDetails: data => dispatch(queryInsuranceDetails(data)),
  setInsuranceDetail: data => dispatch(setInsuranceDetail(data)),
  queryVendorIpollConfig: data => dispatch(queryVendorIpollConfig(data)),
  setHasReceivedCouponAtBooking: data =>
    dispatch(setHasReceivedCouponAtBooking(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(VendorList);
