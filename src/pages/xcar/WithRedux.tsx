import React from 'react';
import { Provider, connect } from 'react-redux';
import { getStore } from './State/StoreRef';
import PreSetSomething from './PreSetSomething';
import { getPushData } from './Util/xTaroTools';
import './SwitchEnv/switchStyle';
import getUrlQuery, { getUrlPageName } from './SwitchEnv/urlQuery';
import { CarStorage } from './Util/Index';
import { StorageKey } from './Constants/Index';
import ErrorBoundary from './Components/Error/ErrorBoundary';

const wrapper = (InnerComponent, renderCheckInfo) => {
  const out = React.forwardRef((props, ref) => {
    // @ts-ignore
    if (!global.isPreSetSomething) {
      PreSetSomething(); // props
    }
    // TODO: @zyr getUrlPageName不接收参数
    // @ts-ignore
    const urlPageName = getUrlPageName(props);
    const pushData = getPushData(urlPageName);
    const urlQuery = getUrlQuery();
    let crossParams;
    if (urlQuery?.isCross) {
      try {
        const paramsStr = CarStorage.loadSync(StorageKey.CAR_CROSS_PARAMS);
        const params = JSON.parse(paramsStr);
        const { storeParams, urlParams } = params || {};
        crossParams = urlParams;
        if (storeParams?.length > 0) {
          storeParams?.forEach(item => {
            const { type, data } = item || {};
            if (type && data) {
              getStore().dispatch({
                type,
                data,
              });
            }
          });
        }
        CarStorage.remove(StorageKey.CAR_CROSS_PARAMS);
        // eslint-disable-next-line no-empty
      } catch (e) {}
    }
    return (
      <ErrorBoundary>
        <Provider store={getStore()}>
          <InnerComponent
            forwardedRef={ref}
            isCross={urlQuery?.isCross}
            {...pushData}
            {...props}
            {...crossParams}
          />
        </Provider>
      </ErrorBoundary>
    );
  });
  // @ts-ignore
  out.renderCheckInfo = renderCheckInfo;
  return out;
};

const redux = (mapStateToProps, mapDispatchToProps) => {
  return ComponentPage => {
    const ComponentWithRef = (props: any) => (
      <ComponentPage {...props} ref={props.forwardedRef} />
    );
    return connect(mapStateToProps, mapDispatchToProps)(ComponentWithRef);
  };
};

const connectPage = (mapStateToProps, mapDispatchToProps, renderCheckInfo?) => {
  return ComponentPage => {
    const reduxInner = redux(
      mapStateToProps,
      mapDispatchToProps,
    )(ComponentPage);
    return wrapper(reduxInner, renderCheckInfo);
  };
};

export default connectPage;
