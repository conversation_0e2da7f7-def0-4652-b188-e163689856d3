import { get as lodashGet } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import IUrs from '@c2x/components/IUrs';
import React, { memo, useState } from 'react';
import { XView as View, xEnv } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import memoizeOne from 'memoize-one';
import c2xStyles from './vehicleListC2xStyles.module.scss';
import BbkListNoMatch from '../../../../ComponentBusiness/ListNoMatch';
import { ImgType } from '../../../../ComponentBusiness/ListNoMatch/src/NoMatchImg';
import SectionListWithControl, {
  SectionListWithControlProps,
} from '../../../../Components/Common/SectionListWithControl';
import AndroidHandlerView from '../../../../Components/Common/AndroidHandlerView';
import Vehicle from '../../../../Containers/VehicleContainer';
import { CarLog, Channel, GetABCache } from '../../../../Util/Index';
import SelectedFilterItems from '../../../../Containers/SelectedFilterItemsContainer';
import TipList from '../../../../Containers/ListTipListContainer';
import SectionFooterContainer from '../../../../Containers/VehicleListSectionFooterContainer';
import { getLogDataFromState } from '../../../../State/List/Mappers';
import { EasyLifeEntrance } from '../TipList';
import { ApiResCode, ListEnum, LogKey } from '../../../../Constants/Index';
import Texts from '../../Texts';
import { ListNoMatch } from '../NoMatch';
import AgeModifyTip from '../../../../Containers/AgeModifyTipContainer';
import ListConstants from '../../../../Constants/ListConstants';
import VehicleListBottomText from '../../../../Containers/ListVehicleListBottomTipContainer';
import { getBaseResData } from '../../../../Global/Cache/ListResSelectors';
import { ItemLabelCodeType } from '../../../../ComponentBusiness/PackageIncludes/src/Types';
import { isOsdUserBrowsed } from '../../Method';

const { getPixel, isIos, useMemoizedFn } = BbkUtils;
const { GroupBarEnums } = ListEnum;
const styles = StyleSheet.create({
  imgStyle: {
    width: getPixel(340),
    height: getPixel(340),
  },
  imgStyleNoMatch: {
    width: getPixel(240),
    height: getPixel(240),
  },
  titleStyle: {
    ...font.title4MediumStyle,
    color: color.fontPrimary,
  },
  ipollContainer: {
    marginBottom: getPixel(12),
  },
});

const NoMatch = (title: string, selectFiltersExposure?: any) => (
  <>
    <View className={c2xStyles.noMatchWrap}>
      <BbkListNoMatch
        imgStyle={styles.imgStyle}
        type={ImgType.No_Search_Result}
        titleStyle={styles.titleStyle}
        title={title}
        subTitle=""
        isShowOperateButton={false}
        isShowRentalDate={false}
      />
    </View>
    <SelectedFilterItems
      selectFiltersExposure={selectFiltersExposure}
      noMatch={true}
    />

    <AgeModifyTip />
  </>
);

export const cacheDom = {
  TipList: memoizeOne(
    (
      resetCache,
      activeGroupId,
      showTipList = true,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      filterNoResult = false,
      noMatchProps,
      isFail = false,
    ) => (
      <>
        {showTipList && <TipList resetCache={resetCache} />}
        {activeGroupId === ApiResCode.EasyLifeCode && !isFail && (
          <EasyLifeEntrance />
        )}
        {/* {filterNoResult && (
        <View style={{ marginHorizontal: space.spaceXXL, marginVertical: space.spaceXL }}>
        <BBkText
        style={[font.body3Medium2Style, { color: color.fontPrimary }]}
        >
        {Texts.noResultText}
        </BBkText>
        </View>
        )} */}
        {noMatchProps && (
          <ListNoMatch
            {...noMatchProps}
            noMatch={false}
            imgStyle={styles.imgStyleNoMatch}
          />
        )}
      </>
    ),
  ),
  SelectedFilterItems: <SelectedFilterItems />,
};

export interface VehicleListProps extends SectionListWithControlProps {
  activeGroupId: string;
  activeGroupName?: string;
  isHideFooter?: boolean;
  selectFiltersExposure?: (data) => void;
  setVehPopData: (args: any) => void;
  setVendorListModalData?: (args: any) => void;
  showMax?: number;
  pageRef?: any;
  resetCache?: () => void;
  showTipList?: boolean;
  filterNoResult?: boolean;
  noMatchProps?: any;
  isNoMatch?: boolean;
  unUrlProduct?: boolean;
  isFail?: boolean;
  scrollRefFn?: (ref) => void;
  closeTipPop?: () => void;
  getLogKey?: (
    vendor?: any,
    section?: any,
    vendorIndex?: any,
    activeGroupId?: string,
  ) => {};
  isShowBottomLoadingText?: boolean;
  progress?: number;
  priceCount?: number;
  vehCount?: number;
  isRecommend?: boolean;
  isRefactor?: boolean;
  isNoResultNew?: boolean;
  style?: any;
  osdUserBrowsed?: object;
  ipollPosNum?: number;
  ipollGroup?: string;
  sceneid?: string;
}

interface IVehicleExposureData {
  isCreditRent?: boolean;
  isEasyLife?: boolean;
  isOptimize?: boolean;
  minDPrice?: number;
  minTPrice?: string;
  vechicleLable?: string;
  vehicleCode?: string;
  vehicleIndex?: number;
  minDPricePStoreCode?: number;
  realGroupId?: string;
  realGroupName?: string;
  ifPriceAdjusted?: boolean;
  vehicleTags?: Array<any>;
}

const isPriceGroup = sectionItem =>
  sectionItem.priceGroup && sectionItem.priceGroup.length > 0;

export const getNoMatchText = (activeGroupName: string = '') => {
  const end = Texts.NoMatch_Second;
  switch (activeGroupName) {
    case GroupBarEnums.all:
      return `${Texts.noMoreFilter_First}${Texts.vehicleAllText}${end}`;
    case GroupBarEnums.hot:
    case GroupBarEnums.easyLife:
      return `${Texts.noMoreFilter_First}${activeGroupName}${Texts.vehicleAllText}${end}`;
    case GroupBarEnums.economy:
    case GroupBarEnums.comfort:
    case GroupBarEnums.luxury:
      return `${Texts.noMoreFilter_First}${activeGroupName}${Texts.vehicleText}${end}`;
    case GroupBarEnums.empty:
      return '';
    default:
      return `${Texts.noMoreFilter_First}${activeGroupName}${end}`;
  }
};

const VehicleList = (props: VehicleListProps) => {
  const {
    sections,
    showMax,
    scrollDownCallback,
    scrollUpCallback,
    scrollEndCallback,
    activeGroupId,
    activeGroupName,
    isHideFooter = false,
    setVehPopData,
    setVendorListModalData,
    filterNoResult,
    noMatchProps,
    isNoMatch,
    unUrlProduct,
    selectFiltersExposure,
    scrollRefFn,
    closeTipPop,
    getLogKey,
    isShowBottomLoadingText,
    progress,
    priceCount,
    vehCount,
    isRecommend,
    isRefactor,
    isNoResultNew,
    osdUserBrowsed,
    ipollPosNum,
    ipollGroup,
    sceneid,
    ...passThroughProps
  } = props;
  const [priceGroupIndex, setPriceGroupIndex] = useState({});
  let allVehicleHeight = 0;

  const sectionsLen = sections.length;
  // android 少于 3 条数据时不展示打通，无法触发 scroll
  const shouldSetMinHeight = sectionsLen <= 3;
  // 是否是android且不满一屏
  const isAndroidScroll = !isIos && shouldSetMinHeight;

  const onVehicleLayout = useMemoizedFn(({ nativeEvent }) => {
    const { height } = nativeEvent.layout;
    allVehicleHeight += height;
  });

  const getPriceGroups = useMemoizedFn(sectionItem => sectionItem.priceGroup);

  const getSection = useMemoizedFn(sectionItem => {
    if (!isPriceGroup(sectionItem)) return sectionItem;
    const priceGroupSection =
      getPriceGroups(sectionItem)[
        priceGroupIndex[sectionItem.vehicleIndex] || 0
      ];

    priceGroupSection.vehicleIndex = sectionItem.vehicleIndex;
    return priceGroupSection;
  });

  const getItem = useMemoizedFn(data => {
    if (!isPriceGroup(data.section)) return lodashGet(data, 'section.data[0]');
    return getSection(data.section).vendorPriceList;
  });

  const onChangePriceGroup = useMemoizedFn(
    ({ index, vehicleIndex, vehiclePriceGroupDesc }) => {
      setPriceGroupIndex({
        ...priceGroupIndex,
        [vehicleIndex]: index,
      });
      CarLog.LogCode({
        name: '点击_列表页_价格分组项',

        vehiclePriceGroupDesc,
      });
    },
  );

  const renderItem = useMemoizedFn(data => {
    const item = getItem(data);
    const curSection = getSection(data.section);
    let vehicleExposureData: IVehicleExposureData = {};
    if (getLogKey) {
      vehicleExposureData = getLogKey(
        item && item[0],
        curSection,
        0,
        activeGroupId,
      );
    }
    const { queryVid, requestId, groupId, selectedFilters } =
      getLogDataFromState();
    const vendorIds = item
      ?.map(vendor => vendor?.reference?.vendorCode)
      ?.join(',');
    const logInfo = {
      groupId,
      groupName: activeGroupName,
      realGroupId: vehicleExposureData?.realGroupId,
      realGroupName: vehicleExposureData?.realGroupName,
    };
    const logData = {
      batchCode: getBaseResData()?.baseResponse?.code,
      currency: item?.[0]?.priceInfo?.currentCurrencyCode,
      isZhima: item?.some(i =>
        i?.allTags?.some(f => f?.labelCode === ItemLabelCodeType.DEPOSITFREE),
      )
        ? 1
        : 0,
    };

    vehicleExposureData = { ...vehicleExposureData, ...logData };
    // 取消重定｜推荐车型不展示AI标签
    const isSupportAiSort =
      GetABCache.isAiSort2() && !curSection?.modifySameVehicle && !isRecommend;
    const isBrowsed =
      isSupportAiSort &&
      isOsdUserBrowsed(curSection?.vehicleKey, osdUserBrowsed);
    let aiRecommendCodeName = [];
    if (isSupportAiSort) {
      aiRecommendCodeName = vehicleExposureData?.vehicleTags?.map(
        tagItem => `${tagItem.type}_${tagItem.tag}`,
      );
    }
    const buryData = {
      pageId: Channel.getPageId().List.ID,
      queryVid,
      requestId,
      ...logInfo,
      vehicleGroupId: activeGroupId,
      vehicleGroupName: activeGroupName,
      selectedFilters,
      vehicleData: vehicleExposureData,
      if_viewd: isBrowsed,
      aiRecommendCodeName,
      pageLoadingStatus:
        progress === ListConstants.hasAllRequestCompleteProgress ? 2 : 1,
      vehicleCode: vehicleExposureData?.vehicleCode,
      batchNo: curSection?.batchNo,
      vehicleCount: vehCount,
      priceCount,
      vendorIds,
      vehicleIndex: vehicleExposureData?.vehicleIndex,
      minTPrice: vehicleExposureData?.minTPrice,
    };
    const Container = isAndroidScroll ? AndroidHandlerView : View;
    const idx = data?.section?.vehicleIndex;
    const isOSDListIpoll = GetABCache.isOSDListIpoll();
    const env = xEnv.getDevEnv()?.toLowerCase();
    return (
      <Container
        scrollEndCallback={scrollEndCallback}
        scrollDownCallback={scrollDownCallback}
        scrollUpCallback={scrollUpCallback}
        testID={CarLog.LogExposure({
          key: LogKey.c_car_trace_list_vehicle_exposure_222013,
          ...buryData,
        })}
      >
        {idx === ipollPosNum &&
          activeGroupId === ipollGroup &&
          isOSDListIpoll &&
          !!sceneid && (
            <IUrs
              sceneId={sceneid}
              env={env}
              bizId="CAR"
              locale="zh-CN"
              containerStyle={styles.ipollContainer}
            />
          )}
        <Vehicle
          data={data.section}
          unUrlProduct={unUrlProduct}
          item={item}
          section={curSection}
          priceGroupItem={getPriceGroups(data.section)}
          priceGroupIndex={priceGroupIndex}
          onLayout={shouldSetMinHeight && onVehicleLayout}
          onChangePriceGroup={onChangePriceGroup}
          setVehPopData={setVehPopData}
          setVendorListModalData={setVendorListModalData}
          pageRef={props.pageRef}
          activeGroupId={activeGroupId}
          vendorListEnterData={curSection.vendorListEnterData}
          closeTipPop={closeTipPop}
          isRecommend={isRecommend}
          isRefactor={isRefactor}
          logInfo={logInfo}
          isNoResultNew={isNoResultNew}
        />
      </Container>
    );

    // eslint-disable-next-line max-len
  });

  const renderSectionFooter = useMemoizedFn(sectionProps => (
    <SectionFooterContainer
      shouldSetMinHeight={false}
      vehicleIndex={sectionProps.section.vehicleIndex}
      section={sectionProps.section}
      vehicleTotalHeight={allVehicleHeight}
      sectionsLen={sectionsLen}
      activeGroupId={activeGroupId}
    />
  ));

  const showTipList = false;
  return (
    <SectionListWithControl
      ref={scrollRefFn}
      selectFiltersExposure={selectFiltersExposure}
      sections={filterNoResult ? [] : sections}
      renderItem={renderItem}
      footerContent={<VehicleListBottomText />}
      isHideFooter={isHideFooter}
      renderSectionFooter={renderSectionFooter}
      ListHeaderExtraComponent={cacheDom.TipList(
        props.resetCache,
        activeGroupId,
        showTipList,
        filterNoResult,
        noMatchProps,
        props.isFail,
      )}
      isAndroidScroll={isAndroidScroll}
      ListEmptyComponent={
        !noMatchProps &&
        NoMatch(getNoMatchText(activeGroupName), selectFiltersExposure)
      }
      scrollDownCallback={scrollDownCallback}
      scrollUpCallback={scrollUpCallback}
      scrollEndCallback={scrollEndCallback}
      {...passThroughProps}
    />
  );
};

export default memo(VehicleList);
