import React from 'react';
import { XView as View } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './vendorListModalVehicleInfoC2xStyles.module.scss';
import { SimpleVehicleDesc } from '../../../../ComponentBusiness/CarVehicleDescribe';
import BbkCarImage from '../../../../ComponentBusiness/CarImage';
import { CarLog } from '../../../../Util/Index';
import { LogKeyDev } from '../../../../Constants/Index';
import { getBreakLabel } from '../../../../State/List/VehicleListMappers';

const { getPixel, useMemoizedFn } = BbkUtils;
const styles = StyleSheet.create({
  simpleVehicleDescTextStyle: {
    color: color.C_555555,
    ...font.body3LightStyle,
  },
  simpleVehicleDescWrapStyle: {
    marginRight: getPixel(49),
  },
  vehicleImage: {
    width: getPixel(168),
    height: getPixel(104),
  },
});

const VendorListModalVehicleInfo = ({
  isRefactor,
  vehicleDesc,
  vehicleCode,
  groupId,
}) => {
  const {
    imgUrl,
    vehicleLabelsGroupName = [],
    vehicleLabelsHorizontal = [],
    vehicleLabels = [],
  } = vehicleDesc;
  if (!imgUrl) {
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_vendorlist_vehicleimg_miss,
      info: {
        eventResult: false,
        vehicleCode,
      },
    });
  }
  const simpleVehicleDescOnPress = useMemoizedFn(luggageCount => {
    CarLog.LogCode({
      name: '点击_列表页_行李箱解释',

      groupId,
      ctripVehicleId: vehicleCode,
      info: {
        luggageCount,
      },
    });
  });
  const vehicleAllLabels = React.useMemo(
    () => [
      ...vehicleLabelsGroupName,
      ...vehicleLabelsHorizontal,
      ...vehicleLabels,
    ],

    [vehicleLabelsGroupName, vehicleLabelsHorizontal, vehicleLabels],
  );
  const { firstLabels = [], secondLabels = [] } = isRefactor
    ? getBreakLabel(vehicleAllLabels)
    : {};
  return (
    <View className={c2xStyles.vehicleInfoWrap}>
      <BbkCarImage
        source={{ uri: imgUrl }}
        resizeMode="cover"
        style={styles.vehicleImage}
      />

      {isRefactor ? (
        <View className={c2xStyles.vehicleLabelWrap}>
          <SimpleVehicleDesc
            data={firstLabels}
            textStyle={styles.simpleVehicleDescTextStyle}
            pressHandle={simpleVehicleDescOnPress}
            wrapStyle={styles.simpleVehicleDescWrapStyle}
          />

          <SimpleVehicleDesc
            data={secondLabels}
            textStyle={styles.simpleVehicleDescTextStyle}
            pressHandle={simpleVehicleDescOnPress}
            wrapStyle={styles.simpleVehicleDescWrapStyle}
          />
        </View>
      ) : (
        <View className={c2xStyles.vehicleLabelWrap}>
          <SimpleVehicleDesc
            data={vehicleAllLabels}
            lastIsBlock={true}
            textStyle={styles.simpleVehicleDescTextStyle}
            pressHandle={simpleVehicleDescOnPress}
            wrapStyle={styles.simpleVehicleDescWrapStyle}
          />
        </View>
      )}
    </View>
  );
};
export default React.memo(VendorListModalVehicleInfo);
