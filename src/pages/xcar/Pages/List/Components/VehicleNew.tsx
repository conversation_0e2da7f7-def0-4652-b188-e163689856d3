import { get as lodashGet } from 'lodash-es';
import Image from '@c2x/components/Image';
import {
  xMergeStyles,
  XLinearGradient as LinearGradient,
  XView as View,
  XImageBackground as ImageBackground,
  XViewExposure,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useCallback, CSSProperties } from 'react';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { icon, color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import {
  ProductListType,
  VendorPriceListType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xVehicleStyle from './vehicleNewC2xVehicleStyle.module.scss';
import { QueryPackageComparisonRequestType } from '../../../Types/Dto/QueryPackageComparison';
import BbkCarVehicleDesc from '../../../ComponentBusiness/CarVehicleDescribe';
import {
  RecommendEventResult,
  RecommendType,
} from '../../../Constants/ListEnum';
import BbkVehicleName, {
  VehicleNameType,
} from '../../../ComponentBusiness/CarVehicleName';
import { getLocalDayJs } from '../../../Components/Calendar/Method';
import BbkCarImage from '../../../ComponentBusiness/CarImage';
import Enums from '../../../ComponentBusiness/Common/src/Enums';
import BbkCarRightIcon from '../../../ComponentBusiness/RightIcon';
import { VendorListEnterRow } from '../../../ComponentBusiness/VendorListEnter/index';
import DetailVendorNew from '../../VendorList/Components/VendorIsdNew';

import { VehicleListStyle as style } from '../Styles';
import Texts from '../Texts';
import {
  CarLog,
  Utils,
  AppContext,
  CarABTesting,
  Channel,
  CarServerABTesting,
} from '../../../Util/Index';
import { ImageUrl } from '../../../Constants/Index';
import {
  setListStatusData,
  listStatusKeyList,
} from '../../../Global/Cache/ListReqAndResData';
import { UrlFilterNoMatch } from './Index';
import {
  getVendorListData,
  getVehicleItemData,
  getVendorListEnterData,
  validateIsSoldOut,
  packageVendorParam,
  getVendorListFirstScreenParam,
  getVendorPriceListByVehicleCode,
  validateRenderVehicleIsEqual,
  hasFees,
  getTotalPriceModalData,
  getTotalPriceModalDataV2,
} from '../../../State/List/VehicleListMappers';
import {
  getSelectedFilterString,
  getSelectedFilterLabelsString,
  getSelectedFilterGroupString,
} from '../../../State/List/Mappers';
import {
  getShowLikeLabel,
  getSelfServiceLogData,
  getIsUserBrowsed,
  getIsUserGroupBrowsedCount,
  setVehicleCodeBigId,
} from '../Method';
import GroupEnterList from './GroupEnterList';
import { getVendorListBasePageParam } from '../../../Global/Cache/ListResSelectors';
import { validateTimeIsOutIsd } from '../../../Helpers/Index';
import UITestId from '../../../Constants/UITestID';
import VehRecomLabel from './VehicleRecommendLabel';
import {
  mappingLabel,
  getSecretBoxLogFromReference,
  getHasSelfService,
} from '../../../State/List/Method';
import RecommendVehicleTip from './RecommendVehicleTip';
import {
  PageParamType,
  VendorListRecommendLogType,
} from '../../../Types/Dto/QueryVehicleDetailListRequestType';
import { openUrlVendorList } from '../../../State/VendorList/Mappers';
import { PlateBgSize } from '../../../ComponentBusiness/CarVehicleName/src/LicensePlate';
import { ListGoToBookingType, SecretBoxModalDataType } from '../Types';
import VehicleTags from './VehicleTags';
import EasyLife2024PriceEnter from './EasyLife2024PriceEnter';

const {
  selector,
  htmlDecode,
  getPixel,
  getProcImageUrl,
  ProcImageParamsType,
  useMemoizedFn,
} = BbkUtils;
const noop = () => {};

export enum VehicleNewFromPage {
  fromVendorList = 1,
}

interface IVehicleProps {
  locationAndDate?: string;
  rentalLocation?: any;
  expandLocationAndDate?: string;
  age?: number | string;
  activeGroupId?: string;
  activeGroupName?: string;
  vehicleIndex?: number;
  hotType?: any;
  rentCenter?: any;
  storeList?: any;
  pTime?: string;
  setVendorListModalData?: (data) => void;
  setTimeOutPopData?: (data) => void;
  onLayout?: (e) => void;
  theme?: any;
  vehicleList?: any;
  data?: any;
  soldOutList?: Array<string>;
  vehicleSoldOutList?: Array<string>;
  selectedFilters?: any;
  setTotalPriceModalData?: (data) => void;
  setTipPopData?: (data) => void;
  closeTipPop?: () => void;
  unUrlProduct?: boolean;
  progressIsFinish?: boolean;
  setPriceSummaryModal?: (data) => void;
  isDiffLocation?: boolean;
  item?: any;
  section?: any;
  priceGroupItem?: any;
  saleOutList?: Array<string>;
  abVersion?: string;
  recommendEventResult?: RecommendEventResult;
  isNewSearchNoResult?: boolean;
  isRecommend?: boolean;
  vehicleCount?: number;
  isFilteredRecommend?: boolean;
  isNotPoiRange?: boolean;
  onShowLocationAndDatePop?: (data) => void;
  priceGroupIndex?: any;
  onChangePriceGroup?: any;
  setVehPopData?: any;
  pageRef?: any;
  fromPage?: VehicleNewFromPage | string;
  wrapperStyle?: CSSProperties;
  vendorListIogInfo?: VendorListRecommendLogType;
  recommendVehicleIndex?: number;
  recommendProducts?: ProductListType[];
  productGroupCodeUesd?: string;
  setDateInfo?: (data: any) => void;
  setLocationInfo?: (data: any) => void;
  pickupCityId?: number;
  isDifferentLocation?: boolean;
  recUniqsign?: string;
  setSecretBoxModalData?: (data: SecretBoxModalDataType) => void;
  listGoToBooking?: (data: ListGoToBookingType) => void;
  goToBookingEasyLife2024?: (data: {
    vendorPriceList: Array<VendorPriceListType>;
    productRef: any;
    vehicleIndex?: number;
  }) => void;
  queryPackageComparison?: (
    data: Partial<QueryPackageComparisonRequestType>,
  ) => void;
  queryVehicleDetailList?: (data: {
    reqParam: PageParamType;
    callbackFun?: () => void;
  }) => void;
  cityId?: string;
  dropOffCityId?: string;
  userBrowsingHistories?: Map<string, object>;
  setUserBrowsingHistory?: (data: {
    activeGroupId: string;
    vehicleCode: string;
    license: string;
  }) => void;
  setVendorListFirstScreenParam?: (data: any) => void;
  searchKeyWord?: string;
}

const getSecretBoxGroupedPriceList = vendorPriceList =>
  vendorPriceList
    ?.map(vendorPrice => {
      return getSecretBoxLogFromReference(vendorPrice?.reference);
    })
    ?.filter(v => !!v)
    ?.join(',');
const VehicleStyle = StyleSheet.create({
  licenseTagStyle: {
    borderRadius: BbkUtils.getPixel(4),
    paddingTop: 0,
    paddingBottom: 0,
    paddingLeft: getPixel(6),
    paddingRight: getPixel(6),
    marginTop: getPixel(-2),
  },
  vehicleHeaderWrap: { borderBottomWidth: 0, paddingTop: 0, paddingBottom: 0 },
  nameTitleTextStyle: { ...font.title2MediumStyle },
  hotLikeSplit: { top: getPixel(38) },
  mt7: {
    marginTop: getPixel(7),
  },
  secondDesc: {
    marginTop: getPixel(4),
    marginBottom: getPixel(14),
  },
  easyLifeSecondDesc: {
    marginBottom: getPixel(0),
    marginTop: getPixel(0),
  },
  easyLifeFirstDesc: {
    marginBottom: getPixel(0),
    marginTop: getPixel(6),
  },
  splitStyle_C: {
    marginLeft: getPixel(3),
    marginRight: getPixel(3),
  },
  vendorWrapStyle: {
    marginLeft: 0,
    marginRight: 0,
    backgroundColor: color.white,
    borderRadius: 0,
  },
  userBrowsingVendorWrapStyle: {
    marginLeft: 0,
    marginRight: 0,
    backgroundColor: color.C_F6F8FB,
    borderRadius: 0,
  },
  vendorWrapStyleNew: {
    marginLeft: getPixel(0),
    marginRight: getPixel(0),
    backgroundColor: color.white,
    borderRadius: 0,
    paddingTop: getPixel(24),
  },
  userBrowsingVendorWrapStyleNew: {
    marginLeft: getPixel(0),
    marginRight: getPixel(0),
    backgroundColor: color.C_F6F8FB,
    borderRadius: 0,
    paddingTop: getPixel(24),
  },
  vehicleNewWrap: {
    marginLeft: getPixel(16),
    marginRight: getPixel(16),
    borderRadius: getPixel(12),
  },
  topVendorNameStyle: {
    marginRight: getPixel(20),
  },
  vendorContainerStyle: {
    marginTop: 0,
  },
  vendorContainerStyleNew: {
    marginTop: 0,
    marginLeft: getPixel(0),
    marginRight: getPixel(0),
  },
  vendorContentStyleNew: {
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
  },
});

/*eslint-disable */
const isEqualVehicle = (prevProps, nextProps) => {
  const preSection = lodashGet(prevProps, 'section');
  const nextSection = lodashGet(nextProps, 'section');

  const preData = lodashGet(prevProps, 'data');
  const nextData = lodashGet(nextProps, 'data');

  const preUnUrlProduct = lodashGet(prevProps, 'unUrlProduct');
  const nextUnUrlProduct = lodashGet(nextProps, 'unUrlProduct');

  const preSoldOutList = lodashGet(prevProps, 'soldOutList');
  const nextSoldOutList = lodashGet(nextProps, 'soldOutList');

  const preProgressIsFinish = lodashGet(prevProps, 'progressIsFinish');
  const nextProgressIsFinish = lodashGet(nextProps, 'progressIsFinish');

  // 之前的车型报价是否已浏览
  const preIsUserBrowsed = getIsUserBrowsedByProps(prevProps);
  // 之前的车型分组情况下浏览的车型数
  const preIsUserGroupBrowsed = getUserGroupBrowsedCountByProps(prevProps);

  // 之后的车型报价是否已浏览
  const nextIsUserBrowsed = getIsUserBrowsedByProps(nextProps);
  // 之后的车型分组情况下浏览的车型数
  const nextIsUserGroupBrowsed = getUserGroupBrowsedCountByProps(nextProps);

  return (
    preSection === nextSection &&
    preData === nextData &&
    preUnUrlProduct === nextUnUrlProduct &&
    preSoldOutList === nextSoldOutList &&
    preProgressIsFinish === nextProgressIsFinish &&
    preIsUserBrowsed === nextIsUserBrowsed &&
    preIsUserGroupBrowsed === nextIsUserGroupBrowsed
  );
};

const getIsUserBrowsedByProps = props => {
  const { data, section, userBrowsingHistories } = props || {};
  const { vehicleCode } = section || {};
  const { productRef } = data || {};
  // 判断车型报价是否已浏览
  return getIsUserBrowsed({
    vehicleCode,
    license: productRef?.license,
    userBrowsingHistories,
  });
};

const getUserGroupBrowsedCountByProps = props => {
  const { data, userBrowsingHistories } = props || {};
  return getIsUserGroupBrowsedCount({
    userBrowsingHistories,
    priceGroup: data?.priceGroup,
  });
};

const getIsUserGroupBrowsedByProps = props => {
  const { data } = props || {};
  // 判断车型卡片是否标记为已浏览
  // isGroup = true 时，要判断车型组是否所有报价是否已浏览
  return getUserGroupBrowsedCountByProps(props) === data?.priceGroup?.length;
};

const isEqualVehicleForListPage = (prevProps, nextProps) => {
  const preData = lodashGet(prevProps, 'data');
  const nextData = lodashGet(nextProps, 'data');

  const preUnUrlProduct = lodashGet(prevProps, 'unUrlProduct');
  const nextUnUrlProduct = lodashGet(nextProps, 'unUrlProduct');

  const preSoldOutList = lodashGet(prevProps, 'soldOutList');
  const nextSoldOutList = lodashGet(nextProps, 'soldOutList');

  const preVehicleSoldOutList = lodashGet(prevProps, 'vehicleSoldOutList');
  const nextVehicleSoldOutList = lodashGet(nextProps, 'vehicleSoldOutList');

  const preSelectedFilters = lodashGet(prevProps, 'selectedFilters');
  const nextSelectedFilters = lodashGet(nextProps, 'selectedFilters');

  // 之前的车型报价是否已浏览
  const preIsUserBrowsed = getIsUserBrowsedByProps(prevProps);
  // 之前的车型分组情况下浏览的车型数
  const preIsUserGroupBrowsed = getUserGroupBrowsedCountByProps(prevProps);

  // 之后的车型报价是否已浏览
  const nextIsUserBrowsed = getIsUserBrowsedByProps(nextProps);
  // 之后的车型分组情况下浏览的车型数
  const nextIsUserGroupBrowsed = getUserGroupBrowsedCountByProps(nextProps);

  const baseIsEqual =
    preUnUrlProduct === nextUnUrlProduct &&
    JSON.stringify(preSelectedFilters) ===
      JSON.stringify(nextSelectedFilters) &&
    preSoldOutList === nextSoldOutList &&
    preVehicleSoldOutList === nextVehicleSoldOutList &&
    preIsUserBrowsed === nextIsUserBrowsed &&
    preIsUserGroupBrowsed === nextIsUserGroupBrowsed;

  const isEqual =
    baseIsEqual && validateRenderVehicleIsEqual(preData, nextData);
  return isEqual;
};

enum ClickPosition {
  vehicle = '3',
  book = '2',
  price = '1',
}

export const VehicleNew = memo(
  (props: IVehicleProps) => {
    const {
      data,
      section,
      theme,
      onLayout = noop,
      priceGroupItem,
      unUrlProduct,
      progressIsFinish,
      vehicleList,
      activeGroupId,
      activeGroupName,
      setVendorListModalData,
      soldOutList,
      vehicleSoldOutList,
      selectedFilters,
      isDiffLocation,
      setTotalPriceModalData = Utils.noop,
      setTipPopData = Utils.noop,
      closeTipPop = Utils.noop,
      pTime,
      setTimeOutPopData,
      isNewSearchNoResult,
      isRecommend,
      isFilteredRecommend,
      vehicleCount,
      recommendEventResult,
      fromPage,
      wrapperStyle,
      vendorListIogInfo,
      recommendVehicleIndex,
      recommendProducts,
      productGroupCodeUesd,
      rentalLocation,
      setDateInfo,
      setLocationInfo,
      pickupCityId,
      isDifferentLocation,
      recUniqsign,
      setSecretBoxModalData,
      listGoToBooking,
      goToBookingEasyLife2024,
      queryPackageComparison,
      queryVehicleDetailList,
      setVendorListFirstScreenParam,
      cityId,
      dropOffCityId,
      userBrowsingHistories,
      setUserBrowsingHistory = Utils.noop, // 列表页车型组才添加浏览记录
      searchKeyWord,
      ...otherData
    } = props;

    const {
      vehicleCode,
      hotType,
      isSpecialized,
      vehicleIndex,
      isGroup,
      recommendType,
      subStrategyType,
      recommendInfo,
      pickUpAvailableTime,
      returnAvailableTime,
      availableLocation,
      longitude,
      latitude,
      cid,
      cname,
      groupNameScope,
      passengerNoRange,
      type = 0,
      extraLog,
    } = section;
    const isRecommendVehicle = !!recommendType;
    const isSecretBox = type === 1;
    const isEasyLife2024 = type === 2;
    const { productTopInfo, priceSize, isCredit, productRef } = data;
    // 置顶分页兼容处理
    const isBrowseProduct = productTopInfo;
    const isUrlTop = productTopInfo === 2;
    const isISDShelves2B = CarServerABTesting.isISDShelves2B();
    const newVehicleList =
      fromPage === VehicleNewFromPage.fromVendorList ? vehicleList : null;
    const easyLife2024ExtraLog = {
      ...(extraLog || {}),
      vehicleCode,
      vehicleIndex,
    };
    const vehicleItemData = getVehicleItemData(
      vehicleCode,
      hotType,
      isSpecialized,
      !isGroup,
      undefined, // modifySameVehicle
      productRef, // 卡拉比3期牌照处理
      newVehicleList,
      groupNameScope, // 盲盒可能取到的车型组
      passengerNoRange, // 盲盒车座范围
      isEasyLife2024,
    );
    const {
      vehicleHeader,
      vehicleDesc,
      vehiclesSetId,
      style: vehicleNameStyle,
    } = vehicleItemData;
    const {
      vehicleName: newVehicleName,
      groupName,
      groupId,
      isSimilar,
      isHotLabel,
      licenseLabel,
      licenseType,
      mediaTypes,
    } = vehicleHeader;
    // 多年款车型名称的处理
    const vehicleName =
      fromPage === VehicleNewFromPage.fromVendorList && vehicleNameStyle
        ? `${newVehicleName} (${vehicleNameStyle})`
        : newVehicleName;
    const {
      imgUrl,
      vehicleLabelsGroupName,
      vehicleLabelsHorizontal = [],
      vehicleLabels,
      recommendLabels,
      isNewEnergy,
      secretBoxGroupLabel,
      secretBoxSecondLabels,
    } = vehicleDesc;
    const recommendGroupId = isRecommend
      ? activeGroupId
      : (productGroupCodeUesd ?? '');
    // 获取某个车型的vendorPriceList
    // 推荐车型，车型组透传(推荐车型组切换不查询接口)
    const vendorPriceList = getVendorPriceListByVehicleCode(
      vehicleCode,
      productTopInfo,
      recommendGroupId,
      recommendProducts,
      productRef?.license,
    );
    // 获取第一排描述信息， 车型组|座位数|门数
    // 如果是车型分组的车型，第一排展示 车型组|座位数|门数|自动OR手动
    // 第二排展示车型组信息
    let firstLabels = [...vehicleLabelsGroupName];
    // 盲盒第一排描述，可选|车型范围
    let secondLabels = [];
    // 是否多行展示车型标签(车型标签+正常标签)
    const isMultipleLabel =
      vehicleLabels?.length + vehicleLabelsHorizontal?.length > 3;
    // 盲盒第二排描述，座位数|自动OR手动
    if (!data.isGroup || isMultipleLabel) {
      secondLabels = secondLabels.concat(vehicleLabels);
    }
    vehicleLabelsHorizontal.map(item => {
      if (
        item?.icon?.iconContent === icon.electricCar ||
        item?.icon?.iconContent === icon.gasoline3 ||
        item?.icon?.iconContent === icon.fuelType
      ) {
        secondLabels.push(mappingLabel(item));
      } else {
        firstLabels.push(mappingLabel(item));
      }
    });
    if (data.isGroup && !isMultipleLabel) {
      vehicleLabels.map(vitem => {
        firstLabels.push(mappingLabel(vitem));
      });
    }

    firstLabels = [...firstLabels, ...secondLabels];
    secondLabels = [];

    const showLikeLabel = getShowLikeLabel({
      activeGroupId,
      vehicleIndex,
      hotType,
    });

    // 判断车型报价是否已浏览
    const isUserBrowsed = getIsUserBrowsedByProps(props);

    // 判断车型卡片是否标记为已浏览
    const isUserGroupBrowsed = getIsUserGroupBrowsedByProps(props);

    const isCardUserBrowsed = isGroup ? isUserGroupBrowsed : isUserBrowsed;

    const vehicleInfo = { vehicleCode, vehicleIndex };
    // 校验是否售罄 推荐车型且非POI推荐无售罄状态
    // 推荐车型，车型组透传(推荐车型组切换不查询接口)
    const isSoldOut = validateIsSoldOut(
      soldOutList,
      vehicleSoldOutList,
      data,
      isRecommendVehicle,
      recommendGroupId,
      recommendProducts,
    );
    // 推荐车型，车型组透传(推荐车型组切换不查询接口)
    const vendorListEnterData: any = getVendorListEnterData(
      data,
      vehicleInfo,
      recommendGroupId,
    );
    const hasCreditRent = isCredit;

    let topVendor = null; // url置顶的车型供应商
    if (isUrlTop) {
      topVendor = (getVendorListData(vendorPriceList, vehicleInfo) || [])[0];
      if (topVendor) {
        topVendor = packageVendorParam(topVendor.vendor, isDiffLocation);
      }
    }

    const VehicleContainer: any = isUrlTop ? View : BbkTouchable;
    
    const getBaseLogInfo = (passInfo = {}) => {
      const isSelfService = getSelfServiceLogData(props?.section?.outTags);
      const selectedFiltersString = getSelectedFilterString(selectedFilters);
      return {
        vehicleIndex, // 车型位置
        selectedFilters: selectedFiltersString, // 筛选项
        realGroupId: groupId, // 车型组id
        realGroupName: groupName,
        groupId: activeGroupId,
        groupName: activeGroupName,
        info: {
          vehicleCount, // 车型数量
          selectedFilterType: getSelectedFilterGroupString(selectedFilters), // 筛选项分组名称
          isSelfService,
          virtualParent: activeGroupId,
          bigGroupRank: vehicleIndex,
          bigGroupId: data?.priceGroup?.[0]?.vehicleCode, // 大车型组id
          filterInfo: selectedFiltersString,
          ...data.extraLog, // 额外日志信息
          ...passInfo,
        },
      };
    };

    const getVendorListPageParam = (
      vehicleCode,
      groupCode,
      recommendType = '',
      vehiclesSetId,
      productRef = null,
      sign = '',
    ) => {
      return {
        ctripVehicleId: vehicleCode,
        productRef,
        groupCode, // 注意这里需要的是真实车型组，而非“全部车型”，“热销”这样的虚拟车型组
        ...getVendorListBasePageParam(sign),
        filterLabelsStr: getSelectedFilterLabelsString(selectedFilters),
        browVendorCode: topVendor?.vendorCode,
        extraMaps: {
          recommendType, // 推荐类型
        },
        vehiclesSetId,
      };
    };

    const vehicleClickLog = (key, logInfo = {}) => {
      CarLog.LogCode({
        name: key,
        vehicleCode, // 车型id
        isCreditRent: hasCreditRent,
        vehicleName,
        groupName: activeGroupName,
        recommendEventResult,
        pickupCityId,
        isDifferentLocation,
        recommendType,
        recommendTimeType: subStrategyType,
        vechicleLable: showLikeLabel ? Texts.youMayLikeLabel : '', // 车型标签，记录猜你喜欢字样
        ...getBaseLogInfo(),
        ...logInfo,
      });
    };

    const handleTimepassed = (ptime, rtime) => {
      const curMinutes = Math.ceil(parseInt(dayjs().format('mm')) / 15) * 15;
      const timeDiff =
        Math.ceil(dayjs(rtime).diff(dayjs(ptime), 'minute') / 15) * 15;
      const newRentalDate = {
        pickup: dayjs().add(4, 'hours').minute(curMinutes),
        dropoff: dayjs()
          .add(4, 'hours')
          .minute(curMinutes + timeDiff),
      };
      setDateInfo(newRentalDate);
      Toast.show('取车时间已过当前时间，已为您修改取车时间', 2);
    };

    const timeChange = (ptime, rtime) => {
      const newRentalDate = { pickup: ptime, dropoff: rtime };
      if (dayjs(ptime).diff(getLocalDayJs(), 'seconds') < 0) {
        handleTimepassed(ptime, rtime);
      } else {
        setDateInfo(newRentalDate);
      }
    };

    const areaChange = (name, lat, lng, cid, cname) => {
      const area = {
        name,
        lat,
        lng,
      };
      const pickUp = {
        ...rentalLocation.pickUp,
        area,
        cid: cid || rentalLocation.pickUp.cid,
        cname: cname || rentalLocation.pickUp.cname,
      };
      setLocationInfo({
        isShowDropOff: false,
        pickUp,
      });
    };

    // 获取产品详情页面参数
    const getPageParam = (
      vehicleCode,
      groupCode,
      priceListLen,
      sign = '',
      productRef,
    ) => ({
      pageParam: getVendorListPageParam(
        vehicleCode,
        groupCode,
        recommendType,
        vehiclesSetId,
        productRef, // 牌照透传参数
        sign, // 推荐车型的缓存标识
      ),
      vehicleIndex: getBaseLogInfo().vehicleIndex,
      bigGroupId: data.priceGroup?.[0]?.vehicleCode, // 大车型组id
      vendorListFirstScreenParam: getVendorListFirstScreenParam(
        vehicleCode,
        vehicleList,
        productRef,
      ),
      priceListLen,
      fromPage,
    });

    // 推荐车型点击事件 跳转详情页，且带入取还车时间
    const recommendVehiclePress = (vehicleCode, productRef?) => {
      switch (recommendType) {
        case RecommendType.Time:
          timeChange(pickUpAvailableTime, returnAvailableTime);
          // 设置当前列表状态为更新了取还车时间区域信息用于刷新列表页
          setListStatusData(
            listStatusKeyList.updateLocationAndDateByRecommend,
            true,
          );
          break;
        case RecommendType.SameArea:
        case RecommendType.City:
          areaChange(availableLocation, latitude, longitude, cid, cname);
          // 设置当前列表状态为更新了取还车时间区域信息用于刷新列表页
          setListStatusData(
            listStatusKeyList.updateLocationAndDateByRecommend,
            true,
          );
          break;
        default:
          break;
      }
      const vendorListPageParam = getPageParam(
        vehicleCode,
        groupId,
        priceSize,
        recUniqsign,
        productRef,
      );
      AppContext.PageInstance.push(
        Channel.getPageId().VendorList.EN,
        vendorListPageParam,
      );
      vehicleClickLog('点击_列表页_推荐补偿车型');
    };

    const gotoDetail = (
      vehicleCode,
      groupCode,
      hasCreditRent,
      priceListLen,
      productRef,
      logData: {
        cardPriceIndex?: number;
      } = {},
    ) => {
      // 推荐车型点击
      if (isRecommendVehicle) {
        recommendVehiclePress(vehicleCode, productRef);
        return;
      }
      // 添加时间过期校验
      if (validateTimeIsOutIsd(pTime)) {
        setTimeOutPopData({
          visible: true,
        });
        return;
      }

      const vendorListPageParam = getPageParam(
        vehicleCode,
        groupCode,
        priceListLen,
        '',
        productRef,
      );

      if (fromPage === VehicleNewFromPage.fromVendorList) {
        vehicleClickLog('点击_产品详情页_看了又看车型推荐列表', {
          info: {
            ...vendorListIogInfo,
            recommendVehicleCode: vehicleCode,
            recommendVehicleIndex,
          },
        });
        openUrlVendorList(vendorListPageParam);
      } else {
        const curLogInfo = getBaseLogInfo({
          license: productRef?.license,
        });
        vehicleClickLog('点击_列表页_打开报价详情弹层', {
          ...curLogInfo,
          vehicleInfo: logData,
          vehicleCode,
        });

        setVendorListFirstScreenParam(
          vendorListPageParam.vendorListFirstScreenParam,
        );

        AppContext.PageInstance.push(Channel.getPageId().VendorList.EN, {
          ...vendorListPageParam,
          cardPriceIndex: logData?.cardPriceIndex || 0,
          isSelectedVendorTop1: data?.extraLog?.isSelectedVendorTop1,
          isSetFirstScreenBefore: true,
        });

        // 跳转后添加已浏览标记
        setUserBrowsingHistory?.({
          activeGroupId,
          vehicleCode,
          license: productRef?.license,
        });
      }
    };
    const gotoBooking = useMemoizedFn((clickPostion = ClickPosition.book) => {
      if (isSoldOut) {
        return;
      }
      CarLog.LogCode({
        name: '点击_列表页_无忧租2024车型卡片',

        ...easyLife2024ExtraLog,
        clickPostion,
      });
      goToBookingEasyLife2024?.({
        vendorPriceList,
        productRef,
        vehicleIndex,
      });

      // 添加已浏览标记
      setUserBrowsingHistory?.({
        activeGroupId,
        vehicleCode,
        license: productRef?.license,
      });
    });
    const onPressEasyLife2024Enter = useMemoizedFn(() => {
      // 设置大车型ID, 即曝光里的vehicleData里排第一的vehicleCode
      setVehicleCodeBigId(vehicleCode);
      gotoBooking(ClickPosition.book);
    });
    const handleVehiclePress = () => {
      // 设置大车型ID, 即曝光里的vehicleData里排第一的vehicleCode
      setVehicleCodeBigId(vehicleCode);
      if (!AppContext.enablePush) return;
      if (isEasyLife2024) {
        // 添加已浏览标记
        setUserBrowsingHistory?.({
          activeGroupId,
          vehicleCode,
          license: productRef?.license,
        });
        gotoBooking(ClickPosition.vehicle);
        return;
      }
      if (isSecretBox) {
        const param = getVendorListPageParam(
          vehicleCode,
          groupId,
          '',
          vehiclesSetId,
        );
        queryVehicleDetailList({
          reqParam: param,
          callbackFun: undefined,
        });
        setSecretBoxModalData({
          visible: true,
          data: section,
        });
        const groupList = vendorPriceList.map(
          vendorPrice => vendorPrice?.vehicleGroup,
        );
        const groupPriceList = getSecretBoxGroupedPriceList(vendorPriceList);
        CarLog.LogCode({
          name: '点击_列表页_盲盒点击',

          info: {
            cityid: cityId,
            grouplist: groupList,
            groupPriceList,
          },
        });
      } else if (isRecommendVehicle) {
        recommendVehiclePress(vehicleCode, productRef);
      } else {
        const logData = {
          cardPriceIndex: 0,
          yearModel: vendorListEnterData?.vehicleStyle,
          plateType: vendorListEnterData?.productRef?.license,
          currentDailyPrice: vendorListEnterData?.price,
          currentOriginalyPrice: vendorListEnterData?.originPrice?.price,
          minTotalPrice: vendorListEnterData?.minTotalPrice,
        };
        gotoDetail(
          vehicleCode,
          groupId,
          hasCreditRent,
          priceSize,
          productRef,
          logData,
        );
      }
    };

    const handleVehicleImage = () => {
      CarLog.LogCode({
        name: '点击_列表页_车辆图片',

        info: {
          ctripVehicleId: vehicleCode,
          mediaType: mediaTypes,
          realGroupId: groupId,
          realGroupName: groupName,
          groupId: activeGroupId,
          groupName: activeGroupName,
          vehicleIndex,
        },
      });
      handleVehiclePress();
    };

    const handleVehicleInfo = () => {
      CarLog.LogCode({
        name: '点击_列表页_车辆信息',

        info: {
          ctripVehicleId: vehicleCode,
          realGroupId: groupId,
          realGroupName: groupName,
          groupId: activeGroupId,
          groupName: activeGroupName,
          vehicleIndex,
        },
      });
      handleVehiclePress();
    };

    const totalPricePress = useCallback(() => {
      // 设置大车型ID, 即曝光里的vehicleData里排第一的vehicleCode
      setVehicleCodeBigId(vehicleCode);
      if (isSoldOut) {
        return;
      }
      if (isRecommendVehicle) {
        recommendVehiclePress(vehicleCode);
        return;
      }
      const { setPriceSummaryModal, storeList } = props;
      const showNewTotalPriceModals = hasFees(vendorPriceList);
      const totalPriceType = isSecretBox
        ? vendorPriceList.length > 1
          ? Enums.TotalPriceModalType.SecretBox
          : Enums.TotalPriceModalType.SecretBoxVendor
        : '';
      const data = showNewTotalPriceModals
        ? getTotalPriceModalData(
            vendorPriceList,
            storeList,
            totalPriceType,
            undefined,
            CarServerABTesting.isISDShelves2B(),
          )
        : getTotalPriceModalDataV2(
            section.minDPrice,
            section.minTPrice,
            undefined,
            CarServerABTesting.isISDShelves2B(),
          );
      data.vendorListPageParam = getVendorListPageParam(
        vehicleCode,
        groupId,
        '',
        vehiclesSetId,
      );
      data.vehicleList = vehicleList;
      data.vehicleIndex = vehicleIndex;
      data.priceListLen = priceSize;
      data.vehiclesSetId = vehiclesSetId;
      data.section = section;
      if (totalPriceType === Enums.TotalPriceModalType.SecretBoxVendor) {
        data.vehicleCode = vehicleCode;
        data.vendorPriceInfo = vendorPriceList[0];
      }
      if (isEasyLife2024) {
        data.isEasyLife2024 = isEasyLife2024;
        const priceInfo0 = vendorPriceList?.[0];
        data.packageCompareRequest = {
          packageComparison: data?.packageComparison,
          reference: priceInfo0?.reference,
        };
        data.vendorPriceList = vendorPriceList;
      }
      // 如果没有fees节点需打开非价格一致性版本的总价说明
      if (showNewTotalPriceModals) {
        setPriceSummaryModal({ visible: true, data });
      } else {
        setTotalPriceModalData({ visible: true, data });
      }
      if (isEasyLife2024) {
        queryPackageComparison(data.packageCompareRequest);
      }
      closeTipPop();
      if (isEasyLife2024) {
        CarLog.LogCode({
          name: '点击_列表页_无忧租2024车型卡片',

          ...easyLife2024ExtraLog,
          clickPostion: ClickPosition.price,
        });
      } else {
        CarLog.LogCode({ name: '点击_列表页_车型总价提示' });
      }
    }, [data]);
    const imageSecretLoadError = useMemoizedFn(error => {
      CarLog.LogImageLoadFail({
        error,
        imageUrl: getProcImageUrl(
          imgUrl,
          ProcImageParamsType.listSecretBoxImage,
        ),
        expPoint: ProcImageParamsType.listSecretBoxImage,
        vehicleCode,
        fromPage,
      });
    });
    const imageLoadError = useMemoizedFn(error => {
      CarLog.LogImageLoadFail({
        error,
        imageUrl: getProcImageUrl(imgUrl, ProcImageParamsType.isdList),
        expPoint: ProcImageParamsType.isdList,
        vehicleCode,
        fromPage,
      });
    });
    const onCheckFaild = useMemoizedFn(checkInfo => {
      const { width, height, scale, scaleGap } = checkInfo || {};
      CarLog.LogImageLoadFail({
        error: '',
        imageUrl: imgUrl,
        expPoint: ProcImageParamsType.isdList,
        vehicleCode,
        fromPage,
        isOnce: true,
        width,
        height,
        scale,
        scaleGap,
      });
    });

    // 校验车图是否可点击 - 新版详情页是多年款情况下第一个年款售罄时则不可点击，反之则所有年款都售罄才不可点击
    // 推荐车型，车型组透传(推荐车型组切换不查询接口)
    const product = data?.priceGroup?.length > 0 ? data.priceGroup[0] : data;
    const isDisable = validateIsSoldOut(
      soldOutList,
      vehicleSoldOutList,
      product,
      isRecommendVehicle,
      recommendGroupId,
      recommendProducts,
    );

    // 校验是否有推荐标签
    const isRecommLabel = recommendLabels?.length > 0 && !isEasyLife2024;

    let nameTitleTextStyle = VehicleStyle.nameTitleTextStyle;
    if (isSoldOut) {
      nameTitleTextStyle = { ...nameTitleTextStyle, ...style.soldOutText };
    }
    const fromVendorList = fromPage === VehicleNewFromPage.fromVendorList;
    // 展示车型名牌照场景 1. 非盲盒 &&从营销页跳转，置顶车型 2. 无忧租一口价车型组下面车型
    const showVehicleNamePlateTag =
      ((!isSecretBox && AppContext.isMarketing && isBrowseProduct) ||
        isEasyLife2024) &&
      licenseLabel;
    const groupPriceList = getSecretBoxGroupedPriceList(vendorPriceList);
    const exposure = isSecretBox
      ? CarLog.LogExposure({
          name: '曝光_列表页_盲盒曝光',

          info: {
            cityid: cityId,
            groupPriceList,
            grouplist: vendorPriceList.map(
              vendorPrice => vendorPrice?.vehicleGroup,
            ),
          },
        })
      : CarLog.LogExposure({
          name: '曝光_产品详情页_看了又看车型推荐列表',

          info: {
            ...vendorListIogInfo,
            recommendVehicleCode: vehicleCode,
            recommendVehicleIndex,
          },
        });
    const secondVehicleDesc = isSecretBox
      ? secretBoxSecondLabels
      : secondLabels;

    // 车型置顶
    const isSelfService = getHasSelfService(section?.outTags);

    // 无忧租2024外露车辆标签
    const vehicleTags = section?.outTags?.filter(v => v.tagGroups === 1) || [];

    // 只有低碳车型角标
    let badgeViewWidth = 150;
    // 有低碳车型角标 和 推荐理由
    if (isNewEnergy && isRecommLabel) {
      badgeViewWidth = 350;
    } else if (!isNewEnergy && isRecommLabel) {
      // 只有 推荐理由
      badgeViewWidth = 210;
    }

    const isShowBadgeWrap = isNewEnergy || isRecommLabel;

    const renderPriceEnter = useMemoizedFn(() => {
      if (isUrlTop) {
        return null;
      }
      if (isEasyLife2024) {
        return (
          <View style={xMergeStyles([style.wrap_B, style.negaTop4])}>
            <EasyLife2024PriceEnter
              data={data}
              isSoldOut={isSoldOut}
              onPressLeft={totalPricePress}
              onPressRight={onPressEasyLife2024Enter}
            />
          </View>
        );
      }

      if (isGroup) {
        return (
          <GroupEnterList
            data={data}
            isRecommend={isRecommend}
            activeGroupId={activeGroupId}
            isRecommendVehicle={isRecommendVehicle}
            recommendEventResult={recommendEventResult}
            storeList={props.storeList}
            vendorSoldOutList={soldOutList}
            vehicleSoldOutList={vehicleSoldOutList}
            setPriceSummaryModal={
              isRecommendVehicle
                ? recommendVehiclePress
                : props.setPriceSummaryModal
            }
            setTotalPriceModalData={
              isRecommendVehicle
                ? recommendVehiclePress
                : props.setTotalPriceModalData
            }
            handleGotoDetail={gotoDetail}
            baseLogInfo={getBaseLogInfo()}
            vendorListBasePageParam={getVendorListBasePageParam()}
            abVersion={props.abVersion}
            showBorder={true}
            cityId={cityId}
            dropOffCityId={dropOffCityId}
            userBrowsingHistories={userBrowsingHistories}
            isUserGroupBrowsed={isUserGroupBrowsed}
            searchKeyWord={searchKeyWord}
          />
        );
      }

      return (
        <VendorListEnterRow
          isRecommend={isRecommend}
          showBorder={true}
          isSoldOut={isSoldOut}
          isUserBrowsed={isUserBrowsed}
          {...vendorListEnterData}
          totalPricePress={totalPricePress}
          isSecretBox={isSecretBox}
        />
      );
    });

    let cardBg = color.white;
    if (isSecretBox) {
      cardBg = color.listSecretBoxBg;
    }
    const isUserBrowsedFlag =
      (isGroup && isUserGroupBrowsed) || (!isGroup && isUserBrowsed);
    // 设置车型卡片背景色
    if (isUserBrowsedFlag) {
      cardBg = color.C_F6F8FB;
    }

    const descTextColor = color.C_555555;
    return (
      <XViewExposure testID={exposure}>
        <View
          testID={UITestId.car_testid_page_list_vehicle_sku}
          style={xMergeStyles([
            { backgroundColor: cardBg },
            isNewSearchNoResult && VehicleStyle.vehicleNewWrap,
            wrapperStyle,
          ])}
        >
          <BbkTouchable
            onPress={handleVehiclePress}
            testID={`${UITestId.car_testid_page_list_vehicle_item_badge}_${vehicleName}`}
            style={xMergeStyles([
              style.badgeWrapper,
              !isShowBadgeWrap && style.jCflexEnd,
            ])}
          >
            {isShowBadgeWrap && (
              <LinearGradient
                start={{ x: 0, y: 0.5 }}
                end={{ x: 1, y: 0.5 }}
                locations={[0, 1]}
                colors={[color.R_223_248_240_0_7, color.R_255_255_255_0]}
                style={xMergeStyles([
                  style.badgeView,
                  { width: getPixel(badgeViewWidth) },
                ])}
              >
                {isNewEnergy && (
                  <Image
                    src={`${ImageUrl.DIMG04_PATH}1tg5f12000d4t4wzw8FFD.png`}
                    mode="aspectFit"
                    style={style.lessImg}
                  />
                )}
                {isRecommLabel && (
                  <VehRecomLabel recommendLabels={recommendLabels} />
                )}
              </LinearGradient>
            )}
            {isBrowseProduct && !fromVendorList && (
              <LinearGradient
                start={{ x: 0.0, y: 0.5 }}
                end={{ x: 1, y: 0.5 }}
                locations={[0, 1]}
                colors={[`rgba(255,255,255,0)`, color.browsingLinear]}
                style={style.browsingView}
              >
                <BbkText style={style.browsingText}>{Texts.browseNow2}</BbkText>
              </LinearGradient>
            )}
          </BbkTouchable>
          {isSecretBox && (
            <Image
              src={`${ImageUrl.componentImagePath}SecretBox/secretBoxWrapper.png`}
              mode="aspectFill"
              style={style.secretBoxImg}
            />
          )}
          {vehicleIndex === 0 && unUrlProduct && progressIsFinish && (
            <UrlFilterNoMatch />
          )}
          <VehicleContainer
            debounce={true}
            onPress={handleVehiclePress}
            disabled={isDisable}
            testID={`${UITestId.car_testid_page_list_vehicle_item}_${vehicleName}`}
          >
            {isRecommend && (
              <RecommendVehicleTip
                recommendType={recommendType}
                recommendInfo={recommendInfo}
              />
            )}
            <View
              style={xMergeStyles([
                isSecretBox && style.wrap_SecretBox,
                isCardUserBrowsed && style.userBrowsed,
              ])}
            >
              <View style={xMergeStyles([style.wrap_B, style.pl14])}>
                <View
                  style={xMergeStyles([
                    style.flexRow,
                    !isRecommLabel && style.vehicleImgWrapPaddingBottom_B,
                    style.pb14,
                    showLikeLabel && isHotLabel && style.vehicleHotLikeWrap,
                    showLikeLabel && !isHotLabel && style.vehicleLikeWrap,
                    isSecretBox && { paddingTop: 0, paddingBottom: 0 },
                    isShowBadgeWrap && style.negaMt12,
                  ])}
                >
                  <BbkTouchable
                    debounce={true}
                    onPress={handleVehicleImage}
                    disabled={isDisable}
                    style={xMergeStyles([
                      style.vehicleImageNew_A,
                      isSoldOut && style.soldOutImg,
                      style.absLeft,
                    ])}
                  >
                    {isSecretBox ? (
                      <ImageBackground
                        source={{
                          uri: getProcImageUrl(
                            imgUrl,
                            ProcImageParamsType.listSecretBoxImage,
                          ),
                        }}
                        style={style.secretBoxImageWrap}
                        imageStyle={style.secretBoxImage}
                        onError={imageSecretLoadError}
                      />
                    ) : (
                      <BbkCarImage
                        testID={UITestId.car_testid_comp_vehicle_car_image}
                        source={{
                          uri: getProcImageUrl(
                            imgUrl,
                            ProcImageParamsType.isdList,
                          ),
                        }}
                        mediaTypes={isEasyLife2024 ? null : mediaTypes}
                        resizeMode="cover"
                        isNewEnergy={isNewEnergy}
                        style={xMergeStyles([
                          style.vehicleImageNew_A,
                          isSoldOut && style.soldOutImg,
                          style.absLeft,
                        ])}
                        energyStyle={
                          !(isBrowseProduct && !fromVendorList) && style.mt4
                        }
                        isUserBrowsed={isUserBrowsedFlag}
                        onError={imageLoadError}
                        checkWidth={3}
                        checkHeight={2}
                        onCheckFaild={onCheckFaild}
                      />
                    )}
                  </BbkTouchable>
                  <BbkTouchable
                    debounce={true}
                    onPress={handleVehicleInfo}
                    disabled={isDisable}
                    testID={`${UITestId.car_testid_page_list_vehicle_item_info}_${vehicleName}`}
                    style={xMergeStyles([
                      style.vehicleDesc_B, // 有展示第二行标签，则标签有上边距，使其对齐车图。非列表页标签简化的无忧租2024都有上边距
                      secondVehicleDesc?.length > 0 && style.vehicleDescTop,
                      !isRecommLabel && style.vehicleDescJustifyCenter_B,
                      isSecretBox && style.secretBoxDesc,
                      style.newVehicleDescWrap,
                    ])}
                  >
                    <BbkVehicleName
                      name={vehicleName}
                      groupName={groupName}
                      isSimilar={isSimilar}
                      licenseTag={licenseLabel}
                      licenseType={licenseType}
                      licenseSize={PlateBgSize.small}
                      licenseTagStyle={VehicleStyle.licenseTagStyle}
                      type={
                        Utils.isCtripIsd() ? 'none' : VehicleNameType.Default
                      }
                      style={VehicleStyle.vehicleHeaderWrap}
                      titleTextStyle={nameTitleTextStyle}
                      isSoldOut={isSoldOut}
                      showPlateTag={showVehicleNamePlateTag}
                    />

                    <BbkCarVehicleDesc
                      items={isSecretBox ? secretBoxGroupLabel : firstLabels}
                      iconColor={
                        isSoldOut ? color.darkGrayBorder : descTextColor
                      }
                      textColor={
                        isSoldOut ? color.darkGrayBorder : descTextColor
                      }
                      horizontal
                      isInner={true}
                      showSplit={true}
                      labelStyle={
                        isEasyLife2024
                          ? VehicleStyle.easyLifeFirstDesc
                          : VehicleStyle.mt7
                      }
                      splitStyle={VehicleStyle.splitStyle_C}
                      hasTransformStyle2={true}
                    />

                    {secondVehicleDesc.length > 0 && (
                      <BbkCarVehicleDesc
                        items={secondVehicleDesc}
                        iconColor={
                          isSoldOut ? color.darkGrayBorder : descTextColor
                        }
                        textColor={
                          isSoldOut ? color.darkGrayBorder : descTextColor
                        }
                        horizontal
                        isInner={true}
                        showSplit={true}
                        labelStyle={
                          isEasyLife2024
                            ? VehicleStyle.easyLifeSecondDesc
                            : VehicleStyle.secondDesc
                        }
                        splitStyle={VehicleStyle.splitStyle_C}
                        hasTransformStyle2={true}
                      />
                    )}
                    {!!vehicleTags?.length && isEasyLife2024 && (
                      <VehicleTags tags={vehicleTags} isSoldOut={isSoldOut} />
                    )}
                  </BbkTouchable>
                </View>
                {showLikeLabel && (
                  <Image
                    src={`${ImageUrl.BBK_IMAGE_PATH}guesslike.png`}
                    mode="aspectFill"
                    className={c2xVehicleStyle.guesslikeImage}
                  />
                )}
                {isHotLabel && (
                  <Image
                    src={`${ImageUrl.BBK_IMAGE_PATH}hot.png`}
                    mode="aspectFill"
                    className={c2xVehicleStyle.hotImage}
                    style={showLikeLabel && VehicleStyle.hotLikeSplit}
                  />
                )}
              </View>
              {renderPriceEnter()}
            </View>

            {!!isUrlTop &&
              (isEasyLife2024 ? (
                <View className={c2xVehicleStyle.easyLife2024TopEnter}>
                  <EasyLife2024PriceEnter
                    data={data}
                    isSoldOut={isSoldOut}
                    onPressLeft={totalPricePress}
                    onPressRight={onPressEasyLife2024Enter}
                  />
                </View>
              ) : (
                <DetailVendorNew
                  key={`${vehicleCode}_${vehicleIndex}_${-1}`}
                  index={0}
                  isUrlTop={isUrlTop}
                  {...topVendor}
                  isHideCommentLabel
                  section={section}
                  vehicleItemData={vehicleItemData}
                  vehicleName={vehicleName}
                  isHotLabel={isHotLabel}
                  isSoldOut={isSoldOut}
                  totalPricePress={totalPricePress}
                  onPressQuestion={totalPricePress}
                  isShowBtnIcon={true}
                  onPressVendor={handleVehiclePress}
                  onPressBooking={handleVehiclePress}
                  onPressReview={handleVehiclePress}
                  vendorContainerStyle={VehicleStyle.vendorContainerStyleNew}
                  vendorContentStyle={VehicleStyle.vendorContentStyleNew}
                  wrapStyle={
                    isUserBrowsedFlag
                      ? VehicleStyle.userBrowsingVendorWrapStyleNew
                      : VehicleStyle.vendorWrapStyleNew
                  }
                  showRightIcon={false}
                  licenseTag={licenseLabel}
                  licenseType={licenseType}
                  licenseSize={PlateBgSize.small}
                  isSelfService={isSelfService}
                  showTopBorder={true}
                  optimizationStrengthenVendorNameStyle={
                    VehicleStyle.topVendorNameStyle
                  }
                  vendorNameMaxByte={10}
                  isHasBookBtn={false}
                />
              ))}
          </VehicleContainer>
        </View>
      </XViewExposure>
    );
  },
  CarABTesting.isListInPage() ? isEqualVehicleForListPage : isEqualVehicle,
);

export const VehicleFooter = memo(
  withTheme(
    ({
      moreNumber,
      setShowMoreArr,
      // showMoreArr,
      vehicleName,
      vehicleIndex,
      theme: themeProps,
    }) => {
      const theme: any = themeProps || {};
      const moreTextStyle = [
        style.moreText,
        {
          color: theme.blueBase || color.blueBase,
        },
      ];

      /* eslint-disable */
      const showMoreHandler = useCallback(() => {
        setShowMoreArr(vehicleIndex);
        CarLog.LogCode({
          name: '点击_列表页_查看更多',

          vehicleIndex,
          vehicleName,
        });
      }, [setShowMoreArr, vehicleIndex, vehicleName]);

      return selector(
        moreNumber,
        <BbkTouchable
          debounce
          onPress={showMoreHandler}
          style={xMergeStyles([
            style.showMoreWrap,
            style.vehicleMarginBottom,
            {
              backgroundColor: theme.backgroundColor || color.white,
              borderTopColor: theme.grayBorder || color.grayBorder,
            },
          ])}
        >
          <BbkCarRightIcon
            text={Texts.listShowMore(moreNumber)}
            style={style.more}
            textStyle={moreTextStyle}
            iconContent={htmlDecode(icon.circleArrowDown)}
            iconStyle={xMergeStyles([moreTextStyle, style.moreIcon])}
          />
        </BbkTouchable>,
        <View style={style.vehicleMarginBottom} />,
      );
    },
  ),
);
