import {
  get as lodashGet,
  omit as lodashOmit,
  debounce as lodashDebounce,
} from 'lodash-es';
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import Device from '@c2x/apis/Device';
import Util from '@c2x/apis/Util';
import React, { useCallback, memo } from 'react';
import {
  xMergeStyles,
  xClassNames as classNames,
  XView as View,
  XViewExposure,
  xGetNetworkType,
} from '@ctrip/xtaro';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import BbkLabel from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import {
  border,
  color,
  icon,
  font,
  layout,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BBkThemingProvider, {
  withTheme,
} from '@ctrip/rn_com_car/dist/src/Theming';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import UIToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import c2xStyles from './vendorC2xStyles.module.scss';
import DiscountLabels from '../../../ComponentBusiness/VendorListEnter/src/DiscountLabels';
import VendorTag from '../../../ComponentBusiness/VendorTag';
import BbkPriceDesc from '../../../ComponentBusiness/CarPriceDescribe';
import { BbkVendorDesc } from '../../../ComponentBusiness/VendorLogo';
import { EasyLifeLabelList } from '../../../ComponentBusiness/Easylife';
import CarRentalCenterDesc from '../../../ComponentBusiness/CarRentalCenterDesc';
import OriginalOrderLabel from './OriginalOrderLabel';
import { Enums } from '../../../ComponentBusiness/Common';
import { VehicleListStyle as style } from '../Styles';
import {
  CarLog,
  Utils,
  AppContext,
  User,
  CarServerABTesting,
  GetABCache,
} from '../../../Util/Index';
import Channel from '../../../Util/Channel';
import {
  Platform,
  EventName,
  LogKey,
  ImageUrl,
  UITestID,
} from '../../../Constants/Index';
import VerdorHeader from './VendorHeader';
import {
  setListStatusData,
  getListStatusData,
  listStatusKeyList,
  getServerRequestId,
} from '../../../Global/Cache/ListReqAndResData';
import Texts from '../Texts';
import {
  getTenancyDays,
  getVendorStore,
  getLogDataFromState,
  getSelectedFilterString,
} from '../../../State/List/Mappers';
import { RecommendTip } from './RecommendVehicleTip';
import { NoResultTip } from './NoResultTip';
import { RecommendType } from '../../../Constants/ListEnum';
import { getIsVehicle2 } from '../../../Global/Cache/ListResSelectors';
import {
  isValidRentalDate,
  ValidRentalDateLogType,
} from '../../../State/LocationAndDate/Util';
import { saveOsdUserBrowsed } from '../Method';

const { getPixel, autoProtocol, isAndroid, isIos } = BbkUtils;
const getNetworkType = async () => {
  return new Promise(resolve => {
    xGetNetworkType({
      success: res => {
        resolve(res?.networkType);
      },
    });
  });
};

// bbk 未发布，属性 testID未添加
const BbkTouchableExt: any = BbkTouchable;
const styles = StyleSheet.create({
  gsPriceWrap: {
    paddingTop: getPixel(isAndroid ? 4 : 2),
  },
  diffFreeIWrap: {
    marginRight: getPixel(44),
  },
  soldOutWrap: {
    paddingRight: getPixel(40),
    marginTop: getPixel(12),
    paddingBottom: getPixel(10),
  },
  gs2PickDropWrap: {
    marginTop: getPixel(1),
  },
  gs2DropWrap: {
    marginTop: getPixel(-6),
  },
  carRentalCenterWrap: {
    marginBottom: getPixel(12),
  },
});

const getProductPageParam = (
  { vehicleHeader, vehicleDesc }: any = {},
  vendorHeaderProps,
  vendor,
  isPreDispatchAtList?: boolean,
) => {
  const realityImageUrl = lodashGet(vehicleHeader, 'realityImageUrl');
  if (!realityImageUrl) {
    return null;
  }

  return {
    vehicleHeader: {
      ...lodashOmit(vehicleHeader, 'vehicleName'),
      name: vehicleHeader.vehicleName,
    },
    vehicleDesc: {
      ...lodashOmit(vehicleDesc, 'vehicleLabelsGroupName'),
      vehicleLabelsHorizontal: [
        ...vehicleDesc.vehicleLabelsGroupName,
        ...vehicleDesc.vehicleLabelsHorizontal,
      ],
    },
    vendorHeaderProps,
    vendor,
    realityImageUrl: BbkUtils.autoProtocol(realityImageUrl),
    isPreDispatchAtList,
  };
};

export interface IListForwardNeedInfoType {
  vendor: any;
  locationAndDate: any;
  expandLocationAndDate: any;
  age: string | number;
  vehicleIndex: number;
  reference: any;
  vendorIndex: number;
  isHotLabel: boolean;
  rentCenter?: any;
  storeList?: any;
  isNoMatchRecommond: boolean;
  hotType?: number;
  setVendorListModalData?: (args: any) => void;
  setTimeOutPopData?: (args: any) => void;
  section?: any;
  vendorHeaderProps?: any;
  selectedFilters?: any;
  pHub?: number;
  rHub?: number;
  isDifferentLocation?: boolean;
  recommendType?: string;
  pickupDateTime?: string;
  dropOffDateTime?: string;
  pickupLocationCode?: string;
  dropOffLocationCode?: string;
  population?: number;
  recommendPickReturnInfo?: any;
  setDateInfo?: (data) => void;
  setLocationInfo?: (data) => void;
  pTime?: Date;
  rTime?: Date;
  queryProduct?: (data: any) => void;
  osdUserBrowsed?: object;
  setOsdUserBrowsed?: (data) => void;
}

// 驾照要求检测
const checkDriverLicense = ({
  vendorInfo,
  licenseInfo,
  setDriverLicensePopData,
}) => {
  /**
   * 判断是否支持中国驾照提示
   * 取车弹层提示条件：当前门店不支持时就提示用户，判断条件 vendorInfo.isPStoreSupportCdl===false
   * 还车弹层提示条件：所有还车门店都不支持中国驾照时，则认为当前国家不支持中国驾照，判断条件 returnSupportCDLType=4
   */
  const {
    returnSupportCDLType,
    pickupSupportCDLType,
    returnCountryName,
    pickupCountryName,
  } = licenseInfo || {};
  const { isPStoreSupportCdl, reference } = vendorInfo || {};
  if (isPStoreSupportCdl === true && returnSupportCDLType !== 4) {
    return false;
  }
  // 判断是否已经显示过弹层vehicleCode
  const { bizVendorCode, vehicleCode } = reference;
  const key = `${bizVendorCode}_${vehicleCode}`;

  let drivelicencePopList = getListStatusData(
    listStatusKeyList.drivelicencePopList,
  );
  if (lodashGet(drivelicencePopList, key)) return false;

  let msg = '';
  let ltext = '再看看';

  // todo 文案改成服务端配置
  if (!isPStoreSupportCdl) {
    // 取车门店不支持中国驾照
    if (pickupSupportCDLType === 4) {
      msg = `根据${pickupCountryName}当地政策规定，中国大陆驾照目前并不在${pickupCountryName}支持的驾照列表中。`;
    } else {
      ltext = '更换车行';
      msg =
        '该车行门店不支持中国大陆驾照租车。我们在该城市还有其他的供应商支持中国大陆驾照+翻译件租车，是否前往查看？';
    }
  } else if (returnSupportCDLType === 4) {
    // 还车门店不支持中国驾照
    msg = `根据${returnCountryName}当地政策规定，中国大陆驾照目前并不在${
      returnCountryName
    }支持的驾照列表中。请确认您已了解异地还车至${
      returnCountryName
    }可能产生的风险（如罚款等）。`;
  }

  if (!drivelicencePopList) {
    drivelicencePopList = {};
  }
  drivelicencePopList[key] = true;
  setListStatusData(listStatusKeyList.drivelicencePopList, drivelicencePopList);

  setDriverLicensePopData({
    visible: true,
    content: msg,
    leftText: ltext,
  });

  return true;
};

const getTraceInfo = (section, reference, vendor) => {
  const { vehicleIndex, vehicleHeader } = section;
  const { vehicleCode, vendorCode, pStoreCode, rStoreCode } = reference;
  const { vehicleName, groupId, groupName } = vehicleHeader;
  const { vendorName } = vendor;
  return {
    vehicleIndex,
    vehicleCode,
    vehicleName,
    vendorCode,
    vendorName,
    storeCode: pStoreCode,
    rStoreCode,
    groupId,
    groupName,
  };
};

const gotoNewDetail = (
  reference,
  section,
  vendorHeaderProps,
  vendor,
  queryProduct?,
) => {
  if (queryProduct) {
    queryProduct({
      reference,
      reset: true,
    });
  }
  AppContext.PageInstance.push(Channel.getPageId().Product.EN, {
    reference,
    pageParam: getProductPageParam(
      section,
      vendorHeaderProps,
      vendor,
      !!queryProduct,
    ),
    pageVehicleHeaderParam: section?.vehicleHeader,
    pageVendorParam: vendor,
    traceInfo: getTraceInfo(section, reference, vendor),
  });
};

const logForward = info => {
  const {
    vehicleIndex,
    reference = {},
    vendorIndex,
    section,
    vendor = {},
    vendorHeaderProps = {},
    selectedFilters,
    isCreditRent,
    traceInfo,
  } = info;
  const { vehicleCode, vehicleHeader = {}, vehicleDesc = {} } = section;
  const { vehicleName, groupName, groupId } = vehicleHeader;
  const { fuel, driveType } = vehicleDesc;
  const {
    vendorCode,
    bizVendorCode,
    pStoreCode,
    rStoreCode,
    pRc,
    rRc,
    pickWayInfo,
    returnWayInfo,
    priceBatchNo,
    klbVersion,
  } = reference;
  const {
    vendorName,
    distance = '',
    rDistance = '',
    pStoreRouteDesc = '',
    rStoreRouteDesc = '',
    priceInfo,
    sortNum,
  } = vendor;
  const { isEasyLife } = vendorHeaderProps;
  const logData = getLogDataFromState();
  CarLog.LogCode({
    name: '点击_列表页_供应商报价',

    batchNo: section?.batchNo,
    ...traceInfo,
    data: {
      ...logData,
      vehicleIndex, // 车型位置
      vendorIndex, // 供应商位置
      bizVendorCode,
      pStoreCode,
      rStoreCode,
      vehicleCode, // 车型id
      vehicleName,
      groupId, // 车型组id
      groupName,
      vendorCode, // 供应商id
      vendorName,
      isEasyLife,
      selectedFilters: getSelectedFilterString(selectedFilters), // 筛选项
      isCreditRent,
      ispickupSource: Utils.getLogStrValue(pRc),
      isdropoffSource: Utils.getLogStrValue(rRc),
      pickWayInfo,
      returnWayInfo,
      priceBatchNo,
      klbVersion,
      fuelType: fuel,
      isFourDrive: driveType,
      isRefactor: getIsVehicle2() ? '1' : '',
      currentRequestId: getServerRequestId(),
      minCurrentDailyPrice: section?.lowestPrice,
      currentDailyPrice: priceInfo?.currentDailyPrice,
      sortNum,
    },
    info: {
      pStoreCode,
      rStoreCode,
      pDistance: distance.toString(),
      rDistance: rDistance.toString(),
      pickWayInfo: pStoreRouteDesc,
      returnWayInfo: rStoreRouteDesc,
      brandLabelCode: vendor?.vendorTag?.code,
      brandLabelName: vendor?.vendorTag?.title,
      skuId: vendor?.reference?.skuId ? `${vendor?.reference?.skuId}` : '',
    },
  });
};

const mapCtripIsdParam = ({
  vendor,
  locationAndDate,
  reference,
  rentCenter,
  storeList,
  isNoMatchRecommond,
  section,
}) => {
  const { pickUp, dropOff } = locationAndDate.rentalLocation;
  const store = getVendorStore(storeList, reference);
  const allTags = lodashGet(vendor, 'allTags') || [];
  const ylabel = allTags.filter(v => v.type === 3); // 营销标签
  const { vehicleDesc = {}, vehicleHeader = {} } = section || {};
  const { imgUrl = '' } = vehicleDesc;
  const { vehicleName = '' } = vehicleHeader;
  const param = {
    pcname: pickUp.cname,
    rcname: dropOff.cname,
    pcid: pickUp.cid,
    rcid: dropOff.cid,
    ptime: dayjs(
      locationAndDate.rentalDate.pickUp.dateTime,
      'YYYYMMDDHHmmss',
    ).format('YYYY/MM/DD HH:mm:ss'),
    rtime: dayjs(
      locationAndDate.rentalDate.dropOff.dateTime,
      'YYYYMMDDHHmmss',
    ).format('YYYY/MM/DD HH:mm:ss'),
    shareptime: dayjs(
      locationAndDate.rentalDate.pickUp.dateTime,
      'YYYYMMDDHHmmss',
    ).format('MM月DD日'),
    sharertime: dayjs(
      locationAndDate.rentalDate.dropOff.dateTime,
      'YYYYMMDDHHmmss',
    ).format('MM月DD日'),
    poiinfo: {
      addr: pickUp.area.name,
      lat: pickUp.area.lat,
      lng: pickUp.area.lng,
      desc: '',
      type: pickUp.area.type,
    },
    rpoiinfo: {
      addr: dropOff.area.name,
      lat: dropOff.area.lat,
      lng: dropOff.area.lng,
      desc: '',
      type: dropOff.area.type,
    },
    pid: vendor.ctripVehicleCode,
    hottype: reference.hotType,
    hotdesc: null,
    comPriceCode: reference.comPriceCode,
    priceVersion: reference.priceVersion,
    // 租车卡废弃
    isRentCard: 0,
    sfname: vendor.isSelect ? '携程优选' : '',
    sfot: vendor.isSelect ? 1 : 0,
    ctripvendortype: vendor.isSelect ? 3 : 0,
    // 无忧租
    isgranted: 0,
    grantedcode: '',
    pricetype: vendor.priceInfo.priceType,
    vpid: reference.vehicleCode,
    psid: reference.pStoreCode,
    rsid: reference.rStoreCode,
    vendorid: reference.vendorCode,
    ratecode: '',
    ratecid: '',
    issend: '0',
    csname: vendor.vendorName,
    mergeInfo: [],
    atype: reference.aType,
    freedeposit: vendor.freeDeposit,
    totalPrice: vendor.priceInfo.currentTotalPrice,
    dprice:
      vendor.priceInfo.currentOriginalDailyPrice ||
      vendor.priceInfo.currentDailyPrice,
    cprice: vendor.priceInfo.currentDailyPrice,
    clientId: lodashGet(Device, 'deviceInfo.clientID') || '',
    pisSend: lodashGet(store, 'psend'),
    risSend: lodashGet(store, 'rsend'),
    rentcenterid: rentCenter && rentCenter.id,
    ftype: reference.fType,
    isAlipay: reference.alipay,
    cinfo: {},
    // 送车上门的圈号
    pickUpLevel: !!store && !!store.pickUpLevel ? store.pickUpLevel : 0,
    // 上门取车的圈号
    pickOffLevel: !!store && !!store.pickOffLevel ? store.pickOffLevel : 0,
    // 送车上门的类型
    sendTypeForPickUpCar:
      !!store && !!store.sendTypeForPickUpCar ? store.sendTypeForPickUpCar : 0,
    // 上门取车的类型
    sendTypeForPickOffCar:
      !!store && !!store.sendTypeForPickOffCar
        ? store.sendTypeForPickOffCar
        : 0,
    isRecommend: isNoMatchRecommond ? 1 : 0,
    ylabel,
    imgUrl,
    vehicleName,
  };

  return param;
};

const networkType = ['none', 'unknown'];

export const forwardNextPage = async (info: IListForwardNeedInfoType) => {
  const {
    vendor,
    locationAndDate,
    expandLocationAndDate,
    vehicleIndex,
    reference,
    rentCenter,
    isHotLabel,
    storeList,
    isNoMatchRecommond,
    setTimeOutPopData,
    section,
    vendorHeaderProps,
    pHub,
    rHub,
    recommendType,
    recommendPickReturnInfo,
    setDateInfo,
    setLocationInfo,
    pTime,
    rTime,
    osdUserBrowsed,
    setOsdUserBrowsed = Utils.noop,
    // queryProduct,
  } = info;

  const areaChange = (name, locationCode, lat, lng, cid) => {
    const rentalLocation = locationAndDate?.rentalLocation;
    setLocationInfo({
      notDisPatchPreFetch: true,
      isShowDropOff: false,
      pickUp: {
        ...rentalLocation.pickUp,
        area: {
          id: locationCode,
          lat,
          lng,
          name,
        },
        cid,
      },
    });
  };

  if (Utils.isCtripOsd()) {
    const curNetwork = (await getNetworkType()) as string;
    CarLog.LogTrace({
      key: LogKey.c_car_trace_network_data,
      info: {
        networkType: curNetwork,
      },
    });
    if (networkType.includes(curNetwork)) {
      UIToast.show(Texts.networkErrTip);
      return;
    }
  }

  const {
    pickUpAvailableTime,
    returnAvailableTime,
    availableLocation,
    latitude,
    longitude,
    cid,
    availableLocationCode,
  } = recommendPickReturnInfo || {};
  const ptime = pickUpAvailableTime || pTime;
  // 时间校验
  const timeIsOut = !isValidRentalDate(
    ptime,
    rTime,
    true,
    false,
    ValidRentalDateLogType.ListVendorClick,
  );
  if (timeIsOut && setTimeOutPopData) {
    setTimeOutPopData({
      visible: true,
      title: Texts.timeOutPopTitle,
      content: Texts.timeOutPopContent,
      rightText: Texts.timeOutPopConfirmText,
    });
    return;
  }

  logForward(info);
  const newReference = { ...reference };
  newReference.priceType = vendor.priceInfo.priceType;
  newReference.priceTip = vendor?.priceTip;
  newReference.pHub = pHub;
  newReference.rHub = rHub;
  if (Utils.isCtripIsd()) {
    // 国内设置填写页参数，对应老版国内参数
    newReference.isdParam = mapCtripIsdParam({
      vendor,
      locationAndDate,
      reference,
      rentCenter,
      storeList,
      isNoMatchRecommond,
      section,
    });
  }

  if (AppContext.ABTesting.newDetail) {
    if (recommendType) {
      const newRentalDate = {
        pickup: pickUpAvailableTime,
        dropoff: returnAvailableTime,
        notDisPatchPreFetch: true,
      };
      switch (recommendType) {
        case RecommendType.PDTime:
          setDateInfo(newRentalDate);
          break;
        case RecommendType.DiffLocation:
        case RecommendType.DefaultArea:
          areaChange(
            availableLocation,
            availableLocationCode,
            latitude,
            longitude,
            cid,
          );
          break;
        default:
          break;
      }
    }
    gotoNewDetail(newReference, section, vendorHeaderProps, vendor);
    // 跳转产品详情页后，设置浏览记录
    if (GetABCache.isAiSort2()) {
      const newBrowsed = { ...osdUserBrowsed };
      newBrowsed[reference?.vehicleKey] = {
        timeSpan: +new Date(),
      };
      // 缓存浏览过的车型
      setOsdUserBrowsed(newBrowsed);
      saveOsdUserBrowsed(newBrowsed);
    }
    return;
  }

  const data: any = {
    ...locationAndDate,
    book: {
      sortNum: vehicleIndex,
      ...reference,
    },
  };

  if (isHotLabel) {
    data.book.sortNum = 1;
  }

  // 跳转Trip详情页地址
  /* eslint-disable max-len */
  const referenceStr = Utils.toParams(reference, true);
  const locationAndDateStr = Utils.toParams(expandLocationAndDate, true);

  let url = '';
  if (Utils.isCtripOsd()) {
    const productKey = Utils.getProductKey(reference);
    url = Platform.CAR_CROSS_URL.DETAIL.OSD;
    url += `&${locationAndDateStr}&${referenceStr}&setSaleOutEventName=${EventName.changeSaleOutList}&productKey=${productKey}`;
  } else if (Utils.isCtripIsd()) {
    // 校验是否登录
    const isLogin = await User.isLogin();
    if (!isLogin) {
      const res = await User.toLogin();
      if (!res) {
        return;
      }
    }
    const productKey = Utils.getProductKey(reference);
    const param = {
      setSaleOutEventName: EventName.changeISDSaleOutList,
      productKey,
      ...mapCtripIsdParam({
        vendor,
        locationAndDate,
        reference,
        rentCenter,
        storeList,
        isNoMatchRecommond,
        section,
      }),
    };

    url = Platform.CAR_CROSS_URL.DETAIL.ISD;
    url += `&channelid=${AppContext.MarketInfo.channelId}&aid=${
      AppContext.MarketInfo.aId
    }&sid=${AppContext.MarketInfo.sId}&sparam=${encodeURIComponent(
      Util.base64Encode(JSON.stringify(param)),
    )}`;
  }

  // console.log('跳转地址++url', url);
  Utils.openUrlWithTicket(url);
};

const DiffFreeLabel = ({ diffFreeTag, isSoldOut }) => {
  if (!diffFreeTag?.title) {
    return null;
  }
  return (
    <View style={xMergeStyles([layout.flexRow, styles.diffFreeIWrap])}>
      <BbkText
        type="icon"
        className={classNames(
          c2xStyles.diffFreeIcon,
          isSoldOut && c2xStyles.diffFreeSoldOut,
        )}
      >
        {icon.diffFree}
      </BbkText>
      <BbkText
        className={classNames(
          c2xStyles.diffFreeText,
          isSoldOut && c2xStyles.diffFreeSoldOut,
        )}
      >
        {diffFreeTag?.title}
      </BbkText>
    </View>
  );
};

const Vendor = memo(
  withTheme(
    ({
      vendor,
      priceDescProps,
      vendorHeaderProps,
      theme,
      soldOutLabel,
      locationAndDate,
      expandLocationAndDate,
      selectedFilters,
      reference,
      section,
      vehicleItemData,
      vehicleIndex,
      vehicleCode,
      vendorIndex,
      age,
      isHotLabel,
      licenseInfo,
      setDriverLicensePopData,
      isSoldOut,
      rentCenter,
      storeList,
      hotType,
      isNoMatchRecommond,
      easyLifeTagList,
      setTimeOutPopData,
      setVendorListModalData,
      isCreditRent,
      setTotalPriceModalData,
      isHideCommentLabel,
      onTotalPriceLayOut,
      setNeedSubmitSecondLog,
      setEasyLifePopVisible,
      hint,
      isShowBottomBorder,
      isShowTopBorder = true,
      setPriceSummaryModal,
      diffFreeTag,
      pHub,
      rHub,
      marketLabels,
      tagsWithoutMarket,
      priceSoldOutTextMap,
      isDifferentLocation,
      recommendType,
      pickupDateTime,
      dropOffDateTime,
      pickupLocationCode,
      dropOffLocationCode,
      population,
      recommendPickReturnInfo,
      setDateInfo,
      setLocationInfo,
      pTime,
      rTime,
      queryProduct,
      traceInfo,
      osdUserBrowsed,
      setOsdUserBrowsed,
    }) => {
      const { fees, allTags } = vendor;
      const disabled = !!isSoldOut;
      const showPriceConsistent = !!(fees && fees.length);
      const isRecommend = CarServerABTesting.isRecommend();
      const isNoResultNew = CarServerABTesting.isNoResult();
      const onVerdorHeaderPress = useCallback(
        lodashDebounce(
          async () => {
            // 防止境外主线程繁忙，超出300ms的情况
            // 20210122 AppContext未及时更新的场景下，导致点击报价无反应
            // if (AppContext.PageInstance.getPageId() !== Channel.getPageId().List.ID) {
            //   return;
            // }
            const forwardNeedInfo = {
              vendor,
              locationAndDate,
              expandLocationAndDate,
              age,
              vehicleIndex,
              reference,
              vendorIndex,
              isHotLabel,
              rentCenter,
              storeList,
              hotType,
              isNoMatchRecommond,
              setVendorListModalData,
              section: { ...section, ...vehicleItemData },
              vendorHeaderProps,
              selectedFilters,
              setTimeOutPopData,
              isCreditRent,
              pHub,
              rHub,
              recommendType,
              recommendPickReturnInfo,
              setDateInfo,
              setLocationInfo,
              pTime,
              rTime,
              queryProduct,
              traceInfo,
              osdUserBrowsed,
              setOsdUserBrowsed,
            };

            if (isRecommend) {
              CarLog.LogCode({
                name: '点击_列表页_补偿车型报价',

                isDifferentLocation,
                recommendType,
                priceIndex: vendorIndex,
                info: {
                  storeId: reference?.pStoreCode,
                  pickupDateTime,
                  dropOffDateTime,
                  pickupLocationCode,
                  dropOffLocationCode,
                  population,
                  age,
                },
              });
            }
            // 驾照要求检测
            if (
              Utils.isCtripOsd() &&
              checkDriverLicense({
                vendorInfo: vendor,
                licenseInfo,
                setDriverLicensePopData,
              })
            ) {
              setListStatusData(
                listStatusKeyList.listForwardNeedInfo,
                forwardNeedInfo,
              );
              return;
            }
            forwardNextPage(forwardNeedInfo);
            if (typeof setNeedSubmitSecondLog === 'function') {
              setNeedSubmitSecondLog(false);
            }
          },
          300,
          {
            trailing: false,
            leading: true,
          },
        ),
        [
          age,
          expandLocationAndDate,
          licenseInfo,
          locationAndDate,
          reference,
          setDriverLicensePopData,
          vehicleCode,
          vehicleIndex,
          vendor,
          vendorIndex,
          rentCenter,
          storeList,
          hotType,
          isNoMatchRecommond,
          selectedFilters,
        ],
      );

      const totalPricePress = useCallback(() => {
        if (disabled) return;
        const { totalPriceVendorNote } = Texts;
        const payload = {
          visible: true,
          data: {},
        } as any;
        if (showPriceConsistent) {
          const store = getVendorStore(storeList, reference);
          // 送车上门的圈号
          const pickUpLevel = lodashGet(store, 'pickUpLevel', 0);
          // 上门取车的圈号
          const pickOffLevel = lodashGet(store, 'pickOffLevel', 0);
          payload.data = {
            reference,
            fees,
            type: Enums.TotalPriceModalType.Vendor,
            content: totalPriceVendorNote,
            title: Texts.totalPriceNoteTitle,
            footer: Texts.totalPriceVendorNoteFooter,
            dailyPriceUnit: Texts.dailyPriceUnit(0),
            dailyPriceName: Texts.dailyPriceName(0),
            totalPriceName: Texts.totalPriceName,
            totalPriceContain: Texts.totalPriceContain,
            onVerdorHeaderPress,
            dayPrice:
              (priceDescProps &&
                priceDescProps.dayPrice &&
                priceDescProps.dayPrice.price) ||
              0,
            totalPrice:
              (priceDescProps &&
                priceDescProps.totalPrice &&
                priceDescProps.totalPrice.price) ||
              0,
            originPrice:
              (priceDescProps &&
                priceDescProps.originPrice &&
                priceDescProps.originPrice.price) ||
              0,
            pickUpLevel,
            pickOffLevel,
          };
          setPriceSummaryModal(payload);
        } else {
          const days = getTenancyDays();
          payload.data = {
            type: Enums.TotalPriceModalType.Vendor,
            content: totalPriceVendorNote,
            title: Texts.totalPriceNoteTitle,
            footer: Texts.totalPriceVendorNoteFooter,
            dailyPriceUnit: Texts.dailyPriceUnit(days),
            dailyPriceName: Texts.dailyPriceName(days),
            totalPriceName: Texts.totalPriceName,
            totalPriceContain: Texts.totalPriceContain,
            onVerdorHeaderPress,
            dayPrice:
              (priceDescProps &&
                priceDescProps.dayPrice &&
                priceDescProps.dayPrice.price) ||
              0,
            totalPrice:
              (priceDescProps &&
                priceDescProps.totalPrice &&
                priceDescProps.totalPrice.price) ||
              0,
          };
          setTotalPriceModalData(payload);
        }
        CarLog.LogCode({ name: '点击_列表页_供应商总价提示' });
      }, [
        onVerdorHeaderPress,
        priceDescProps,
        setTotalPriceModalData,
        setPriceSummaryModal,
        reference,
        fees,
        showPriceConsistent,
        storeList,
        disabled,
      ]);

      const soldoutLabelProps = {
        ...soldOutLabel,
        theme: {
          labelColor: theme.redBase,
        },
      };

      const {
        pStoreRouteDesc = '',
        rStoreRouteDesc = '',
        pStoreSortDesc = '',
        rStoreSortDesc = '',
        isBroker,
        priceRec,
        priceTip,
      } = vendor;

      const pHeadDesc = `${
        !Utils.isCtripIsd()
          ? `${pStoreSortDesc}${pStoreSortDesc ? '，' : ''}`
          : ''
      }`;

      const rHeadDesc = `${
        !Utils.isCtripIsd()
          ? `${rStoreSortDesc}${rStoreSortDesc ? '，' : ''}`
          : ''
      }`;

      const pickUpTitle = '取';
      const dropOffTitle = '还';
      const showDiff = !!rStoreRouteDesc; // 2021-9-17 是否展示还车方式，以服务端返回的字段为准，有值则展示，无值则不展示，不再判断是否为异地或其它条件
      const tempPickUpDesc = `${showDiff ? `${pickUpTitle}: ` : ''}${
        pStoreRouteDesc
      }`;

      const tempDropOffDesc = `${
        showDiff ? `${dropOffTitle}: ${rStoreRouteDesc}` : ''
      }`;

      const pickUpDesc = `${tempPickUpDesc ? pHeadDesc : ''}${tempPickUpDesc}`;
      const dropOffDesc = `${tempDropOffDesc ? rHeadDesc : ''}${
        tempDropOffDesc
      }`;

      const soldOutTextColor = isSoldOut && color.darkGrayBorder;
      const { isEasyLife } = vendorHeaderProps;
      const vendorEasyLifeTagList = easyLifeTagList.filter(f => f.type === 1);
      let category1Tags;
      let category2Tags;
      let category3Tags;
      let category4Tags;
      let otherTags;
      let vehicleTags = [];
      let serviceTags = [];
      let marketTags = [];
      let hasGS2Tags = true;
      if (
        Utils.isCtripIsd() &&
        vendor &&
        vendor.allTags &&
        vendor.allTags.length
      ) {
        vehicleTags = vendor.allTags.filter(
          f => f.groupId === Enums.LabelGroupType.Vehicle,
        );
        serviceTags = vendor.allTags.filter(
          f => f.groupId === Enums.LabelGroupType.Service,
        );
        marketTags = vendor.allTags.filter(
          f => f.groupId === Enums.LabelGroupType.Market,
        );
        // 无忧租集合标签
        if (serviceTags && serviceTags.length && isEasyLife) {
          const easyLifeSetLabelBase = serviceTags.filter(
            f => f.mergeId === Enums.LabelMergeType.EasyLife,
          )[0];
          if (easyLifeSetLabelBase) {
            const easyLifeSetLabel = { ...easyLifeSetLabelBase };
            let easyLifeSetLabelTitle = '';
            serviceTags.forEach(serviceTagItem => {
              if (serviceTagItem.mergeId === Enums.LabelMergeType.EasyLife) {
                if (!easyLifeSetLabelTitle) {
                  easyLifeSetLabelTitle = serviceTagItem.title;
                } else {
                  easyLifeSetLabelTitle += `·${serviceTagItem.title}`;
                }
              }
            });
            if (easyLifeSetLabelTitle) {
              easyLifeSetLabel.title = `${easyLifeSetLabelTitle}${' 等9项'}`;

              easyLifeSetLabel.colorCode = Enums.LabelColorCodeType.EasyLifeSet;
              const otherServiceTags =
                serviceTags.filter(
                  f => f.mergeId !== Enums.LabelMergeType.EasyLife,
                ) || [];
              serviceTags = [{ ...easyLifeSetLabel }, ...otherServiceTags];
            }
          }
        }
        if (marketTags.length > 1) {
          marketTags = [marketTags[0]];
        }
        if (vehicleTags.length + serviceTags.length === 0) {
          hasGS2Tags = false;
        }
      } else if (Utils.isCtripIsd()) {
        if (vendor.allTags && vendor.allTags.length > 0) {
          category1Tags = vendor.allTags.filter(f => f.category === 1);
          category2Tags = vendor.allTags.filter(f => f.category === 2);
          category3Tags = vendor.allTags.filter(f => f.category === 3);
          category4Tags = vendor.allTags.filter(f => f.category === 4);
        }
      } else if (isEasyLife && vendor.allTags && vendor.allTags.length > 0) {
        category1Tags = vendor.allTags.filter(f => f.category === 1);
        otherTags = vendor.allTags.filter(
          f => f.category === 3 || f.category === 4,
        );
        category2Tags = vendor.allTags.filter(f => f.category === 2);
      }
      const hasSoldOutLabel = soldOutLabel || isSoldOut;

      const descDecorationFn = (desc, isRc, isGS2VendorDom = true) => {
        if (isRc) {
          return `${desc}${isGS2VendorDom ? ' · ' : '，门店位于携程租车中心'}`;
        }
        return desc;
      };

      const priceSoldOutText =
        priceSoldOutTextMap?.[Utils.getProductKey(reference)] ||
        Texts.soldOutText;
      const Dom = (
        <>
          <RecommendTip recommendInfo={priceRec} isModal={true} />
          {isNoResultNew && <NoResultTip tip={priceTip} />}
          <View
            testID={UITestID.car_testid_page_list_vendor_list_modal_item_osd}
            style={xMergeStyles([
              Utils.isCtripIsd() ? style.vendor_B : style.vendor,
              isEasyLife && {
                backgroundColor: color.easylifeBg3,
              },
            ])}
          >
            <View
              style={xMergeStyles([
                style.vendorWrap,
                {
                  borderBottomColor: theme.grayBorder || color.grayBorder,
                },
              ])}
            >
              {/**
              20200429 @t_xiao 标签+价格的布局方式
              无忧租场景：
              1、价格与营销标签开始顶对齐
              2、新增一行展示供应商名称
              普通租场景：
              价格与取消确认标签开始底对齐
              备注：广告与库存激励依旧保持单独一行展示
              */}
              <BbkTouchableExt
                debounce={true}
                onPress={onVerdorHeaderPress}
                disabled={isSoldOut}
                testID="list_vehicle_price"
              >
                {!!vendor.modifySameStore && (
                  <OriginalOrderLabel
                    wrapStyle={style.vendorOriginalOrderLabel}
                    name={Texts.modifySameStore}
                    labelStyle={style.modifySameVehicle}
                  />
                )}
                <VerdorHeader
                  {...vendorHeaderProps}
                  isBroker={isBroker}
                  isEasyLife={isEasyLife}
                  logoImgStyleExtra={style.logoImgStyleExtra}
                  isHideCommentLabel={isHideCommentLabel}
                />

                {isEasyLife && (
                  <View className={c2xStyles.vendorDescWrap}>
                    <BbkVendorDesc
                      vendorName={vendorHeaderProps.vendorName}
                      isOptimize={vendorHeaderProps.isSelect}
                      isBroker={isBroker}
                      {...vendorHeaderProps}
                    />
                  </View>
                )}

                <View
                  style={xMergeStyles([style.flexRow, style.vendorPriceWrap])}
                >
                  <View style={{ flex: 1 }}>
                    <View style={xMergeStyles([style.flexRow, style.flexWrap])}>
                      <BbkText
                        style={xMergeStyles([
                          style.distanceTextNew,
                          soldOutTextColor && { color: soldOutTextColor },
                        ])}
                      >
                        {descDecorationFn(
                          pickUpDesc,
                          reference?.pRc > 0,
                          false,
                        )}
                      </BbkText>
                    </View>
                    {!!dropOffDesc && (
                      <BbkText
                        style={xMergeStyles([
                          style.distanceTextNew,
                          soldOutTextColor && { color: soldOutTextColor },
                        ])}
                      >
                        {descDecorationFn(
                          dropOffDesc,
                          reference?.rRc > 0,
                          false,
                        )}
                      </BbkText>
                    )}
                  </View>
                </View>
                {!isEasyLife && (
                  <View style={layout.flexRow}>
                    <XViewExposure
                      testID={CarLog.LogExposure({
                        name: '曝光_列表页_标签_报价弹窗',

                        info: {
                          tagInfo: Utils.getLabelTraceInfo(allTags),
                          vendorCode: reference?.bizVendorCode,
                          groupName: section?.vehicleHeader?.groupName,
                          ctripVehicleId: vehicleCode,
                          vehicleIndex,
                          vendorIndex,
                        },
                      })}
                      style={style.priceAndLabelWrap}
                    >
                      <View style={style.priceTagsWrap}>
                        <View style={style.tagWrap}>
                          <VendorTag
                            vendor={vendor}
                            tags={tagsWithoutMarket}
                            style={xMergeStyles([style.mLeft2, style.mTop8])}
                          />
                        </View>
                        <View style={style.priceWrap}>
                          {priceDescProps && (
                            <View
                              className={c2xStyles.priceEntryWrapNew}
                              style={isSoldOut && style.soldOutImg}
                            >
                              <BbkPriceDesc
                                {...priceDescProps}
                                dayTextDesc="日均"
                                showTotalPrice={priceDescProps.totalPrice}
                                originStyle={{
                                  fontSize: font.labelLStyle.fontSize,
                                  color: soldOutTextColor || color.C_888888,
                                }}
                                totalTextStyle={{
                                  ...font.labelLStyle,
                                  color:
                                    soldOutTextColor || color.fontSecondary,
                                }}
                                totalCurrencyStyle={{
                                  ...font.labelLStyle,
                                  color: soldOutTextColor || color.C_888888,
                                }}
                                totalPriceStyle={xMergeStyles([
                                  font.caption1LightStyle,
                                  { color: soldOutTextColor || color.C_888888 },
                                ])}
                                mainCurrencyStyle={{
                                  ...font.F_30_10_regular_TripNumberRegular,
                                  color: color.deepBlueBase,
                                  paddingTop: isIos ? getPixel(1) : getPixel(4),
                                }}
                                mainPriceStyle={{
                                  ...font.F_32_10_regular_TripNumberSemiBold,
                                  paddingTop: isIos ? 0 : getPixel(2),
                                  color: color.deepBlueBase,
                                }}
                                totalPricePress={totalPricePress}
                                onTotalPriceLayOut={onTotalPriceLayOut}
                              />

                              {Utils.isCtripIsd() && !isSoldOut && (
                                <Image
                                  className={c2xStyles.priceEntryWrapArrow}
                                  style={
                                    !priceDescProps.totalPrice && {
                                      marginTop: getPixel(-10),
                                    }
                                  }
                                  resizeMode="cover"
                                  source={{
                                    uri: `${ImageUrl.BBK_IMAGE_PATH}orange_arrow.png`,
                                  }}
                                />
                              )}
                            </View>
                          )}
                          <View
                            className={c2xStyles.bookBtnWrapNew}
                            style={isSoldOut && style.soldOutImg}
                          >
                            <BbkText
                              className={c2xStyles.bookBtnText}
                              type="icon"
                            >
                              {icon.signBtnArrow}
                            </BbkText>
                          </View>
                        </View>
                      </View>
                      <View style={layout.betweenHorizontal}>
                        <View>
                          {isSoldOut && !!priceSoldOutText && (
                            <BbkText className={c2xStyles.solidText}>
                              {priceSoldOutText}
                            </BbkText>
                          )}
                        </View>
                        {!!marketLabels?.length && (
                          <DiscountLabels
                            tags={marketLabels}
                            style={xMergeStyles([
                              style.discountLabels,
                              isSoldOut && style.soldOutImg,
                            ])}
                          />
                        )}
                      </View>
                    </XViewExposure>
                  </View>
                )}
                {isEasyLife && (
                  <>
                    <VendorTag vendor={vendor} tags={category1Tags} />
                    <EasyLifeLabelList
                      tagList={vendorEasyLifeTagList}
                      style={{ marginBottom: getPixel(12) }}
                    />

                    <VendorTag vendor={vendor} tags={category2Tags} />
                    <View style={style.easyLifeTagPriceWrap}>
                      <View style={{ flex: 1 }}>
                        <VendorTag vendor={vendor} tags={otherTags} />
                      </View>
                      <View style={style.priceWrap}>
                        {priceDescProps && (
                          <View className={c2xStyles.priceEntryWrap}>
                            <BbkPriceDesc
                              {...priceDescProps}
                              showTotalPrice={priceDescProps.totalPrice}
                              originStyle={{
                                fontSize: font.labelLStyle.fontSize,
                                color: soldOutTextColor || color.fontSecondary,
                              }}
                              totalTextStyle={{
                                ...font.labelLStyle,
                                color: soldOutTextColor || color.fontSecondary,
                              }}
                              totalCurrencyStyle={{
                                ...font.labelLStyle,
                                color: soldOutTextColor || color.fontSecondary,
                              }}
                              totalPriceStyle={xMergeStyles([
                                font.caption1LightStyle,
                                { color: color.fontSecondary },
                              ])}
                              mainCurrencyStyle={{
                                fontSize: font.labelLStyle.fontSize,
                                color: soldOutTextColor || color.orangePrice,
                              }}
                              mainPriceStyle={{
                                fontSize: font.subHeadStyle.fontSize,
                                color: soldOutTextColor || color.orangePrice,
                              }}
                              totalPricePress={totalPricePress}
                            />

                            {Utils.isCtripIsd() && !isSoldOut && (
                              <Image
                                className={c2xStyles.priceEntryWrapArrow}
                                style={
                                  !priceDescProps.totalPrice && {
                                    marginTop: getPixel(-10),
                                  }
                                }
                                resizeMode="cover"
                                source={{
                                  uri: autoProtocol(
                                    'http://pic.c-ctrip.com/car/osd/mobile/bbk/resource/orange_arrow.png',
                                  ),
                                }}
                              />
                            )}
                          </View>
                        )}
                      </View>
                    </View>
                  </>
                )}

                {(vendor.adverts > 0 || soldOutLabel || isSoldOut) && (
                  <View
                    style={xMergeStyles([
                      style.flexRow,
                      Utils.isCtripIsd() &&
                        !isSoldOut && { paddingRight: getPixel(40) },
                    ])}
                  >
                    <View style={{ flex: 1, alignItems: 'flex-start' }}>
                      {vendor.adverts > 0 && (
                        <BbkLabel
                          labelSize="LLight"
                          textStyle={{ color: color.guideStepSequenceColor }}
                          text={Texts.adverts}
                          noBg={true}
                        />
                      )}
                    </View>
                    {!isSoldOut && soldOutLabel && (
                      <View>
                        <BbkLabel
                          {...soldoutLabelProps}
                          labelSize="LLight"
                          textStyle={{}}
                        />
                      </View>
                    )}
                  </View>
                )}
              </BbkTouchableExt>
            </View>
          </View>
        </>
      );

      const GS2Dom = (
        <View className={c2xStyles.vendorGS2}>
          <BbkTouchable
            className={c2xStyles.vendorGS2Wrap}
            onPress={onVerdorHeaderPress}
            disabled={isSoldOut}
          >
            <View>
              <VerdorHeader
                {...vendorHeaderProps}
                isBroker={isBroker}
                isEasyLife={false}
                isHideCommentLabel={isHideCommentLabel}
              />

              <View
                style={xMergeStyles([
                  style.flexRow,
                  style.vendorPriceWrap,
                  styles.gs2PickDropWrap,
                ])}
              >
                <View className={c2xStyles.flex1}>
                  <View style={xMergeStyles([style.flexRow, style.flexWrap])}>
                    <BbkText
                      className={c2xStyles.distanceText}
                      style={xMergeStyles([
                        soldOutTextColor && { color: soldOutTextColor },
                        !!dropOffDesc && { marginBottom: getPixel(8) },
                      ])}
                    >
                      {descDecorationFn(pickUpDesc, reference?.pRc > 0)}
                    </BbkText>
                    {reference?.pRc > 0 && (
                      <CarRentalCenterDesc
                        contentWrapStyle={styles.carRentalCenterWrap}
                      />
                    )}
                  </View>
                  {!!dropOffDesc && (
                    <View
                      style={xMergeStyles([
                        style.flexRow,
                        style.flexWrap,
                        styles.gs2DropWrap,
                      ])}
                    >
                      <BbkText
                        className={c2xStyles.distanceText}
                        style={soldOutTextColor && { color: soldOutTextColor }}
                      >
                        {descDecorationFn(dropOffDesc, reference?.rRc > 0)}
                      </BbkText>
                      {reference?.rRc > 0 && (
                        <CarRentalCenterDesc
                          contentWrapStyle={styles.carRentalCenterWrap}
                        />
                      )}
                    </View>
                  )}
                </View>
              </View>
              {vehicleTags.length > 0 && (
                <View className={c2xStyles.gs2LabelWrapLine1}>
                  <VendorTag vendor={vendor} tags={vehicleTags} />
                </View>
              )}
              <>
                <View className={c2xStyles.contentWrap}>
                  <View
                    className={c2xStyles.contentLeftWrap}
                    style={!hasGS2Tags && { justifyContent: 'flex-end' }}
                  >
                    {serviceTags.length > 0 && (
                      <View
                        className={
                          vehicleTags.length === 0
                            ? c2xStyles.gs2LabelWrapLine1
                            : c2xStyles.gs2LabelWrapLine2
                        }
                      >
                        <VendorTag vendor={vendor} tags={serviceTags} />
                      </View>
                    )}
                    {hasSoldOutLabel ? (
                      <View
                        style={xMergeStyles([
                          style.flexRow,
                          styles.soldOutWrap,
                        ])}
                      >
                        {!isSoldOut && soldOutLabel && (
                          <View>
                            <BbkLabel
                              {...soldoutLabelProps}
                              labelSize="LLight"
                              textStyle={{}}
                            />
                          </View>
                        )}
                        {isSoldOut && (
                          <BbkText className={c2xStyles.soldOutTextStyle}>
                            {Texts.soldOutText}
                          </BbkText>
                        )}
                      </View>
                    ) : (
                      <View
                        className={c2xStyles.hintContainer}
                        style={
                          !hasSoldOutLabel && { paddingBottom: getPixel(10) }
                        }
                      >
                        {!!hint && (
                          <View className={c2xStyles.hintWrap}>
                            <BbkText
                              className={c2xStyles.hintText}
                              style={
                                soldOutTextColor && {
                                  color: soldOutTextColor,
                                }
                              }
                            >
                              {hint}
                            </BbkText>
                          </View>
                        )}
                      </View>
                    )}
                  </View>
                  <View className={c2xStyles.contentRightWrap}>
                    <View
                      style={xMergeStyles([
                        style.priceWrap,
                        styles.gsPriceWrap,
                      ])}
                    >
                      {!!priceDescProps && (
                        <View className={c2xStyles.priceEntryWrap}>
                          <BbkPriceDesc
                            {...priceDescProps}
                            showTotalPrice={priceDescProps.totalPrice}
                            originStyle={{
                              fontSize: font.labelLStyle.fontSize,
                              color: soldOutTextColor || color.fontSecondary,
                            }}
                            totalTextStyle={{
                              ...font.labelLStyle,
                              color: soldOutTextColor || color.fontSecondary,
                            }}
                            totalCurrencyStyle={{
                              ...font.labelLStyle,
                              color: soldOutTextColor || color.fontSecondary,
                            }}
                            totalPriceStyle={xMergeStyles([
                              font.caption1LightStyle,
                              {
                                color: color.fontSecondary,
                                height: getPixel(20),
                              },
                            ])}
                            mainCurrencyStyle={{
                              fontSize: font.labelLStyle.fontSize,
                              color: soldOutTextColor || color.orangePrice,
                            }}
                            mainPriceStyle={{
                              fontSize: font.subHeadStyle.fontSize,
                              color: soldOutTextColor || color.orangePrice,
                            }}
                            totalPricePress={totalPricePress}
                          />
                        </View>
                      )}
                    </View>
                    {marketTags.length > 0 && (
                      <View className={c2xStyles.marketTagsWrap}>
                        <VendorTag vendor={vendor} tags={marketTags} />
                      </View>
                    )}
                  </View>
                </View>
              </>
            </View>
          </BbkTouchable>
        </View>
      );

      // eslint-disable-next-line no-nested-ternary
      return Utils.isCtripIsd() ? GS2Dom : Dom;
    },
  ),
);

const soldOutTheme = {
  soldOutTextColor: color.darkGrayBorder,
  soldOutEasyLifeLabelBgColor: color.soldOutEasyLifeLabelBgColor,
  soldOutLabelBgColor: color.soldOutLabelBgColor,
  soldOutTagBgColor: color.grayBgSecondary,
};

export default memo(
  withTheme(props => {
    const { isSoldOut, rentCenter } = props;
    const VendorDom = (
      <Vendor {...props} isSoldOut={isSoldOut} rentCenter={rentCenter} />
    );

    if (isSoldOut) {
      return (
        <BBkThemingProvider theme={soldOutTheme}>
          {VendorDom}
        </BBkThemingProvider>
      );
    }
    return VendorDom;
  }),
);
