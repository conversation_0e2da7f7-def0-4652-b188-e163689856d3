import { get as lodashGet } from 'lodash-es';
import React, { memo } from 'react';
import { xRouter, xShowToast, XView as View, xEnv } from '@ctrip/xtaro';
import IUrs from '@c2x/components/IUrs';
import { space } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import StyleSheet from '@c2x/apis/StyleSheet';
import BbkComponentRecommendTips, {
  TIPS_TYPE,
} from '../../../ComponentBusiness/CarRecommendTips';
import { controlHeight } from '../../../Components/Common/SectionListWithControl';
import { CarLog, Hooks, Utils } from '../../../Util/Index';
import { ApiResCode } from '../../../Constants/Index';
import SelectedFilterItems from '../../../Containers/SelectedFilterItemsContainer';
import RecommendFilter from '../../../Containers/RecommendFilterContainer';
import Texts from '../Texts';
import { VehicleListStyle } from '../Styles';
import {
  getListStatusData,
  listStatusKeyList,
} from '../../../Global/Cache/ListReqAndResData';
import {
  EasyLifeEntrance,
  CarCenterEntrance,
  FeedBackEnterance,
  ActivityEnterce,
  TangramEntrance,
  YunnanBanner,
  SecretBoxBanner,
  EasyLifeAllVehicleBanner,
  SelfServiceBanner,
  FilterEntrance,
} from './TipList';
import AgeModifyTip from '../../../Containers/AgeModifyTipContainer';

const { fixIOSOffsetBottom, getPixel } = BbkUtils;
const styles = StyleSheet.create({
  ipollContainer: {
    marginBottom: getPixel(12),
  },
});
const cacheDom = {
  SelectedFilterItems: (
    <SelectedFilterItems style={VehicleListStyle.borderTopWidth0} />
  ),
};
const SectionFooter = memo(
  ({
    shouldSetMinHeight,
    scrollViewHeight,
    vehicleTotalHeight,
    sectionsLen,
    enterList,
    activeGroupId,
    selectedFilters,
    getC2BOrderStatus,
    updateSelectedFilter,
    rentCenter,
    vehicleIndex,
    isNewSearchNoResult,
    listGoToBooking,
    cityId,
    clickSearchBar,
    ipollSceneid,
  }: any) => {
    const env = xEnv.getDevEnv()?.toLowerCase();
    const tipHeight = getListStatusData(listStatusKeyList.tipHeight) || 0;

    const minHeight = shouldSetMinHeight
      ? scrollViewHeight -
        fixIOSOffsetBottom(vehicleTotalHeight + controlHeight + tipHeight)
      : 0;

    const getTipType = type => {
      switch (type) {
        case 0:
          return TIPS_TYPE.RENTAL_FLAGSHIP_STORE;
        case 1:
          return TIPS_TYPE.PREFERRED;
        case 2:
        case 3:
          return TIPS_TYPE.GRAB_ORDER;
        case 4:
          return TIPS_TYPE.PRICE_INCREASE; // todo 待确认
        case 5:
          return TIPS_TYPE.FREE_CANCEL; // todo 待确认
        default:
          break;
      }

      return null;
    };

    const goC2BPage = () => {
      getC2BOrderStatus((url, jumpCallback) => {
        xRouter.navigateTo({ url, success: jumpCallback });
      });
    };

    const handleTipPress = data => {
      const { type, filterItem } = data;
      const url = BbkUtils.autoProtocol(
        type === 0
          ? '//m.ctrip.com/html5/isd/flagship?isHideNavBar=YES'
          : '//m.ctrip.com/html5/zuche/excellent?isHideNavBar=YES',
      );
      const callBackCode = type === 0 ? 'flagship' : 'used';
      const toastText = type === 0 ? Texts.flapshipToast : Texts.youxuanToast;
      const itemCode = lodashGet(filterItem, 'itemCode');
      switch (type) {
        case 0:
        case 1:
          xRouter.navigateTo({
            url,
            success: result => {
              if (result && result.data === callBackCode) {
                xShowToast({ title: toastText, duration: 1000 });
                Hooks.updateSelectedFilterByCode({
                  code: itemCode,
                  isSelected: true,
                  selectedFilters,
                  updateSelectedFilter,
                  activeGroupId,
                });
              }
            },
          });
          break;
        case 2:
        case 3:
          goC2BPage();
          break;
        case 22:
          // 搜筛中插选中筛选项
          Hooks.updateSelectedFilterByCode({
            code: itemCode,
            isSelected: true,
            selectedFilters,
            updateSelectedFilter,
            activeGroupId,
          });
          break;
        default:
          break;
      }
      CarLog.LogCode({ name: '点击_列表页_推荐提示', type });
    };

    const renderEnterDom = () => {
      let enterDom = null;
      if (enterList && enterList.length > 0) {
        const curEnterInfo = enterList.find(
          f =>
            lodashGet(f, 'locations[0].groupCode') === activeGroupId &&
            lodashGet(f, 'locations[0].index') === vehicleIndex + 1,
        );
        if (curEnterInfo) {
          const {
            type,
            title,
            subTitle,
            button,
            icon,
            jumpUrl,
            backGroundUrl,
          } = curEnterInfo;
          const buttonText = lodashGet(button, 'title');
          // ipoll调研中插 > 搜筛中插
          if (ipollSceneid) {
            enterDom = (
              <IUrs
                sceneId={ipollSceneid}
                env={env}
                bizId="CAR"
                locale="zh-CN"
                containerStyle={styles.ipollContainer}
              />
            );
          } else if (
            type === ApiResCode.ListPromptType.SearchCar &&
            !ipollSceneid
          ) {
            enterDom = (
              <FilterEntrance
                data={curEnterInfo}
                clickSearchBar={clickSearchBar}
                handleTipPress={handleTipPress}
                groupId={activeGroupId}
                vehicleIndex={vehicleIndex + 1}
              />
            );
          } else if (type === 'favoriteInfo') {
            enterDom = (
              <View style={VehicleListStyle.recommendFilter}>
                <RecommendFilter />
              </View>
            );
          } else if (type === ApiResCode.ListPromptType.EasyLifeEnter) {
            // 权益/营销活动/无忧租B版保持与A版一致
            if (Utils.isCtripIsd()) {
              const imageUrl = lodashGet(
                curEnterInfo,
                'contents[0].stringObjs[0].content',
              );
              const jumpUrl = lodashGet(
                curEnterInfo,
                'contents[0].stringObjs[0].url',
              );
              enterDom = (
                <ActivityEnterce imageUrl={imageUrl} jumpUrl={jumpUrl} />
              );
            } else {
              enterDom = <EasyLifeEntrance />;
            }
          } else if (type === ApiResCode.ListPromptType.RentCenterEnter) {
            if (rentCenter) {
              enterDom = <CarCenterEntrance rentCenter={rentCenter} />;
            }
          } else if (type === ApiResCode.ListPromptType.FeedBackEnter) {
            enterDom = <FeedBackEnterance />;
          } else if (type === ApiResCode.ListPromptType.Market) {
            enterDom = <TangramEntrance data={curEnterInfo} />;
          } else if (type === ApiResCode.ListPromptType.SelfServiceBanner) {
            enterDom = <SelfServiceBanner />;
          } else if (type === ApiResCode.ListPromptType.EasyLife2024) {
            enterDom = (
              <EasyLifeAllVehicleBanner
                backGroundUrl={backGroundUrl}
                jumpUrl={jumpUrl}
              />
            );
          } else if (type === ApiResCode.ListPromptType.YunnanEnter) {
            enterDom = <YunnanBanner data={curEnterInfo} />;
          } else if (type === ApiResCode.ListPromptType.SecretBoxBanner) {
            enterDom = (
              <SecretBoxBanner
                data={curEnterInfo}
                cityId={cityId}
                listGoToBooking={listGoToBooking}
              />
            );
          } else {
            enterDom = (
              <BbkComponentRecommendTips
                type={getTipType(type)}
                title={title}
                desc={subTitle}
                buttonText={buttonText}
                imgUrl={icon}
                style={{
                  marginBottom: Utils.isCtripIsd()
                    ? space.spaceM
                    : space.spaceL,
                  flex: 1,
                }}
                onPressCallback={() => {
                  handleTipPress(curEnterInfo);
                }}
              />
            );
          }
        }
      }

      return enterDom;
    };

    return (
      <View
        style={{
          minHeight,
        }}
      >
        <View
          style={
            (Utils.isCtripIsd() && !isNewSearchNoResult) || Utils.isCtripOsd()
              ? VehicleListStyle.vehicleMarginBottom_B
              : VehicleListStyle.vehicleMarginBottom
          }
        />

        <View>{renderEnterDom()}</View>

        {
          vehicleIndex === sectionsLen - 1 && cacheDom.SelectedFilterItems
          // todo remove this in batch_LIST
        }
        {vehicleIndex === sectionsLen - 1 && <AgeModifyTip />}
      </View>
    );
  },
);

export default SectionFooter;
