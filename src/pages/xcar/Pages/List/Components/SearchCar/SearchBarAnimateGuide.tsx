import React, { memo, useEffect, useState, CSSProperties, useRef } from 'react';
import {
  XView as View,
  xClassNames,
  XAnimated,
  xCreateAnimation,
  xMergeStyles,
  XImage,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';

import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import StyleSheet from '@c2x/apis/StyleSheet';
import getUrlQuery from '../../../../SwitchEnv/urlQuery';
import c2xStyles from './searchBar.module.scss';
import { StorageKey } from '../../../../Constants/Index';
import { CarLog, CarStorage, Utils } from '../../../../Util/Index';

const gif = 'https://dimg04.c-ctrip.com/images/1tg4112000litvm5gAF08.gif';

const { getPixel, useMemoizedFn } = BbkUtils;
const styles = StyleSheet.create({
  lottieWrap: {
    width: getPixel(360),
    height: getPixel(133),
  },
});

export interface ISearchBar {
  isFixed?: boolean;
  top?: number;
  containerStyle?: CSSProperties;
  clickSearchBar?: (value?: boolean) => void;
  visible?: boolean;
}

const SearchBarAnimateGuide: React.FC<ISearchBar> = ({
  isFixed,
  top,
  containerStyle,
  clickSearchBar,
  visible,
}) => {
  const [isBLueBorder, setIsBLueBorder] = useState(false);
  const [animationData, setAnimationData] = React.useState<any>(null);
  const [isShowLottie, setIsShowLottie] = React.useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef2 = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef3 = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef4 = useRef<NodeJS.Timeout | null>(null);

  const animation = React.useMemo(() => {
    return xCreateAnimation({
      duration: 720,
      delay: 0,
      timingFunction: 'linear',
    });
  }, []);
  useEffect(() => {
    if (top) {
      const urlQuery = getUrlQuery();
      if (urlQuery && urlQuery.debug === 'true') {
        CarStorage.remove(StorageKey.CAR_LIST_LOADED_SEARCH_BAR_GUIDE);
      }
      const data = CarStorage.loadSync(
        StorageKey.CAR_LIST_LOADED_SEARCH_BAR_GUIDE,
      );
      if (data) {
        setIsShowLottie(false);
        setIsBLueBorder(false);
      } else {
        Utils.getImageSize(
          gif,
          (width, height) => {
            if (height) {
              setIsShowLottie(true);
              timeoutRef.current = setTimeout(() => {
                setIsBLueBorder(true);
              }, 1400);
              timeoutRef2.current = setTimeout(() => {
                setIsShowLottie(false);
              }, 10000);
            }
          },
          () => {},
        );
      }
      CarStorage.save(
        StorageKey.CAR_LIST_LOADED_SEARCH_BAR_GUIDE,
        true,
        '100000M',
      );
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (timeoutRef2.current) {
        clearTimeout(timeoutRef2.current);
      }
    };
  }, [top, visible]);
  useEffect(() => {
    if (isBLueBorder) {
      animation.opacity(1).step();
      animation.opacity(0).step();
      animation.opacity(1).step();
      setAnimationData(animation?.export());
      timeoutRef3.current = setTimeout(() => {
        animation.opacity(0).step();
        setAnimationData(animation?.export());
        timeoutRef4.current = setTimeout(() => {
          setIsBLueBorder(false);
        }, 1000);
      }, 6500);
    }
    return () => {
      if (timeoutRef3.current) {
        clearTimeout(timeoutRef3.current);
      }
      if (timeoutRef4.current) {
        clearTimeout(timeoutRef4.current);
      }
    };
  }, [isBLueBorder, animation]);
  const closeLottie = useMemoizedFn(() => {
    setIsShowLottie(false);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsBLueBorder(true);
  });

  if (!top) {
    return null;
  }
  return (
    <>
      {!!isBLueBorder && (
        <BbkTouchable
          className={xClassNames(
            c2xStyles.contain,
            isFixed && c2xStyles.containAbs,
          )}
          style={xMergeStyles([top ? { top } : {}, containerStyle])}
          onPress={clickSearchBar}
          testID={CarLog.LogExposure({
            name: '曝光_列表页_搜索框高亮动画',
          })}
        >
          <XAnimated.View
            animation={animationData}
            className={xClassNames(c2xStyles.guideContain)}
            useNativeDriver={true}
            collapsable={false}
            style={{ opacity: 0 }}
          />
        </BbkTouchable>
      )}
      {!!isShowLottie && (
        <BbkTouchable
          testID={CarLog.LogExposure({
            name: '曝光_列表页_搜索框气泡动画',
          })}
          onPress={closeLottie}
          className={c2xStyles.containAbs}
          style={top ? { top: top + getPixel(60), right: getPixel(10) } : {}}
        >
          <XImage style={styles.lottieWrap} src={gif} />
        </BbkTouchable>
      )}
    </>
  );
};

export default memo(SearchBarAnimateGuide);
