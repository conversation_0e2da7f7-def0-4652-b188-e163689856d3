@import '../../../../Common/src/Tokens/tokens/color.scss';
.contain {
  align-self: center;
}
.shadowContain {
  flex-direction: row;
  align-items: center;
  width: 189px;
  height: 56px;
  border-radius: 56px;
  border-width: 1px;
  border-style: solid;
  border-color: $C_EFEFEF;
  background-color: $C_FFF;
  justify-content: space-between;
  padding-left: 19px;
  padding-right: 16px;
}
.guideContain {
  width: 189px;
  height: 56px;
  border-radius: 56px;
  border-width: 3px;
  border-style: solid;
  border-color: $C_006ff6;
  padding-left: 19px;
  padding-right: 16px;
}
.largeStyle {
  height: 63px;
  border: 2px solid $C_006ff6;
}
.hei63 {
  height: 63px;
}
.fixedStyle {
  border: 2px solid $C_EFEFEF;
}
.searchIcon {
  color: $C_999;
  font-size: 30px;
  margin-right: 3px;
}
.searchIcon2 {
  margin-left: 7px;
}
.inputWrap {
  flex-direction: row;
  align-items: center;
}
.inputContain {
  color: $C_999;
  font-size: 26px;
}
.containAbs {
  position: absolute;
  right: 16px;
  z-index: 999999999;
}
.deleteAllIcon {
  color: $C_ccc;
  font-size: 36px;
}
.activeInput {
  color: $C_111111;
}
.deleteAllIconWrap {
  margin-right: 16px;
  margin-bottom: -3px;
}
.shadowWrap {
  background-color: $C_FFF;
  border-radius: 56px;
}
