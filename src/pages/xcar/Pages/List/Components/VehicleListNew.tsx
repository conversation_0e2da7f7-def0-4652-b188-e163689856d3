import { get as lodashGet, forEach as lodashForEach } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import FlatList from '@c2x/components/FlatList';
import React, { PureComponent } from 'react';
import { XView as View, XViewExposure, xEnv } from '@ctrip/xtaro';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import memoizeOne from 'memoize-one';
import c2xStyles from './vehicleListNewC2xStyles.module.scss';
import BbkListNoMatch, {
  ImgType,
} from '../../../ComponentBusiness/ListNoMatch';
import { AndroidHandlerView, FlatListBounce } from '../../../Components/Index';
import VehicleNew from '../../../Containers/VehicleNewContainer';
import { CarLog, Utils, Channel, GetABCache } from '../../../Util/Index';
import {
  getLogDataFromState,
  setExposureKey,
  getSelecterFiltersExposureData,
} from '../../../State/List/Mappers';
import { ApiResCode, ListEnum, LogKey } from '../../../Constants/Index';
import { RecommendEventResult } from '../../../Constants/ListEnum';
import { cacheDom, getNoMatchText } from './Index';
import { getLogKey } from '../Method';
import SelectedFilterItems from '../../../Containers/SelectedFilterItemsContainer';
import SectionFooterContainer from '../../../Containers/VehicleListSectionFooterContainer';
import ListFooterComponent from '../../../Containers/ListFooterContainer';
import SearchNoResult from '../../../Containers/ListSearchNoResultContainer';
import RecommendBanner from '../../../Containers/ListRecommendBannerContainer';
import SearchLessResult from '../../../Containers/ListSearchLessResultContainer';
import SearchLessResultTip from '../../../Containers/ListSearchLessResultTipContainer';
import Texts from '../Texts';
import ScrollController from './ScrollController';

const { getPixel, isIos, vh, fixOffsetTop } = BbkUtils;
const { FetchListPageType } = ListEnum;
const searchNoResultHeight = getPixel(290);
const styles = StyleSheet.create({
  imgStyle: {
    width: getPixel(240),
    height: getPixel(240),
  },
  titleStyle: {
    ...font.title3LightStyle,
    color: color.fontPrimary,
  },
  noMatchStyle: {
    paddingTop: getPixel(64),
    backgroundColor: color.white,
  },
  operateButtonStyle: {
    width: getPixel(250),
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
    marginTop: getPixel(24),
  },
  wrapper: {
    marginTop: getPixel(-1),
  },
  sectionsEmptyBg: {
    backgroundColor: color.white,
  },
  footerStyle: {
    ...layout.flex1,
    justifyContent: 'space-between',
  },
  noMatchSelectedFilterItems: {
    paddingTop: getPixel(40),
  },
  recommendBannerWrap: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 99,
  },
  lessResultWrap: {
    marginTop: getPixel(12),
  },
  ipollContainer: {
    marginBottom: getPixel(12),
  },
});

const noop = () => {};

export interface VehicleListNewProps {
  throttle?: number;
  onEndReachedThreshold?: number;
  scrollUpCallback?: (e?: any) => void;
  scrollDownCallback?: (e?: any) => void;
  scrollEndCallback?: (e?: any) => void;
  activeGroupId: string;
  activeGroupName?: string;
  setVehPopData?: (args: any) => void;
  setVendorListModalData?: (args: any) => void;
  setEasyLifePopVisible: () => void;
  pageRef?: any;
  resetCache?: () => void;
  filterNoResult?: boolean;
  noMatchProps?: any;
  unUrlProduct?: boolean;
  scrollRefFn?: (ref) => void;
  closeTipPop?: () => void;
  scrollCallback?: (e?: any) => void;
  fetchListPage: (args?: any) => void;
  siblingsGroups: { before: any; next: any };
  setActiveGroupId: (data) => void;
  isLoading: boolean;
  isLoadingFail: boolean;
  isLastPage: boolean;
  priceCount: number;
  abVersion: string;
  recommendEventResult?: RecommendEventResult;
  isNewSearchNoResult?: boolean;
  isRecommend?: boolean;
  curVehicleCount?: number;
  refFn?: (ref) => void;
  isFilteredRecommend?: boolean;
  isFilteredRecommendAb?: boolean;
  isEasyLife2024NoResult?: boolean;
  isGroupLoading: boolean;
  isFilterFilterNoResult: boolean;
  fromPage?: string;
  onScrollEndDrag?: any;
  sections?: any;
  initialNumToRender?: any;
  endFillColor?: any;
  clickSearchBar?: () => void;
  ipollPosNum?: number;
  ipollGroup?: string;
  sceneid?: string;
  searchKeyWord?: string;
}

interface VehicleListNewState {
  priceGroupIndex?: any;
  isShowRecommendBanner?: boolean;
  isReadyToNextGroup: boolean;
  isReadyToBeforeGroup: boolean;
  isShowHeaderSC: boolean;
  wrapperHeight: number;
}

const isPriceGroup = sectionItem => {
  return sectionItem.priceGroup && sectionItem.priceGroup.length > 0;
};

export default class VehicleListNew extends PureComponent<
  VehicleListNewProps,
  VehicleListNewState
> {
  renderSectionCount = {
    current: 0,
    total: Number.MAX_SAFE_INTEGER,
  };

  scroller = null;

  viewabilityConfig = null;

  allVehicleHeight = 0;

  lastScrollY = 0;

  onScrollBegin = false;

  constructor(props) {
    super(props);
    this.state = {
      priceGroupIndex: {},
      isShowRecommendBanner: false,
      isReadyToNextGroup: false,
      isReadyToBeforeGroup: false,
      isShowHeaderSC: false,
      wrapperHeight: vh(100) - getPixel(fixOffsetTop(230)),
    };
    this.viewabilityConfig = {
      minimumViewTime: props.throttle,
      viewAreaCoveragePercentThreshold: 100,
    };
    props.refFn(this);
  }

  componentDidUpdate(prevProps) {
    const { isGroupLoading, sections } = this.props;
    const { isGroupLoading: isGroupLoadingPrev } = prevProps;

    if (isGroupLoadingPrev !== isGroupLoading && sections?.length > 0) {
      setTimeout(() => {
        if (isIos) {
          this.scroller?.scrollToIndex({
            index: 0,
            viewPosition: 0,
          });
        }
      });
    }
  }

  onVehicleLayout = ({ nativeEvent }) => {
    const { height } = nativeEvent.layout;
    this.allVehicleHeight += height;
  };

  getPriceGroups = sectionItem => {
    return sectionItem?.priceGroup;
  };

  getSection = sectionItem => {
    const { priceGroupIndex } = this.state;
    if (!isPriceGroup(sectionItem) || sectionItem.isEasyLife2024)
      return sectionItem;
    const priceGroupSection =
      this.getPriceGroups(sectionItem)[
        priceGroupIndex[sectionItem.vehicleIndex] || 0
      ];

    priceGroupSection.vehicleIndex = sectionItem.vehicleIndex;
    priceGroupSection.recommendInfo = sectionItem.recommendInfo;
    return priceGroupSection;
  };

  getItem = data => {
    if (!isPriceGroup(data.item) || data.item.isEasyLife2024)
      return lodashGet(data, 'item.data[0]');
    return this.getSection(data.item).vendorPriceList;
  };

  isLoadCompleted() {
    const {
      isLoading,
      isLoadingFail,
      isLastPage,
      isFilterFilterNoResult,
      isEasyLife2024NoResult,
    } = this.props;
    const { current, total } = this.renderSectionCount;
    const gap = 2;
    if (isFilterFilterNoResult || isEasyLife2024NoResult) {
      return true;
    }
    return !isLoading && !isLoadingFail && isLastPage && current + gap >= total;
  }

  onChangePriceGroup = ({ index, vehicleIndex, vehiclePriceGroupDesc }) => {
    const { priceGroupIndex } = this.state;
    this.setState({
      priceGroupIndex: { ...priceGroupIndex, [vehicleIndex]: index },
    });
    CarLog.LogCode({
      name: '点击_列表页_价格分组项',

      vehiclePriceGroupDesc,
    });
  };

  getExposureData = (vehicleExposureData, isGroup, isEasyLife2024) => {
    const { recommendEventResult, activeGroupName, searchKeyWord } = this.props;
    const isNewDetail = Utils.isCtripIsd();
    if (isNewDetail && isGroup && !isEasyLife2024) {
      return null;
    }
    const { queryVid, groupId, selectedFilters } = getLogDataFromState();
    const baseData = {
      pageId: Channel.getPageId().List.ID,
      queryVid,
      groupId,
      groupName: activeGroupName,
      realGroupId: vehicleExposureData?.realGroupId,
      realGroupName: vehicleExposureData?.realGroupName,
      selectedFilters,
    };

    const key = isNewDetail
      ? LogKey.c_car_trace_list_vehicle_exposure_new
      : LogKey.c_car_trace_list_vehicle_exposure_222013;
    const vehicleData = isNewDetail
      ? [vehicleExposureData]
      : vehicleExposureData;
    return CarLog.LogExposure({
      key,
      vehicleData,
      ...baseData,
      abVersion: this.props.abVersion,
      recommendEventResult,
      searchKeyWord,
    });
  };

  renderItem = data => {
    const {
      sections,
      curVehicleCount,
      isRecommend,
      priceCount,
      activeGroupName,
      recommendEventResult,
      scrollDownCallback,
      scrollUpCallback,
      scrollEndCallback,
      unUrlProduct,
      activeGroupId,
      closeTipPop,
      // getLogKey,
      setVehPopData,
      setVendorListModalData,
      isNewSearchNoResult,
      isFilteredRecommend,
      fromPage,
      ipollPosNum,
      ipollGroup,
      sceneid,
      searchKeyWord,
    } = this.props;
    const idx = data?.item?.vehicleIndex;
    this.renderSectionCount.current = idx + 1;
    this.renderSectionCount.total = sections.length;

    const { priceGroupIndex } = this.state;
    const item = this.getItem(data);
    const sectionsLen = lodashGet(this.props.sections, 'length');
    const curSection = this.getSection(data.item);
    const vehicleExposureData = getLogKey(
      item && item[0],
      curSection,
      0,
      activeGroupId,
    );

    // android 少于 2 条数据时不展示打通，无法触发 scroll
    const shouldSetMinHeight = sectionsLen <= 3;
    // 是否是android且不满一屏
    const isAndroidScroll = !isIos && shouldSetMinHeight;
    const Container = isAndroidScroll ? AndroidHandlerView : XViewExposure;
    const isHasFilterNoResult = isFilteredRecommend && idx === 0;
    const isHasRecommendTip =
      isRecommend && curVehicleCount === idx && priceCount > 0;
    const env = xEnv.getDevEnv()?.toLowerCase();

    const ipollSceneid =
      GetABCache.isISDListIpoll() &&
      idx + 1 === ipollPosNum &&
      activeGroupId === ipollGroup &&
      sceneid;
    return (
      <>
        <Container
          scrollEndCallback={scrollEndCallback}
          scrollDownCallback={scrollDownCallback}
          scrollUpCallback={scrollUpCallback}
          testID={this.getExposureData(
            vehicleExposureData,
            curSection.isGroup,
            curSection.isEasyLife2024,
          )}
        >
          {isHasFilterNoResult && <SelectedFilterItems />}
          {isNewSearchNoResult && idx === 0 && !isRecommend && (
            <SearchNoResult />
          )}
          {
            // 有推荐且列表页无此车型组时，少结果展示在第一辆推荐车头部
            isRecommend &&
              curVehicleCount === 0 &&
              curVehicleCount === idx &&
              priceCount > 0 && <SearchLessResult />
          }
          {(isHasRecommendTip || isHasFilterNoResult) && (
            <SearchLessResultTip />
          )}
          <VehicleNew
            isRecommend={isRecommend}
            data={data?.item}
            unUrlProduct={unUrlProduct}
            vehicleCount={sectionsLen}
            section={curSection}
            priceGroupItem={this.getPriceGroups(data)}
            priceGroupIndex={priceGroupIndex}
            onLayout={shouldSetMinHeight && this.onVehicleLayout}
            onChangePriceGroup={this.onChangePriceGroup}
            setVehPopData={setVehPopData}
            setVendorListModalData={setVendorListModalData}
            activeGroupId={activeGroupId}
            activeGroupName={activeGroupName}
            closeTipPop={closeTipPop}
            fromPage={fromPage}
            searchKeyWord={searchKeyWord}
          />

          {isRecommend &&
            idx + 1 === curVehicleCount &&
            recommendEventResult === RecommendEventResult.LessResult && (
              <SearchLessResult
                wrapStyle={styles.lessResultWrap}
                isShowGradient={true}
              />
            )}
        </Container>
        {this.renderSectionFooter(data, ipollSceneid)}
      </>
    );
  };

  renderSectionFooter = (data, ipollSceneid) => {
    const { activeGroupId, isRecommend, curVehicleCount, clickSearchBar } =
      this.props;
    const { item } = data;
    const { vehicleIndex } = item;
    return isRecommend ? (
      vehicleIndex === curVehicleCount - 1 ? null : (
        <View className={c2xStyles.splitView} />
      )
    ) : (
      <SectionFooterContainer
        shouldSetMinHeight={false}
        vehicleIndex={vehicleIndex}
        vehicleTotalHeight={this.allVehicleHeight}
        // sectionsLen={this.sectionsLen}
        activeGroupId={activeGroupId}
        clickSearchBar={clickSearchBar}
        ipollSceneid={ipollSceneid}
      />
    );
  };

  scrollToTop = () => {
    const { sections = [] } = this.props;

    if (sections.length > 0) {
      this.scroller?.scrollToLocation?.({
        viewOffset: 100,
        sectionIndex: 0,
        itemIndex: 0,
        animated: true,
      });
    }
  };

  NoMatch = memoizeOne(
    (
      title: string = '',
      data?: any,
      showSelecterFilterItems: boolean = false,
      isFilteredRecommendAb: boolean = false,
    ) =>
      isFilteredRecommendAb ? (
        <SelectedFilterItems />
      ) : (
        <>
          <View className={c2xStyles.noMatchWrap}>
            <BbkListNoMatch
              imgStyle={styles.imgStyle}
              type={ImgType.No_Search_Result}
              titleStyle={styles.titleStyle}
              style={styles.noMatchStyle}
              title={title}
              subTitle=""
              isShowOperateButton={false}
              isShowRentalDate={false}
              useOnlyProps={true}
              operateButtonStyle={styles.operateButtonStyle}
              operateButtonSize="S"
            />
          </View>
          {showSelecterFilterItems && (
            <SelectedFilterItems
              buryData={data}
              noMatch={true}
              style={styles.noMatchSelectedFilterItems}
            />
          )}
        </>
      ),
  );

  getKeyExtractor = item => {
    return `vehicle_${item?.vehicleKey}_${item?.vehicleIndex}`;
  };

  scrollUpDown = (event, triggerEvent = 'onScroll') => {
    const { y } = event.nativeEvent.contentOffset;
    const scrollUp = y - this.lastScrollY;
    if (triggerEvent === 'onScrollBeginDrag') {
      this.lastScrollY = y;
    }
    const { scrollUpCallback, scrollDownCallback } = this.props;

    /**
     * 头部隐藏/显示
     */
    let triggerScrollUpCallback = false;
    let triggerScrollDownCallback = false;
    if (this.onScrollBegin && triggerEvent === 'onScrollEndDrag') {
      if (scrollUp > 0) {
        triggerScrollUpCallback = true;
      } else if (scrollUp < 0) {
        triggerScrollDownCallback = true;
      }
    }

    // 安卓顶部不会触发 onScroll的情况 当吸顶时，y<0 而且 scrollUp<0
    if (
      !isIos &&
      this.onScrollBegin &&
      triggerEvent === 'onScrollEndDrag' &&
      y <= 0 &&
      scrollUp <= 0
    ) {
      triggerScrollDownCallback = true;
    }

    if (triggerScrollUpCallback && scrollUpCallback) {
      //  this.setCurPage(true);
      scrollUpCallback(event);
    } else if (triggerScrollDownCallback && scrollDownCallback) {
      scrollDownCallback(event);
    }
  };

  onScroll = event => {
    const offsetY = event?.nativeEvent?.contentOffset?.y;
    const { scrollCallback = noop, isNewSearchNoResult } = this.props;
    scrollCallback(event);
    this.scrollUpDown(event);
    // RecommendBanner滚动交互
    if (isNewSearchNoResult) {
      const { isShowRecommendBanner } = this.state;
      if (
        offsetY < searchNoResultHeight * 2 &&
        isShowRecommendBanner !== offsetY > searchNoResultHeight
      ) {
        this.setState({
          isShowRecommendBanner: offsetY > searchNoResultHeight,
        });
      }
    }
    this.setChangeGroupStatus(event);
  };

  setChangeGroupStatus = event => {
    const { siblingsGroups, isRecommend } = this.props;

    const nextGroupCode = siblingsGroups.next?.groupCode;
    const beforeGroupCode = siblingsGroups.before?.groupCode;

    const layoutHeight = event?.nativeEvent?.layoutMeasurement.height;
    const contentHeight = event?.nativeEvent?.contentSize.height;
    const offsetY = event?.nativeEvent?.contentOffset.y;

    const scrollUp = offsetY + layoutHeight - contentHeight;

    const isLoadCompleted = this.isLoadCompleted();

    const { isShowHeaderSC } = this.state;

    const limitHeight = vh(10);

    if (isShowHeaderSC !== offsetY < 0) {
      const platformSwitch = this.isAndroidUseScrollView() || isIos;
      const isShowHeaderSCNew =
        !isRecommend && platformSwitch && offsetY < 0 && !!beforeGroupCode;
      this.setState({ isShowHeaderSC: isShowHeaderSCNew });
    }

    if (nextGroupCode && isLoadCompleted) {
      this.setState({ isReadyToNextGroup: scrollUp > limitHeight });
    }

    if (beforeGroupCode && offsetY < 0) {
      this.setState({ isReadyToBeforeGroup: offsetY < -limitHeight });
    }
  };

  changeGroup = () => {
    const { siblingsGroups, setActiveGroupId } = this.props;
    const { isReadyToNextGroup, isReadyToBeforeGroup } = this.state;
    const nextGroupCode = siblingsGroups.next?.groupCode;
    const beforeGroupCode = siblingsGroups.before?.groupCode;

    let activeGroupId = null;
    if (isReadyToBeforeGroup && beforeGroupCode) {
      activeGroupId = beforeGroupCode;
    } else if (isReadyToNextGroup && nextGroupCode) {
      activeGroupId = nextGroupCode;
    }

    if (!activeGroupId) {
      return;
    }

    setTimeout(() => {
      setActiveGroupId({ activeGroupId, isAutoChange: true });
    });
    this.setState({
      isReadyToNextGroup: false,
      isReadyToBeforeGroup: false,
      isShowHeaderSC: false,
    });
  };

  onScrollBeginDrag = event => {
    this.onScrollBegin = true;
    this.scrollUpDown(event, 'onScrollBeginDrag');
  };

  onScrollEndDrag = event => {
    // this.recoverMoveLength();
    this.scrollUpDown(event, 'onScrollEndDrag');
    const {
      onScrollEndDrag,
      scrollEndCallback = noop,
      isRecommend,
    } = this.props;
    scrollEndCallback(event);
    BbkUtils.ensureFunctionCall(onScrollEndDrag);
    if (!isRecommend) {
      this.changeGroup();
    }
  };

  refFn = ref => {
    const { scrollRefFn: propsRefFn } = this.props;
    if (propsRefFn) {
      propsRefFn(ref);
    }
    this.scroller = ref;
  };

  listFooterComponent = () => {
    const {
      sections,
      activeGroupId,
      activeGroupName,
      isNewSearchNoResult,
      siblingsGroups,
    } = this.props;
    const { isReadyToNextGroup } = this.state;
    const isAndroidUseScrollView = this.isAndroidUseScrollView();
    const sectionsLen = lodashGet(sections, 'length');
    const selectFiltersExposureData = getSelecterFiltersExposureData(
      activeGroupId,
      activeGroupName,
      sectionsLen,
    );
    if (isNewSearchNoResult) {
      return (
        <View className={c2xStyles.newSearchNoResultListFooterWrap}>
          <BbkText className={c2xStyles.newSearchNoResultListFooterText}>
            {Texts.alreadyShowAllPrice}
          </BbkText>
        </View>
      );
    }
    return (
      <ListFooterComponent
        sectionsLen={sectionsLen}
        showScrollController={isAndroidUseScrollView || isIos}
        isReadyToNextGroup={isReadyToNextGroup}
        nextGroupName={siblingsGroups.next?.groupName}
        selectFiltersExposureData={selectFiltersExposureData}
        fetchNextPage={this.fetchNextPage}
      />
    );
  };

  onMomentumScrollEnd = event => {
    const { scrollEndCallback = noop } = this.props;
    scrollEndCallback(event);
  };

  setExposureData = ({ changed }) => {
    lodashForEach(changed, ({ item }) => {
      try {
        if (item.data) {
          lodashForEach(item.data[0], (vendor, index) => {
            const key = getLogKey(vendor, item, index);
            setExposureKey(key);
          });
        } else {
          const key = getLogKey(item[0], item, 0);
          setExposureKey(key);
        }
      } catch (e) {
        // eslint-disable-next-line
        console.warn('setExposureData error', item.data, item);
      }
    });
  };

  onViewableItemsChanged = args => {
    const { viewableItems } = args;
    if (viewableItems.length > 0) {
      try {
        this.setExposureData(args);
      } catch (e) {
        // eslint-disable-next-line
        console.warn('onViewableItemsChanged error', e);
      }
    }
  };

  fetchNextPage = () => {
    const { fetchListPage, isRecommend } = this.props;
    // 无少结果推荐时，下拉不刷新数据
    if (isRecommend) {
      return;
    }
    fetchListPage({ type: FetchListPageType.Next_Page_Search });
  };

  onEndReached = () => {
    const { isLoading, isLastPage, isLoadingFail } = this.props;
    if (!isLoading && !isLastPage && !isLoadingFail) {
      this.fetchNextPage();
    }
  };

  isAndroidUseScrollView = () => {
    const { activeGroupId } = this.props;
    return !isIos && activeGroupId === ApiResCode.EasyLife2024GroupCode;
  };

  onLayoutWrapper = e => {
    this.setState({
      wrapperHeight: e.nativeEvent.layout.height,
    });
  };

  render() {
    const {
      sections,
      initialNumToRender,
      endFillColor,
      activeGroupId,
      filterNoResult,
      noMatchProps,
      activeGroupName,
      onEndReachedThreshold = 0.2,
      resetCache,
      isNewSearchNoResult,
      isRecommend,
      isFilteredRecommendAb,
      isEasyLife2024NoResult,
      setEasyLifePopVisible,
      siblingsGroups,
    } = this.props;
    const {
      isShowRecommendBanner,
      isReadyToBeforeGroup,
      isShowHeaderSC,
      wrapperHeight,
    } = this.state;
    const showTipListNew = false;
    const sectionsLen = lodashGet(sections, 'length');
    const selectFiltersExposureData = getSelecterFiltersExposureData(
      activeGroupId,
      activeGroupName,
      sectionsLen,
    );
    const isEmpty = !isFilteredRecommendAb && sectionsLen === 0;
    const isEasyLife2024 = activeGroupId === ApiResCode.EasyLife2024GroupCode;
    const isAndroidUseScrollView = this.isAndroidUseScrollView();

    const List = isAndroidUseScrollView ? FlatListBounce : FlatList;
    return (
      <View className={c2xStyles.out} onLayout={this.onLayoutWrapper}>
        {isNewSearchNoResult && isShowRecommendBanner && !isRecommend && (
          <RecommendBanner wrapStyle={styles.recommendBannerWrap} />
        )}
        {isShowHeaderSC && (
          <ScrollController
            position="header"
            isReady={isReadyToBeforeGroup}
            nextGroupName={siblingsGroups.before?.groupName}
          />
        )}
        <List
          style={isEmpty ? styles.sectionsEmptyBg : styles.wrapper}
          contentContainerStyle={{ minHeight: wrapperHeight }}
          showsVerticalScrollIndicator={false}
          ref={this.refFn}
          onScroll={this.onScroll}
          initialNumToRender={Math.min(
            Math.max(initialNumToRender, sections.length),
            500,
          )}
          endFillColor={endFillColor}
          renderItem={this.renderItem}
          data={sections}
          keyExtractor={this.getKeyExtractor}
          scrollEventThrottle={100}
          onMomentumScrollEnd={this.onMomentumScrollEnd}
          onScrollBeginDrag={this.onScrollBeginDrag}
          onScrollEndDrag={this.onScrollEndDrag}
          ListFooterComponentStyle={styles.footerStyle}
          ListHeaderComponent={cacheDom.TipList(
            resetCache,
            activeGroupId,
            showTipListNew,
            filterNoResult,
            noMatchProps,
            undefined,
            isEasyLife2024,
            isEasyLife2024NoResult,
            setEasyLifePopVisible,
          )}
          ListFooterComponent={!isRecommend && this.listFooterComponent()}
          ListEmptyComponent={
            !noMatchProps &&
            !isRecommend &&
            this.NoMatch(
              getNoMatchText(activeGroupName),
              selectFiltersExposureData,
              true,
              isFilteredRecommendAb,
            )
          }
          onViewableItemsChanged={this.onViewableItemsChanged}
          onEndReachedThreshold={onEndReachedThreshold}
          onEndReached={isNewSearchNoResult ? noop : this.onEndReached}
          // https://github.com/facebook/react-native/blob/master/Libraries/Lists/ViewabilityHelper.js
          viewabilityConfig={this.viewabilityConfig}
        />
      </View>
    );
  }
}
