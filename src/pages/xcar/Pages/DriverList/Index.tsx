import ViewPort from '@c2x/components/ViewPort';
import { IBasePageProps } from '@c2x/components/Page';
import Device from '@c2x/apis/Device';
import Loading from '@c2x/apis/Loading';
import React from 'react';

import BbkToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import {
  Passenger,
  CertificateType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import BbkDriverListModal from '../../ComponentBusiness/DriverListModal';
import Utils from '../../Util/Utils';
import CPage, { IStateType } from '../../Components/App/CPage';

import { User, CarLog } from '../../Util/Index';
import Channel from '../../Util/Channel';
import texts from './Texts';

const noop = () => {};

interface IDriverListPropsType extends IBasePageProps {
  isLoading: boolean;
  isLoadingSuccess?: boolean;
  fetchApiDriverList: (data?: any) => void;
  fetchApiIdCardList: (params?: any) => void;
  availableCertificates: Array<CertificateType>;
  curCertificates: any;
  deleteDriver: (params?: any, callback?: (res: any) => void) => void;
  passengerList: Array<Passenger>;
  passenger: Passenger;
  selectDriver: (driver: Passenger) => void;
  isForceUpdate: boolean;
  yongAge?: number;
  oldAge?: number;
  isSupportZhima?: boolean;
  isAuthorized?: boolean;
  userName?: string;
  pageParam?: {
    passengerId?: string;
  };
  isCreditRent?: boolean;
  isCtripCreditRent?: boolean;
  fromurl?: string;
  addInstructData?: {
    title: string;
    content: string;
  };
}

interface IDriverListStateType extends IStateType {}

export default class DriverList extends CPage<
  IDriverListPropsType,
  IDriverListStateType
> {
  bbkDriverListModalRef: any;

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().DriverList.ID;
  }

  pageGoBack = () => {
    this.pop();
    CarLog.LogCode({ name: '点击_驾驶员列表页_后退' });
  };

  getIdCardList = () => {
    this.props.fetchApiIdCardList();
  };

  onLogin = async () => {
    const res = await User.toLogin();
    if (res) {
      this.props.fetchApiDriverList({
        fromurl: this.props.fromurl,
      });
    } else {
      this.pop();
    }
  };

  isLogin = async () => {
    const a = await User.isLogin();
    if (!a) {
      this.onLogin();
    } else {
      this.props.fetchApiDriverList({
        fromurl: this.props.fromurl,
      });
    }
  };

  clearSlider = () => {
    const { clearSlider = noop } = this.bbkDriverListModalRef || {};
    clearSlider();
  };

  addDriver = () => {
    const { fromurl } = this.props;
    this.push(Channel.getPageId().DriverEdit.EN, {
      isAdd: true,
      fromurl: fromurl || 'Booking',
    });
    this.clearSlider();
    CarLog.LogCode({ name: '点击_驾驶员列表页_添加驾驶员' });
  };

  editDriver = (
    driver: Passenger,
    index: number,
    driverLength: number,
    isSelectVaild?: boolean,
  ) => {
    const { fromurl } = this.props;
    this.push(Channel.getPageId().DriverEdit.EN, {
      passenger: driver,
      fromurl: fromurl || 'Booking',
    });
    const data = {
      index,
      driverLength,
      isSelectVaild,
    };
    let logEnName = '点击_驾驶员列表页_编辑驾驶员';
    // 如果是选中了无效的驾驶员，进入编辑页面，但埋点为点击埋点
    if (isSelectVaild) {
      logEnName = '点击_驾驶员列表页_选择驾驶员';
    }
    CarLog.LogCode({
      name: logEnName,
      data,
    });
  };

  deleteDriver = (driver: Passenger, index: number, driverLength: number) => {
    const { deleteDriver, fetchApiDriverList } = this.props;
    deleteDriver({ passengerId: driver.passengerId }, result => {
      const { isSuccess } = result;
      if (isSuccess) {
        BbkToast.show(texts.deleteSuccess, 1);
        fetchApiDriverList({
          isForceUpdate: true,
          fromurl: this.props.fromurl,
        });
        this.clearSlider();
      }
    });
    const data = {
      index,
      driverLength,
    };
    CarLog.LogCode({
      name: '点击_驾驶员列表页_删除驾驶员',

      data,
    });
  };

  selectDriver = (
    driver: Passenger,
    index: number,
    driverLength: number,
    isSelectVaild?: boolean,
  ) => {
    const { selectDriver, fetchApiDriverList, passenger } = this.props;
    if (passenger?.passengerId === driver?.passengerId) {
      return;
    }
    selectDriver(driver);
    fetchApiDriverList({ isForceUpdate: true, fromurl: this.props.fromurl });
    const data = {
      index,
      driverLength,
      isSelectVaild,
    };
    CarLog.LogCode({
      name: '点击_驾驶员列表页_选择驾驶员',

      data,
    });
  };

  componentDidMount() {
    super.componentDidMount();
    Device.setStatusBarStyle('darkContent');
    const { availableCertificates = [] } = this.props;
    // 国内渠道初始化身份证类型列表
    if (Utils.isCtripIsd() && availableCertificates.length <= 0) {
      this.getIdCardList();
    }

    this.isLogin();
  }

  componentDidUpdate(prevProps) {
    const { passengerList = [] } = this.props;
    if (
      !prevProps.isLoading &&
      this.props.isLoading &&
      !passengerList?.length
    ) {
      Loading.showMaskLoading();
    } else if (prevProps.isLoading && !this.props.isLoading) {
      Loading.hideMaskLoading();
    }
  }

  renderPage() {
    const {
      isLoading,
      isLoadingSuccess,
      passengerList = [],
      curCertificates,
      passenger,
      yongAge,
      oldAge,
      availableCertificates,
      isSupportZhima,
      isAuthorized,
      userName,
      pageParam = {},
      isCtripCreditRent,
      isCreditRent,
      addInstructData,
    } = this.props;

    return (
      <ViewPort>
        {(!isLoading || passengerList.length > 0) && isLoadingSuccess && (
          <BbkDriverListModal
            ref={ref => {
              this.bbkDriverListModalRef = ref;
            }}
            data={passengerList}
            yongAge={yongAge}
            oldAge={oldAge}
            type={Utils.getType()}
            onAddDriver={this.addDriver}
            onPress={(driver, index, driverLength, isSelectVaild) => {
              this.selectDriver(driver, index, driverLength, isSelectVaild);
            }}
            isSupportZhima={isSupportZhima}
            isAuthorized={isAuthorized}
            userName={userName}
            curCertificates={curCertificates}
            onEdit={(driver, index, driverLength, isSelectVaild) => {
              this.editDriver(driver, index, driverLength, isSelectVaild);
            }}
            onDelete={(driver, index, driverLength) => {
              this.deleteDriver(driver, index, driverLength);
            }}
            isModal={false}
            passenger={passenger}
            availableCertificates={availableCertificates}
            onCancel={this.pageGoBack}
            isCtripCreditRent={isCtripCreditRent}
            isCreditRent={isCreditRent}
            scrollToPassengerId={pageParam.passengerId}
            addInstructData={addInstructData}
          />
        )}
      </ViewPort>
    );
  }
}
