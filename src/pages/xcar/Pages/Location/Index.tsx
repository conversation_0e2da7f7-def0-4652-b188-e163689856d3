import ViewPort from '@c2x/components/ViewPort';
import React from 'react';

import CPage, { ICPageProps, IStateType } from '../../Components/App/CPage';
import LocationSelector from '../../Components/LocationSelector/Index';
import Channel from '../../Util/Channel';
import { CarLog, EventHelper, Utils } from '../../Util/Index';
import { AppType, PageType } from '../../Components/LocationSelector/Types';
import { getLocationSelectorPromises } from './Utils';
import { AreaInfo } from '../../Types/Dto/GetAreaListType';
import { CityInfo } from '../../Types/Dto/GetCityListType';
import { EventName } from '../../Constants/Index';
import withForwardPage from '../../withForwardPage';
import AppContext from '../../Util/AppContext';
import { initializeABLocationPage } from '../../Util/CarABTesting/InitializeAB';
import getUrlQuery from '../../SwitchEnv/urlQuery';

interface LocationComponentProps {
  pickType: 'pickup' | 'dropOff';
  pageType: PageType.City;
  cityId?: string;
  areaId?: string;
  areaName?: string;
  cityName?: string;
  isHideAutonomy?: boolean; // 是否屏蔽港澳台自治区，包括历史屏蔽、定位屏蔽
}
interface ILocationProps extends ICPageProps, LocationComponentProps {
  handleAreaPress?: (data: { area: AreaInfo; city: CityInfo }) => void;
}

let popTimer = null;

class Location extends CPage<
  ILocationProps,
  IStateType & LocationComponentProps
> {
  constructor(props) {
    super(props);
    const urlQuery = getUrlQuery();
    const { pickType, pageType, cityId, cityName, areaId, areaName } =
      urlQuery || {};
    const {
      pickType: pickTypeProp,
      pageType: pageTypeProp,
      cityId: cityIdProp,
      cityName: cityNameProp,
      areaId: areaIdProp,
      areaName: areaNameProp,
    } = props;
    this.state = {
      pickType: pickTypeProp || pickType,
      pageType: pageTypeProp || pageType,
      cityId: String(cityIdProp || cityId),
      cityName: cityNameProp || cityName,
      areaId: String(areaIdProp || areaId),
      areaName: areaNameProp || areaName,
    };
    this.onPageReady = this.onPageReady.bind(this);
    this.handleAreaPress = this.handleAreaPress.bind(this);
    initializeABLocationPage();
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().Location.ID;
  }

  componentDidMount(): void {
    super.componentDidMount();
    this.addTimer(
      setTimeout(() => {
        super.lazyLoadOtherModules();
      }, 1000),
    );
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    if (popTimer) {
      clearTimeout(popTimer);
    }
  }

  goBack = () => {
    if (Utils.isCtripIsd()) {
      this.pop();
    } else {
      popTimer = setTimeout(() => {
        this.pop();
      }, 100);
    }
  };

  handleCrossPress = type => {
    this.goBack();
    CarLog.LogCode({
      name: '点击_城市区域页_关闭',
      info: {
        cityPageType: type,
        mainPageSearchFrom: this.state.pageType,
        cityPageTabFrom: Utils.isCtripIsd() ? AppType.ISD : AppType.OSD,
      },
    });
  };

  handleAreaPress = ({ area, city }) => {
    if (this.props.handleAreaPress) {
      this.props.handleAreaPress({ area, city });
    } else {
      EventHelper.sendEvent(EventName.LocationChanged, {
        area,
        city,
        appType: AppContext.CarEnv.appType,
      });
    }
    this.goBack();
  };

  renderPage() {
    const { pickType, pageType, cityId, cityName, areaId, areaName } =
      this.state;
    const { isHideAutonomy } = this.props;
    return (
      <ViewPort>
        <LocationSelector
          promises={getLocationSelectorPromises()}
          pickType={pickType}
          pageType={pageType}
          cityId={cityId}
          cityName={cityName}
          areaId={areaId}
          areaName={areaName}
          filterPoi={AppContext?.UrlQuery?.filterPoi || AppContext?.filterPoi}
          isHideAutonomy={isHideAutonomy}
          onPageReady={this.onPageReady}
          handleAreaPress={this.handleAreaPress}
          handleCrossPress={this.handleCrossPress}
        />
      </ViewPort>
    );
  }
}
// 检测区域顶部忽略25%，底部忽略15%，因为有的城市可选区域数据少，页面显示内容占比少
function renderCheckInfo() {
  return { top: 0.25, bottom: 0.15 };
}

export default withForwardPage(Location, renderCheckInfo);
