import Image from '@c2x/components/Image';
import React, { memo, CSSProperties } from 'react';
import { XView as View, xMergeStyles, XViewExposure } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { flexRow } from '@ctrip/rn_com_car/dist/src/Tokens/tokens/layout';
import c2xStyles from './marketingFooterC2xStyles.module.scss';
import RelievedBooking from '../../../ComponentBusiness/RelievedBooking';
import { Utils, CarLog } from '../../../Util/Index';

const { uuid } = BbkUtils;

interface NewMarketingFooterTypes {
  bottomImg: string;
  titleImg?: string;
  lists?: {
    imgUrl: string;
    title: string;
    subTitle: string;
  }[];
  wrapStyle?: CSSProperties;
  isBookIsdGS3?: boolean;
}
const NewMarketingFooter: React.FC<NewMarketingFooterTypes> = memo(
  ({ titleImg, lists, bottomImg, wrapStyle, isBookIsdGS3 }) => (
    <XViewExposure
      className={c2xStyles.grayBg}
      style={wrapStyle}
      testID={
        Utils.isCtripIsd()
          ? CarLog.LogExposure({ name: '曝光_首页底部' })
          : undefined
      }
    >
      {Utils.isCtripOsd() && !!titleImg && (
        <View className={c2xStyles.indemnity}>
          <View className={c2xStyles.topTitle}>
            <Image
              className={c2xStyles.titleBg}
              src={titleImg}
              mode="aspectFit"
            />
          </View>
          <View style={xMergeStyles([layout.flexCenter, flexRow])}>
            {lists?.length > 0 &&
              lists.map(item => (
                <View className={c2xStyles.indeCon} key={uuid()}>
                  <View
                    style={xMergeStyles([layout.flexRow, layout.justifyCenter])}
                  >
                    <Image
                      className={c2xStyles.surePic}
                      src={item.imgUrl}
                      mode="aspectFit"
                    />

                    <View className={c2xStyles.indemText}>
                      <Text className={c2xStyles.indemTex}>{item.title}</Text>
                      <Text className={c2xStyles.indemDesc}>
                        {item.subTitle}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
          </View>
        </View>
      )}
      {/* 安心订放心行 */}
      <RelievedBooking style={xMergeStyles([wrapStyle])} />
      {!isBookIsdGS3 && (
        <View className={c2xStyles.active}>
          <Image
            className={c2xStyles.botLogo}
            src={bottomImg}
            mode="aspectFill"
          />
        </View>
      )}
    </XViewExposure>
  ),
);

export default NewMarketingFooter;
