import StyleSheet from '@c2x/apis/StyleSheet';
import Alert from '@c2x/apis/Alert';
import DeviceEventEmitter from '@c2x/apis/DeviceEventEmitter';
import Dimensions from '@c2x/apis/Dimensions';
import Util from '@c2x/apis/Util';
import Event from '@c2x/apis/Event';
import React, { PureComponent, memo, useCallback, CSSProperties } from 'react';
import {
  XView as View,
  xRouter,
  xShowToast,
  xMergeStyles,
  XViewExposure,
} from '@ctrip/xtaro';

import Button from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { color, font, tokenType } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import memoize from 'memoize-one';
import c2xStyles from './optionButtonsC2xStyles.module.scss';
import {
  ORDER_BUTTON,
  ReviewButton,
  ORDER_INTERVAL_TIME,
  DirectOpen,
  SelfServiceCode,
  SelfServicePageName,
} from '../../../Constants/OrderDetail';
import { TipPopType } from '../../../Constants/ListEnum';
import {
  CarLog,
  Utils,
  AppContext,
  EventHelper,
  GetAB,
} from '../../../Util/Index';

import { Url, Platform, EventName, UITestID } from '../../../Constants/Index';
import RerentModal from './RerentModal';
import Channel from '../../../Util/Channel';
import {
  OrderModalsVisible,
  CnButtonTypes,
  CnPayButtonTypes,
  IInvoiceButtonCode,
} from '../Types';
import ConfirmModal from '../../../ComponentBusiness/OrderConfirmModal/Index';
import {
  AdvanceReturnTipType,
  AllOperationsType,
  OrderOperation,
} from '../../../Types/Dto/OrderDetailRespaonseType';
import Texts from '../Texts';
import OrderRenewTipContainer from '../../../Containers/OrderRenewTipContainer';
import { OrderRenewTipClosedStorage } from '../../../State/ModifyOrder/ModifiedStorage';
import UITestId from '../../../Constants/UITestID';

const { width } = Dimensions.get('window');
const { getPixel, selector, isAndroid, ensureFunctionCall } = BbkUtils;
const styles = StyleSheet.create({
  cnPayButton: {
    borderRadius: getPixel(8),
    backgroundColor: color.transparent,
    marginRight: getPixel(16),
    alignItems: 'center',
    justifyContent: 'center',
    width: getPixel(152),
    marginBottom: getPixel(16),
    height: getPixel(62),
  },
  cnbutton: {
    borderWidth: StyleSheet.hairlineWidth,
    borderStyle: 'solid',
    borderColor: color.blueDeepBase,
    borderRadius: getPixel(8),
    backgroundColor: color.transparent,
    paddingTop: getPixel(12),
    paddingBottom: getPixel(12),
    marginRight: getPixel(15),
    alignItems: 'center',
    justifyContent: 'center',
    width: getPixel(152),
    marginBottom: getPixel(16),
  },
  cnbuttonNew: {
    borderWidth: StyleSheet.hairlineWidth,
    borderStyle: 'solid',
    borderColor: color.deepBlueBase,
    borderRadius: getPixel(8),
    backgroundColor: color.transparent,
    paddingTop: getPixel(12),
    paddingBottom: getPixel(12),
    marginRight: getPixel(15),
    alignItems: 'center',
    justifyContent: 'center',
    width: getPixel(152),
    marginBottom: getPixel(16),
  },
  cnbuttonDisable: {
    backgroundColor: color.grayBorder,
    borderColor: color.grayBorder,
  },
  buttonTxtDisable: {
    color: color.grayBase,
  },
  buttonTxt: {
    color: color.blueDeepBase,
    ...font.rcFont,
  },
  wrap: {
    justifyContent: 'flex-end',
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: width - getPixel(40),
    paddingRight: getPixel(Utils.isCtripIsd() ? 2 : 13),
    left: getPixel(Utils.isCtripIsd() ? 0 : -28),
  },
  wrapNotFirstLine: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
    flexWrap: 'wrap',
    width,
    paddingRight: getPixel(16),
  },
  selfServiceTipText: {
    marginTop: getPixel(isAndroid ? 0 : 2),
    ...font.buttonSideLabel,
    color: color.white,
  },
});

interface OptionProps {
  operationButtons: AllOperationsType[];
  orderBaseInfo?: any;
  continuePayInfo?: any;
  orderId?: number;
  pickupStore?: any;
  returnStore?: any;
  cancelRuleInfo?: any;
  modifyCancelRule?: any;
  fetchOrder: (data?: any) => void;
  setIsdOrderChangeModalVisible: (data?: any) => void;
  fetchQueryCancelFee: (data?: any) => void;
  setLocationInfo: (data: any) => void;
  setDateInfo: (data: any) => void;
  setLatestDateInfo: (data: any) => void;
  goHomeParams?: any;
  goNewHomeParams?: any;
  orderRenewalEntry?: any;
  ctripContinuePay: (data?: any) => void;
  orderParamFromOrder?: any;
  setModifyOrderWarnModalVisible: (visible: boolean, content?: any) => void;
  setOrderModalsVisible?: (data: OrderModalsVisible) => void;
  modalsVisible?: OrderModalsVisible;
  payCountDownTimeOutFlag: boolean;
  isOrderDataByPhone: boolean;
  cancelModifyOrder?: () => void;
  setTipPopData?: (data) => void;
  directOpen?: DirectOpen | string;
  isEhi?: boolean;
  cnBtnWarpStyle?: CSSProperties;
  cnBtnFirstWrapStyle?: CSSProperties;
  cnButtonStyle?: CSSProperties;
  cnButtonTextStyle?: CSSProperties;
  payButtonType?: tokenType.ButtonType;
  needRenewTip?: boolean; // 是否需要展示续租气泡提醒
  supportModifyOrder?: boolean;
  extendedInfo?: any;
  isKlbVersion?: boolean;
  isQueryOrderLoading?: boolean;
  isFulfillmentOSD?: boolean;
  isNewOsdModifyOrder?: boolean;
  osdModifyOrderInit: () => void;
  setLocationAndDatePopIsShow: (data) => void;
}

interface ICnButtonContainer {
  children: React.ReactNode;
  testID?: string;
}

const CnButtonContainer: React.FC<ICnButtonContainer> = memo(
  ({ testID, children }: ICnButtonContainer) => {
    return !testID ? (
      <>{children}</>
    ) : (
      <XViewExposure testID={testID}>{children}</XViewExposure>
    );
  },
);

const CnButton: React.FC<CnButtonTypes> = memo(
  ({
    text,
    onPress,
    disabled,
    isGray,
    id,
    testID,
    children,
    buttonStyle,
    textStyle,
    isLoading,
  }: CnButtonTypes) => {
    const itemOnPress = useCallback(() => onPress(id), [onPress, id]);
    const isISDInterestPoints = GetAB.isISDInterestPoints();
    return (
      <CnButtonContainer testID={testID}>
        <Button
          text={text}
          style={xMergeStyles([
            isISDInterestPoints ? styles.cnbuttonNew : styles.cnbutton,
            buttonStyle,
            isGray && styles.cnbuttonDisable,
          ])}
          textStyle={xMergeStyles([
            styles.buttonTxt,
            textStyle,
            isGray && styles.buttonTxtDisable,
            text?.length > 5 && font.caption1LightStyle,
          ])}
          onPress={itemOnPress}
          disabled={disabled}
          debounce={true}
          testID={`${UITestId.car_testid_page_order_op_btn}_${text}`}
          isLoading={isLoading}
        />

        {children}
      </CnButtonContainer>
    );
  },
);

const CnPayButton: React.FC<CnPayButtonTypes> = memo(
  ({
    text,
    onPress,
    id,
    testID,
    buttonStyle,
    textStyle,
    buttonType = 'gradient',
  }: CnPayButtonTypes) => {
    const itemOnPress = useCallback(() => onPress(id), [onPress, id]);
    const Container = !testID ? View : XViewExposure;
    return (
      <Container testID={testID}>
        <Button
          text={text}
          buttonStyle={xMergeStyles([styles.cnPayButton, buttonStyle])}
          textStyle={xMergeStyles([
            styles.buttonTxt,
            {
              color: color.white,
            },
            textStyle,
          ])}
          buttonSize="S"
          buttonType={buttonType}
          onPress={itemOnPress}
          debounce={true}
          testID={UITestId.car_testid_page_order_continue_pay}
          gradientColorArr={
            Utils.isCtripOsd()
              ? [color.blueBase, color.blueDeepBase]
              : undefined
          }
        />
      </Container>
    );
  },
);

const getDefaultTestId = (id, isFulfillmentOSD) => {
  let testId = '';
  switch (id) {
    case ORDER_BUTTON.BookAgain:
      testId = CarLog.LogExposure({
        name: isFulfillmentOSD
          ? '曝光_订单详情页_再次预订'
          : '曝光_订单详情页_自主操作栏_再次预订',
      });
      break;
    case ORDER_BUTTON.PrintInvoice:
      testId = CarLog.LogExposure({ name: '曝光_订单详情页_报销凭证' });
      break;
    case ORDER_BUTTON.Invoice:
      testId = CarLog.LogExposure({ name: '曝光_订单详情页_发票明细' });
      break;
    default:
      testId = '';
  }
  return testId;
};

interface IConfirmModal {
  visible: boolean;
  title?: string;
  desc?: string;
}
export default class OptionButtons extends PureComponent<
  OptionProps & any,
  {
    rerentModalVisible: boolean;
    confirmModal: IConfirmModal;
    showRenewTip: boolean;
    isSelfServiceLoading: boolean;
  }
> {
  hasTip = false;

  renewTipContent = '';

  didShowRenewTip = false;

  constructor(props) {
    super(props);
    this.state = {
      rerentModalVisible: false,
      confirmModal: {
        visible: false,
      },
      showRenewTip: false,
      isSelfServiceLoading: false,
    };
  }

  componentDidMount() {
    DeviceEventEmitter.addListener(EventName.orderToRebookNotice, value => {
      if (value === 'rebook') {
        this.toRebook();
      } else if (value === 'cancel') {
        this.toCancel();
      }
    });
    this.getShowRenewTip();
    this.onDirectOpen();
  }

  componentDidUpdate(prevProps) {
    const { isQueryOrderLoading } = this.props;
    const { isSelfServiceLoading } = this.state;
    if (
      isSelfServiceLoading &&
      prevProps.isQueryOrderLoading &&
      !isQueryOrderLoading
    ) {
      this.setState({ isSelfServiceLoading: false });
      if (
        AppContext?.PageInstance?.getPageId() === Channel.getPageId().Order.ID
      ) {
        this.handlePressSelfService();
      }
    }
  }

  componentWillUnmount() {
    const { isSelfServiceLoading } = this.state;
    if (isSelfServiceLoading) {
      this.setState({ isSelfServiceLoading: false });
    }
    Event.removeEventListener(EventName.orderToRebookNotice);
  }

  onDirectOpen = () => {
    const { directOpen, orderBaseInfo } = this.props;
    const directOpenId = Number(directOpen);

    const { allOperations = [] } = orderBaseInfo;
    let operaterBtn = null;
    switch (directOpenId) {
      case DirectOpen.ToPay:
        operaterBtn = allOperations.find(
          item => item.operationId === ORDER_BUTTON.PayNow,
        );
        break;
      case DirectOpen.CancelBooking:
        operaterBtn = allOperations.find(
          item => item.operationId === ORDER_BUTTON.CancelBooking,
        );
        break;
      case DirectOpen.ModifyOrder:
        operaterBtn = allOperations.find(
          item => item.operationId === ORDER_BUTTON.ModifyOrder,
        );
        break;
      case DirectOpen.Renewal:
        operaterBtn = allOperations.find(
          item => item.operationId === ORDER_BUTTON.Renew,
        );
        break;
      case DirectOpen.AdvanceReturnApply:
        operaterBtn = allOperations.find(
          item => item.operationId === ORDER_BUTTON.AdvanceReturn,
        );
        break;
      default:
        break;
    }
    if (operaterBtn?.enable) {
      this.onBtnPress(operaterBtn.operationId);
    }
  };

  getShowRenewTip = async () => {
    const { orderId, needRenewTip = true } = this.props;
    const isClosed = await OrderRenewTipClosedStorage.loadOne(orderId);
    this.setState({
      showRenewTip: !isClosed && needRenewTip,
    });
  };

  getOptionBtnExposureData = memoize(
    (id: number, isFulfillmentOSD: boolean) => {
      let enName = '';
      switch (id) {
        case ORDER_BUTTON.PayNow:
          enName = isFulfillmentOSD
            ? '曝光_订单详情页_去支付'
            : '曝光_订单详情页_自主操作栏_继续支付';

          break;
        case ORDER_BUTTON.CancelBooking:
          enName = isFulfillmentOSD
            ? '曝光_订单详情页_取消订单'
            : '曝光_订单详情页_自主操作栏_取消订单';

          break;
        default:
          break;
      }
      if (!enName) return '';
      return CarLog.LogExposure({
        name: enName,
      });
    },
  );

  getCNButtons = memoize(
    (
      operationButtons,
      orderBaseInfo,
      payCountDownTimeOutFlag,
      orderRenewalEntry = {},
      isSelfServiceLoading,
    ) => {
      // 按钮更新时，storage 可能发生变化，需要重新获取
      this.getShowRenewTip();
      const {
        orderId,
        cnButtonStyle,
        cnButtonTextStyle,
        payButtonType,
        isFulfillmentOSD,
      } = this.props;
      const { showRenewTip } = this.state;

      const dom = operationButtons?.map(item => {
        const id = item.operationId;
        const btnUnable = !item.enable;
        switch (id) {
          case ORDER_BUTTON.PayNow:
            // 如果倒计时结束，隐藏继续支付按钮
            if (payCountDownTimeOutFlag) return null;
            return (
              <CnPayButton
                key={`oporateBtn_${id}`}
                text={item.buttonName}
                onPress={this.onBtnPress}
                buttonStyle={cnButtonStyle}
                buttonType={payButtonType}
                textStyle={cnButtonTextStyle}
                id={id}
                testID={this.getOptionBtnExposureData(id, isFulfillmentOSD)}
              />
            );

          case ORDER_BUTTON.CancelBooking:
            return (
              <CnButton
                key={`oporateBtn_${id}`}
                text={item.buttonName}
                buttonStyle={cnButtonStyle}
                textStyle={cnButtonTextStyle}
                isGray={!item.enable}
                disabled={!item.enable}
                onPress={() => {
                  this.goToCancelPage(item, id);
                }}
                id={id}
                testID={this.getOptionBtnExposureData(id, isFulfillmentOSD)}
              />
            );

          case ORDER_BUTTON.ModifyOrder:
            return (
              <XViewExposure
                key={`oporateBtn_${id}`}
                testID={CarLog.LogExposure({
                  name: '曝光_订单详情页_修改订单',

                  status: item.enable ? 1 : 2, // 1 有效露出，2 有效置灰
                  orderStatus: orderBaseInfo?.orderStatusDesc || '',
                  orderStatusCode: orderBaseInfo?.orderStatus || '',
                  orderId,
                })}
              >
                <CnButton
                  text={item.buttonName}
                  buttonStyle={cnButtonStyle}
                  textStyle={cnButtonTextStyle}
                  // 出境修改订单按钮置灰逻辑
                  isGray={
                    Utils.isCtripOsd() ? item?.display === 'gray' : btnUnable
                  }
                  disabled={
                    Utils.isCtripOsd() ? item?.display === 'gray' : btnUnable
                  } // 等价格接口回调
                  onPress={this.modifyOrderFn}
                  id={id}
                />
              </XViewExposure>
            );

          case ORDER_BUTTON.CancelModify:
            return (
              <CnButton
                key={`oporateBtn_${id}`}
                text={item.buttonName}
                buttonStyle={cnButtonStyle}
                textStyle={cnButtonTextStyle}
                isGray={btnUnable}
                disabled={btnUnable} // 等价格接口回调
                onPress={this.onBtnPress}
                id={id}
              />
            );

          case ORDER_BUTTON.Renew:
            this.hasTip = !!item?.label && showRenewTip;
            this.renewTipContent = item?.label;
            /* eslint-disable no-case-declarations */
            // 国内续租
            return (
              <CnButton
                key={`oporateBtn_${id}`}
                text={item.buttonName}
                buttonStyle={cnButtonStyle}
                textStyle={cnButtonTextStyle}
                isGray={!item.enable}
                onPress={() => {
                  if (!item.enable) {
                    if (item.contents) {
                      this.showConfirmModal({
                        visible: true,
                        title: item?.contents[0]?.description,
                      });
                    }
                  } else {
                    this.onBtnPress(id);
                  }
                }}
                id={id}
                testID={CarLog.LogExposure({ name: '自助操作区_续租' })}
              >
                {this.hasTip && !this.didShowRenewTip && (
                  <OrderRenewTipContainer
                    orderId={orderId}
                    orderStatus={orderBaseInfo?.orderStatusDesc}
                  />
                )}
              </CnButton>
            );

          case ORDER_BUTTON.AdvanceReturn:
            return (
              <CnButton
                key={`oporateBtn_${id}`}
                text={item.buttonName}
                buttonStyle={cnButtonStyle}
                textStyle={cnButtonTextStyle}
                isGray={!item.enable}
                onPress={() => this.onClickAdvanceReturnButton(item)}
                testID={CarLog.LogExposure({
                  name: '曝光_订单详情页_提前还车',
                })}
                id={id}
              />
            );

          case ORDER_BUTTON.Comments:
            return (
              <CnButton
                key={`oporateBtn_${id}`}
                text={item.buttonName}
                buttonStyle={cnButtonStyle}
                textStyle={cnButtonTextStyle}
                isGray={!item.enable}
                onPress={() => this.onBtnPress(id, item?.code, item?.url)}
                id={id}
                testID={CarLog.LogExposure({
                  name: '曝光_订单详情页_自主操作栏_点评',

                  info: {
                    commentType: item.code,
                  },
                })}
              >
                {!!item.label && (
                  <View className={c2xStyles.returnTipWrap}>
                    <BbkText className={c2xStyles.returnTipText}>
                      {item.label}
                    </BbkText>
                  </View>
                )}
              </CnButton>
            );

          case ORDER_BUTTON.SelfService:
            return (
              <CnButton
                key={`oporateBtn_${id}`}
                text={item.buttonName}
                buttonStyle={cnButtonStyle}
                textStyle={cnButtonTextStyle}
                isGray={!item.enable}
                onPress={() => this.onBtnPress(id, item?.code, item?.url)}
                id={id}
                isLoading={isSelfServiceLoading}
              >
                {!!item.label && (
                  <Touchable
                    onPress={() => this.onBtnPress(id, item?.code, item?.url)}
                    testID={UITestID.car_testid_page_order_selfservice_button}
                    disabled={isSelfServiceLoading}
                    debounce={true}
                    activeOpacity={1}
                    className={c2xStyles.selfServiceTipWrap}
                  >
                    <BbkText style={styles.selfServiceTipText}>
                      {item.label}
                    </BbkText>
                  </Touchable>
                )}
              </CnButton>
            );

          default:
            return (
              <CnButton
                key={`oporateBtn_${id}`}
                text={item.buttonName}
                buttonStyle={cnButtonStyle}
                textStyle={cnButtonTextStyle}
                onPress={() => this.onBtnPress(id, item?.code, item?.url)}
                id={id}
                testID={getDefaultTestId(id, isFulfillmentOSD)}
              />
            );
        }
      });
      // 海外续租
      if (orderRenewalEntry?.renewalButton?.actionUrl) {
        dom.push(
          <CnButton
            key={`orderFees_btn_${7}`}
            text="续租"
            onPress={this.rerentOrder}
            id={7}
          />,
        );
      }

      const buttons = dom.filter(item => item !== null);
      this.setRenewTip(buttons, this.hasTip, this.renewTipContent, orderId);

      return buttons;
    },
  );

  goToCancelPage = (item, id) => {
    const {
      fetchQueryCancelFee,
      isKlbVersion,
      setOrderModalsVisible,
      orderBaseInfo,
      fetchOrder,
      isFulfillmentOSD,
    } = this.props;
    const { freeCancelTime } = orderBaseInfo || {};
    // 存在取消截止时间 且 已过期
    if (
      Utils.isCtripIsd() &&
      !!freeCancelTime &&
      dayjs(freeCancelTime)?.diff(dayjs(), 'second') <= 0 &&
      item?.contents?.[0]?.description
    ) {
      setOrderModalsVisible({
        confirmModal: {
          visible: true,
          data: {
            contentText: item?.contents?.[0]?.description,
            btns: [
              {
                name: Texts.gotIt, // 知道了
                isPrimary: true,
                onPress: () => {
                  // 关闭弹窗
                  setOrderModalsVisible({ confirmModal: { visible: false } });
                  // 刷新订详
                  fetchOrder();
                },
              },
            ],
          },
        },
      });
      return;
    }
    if (isFulfillmentOSD) {
      CarLog.LogCode({ name: '点击_订单详情页_取消订单' });
    }
    if (
      GetAB.isOSDNewOrderCancel(isKlbVersion) &&
      item?.contents?.[0]?.description
    ) {
      this.showCancelConfirmModal({
        title: Texts.warmTips,
        cancelBtnName: Texts.thinkTwice,
        submitBtnName: Texts.continueToCancel,
        submitFn: fetchQueryCancelFee,
        contentText: item?.contents?.[0]?.description,
        exposeTestID: CarLog.LogExposure({
          name: '曝光_订单详情页_取消挽留弹窗',
        }),
      });
    } else {
      this.onBtnPress(id);
    }
  };

  setRenewTip = (buttons, hasTip, renewTipContent, orderId) => {
    const { length } = buttons;
    const renewButtonIndex = buttons.findIndex(
      button => button?.props?.id === ORDER_BUTTON.Renew,
    );
    const { setTipPopData } = this.props;
    if (renewButtonIndex > -1 && hasTip) {
      const alignLeft = renewButtonIndex % 4 < 2 && length > 2;
      const diffWidth = isAndroid ? -40 : 0;
      setTipPopData({
        visible: true,
        data: {
          style: {
            bottom: Utils.isCtripIsd() ? getPixel(-78) : getPixel(78),
            // 按钮固定宽度，按照每行4个计算，
            // 排在前两位左对齐，排在后两位右对齐
            // 数量小于3时，flex-end，固定右对齐
            [alignLeft ? 'left' : 'right']: alignLeft ? 0 : getPixel(16),
            minWidth: getPixel(
              renewTipContent.length > 15 ? 486 + diffWidth : 407 + diffWidth,
            ),
          },
          content: renewTipContent,
          type: TipPopType.OrderRenew,
          hideClose: isAndroid,
          orderRenewTipArrowStyle: Utils.isCtripIsd() && {
            width: getPixel(36),
            height: getPixel(16),
            position: 'absolute',
            top: -getPixel(15.5),
            transform: [
              {
                rotate: '180deg',
              },
            ],
          },
        },
      });
      if (isAndroid) {
        setTimeout(() => {
          setTipPopData({
            visible: false,
          });
          OrderRenewTipClosedStorage.save(orderId);
          this.didShowRenewTip = true;
        }, ORDER_INTERVAL_TIME);
      }
    }
  };

  rerentOrder = () => {
    // 海外续租
    const { orderRenewalEntry = {} } = this.props;

    const { renewalButton } = orderRenewalEntry;
    let key = '';

    if (renewalButton && renewalButton.actionUrl === 'renewalEntry') {
      // 提前24h+续租
      this.popGoRerent();
      key = '点击_订单详情页_续租按钮_还车前超出24小时';
    } else {
      // 显示弹层
      this.setState({
        rerentModalVisible: true,
      });
      key = '点击_订单详情页_续租按钮_还车前24小时内';
    }
    CarLog.LogCode({
      name: key,
    });
  };

  popGoRerent = () => {
    const { pickupStore, orderRenewalEntry, returnStore, orderId, fetchOrder } =
      this.props;
    this.setState({
      rerentModalVisible: false,
    });
    const { renewalButton } = orderRenewalEntry;
    const url = `${Url.OSD_CRN_URL}&landingto=rerent&orderId=${orderId}&
    actionUrl=${renewalButton.actionUrl}
    &storeTelephone=${pickupStore.storeTel}
    &originalReturnDateLocal=${dayjs(returnStore.localDateTime).format(
      'YYYYMMDDHHmmss',
    )}`;
    xRouter.navigateTo({ url, success: () => fetchOrder() });
  };

  toCancel = () => {
    const { fetchQueryCancelFee } = this.props;
    fetchQueryCancelFee();
  };

  isNewHome = () => true;

  toRebook = () => {
    const reBookUrl = {
      isd: `${Url.ISD_CRN_URL}&initialPage=isdhome`,
      trip: Url.tripUrl,
      osd: Url.OSD_CRN_URL,
    };
    let url;
    if (this.isNewHome()) {
      const {
        goNewHomeParams,
        setLocationInfo,
        setDateInfo,
        setLatestDateInfo,
      } = this.props;

      setLocationInfo(goNewHomeParams.rentalLocation);
      setDateInfo(goNewHomeParams.rentalDate);
      setLatestDateInfo({
        time: dayjs(goNewHomeParams.rentalDate.pickup),
        showToast: false,
        callback: rentalLocationAndDate => {
          const data = { ...rentalLocationAndDate };
          const baseUrl = Utils.isCtripIsd()
            ? Platform.CAR_CROSS_URL.CTQHOME.ISD
            : Platform.CAR_CROSS_URL.CTQHOME.OSD;
          url = `${baseUrl}&data=${encodeURIComponent(JSON.stringify(data))}`;
          xRouter.navigateTo({ url });
        },
      });
    } else {
      url = reBookUrl.trip;
    }

    xRouter.navigateTo({ url });
  };

  showReviewModal = () => {
    const { orderId } = this.props;
    if (Utils.isCtripOsd()) {
      this.goOsdComment(orderId);
    } else {
      this.goIsdComment(orderId);
    }
  };

  goOsdComment = orderId => {
    const param = {
      oid: orderId,
      isHideNavBar: 'YES',
      isFromCRNWebDetail: false,
      clienttype: Utils.getClientType(),
    };
    const url = `${Utils.getHost()}/webapp/cars/osd/osd/osdcomment?${Utils.toParams(
      param,
    )}`;
    CarLog.LogCode({
      name: '点击_订单详情页_按钮_点评',

      from: 'osd-orderdetail',
    });
    xRouter.navigateTo({ url });
  };

  goIsdComment = orderId => {
    const osdUrl = `${Url.ISD_CRN_URL}`;
    const paramStr = encodeURIComponent(
      Util.base64Encode(JSON.stringify({ orderid: orderId })),
    );
    const url = `${osdUrl}&initialPage=isdcomment&sparam=${paramStr}`; // &referrer=isdorderdetail
    CarLog.LogCode({
      name: '点击_订单详情页_按钮_点评',

      from: 'isd-orderdetail',
    });
    xRouter.navigateTo({ url });
  };

  getCreateInsLoadingIsShow = () => {
    const { modalsVisible = {} } = this.props;
    return modalsVisible.createInsModalVisible.visible;
  };

  gotoPayment = () => {
    const { orderBaseInfo, continuePayInfo, ctripContinuePay } = this.props;
    ctripContinuePay();
    CarLog.LogCode({
      name: '点击_订详_继续支付',

      info: { orderBaseInfo, continuePayInfo },
    });
  };

  modifyOrder = () => {
    if (Utils.isCtripIsd()) {
      const {
        setModifyOrderWarnModalVisible,
        orderBaseInfo,
        modifyCancelRule,
        isEhi,
        supportModifyOrder,
      } = this.props;
      if (!supportModifyOrder) {
        const content = {
          header: Texts.modifyOrderWarning,
          title: Texts.modifyOrderWarningSub,
          isWarnTip: modifyCancelRule?.color !== 1,
          desc: modifyCancelRule?.contentAlert,
          highLight: modifyCancelRule?.highLight,
          isInfoFromOrder: true,
        };
        setModifyOrderWarnModalVisible(true, content);
        return;
      }
      AppContext.PageInstance.push(Channel.getPageId().ModifyOrder.EN, {
        orderId: orderBaseInfo.orderId,
        isEhi,
      });
      return;
    }
    // 出境修改订单
    const {
      isNewOsdModifyOrder,
      osdModifyOrderInit,
      setLocationAndDatePopIsShow,
    } = this.props;
    if (isNewOsdModifyOrder) {
      osdModifyOrderInit();
      setLocationAndDatePopIsShow({ visible: true });
    } else {
      AppContext.PageInstance.push(Channel.getPageId().OrderChange.EN, {
        visible: true,
      });
    }
  };

  showCancelConfirmModal = ({
    title,
    contentText,
    cancelBtnName,
    submitBtnName,
    submitFn,
    exposeTestID,
  }: {
    title: string;
    contentText?: string;
    cancelBtnName: string;
    submitBtnName: string;
    submitFn: () => void;
    exposeTestID?: string;
  }) => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      confirmModal: {
        visible: true,
        data: {
          title,
          contentText: contentText || '',
          exposeTestID,
          btns: [
            {
              name: cancelBtnName,
              onPress: () => {
                setOrderModalsVisible({ confirmModal: { visible: false } });
                CarLog.LogCode({
                  name: '点击_订单详情页_取消挽留弹窗我再想想',
                });
              },
            },
            {
              name: submitBtnName,
              isPrimary: true,
              onPress: () => {
                setOrderModalsVisible({ confirmModal: { visible: false } });
                ensureFunctionCall(submitFn);
                CarLog.LogCode({
                  name: '点击_订单详情页_取消挽留弹窗继续取消',
                });
              },
            },
          ],
        },
      },
    });
  };

  // 提前还车按钮提示弹层
  onClickAdvanceReturnButton = (item: OrderOperation) => {
    CarLog.LogCode({ name: '点击_订单详情页_提前还车' });
    if (item?.contents?.length > 0) {
      const { setOrderModalsVisible } = this.props;

      const defaultBtns = [
        {
          name: Texts.iKnown,
          isPrimary: true,
          onPress: () =>
            setOrderModalsVisible({ confirmModal: { visible: false } }),
        },
      ];

      const advanceTempNotApplyBtn = {
        name: Texts.advanceTempNotApply,
        onPress: () =>
          setOrderModalsVisible({ confirmModal: { visible: false } }),
      };

      const buttonsMap = {
        [AdvanceReturnTipType.hasApply]: [
          advanceTempNotApplyBtn,
          {
            name: Texts.retryApply,
            isPrimary: true,
            onPress: () => {
              setOrderModalsVisible({ confirmModal: { visible: false } });
              this.onBtnPress(item?.operationId);
            },
          },
        ],

        [AdvanceReturnTipType.hasWaitPayRenew]: [
          advanceTempNotApplyBtn,
          {
            name: Texts.applyAdvanceReturn,
            isPrimary: true,
            onPress: () => {
              setOrderModalsVisible({ confirmModal: { visible: false } });
              this.onBtnPress(item?.operationId);
            },
          },
        ],
      };

      const content = item?.contents?.[0];
      setOrderModalsVisible({
        confirmModal: {
          visible: true,
          data: {
            title: content?.description,
            btns: buttonsMap[content?.type] || defaultBtns,
          },
        },
      });
    } else {
      this.onBtnPress(item?.operationId);
    }
  };

  showReviewUnopenedModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ reviewUnopenedModal: { visible: true } });
  };

  // 点击自助取还
  onPressSelfService = () => {
    const { isQueryOrderLoading } = this.props;
    const { isSelfServiceLoading } = this.state;
    if (isQueryOrderLoading) {
      this.setState({ isSelfServiceLoading: true });
    } else {
      if (isSelfServiceLoading) {
        this.setState({ isSelfServiceLoading: false });
      }
      this.handlePressSelfService();
    }
  };

  // 处理自助取还跳转
  handlePressSelfService = () => {
    const { orderBaseInfo } = this.props;
    const { allOperations = [] } = orderBaseInfo || {};
    const { code, contents } =
      allOperations?.find(
        item => item?.operationId === ORDER_BUTTON.SelfService,
      ) || {};
    const { description } = contents?.[0] || {};
    this.selfServiceRouter(code, description);
  };

  // 自助取还跳转规则
  selfServiceRouter = (code: number, description?: string) => {
    let pageName = '';
    switch (code) {
      case SelfServiceCode.TO_AUTH_PAGE:
        pageName = SelfServicePageName.AUTH_PAGE;
        break;
      case SelfServiceCode.TO_LOOK_FOR_CAR_PAGE:
        pageName = SelfServicePageName.LOOK_FOR_CAR_PAGE;
        break;
      case SelfServiceCode.TO_INSPECT_CAR_PAGE:
        pageName = SelfServicePageName.INSPECT_CAR_PAGE;
        break;
      case SelfServiceCode.TO_CONTROL_CAR_PAGE:
        pageName = SelfServicePageName.CONTROL_CAR_PAGE;
        break;
      case SelfServiceCode.TO_DROP_OFF_COMPLETE_PAGE:
        pageName = SelfServicePageName.DROP_OFF_COMPLETE_PAGE;
        break;
      default:
        pageName = '';
        break;
    }
    if (pageName) {
      const { orderId } = this.props;
      const url = `${Url.rNXTaroCarOrderBaseUrl}&initialPage=${pageName}&orderId=${orderId}`;
      xRouter.navigateTo({ url });
    } else if (code === SelfServiceCode.SHOW_TOAST && description) {
      xShowToast({ title: description, duration: 3000 });
    }
  };

  // 点击发票按钮
  onPressInvoice = code => {
    const { orderId } = this.props;
    let url = '';
    if (code === IInvoiceButtonCode.EDIT) {
      url = `${Url.rNXTaroCarOrderBaseUrl}&initialPage=editInvoice&orderId=${orderId}`;
    } else if (code === IInvoiceButtonCode.LOOK) {
      url = `${Url.rNXTaroCarOrderBaseUrl}&initialPage=invoiceDetail&orderId=${orderId}`;
    }
    if (url) {
      CarLog.LogCode({ name: '点击_订单详情页_发票明细' });
      xRouter.navigateTo({ url });
    }
  };

  // 点击Button的处理函数
  onBtnPress = (id: number | string, code?: ReviewButton, link?: string) => {
    const {
      orderBaseInfo,
      isOrderDataByPhone,
      extendedInfo,
      cancelModifyOrder,
      isFulfillmentOSD,
    } = this.props;
    if (
      isOrderDataByPhone &&
      [
        ORDER_BUTTON.PayNow,
        ORDER_BUTTON.CancelBooking,
        ORDER_BUTTON.ModifyOrder,
        ORDER_BUTTON.Renew,
        ORDER_BUTTON.Comments,
        ORDER_BUTTON.Invoice,
      ].includes(Number(id))
    ) {
      EventHelper.sendEvent(EventName.orderBtnsClickFromPhoneCheck, {});
      return;
    }
    const { orderId, newOrderId } = orderBaseInfo;

    const { fetchOrder } = this.props;
    let invoiceUrl;
    switch (id) {
      case ORDER_BUTTON.PrintVoucher:
        const isLicenseApprove = extendedInfo?.osdDetailVersion === 'B';
        CarLog.LogCode({ name: '点击_订单详情页_按钮_提车凭证' });
        const pvUrl = isLicenseApprove
          ? link
          : `${Url.OSD_CRN_URL}&initialPage=OsdRentalCarMaterial&orderid=${orderId}`;
        xRouter.navigateTo({ url: pvUrl });
        break;
      case ORDER_BUTTON.PrintInvoice:
        CarLog.LogCode({ name: '点击_订单详情页_按钮_电子发票' });
        invoiceUrl = `${Utils.getUrlHost()}/webapp/carhire/xsd/osdinvoice?id=${orderId}`;
        xRouter.navigateTo({ url: link || invoiceUrl });
        break;
      case ORDER_BUTTON.CancelBooking:
        CarLog.LogCode({
          name: isFulfillmentOSD
            ? '点击_订单详情页_取消订单'
            : '点击_订单详情页_按钮_取消订单',
        });
        this.toCancel();
        break;
      case ORDER_BUTTON.PayNow:
        CarLog.LogCode({
          name: isFulfillmentOSD
            ? '点击_订单详情页_去支付'
            : '点击_订单详情页_按钮_继续支付',
        });
        this.gotoPayment();
        break;
      case ORDER_BUTTON.BookAgain:
        CarLog.LogCode({
          name: isFulfillmentOSD
            ? '点击_订单详情页_再次预订'
            : '点击_订单详情页_按钮_再次预订',
        });
        this.toRebook();
        break;
      case 'priceDetail':
        CarLog.LogCode({ name: '点击_订单详情页_按钮_费用明细' });
        break;
      case ORDER_BUTTON.Comments:
        CarLog.LogCode({
          name: '点击_订单详情页_按钮_点评',

          info: {
            commentType: code,
          },
        });
        if (code !== undefined) {
          if (code === ReviewButton.Unenable) {
            this.showReviewUnopenedModal();
            break;
          } else if (link) {
            xRouter.navigateTo({ url: link });
            break;
          }
        }
        this.showReviewModal();
        break;
      case ORDER_BUTTON.ModifyOrder:
        CarLog.LogCode({
          name: '点击_订单详情页_按钮_修改订单',

          status: 1,
          orderStatus: orderBaseInfo?.orderStatusDesc || '',
          orderStatusCode: orderBaseInfo?.orderStatus || '',
          orderId,
        });
        this.modifyOrder();
        break;
      case ORDER_BUTTON.ModifyDetail:
      case ORDER_BUTTON.ViewNewOrder:
      case ORDER_BUTTON.ViewOldOrder:
        fetchOrder({ orderId: newOrderId });
        break;
      case ORDER_BUTTON.Renew:
        CarLog.LogCode({ name: '点击_自助操作区_续租' });
        AppContext.PageInstance.push(Channel.getPageId().Rerent.EN, {
          visible: true,
        });
        break;
      case ORDER_BUTTON.AdvanceReturn:
        AppContext.PageInstance.push(Channel.getPageId().AdvanceReturn.EN);
        break;
      case ORDER_BUTTON.CancelModify:
        this.showCancelConfirmModal({
          title: Texts.modifyCancelTip,
          cancelBtnName: Texts.thinkTwice,
          submitBtnName: Texts.modifyCancel,
          submitFn: cancelModifyOrder,
        });
        break;
      case ORDER_BUTTON.SelfService:
        this.onPressSelfService();
        break;
      case ORDER_BUTTON.Invoice:
        this.onPressInvoice(code);
        break;
      // case 'changeTime':
      //   break;
      // case 'changeInfo':
      //   break;
      default:
        break;
    }
  };

  setLayerState = s => {
    const { setIsdOrderChangeModalVisible } = this.props;
    setIsdOrderChangeModalVisible({ visible: s });
  };

  goIsdminihome = () => {
    const { goHomeParams, orderParamFromOrder } = this.props;
    const query = { ...goHomeParams, order: orderParamFromOrder };
    const page = 'isdminihome';
    const params = encodeURIComponent(JSON.stringify(query));
    const { ISD_CRN_URL } = Url;
    const url = `${ISD_CRN_URL}&forceOld=1&initialPage=${page}&channelID=14440&sparam=${params}`;
    xRouter.navigateTo({ url });
  };

  alertMsg = msg => {
    Alert.alert(
      '',
      msg,
      [
        {
          get text() {
            return '知道了';
          },
          onPress: () => {},
        },
      ],

      { cancelable: false },
    );
  };

  mySetState = (name, value) => {
    const obj = {};
    obj[name] = value;
    this.setState(obj);
  };

  hideConfirmModal = () => {
    this.mySetState('confirmModal', {
      visible: false,
    });
  };

  showConfirmModal = (config: IConfirmModal) => {
    this.mySetState('confirmModal', config);
  };

  hideRerentModal = () => {
    this.mySetState('rerentModalVisible', false);
  };

  modifyOrderFn = () => {
    this.onBtnPress(ORDER_BUTTON.ModifyOrder);
  };

  render() {
    const { rerentModalVisible, confirmModal, isSelfServiceLoading } =
      this.state;
    const {
      pickupStore,
      orderRenewalEntry = {},
      orderBaseInfo = {},
      payCountDownTimeOutFlag,
      cnBtnFirstWrapStyle,
      cnBtnWarpStyle,
      operationButtons,
    } = this.props;
    const cnBtns = this.getCNButtons(
      operationButtons,
      orderBaseInfo,
      payCountDownTimeOutFlag,
      orderRenewalEntry,
      isSelfServiceLoading,
    );
    const firstLine = cnBtns?.slice(0, 4);
    const restLine = cnBtns?.slice(4);
    return (
      <>
        <View
          testID={UITestID.car_testid_comp_orderDetail_optionButtons}
          className={c2xStyles.cnBtnsWarp}
          style={cnBtnWarpStyle}
        >
          <View style={xMergeStyles([styles.wrap, cnBtnFirstWrapStyle])}>
            {firstLine}
          </View>
          {selector(
            restLine.length > 0,
            <View style={styles.wrapNotFirstLine}>{restLine}</View>,
          )}
        </View>
        {rerentModalVisible && (
          <RerentModal
            onRequestClose={this.hideRerentModal}
            pickupStore={pickupStore}
            orderRenewalEntry={orderRenewalEntry}
            popGoRerent={this.popGoRerent}
            modalVisible={rerentModalVisible}
          />
        )}
        {confirmModal?.visible && (
          <ConfirmModal
            visible={confirmModal?.visible}
            title={confirmModal?.title}
            contentText={confirmModal?.desc}
            btns={[
              {
                get name() {
                  return '知道了';
                },
                isPrimary: true,
                onPress: this.hideConfirmModal,
              },
            ]}
          />
        )}
      </>
    );
  }
}
