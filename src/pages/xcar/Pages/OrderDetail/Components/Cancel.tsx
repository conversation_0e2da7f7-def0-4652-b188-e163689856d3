/* eslint-disable max-classes-per-file */
import { isEmpty as lodashIsEmpty } from 'lodash-es';
/* eslint-disable max-classes-per-file */
import StyleSheet from '@c2x/apis/StyleSheet';
import TextInput from '@c2x/components/TextInput';
import Keyboard from '@c2x/apis/Keyboard';
import Dimensions from '@c2x/apis/Dimensions';
import Image from '@c2x/components/Image';
import DeviceEventEmitter from '@c2x/apis/DeviceEventEmitter';
import { IBasePageProps } from '@c2x/components/Page';
import React, {
  PureComponent,
  memo,
  useState,
  ReactNode,
  ReactNodeArray,
  useMemo,
  CSSProperties,
} from 'react';
import {
  xMergeStyles,
  xClassNames as classNames,
  XView as View,
  XImageBackground as ImageBackground,
  XLinearGradient as LinearGradient,
  xRouter,
  XBoxShadow,
  XViewExposure,
  XAnimated,
  xCreateAnimation,
} from '@ctrip/xtaro';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import Button from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkInput from '@ctrip/rn_com_car/dist/src/Components/Basic/Input';
import BbkToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { InputFormatType } from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import {
  color,
  font,
  space,
  layout,
  icon,
  setOpacity,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import CText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { KeyboardAwareScrollView } from '@c2x/extraPackages/react-native-keyboard-aware-scroll-view';
import CTouchableOpacity from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';

import {
  useMemoizedFn,
  uuid,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './cancelC2xStyles.module.scss';
import {
  OrderCancelInfo,
  NonRefundable,
  LossDetail,
  CancelReasonList2,
  IPenaltyChangeTip,
  CancelTip,
  CancelReasonList,
  OrderModalsVisible,
} from '../Types';
import BbkHeadFootModal from '../../../ComponentBusiness/HeadfootModal';
import OrderCustomerPhoneModalContainer from '../../../Containers/OrderCustomerPhoneModalContainer';
import { CarLog, AppContext, Utils, Channel, GetAB } from '../../../Util/Index';
import {
  ImageUrl,
  EventName,
  Platform,
  UITestID,
} from '../../../Constants/Index';
import {
  CustomerPhoneModalType,
  ORDER_BUTTON,
  CancelReasonCode,
  OrderStatusCtrip,
} from '../../../Constants/OrderDetail';
import OrderCancelConfirmModal, {
  ApplyPenaltyConfirmModal,
  TitleWithTag,
} from '../../../ComponentBusiness/OrderConfirmModal/Index';
import CPage, { IStateType } from '../../../Components/App/CPage';
import { CancelPolicyModal } from './CancelPolicy';
import CancelPenaltyModal from './CancelPenaltyModal';
import Texts, {
  applyLossyCancelSpacehold,
  lossyAmountInfo,
  lossyConfirmContentFirst,
  lossyConfirmContentSecond,
} from '../Texts';
import {
  CancelTipImg,
  CancelReasonGuide,
} from '../../../ComponentBusiness/Common/src/Constants';
import { NewBbkComponentWarningTips as WarningTips } from '../../../ComponentBusiness/Tips';
import { mapChangeReminderToWaringInfo } from '../../../State/OrderDetail/Mappers';
import BbkSkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import OsdModifyOrderModal from './OsdModifyOrderModal';
import { QueryOsdModifyOrderNoteResponseType } from '../../../Types/Dto/QueryOsdModifyOrderNoteRequestAndResponseType';

const { getPixel, getLineHeight } = BbkUtils;
const { width } = Dimensions.get('window');
const contentWidth = width - getPixel(32) * 2 - getPixel(16.5) * 2;
const itemWidth = contentWidth / 3;
const storeNoCarHeight = getPixel(-218);
const isIsd = !!Utils.isCtripIsd();

const styles = StyleSheet.create({
  pb12: {
    paddingBottom: getPixel(12),
  },
  mr0: { marginRight: 0 },
  flexWrap: {
    flexWrap: 'wrap',
  },
  freeDepositLable: {
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: color.orderCardStatusBlueShadow,
    borderStyle: 'solid',
    borderRadius: getPixel(4),
    height: getPixel(32),
    width: getPixel(96),
    ...layout.alignHorizontal,
    marginLeft: getPixel(4),
    marginRight: getPixel(4),
  },
  reasonTipBtnTxt: {
    ...font.caption1LightStyle,
    color: color.fontPrimary,
  },
  reasonTipBtn: {
    minHeight: getPixel(58),
    borderStyle: 'solid',
    borderColor: color.cancelTipBtnBorder,
    borderWidth: StyleSheet.hairlineWidth,
    paddingLeft: getPixel(20),
    paddingRight: getPixel(20),
    paddingTop: getPixel(0),
    paddingBottom: getPixel(0),
    backgroundColor: color.white,
    marginRight: getPixel(16),
    minWidth: getPixel(112),
    marginBottom: getPixel(12),
  },
  onReasonTipBtn: {
    backgroundColor: color.linearGradientCancelTipDark,
    borderColor: color.blueBase,
  },
  onReasonTipBtnTxt: {
    color: color.blueBase,
  },
  cancelFee: {
    paddingBottom: getPixel(27),
  },
  mgTop: {
    marginTop: space.spaceXL,
    paddingBottom: space.spaceXXL,
    borderStyle: 'solid',
    borderTopWidth: getPixel(20),
    paddingTop: space.spaceXXL,
  },
  inputWrap: {
    marginTop: getPixel(0),
  },
  inputReason: {
    color: color.blueGrayBase,
    height: getPixel(340),
    padding: 0,
    textAlignVertical: 'top',
    borderRadius: getPixel(2),
    borderColor: color.underlineColor,
    borderWidth: getPixel(1),
    borderStyle: 'solid',
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
    marginTop: getPixel(18),
  },
  titin: { paddingTop: space.spaceM, paddingBottom: space.spaceM },
  titleStyle: {
    ...font.title4MediumStyle,
    color: color.blueGrayBase,
  },
  buttonStyle: {
    backgroundColor: color.grayBgSecondary,
    borderRadius: 4,
    height: getPixel(64),
    marginRight: getPixel(16),
    marginBottom: getPixel(8),
    width: itemWidth,
    paddingLeft: 0,
    paddingRight: 0,
  },
  textStyle: {
    ...font.caption1LightStyle,
    color: color.blueGrayBase,
  },
  onButtonStyle: {
    backgroundColor: color.blueBgSecondary,
  },
  onTextStyle: {
    ...font.caption1BoldStyle,
    color: color.blueBase,
  },
  rightButtonStyle: {
    marginRight: 0,
  },
  storeNoCarView: {
    zIndex: 1,
    borderRadius: getPixel(4),
    overflow: 'hidden',
  },
  storeNoCarBg: {
    width: getPixel(686),
    marginBottom: getPixel(14),
    padding: getPixel(24),
    borderRadius: getPixel(8),
    overflow: 'hidden',
  },
  storeNoCarBtn: {
    minWidth: getPixel(130),
    width: getPixel(136),
    height: getPixel(60),
    minHeight: getPixel(60),
    marginLeft: getPixel(40),
    paddingTop: getPixel(0),
    paddingBottom: getPixel(0),
    paddingLeft: 0,
    paddingRight: 0,
  },
  storeNoCarText: {
    color: color.white,
    ...font.body3LightStyle,
  },
  cancelBtn: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: getPixel(8),
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    paddingTop: getPixel(28),
    paddingBottom: getPixel(28),
    backgroundColor: color.blueBase,
  },
  cancelBtnText: {
    color: color.white,
  },
  cnbuttonDisable: {
    backgroundColor: color.grayBorder,
    borderColor: color.grayBorder,
  },
  buttonTxtDisable: {
    color: color.grayBase,
  },
  inputStyle: {
    height: getPixel(38),
    ...font.title3BoldStyle,
    lineHeight: getLineHeight(38),
    marginTop: getPixel(4),
  },
  inputWrapperStyle: {
    marginTop: getPixel(16),
    height: getPixel(76),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: color.fontSubLight,
    borderRadius: getPixel(12),
    paddingTop: getPixel(20),
    paddingBottom: getPixel(20),
    paddingLeft: getPixel(24),
    backgroundColor: color.white,
  },
  errorBorder: {
    borderColor: color.modifyOrderExplain,
  },
  cancelTipText: {
    marginBottom: getPixel(8),
  },
  ml58: {
    marginLeft: getPixel(58),
    position: 'relative',
  },
  cancelTipDesc: {
    opacity: 0.6,
    marginLeft: getPixel(16),
  },
  mb6: {
    marginBottom: getPixel(6),
  },
  mb24: {
    marginBottom: getPixel(24),
  },
});

const colors = {
  green: color.successGreen,
  red: color.orangePrice,
  orange: color.orangePrice,
};

interface PressSecondReason {
  code?: number;
  reason?: string;
}

interface ApplyForRefundTypes {
  orderId: string;
  amount: string;
  amountText: string;
  refundPenaltyAmount: string;
  isApplyRefundPenalty: boolean;
  refundPenaltyDesc: string;
  refundPenaltyError: string;
  clickApplyPenalty: () => void;
  unCheckedChange: () => void;
  onChangeAmount: (amount: string) => void;
  lossyInputRefHandle: (ref: any) => void;
  style?: CSSProperties;
}
// 申请退违约金模块
const ApplyForRefund = ({
  orderId,
  amount,
  amountText,
  refundPenaltyAmount,
  isApplyRefundPenalty,
  refundPenaltyDesc,
  refundPenaltyError,
  clickApplyPenalty,
  unCheckedChange,
  onChangeAmount,
  lossyInputRefHandle,
  style,
}: ApplyForRefundTypes) => {
  return (
    <XViewExposure
      className={c2xStyles.refundWrap}
      style={style}
      testID={CarLog.LogExposure({
        name: '曝光_取消订单页_申请退违约金',

        info: {
          orderId,
          orderAmount: amountText,
          penaltyAmount: amount,
        },
      })}
    >
      <View className={c2xStyles.row}>
        <CTouchableOpacity
          className={c2xStyles.row}
          onPress={unCheckedChange}
          testID={UITestID.car_testid_page_cancel_penaly_apply}
        >
          <Image
            className={c2xStyles.selectLogo}
            src={
              isApplyRefundPenalty
                ? `${ImageUrl.componentImagePath}OrderCancel/select.png`
                : `${ImageUrl.componentImagePath}OrderCancel/unSelect.png`
            }
          />

          <CText className={c2xStyles.refundPenaltyTip}>
            {Texts.applyLossyCancelTitle}
          </CText>
        </CTouchableOpacity>
        <CText
          type="icon"
          className={c2xStyles.helpIcon}
          onPress={clickApplyPenalty}
        >
          {icon.circleQuestion}
        </CText>
      </View>
      {isApplyRefundPenalty && (
        <BbkInput
          style={styles.inputWrap}
          value={refundPenaltyAmount}
          isShowTitle={false}
          leftChildren={
            <CText className={c2xStyles.inputLeft} fontWeight="medium">
              {Texts.rmb}
            </CText>
          }
          showClearWhileEditing={false}
          title={applyLossyCancelSpacehold(`${Texts.rmb}${amount}`)}
          inputWrapperStyle={xMergeStyles([
            styles.inputWrapperStyle,
            !!refundPenaltyError && styles.errorBorder,
          ])}
          inputStyle={styles.inputStyle}
          formatType={InputFormatType.decimal}
          onChangeText={onChangeAmount}
          placeholder={applyLossyCancelSpacehold(`${Texts.rmb}${amount}`)}
          inputRefHandler={lossyInputRefHandle}
        />
      )}
      {isApplyRefundPenalty && (
        <View className={c2xStyles.validateWrap}>
          {!!refundPenaltyError && (
            <CText className={c2xStyles.error}>{refundPenaltyError}</CText>
          )}
          {!!refundPenaltyDesc && (
            <CText className={c2xStyles.info}>{refundPenaltyDesc}</CText>
          )}
        </View>
      )}
    </XViewExposure>
  );
};

ApplyForRefund.defaultProps = {
  style: null,
};

const Input = ({
  onChangeCallBack,
}: {
  onChangeCallBack: (text: string) => void;
}) => {
  const [input, setInput] = useState('');
  return (
    <TextInput
      style={xMergeStyles([font.body3LightStyle, styles.inputReason])}
      multiline={true}
      testID={UITestID.car_testid_page_cancel_reason_input}
      numberOfLines={7}
      onSubmitEditing={Keyboard.dismiss}
      underlineColorAndroid="transparent"
      value={input}
      onChangeText={text => {
        setInput(text);
        onChangeCallBack(text);
      }}
      placeholder="其他意见和建议，最多输入100字"
      placeholderTextColor={color.fontSubLight}
    />
  );
};

interface CancelTopBlockType extends LossDetail {
  showCancelPolicyModal: () => void;
  children?: ReactNode | ReactNodeArray;
}
const CancelTopBlock = memo(
  ({
    title,
    titleSuppl,
    backDescList,
    nonRefundable,
    showCancelPolicyModal,
    children,
  }: CancelTopBlockType) => {
    const { color: iColor, text } = titleSuppl || {};
    return (
      <>
        <View style={xMergeStyles([layout.betweenHorizontal, styles.mb24])}>
          <CText style={xMergeStyles([font.title2MediumStyle, layout.flex1])}>
            {title?.replace('{tag}', '')}
            <CText
              style={xMergeStyles([
                { color: colors[iColor] },
                font.title2MediumStyle,
              ])}
              fontWeight="medium"
            >
              {text}
            </CText>
          </CText>
          <CText
            className={c2xStyles.buttonText}
            testID={UITestID.car_testid_page_cancel_showcancelpolicy}
            onPress={showCancelPolicyModal}
          >
            取消政策
          </CText>
        </View>
        {children}
        {!!backDescList?.length &&
          backDescList.map((item, idx) => (
            <View
              key={item?.title}
              className={idx !== backDescList.length - 1 && c2xStyles.tipDesc}
            >
              <View style={layout.rowStart}>
                <View className={c2xStyles.square} />
                {!!item?.title && (
                  <CText
                    style={xMergeStyles([layout.flex1, font.title3LightStyle])}
                  >
                    {item?.title}
                  </CText>
                )}
              </View>
              {!!item?.subTitle && (
                <CText
                  className={c2xStyles.subTitle}
                  style={font.title3LightStyle}
                >
                  {item?.subTitle}
                </CText>
              )}
            </View>
          ))}
        <CancelNonRefundable {...nonRefundable} />
      </>
    );
  },
);

const CancelNonRefundable = memo(({ title, descList }: NonRefundable) => {
  const [descWrapWidth, setdescWrapWidth] = useState(0);
  const [descRightTxtWidths, setdescRightTxtWidths] = useState({});
  if (!title || !descList.length) return null;
  return (
    <LinearGradient
      className={c2xStyles.cancelTip}
      start={{ x: 0.0, y: 0.0 }}
      end={{ x: 1.0, y: 1.0 }}
      locations={[0, 1]}
      colors={[
        color.linearGradientCancelTipDark,
        color.linearGradientCancelTipLight,
      ]}
    >
      <View style={xMergeStyles([layout.rowStart, styles.ml58])}>
        <Image
          className={c2xStyles.cancelTipImg}
          source={{ uri: CancelTipImg }}
        />

        <CText
          className={classNames(
            c2xStyles.cancelTipTitleColor,
            c2xStyles.cancelTipTitle,
          )}
          fontWeight="medium"
        >
          {title}
        </CText>
      </View>
      {!!descList.length &&
        descList.map((item, i) => (
          <View
            key={item?.title}
            style={xMergeStyles([
              layout.flexRow,
              i !== descList.length - 1 && styles.cancelTipText,
              styles.ml58,
            ])}
            onLayout={event => {
              setdescWrapWidth(event.nativeEvent.layout.width);
            }}
          >
            <View className={c2xStyles.point} />
            <CText
              className={c2xStyles.cancelTipTitleColor}
              style={xMergeStyles([
                font.caption1LightStyle,
                descRightTxtWidths[i] && {
                  maxWidth:
                    descWrapWidth - descRightTxtWidths[i] - getPixel(44),
                },
              ])}
              numberOfLines={1}
            >
              {item?.title}
            </CText>
            {!!item?.subTitle && (
              <CText
                className={c2xStyles.cancelTipTitleColor}
                style={xMergeStyles([
                  font.caption1LightStyle,
                  styles.cancelTipDesc,
                ])}
                onLayout={event => {
                  if (!descRightTxtWidths[i]) {
                    setdescRightTxtWidths({
                      [i]: event.nativeEvent.layout.width,
                    });
                  }
                }}
              >
                {item.subTitle}
              </CText>
            )}
          </View>
        ))}
    </LinearGradient>
  );
});

interface ReasonBoxItemProps {
  noMr: boolean;
  isSelect: boolean;
  onPressToggle?: () => void;
  title: string;
  hasSecondReason?: boolean;
}
const ReasonBoxItem: React.FC<ReasonBoxItemProps> = props => {
  const { isSelect, title, onPressToggle, noMr, hasSecondReason } = props;
  const buttonStyle = xMergeStyles([
    styles.buttonStyle,
    isSelect && styles.onButtonStyle,
    noMr && styles.rightButtonStyle,
  ]);

  const textStyle = xMergeStyles([
    styles.textStyle,
    isSelect && styles.onTextStyle,
  ]);
  return (
    <View className={c2xStyles.rel} style={layout.verticalItem}>
      <Button
        numberOfLines={10}
        text={title}
        buttonStyle={buttonStyle}
        textStyle={textStyle}
        onPress={onPressToggle}
        testID={`${UITestID.car_testid_page_cancel_reason_item}_${title}`}
        buttonSize="S"
      />

      {isSelect && hasSecondReason ? (
        <Image
          className={c2xStyles.triangleImg}
          src={CancelReasonGuide.triangle}
          mode="aspectFill"
        />
      ) : (
        <View className={c2xStyles.triangleImg} />
      )}
    </View>
  );
};
ReasonBoxItem.defaultProps = {
  onPressToggle: null,
};

interface CancelReasonTipAboutDepositType {
  title: string;
  hasIcon: boolean;
  canDeposit: boolean;
  bothFreeDeposithInfo: BothFreeDeposithInfo;
  unableDepositInfo: UnableDepositInfo;
}

interface ReasonBoxProps {
  onSelect?: (data: any) => void;
  data: any;
  animatedHeight?: any;
  btnPressHandle: (
    code: number,
    isReBookCode?: boolean,
    secondCancelReason?: CancelReasonList,
  ) => void;
  cancelReasonTipAboutDeposit: CancelReasonTipAboutDepositType;
  supportReBook: boolean;
  supportModifyOrder?: boolean;
  pressSecondReasonHandle: (params: PressSecondReason) => void;
  isApplyRefundPenalty?: boolean;
  isOSDNewOrderCancel?: boolean;
  isRetainOrder?: boolean;
  isNewOsdModifyOrder?: boolean;
}

interface ReasonBoxStates {
  selectedIdx: number;
}
class ReasonBox extends PureComponent<ReasonBoxProps, ReasonBoxStates> {
  constructor(props) {
    super(props);
    this.state = {
      selectedIdx: -1,
    };
  }

  getReasonsDom = (data, lineIndex, hasSecondReason) => {
    const { onSelect } = this.props;
    return data.map((item, i) => (
      <ReasonBoxItem
        key={`ReasonBoxItem_${i + 1}`}
        noMr={(i + 1) % 3 === 0 || i === data.length - 1}
        isSelect={item.isSelect}
        title={item.title || item.reason}
        hasSecondReason={hasSecondReason}
        onPressToggle={() => {
          onSelect(lineIndex * 3 + i);
          this.setState({
            selectedIdx: lineIndex * 3 + i,
          });
          CarLog.LogCode({
            name: '点击_订单详情页_取消原因_选择',

            info: {
              reasonLevel1: item.title || item.reason,
            },
          });
        }}
      />
    ));
  };

  tipOnPressHandle = secondCancelReason => {
    const { data, btnPressHandle } = this.props;
    const { selectedIdx } = this.state;
    const { tip, code } = data?.[selectedIdx] || {};
    const { title, button } = tip || {};
    CarLog.LogCode({
      name: '点击_订单取消页-取消原因',

      info: {
        cancelCode: code,
        cancelName: title,
        buttonName: button,
      },
    });
    btnPressHandle(code, this.isReBookCode(code), secondCancelReason);
  };

  splitData = () => {
    const { data } = this.props;
    const nData = [];
    if (data?.length > 0) {
      for (let i = 0; i < data.length; i += 3) {
        nData.push(data.slice(i, i + 3));
      }
    }
    return nData;
  };

  getCurTip = () => {
    const {
      data,
      cancelReasonTipAboutDeposit,
      supportReBook,
      supportModifyOrder,
    } = this.props;
    const { selectedIdx } = this.state;
    const { tip, code } = data?.[selectedIdx] || {};
    const curTip =
      code === CancelReasonCode.unFreeDeposit
        ? cancelReasonTipAboutDeposit
        : tip;
    if (code === CancelReasonCode.changeOrder && !supportModifyOrder) {
      curTip.button = Texts.reBookingText;
    }
    if (
      (this.isReBookCode(code) || code === CancelReasonCode.changeOrder) &&
      !supportReBook
    ) {
      curTip.button = '';
    }
    return curTip;
  };

  isReBookCode = code => {
    // 提示模块的按钮走重新预订逻辑的条件
    const { cancelReasonTipAboutDeposit } = this.props;
    return !!(
      [CancelReasonCode.TripCancellation, CancelReasonCode.StoreNoCar].includes(
        code,
      ) ||
      (code === CancelReasonCode.unFreeDeposit &&
        cancelReasonTipAboutDeposit?.unableDepositInfo?.lable)
    );
  };

  render() {
    const {
      data,
      supportReBook,
      supportModifyOrder,
      pressSecondReasonHandle,
      isApplyRefundPenalty,
      isOSDNewOrderCancel,
      isRetainOrder,
      isNewOsdModifyOrder,
    } = this.props;
    const { selectedIdx } = this.state;
    const { code, cancelReasonList } = data?.[selectedIdx] || {};

    const reBookBtn =
      supportReBook &&
      (this.isReBookCode(code) ||
        (code === CancelReasonCode.changeOrder && !supportModifyOrder));

    const curTip = this.getCurTip();
    const { title, button } = curTip || {};
    const hasRefundSecondReason = isApplyRefundPenalty
      ? [
          CancelReasonCode.haveLowerPrice,
          CancelReasonCode.vehicleProblem,
          CancelReasonCode.Other,
        ].includes(code)
      : true;

    return (
      <View
        className={classNames(
          c2xStyles.reasonWrap,
          Utils.isCtripOsd() && c2xStyles.pb0,
        )}
        style={isOSDNewOrderCancel && layout.alignItemsStart}
      >
        {this.splitData().map((item, lineIdx) => {
          return (
            <View key={uuid()}>
              <View
                style={xMergeStyles([
                  layout.startHorizontal,
                  !isRetainOrder && styles.mb6,
                ])}
              >
                {this.getReasonsDom(
                  item,
                  lineIdx,
                  !lodashIsEmpty(curTip) || !!cancelReasonList?.length,
                )}
              </View>
              {!!isRetainOrder &&
                selectedIdx > -1 &&
                lineIdx === Math.floor(selectedIdx / 3) &&
                (!lodashIsEmpty(curTip) || !!cancelReasonList?.length) &&
                hasRefundSecondReason && (
                  <XViewExposure
                    testID={CarLog.LogExposure({
                      name: '曝光_订单取消页-取消原因',

                      info: {
                        cancelCode: code,
                        cancelName: title,
                        buttonName: button,
                      },
                    })}
                  >
                    <ReasonSecondBox
                      {...curTip}
                      supportReBook={reBookBtn}
                      pressBtn={this.tipOnPressHandle}
                      pressSecondReasonHandle={pressSecondReasonHandle}
                      cancelReasonList={cancelReasonList}
                      reasonName={data?.[selectedIdx].reason}
                      isNewOsdModifyOrder={isNewOsdModifyOrder}
                    />
                  </XViewExposure>
                )}
            </View>
          );
        })}
      </View>
    );
  }
}

interface BothFreeDeposithInfo {
  preAmountForCarTxt: string;
  preAmountForCar: string;
  preAmountForPeccancy: string;
  preAmountForPeccancyTxt: string;
}
interface UnableDepositInfo {
  lable: string;
  text1: string;
  text2: string;
  text3: string;
}
interface ReasonSecondBoxProps {
  title?: string;
  desc?: string;
  animatedHeight?: any;
  supportReBook?: boolean;
  pressBtn: (data?) => void;
  button?: string;
  style?: CSSProperties;
  cancelReasonList?: CancelReasonList2[];
  bothFreeDeposithInfo?: BothFreeDeposithInfo;
  unableDepositInfo?: UnableDepositInfo;
  hasIcon?: boolean;
  pressSecondReasonHandle?: (params: PressSecondReason) => void;
  canDeposit?: boolean;
  reasonName?: string;
  code?: number;
  isNewOsdModifyOrder?: boolean;
}
const ReasonSecondBox = memo((props: ReasonSecondBoxProps) => {
  const {
    title,
    desc,
    animatedHeight,
    pressBtn,
    button,
    style,
    cancelReasonList,
    bothFreeDeposithInfo,
    unableDepositInfo,
    hasIcon = true,
    pressSecondReasonHandle,
    supportReBook,
    canDeposit,
    reasonName,
    code: reasonCode,
    isNewOsdModifyOrder,
  } = props;
  const [selectedSecondReasonIdx, setSelectedSecondReasonIdx] = useState(-1);
  const pressSecondReason = useMemoizedFn(idx => {
    const data = cancelReasonList?.[idx];
    const { code, reason } = data || {};
    if (selectedSecondReasonIdx === idx) {
      setSelectedSecondReasonIdx(-1);
      pressSecondReasonHandle({});
    } else {
      setSelectedSecondReasonIdx(idx);
      pressSecondReasonHandle({
        code,
        reason,
      });
    }
    CarLog.LogCode({
      name: '点击_订单详情页_取消二级原因',

      info: {
        reasonLevel2: reason,
      },
    });
  });
  const secondReason = cancelReasonList[selectedSecondReasonIdx];
  const secondTip = secondReason?.tip;
  const pressThirdReasonHandle = useMemoizedFn(() => {
    if (
      secondReason?.code === CancelReasonCode.goForLicense &&
      secondReason?.tip?.button
    ) {
      CarLog.LogCode({ name: '点击_订单详情页_驾照翻译件去办理' });
    }
    if (
      [
        CancelReasonCode.UnionPayDualCardsNotSupported,
        CancelReasonCode.CardsKind,
      ].includes(secondTip?.code)
    ) {
      CarLog.LogCode({
        name: '点击_订单详情页_取消重订按钮',

        info: {
          reasonLevel2: secondReason.reason,
        },
      });
    }
    pressBtn(secondReason);
  });
  const getSecondTipTestID = useMemo(() => {
    return secondReason?.code === CancelReasonCode.goForLicense &&
      secondReason?.tip?.button
      ? CarLog.LogExposure({ name: '曝光_订单详情页_驾照翻译件去办理' })
      : '';
  }, [secondReason]);
  const handlePressBtn = useMemoizedFn(() => {
    pressBtn();
    if (
      [
        CancelReasonCode.OsdChangeOrder,
        CancelReasonCode.OsdTripCancellation,
        CancelReasonCode.UnCrossTheBorder,
      ].includes(reasonCode)
    ) {
      CarLog.LogCode({
        name: '点击_订单详情页_取消重订按钮',

        info: {
          reasonLevel2: reasonName,
        },
      });
    }
  });
  const getOsdModifyOrderBtnExposureData = useMemoizedFn(() => {
    if (isNewOsdModifyOrder) {
      if (
        [
          CancelReasonCode.OsdChangeOrder,
          CancelReasonCode.OsdTripCancellation,
        ].includes(reasonCode)
      ) {
        return CarLog.LogExposure({ name: '曝光_取消订单_修改订单' });
      }
      return reasonCode === CancelReasonCode.UnCrossTheBorder
        ? CarLog.LogExposure({
            name: '曝光_订单详情页_取消重订按钮',

            info: {
              reasonLevel2: reasonName,
            },
          })
        : '';
    }
    return [
      CancelReasonCode.OsdChangeOrder,
      CancelReasonCode.OsdTripCancellation,
      CancelReasonCode.UnCrossTheBorder,
    ].includes(reasonCode)
      ? CarLog.LogExposure({
          name: '曝光_订单详情页_取消重订按钮',

          info: {
            reasonLevel2: reasonName,
          },
        })
      : '';
  });

  return (
    <View
      style={xMergeStyles([
        styles.storeNoCarView,
        {
          marginTop: animatedHeight,
        },
        style,
      ])}
    >
      <ImageBackground
        source={{ uri: CancelReasonGuide.bg }}
        style={xMergeStyles([
          styles.storeNoCarBg,
          cancelReasonList?.length && styles.pb12,
        ])}
      >
        {!!title && (
          <View style={layout.betweenHorizontal}>
            <View style={xMergeStyles([layout.flexRow, layout.flex1])}>
              {!!hasIcon && (
                <Image
                  className={c2xStyles.logoIcon}
                  src={
                    cancelReasonList?.length
                      ? CancelReasonGuide.iconHelp
                      : CancelReasonGuide.logoIcon
                  }
                  mode="aspectFill"
                />
              )}
              <CText
                className={c2xStyles.storeNoCarTitleText}
                fontWeight="medium"
              >
                {title}
              </CText>
            </View>
            {!!canDeposit && !!button && (
              <Button
                onPress={pressBtn}
                testID={`${UITestID.car_testid_page_cancel_reason_item}_${button}`}
                text={button}
                buttonStyle={styles.storeNoCarBtn}
                textStyle={styles.storeNoCarText}
              />
            )}
          </View>
        )}
        <View className={c2xStyles.storeNoCarExplain}>
          {!!desc && (
            <View className={c2xStyles.explain}>
              <CText className={c2xStyles.storeNoCarExplainText}>{desc}</CText>
            </View>
          )}
          {!!bothFreeDeposithInfo?.preAmountForCar && (
            <View className={c2xStyles.explain} style={layout.flexRow}>
              <CText className={c2xStyles.storeNoCarExplainText}>
                {bothFreeDeposithInfo.preAmountForCarTxt}
              </CText>
              <CText
                className={classNames(
                  c2xStyles.storeNoCarExplainText,
                  c2xStyles.lineThrough,
                )}
              >
                {bothFreeDeposithInfo.preAmountForCar}
              </CText>
              <CText className={c2xStyles.storeNoCarExplainText}>
                {`，${bothFreeDeposithInfo.preAmountForPeccancyTxt}`}
              </CText>
              <CText
                className={classNames(
                  c2xStyles.storeNoCarExplainText,
                  c2xStyles.lineThrough,
                )}
              >
                {bothFreeDeposithInfo.preAmountForPeccancy}
              </CText>
            </View>
          )}
          {!!unableDepositInfo?.lable && (
            <View className={c2xStyles.explain} style={layout.flexRowWrap}>
              <CText className={c2xStyles.storeNoCarExplainText}>
                {unableDepositInfo.text1}
              </CText>
              <View style={styles.freeDepositLable}>
                <CText className={c2xStyles.freeDepositLableTxt}>
                  {unableDepositInfo.lable}
                </CText>
              </View>
              <CText className={c2xStyles.storeNoCarExplainText}>
                {unableDepositInfo.text2}
              </CText>
              <CText className={c2xStyles.storeNoCarExplainText}>
                {unableDepositInfo.text3}
              </CText>
            </View>
          )}
          {(!!button || supportReBook) && !canDeposit && (
            <XViewExposure testID={getOsdModifyOrderBtnExposureData()}>
              <Button
                onPress={handlePressBtn}
                text={button || Texts.reBookingText}
                buttonStyle={styles.storeNoCarBtn}
                textStyle={styles.storeNoCarText}
                testID={`${UITestID.car_testid_page_cancel_reason_item}_${
                  button || Texts.reBookingText
                }`}
              />
            </XViewExposure>
          )}
        </View>
        {!!cancelReasonList?.length && (
          <View className={c2xStyles.mt12}>
            <View style={xMergeStyles([layout.rowStart, styles.flexWrap])}>
              {cancelReasonList.map((item, idx) => (
                <XBoxShadow
                  testID={CarLog.LogExposure({
                    name: '曝光_订单详情页_取消二级原因',

                    info: {
                      reasonLevel2: item?.reason,
                    },
                  })}
                  coordinate={{ x: 0, y: 2 }}
                  color={
                    selectedSecondReasonIdx === idx
                      ? setOpacity(color.cancelSecondLevelReason, 0.05)
                      : setOpacity(color.cancelSecondLevelReason, 0)
                  }
                  opacity={1}
                  blurRadius={getPixel(4)}
                  elevation={selectedSecondReasonIdx === idx ? 4 : 0}
                >
                  <Button
                    key={item?.reason}
                    onPress={() => pressSecondReason(idx)}
                    text={item?.reason}
                    buttonStyle={xMergeStyles([
                      styles.reasonTipBtn,
                      selectedSecondReasonIdx === idx && styles.onReasonTipBtn,
                      (idx + 1) % 3 === 0 && styles.mr0,
                    ])}
                    textStyle={xMergeStyles([
                      styles.reasonTipBtnTxt,
                      selectedSecondReasonIdx === idx &&
                        styles.onReasonTipBtnTxt,
                    ])}
                    border={true}
                    testID={`${UITestID.car_testid_page_cancel_reason_item}_${item?.reason}`}
                  />
                </XBoxShadow>
              ))}
            </View>
            {!!secondTip && (
              <View
                className={classNames(
                  c2xStyles.storeNoCarExplain,
                  c2xStyles.secondTip,
                )}
                testID={getSecondTipTestID}
              >
                {!!secondTip?.title && (
                  <View className={c2xStyles.explain}>
                    <CText className={c2xStyles.storeNoCarExplainText}>
                      {secondTip?.title}
                    </CText>
                  </View>
                )}
                {secondTip?.button && (
                  <XViewExposure
                    testID={
                      [
                        CancelReasonCode.UnionPayDualCardsNotSupported,
                        CancelReasonCode.CardsKind,
                      ].includes(secondTip.code)
                        ? CarLog.LogExposure({
                            name: '曝光_订单详情页_取消重订按钮',

                            info: {
                              reasonLevel2: secondReason.reason,
                            },
                          })
                        : ''
                    }
                  >
                    <Button
                      onPress={pressThirdReasonHandle}
                      text={secondTip?.button}
                      buttonStyle={styles.storeNoCarBtn}
                      textStyle={styles.storeNoCarText}
                      testID={`${UITestID.car_testid_page_cancel_reason_item}_${secondTip?.button}`}
                    />
                  </XViewExposure>
                )}
              </View>
            )}
          </View>
        )}
      </ImageBackground>
    </View>
  );
});

interface CancelProps extends IBasePageProps {
  cancelModalVisible: boolean;
  resCancelFee: any;
  toCancelBook?: (arg: any) => void;
  orderBaseInfo: any;
  isdFeeInfo: any;
  cancelReasons: any;
  cancelRuleInfo: any;
  superVipInfo: any;
  fetchQueryCancelFee: (data?: any) => void;
  queryOrderCancelInfo: (data?: any) => void;
  menuList?: any;
  noCarReason?: string;
  reBooking: (isInfoFromOrder?: boolean) => void;
  orderCancelInfo?: OrderCancelInfo;
  setOrderModalsVisible: (data) => void;
  isEhi?: boolean;
  cancelReasonTipAboutDeposit: CancelReasonTipAboutDepositType;
  setDepositDetailModalVisible: (data: { visible: boolean }) => void;
  setPhoneModalVisible: (data: any) => void;
  supportModifyOrder?: boolean;
  isFetchCancelInfoLoading?: boolean; // 刷新取消页信息
  isPenaltyChange?: boolean; // 违约金发生变更
  penaltyChangeTip?: IPenaltyChangeTip; // 违约金变更提醒
  goNewHomeParams?: { rentalLocation: any; rentalDate: any };
  penaltyChangeCancelTip: CancelTip;
  isKlbVersion?: boolean;
  isNewCancelRule?: boolean;
  modalsVisible?: OrderModalsVisible;
  cancelOrderSubmitId?: string;
  setCancelOrderSubmitId: (data: string) => void;
  fetchOrder2: (data?: any) => void;
  isNewOsdModifyOrder?: boolean;
  osdModifyOrderNote?: QueryOsdModifyOrderNoteResponseType;
  osdModifyOrderInit?: () => void;
  isCross?: boolean; // 是否跨容器跳转
}

interface CancelState extends IStateType {
  reasons: any;
  input: string;
  isShowCancelConfirm: boolean;
  isShowCancelPolicyModal: boolean;
  isShowApplyPenaltyConfirm: boolean;
  isShowCancelPenaltyModal: boolean;
  isShowPhoneModalVisible: boolean;
  showPhoneModalType: number;
  refundPenaltyAmount: string;
  isApplyRefundPenalty: boolean;
  isAgreeApply: boolean;
  refundPenaltyError: string;
  refundPenaltyDesc: string;
  isShowNoReasonError: boolean;
  animatedHeight: any;
  animateTipHeight: any;
  selectSecondReason: { code?: number; reason?: string };
  isShowCancelPolicyChangeTip: boolean;
  locationDatePopVisible: boolean;
}
class Cancel extends CPage<CancelProps, CancelState> {
  lossyInputRef: any = null;

  inputValue: string = '';

  applyPenaltyConfirmBtn = [
    {
      get name() {
        return Texts.orderCancelCancelBtn;
      },
      onPress: () => {
        this.switchCancelConfirm(false);
      },
    },
    {
      get name() {
        return Texts.order_cancelOrder;
      },
      isPrimary: true,
      onPress: () => {
        this.toCancelBook();
      },
    },
  ];

  isClickCancelToRebook: boolean = false; // 是否点击了取消重订按钮

  homeSelectedFilters: string[] | null = null; // 跳转列表页筛选参数

  searchPanelModalRef: any; // 出境修改订单弹层

  constructor(props) {
    super(props);
    this.state = {
      input: '',
      reasons: props.cancelReasons,
      isShowCancelConfirm: false,
      isShowCancelPolicyModal: false,
      isShowApplyPenaltyConfirm: false,
      isShowCancelPenaltyModal: false,
      isShowPhoneModalVisible: false,
      refundPenaltyAmount: '',
      isApplyRefundPenalty: false,
      isAgreeApply: false,
      refundPenaltyError: '',
      refundPenaltyDesc: '',
      isShowNoReasonError: false,
      animatedHeight: null,
      animateTipHeight: null,
      selectSecondReason: {},
      showPhoneModalType: CustomerPhoneModalType.Phone,
      isShowCancelPolicyChangeTip: false,
      locationDatePopVisible: false,
    };
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().OrderCancel.ID;
  }

  componentDidMount() {
    super.componentDidMount();
    // 轮询校验取消政策提示
    this.checkPolicyChangeTip();
  }

  componentDidUpdate(prevProps, prevState) {
    const { isApplyRefundPenalty: preIsApplyRefundPenalty } = prevState;
    const { isApplyRefundPenalty } = this.state;
    if (
      this.lossyInputRef &&
      !preIsApplyRefundPenalty &&
      isApplyRefundPenalty
    ) {
      this.lossyInputRef.focus();
    }
  }

  onBackAndroid() {
    const { isShowCancelPolicyModal, locationDatePopVisible } = this.state;
    if (isShowCancelPolicyModal) {
      this.setState({ isShowCancelPolicyModal: false });
    } else if (locationDatePopVisible) {
      this.setState({ locationDatePopVisible: false });
    } else {
      this.pop();
    }
  }

  pressSecondReasonHandle = (params: PressSecondReason) => {
    this.setState({
      selectSecondReason: params,
    });
  };

  getReasonCodes = () => {
    const { reasons, selectSecondReason } = this.state;
    const reasonCodes = reasons.filter(v => v.isSelect).map(v => v.code);
    reasonCodes.push(selectSecondReason.code);
    return reasonCodes;
  };

  toCancelBook = () => {
    CarLog.LogCode({ name: '点击_订单详情页_取消弹窗_确认取消' });
    // @ts-ignore
    const { reasons, refundPenaltyAmount, selectSecondReason } = this.state;
    const input = this.inputValue;
    const selectedReason = reasons.filter(v => v.isSelect);
    const reason = `${selectedReason.map(v => v.title || v.reason).join(';')}${
      selectSecondReason?.reason ? `;${selectSecondReason?.reason}` : ''
    }${input ? `;${input}` : ''}`;
    const reasonCode = `${selectedReason.map(v => v.code).join(';')}${
      selectSecondReason?.code ? `;${selectSecondReason?.code}` : ''
    }`;

    const { toCancelBook, fetchQueryCancelFee } = this.props;
    toCancelBook({
      reason,
      refundPenaltyAmount: `${Number(refundPenaltyAmount)}`,
      reasonCode,
      callback: ({ isPenaltyChange, isShowCancelTip }) => {
        const isOSDNewOrderCancel = this.isOSDNewOrderCancel();
        if (isIsd) {
          this.switchCancelConfirm(false);
        }
        if (isPenaltyChange) {
          if (isOSDNewOrderCancel) {
            return;
          }
          this.showCancelPolicyModal();
        } else if (isOSDNewOrderCancel && this.isClickCancelToRebook) {
          this.switchCancelConfirm(false);
          fetchQueryCancelFee({ callback: Utils.noop });
          if (this.homeSelectedFilters) {
            this.openList();
          } else {
            this.osdReBook();
          }
        } else if (!isShowCancelTip) {
          AppContext.PageInstance.pop();
        }
      },
    });
  };

  closeModal = () => {
    AppContext.PageInstance.pop();
    if (Keyboard && typeof Keyboard.dismiss === 'function') {
      Keyboard.dismiss();
    }
    if (
      // @ts-ignore
      this.scrollerView &&
      // @ts-ignore
      typeof this.scrollerView.scrollToPosition === 'function'
    ) {
      // @ts-ignore
      this.scrollerView.scrollToPosition(0, 0, false);
    }
  };

  alertNoReason = () => {
    BbkToast.show(Texts.noReasonMessage);
  };

  isOSDNewOrderCancel = () => {
    const { isKlbVersion } = this.props;
    return GetAB.isOSDNewOrderCancel(isKlbVersion);
  };

  isRetainOrder = () => isIsd || this.isOSDNewOrderCancel();

  onPressCancelBtn = () => {
    const isNoReason = this.isRetainOrder() && !this.getHasReason();
    this.logClick('点击_订单取消页_取消订单');
    if (isNoReason) {
      this.alertNoReason();
    } else {
      this.switchCancelConfirm(true);
    }
  };

  getFooter = () => {
    const isNoReason = this.isRetainOrder() && !this.getHasReason();
    return (
      <Button
        text={Texts.order_cancelOrder}
        style={xMergeStyles([
          styles.cancelBtn,
          isNoReason && styles.cnbuttonDisable,
        ])}
        textStyle={xMergeStyles([
          styles.cancelBtnText,
          isNoReason && styles.buttonTxtDisable,
        ])}
        testID="car_osd_order_cancel_btn"
        onPress={this.onPressCancelBtn}
      />
    );
  };

  getHasStoreNoCar = () => {
    const { reasons } = this.state;
    return !!reasons?.find(item => item.code === CancelReasonCode.StoreNoCar);
  };

  getHasReason = () => {
    const { reasons } = this.state;
    return !!reasons?.find(item => item.isSelect);
  };

  supportReBook = () => {
    const { orderBaseInfo } = this.props;
    const allOperations = orderBaseInfo?.allOperations;
    return !!allOperations?.find(
      v => v.operationId === ORDER_BUTTON.ModifyOrder && v.enable,
    );
  };

  onReasonSelect = id => {
    let { reasons } = this.state;
    let reason;
    const hasStoreNoCar = isIsd && this.getHasStoreNoCar();
    let animatedValue = storeNoCarHeight;
    reasons = reasons.map((item, i) => {
      const nItem = item;
      const { code } = item;
      if (i === id) {
        if (code === CancelReasonCode.StoreNoCar) {
          animatedValue = 0;
        }
        nItem.isSelect = true;
        reason = nItem.title || nItem.reason;
      } else {
        nItem.isSelect = false;
      }
      return nItem;
    });

    this.setState(
      {
        reasons,
        isShowNoReasonError: !reason,
      },
      () => {
        if (hasStoreNoCar) {
          this.setState({
            animatedHeight: animatedValue,
          });
        }
      },
    );
  };

  switchCancelConfirm = async (isShow: boolean) => {
    if (Keyboard && typeof Keyboard.dismiss === 'function') {
      Keyboard.dismiss();
    }
    if (this.validateCancelOrder()) {
      this.isShowConfirmModal(isShow);
    }

    if (!isShow) {
      // 点击确认取消弹窗的再看看时需要将点击过取消重订的按钮状态置为false
      if (this.isClickCancelToRebook) {
        this.isClickCancelToRebook = false;
      }
      const { isPenaltyChange, fetchQueryCancelFee } = this.props;
      if (isPenaltyChange && this.isOSDNewOrderCancel()) {
        fetchQueryCancelFee({ callback: Utils.noop });
      }
    }
  };

  isShowConfirmModal = isShow => {
    const { refundPenaltyAmount } = this.state;
    // 如果有申请违约金，展示申请违约金弹窗
    const amount = Number(refundPenaltyAmount);
    if (amount > 0) {
      this.setState({
        isShowApplyPenaltyConfirm: isShow,
      });
    } else {
      this.setState({
        isShowCancelConfirm: isShow,
      });
    }
  };

  hideCashBackModal = () => {
    this.isShowConfirmModal(true);
  };

  showCancelPolicyModal = () => {
    this.setState({ isShowCancelPolicyModal: true });
    Keyboard.dismiss();
    CarLog.LogCode({ name: '点击_取消订单二级页面_取消政策' });
  };

  hideCancelPolicyModal = () => {
    this.setState({ isShowCancelPolicyModal: false });
  };

  showCancelPenaltyModal = () => {
    this.setState({ isShowCancelPenaltyModal: true });
  };

  hideCancelPenaltyModal = () => {
    this.setState({ isShowCancelPenaltyModal: false });
  };

  getInitialMessage = check => {
    const { refundPenaltyError: curRefundPenaltyError } = this.state;
    const { resCancelFee } = this.props;
    // 2022-12-7 勾选退违约金时，输入框默认填入全额金额
    const curRefundPenaltyAmount =
      check && resCancelFee?.amount ? `${resCancelFee?.amount}` : '';
    const penaltyAmountDisplay =
      Number(curRefundPenaltyAmount) > 0
        ? `${Texts.rmb}${Number(curRefundPenaltyAmount)}`
        : '';
    const refundPenaltyDesc = lossyAmountInfo(penaltyAmountDisplay);
    return {
      refundPenaltyAmount: curRefundPenaltyAmount,
      refundPenaltyError: check ? curRefundPenaltyError : '',
      refundPenaltyDesc,
    };
  };

  agreeApply = () => {
    this.setState({
      isShowCancelPenaltyModal: false,
      isApplyRefundPenalty: true,
      isAgreeApply: true,
      ...this.getInitialMessage(true),
    });
  };

  showPhoneModal = () => {
    this.setState({ isShowPhoneModalVisible: true });
  };

  hidePhoneModal = () => {
    this.setState({ isShowPhoneModalVisible: false });
  };

  validateCancelOrder = () => {
    const {
      reasons,
      isApplyRefundPenalty,
      refundPenaltyError,
      refundPenaltyAmount,
    } = this.state;
    const reason = reasons.find(v => v.isSelect);
    this.setState({
      isShowNoReasonError: !reason,
    });
    const isPenaltyValidate =
      !isApplyRefundPenalty || (!refundPenaltyError && refundPenaltyAmount);
    if (!isPenaltyValidate && this.lossyInputRef) {
      this.lossyInputRef.focus();
    }
    return !!reason && isPenaltyValidate;
  };

  logApplyPenalty = () => {
    const { resCancelFee, orderBaseInfo } = this.props;
    const { isApplyRefundPenalty } = this.state;
    const { amount } = resCancelFee;
    const { orderId } = orderBaseInfo;
    const amountText = this.getAmountText();
    CarLog.LogCode({
      name: '点击_取消订单页_申请退违约金',

      info: {
        orderId: `${orderId}`,
        orderAmount: amountText,
        penaltyAmount: amount,
        isSelect: isApplyRefundPenalty ? 0 : 1,
      },
    });
  };

  clickApplyPenalty = () => {
    this.showCancelPenaltyModal();
    this.logApplyPenalty();
  };

  onCheckedChange = check => {
    if (!check) {
      Keyboard.dismiss();
    }
    this.setState({
      isApplyRefundPenalty: check,
      ...this.getInitialMessage(check),
    });
    this.logApplyPenalty();
  };

  unCheckedChange = () => {
    const { isApplyRefundPenalty } = this.state;
    this.onCheckedChange(!isApplyRefundPenalty);
  };

  setRefundPenaltyAmount = refundAmount => {
    const { resCancelFee } = this.props;
    const { amount } = resCancelFee;
    let writeAmount = refundAmount;
    let penaltyAmount = refundAmount;
    const isNeedRound =
      `${penaltyAmount}`.substring(penaltyAmount.indexOf('.') + 1).length > 2;

    if (isNeedRound) {
      penaltyAmount = Utils.toFixed(penaltyAmount, 2);
    }
    if (penaltyAmount > 0) {
      writeAmount = penaltyAmount;
    }

    let errorMessage = '';
    let refundInfo = '';
    if (penaltyAmount >= 0) {
      if (penaltyAmount > amount) {
        errorMessage = Texts.lossyAmountValidateErrorRange;
      } else if (penaltyAmount > 0) {
        refundInfo = lossyAmountInfo(`${Texts.rmb}${penaltyAmount}`);
      } else {
        errorMessage = Texts.lossyAmountValidateError;
      }

      this.setState({
        refundPenaltyAmount: `${writeAmount}`,
        refundPenaltyError: errorMessage,
        refundPenaltyDesc: refundInfo,
      });
    }
  };

  onChangeAmount = refundAmount => {
    this.setRefundPenaltyAmount(refundAmount);
  };

  lossyInputRefHandle = ref => {
    this.lossyInputRef = ref;
  };

  // 取消订单展示的订单总额为订单详情页展示的订单金额
  getAmountText = () => {
    const { orderBaseInfo, isdFeeInfo } = this.props;
    const { totalAmount, salesAmount } = isdFeeInfo;
    const { orderType, payMode } = orderBaseInfo;
    const amountText =
      payMode === 12 || orderType === 1 ? salesAmount : totalAmount;
    return amountText;
  };

  logClick = clickKey => {
    const { orderBaseInfo } = this.props;
    const { orderId } = orderBaseInfo;
    CarLog.LogCode({
      name: clickKey,
      info: {
        orderId: `${orderId}`,
      },
    });
  };

  goReBooking = () => {
    const { reBooking } = this.props;
    this.logClick('点击_订单取消页_无车去重订');
    reBooking(true);
  };

  osdReBook = () => {
    DeviceEventEmitter.emit(EventName.orderToRebookNotice, 'rebook');
  };

  openList = () => {
    const { goNewHomeParams } = this.props;
    const baseUrl = Platform.CAR_CROSS_URL.MARKETLIST.OSD;
    const { rentalLocation, rentalDate } = goNewHomeParams || {};
    const rentalDateToList = {
      pickUp: {
        dateTime: dayjs(rentalDate?.pickup).format('YYYYMMDDHHmmss'),
      },
      dropOff: {
        dateTime: dayjs(rentalDate?.dropoff).format('YYYYMMDDHHmmss'),
      },
    };
    const data = {
      rentalDate: rentalDateToList,
      rentalLocation,
    };
    const url = `${baseUrl}${Platform.NewHomeParams()}${Platform.NewHomeTabAbInfo()}&data=${encodeURIComponent(
      JSON.stringify(data),
    )}&filters=${encodeURIComponent(
      JSON.stringify(this.homeSelectedFilters),
    )}&isHomeCombine=true`;
    xRouter.navigateTo({ url });
  };

  handleSearchPanelModalRef = ref => {
    this.searchPanelModalRef = ref;
  };

  setLocationAndDatePopIsShow = data => {
    this.setState({
      locationDatePopVisible: data?.visible,
    });
  };

  tipOnPressHandle = (code, isReBookCode, secondCancelReason) => {
    const {
      cancelReasonTipAboutDeposit,
      setDepositDetailModalVisible,
      orderBaseInfo,
      isEhi,
      supportModifyOrder,
      isNewOsdModifyOrder,
      osdModifyOrderInit,
    } = this.props;
    const { orderId } = orderBaseInfo || {};
    if (isReBookCode) {
      // 重新预订
      this.goReBooking();
      return;
    }
    const urlLink = secondCancelReason?.tip?.url;
    switch (code) {
      case CancelReasonCode.changeOrder:
        if (!supportModifyOrder) {
          this.goReBooking();
        } else {
          // 修改订单
          AppContext.PageInstance.push(Channel.getPageId().ModifyOrder.EN, {
            orderId,
            isEhi,
          });
        }
        break;
      case CancelReasonCode.unFreeDeposit:
        if (cancelReasonTipAboutDeposit?.canDeposit) {
          // 弹免押政策弹窗
          this.pop();
          setDepositDetailModalVisible({ visible: true });
        }
        break;
      case CancelReasonCode.WithoutCertificate:
        // 联系门店
        this.setState({
          isShowPhoneModalVisible: true,
          showPhoneModalType: CustomerPhoneModalType.FooterBar,
        });
        break;
      case CancelReasonCode.OsdChangeOrder:
      case CancelReasonCode.OsdTripCancellation:
        if (isNewOsdModifyOrder) {
          osdModifyOrderInit();
          this.setState({ locationDatePopVisible: true });
          CarLog.LogCode({ name: '点击_取消订单_修改订单' });
        } else {
          // 取消重订
          this.isClickCancelToRebook = true;
          if (code === CancelReasonCode.CreditCardProblems) {
            if (
              secondCancelReason?.code ===
              CancelReasonCode.UnionPayDualCardsNotSupported
            ) {
              this.homeSelectedFilters = ['CreditCard_SupportUnionLogoNew'];
            } else if (
              secondCancelReason?.code === CancelReasonCode.CardsKind
            ) {
              this.homeSelectedFilters = [];
            }
          }
          this.switchCancelConfirm(true);
        }
        break;
      case CancelReasonCode.CreditCardProblems:
      case CancelReasonCode.UnCrossTheBorder:
        // 取消重订
        this.isClickCancelToRebook = true;
        if (code === CancelReasonCode.CreditCardProblems) {
          if (
            secondCancelReason?.code ===
            CancelReasonCode.UnionPayDualCardsNotSupported
          ) {
            this.homeSelectedFilters = ['CreditCard_SupportUnionLogoNew'];
          } else if (secondCancelReason?.code === CancelReasonCode.CardsKind) {
            this.homeSelectedFilters = [];
          }
        }
        this.switchCancelConfirm(true);
        break;
      case CancelReasonCode.LicenseDocumentIssues:
        // 去办理 跳转驾照翻译件页面
        if (urlLink) {
          xRouter.navigateTo({ url: urlLink });
        }
        break;
      default:
        break;
    }
  };

  getTit = () => {
    const { isApplyRefundPenalty } = this.state;
    if (this.isOSDNewOrderCancel()) {
      return {
        tit: Texts.cancelReason,
        desc: Texts.osdCancelTipDesc,
      };
    }
    return isApplyRefundPenalty
      ? {
          tit: Texts.giveCancelReason,
          desc: Texts.giveCancelReasonDesc,
        }
      : {
          tit: Texts.whyCancel,
          desc: Texts.whyCancelDesc,
        };
  };

  getRefundProps = () => {
    const { resCancelFee, orderBaseInfo } = this.props;
    const {
      refundPenaltyAmount,
      isApplyRefundPenalty,
      refundPenaltyDesc,
      refundPenaltyError,
    } = this.state;
    return {
      orderId: orderBaseInfo.orderId,
      amount: resCancelFee?.amount,
      amountText: this.getAmountText(),
      isApplyRefundPenalty,
      refundPenaltyAmount,
      refundPenaltyDesc,
      refundPenaltyError,
      unCheckedChange: this.unCheckedChange,
      onCheckedChange: this.onCheckedChange,
      clickApplyPenalty: this.clickApplyPenalty,
      onChangeAmount: this.onChangeAmount,
      lossyInputRefHandle: this.lossyInputRefHandle,
    };
  };

  onInputChangeCallBack = text => {
    this.inputValue = text;
  };

  checkPolicyChangeTip = () => {
    const changeReminder =
      this.props.orderCancelInfo?.lossDetail?.changeReminder;
    let timer = null;
    const checkStatus = () => {
      if (changeReminder && +new Date() > changeReminder?.changeTipTime) {
        this.setState({
          isShowCancelPolicyChangeTip: true,
        });
        const animateTip = xCreateAnimation({
          duration: 100,
        });
        animateTip.height(getPixel(68)).step();
        this.setState({
          animateTipHeight: animateTip.export(),
        });
        clearInterval(timer);
        timer = null;
      }
    };
    timer = setInterval(() => {
      checkStatus();
    }, 5000);
    checkStatus();
  };

  getCancelTip = () => {
    const {
      orderCancelInfo,
      isPenaltyChange,
      penaltyChangeCancelTip = {},
    } = this.props;
    const { isShowCancelConfirm } = this.state;

    const { cancelTip: newCancelTip = {} } = orderCancelInfo || {};
    let cancelTip: any = this.isRetainOrder() ? newCancelTip : {};
    if (isPenaltyChange && isShowCancelConfirm && this.isOSDNewOrderCancel()) {
      cancelTip = penaltyChangeCancelTip;
    }
    cancelTip.newTitle = cancelTip?.title;
    if (this.isClickCancelToRebook) {
      cancelTip.newTitle = cancelTip.rebookingTitle;
    }
    return cancelTip;
  };

  renderCancelConfirmTitle = titleStyle => {
    const { newTitle, titleSuppl } = this.getCancelTip() || {};
    const titleArr = newTitle?.split('{tag}');
    if (titleArr?.length > 1) {
      // 兼容'xxx{tag}yyy'
      const { color: iColor, text } = titleSuppl || {};
      const iTitleArr = [titleArr[0], text, titleArr[1]];
      return (
        <TitleWithTag
          titleStyle={titleStyle}
          titleArr={iTitleArr}
          tagStyle={{ color: iColor }}
        />
      );
    }
    return null;
  };

  renderCancelConfirmBtn = () => {
    return [
      {
        get name() {
          return Texts.orderCancelCancelBtn;
        },
        onPress: () => {
          this.switchCancelConfirm(false);
          CarLog.LogCode({ name: '点击_订单详情页_二次确认弹窗我再想想' });
        },
      },
      {
        get name() {
          return Texts.order_cancelOrder;
        },
        isPrimary: true,
        onPress: () => {
          this.toCancelBook();
          CarLog.LogCode({ name: '点击_订单详情页_二次确认弹窗取消订单' });
        },
      },
    ];
  };

  // 全程保留订单项目-取消订单拦截弹窗-按钮
  renderCancelOrderConfirmBtn = () => {
    const {
      cancelOrderSubmitId,
      setOrderModalsVisible,
      setCancelOrderSubmitId,
      fetchOrder2,
    } = this.props;
    // 弱拦截
    if (cancelOrderSubmitId) {
      return [
        {
          get name() {
            return Texts.orderCancelCancelBtn; // 再想想
          },
          onPress: () => {
            // 关闭弹窗
            setOrderModalsVisible({
              cancelOrderConfirmModal: {
                visible: false,
              },
            });
            // 清空submitId
            setCancelOrderSubmitId('');
          },
        },
        {
          get name() {
            return Texts.lossyConfirmSureBtn; // 确认取消订单
          },
          isPrimary: true,
          onPress: () => {
            // 关闭弹窗
            setOrderModalsVisible({
              cancelOrderConfirmModal: {
                visible: false,
              },
            });
            // 取消订单
            this.toCancelBook();
          },
        },
      ];
    }
    // 强拦截
    return [
      {
        get name() {
          return Texts.gotIt; // 知道了
        },
        isPrimary: true,
        onPress: () => {
          // 关闭弹窗
          setOrderModalsVisible({
            cancelOrderConfirmModal: {
              visible: false,
            },
          });
          // 刷新订详
          fetchOrder2();
          // 回退
          this.pop();
        },
      },
    ];
  };

  pageDidAppear() {
    const { orderBaseInfo } = this.props;
    if (
      [OrderStatusCtrip.CANCELLED, OrderStatusCtrip.CANCELLING].includes(
        orderBaseInfo?.orderStatusCtrip,
      )
    ) {
      this.pop();
    }
  }

  pageDidDisappear() {
    this.homeSelectedFilters = null;
    this.isClickCancelToRebook = false;
  }

  closeOsdModifyOrderModal = () => {
    this.setState({
      locationDatePopVisible: false,
    });
    const { isCross } = this.props;
    // 新版订单详情页不会退取消订单页，防止进入列表页后会退页面，(新版订详&取消订单页不属于同一容器引起)
    if (!isCross) {
      this.pop();
    }
  };

  renderPage() {
    const {
      resCancelFee,
      cancelModalVisible,
      orderBaseInfo,
      cancelRuleInfo,
      fetchQueryCancelFee,
      menuList,
      orderCancelInfo,
      cancelReasonTipAboutDeposit,
      supportModifyOrder,
      isFetchCancelInfoLoading,
      isPenaltyChange,
      penaltyChangeTip,
      isNewCancelRule,
      modalsVisible,
      isNewOsdModifyOrder,
      osdModifyOrderNote,
    } = this.props;
    // eslint-disable-next-line
    const {
      reasons,
      isShowCancelConfirm,
      isShowCancelPolicyModal,
      isShowApplyPenaltyConfirm,
      isShowCancelPenaltyModal,
      isShowPhoneModalVisible,
      refundPenaltyAmount,
      isApplyRefundPenalty,
      isShowNoReasonError,
      animatedHeight,
      showPhoneModalType,
      isShowCancelPolicyChangeTip,
      animateTipHeight,
      locationDatePopVisible,
    } = this.state;
    const { amount, canRefund, confirmMsg, auditTime } = resCancelFee;
    const supportReBook = this.supportReBook();
    if (!orderBaseInfo) return false;
    const { orderId } = orderBaseInfo || {};
    const currencyStyle = {
      ...font.title1MediumStyle,
      color: color.orangeBase,
    };
    const bgWhite = { backgroundColor: color.white };
    const isFreeCancel = amount === 0;
    const amountText = this.getAmountText();
    const refundAmount = Utils.Sub(amountText, amount);
    const { lossDetail } = orderCancelInfo || {};
    const { title, newTitle, desc } = this.getCancelTip() || {};
    const titleObj = this.getTit();
    const refundProps = this.getRefundProps();
    const showApplyRefund = isIsd && canRefund && !isFreeCancel;
    return (
      <>
        <BbkHeadFootModal
          visible={cancelModalVisible}
          onCancel={this.closeModal}
          isModal={false}
          headerProps={{
            get title() {
              return Texts.order_cancelOrder;
            },
            contentStyle: layout.flexCenter,
            styleInner: styles.titin,
            titleStyle: styles.titleStyle,
            subtitleColor: color.fontSubDark,
            leftIcon: icon.back,
          }}
          closeModalBtnTestID={UITestID.car_testid_page_cancel_closemask}
          leftIconTestID={UITestID.car_testid_page_cancel_header_lefticon}
          footerChildren={this.getFooter()}
          contentStyle={{
            paddingLeft: 0,
            paddingRight: 0,
            backgroundColor: color.white,
          }}
          isScrollView={false}
        >
          {/* 取消政策变化提前提醒模块 */}
          {isIsd &&
            isShowCancelPolicyChangeTip &&
            lossDetail?.changeReminder && (
              <XAnimated.View
                style={{ height: 0 }}
                animation={animateTipHeight}
              >
                <WarningTips
                  waringInfo={mapChangeReminderToWaringInfo(
                    lossDetail?.changeReminder,
                  )}
                  onClick={this.showCancelPolicyModal}
                />
              </XAnimated.View>
            )}
          <KeyboardAwareScrollView
            ref={v => {
              // @ts-ignore
              this.scrollerView = v;
            }}
            enableResetScrollToCoords={false}
            scrollEventThrottle={60}
            keyboardDismissMode="on-drag"
            keyboardShouldPersistTaps="never"
            automaticallyAdjustContentInsets={false}
            keyboardOpeningTime={100}
            extraHeight={100}
            // @ts-ignore
            extraScrolHeight={50}
            enableOnAndroid={true}
            style={{ backgroundColor: color.white }}
          >
            {isFetchCancelInfoLoading ? (
              <BbkSkeletonLoading
                pageName={PageType.CancelInfo}
                visible={true}
              />
            ) : (
              <>
                {this.isRetainOrder() && (
                  <>
                    {/* 国内全程保留订单项目顶部提示 */}
                    {isIsd && !!lossDetail?.cancelPageTopTip && (
                      <View className={c2xStyles.tipWrap}>
                        <CText
                          className={c2xStyles.tipText}
                          fontWeight="medium"
                        >
                          {lossDetail?.cancelPageTopTip}
                        </CText>
                      </View>
                    )}
                    <View
                      className={classNames(
                        c2xStyles.wrap,
                        isIsd &&
                          !!lossDetail?.cancelPageTopTip &&
                          c2xStyles.pt18,
                      )}
                    >
                      <CancelTopBlock
                        {...lossDetail}
                        showCancelPolicyModal={this.showCancelPolicyModal}
                      >
                        {showApplyRefund && (
                          <ApplyForRefund
                            {...refundProps}
                            style={styles.mb24}
                          />
                        )}
                      </CancelTopBlock>
                    </View>
                  </>
                )}
              </>
            )}
            {!this.isRetainOrder() && (
              <View style={bgWhite}>
                <View className={c2xStyles.content}>
                  <CText className={c2xStyles.txt}>
                    您将要取消该订单，请留意可能存在的违约费用
                  </CText>
                  <View
                    style={xMergeStyles([
                      layout.betweenHorizontal,
                      styles.cancelFee,
                    ])}
                  >
                    <CText style={font.title2MediumStyle} fontWeight="medium">
                      取消费用
                    </CText>
                    <BbkCurrencyFormatter
                      currency="¥"
                      price={amount}
                      currencyStyle={currencyStyle}
                      priceStyle={currencyStyle}
                    />
                  </View>
                </View>
              </View>
            )}

            <View
              className={c2xStyles.content}
              style={xMergeStyles([
                bgWhite,
                styles.mgTop,
                { borderTopColor: color.grayBg },
              ])}
            >
              {this.isRetainOrder() ? (
                <>
                  <CText
                    className={c2xStyles.cancelReasonTitle}
                    fontWeight="medium"
                  >
                    {titleObj.tit}
                  </CText>
                  <CText className={c2xStyles.cancelReasonDesc}>
                    {titleObj.desc}
                  </CText>
                </>
              ) : (
                <CText
                  className={c2xStyles.osdCancelReasonTitle}
                  fontWeight="medium"
                >
                  取消原因
                </CText>
              )}

              {isShowNoReasonError && (
                <View className={c2xStyles.cancelOrderError}>
                  <CText type="icon" className={c2xStyles.infoIcon}>
                    {icon.circleIFilled}
                  </CText>
                  <CText className={c2xStyles.error}>
                    {Texts.cancelOrderValidateError}
                  </CText>
                </View>
              )}
              <ReasonBox
                data={reasons}
                onSelect={this.onReasonSelect}
                animatedHeight={animatedHeight}
                btnPressHandle={this.tipOnPressHandle}
                cancelReasonTipAboutDeposit={cancelReasonTipAboutDeposit}
                supportReBook={supportReBook}
                supportModifyOrder={supportModifyOrder}
                pressSecondReasonHandle={this.pressSecondReasonHandle}
                isApplyRefundPenalty={isApplyRefundPenalty}
                isOSDNewOrderCancel={this.isOSDNewOrderCancel()}
                isRetainOrder={this.isRetainOrder()}
                isNewOsdModifyOrder={isNewOsdModifyOrder}
              />

              <View className={c2xStyles.inputWrap}>
                <Input onChangeCallBack={this.onInputChangeCallBack} />
              </View>
            </View>
          </KeyboardAwareScrollView>
        </BbkHeadFootModal>
        <OrderCancelConfirmModal
          visible={isShowCancelConfirm}
          title={newTitle || '取消订单后不可恢复，是否继续'}
          contentText={desc?.length ? desc : confirmMsg}
          renderTitle={this.renderCancelConfirmTitle}
          btns={this.renderCancelConfirmBtn()}
          exposeTestID={CarLog.LogExposure({
            name: '曝光_订单详情页_二次确认弹窗',

            info: {
              defaultAmount: amount,
            },
          })}
        />

        {/* 全程保留订单项目-取消订单确认弹窗 */}
        {isIsd && (
          <OrderCancelConfirmModal
            visible={modalsVisible.cancelOrderConfirmModal.visible}
            contentText={
              modalsVisible.cancelOrderConfirmModal.data?.contentText
            }
            btns={this.renderCancelOrderConfirmBtn()}
          />
        )}
        <ApplyPenaltyConfirmModal
          visible={isShowApplyPenaltyConfirm}
          title={title || Texts.lossyConfirmTitle}
          renderTitle={this.renderCancelConfirmTitle}
          contentText={
            desc?.length
              ? desc
              : [lossyConfirmContentFirst(`${Texts.rmb}${refundAmount}`)]
          }
          description={lossyConfirmContentSecond(
            `${Texts.rmb}${Number(refundPenaltyAmount)}`,
          )}
          btns={this.applyPenaltyConfirmBtn}
        />

        {this.isRetainOrder() && (
          <CancelPolicyModal
            isHideButton={!isPenaltyChange}
            onHide={this.hideCancelPolicyModal}
            modalVisible={isShowCancelPolicyModal}
            cancelRuleInfo={cancelRuleInfo}
            orderBaseInfo={orderBaseInfo}
            fetchQueryCancelFee={fetchQueryCancelFee}
            isPenaltyChange={isPenaltyChange}
            penaltyChangeTip={penaltyChangeTip}
            isNewCancelRule={Utils.isCtripOsd() && isNewCancelRule}
          />
        )}
        {isIsd && (
          <CancelPenaltyModal
            orderId={`${orderId}`}
            auditTime={auditTime}
            onHide={this.hideCancelPenaltyModal}
            onAgreeApply={this.agreeApply}
            modalVisible={isShowCancelPenaltyModal}
          />
        )}

        <OrderCustomerPhoneModalContainer
          // @ts-ignore 已定义，提示未找到
          menuList={menuList}
          type={showPhoneModalType}
          modalVisible={isShowPhoneModalVisible}
          onRequestClose={this.hidePhoneModal}
        />

        {/* 出境修改订单弹层 */}
        {Utils.isCtripOsd() && (
          <OsdModifyOrderModal
            visible={locationDatePopVisible}
            setLocationAndDatePopIsShow={this.setLocationAndDatePopIsShow}
            searchPanelModalRefFn={this.handleSearchPanelModalRef}
            currentPageId={this.getPageId()}
            orderId={orderId}
            osdModifyOrderNote={osdModifyOrderNote}
            pressSearchCallback={this.closeOsdModifyOrderModal}
          />
        )}
      </>
    );
  }
}
export default withTheme(Cancel);
