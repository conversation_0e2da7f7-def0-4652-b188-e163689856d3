import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useMemo } from 'react';
import { XView as View } from '@ctrip/xtaro';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkModalHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalHeader/src/Index';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './noLimitModalC2xStyles.module.scss';
import BbkHalfPageModal from '../../../../ComponentBusiness/HalfPageModal';
import { UITestID } from '../../../../Constants/Index';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  mainWrap: {
    backgroundColor: color.white,
    width: BbkUtils.vw(100),
    borderTopLeftRadius: BbkUtils.getPixel(30, 'floor'),
    borderTopRightRadius: BbkUtils.getPixel(30, 'floor'),
    paddingBottom: BbkUtils.fixIOSOffsetBottom() + getPixel(16),
  },
  headWrap: {
    backgroundColor: color.white,
  },
  leftIcon: {
    paddingLeft: getPixel(0),
    height: getPixel(88),
    width: getPixel(88),
  },
  container: {
    paddingTop: 0,
    paddingBottom: 0,
    paddingLeft: 0,
    paddingRight: 0,
    backgroundColor: color.white,
    minHeight: BbkUtils.vh(25),
    maxHeight: BbkUtils.vh(90),
    borderTopLeftRadius: BbkUtils.getPixel(30, 'floor'),
    borderTopRightRadius: BbkUtils.getPixel(30, 'floor'),
  },
});

interface IPropsType {
  visible: boolean;
  content?: string;
  onClose: () => void;
}

const NoLimitModal: React.FC<IPropsType> = ({ visible, content, onClose }) => {
  const pageModalProps = useMemo(
    () => ({
      visible,
      onMaskPress: onClose,
    }),
    [visible, onClose],
  );

  return (
    <BbkHalfPageModal
      pageModalProps={pageModalProps}
      headerDom={<View />}
      contentStyle={styles.container}
      style={styles.container}
      closeModalBtnTestID={
        UITestID.car_testid_page_vendorlist_nolimit_modal_closemask
      }
    >
      <View style={styles.mainWrap}>
        <BbkModalHeader
          hasTopBorderRadius={true}
          showRightIcon={false}
          showLeftIcon={true}
          onClose={onClose}
          leftIconWrapStyle={styles.leftIcon}
          leftIconTestID={
            UITestID.car_testid_page_vendorlist_lessdetail_modal_lefticon
          }
          style={styles.headWrap}
        >
          <View className={c2xStyles.titleTextWrp}>
            <BbkText className={c2xStyles.titleText} fontWeight="medium">
              不限行说明
            </BbkText>
          </View>
        </BbkModalHeader>
        <View className={c2xStyles.descWrap}>
          {!!content && (
            <BbkText className={c2xStyles.mainDesc}>{content}</BbkText>
          )}
        </View>
      </View>
    </BbkHalfPageModal>
  );
};

export default NoLimitModal;
