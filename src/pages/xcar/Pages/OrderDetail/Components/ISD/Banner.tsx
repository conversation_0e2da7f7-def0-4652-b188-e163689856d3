/* eslint-disable react/require-default-props */
import React from 'react';
import { XImage as Image } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import URL from '@c2x/apis/URL';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src/index';
import { CarLog } from '../../../../Util/Index';

const { getPixel } = BbkUtils;

const styles = StyleSheet.create({
  card: {
    alignItems: 'center',
    justifyContent: 'center',
    // marginTop: getPixel(16),
    marginBottom: getPixel(16),
  },
  bg: {
    width: getPixel(710),
    height: getPixel(78),
    borderRadius: getPixel(12),
  },
});

const Banner = ({ qiWeiConfig, orderStatus }) => {
  const qiWeiLanding = qiWeiConfig?.[0]?.h5Url;
  const onPress = useMemoizedFn(() => {
    URL?.openURL(qiWeiLanding);
    CarLog.LogCode({
      name: '国内_订详页_企微组件_点击',
      info: { WeComLabel: qiWeiLanding },
    });
  });
  return (
    <Touchable
      debounce={true}
      activeOpacity={0.9}
      style={styles.card}
      onPress={onPress}
      testID={CarLog.LogExposure({
        name: '国内_订详页_企微组件_曝光',
        orderStatus: orderStatus,
        info: { WeComLabel: qiWeiLanding },
      })}
    >
      <Image src={qiWeiConfig?.[0]?.imgUrl} style={styles.bg} />
    </Touchable>
  );
};

export default Banner;
