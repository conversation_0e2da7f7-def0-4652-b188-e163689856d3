@import '../../Common/src/Tokens/tokens/color.scss';

.fixBgStyle {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
}
.bgImg {
  width: 100vw;
  height: 180px;
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
}
.fixBgLinear {
  position: absolute;
  top: 180px;
  left: 0px;
  right: 0px;
  bottom: 0px;
}
.activityContent {
  margin-bottom: 40px;
}
.activityItem {
  margin-bottom: 16px;
}
.activityTitle {
  font-size: 30px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  margin-bottom: 16px;
  color: $fontPrimary;
}
.activityContentTxt {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-bottom: 16px;
  color: $fontPrimary;
}
.subTitle {
  font-size: 28px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  margin-bottom: 6px;
  color: $fontPrimary;
}
.contentText {
  font-size: 28px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-bottom: 24px;
  color: $fontPrimary;
}
