@import '../../Common/src/Tokens/tokens/color.scss';

.slideBottomEmpty {
  flex: 1;
}
.wrap {
  width: 100vw;
  background-color: $white;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
}
.modalWrap {
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 0px;
  padding-bottom: 0px;
  line-height: 42px;
  min-height: 20%;
}
.content {
  padding-top: 32px;
  padding-bottom: 32px;
  padding-left: 32px;
  padding-right: 32px;
  background-color: $white;
  flex-grow: 1;
  flex-shrink: 0;
}
.tipWrap {
  flex-direction: row;
}
.refundPenaltyTip {
  font-size: 28px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
}
.helpIcon {
  font-size: 30px;
  color: $grayDescLine;
  line-height: 36px;
  margin-left: 5px;
}
.inputLeft {
  margin-right: 8px;
  font-size: 28px;
  line-height: 38px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.validateWrap {
  margin-top: 16px;
  min-height: 80px;
}
.error {
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $refundPenaltyError;
}
.info {
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $grayBase;
}
.IntegralModalFooterWrap {
  width: 100vw;
  padding-top: 16px;
  padding-bottom: 16px;
  padding-left: 32px;
  padding-right: 32px;
}
