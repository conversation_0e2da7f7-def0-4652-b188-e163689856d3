@import '../../Common/src/Tokens/tokens/color.scss';

.wayDot {
  width: 12px;
  height: 12px;
  border-radius: 12px;
  background-color: $blueBase;
}
.wayText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.wayTitle {
  margin-left: 20px;
  margin-right: 10px;
}
.wayDesc {
  margin-right: 10px;
}
.wayAddressDesc {
  margin-left: 66px;
  margin-top: 4px;
}
.nationalChainWrap {
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
  margin-left: 8px;
  background-color: $nationalChainTagBackgroundColor;
}
.nationalChainText {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $nationalChainTagTextColor;
}
.dashLineImg {
  height: 1px;
  margin-top: 24px;
}
.limitRulesIcon {
  font-size: 28px;
  margin-right: 8px;
  color: $refundFaile;
}
.limitRulesIconRight {
  font-size: 25px;
  margin-left: 8px;
}
.vendorWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
}
.vendorIconRight {
  font-size: 22px;
  color: $grayDescLine;
  margin-left: 14px;
}
.wrapper {
  background-color: $white;
  margin-left: 16px;
  margin-right: 16px;
  border-radius: 16px;
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 40px;
  padding-bottom: 24px;
}
.vehicleWrap {
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 16px;
}
.vehicleNameText {
  font-size: 36px;
  line-height: 48px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.hotImage {
  width: 32px;
  height: 32px;
  margin-left: 8px;
}
.lessImg {
  width: 156px;
  height: 32px;
  margin-left: 8px;
}
.timeText {
  font-size: 24px;
  line-height: 34px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.fulfillmentModifyRemind {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  background-color: #FFF8F2;
  border-radius: 8px;
  margin-top: 24px;
  padding-top: 16px;
  padding-bottom: 16px;
  padding-left: 32px;
  padding-right: 32px;
}
.fulfillmentModifyText {
  flex: 1;
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: #FF7700;
}
.fulfillmentModifyIcon {
  line-height: 36px;
}
.fulfillmentModifyInfoIcon {
  color: #FF7700;
  margin-right: 8px;
}
.fulfillmentModifyRightIcon {
  color: #666666;
}
.advanceReturnTip {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: rgba($blueBase, 0.08);
  border-radius: 12px;
  padding-left: 16px;
  padding-right: 16px;
  margin-top: 8px;
  flex-shrink: 1;
}
.returnTipIcon {
  width: 50px;
  height: 50px;
}
.returnTipText {
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-left: 6px;
  color: $fontPrimary;
}
.returnTipIconRight {
  font-size: 24px;
  margin-left: 6px;
  color: $fontSecondary;
}
.wayWrapper {
  margin-top: 16px;
  margin-bottom: 24px;
  z-index: -1;
}
.mapBackgroundImg {
  width: 378px;
  height: 198px;
  position: absolute;
  right: -32px;
  bottom: 0px;
}
.mapIconWrap {
  align-items: center;
  justify-content: center;
  margin-left: 44px;
}
.mapIconTextNew {
  color: $deepBlueBase;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.mapIconText {
  color: $blueBase;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.tagsWrap {
  flex-direction: row;
  flex-wrap: wrap;
}