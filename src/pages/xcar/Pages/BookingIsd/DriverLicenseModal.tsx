import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useMemo } from 'react';
import { XView as View, xClassNames as classNames } from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkModalHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalHeader/src/Index';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { getProductTraceData } from '../../State/Product/Method';
import c2xStyles from './driverLicenseModalC2xStyles.module.scss';
import BbkHalfPageModal from '../../ComponentBusiness/HalfPageModal';
import { IInputType } from '../../ComponentBusiness/BookForm/src/Type';
import { IDriverLicenseModalProps, DriverLicenseStatus } from './Types';
import { CarLog, Utils } from '../../Util/Index';
import { UITestID } from '../../Constants/Index';
import Texts from './Texts';
import c2xCommonStyles from '../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, vh, fixIOSOffsetBottom } = BbkUtils;
const styles = StyleSheet.create({
  wrap: {
    minHeight: vh(28),
  },
  contentWrap: {
    paddingTop: getPixel(0),
    maxHeight: vh(80),
    marginLeft: -getPixel(32),
    marginRight: -getPixel(32),
    backgroundColor: color.transparent,
  },
  titleWrap: {
    textAlign: 'center',
    marginRight: getPixel(32),
    minHeight: getPixel(98),
    backgroundColor: color.transparent,
  },
  title: {
    textAlign: 'center',
    marginRight: getPixel(32),
    ...font.title4MediumStyle,
  },
  iconWrap: {
    minHeight: getPixel(88),
    paddingRight: getPixel(32),
    zIndex: 1,
  },
  container: {
    paddingBottom: fixIOSOffsetBottom(0),
  },
  driverLicenseItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: getPixel(32),
    paddingTop: getPixel(24),
    paddingBottom: getPixel(24),
    paddingRight: getPixel(32),
    borderBottomColor: color.grayBorder,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
});

const DriverLicenseModal = (props: IDriverLicenseModalProps) => {
  const {
    driverLicenseItems,
    driverLicenseGeoInfo,
    visible,
    passenger,
    changeFormData = Utils.noop,
    handleClose = Utils.noop,
    selectCurDriverLicense = Utils.noop,
  } = props;
  const logInfo = getProductTraceData();
  const isOnlyOne = driverLicenseItems?.length === 1;
  const pageModalProps = useMemo(
    () => ({
      visible,
      onMaskPress: handleClose,
    }),
    [visible, handleClose],
  );

  const headerDom = (
    <BbkModalHeader
      hasTopBorderRadius={true}
      hasBottomBorder={false}
      onClose={pageModalProps.onMaskPress}
      titleStyle={styles.title}
      style={styles.titleWrap}
      leftIconWrapStyle={styles.iconWrap}
      title={Texts.driverLicenseType}
    />
  );

  return (
    <BbkHalfPageModal
      style={styles.wrap}
      pageModalProps={pageModalProps}
      contentStyle={styles.contentWrap}
      closeModalBtnTestID={
        UITestID.car_testid_page_booking_driverLicense_modal_closemask
      }
      headerDom={headerDom}
    >
      <View style={styles.container}>
        {driverLicenseItems?.map(item => {
          const { code, title, tips, statuses, areaCode } = item || {};
          const isAble = statuses === DriverLicenseStatus.Able;
          const Container = isAble ? Touchable : View;
          return (
            !!code &&
            !!title && (
              <View key={code}>
                <Container
                  style={styles.driverLicenseItem}
                  debounce={true}
                  onPress={() => {
                    handleClose();
                    selectCurDriverLicense(item);
                    changeFormData([
                      {
                        type: IInputType.driverLicense,
                        error: false,
                        value: `${areaCode}|${code}`,
                      },
                      {
                        type: IInputType.driverLicenseName,
                        error: false,
                        value: title,
                      },
                    ]);
                    CarLog.LogCode({
                      name: '点击_填写页_驾照类型选择',

                      info: {
                        ...logInfo,
                        nationality: passenger?.nationality,
                        driverLicenseCountry: areaCode,
                        driverLicenseType: code,
                        driverAge: passenger?.age,
                      },
                    });
                  }}
                  testID={`${UITestID.car_testid_page_booking_driverLicense_modal_license_item}_${title}`}
                >
                  <View>
                    {!!title && (
                      <BbkText
                        className={classNames(
                          c2xCommonStyles.c2xTextDefaultCss,

                          isAble ? c2xStyles.text : c2xStyles.unAbleText,
                        )}
                      >
                        {title}
                      </BbkText>
                    )}
                    {!!tips && !isOnlyOne && (
                      <View className={c2xStyles.tipsWrap}>
                        <BbkText type="icon" className={c2xStyles.icon}>
                          {icon.circleI}
                        </BbkText>

                        <BbkText className={c2xStyles.tipText}>{tips}</BbkText>
                      </View>
                    )}
                  </View>
                  {code === driverLicenseGeoInfo &&
                    statuses === DriverLicenseStatus.Able && (
                      <BbkText type="icon" className={c2xStyles.selectedIcon}>
                        {icon.tick}
                      </BbkText>
                    )}
                </Container>
                {!!tips && isOnlyOne && (
                  <View className={c2xStyles.tipsWrapOne}>
                    <BbkText type="icon" className={c2xStyles.icon}>
                      {icon.circleI}
                    </BbkText>

                    <BbkText className={c2xStyles.tipText}>{tips}</BbkText>
                  </View>
                )}
              </View>
            )
          );
        })}
      </View>
    </BbkHalfPageModal>
  );
};
export default DriverLicenseModal;
