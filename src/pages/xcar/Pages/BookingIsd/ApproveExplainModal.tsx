import ScrollView from '@c2x/components/ScrollView';
import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import LoadingView from '@c2x/components/LoadingView';
import React, { useMemo, memo, CSSProperties } from 'react';
import { font, color, tokenType } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkComponentButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './approveExplainModalC2xStyles.module.scss';
import BbkHalfPageModal from '../../ComponentBusiness/HalfPageModal';
import {
  ContentsType2,
  CarRentalDepositType,
} from '../../ComponentBusiness/Common/src/ServiceType/src/queryProductInfo';
import texts from './Texts';
import { UITestID } from '../../Constants/Index';
import { Utils } from '../../Util/Index';

export interface ApproveExplainType {
  title?: ContentsType2;
  items?: Array<CarRentalDepositType>;
}

interface IApproveExplainModal {
  visible: boolean;
  loading?: boolean;
  onMaskPress: () => void;
  onPressBtn?: (check: boolean) => void;
  style: CSSProperties;
  data?: ApproveExplainType;
  hasFooterBtn?: boolean;
  buttonName?: string;
}

const { getPixel, htmlDecode, fixIOSOffsetBottom, vh, vw, getLineHeight } =
  BbkUtils;
const styles = StyleSheet.create({
  modalWrap: {
    paddingBottom: 0,
    paddingTop: 0,
    paddingLeft: 0,
    paddingRight: 0,
    height: vh(60),
    lineHeight: getLineHeight(42),
  },
  content: {
    paddingTop: getPixel(0),
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    paddingBottom: fixIOSOffsetBottom(0),
  },
  mt32: {
    marginTop: getPixel(32),
  },
  btn: {
    width: vw(100) - getPixel(64),
    marginLeft: getPixel(32),
    marginRight: getPixel(32),
    paddingBottom: 0,
  },
  pdb40: {
    paddingBottom: getPixel(40),
  },
  bold: {
    fontWeight: 'bold',
  },
  underline: {
    textDecorationLine: 'underline',
  },
});

enum Style {
  B = 'B',
  U = 'U',
  BU = 'BU',
}

const styleMap = {
  [Style.B]: styles.bold,
  [Style.U]: styles.underline,
  [Style.BU]: xMergeStyles([styles.bold, styles.underline]),
};

const ApproveExplainModal: React.FC<IApproveExplainModal> = memo(
  ({
    visible,
    loading,
    onMaskPress,
    onPressBtn,
    style,
    data,
    hasFooterBtn,
    buttonName,
  }: IApproveExplainModal) => {
    const { title, items } = data || {};
    const subTitle = title?.stringObjs?.[0]?.content || texts.approveExplain;
    const modalHeaderProps = useMemo(
      () => ({
        title: subTitle,
        titleStyle: {
          ...font.title4MediumStyle,
          textAlign: 'center',
          marginRight: getPixel(32),
          lineHeight: getLineHeight(48),
          color: color.fontPrimary,
        },
        leftIconStyle: {
          color: color.fontPrimary,
        },
        hasBottomBorder: false,
      }),
      [subTitle],
    );
    const pageModalProps = {
      visible,
      onMaskPress,
      style,
    };

    // eslint-disable-next-line @typescript-eslint/no-shadow
    const getStyle = (contentStyle, style) =>
      styleMap[style] || styleMap[contentStyle];
    const handlePressBtn = useMemoizedFn(() => {
      onMaskPress();
      if (typeof onPressBtn === 'function') {
        onPressBtn(true);
      }
    });

    return (
      <BbkHalfPageModal
        pageModalProps={pageModalProps}
        style={styles.modalWrap}
        contentStyle={xMergeStyles([
          styles.content,
          hasFooterBtn && styles.pdb40,
        ])}
        modalHeaderProps={modalHeaderProps}
        footerShadow={false}
        closeModalBtnTestID={
          UITestID.car_testid_page_booking_approveexplain_modal_closemask
        }
        footerChildren={
          hasFooterBtn ? (
            <View style={styles.btn}>
              <BbkComponentButton
                text={buttonName || texts.readAndAgree}
                onPress={handlePressBtn}
                colorType={
                  Utils.isCtripOsd()
                    ? tokenType.ColorType.DeepBlue
                    : tokenType.ColorType.Orange
                }
                testID={
                  UITestID.car_testid_page_booking_approveexplain_modal_agree
                }
                buttonType={
                  Utils.isCtripOsd()
                    ? tokenType.ButtonType.Default
                    : tokenType.ButtonType.Gradient
                }
                gradientColorArr={[color.orangeFF8800, color.orangePrice]}
              />
            </View>
          ) : null
        }
      >
        {loading ? (
          <LoadingView />
        ) : (
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{ flexGrow: 1, flexShrink: 1, flexBasis: 0 }}
            testID={UITestID.car_testid_page_booking_approve_explain_modal}
          >
            {items?.map(item => {
              const { title: itemTitle, desc } = item || {};
              return (
                <>
                  {itemTitle?.stringObjs?.map(
                    ({ content, style } = {}) =>
                      !!content && (
                        <BbkText
                          className={c2xStyles.contentText}
                          style={getStyle(itemTitle?.contentStyle, style)}
                        >
                          {htmlDecode(content)}
                        </BbkText>
                      ),
                  )}
                  {desc?.map(({ contentStyle, stringObjs } = {}, index) => {
                    return (
                      stringObjs?.length > 0 && (
                        <View className={index !== 0 && c2xStyles.mt32}>
                          <BbkText>
                            {stringObjs?.map(
                              ({ content, style } = {}) =>
                                !!content && (
                                  <BbkText
                                    className={c2xStyles.contentText}
                                    style={getStyle(contentStyle, style)}
                                  >
                                    {htmlDecode(content)}
                                  </BbkText>
                                ),
                            )}
                          </BbkText>
                        </View>
                      )
                    );
                  })}
                </>
              );
            })}
            {!hasFooterBtn && <View className={c2xStyles.pdView} />}
          </ScrollView>
        )}
      </BbkHalfPageModal>
    );
  },
);

export default ApproveExplainModal;
