import Image from '@c2x/components/Image';
import {
  xMergeStyles,
  XView as View,
  XLinearGradient as LinearGradient,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import * as ImageUrl from '../../Constants/ImageUrl';
import c2xStyles from './bookingHalfPageModalC2xStyles.module.scss';
import BbkHalfPageModal from '../../ComponentBusiness/HalfPageModal';
import { Utils } from '../../Util/Index';
import { UITestID } from '../../Constants/Index';

interface IHalfPageModal {
  visible: boolean;
  onMaskPress: () => void;
  style: CSSProperties;
  title: string;
  content: Array<string>;
  subTitle?: string;
  hasActivityBg?: boolean;
  activityContents?: Array<ActivityType>;
}

interface ActivityType {
  title?: string;
  description?: string;
}

const { getPixel, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  modalWrap: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 0,
    paddingBottom: 0,
    lineHeight: getLineHeight(42),
  },
  content: {
    marginBottom: -getPixel(64),
    paddingTop: 0,
    zIndex: -1,
  },
  hasContent: {
    paddingTop: 0,
    paddingLeft: getPixel(18),
    paddingRight: getPixel(18),
    zIndex: 99,
    backgroundColor: color.transparent,
  },
  whiteContent: {
    backgroundColor: color.white,
    borderTopLeftRadius: getPixel(16),
    borderTopRightRadius: getPixel(16),
    paddingTop: getPixel(32),
    paddingBottom: getPixel(32),
    paddingLeft: getPixel(36),
    paddingRight: getPixel(36),
    minHeight: BbkUtils.vh(40) - getPixel(48),
  },
});

const activityBg = `${ImageUrl.DIMG04_PATH}0AS4j120009clcef884C7.png`;

const HalfPageModal: React.FC<IHalfPageModal> = ({
  visible,
  onMaskPress,
  style,
  title,
  content,
  subTitle,
  hasActivityBg,
  activityContents,
}) => {
  const modalHeaderProps = {
    title,
    titleStyle: {
      flex: 1,
      paddingTop: getPixel(24),
      ...font.title4MediumStyle,
      textAlign: 'center',
      marginRight: getPixel(32),
      lineHeight: getLineHeight(48),
      color: hasActivityBg ? color.white : color.fontPrimary,
    },
    leftIconStyle: {
      position: 'absolute',
      top: getPixel(28),
      left: getPixel(32),
      color: hasActivityBg ? color.white : color.fontPrimary,
    },
    leftIconWrapStyle: {
      minHeight: BbkUtils.getPixel(114),
      paddingRight: getPixel(32),
      zIndex: 1,
    },
    hasBottomBorder: false,
    style: {
      minHeight: getPixel(114),
      backgroundColor: hasActivityBg ? color.transparent : color.white,
    },
  };
  const pageModalProps = {
    visible,
    onMaskPress,
    style,
  };

  const bgDom = hasActivityBg ? (
    <View className={c2xStyles.fixBgStyle}>
      <Image
        src={Utils.compatImgUrlWithWebp(activityBg)}
        mode="aspectFill"
        className={c2xStyles.bgImg}
      />

      <LinearGradient
        start={{ x: 0.5, y: 0.0 }}
        end={{ x: 0.5, y: 1.0 }}
        locations={[0, 1]}
        colors={[color.activityLinearBg1, color.activityLinearBg2]}
        className={c2xStyles.fixBgLinear}
      />
    </View>
  ) : null;

  return (
    <BbkHalfPageModal
      pageModalProps={pageModalProps}
      style={styles.modalWrap}
      contentStyle={xMergeStyles([
        styles.content,
        hasActivityBg && styles.hasContent,
      ])}
      modalHeaderProps={modalHeaderProps}
      bgDom={bgDom}
      closeModalBtnTestID={
        UITestID.car_testid_page_booking_activity_modal_closemask
      }
    >
      <View style={hasActivityBg && styles.whiteContent}>
        {activityContents?.length > 0 ? (
          <View className={c2xStyles.activityContent}>
            {activityContents?.map(item => (
              <View className={c2xStyles.activityItem}>
                {!!item?.title && (
                  <Text className={c2xStyles.activityTitle}>{item.title}</Text>
                )}
                {!!item?.description && (
                  <Text
                    key={item.description}
                    className={c2xStyles.activityContentTxt}
                  >
                    {item.description}
                  </Text>
                )}
              </View>
            ))}
          </View>
        ) : (
          <>
            {!!subTitle && (
              <Text className={c2xStyles.subTitle}>{subTitle}</Text>
            )}
            {content?.map(value => (
              <Text key={value} className={c2xStyles.contentText}>
                {value}
              </Text>
            ))}
          </>
        )}
      </View>
    </BbkHalfPageModal>
  );
};

export default HalfPageModal;
