/* eslint-disable no-nested-ternary */
import { isEmpty as lodashIsEmpty, get as lodashGet } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import AppState from '@c2x/apis/AppState';
import DeviceEventEmitter from '@c2x/apis/DeviceEventEmitter';
import Page, { IBasePageProps } from '@c2x/components/Page';
import Loading from '@c2x/apis/Loading';
import Event from '@c2x/apis/Event';
import React, { memo } from 'react';
import {
  xMergeStyles,
  XView as View,
  xRouter,
  xApplication as Application,
  xShowToast,
  XViewExposure,
} from '@ctrip/xtaro';

import memoize from 'memoize-one';
import { CouponList } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailDtoType';
import BbkToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { DetailReqType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailReqDtoType';
import {
  ResInfoType,
  ResultInfoType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/CreateOrderResponseType';
import { KeyboardAwareScrollView } from '@c2x/extraPackages/react-native-keyboard-aware-scroll-view';
import { layout, color, space, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils, BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';

import {
  LabelsType,
  FeeItemsType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/PriceResponseDtoType';
import {
  Certificate,
  Passenger,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import { RentalGuaranteeV2Type } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import c2xStyles from './booking.module.scss';
import {
  DepositPayInfosType,
  PromptInfoType,
  MembershipPerceptionType,
} from '../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';
import { DriverItem, IInputType } from '../../ComponentBusiness/BookForm';
import {
  ModifyOrderModal,
  TitleHightlightType,
} from '../../ComponentBusiness/OrderConfirmModal/Index';
import RebookTip from '../../ComponentBusiness/Tips/src/RebookTip';
import {
  rebookPenaltyTipText,
  rebookApplyPenaltyTip,
  rebookTipText,
} from '../../Constants/CommonTexts';
import { PageRole } from '../../Constants/CommonEnums';
import CancelZhimaModal from '../../Containers/BookingCancelZhimaModalContainer';
import CPage, { IStateType } from '../../Components/App/CPage';
import Insurance from '../../Containers/BookingIsdInsuranceContainer';
import Extras from '../../Containers/ExtrasInfoContainer';
import BookingFooter from '../../Containers/BookingFooterContainer';
import GpdrContainer from '../../Containers/GpdrContainer';
import BookingModalsContainer from '../../Containers/BookingModalsContainer';
import DepositPaymentContainer from '../../Containers/DepositPaymentISDContanier';
import BookingCouponAndDepositContainer from '../../Containers/BookingCouponAndDepositISDContainer';
import { AssistiveTouch } from '../../Components/Index';

import {
  User,
  CarStorage,
  CarLog,
  Utils,
  CarABTesting,
  AppContext,
  InsuranceConfirmUtil,
  Channel,
  CarFetch,
  EventHelper,
  CarServerABTesting,
  GetABCache,
} from '../../Util/Index';
import { logValidate } from '../../Util/Log/ToastLog';
import { MiddlePay } from '../../Util/Payment/Index';
import SesameContainer from '../../Containers/SesameContainer';
import BookingFormContainer from '../../Containers/BookingFormContainerNew';
import BusinessLicenseModal from '../../Containers/BusinessLicenseModalContainer';
import BookingConfirmModal from '../../Containers/BookingConfirmModalContainer';

import {
  VendorListOptimizationStrengthenModal,
  EtcIntroModalContainer,
  LimitRulesPop,
} from '../../Containers/VendorListIndex';
import {
  StorageKey,
  Platform as PlatformRouter,
  LogKey,
  EventName,
  Enquiry,
  Document,
  ImageUrl,
  LogKeyDev,
  UITestID,
} from '../../Constants/Index';

import {
  getProductReq,
  getProductMapWithHandleCache,
  getBookingFirstScreenParam,
  getProductRequestReference,
  getBaseResData,
  getIsRP,
} from '../../Global/Cache/ProductSelectors';
import {
  ProductSelectors,
  ProductReqAndResData,
  listStatusKeyList,
} from '../../Global/Cache/Index';
import {
  getListRequestId,
  getRegisterPageData,
  getServerRequestId,
  setListStatusData,
} from '../../Global/Cache/ListReqAndResData';
import {
  LogBookQueryPrice,
  LogPaymentCallback,
} from '../../State/Product/UBTLog';
import { PriceTimer } from '../../State/Product/Model';
import {
  isCreditRentPayType,
  getGuidePageParam,
  getRentalGuaranteeV2,
} from '../../State/Product/BbkMapper';
import { ProductReq } from '../../State/Product/FuntionTypes';
import {
  NoMatch,
  BookingWrapper,
  FormWrapper,
  InvoiceInfo,
} from './Components';
import Header from '../../Containers/Book/BookingIsdHeaderContainer';
import AdvantageInfo from './Component/Advantage';
import { CarPayParams } from '../../Types/PaymentType';
import { IBU_SEQUENCE, ISD_SEQUENCE } from '../../State/Booking/Types';
import { ShowPayModeType } from '../../State/Product/Enums';
import { Enums } from '../../ComponentBusiness/Common';
import { isCreditRent } from '../../Util/ABTesting';
import { ButtonAction } from '../../Components/CouponPreValidationModals/Index';
import StoreModal from '../../ComponentBusiness/OptimizeStoreModal';
import {
  getSnapImageData,
  getSnapShotData,
  getBookingSnapImageData,
} from './SnapShot';
import { ISelectedIdType } from '../Product/Types';
import texts from './Texts';
import { modifyOrderModalText } from '../ModifyOrderConfirm/Texts';
import BookingPriceChangePop from '../../Containers/BookingPriceChangePopContainer';
import BookingCreateInsLoading from '../../Containers/BookingCreateInsLoadingContainer';
import BookingCreateInsFailPop from '../../Containers/BookingCreateInsFailPopContainer';
import Distance from '../../Containers/BookingDistanceContainer';
import ProductConfirmModalNew from '../../Containers/ProductConfirmModalContainerNew';
import BookingEasyLifeModal from '../../Containers/BookingEasyLifeModalContainer';
import BookingPriceDetail from '../../Containers/BookingPriceDetailContainer';

import { BuildInsuranceParamsRequestType } from '../../Constants/Types/InsuranceConfirmDtoType';
import ValidatePassenger, {
  IDriversMapType,
} from '../../ComponentBusiness/DriverAddeditModal/src/Validate';
import AddInstructModal from './AddInstructModals';
import ApplyPenaltyInputModal from './ApplyPenaltyInputModal';
import ApproveExplainModal, { ApproveExplainType } from './ApproveExplainModal';
import HalfPageModal from './BookingHalfPageModal';
import EhiFreeDepositRuleModalContainer from '../../Containers/EhiFreeDepositRuleModalContainer';
import { OnAuthenticationDateType } from '../../State/Sesame/Types';
import { IInstalmentInfo } from '../../ComponentBusiness/Naquhua/src/Types';
import ServiceClaimMoreModal from '../../ComponentBusiness/ServiceClaimMoreModal';
import CarServiceDetailModal from '../../ComponentBusiness/CarServiceDetailModal';
import { getImAddress } from '../../Global/IM/IM';
import BookingLoading from './Component/Loading';

import BookingSnapShot from './Component/SnapShot';
import { LayoutPartEnum } from '../../ComponentBusiness/ProductConfirmModal/Type';
import { PriceAlertType } from '../../ComponentBusiness/Common/src/Enums';
import PickupDownGradePop from '../../Containers/PickupDownGradePopContainer';
import SkeletonLoading, {
  PageType,
} from '../../ComponentBusiness/SkeletonLoading';
import { CarServiceFromPageTypes } from '../../ComponentBusiness/CarService';
import PriceAlert from './Component/PriceAlert';
import { QConfigType } from '../../Types/Dto/QConfigResponseType';
import { PayScene, PayType } from '../../Constants/PayEnums';
import ModifyOrderInfoExplain from '../../ComponentBusiness/ModifyOrderInfoExplain/Index';
import BlockCard from './Component/BlockCard';
import CouponModal from '../../ComponentBusiness/Coupon/index';
import { initializeABBookingPage } from '../../Util/CarABTesting/InitializeAB';
import { PersonalAuthCheckModal } from '../../ComponentBusiness/PersonalInfoAuthCheck';
import ErrorKey from '../../Constants/ErrorKey';
import { getSelfServiceLogData, getVehicleCodeBigId } from '../List/Method';
import { DriverLicense, IGuidInfoType, IMergeGuidInfo } from './Types';
import BookingVehicleAndVendorInfoVCContainer from '../../Containers/BookingVehicleAndVendorInfoContainerISD';
import { MarketingFooter } from '../Home/Components/Index';
import DepositTip from './Component/DepositTip';
import { QueryOsdModifyOrderNoteResponseType } from '../../Types/Dto/QueryOsdModifyOrderNoteRequestAndResponseType';
import {
  ContactType,
  LocalContactInfoType,
} from '../../Constants/LocalContactsData';
import AsMobileBlock from '../../ComponentBusiness/AsMobileBlock';
import { getRequestId } from '../../Global/Cache/ListResSelectors';
import { AdjustPriceDetail } from '../../Types/Dto/QueryPriceInfoType';
import CarServiceDetailModalShelves2 from '../../ComponentBusiness/CarServiceDetailModal/src/CarServiceDetailModalShelves2';
import { Floor } from '../../Types/Dto/QueryVehicleDetailListResponseType';
import NoLimitModal from './Component/NoLimitModal';
import HeaderNew from './Component/ISD/Header';
import IncentiveMarqueeVertical from './Component/ISD/IncentiveMarqueeVertical';

const { DepositPayType } = Enums;

export interface PropsType extends IBasePageProps {
  isRebook: boolean;
  isRebookOsd: boolean;
  hasBookingConfirmInfo: boolean;
  toolBoxCustomerJumpUrl: string;
  ctripOrderId?: string | number;
  resCancelFeeRebook?: any;
  setRebookPenalty: (data) => void;
  fetchQueryCancelFeeRebook: (data) => void;
  resetCancelFeeRebook: () => void;
  rebookPenalty?: string;
  config: QConfigType;
  addInstructData: {
    title: string;
    content: string;
  };
  positivePolicies: Array<LabelsType>;
  preLicensData: any;
  activityDetail: any;
  adjustPriceDetail: AdjustPriceDetail;
  passenger: Passenger;
  passengerIDCard: Certificate;
  isEasyLife: boolean;
  confirmInfo: FeeItemsType;
  cancelRuleInfo: FeeItemsType;
  invoiceInfo: any;
  couponList: CouponList;
  driverInfo: DriverItem[];
  orderData: ResInfoType;
  payParams: CarPayParams;
  needFlightNo: boolean;
  isMaskLoading: boolean;
  hasVehicleConfig: boolean;
  isOnlyCreditCard: boolean;
  orderId: number;
  payAmount: number;
  showPayMode: ShowPayModeType;
  materialModalRef: any;
  storePolicyRef: any;
  easyLifePopVisible: boolean;
  passengerList: Array<Passenger>;
  isMergeDeposit: boolean;
  checkFlightNoLoading: boolean;
  createOrder: (data?: {
    isCheckOrder?: boolean;
    inverseInsuranceIds?: Array<string | number>;
    insuranceAgentToken?: string;
    payRequestId?: string;
    isSecretBox?: boolean;
    isContractTemplates?: boolean;
  }) => void;
  changeFormData: (data: DriverItem[]) => void;
  changeModalStatus: (data) => void;
  saveSnapshot: (data) => void;
  selectDriver: (data) => void;
  queryPriceInfo: () => void;
  initSesameAuthState: () => void;
  onPressEasyLife: (visible: boolean) => void;
  clear: () => void;
  theme?: any;
  authenStatusTicket?: string;
  isLogin?: boolean;
  flightDelayRules?: any;
  queryProduct: (data) => void;
  reference?: any;
  isDebugMode?: boolean;
  payRequestId?: string;
  selectedIdType?: ISelectedIdType;
  isSnapShotFinish?: boolean;
  noNeedRefresh?: boolean;
  isFail?: boolean;
  isOnMask?: boolean;
  isPriceLoading?: boolean;
  depositPayType?: number;
  curDepositPayInfo?: DepositPayInfosType;
  isCtripCreditRent?: boolean;
  productReq?: ProductReq;
  productRes?: any;
  curInsPackageId?: number;
  isShowPriceConfirm: boolean; // 是否展示CarDialog
  showPriceConfirm?: () => void;
  supplierModalVisible: boolean;
  depositIntroduceModalVisible: boolean;
  productRentalLocationInfo: any;
  isProductLoading: boolean;
  bookPriceTrackInfo?: Object;
  resetLoading?: (data: boolean, option?: any) => void;
  maskerDriver?: () => void;
  unMaskerDriver?: () => void;
  payMode?: number;
  priceVersion?: string;
  selectedInsuranceId?: Array<string>;
  insConfirmReqParam: BuildInsuranceParamsRequestType;
  createInsLoadingPopVisible: boolean;
  setPriceChangePopIsShow: (data: boolean) => void;
  setCreateInsLoadingIsShow: (data: boolean) => void;
  setCreateInsFailPopIsShow: (data: boolean) => void;
  setInsRemindPopIsShow: (data: boolean) => void;
  refreshList: () => void;
  retry: (data) => void;
  insConfirmCallBack: (data) => void;
  priceChangeCode?: string;
  driversMap?: IDriversMapType;
  needValidateOrder?: boolean;
  ctripRentNeedValidateOrder?: boolean;
  isPriceFail?: boolean;
  yongAge?: number;
  oldAge?: number;
  authStatus?: number;
  onAuthentication?: (param: OnAuthenticationDateType) => void;
  ehiNoteInfo?: PromptInfoType;
  ehiFreeDepositModalVisible?: boolean;
  preferentialTips?: any;
  validateIsDownGrade: (data) => void;
  needDownGrade?: boolean;
  cancelModalData?: any;
  setVendorListModalData?: (data: any) => void;
  iousInfo: IInstalmentInfo;
  selectedLoanPayStageCount: string;
  setSelectedLoanPayStageCount: (data: string) => void;
  setPassengerError: (isError: boolean) => void;
  setCouponPreValidationModalVisible: (
    visible: boolean,
    content: ResultInfoType,
  ) => void;
  isNaquhuaValidated: boolean;
  rentCenterId: any;
  dropOffRentCenterId: any;
  membershipPerception: MembershipPerceptionType;
  addSaleOutList: (productKey: string) => void;
  addRecommendSaleOutList: (productKey: string) => void;
  firstScreenParam?: any;
  createOrderFailModalVisible?: boolean;
  uniqueOrderModalVisible?: boolean;
  flightErrorModalVisible?: boolean;
  dayGap?: number;
  addOnCodes?: string[];
  changeSelectInsurance?: (data) => void;
  ptime?: string;
  rtime?: string;
  setLogInfo?: (data) => void;
  isPriceTimerLoading?: boolean;
  modifyOrderDesc?: string;
  fromPage?: string;
  setVendorPriceData: (data) => void;
  vendorPriceInfo: any;
  changeCoupon: (coupon: any) => void;
  isSecretBox?: boolean;
  agreeSubmitName: string;
  isKlb?: boolean;
  setFlightDelayRulesModalVisible: (data: boolean) => void;
  depositRateDescriptionModalVisible: boolean;
  setDepositRateDescriptionModalVisible: (data: boolean) => void;
  currenctTotalPrice?: number;
  driverLicenseItems?: Array<DriverLicense>;
  curDriverLicense?: DriverLicense;
  selectCurDriverLicense?: (data) => void;
  queryLicencePolicy?: (data) => void;
  setBusinessLicenseModalVisible?: (data) => void;
  isBusinessLicenseModalVisible?: boolean;
  setEhiFreeDepositModalVisible?: (data) => void;
  isRefactor?: boolean;
  isEasyLife2024?: boolean;
  hasExtrasProducts?: boolean;
  isFuelDescriptionModalVisible?: boolean;
  setFuelDescriptionModalVisible?: (data: boolean) => void;
  queryEquipmentInfo?: (params) => void;
  osdModifyOrderNote: QueryOsdModifyOrderNoteResponseType;
  setRebookParamsOsd: (data: any) => void;
  pickUpAreaCode?: string;
  queryCountrysInfo?: () => void;
  changeLocalContactsData?: (data: LocalContactInfoType[]) => void;
  localContactsData?: LocalContactInfoType[];
  optionalContactMethods?: LocalContactInfoType[];
  logBaseInfo?: any;
  newRecommendType?: any;
  setEtcIntroModal?: (data: any) => void;
  isEtcIntroModalVisible?: boolean;
  isHasEtcIntroModal?: boolean;
  curFloor?: Floor;
  saleOutList?: string[];
  addVehicleSaleOutList?: (vehicleCode: string) => void;
  activeGroupId?: string;
  vehicleIndex?: number;
  cancelEncourage?: string;
  storeGuidInfos?: Array<IMergeGuidInfo>;
}
interface StateType extends IStateType {
  opacity: number;
  isLogin: boolean;
  isFinishRender: boolean;
  isSnapShotRender: boolean;
  getSnapImageFinish: boolean;
  addInstructModalVisible: boolean;
  approveExplainModalVisible: boolean;
  approveExplain: ApproveExplainType;
  isApproveExplainLoading?: boolean;
  productConfirmModalVisible: boolean;
  driverLicenseModalVisible?: boolean;
  optimizationStrengthenModalVisible: boolean;
  priceDetailModalVisible: boolean;
  osdPriceDetailModalVisible: boolean;
  isShowModifyOrderModal: boolean;
  isShowOptimizeStoreModal: boolean;
  activityNoteModalVisible: boolean;
  adjustPriceNoteModalVisible: boolean;
  productConfirmAnchor: LayoutPartEnum;
  serviceClaimMoreVisible: boolean;
  carServiceDetailVisible: boolean;
  showServiceDetailCode: string;
  isShowApplyPenaltyInputModal: boolean;
  isShowCouponModal: boolean;
  personalInfoChecked: boolean;
  personalInfoAuthModalVisible: boolean;
  showConfirm: boolean;
  extrasInfoVisible: boolean;
  isShowTableArrow: boolean;
  keyboardHeight: number;
  localContactsModalVisible: boolean;
  localContactsInputIsFocus: boolean;
  currentActivityCode: string;
  noLimitModalVisible: boolean;
  noLimitDesc: string;
}
const {
  vh,
  vw,
  ensureFunctionCall,
  getPixel,
  fixOffsetTop,
  uuid,
  fixIOSOffsetBottom,
  adaptNoaNomalousBottom,
  isHarmony,
  isIos,
} = BbkUtils;
const { DEFAULT_HEADER_HEIGHT } = BbkConstants;
const styles = StyleSheet.create({
  shadow: {
    // @ts-expect-error
    shadowOffset: { width: 0, height: getPixel(-5) },
    shadowRadius: getPixel(5),
    shadowColor: color.black,
    shadowOpacity: 0.05,
    elevation: 20,
  },
  priceDetailStyle: {
    bottom: getPixel(120) + fixIOSOffsetBottom() + adaptNoaNomalousBottom(),
  },
  loadingSpace: {
    marginTop: space.verticalXXL,
    marginBottom: space.verticalXXL,
  },
  modifyRuleExplainWrap: {
    marginLeft: getPixel(16),
    marginRight: getPixel(16),
    marginBottom: getPixel(16),
  },
  osdModifyRuleExplainWrap: {
    paddingRight: getPixel(32),
    paddingLeft: getPixel(46),
    paddingTop: getPixel(40),
    borderRadius: 0,
    backgroundColor: color.C_fafcff,
  },
  freeCancelLoadingBg: {
    overflow: 'hidden',
    marginBottom: getPixel(-25),
    marginTop: getPixel(16),
  },
  freeCancelLoadingBgShelves2: {
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
    borderTopLeftRadius: getPixel(12),
    borderTopRightRadius: getPixel(12),
    overflow: 'hidden',
    marginBottom: getPixel(-10),
  },
  freeCancelLoadingBgHeight: {
    height: getPixel(66),
  },
  bookingOptimizationLoadingSpace: {
    marginTop: space.verticalXXL,
    marginBottom: space.verticalXXL,
    width: vw(100) - getPixel(48),
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
  },
  bookingOptimizationButtonTextStyle: {
    ...font.title4Style,
  },
  bookingOptimizationRelievedBookingMtNew: {
    backgroundColor: color.C_EDF2F8,
  },
  extrasInfo: {
    marginTop: getPixel(16),
  },
  footBarWrap: {
    position: 'relative',
    zIndex: 8,
  },
});

interface DepositPaymentProps {
  onPressDriver: () => void;
}
const depositPaymentComponentConfig = memoize(fixIsCreditRent => ({
  [fixIsCreditRent && PlatformRouter.COMPONENT_CHANNEL.ISD]: memo(
    ({ onPressDriver }: DepositPaymentProps) => (
      <DepositPaymentContainer
        title={texts.depositPaymentTitle}
        rightBtnText={texts.giveupDeposit}
        onPressDriver={onPressDriver}
        isBooking={true}
      />
    ),
  ),
}));

class BookingIsd extends CPage<PropsType, StateType> {
  authenStatusTicket = '';

  authLogin = false;

  materialModalRef = null;

  storePolicyRef = null;

  snapImageData = null;

  snapTime = 0;

  insConfirmData = {};

  // 统计跳转保险页面时长
  isForwardInsPage = false;

  insActiveTime = 0;

  insuranceEndTime = 0;

  priceTimer = null;

  createPiceTimerBySelf = false;

  // 控制 pageDidAppear 刷新价格
  // 只有提交订单后才需刷新
  needRefreshPrice = false;

  personalInfoChecked = false;

  isShowConfirmed = false;

  backgroundTime = 0;

  appState = AppState.currentState;

  keyboardWillShowListener = null;

  keyboardWillHideListener = null;

  appStateActiveCallBack = null;

  appStateBackgroundCallBack = null;

  constructor(props) {
    super(props);
    this.state = {
      opacity: 0,
      isLogin: false,
      isFinishRender: false,
      isSnapShotRender: false,
      getSnapImageFinish: false,
      addInstructModalVisible: false,
      approveExplainModalVisible: false,
      approveExplain: {},
      isApproveExplainLoading: true,
      productConfirmModalVisible: false,
      driverLicenseModalVisible: false,
      optimizationStrengthenModalVisible: false,
      priceDetailModalVisible: false,
      osdPriceDetailModalVisible: false,
      isShowModifyOrderModal: false,
      isShowOptimizeStoreModal: false,
      productConfirmAnchor: null,
      activityNoteModalVisible: false,
      adjustPriceNoteModalVisible: false,
      serviceClaimMoreVisible: false,
      carServiceDetailVisible: false,
      showServiceDetailCode: '',
      isShowApplyPenaltyInputModal: false,
      isShowCouponModal: false,
      personalInfoChecked: false,
      personalInfoAuthModalVisible: false,
      showConfirm: false,
      extrasInfoVisible: false,
      isShowTableArrow: false,
      keyboardHeight: 0,
      localContactsModalVisible: false,
      localContactsInputIsFocus: false,
      currentActivityCode: '',
      noLimitModalVisible: false,
      noLimitDesc: '',
    };
    // 填写页实验批量初始化
    initializeABBookingPage();
    this.authenStatusTicket = props.authenStatusTicket;
    this.authLogin = props.isLogin;
    this.onScroll = this.onScroll.bind(this);
    this.onLayoutDriver = this.onLayoutDriver.bind(this);
    this.handleBookPress = this.handleBookPress.bind(this);
    this.scrollView = React.createRef();
    this.materialModalRef = React.createRef();
    this.storePolicyRef = React.createRef();
    this.priceDetailsRef = React.createRef();
    this.insuranceSuitsModalRef = React.createRef();
    this.onPressDriver = this.onPressDriver.bind(this);
    this.onPressBar = this.onPressBar.bind(this);
    this.onPressTandC = this.onPressTandC.bind(this);
    this.onPressCoupon = this.onPressCoupon.bind(this);
    this.onConfirmFightNo = this.onConfirmFightNo.bind(this);
    this.onPressFlightDelayRules = this.onPressFlightDelayRules.bind(this);
    this.queryProduct = this.queryProduct.bind(this);
    this.priceTimer = props.priceTimer;
    // insuranceRequestId & insuranceSelectedIds 赋值时机
    // 进入填写页时赋值，标识一次填写页内的操作
    AppContext.setInsuranceRules(props.selectedInsuranceId);
    // 缓存供应商报价信息，用于 Product 接口请求
    const { setVendorPriceData, vendorPriceInfo } = this.props || {};
    setVendorPriceData(vendorPriceInfo);

    if (!isHarmony && Utils.isCtripIsd()) {
      this.queryProduct(false, true);
      this.initLogInfo();
    }
    this.initListData();
    this.isShowConfirmed = false;
    this.keyboardWillShowListener =
      isIos &&
      Keyboard.addListener(
        'keyboardWillShow',
        this.keyboardWillShow.bind(this),
      );

    this.keyboardWillHideListener =
      isIos &&
      Keyboard.addListener(
        'keyboardWillHide',
        this.keyboardWillHide.bind(this),
      );
  }

  keyboardWillShow(e) {
    this.setState({
      keyboardHeight: e.endCoordinates.height,
    });
  }

  keyboardWillHide() {
    this.setState({
      keyboardHeight: 0,
    });
  }

  priceDetailsRef: any;

  insuranceSuitsModalRef: any;

  scrollView: any;

  dirverY: number;

  depositPayY: number;

  osdDepositPayY: number;

  couponY: number;

  couponHeight: number;

  disableBookingFlag: boolean;

  insConfirmInsList: Array<any>;

  /* eslint-disable class-methods-use-this */
  // do not use static for Log pV
  getPageId() {
    return Channel.getPageId().Book.ID;
  }

  getPageParamInfo() {
    const info: any = {};
    const {
      reference = {},
      rentCenterId,
      dropOffRentCenterId,
      vendorPriceInfo,
      isSecretBox,
    } = this.props;
    const { isdParam, pHub, rHub } = reference;
    const { name } = vendorPriceInfo || {};
    if (isdParam) {
      info.vehicledata = {
        vehiclecode: isdParam.vpid,
        vehicleName: isdParam.vehicleName,
        vendorCode: isdParam.vendorid,
        vendorName: isdParam.csname,
        storeCode: isdParam.psid,
        groupId: isdParam.groupId,
        ispickupStation: Utils.getLogStrValue(pHub),
        isdropoffStation: Utils.getLogStrValue(rHub),
        ispickupSource: Utils.getLogStrValue(rentCenterId),
        isdropoffSource: Utils.getLogStrValue(dropOffRentCenterId),
        allTags: vendorPriceInfo?.allTags,
        productName: name,
        isSelfService: getSelfServiceLogData(vendorPriceInfo?.allTags),
        iseasyLife2024: !!isdParam.isEasyLife2024,
      };
    }
    // 盲盒曝光埋点，1盲盒活动 0常规活动
    info.actType = isSecretBox ? '1' : '0';
    return info;
  }

  onScroll(event: any) {
    const { opacity } = this.state;
    const { nativeEvent } = event;
    const scrollY = nativeEvent.contentOffset.y;
    if (Utils.isCtripIsd()) {
      if (scrollY > 540) {
        this.setState({ isShowTableArrow: true });
      }
      return;
    }

    let curOpacity = Math.floor(scrollY) / 50;
    if (curOpacity > 1) curOpacity = 1;
    if (curOpacity < 0) curOpacity = 0;
    if (opacity !== curOpacity) {
      this.setState({ opacity: curOpacity });
    }
  }

  queryProduct(isLoginRefresh?: boolean, isStartPriceTimer?: boolean) {
    const productParams: DetailReqType = getProductReq();
    const reference = productParams && productParams.reference;
    const { queryProduct } = this.props;
    if (Utils.isCtripIsd()) {
      queryProduct({ reset: true, isStartPriceTimer });
    } else {
      queryProduct({
        reference,
        reset: true,
        isLoginRefresh,
        isProductLoading: false,
      });
    }
  }

  async onPressDriver() {
    Keyboard.dismiss();
    if (!this.state.isLogin) {
      const res = await User.toLogin();
      if (!res) {
        return;
      }
      this.setState({ isLogin: res });
      this.queryProduct();
    }
    const { passengerList = [], passenger, passengerIDCard } = this.props;

    if (
      (!lodashIsEmpty(passenger) && passengerIDCard?.certificateType) ||
      (passengerList.length && lodashIsEmpty(passenger))
    ) {
      // 选中了驾驶员&&选中的驾驶员证件可用 || 账户内的驾驶员都不可用
      this.goToDriverEdit();
      return;
    }
    const isAdd = !passengerIDCard?.certificateType;
    this.goToDriverEdit(isAdd);
  }

  handlePresssMore = () => {
    Keyboard.dismiss();
    CarLog.LogCode({ name: '点击_填写页_驾驶员更多和新增' });
    this.push(Channel.getPageId().DriverList.EN);
  };

  goToDriverEdit(isAdd?: boolean) {
    const { passenger } = this.props;
    this.push(Channel.getPageId().DriverEdit.EN, {
      passenger,
      isAdd,
      fromurl: 'Booking',
    });
    CarLog.LogCode({
      name: `点击_驾驶员列表页_${isAdd ? '添加' : '编辑'}驾驶员`,
    });
  }

  onPressFlightDelayRules() {
    CarLog.LogCode({ name: '点击_填写页_航班延迟政策' });
    const { setFlightDelayRulesModalVisible } = this.props;
    Keyboard.dismiss();
    setFlightDelayRulesModalVisible(true);
  }

  onPressBar() {
    const { addOnCodes, curInsPackageId } = this.props;
    const fees = [];
    if (addOnCodes?.length) {
      const rentalGuaranteeV2: RentalGuaranteeV2Type =
        getRentalGuaranteeV2(curInsPackageId);
      const selectInsInfo = rentalGuaranteeV2?.packageDetailList?.filter(item =>
        addOnCodes.includes(item?.uniqueCode),
      );
      selectInsInfo?.forEach(item => fees.push(item.currentDailyPrice));
    }
    CarLog.LogCode({
      name: '点击_填写页_底部_费用明细',

      info: {
        carAgentInsuranceFee: fees?.join(','),
      },
    });
    if (Utils.isCtripIsd()) {
      this.setPriceDetailModal();
    }
  }

  onCheckBarCheck = check => {
    this.setState({
      personalInfoChecked: check,
    });
    this.personalInfoChecked = check;
  };

  onCheckBarPress = () => {
    this.setState({
      personalInfoAuthModalVisible: true,
    });
  };

  hidePersonalInfoAuthModal = () => {
    this.setState({
      personalInfoAuthModalVisible: false,
    });
  };

  onPressTandC() {
    Keyboard.dismiss();
    this.push(Channel.getPageId().Policy.EN);
  }

  onPressCtripDepositFee = () => {
    xRouter.navigateTo({
      url: 'https://pages.c-ctrip.com/cars/doc/CreditRentAggrementV4.doc',
    });
  };

  onPressSelfServiceInstruction = () => {
    xRouter.navigateTo({ url: Document.selfServiceInstruction });
  };

  onPressCoupon() {
    Keyboard.dismiss();
    if (!this.state.isLogin) {
      this.handleLogin();
      return;
    }
    CarLog.LogCode({ name: '点击_填写页_优惠券' });
    if (Utils.isCtripIsd()) {
      this.setState({ isShowCouponModal: true });
    } else {
      this.push('Coupon');
    }
  }

  getActivityLog = () => {
    const { activityDetail } = this.props;
    const { title = '' } = activityDetail || {};
    const { vendorInfo } = getBaseResData();
    const { vendorCode, bizVendorCode } = vendorInfo || {};
    const activityId = [];
    if (activityDetail?.promotions?.length > 0) {
      activityDetail?.promotions?.forEach(item => {
        if (item?.labelCode) {
          activityId.push(item.labelCode);
        }
      });
    } else if (activityDetail?.promotion?.labelCode) {
      activityId.push(activityDetail?.promotion?.labelCode);
    }
    return {
      vendorCode,
      vendorId: bizVendorCode,
      activityId,
      activityName: title,
    };
  };

  onPressActivity = (code?: string) => {
    Keyboard.dismiss();
    const data = this.getActivityLog();
    CarLog.LogCode({
      name: '点击_填写页_活动模块',

      info: {
        ...data,
      },
    });
    if (code) {
      this.setState({
        currentActivityCode: code,
        activityNoteModalVisible: true,
      });
    }
  };

  onCloseActivityNoteModal = () => {
    this.setState({
      activityNoteModalVisible: false,
    });
  };

  onPressAdjustPrice = () => {
    Keyboard.dismiss();
    this.setState({
      adjustPriceNoteModalVisible: true,
    });
  };

  onCloseAdjustPriceNoteModal = () => {
    this.setState({
      adjustPriceNoteModalVisible: false,
    });
  };

  // 开启轮询
  openPriceTimer = (needFirstFresh?: boolean) => {
    const { productReq, showPriceConfirm, queryEquipmentInfo } = this.props;
    if (this.priceTimer) {
      this.priceTimer.clearPriceTimer?.();
    }
    this.priceTimer = new PriceTimer();
    this.createPiceTimerBySelf = true;
    this.priceTimer.setPriceTimer(
      productReq,
      showPriceConfirm, // 设置CarDialog展示
      this.props.retry,
      needFirstFresh,
      // 增加额外设备轮询，防止缓存失效
      params => Utils.isCtripOsd() && queryEquipmentInfo(params),
    );
  };

  handleAppBackground = () => {
    this.backgroundTime = Date.now();
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_app_state,
      info: {
        state: 'background',
      },
    });
  };

  handleAppActive = () => {
    const { isShowPriceConfirm } = this.props;
    const intervalTime = Date.now() - this.backgroundTime;
    if (
      !!this.backgroundTime &&
      intervalTime > Enquiry.INTERVAL_TIME &&
      !isShowPriceConfirm
    ) {
      this.openPriceTimer(true);
    }
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_app_state,
      info: {
        time: Date.now() - this.backgroundTime,
        state: 'active',
      },
    });
  };

  componentDidMount() {
    super.componentDidMount();
    if (!Utils.isCtripIsd()) {
      this.onPageReady(null);
      this.props.queryCountrysInfo();
    }
    if (isHarmony && Utils.isCtripIsd()) {
      this.queryProduct(false, true);
      this.initLogInfo();
    }
    const {
      noNeedRefresh,
      productRes,
      isEasyLife,
      isPriceLoading,
      isPriceFail,
      isRebook,
      ctripOrderId,
      fetchQueryCancelFeeRebook = Utils.noop,
      optionalContactMethods,
      changeLocalContactsData,
    } = this.props;
    if (optionalContactMethods?.length > 0) {
      changeLocalContactsData(optionalContactMethods);
    }

    const isSelected = !!lodashGet(productRes, 'isSelected');
    const gs = lodashGet(productRes, 'gs') || {};
    const { id = 0, title = '' } = gs;

    if (isRebook && !!ctripOrderId) {
      fetchQueryCancelFeeRebook({ orderId: ctripOrderId });
    }
    if (!noNeedRefresh && !Utils.isCtripIsd()) {
      // 重新查询但需要保留用户在产品详情页的勾选，儿童座椅，保险
      this.queryProduct(true);
    }

    if (!Utils.isCtripIsd()) {
      this.openPriceTimer();
    }

    CarLog.LogTrace({
      key: LogKey.c_car_trace_book_insurance_222017,
      info: {
        insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
        insuranceId: AppContext.InsuranceRules.insuranceSelectedIds,
      },
    });

    if (Utils.isCtripIsd()) {
      // 货架信息订单填写页漏斗埋点
      CarLog.LogTrace({
        key: LogKey.vac_car_trace_product_goodsshelf,
        info: {
          gsId: id,
          gsName: title,
          isEasyLife,
          isOptimize: isSelected,
        },
      });
    }

    this.appStateActiveCallBack = DeviceEventEmitter.addListener(
      'AppEnterForeground',
      this.handleAppActive,
    );
    this.appStateBackgroundCallBack = DeviceEventEmitter.addListener(
      'AppEnterBackground',
      this.handleAppBackground,
    );

    setTimeout(async () => {
      const isLogin = await User.isLogin();
      this.setState({ isLogin });

      this.initForm();
    });
    setTimeout(() => {
      this.setState({ isSnapShotRender: true });
    }, 1000);

    if (!isPriceLoading) {
      LogBookQueryPrice(!isPriceFail);
    }
    AppContext.setIsReachedBookingPage(true);
  }

  lazyComponentDidMount() {
    if (!Utils.isCtripIsd()) {
      this.setState({ isFinishRender: true });
    }
    if (Utils.isCtripIsd()) {
      this.initialConfirmedData();
    }
    this.registerEvents();
  }

  initialConfirmedData = () => {
    this.addTimer(
      setTimeout(async () => {
        const isShowed = await CarStorage.loadAsync(
          StorageKey.CAR_BOOKING_CONFIRM,
          true,
          true,
        );
        if (!isShowed) {
          this.isShowConfirmed = true;
        }
      }, 5 * 1000),
    );
  };

  showInsRemindPopFun = () => {
    this.props.setInsRemindPopIsShow(true);
  };

  handleMaskLoading = () => {
    Loading.hideMaskLoading();
  };

  handleInsConfirmCallback = () => {
    const status = lodashGet(this.insConfirmData, 'status');
    if (status === 0 || status === 2) {
      this.props.insConfirmCallBack({
        insConfirmData: this.insConfirmData,
        callbackFun: this.handleMaskLoading,
        continueCreateOrder: this.continueCreateOrder,
        logInsurancePageActiveTime: this.logInsurancePageActiveTime,
      });
      Loading.showMaskLoading({
        cancelable: false,
      });
    } else {
      // 从保代页面回退后的场景，也需要清空标记
      ProductReqAndResData.setData(
        ProductReqAndResData.keyList.hasGoToInsConfirm,
        false,
      );
      this.logInsurancePageActiveTime();
    }
  };

  addInsConfirmBackToBookEvent = () => {
    EventHelper.addEventListener(EventName.insConfirmBackToBook, data => {
      const status = lodashGet(data, 'status');
      const selectedInsuranceList =
        lodashGet(data, 'data.selectedInsuranceList') || [];
      const callbackInsuranceId = selectedInsuranceList.map(m => m.insuranceId);
      const token = lodashGet(data, 'token');
      this.insConfirmData = {
        insConfirmInsList: selectedInsuranceList,
        callbackInsuranceId,
        token,
        status,
      };
      this.handleInsConfirmCallback();

      CarLog.LogTrace({
        key: LogKey.c_car_trace_book_insurance_callback_222017,
        info: {
          callbackInsuranceId,
          insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
        },
      });
      // remove事件
      Event.removeEventListener(EventName.insConfirmBackToBook);
    });
  };

  registerEvents() {
    const { fromPage } = this.props;
    EventHelper.addEventListener(EventName.orderBack2Home, () => {
      Page.getRegisteredPageList(res => {
        const index = res?.findIndex(
          item => item === Channel.getPageId().Order.EN,
        );
        // 存在且不是最后一个
        if (index > -1 && index < res?.length - 1) {
          Page.popToPage(Channel.getPageId().Order.EN);
          EventHelper.sendEvent(EventName.modifyOrder2Order, {});
        } else {
          if (fromPage === Channel.getPageId().RecommendVehicle.EN) {
            return;
          }
          if (AppContext.isHomeCombine) {
            // @ts-ignore
            Page.backToLast({ animated: false });
            return;
          }
          const isFromHome = !!getRegisterPageData(Channel.getPageId().Home.EN);
          const isFromList =
            !isFromHome && !!getRegisterPageData(Channel.getPageId().List.EN);
          if (!(isFromHome || isFromList)) {
            return;
          }
          this.pop(Channel.getPageId()[isFromHome ? 'Home' : 'List'].EN);
          if (isFromList) {
            this.props.setVendorListModalData({ visible: false });
          }
        }
      });
    });
  }

  removeEvents() {
    Event.removeEventListener(EventName.orderBack2Home);
    Event.removeEventListener(EventName.insConfirmBackToBook);
    this.appStateActiveCallBack?.remove?.();
    this.appStateBackgroundCallBack?.remove?.();
    Event.removeEventListener(EventName.closeProductExtraInfoModal);
  }

  initForm() {
    const { passenger } = this.props;
    if (passenger) {
      this.props.selectDriver(passenger);
    }
    setTimeout(() => {
      this.onPageExposure();
    }, 100);
  }

  getDriverInfoFromStorage = () => {
    const { changeFormData } = this.props;
    // 声明需要从缓存获取的表单类型
    const storageSelectTypes = [IInputType.email, IInputType.localContacts]; // 本次只需要邮箱
    // 获取表单缓存信息
    CarStorage.load(StorageKey.DRIVER, true).then(result => {
      if (result) {
        try {
          const res = JSON.parse(result);
          if (!Array.isArray(res)) return;
          const dataDriver = res.reduce((m, v) => {
            if (v?.value && storageSelectTypes.includes(v?.type)) {
              return [...m, v];
            }
            return m;
          }, []);
          changeFormData(dataDriver);
        } catch (error) {
          CarLog.LogError(ErrorKey.e_book_get_storage_driver_info, error);
        }
      }
    });
  };

  componentWillUnmount() {
    super.componentWillUnmount();
    const {
      isRebook,
      ctripOrderId,
      resetCancelFeeRebook = Utils.noop,
    } = this.props;
    if (isRebook && !!ctripOrderId) {
      resetCancelFeeRebook();
    }
    this.props.clear();
    this.props.setSelectedLoanPayStageCount('');
    if (this.createPiceTimerBySelf) {
      // 如果询价轮询是booking页面创建的，说明没有经过产详页(比如h5保代页面跳转到填写页)
      this.priceTimer.clearPriceTimer();
    }
    this.removeEvents();
  }

  pageDidAppear() {
    super.pageDidAppear();
    const insuranceAgentToken = lodashGet(this.insConfirmData, 'token');
    if (!insuranceAgentToken) {
      // 从保代页面侧滑回退后的场景，也需要清空标记
      ProductReqAndResData.setData(
        ProductReqAndResData.keyList.hasGoToInsConfirm,
        false,
      );
    }
    this.logInsurancePageBackTime();
    this.refreshPrice();
  }

  refreshPrice = () => {
    const { orderId, queryPriceInfo, initSesameAuthState } = this.props;
    if (orderId && this.needRefreshPrice) {
      this.needRefreshPrice = false;
      queryPriceInfo();
      ensureFunctionCall(initSesameAuthState, this);
    }
  };

  pageDidDisappear() {
    super.pageDidDisappear();
    Loading.hideMaskLoading();
    const {
      createOrderFailModalVisible,
      uniqueOrderModalVisible,
      flightErrorModalVisible,
    } = this.props;
    if (
      createOrderFailModalVisible ||
      uniqueOrderModalVisible ||
      flightErrorModalVisible
    ) {
      this.props.changeModalStatus({
        createOrderFailModalVisible: false,
        uniqueOrderModalVisible: false,
        flightErrorModalVisible: false,
      });
    }
    CarStorage.save(StorageKey.DRIVER, this.props.driverInfo, undefined, true);
  }

  onPageExposure() {
    const { payParams } = this.props;
    if (payParams && payParams.driver) {
      CarLog.LogTrace({
        key: LogKey.c_car_exposure_write_driver_recommend,
        info: {
          recommendDriverName: payParams.driver.name || '',
          recommendDriverCardNum: payParams.driver.idnumber || '',
          recommendDriverTelNum: payParams.driver.cellPhone || '',
        },
      });
    }
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (
      Utils.isCtripIsd() &&
      this.props.isProductLoading &&
      !nextProps.isProductLoading
    ) {
      this.performanceMonitor.receiveResStart = new Date();
    }
    // 海外详情页优化新版，驾驶员国家发生变化时，查询驾照政策
  }

  setFirstScreenFinishRender() {
    if (!this.state.isFinishRender) {
      setTimeout(() => {
        this.setState({
          isFinishRender: true,
        });
      });
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { isProductLoading, isFail } = this.props;
    if (
      Utils.isCtripIsd() &&
      prevProps.isProductLoading &&
      !isProductLoading &&
      !isFail
    ) {
      this.setFirstScreenFinishRender();
      this.onPageReady(getProductMapWithHandleCache());
    }

    if (!prevProps.isMaskLoading && this.props.isMaskLoading) {
      Loading.showMaskLoading({
        cancelable: false,
      });
    } else if (prevProps.isMaskLoading && !this.props.isMaskLoading) {
      Loading.hideMaskLoading();
    }

    if (!prevProps.isOnMask && this.props.isOnMask) {
      // 开始截图
      setTimeout(() => {
        this.getSnapImageData();
      }, 50);
    }

    if (!prevProps.orderId && this.props.orderId) {
      this.handleOrderSuccess();
    }

    if (
      (!prevProps.orderId || !prevState.getSnapImageFinish) &&
      this.props.orderId &&
      this.state.getSnapImageFinish
    ) {
      // 上传交易快照
      this.createSnapShotData();
    }

    if (!prevProps.preLicensData && this.props.preLicensData) {
      this.goToRapidPayment();
    }

    // monitor sesame state
    this.onSesameAuthStatusChange();

    if (
      prevProps.ehiFreeDepositModalVisible !==
      this.props.ehiFreeDepositModalVisible
    ) {
      this.setEnableDragBack(!this.props.ehiFreeDepositModalVisible);
    }

    // 新版填写页的轮询需在productInfo接口回来后
    if (
      Utils.isCtripIsd() &&
      prevProps.isPriceTimerLoading &&
      !this.props.isPriceTimerLoading
    ) {
      this.openPriceTimer();
    }
  }

  onSesameAuthStatusChange = async () => {
    if (this.authenStatusTicket !== this.props.authenStatusTicket) {
      this.authenStatusTicket = this.props.authenStatusTicket;
      setTimeout(() => this.props.queryPriceInfo(), 500);
    }
  };

  onPressFormQuestion = () => {
    this.push('DriverIntroduction');
    CarLog.LogCode({ name: '点击_填写页_驾驶员信息_详情按钮' });
  };

  onPressPickUpMaterials = () => {
    Keyboard.dismiss();
    this.openProductConfirmModal(LayoutPartEnum.PickUpMaterials);
  };

  showApplyPenaltyInputModal = () => {
    this.setState({
      isShowApplyPenaltyInputModal: true,
    });
  };

  hideApplyPenaltyInputModal = () => {
    this.setState({
      isShowApplyPenaltyInputModal: false,
    });
  };

  getDataFail() {
    BbkToast.show(texts.orderFailTip);
  }

  showApproveExplainModal = () => {
    this.setState({
      approveExplainModalVisible: true,
      isApproveExplainLoading: true,
    });

    CarFetch.reservationTerms({
      termsType: 'PersonalInfoAuth',
    })
      .then(res => {
        if (res?.baseResponse?.isSuccess) {
          this.setState({
            approveExplain: res,
            isApproveExplainLoading: false,
          });
        } else {
          this.getDataFail();
        }
      })
      .catch(() => {
        this.getDataFail();
      });
  };

  hideApproveExplainModal = () => {
    this.setState({
      approveExplainModalVisible: false,
    });
  };

  ctripRentNeedValidateOrder = () => {
    const { ctripRentNeedValidateOrder } = this.props;
    return ctripRentNeedValidateOrder;
  };

  needValidateOrder = () => {
    const { needValidateOrder } = this.props;
    return needValidateOrder;
  };

  isPayAtStore = () => {
    const { showPayMode, depositPayType } = this.props;
    return (
      showPayMode === ShowPayModeType.Store &&
      depositPayType === DepositPayType.Store
    );
  };

  disableBooking = () => {
    this.disableBookingFlag = true;
    this.addTimer(
      setTimeout(() => {
        this.disableBookingFlag = false;
      }, 2000),
    );
  };

  resetRebookParams = () => {
    const { setRebookParamsOsd, isRebookOsd } = this.props;
    if (isRebookOsd) {
      setRebookParamsOsd(null);
    }
  };

  async handleOrderSuccess() {
    const { orderData, payParams } = this.props;
    // @ts-ignore
    const { orderId, payAmount, payToken, payLink } = orderData;

    if (
      orderId &&
      (payAmount || payToken || payLink) &&
      !(this.needValidateOrder() || this.isPayAtStore())
    ) {
      try {
        let payRes = null;
        Loading.showMaskLoading({
          cancelable: false,
        });
        payRes = await MiddlePay({
          params: payParams,
          scene: PayScene.BookingCreateOrder,
        });
        this.disableBooking();
        Loading.hideMaskLoading();
        const { success, showError } = payRes;
        LogPaymentCallback(success);
        if (success) {
          // 支付成功 清空列表页缓存
          AppContext.setUserFetchCacheId({
            actionType: 'bookingPaySuccess',
          });
          this.goToOrder();
        } else if (showError) {
          this.payErrorHandler();
        } else {
          this.goToOrder();
        }
        // 修改订单清除缓存
        this.resetRebookParams();
        return;
      } catch (e) {
        this.payErrorHandler();
      }
    }
    this.goToOrder();
  }

  payErrorHandler = () => {
    BbkToast.show('支付失败', 1, () => {
      this.goToOrder();
    });
  };

  async goToRapidPayment() {
    const {
      preLicensData,
      isOnlyCreditCard,
      payRequestId,
      payAmount,
      payParams,
      showPayMode,
      resetLoading,
      isSecretBox,
    } = this.props;
    const params = {
      ...payParams,
      amount: payAmount,
      isOnlyCreditCard,
      title: preLicensData && preLicensData.payTip,
      payRequestId,
      payType:
        showPayMode === ShowPayModeType.Auth
          ? PayType.RegularPayAndCredit
          : PayType.CtripCredit,
      busType: this.ctripRentNeedValidateOrder()
        ? PlatformRouter.BUS_TYPE.ISD_CREDIT
        : PlatformRouter.BUS_TYPE.ISD_AUTH,
    };
    try {
      const payRes = await MiddlePay({
        params,
        scene: PayScene.BookingCreditRentAuth,
      });
      // eslint-disable-next-line no-shadow
      const { success, payRequestId: resInfoPayRequestId, isNew } = payRes;
      LogPaymentCallback(success);
      if (success) {
        const data = {
          payRequestId: isNew ? resInfoPayRequestId : payRequestId,
          isSecretBox,
        };
        this.props.createOrder(data);
      } else {
        BbkToast.show(texts.payFail);
      }
    } catch (e) {
      BbkToast.show(texts.payFail);
    } finally {
      // 解决重复下单优惠券失效问题，createOrder 无法唤起 Loading
      // 由于 MiddlePay 内部会关闭 MaskLoading，支付返回统一设置 Loading 关闭，保证 Loading 状态统一
      resetLoading(false);
    }
  }

  getOrderPath = (oid?: number) => {
    let orderPath = '';
    const orderId = oid || this.props.orderId;
    const { ORDERDETAIL: url } = PlatformRouter.CAR_CROSS_URL;
    const disableNativeDragBackSuffix = '&dragBack=false';
    if (Utils.isCtripIsd()) {
      orderPath = url.NEWISD;
    }
    orderPath = `${orderPath}&orderId=${orderId}&from=${PlatformRouter.ORDER_BACK_PARAMS.newBook}${disableNativeDragBackSuffix}`;
    return orderPath;
  };

  goToOrder = (oid?: number) => {
    const orderId = oid || this.props.orderId;
    if (!orderId) return;
    CarLog.LogCode({ name: '点击_填写页_跳转新版订单详情页' });
    const orderPath = this.getOrderPath(oid);
    xRouter.navigateTo({ url: orderPath });
    if (this.priceTimer) {
      this.priceTimer.clearPriceTimer();
    }
  };

  validateDepositPayment = () => {
    const { curDepositPayInfo } = this.props;
    return !lodashIsEmpty(curDepositPayInfo);
  };

  validate = () => {
    const { driverInfo, needFlightNo } = this.props;
    const errors = driverInfo.filter(v => v.error);
    let isValid = !errors.length;
    let sequence = IBU_SEQUENCE;

    if (Utils.isCtripIsd()) {
      sequence = ISD_SEQUENCE;
    }
    if (isValid) {
      const noValues = driverInfo
        .filter(v => v.value === '' && !!sequence.find(s => s === v.type))
        .map(v => {
          if (v.type === 'flightNumber') {
            return { ...v, error: needFlightNo };
          }
          return { ...v, error: true };
        })
        .filter(v => v.error);
      if (noValues.length) {
        isValid = false;
        const noValues1 = noValues?.[0];
        logValidate(`${noValues1?.type} is empty or error`, noValues1?.value);
        this.props.changeFormData(noValues);
      }
    } else {
      const error1 = errors?.[0];
      logValidate(`${error1?.type} is error`, error1?.value);
    }

    return isValid;
  };

  async handleLogin() {
    const res = await User.toLogin();
    if (res) {
      this.setState({ isLogin: res });
      this.queryProduct();
    }
  }

  getSnapImageData = async () => {
    if (this.snapImageData) {
      return this.snapImageData;
    }
    const start = +new Date();
    const bookingData = await getBookingSnapImageData(this.scrollView);
    this.props.unMaskerDriver();
    const snapImageData = await getSnapImageData({
      materialModalRef: this.materialModalRef,
      priceDetailsRef: this.priceDetailsRef,
      insuranceSuitsModalRef: this.insuranceSuitsModalRef,
    });
    this.snapTime = +new Date() - start;
    this.snapImageData = [...snapImageData, bookingData];
    this.setState({
      getSnapImageFinish: true,
    });
    return this.snapImageData;
  };

  createSnapShotData = async () => {
    CarLog.LogCode({ name: '点击_填写页_交易快照' });
    const { orderId } = this.props;
    const snaps = await this.getSnapImageData();
    const snapImageData = getSnapShotData({
      orderId,
      snaps,
    });
    this.props.saveSnapshot(snapImageData);
  };

  getCreateInsLoadingIsShow = () => this.props.createInsLoadingPopVisible;

  handleGoToInsConfirmPage = () => {
    const {
      priceVersion,
      insConfirmReqParam,
      setCreateInsLoadingIsShow,
      setCreateInsFailPopIsShow,
    } = this.props;
    InsuranceConfirmUtil.goToInsConfirmPage({
      eventName: EventName.insConfirmBackToBook,
      reqParams: insConfirmReqParam,
      setCreateInsLoadingIsShow,
      setCreateInsFailPopIsShow,
      getCreateInsLoadingIsShow: this.getCreateInsLoadingIsShow,
      callbackFun: () => {
        // 注册事件
        this.addInsConfirmBackToBookEvent();
        ProductReqAndResData.setData(
          ProductReqAndResData.keyList.hasGoToInsConfirm,
          true,
        );
        ProductReqAndResData.setData(
          ProductReqAndResData.keyList.priceVersionBeforeInsConfirm,
          priceVersion,
        );
      },
    });
    this.isForwardInsPage = true;
    this.insActiveTime = new Date().getTime();
  };

  continueCreateOrder = (
    inverseInsuranceIds?: Array<string | number>,
    insuranceAgentToken?: string,
  ) => {
    // 校验送车上门是否需要降级
    if (this.props.needDownGrade) {
      this.props.validateIsDownGrade({
        callbackFun: () => {
          this.continueCreateOrderAfterValidate(
            inverseInsuranceIds,
            insuranceAgentToken,
          );
        },
        // 请求失败后是否要继续下一步操作,如点击下一步失败，则可以直接进入到填写页，若是点击提交订单时获取降级信息失败，则需Toast提示用户
        isFailToContinue: false,
      });
      return;
    }
    this.continueCreateOrderAfterValidate(
      inverseInsuranceIds,
      insuranceAgentToken,
    );
  };

  continueCreateOrderAfterValidate = (
    inverseInsuranceIds?: Array<string | number>,
    insuranceAgentToken?: string,
  ) => {
    Loading.showMaskLoading({
      cancelable: false,
    });
    this.props.maskerDriver();
    this.props.createOrder({
      isCheckOrder: this.needValidateOrder(),
      inverseInsuranceIds,
      insuranceAgentToken,
      isSecretBox: this.props.isSecretBox,
      isContractTemplates: Utils.isCtripOsd(),
    });
    // 重置交易快照状态
    this.setState({
      getSnapImageFinish: false,
    });
    this.snapImageData = null;
  };

  handleValidateScroll() {
    const { driverInfo } = this.props;
    if (this.scrollView && this.scrollView.current) {
      this.scrollView.current.scrollToPosition(0, this.dirverY, true);
    }
    CarLog.LogCode({
      name: '点击_填写页_表单验证',

      data: driverInfo,
    });
  }

  handleBookPress = async () => {
    const { isLogin } = this.state;
    const {
      passenger,
      isMaskLoading,
      isPriceLoading,
      curDepositPayInfo,
      showPayMode,
      payMode,
      selectedInsuranceId,
      bookPriceTrackInfo,
      depositPayType,
      driversMap,
      oldAge,
      yongAge,
      selectedLoanPayStageCount,
      setPassengerError,
      passengerList,
      config: remoteQConfig,
      checkFlightNoLoading,
      isRefactor,
      isEasyLife2024,
      activeGroupId,
      vehicleIndex,
    } = this.props;
    const isNoResultNew = CarServerABTesting.isNoResult();

    if (this.disableBookingFlag) {
      return;
    }

    // 在航班号校验期间不可提交
    if (checkFlightNoLoading) {
      return;
    }

    // loading时不可提交
    // fix bug: http://iwork.ctripcorp.com/#/carddetail/2567/4803/9149/552202
    if (isPriceLoading) {
      return;
    }

    // 防止多次点击提交订单
    if (isMaskLoading) {
      return;
    }

    if (!isLogin) {
      this.handleLogin();
      return;
    }

    if (Utils.isCtripIsd()) {
      if (lodashIsEmpty(passenger)) {
        setPassengerError(true);
        if (passengerList?.length) {
          xShowToast({ title: texts.emptyDriver, duration: 3000 }); // 请选择驾驶员
        } else {
          xShowToast({ title: texts.drivers_addDriver, duration: 3000 }); // 请新增驾驶员
        }
        this.handleValidateScroll();
        return;
      }
      if (!ValidatePassenger(driversMap, oldAge, yongAge, true)) {
        this.handleValidateScroll();
        return;
      }
    }

    if (!this.validate()) {
      this.handleValidateScroll();
      return;
    }

    const isValid = this.validateDepositPayment();

    // 程信分押金支付方式校验
    if (CarABTesting.isCreditRent() && !isValid) {
      if (this.scrollView && this.scrollView.current) {
        this.scrollView.current.scrollToPosition(0, this.depositPayY, true);
      }
      xShowToast({ title: texts.depositPaymentTip, duration: 3000 });
      CarLog.LogCode({
        name: '点击_填写页_押金支付方式验证',

        data: curDepositPayInfo,
      });
      return;
    }

    // @zxy 去掉enName中的状态
    // traceCode 用于关联 点击提交订单和支付回调埋点
    AppContext.setBookingTraceCode();
    ProductReqAndResData.setData(
      ProductReqAndResData.keyList.createOrderClickId,
      uuid(),
    );
    const productReqReference = getProductRequestReference();
    const vehicleCodeBigId = getVehicleCodeBigId();
    CarLog.LogCode({
      name: '点击_填写页_底部_去支付',
      showPayMode,
      payMode,
      insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
      insuranceId: selectedInsuranceId,
      traceCode: AppContext.Booking.traceCode,
      isRefactor: isRefactor ? '1' : '',
      data: {
        ...bookPriceTrackInfo,
        depositPayType,
        createOrderClickId: ProductSelectors.getCreateOrderClickId(),
        isContainInstalments: !!selectedLoanPayStageCount,
      },
      iseasyLife2024: isEasyLife2024,
      info: {
        isRecommendOrder: isNoResultNew ? '1' : '0',
        requestId: getRequestId(),
        newRecommendType: this.props.newRecommendType,
        vehicleId: productReqReference.kVehicleId,
        vehicleCodeBigId,
        groupId: activeGroupId,
        serverRequestId: AppContext.UserTrace.serverRequestId,
        vehicleIndex,
      },
    });
    CarLog.LogTrace({
      key: LogKey.cm_car_app_click_airanking,
      info: {
        name: '点击_填写页_底部_去支付',
        newMergeId: AppContext.currentNewMergeId,
        vehicleId: productReqReference.kVehicleId,
        serverRequestId: getServerRequestId(),
        requestId: getListRequestId(),
      },
    });
    this.needRefreshPrice = true;

    // 关闭费用明细弹层
    if (Utils.isCtripIsd() && this.state.priceDetailModalVisible) {
      this.closePriceDetailModal();
    }
    // 展示个人信息授权勾选弹窗
    const showPersonalInfoCheckModal =
      !this.personalInfoChecked && remoteQConfig?.personalInfoAuthCheck;
    // 检测是否选购了自营险
    if (this.selectInsuranceStatus()) {
      if (showPersonalInfoCheckModal) {
        this.onCheckBarPress();
      } else {
        this.handleGoToInsConfirmPage();
      }
    } else if (showPersonalInfoCheckModal) {
      this.onCheckBarPress();
    } else {
      this.continueCreateOrder();
    }
  };

  checkedAndToBook = () => {
    this.onCheckBarCheck(true);
    this.hidePersonalInfoAuthModal();
    this.handleBookPress();
  };

  selectInsuranceStatus = () => {
    const { selectedInsuranceId, config: remoteQConfig } = this.props;
    // 检测是否选购了自营险
    return (
      Utils.isCtripIsd() &&
      lodashGet(selectedInsuranceId, 'length') > 0 &&
      remoteQConfig?.insuranceFlag
    );
  };

  onConfirmFightNo() {
    if (this.scrollView && this.scrollView.current) {
      this.scrollView.current.scrollToPosition(0, this.dirverY + 120, true);
    }
  }

  onCancelFightNo = () => {
    // TODO push to list to filter
    this.push('List');
  };

  onPressCouponModalButton = type => {
    const { setCouponPreValidationModalVisible, queryPriceInfo } = this.props;
    const imUrl = getImAddress({
      pageId: Channel.getPageId().Book.ID,
      isPreSale: 1,
    });
    switch (type) {
      case ButtonAction.scrollToCoupon:
        setCouponPreValidationModalVisible(false, null);
        queryPriceInfo();
        if (this.scrollView && this.scrollView.current) {
          // 优惠券模块滚动到屏幕正中间位置计算（优惠券模块置顶滚动距离 - （屏幕一半高度 - 优惠券模块一半高度）- 头部修正高度）
          const couponY =
            this.couponY +
            this.dirverY -
            (vh(50) -
              this.couponHeight / 2 -
              (DEFAULT_HEADER_HEIGHT + fixOffsetTop()));
          this.scrollView.current.scrollToPosition(0, couponY, true);
        }
        break;
      case ButtonAction.goToIM:
        setCouponPreValidationModalVisible(false, null);
        xRouter.navigateTo({ url: imUrl });
        break;
      case ButtonAction.closeModal:
      default:
        setCouponPreValidationModalVisible(false, null);
        break;
    }
  };

  onLayoutCoupon = e => {
    this.couponY = e.nativeEvent.layout.y;
    this.couponHeight = e.nativeEvent.layout.height;
  };

  onLayoutDriver(e) {
    this.dirverY = e.nativeEvent.layout.y;
  }

  onLayoutDepositPay = e => {
    this.depositPayY = e.nativeEvent.layout.y;
  };

  onLayoutOsdDepositPay = e => {
    const layoutY = e?.nativeEvent?.layout?.y;
    if (layoutY) {
      this.osdDepositPayY = layoutY + 260;
    }
  };

  getDepositTestId = () => {
    const { bookPriceTrackInfo } = this.props;
    return CarLog.createExposureId(
      LogKey.c_car_trace_book_deposit_payment_exposure_222017,
      {
        bookPriceTrackInfo,
      },
    );
  };

  getCouponTestId = data =>
    CarLog.createExposureId(LogKey.c_car_exposure_write_coupon, data);

  getActivityTestId = data =>
    CarLog.createExposureId(LogKey.c_car_exposure_write_activity, data);

  jumpToGuidePage = (guideTabId?: number) => {
    const { productRentalLocationInfo } = this.props;
    const param = getGuidePageParam(
      guideTabId,
      Utils.isCtripIsd(),
      productRentalLocationInfo,
    );
    AppContext.PageInstance.push(Channel.getPageId().Guide.EN, {
      pageParam: param,
    });
  };

  gotoGuidePage = guideTabId => {
    Keyboard.dismiss();
    if (Utils.isCtripIsd()) {
      const { vehicleInfo } = getBaseResData();
      const { vehicleCode } = vehicleInfo || {};
      const {
        skuId,
        pStoreCode: pstoreCode,
        rStoreCode: rstoreCode,
      } = getProductRequestReference() || {};
      CarLog.LogCode({
        name: '点击_填写页_取还车地图及指引',
        info: {
          skuId,
          pstoreCode,
          rstoreCode,
          vehicleCode,
        },
      });
    }
    this.jumpToGuidePage(guideTabId);
  };

  goBack = () => {
    const { hasBookingConfirmInfo } = this.props;
    CarLog.LogCode({ name: '点击_填写页_返回上级页面' });
    if (Utils.isCtripIsd() && this.isShowConfirmed && hasBookingConfirmInfo) {
      this.setState({ showConfirm: true });
      this.isShowConfirmed = false;
      CarStorage.save(StorageKey.CAR_BOOKING_CONFIRM, '1', '24H', true);
      return;
    }
    this.pop();
  };

  clickSideToolBoxBtn = () => {
    CarLog.LogCode({ name: '点击_填写页_右上角侧边栏按钮点击' });
  };

  onBackAndroid = () => {
    const {
      supplierModalVisible,
      depositIntroduceModalVisible,
      easyLifePopVisible,
      depositRateDescriptionModalVisible,
      setDepositRateDescriptionModalVisible,
      ehiFreeDepositModalVisible,
      setEhiFreeDepositModalVisible,
    } = this.props;
    const {
      priceDetailModalVisible,
      osdPriceDetailModalVisible,
      productConfirmModalVisible,
      driverLicenseModalVisible,
      optimizationStrengthenModalVisible,
      carServiceDetailVisible,
      serviceClaimMoreVisible,
      isShowOptimizeStoreModal,
      personalInfoAuthModalVisible,
      showConfirm,
      addInstructModalVisible,
      isShowCouponModal,
      activityNoteModalVisible,
      approveExplainModalVisible,
    } = this.state;
    if (isShowOptimizeStoreModal) {
      this.hideOptimizeStoreModal();
    } else if (supplierModalVisible) {
      this.props.changeModalStatus({
        supplierModalVisible: false,
      });
    } else if (depositIntroduceModalVisible) {
      this.props.changeModalStatus({
        depositIntroduceModalVisible: false,
      });
    } else if (priceDetailModalVisible) {
      this.closePriceDetailModal();
    } else if (osdPriceDetailModalVisible) {
      this.closeOsdPriceDetailModal();
    } else if (easyLifePopVisible) {
      this.props.onPressEasyLife(false);
    } else if (productConfirmModalVisible) {
      this.closeProductConfirmModal();
    } else if (driverLicenseModalVisible) {
      this.closeDriverLicenseModal();
    } else if (carServiceDetailVisible) {
      this.hideCarServiceDetail();
    } else if (serviceClaimMoreVisible) {
      this.hideServiceClaimMore();
    } else if (optimizationStrengthenModalVisible) {
      this.closeOptimizationStrengthenModal();
    } else if (personalInfoAuthModalVisible) {
      this.hidePersonalInfoAuthModal();
    } else if (depositRateDescriptionModalVisible) {
      setDepositRateDescriptionModalVisible(false);
    } else if (showConfirm) {
      this.closeBookingConfirmModal();
    } else if (addInstructModalVisible) {
      // 关闭增加多名驾驶员弹层
      this.onCloseSelected();
    } else if (isShowCouponModal) {
      // 关闭优惠券弹层
      this.closeCouponModal();
    } else if (activityNoteModalVisible) {
      // 关闭活动说明弹层
      this.onCloseActivityNoteModal();
    } else if (ehiFreeDepositModalVisible) {
      // 关闭减免规则弹窗
      setEhiFreeDepositModalVisible(false);
    } else if (approveExplainModalVisible) {
      // 关闭个人身份信息授权声明弹层
      this.hideApproveExplainModal();
    } else {
      this.goBack();
    }
  };

  closePriceChangePop = () => {
    this.props.setPriceChangePopIsShow(false);
  };

  // 处理从保代回来后的变价弹层左侧按钮
  handlePriceChangeLeftButton = () => {
    this.closePriceChangePop();
    this.props.refreshList();
    AppContext.PageInstance.pop(Channel.getPageId().List.EN);

    const callbackInsuranceId =
      lodashGet(this.insConfirmData, 'callbackInsuranceId') || [];
    const { showPayMode, payMode, priceChangeCode } = this.props;
    CarLog.LogCode({
      name: '点击_详情页_变价弹层_重新选车',
      priceChangeType: priceChangeCode,
      payMode,
      showPayMode,
      insuranceId: callbackInsuranceId,

      insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
    });
  };

  // 处理从保代回来后的变价弹层右侧按钮
  handlePriceChangeRightButton = () => {
    this.closePriceChangePop();
    /* eslint-disable max-len */
    const preSelectedIns =
      ProductReqAndResData.getData(
        ProductReqAndResData.keyList.priceChangeBeforeIns,
      ) || [];
    const insConfirmInsList =
      lodashGet(this.insConfirmData, 'insConfirmInsList') || [];
    const insuranceAgentToken = lodashGet(this.insConfirmData, 'token');
    const callbackInsuranceId =
      lodashGet(this.insConfirmData, 'callbackInsuranceId') || [];
    const inverseInsuranceIds = InsuranceConfirmUtil.getInverseInsuranceIds(
      preSelectedIns,
      insConfirmInsList,
    );

    const { showPayMode, payMode, priceChangeCode } = this.props;
    CarLog.LogCode({
      name: '点击_详情页_变价弹层_去支付',
      priceChangeType: priceChangeCode,
      payMode,
      showPayMode,
      insuranceId: callbackInsuranceId,

      insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
    });
    this.continueCreateOrder(inverseInsuranceIds, insuranceAgentToken);
  };

  // 处理保险订单创建失败弹层左侧按钮
  handleCreateInsFailPopLeftButton = () => {
    this.props.setCreateInsFailPopIsShow(false);
    setTimeout(() => {
      this.handleGoToInsConfirmPage();
    }, 1000);
  };

  // 处理保险订单创建失败弹层右侧按钮
  handleCreateInsFailPopRightButton = () => {
    this.props.setCreateInsFailPopIsShow(false);
    this.props.setCreateInsLoadingIsShow(false);
    // @ts-ignore
    this.insConfirmData = {
      status: 2,
    };
    this.handleInsConfirmCallback();
  };

  closeBookingConfirmModal = () => {
    this.setState({ showConfirm: false });
  };

  closeBookingConfirmModalWithPop = () => {
    this.setState({ showConfirm: false });
    this.pop();
  };

  // 从保代回来后的变价弹层曝光
  onPriceChangeExposure = () => {
    CarLog.LogTrace({
      key: LogKey.c_car_trace_book_insurance_changeprice_222017,
      info: {
        priceChangeType: this.props.priceChangeCode,
        payMode: this.props.payMode,
        showPayMode: this.props.showPayMode,
        insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
      },
    });
  };

  // 保代页面回退时间
  logInsurancePageBackTime = () => {
    if (this.isForwardInsPage) {
      this.insuranceEndTime = new Date().getTime();
    }
  };

  // 保代页面停留时长
  logInsurancePageActiveTime = () => {
    if (this.isForwardInsPage) {
      this.insuranceEndTime = new Date().getTime();

      CarLog.LogTrace({
        key: LogKey.c_car_trace_book_insurance_activetime_222017,
        info: {
          insuranceBeginTime: this.insActiveTime,
          insuranceEndTime: this.insuranceEndTime,
          payMode: this.props.payMode,
          showPayMode: this.props.showPayMode,
          insuranceActiveTime: this.insuranceEndTime - this.insActiveTime,
          insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
        },
      });
      this.isForwardInsPage = false;
      this.insActiveTime = 0;
      this.insuranceEndTime = 0;
    }
  };

  // 关闭额外驾驶员说明模态框
  onCloseSelected = () => {
    this.setState({
      addInstructModalVisible: false,
    });
  };

  gotoExtras = () => {
    CarLog.LogCode({
      name: '点击_详情页_附加产品',

      modalVisible: true,
    });
    this.push(Channel.getPageId().Extras.EN);
  };

  getOrderListUrlHost = () => {
    const { env } = Application;
    switch (env) {
      case 'dev':
      case 'fat':
        return 'http://order.car.fat4556.qa.nt.ctripcorp.com';
      case 'uat':
        return 'http://order.car.uat.qa.nt.ctripcorp.com';
      case 'prod':
      default:
        return 'http://order.car.ctripcorp.com';
    }
  };

  getModifyOrderModalData = () => {
    // @ts-ignore
    switch (AppContext.fromType) {
      case '1': // 修改订单的取消重订
        return {
          visible: true,
          title: modifyOrderModalText.submitSuccess.title,
          contentText: modifyOrderModalText.submitSuccess.content,
          titleHeightlightStyle: TitleHightlightType.Success,
        };
      default:
        return {};
    }
  };

  closePriceDetailModal = () => {
    this.setState({ priceDetailModalVisible: false });
  };

  setPriceDetailModal = () => {
    this.setState({
      priceDetailModalVisible: !this.state.priceDetailModalVisible,
    });
  };

  closeOsdPriceDetailModal = () => {
    this.setState({ osdPriceDetailModalVisible: false });
  };

  setOsdPriceDetailModal = () => {
    this.setState({
      osdPriceDetailModalVisible: !this.state.osdPriceDetailModalVisible,
    });
  };

  showEasyLifeModal = () => {
    this.props.onPressEasyLife(true);
  };

  closeProductConfirmModal = () => {
    this.setState({ productConfirmModalVisible: false });
  };

  openProductConfirmModal = (productConfirmAnchor: LayoutPartEnum) => {
    const config = {
      [LayoutPartEnum.VehicleConfig]: '点击_门店信息',
      [LayoutPartEnum.Mileage]: '点击_限制政策',
      [LayoutPartEnum.VehicleDetail]: '点击_门店信息',
      [LayoutPartEnum.PickUpMaterials]: '点击_填写页_取车证件说明',
    };
    const type = config[productConfirmAnchor];
    if (type) {
      CarLog.LogCode({
        key: LogKey.CLICK_KEY,
        name: type,
        pageId: Channel.getPageId().Book.ID,
      });
    }
    this.setState({ productConfirmModalVisible: true, productConfirmAnchor });
  };

  closeOptimizationStrengthenModal = () => {
    this.setState({ optimizationStrengthenModalVisible: false });
  };

  handleEtcIntroModalClose = () => {
    const { setEtcIntroModal } = this.props;
    setEtcIntroModal(false);
  };

  openOptimizationStrengthenModal = () => {
    this.setState({ optimizationStrengthenModalVisible: true });
  };

  onPressLimit = () => {
    Keyboard.dismiss();
    this.openProductConfirmModal(LayoutPartEnum.Mileage);
  };

  onPressVendor = () => {
    Keyboard.dismiss();
    this.openProductConfirmModal(LayoutPartEnum.VehicleDetail);
  };

  onPressLocation = () => {
    Keyboard.dismiss();
    this.openProductConfirmModal(LayoutPartEnum.PickupAndDrop);
  };

  onPressCancelRule = () => {
    Keyboard.dismiss();
    this.openProductConfirmModal(LayoutPartEnum.CancelInfo);
    CarLog.LogCode({
      name: '点击_填写页_取消政策',
    });
  };

  onPickupDriveTimeCalculated = (time: number) => {
    AppContext.setDriveTime(time);
  };

  isShelvesSoldOutAll = productKey => {
    const { curFloor, saleOutList } = this.props;
    const soldOutVenderList = [...saleOutList, productKey];
    const unSoldOutPackage = curFloor?.packageList?.find(pItem => {
      const curKey = Utils.getProductKey(pItem.reference);
      return !soldOutVenderList.includes(curKey);
    });
    return !unSoldOutPackage;
  };

  popToVendorList = (type?: PriceAlertType) => {
    const {
      fromPage,
      addSaleOutList,
      addRecommendSaleOutList,
      addVehicleSaleOutList,
    } = this.props;
    if (type === PriceAlertType.SoldOut) {
      const productReqReference = getProductRequestReference();
      const productKey = Utils.getProductKey(productReqReference);
      if (fromPage === Channel.getPageId().RecommendVehicle.EN) {
        addRecommendSaleOutList(productKey);
      } else {
        addSaleOutList(productKey);
        const isShelves2 = CarServerABTesting.isISDShelves2B();
        // 货架2.0由于详情页车型轮转导致展示报价与列表页报价数量不一致时，需判断详情页全部报价售罄是否增加售罄车型列表用于列表页展示售罄
        if (isShelves2 && this.isShelvesSoldOutAll(productKey)) {
          addVehicleSaleOutList(`${productReqReference.kVehicleId}`);
        }
      }
      // 盲盒售罄回到列表页强刷，因为是按价格顺排，不存在售罄。
      if (getProductRequestReference()?.secretBoxParam) {
        setListStatusData(listStatusKeyList.refreshFromVendorListPage, true);
      }
    }
    AppContext.PageInstance.pop(fromPage || Channel.getPageId().VendorList.EN);
  };

  // 各种异常状态点击处理
  priceAlertHandler = (type?: PriceAlertType) => {
    switch (type) {
      case PriceAlertType.PriceChange:
      case PriceAlertType.Error:
      case PriceAlertType.PriceCacheError:
        // 重试
        this.props.queryProduct({ reset: true, isStartPriceTimer: true });
        break;
      default:
        // 回退到新详情页
        this.popToVendorList(type);
    }
  };

  initListData = () => {
    const { firstScreenParam } = this.props;
    ProductReqAndResData.setData(
      ProductReqAndResData.keyList.bookingFirstScreenParam,
      firstScreenParam,
    );
  };

  initLogInfo = () => {
    const { setLogInfo, vendorPriceInfo } = this.props;
    if (setLogInfo) {
      setLogInfo({ allTags: vendorPriceInfo?.allTags });
    }
  };

  getActivityNoteModalProps = () => {
    const { activityDetail } = this.props;
    const { activityNoteModalVisible, currentActivityCode } = this.state;
    const currentPromotion = activityDetail?.promotions?.find(
      item => item.code === currentActivityCode,
    );
    const { title = '', description = '' } = currentPromotion || {};
    // 处理描述内容，兼容有无换行符的情况
    const content = description
      ? description.includes('\n')
        ? description.split('\n').slice(1) // 有换行符时，移除第一行
        : [description] // 无换行符时，使用完整描述
      : [];
    return {
      visible: activityNoteModalVisible,
      onMaskPress: this.onCloseActivityNoteModal,
      style: styles.shadow,
      title: `${title}${texts.couponActivity}`,
      content,
      hasActivityBg: true,
      location: 'bottom',
    };
  };

  // 调整价格说明弹窗Props
  getAdjustPriceNoteModalProps = () => {
    const { adjustPriceDetail } = this.props;
    const { adjustPriceNoteModalVisible } = this.state;
    const { desc, shortDesc } = adjustPriceDetail || {};
    return {
      visible: adjustPriceNoteModalVisible,
      onMaskPress: this.onCloseAdjustPriceNoteModal,
      style: styles.shadow,
      title: shortDesc,
      content: [desc],
      hasActivityBg: true,
      location: 'bottom',
    };
  };

  showOptimizeStoreModal = () => {
    this.setState({
      isShowOptimizeStoreModal: true,
    });
  };

  hideOptimizeStoreModal = () => {
    this.setState({
      isShowOptimizeStoreModal: false,
    });
  };

  showServiceClaimMore = () => {
    this.setState({
      serviceClaimMoreVisible: true,
    });
  };

  hideServiceClaimMore = () => {
    this.setState({
      serviceClaimMoreVisible: false,
    });
  };

  showCarServiceDetail = code => {
    Keyboard.dismiss();
    this.setState({
      carServiceDetailVisible: true,
      showServiceDetailCode: code,
    });
  };

  hideCarServiceDetail = () => {
    this.setState({
      carServiceDetailVisible: false,
      showServiceDetailCode: '',
    });
  };

  closeCouponModal = () => {
    this.setState({
      isShowCouponModal: false,
    });
  };

  showDriverLicenseModal = () => {
    const { driversMap, passenger } = this.props;
    const { vehicleInfo, vendorInfo } = getBaseResData();
    const { vendorCode, bizVendorCode } = vendorInfo || {};
    const { vehicleCode } = vehicleInfo || {};
    const driverLicenseValues = driversMap?.driverLicense?.split('|');
    CarLog.LogCode({
      name: '点击_填写页_驾照切换',

      info: {
        vendorCode,
        vendorId: bizVendorCode,
        vehicleCode,
        nationality: passenger?.nationality,
        driverLicenseCountry: driversMap?.driverLicenseName,
        driverLicenseType: driverLicenseValues?.[1],
        driverAge: driversMap?.age,
      },
    });
    this.setState({
      driverLicenseModalVisible: true,
    });
  };

  closeDriverLicenseModal = () => {
    this.setState({
      driverLicenseModalVisible: false,
    });
  };

  handleFuelDescClose = () => {
    const { setFuelDescriptionModalVisible } = this.props;
    setFuelDescriptionModalVisible(false);
  };

  hideLocalContactsModal = () => {
    this.setState({
      localContactsModalVisible: false,
    });
  };

  showLocalContactsModal = () => {
    Keyboard.dismiss();
    this.setState({
      localContactsModalVisible: true,
    });
    CarLog.LogCode({ name: '点击_填写页_其他联系方式_切换' });
  };

  localContactsInputFocus = isFocus => {
    this.setState({
      localContactsInputIsFocus: isFocus,
    });
  };

  onSelectContactType = item => {
    const {
      pickUpAreaCode,
      changeFormData,
      localContactsData,
      changeLocalContactsData,
    } = this.props;
    let value = '';
    let contactType = -1;
    if (!localContactsData?.length) return;
    const curLocalContactsData = localContactsData?.map(info => {
      const isSelected = item.contactType === info.contactType;
      if (isSelected) {
        value = info.data;
        contactType = info.contactType;
      }
      return {
        ...info,
        isSelected: item.contactType === info.contactType,
      };
    });
    if (contactType > -1) {
      CarLog.LogCode({
        name: '点击_填写页_其他联系方式_选择',

        info: {
          uidTypeSelected: contactType,
        },
      });
    }
    changeLocalContactsData(curLocalContactsData);
    changeFormData([
      {
        type: 'localContacts',
        contactType: item.contactType,
        contactTypeName:
          item.contactType === ContactType.localPhone
            ? `+${pickUpAreaCode}`
            : item.title,
        value,
      },
    ]);
  };

  getApproveExplainModalBtnFn = () => {
    const { personalInfoAuthModalVisible, personalInfoChecked } = this.state;
    if (personalInfoAuthModalVisible) {
      return this.checkedAndToBook;
    }
    if (personalInfoChecked) {
      return this.handleBookPress;
    }
    return this.onCheckBarCheck;
  };

  handleBusinessLicenseClose = () => {
    const { setBusinessLicenseModalVisible } = this.props;
    setBusinessLicenseModalVisible(false);
  };

  gotoExtrasV2 = () => {
    EventHelper.sendEvent(EventName.showProductExtraInfoModal, null);
    this.setEnableDragBack(false);
  };

  closeExtrasInfoModal = () => {
    this.setState({
      extrasInfoVisible: false,
    });
  };

  setAsMobile = asMobileNumber => {
    const { changeLocalContactsData, localContactsData, changeFormData } =
      this.props;
    let contactType = -1;
    const curLocalContactsData = localContactsData?.map(item => {
      if (item.isSelected) {
        // eslint-disable-next-line prefer-destructuring
        contactType = item.contactType;
      }
      return {
        ...item,
        data: item.isSelected ? asMobileNumber : item.data,
      };
    });
    if (contactType > -1) {
      CarLog.LogCode({
        name: '点击_填写页_其他联系方式_同手机号',

        info: {
          uidTypeSelected: contactType,
        },
      });
    }
    changeLocalContactsData(curLocalContactsData);
    changeFormData([
      {
        type: 'localContacts',
        value: asMobileNumber,
      },
    ]);
    Keyboard.dismiss();
  };

  getAsMobileNumber = () => {
    return '';
  };

  showNoLimitModal = noLimitDesc => {
    this.setState({
      noLimitModalVisible: true,
      noLimitDesc,
    });
  };

  goMap = limitScope => {
    this.push(Channel.getPageId().LimitMap.EN, { limitScope });
  };

  hideNoLimitModal = () => {
    this.setState({
      noLimitModalVisible: false,
    });
  };

  getHeaderExpInfo = () => {
    const { vehicleInfo, commodityDescDTO } = getBaseResData();
    const { vehicleCode, name, license } = vehicleInfo || {};
    const { vehicleBasicInfo, carAgeLabel, packageDesc } =
      commodityDescDTO || {};
    const { storeGuidInfos } = this.props;
    let pickUpStoreInfo = '';
    let dropOffStoreInfo = '';
    const mergeInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.Merge,
    );
    if (mergeInfo) {
      pickUpStoreInfo = mergeInfo?.storeGuid;
      dropOffStoreInfo = mergeInfo?.storeGuid;
    } else {
      pickUpStoreInfo = storeGuidInfos.find(
        item => item.type === IGuidInfoType.PickUp,
      )?.storeGuid;
      dropOffStoreInfo = storeGuidInfos.find(
        item => item.type === IGuidInfoType.DropOff,
      )?.storeGuid;
    }
    return {
      vehicleName: name,
      vehicleCode,
      license,
      vehicleBasicInfo,
      carAge: carAgeLabel,
      insuranceInfo: packageDesc,
      pickUpStoreInfo,
      dropOffStoreInfo,
    };
  };

  renderPage() {
    const {
      positivePolicies,
      activityDetail,
      adjustPriceDetail,
      cancelRuleInfo,
      couponList,
      invoiceInfo,
      confirmInfo,
      payParams,
      selectedIdType,
      isFail,
      depositPayType,
      curInsPackageId,
      isShowPriceConfirm,
      membershipPerception,
      isProductLoading,
      needDownGrade,
      ptime,
      rtime,
      addOnCodes,
      isRebook,
      resCancelFeeRebook,
      changeSelectInsurance,
      ctripOrderId,
      rebookPenalty,
      setRebookPenalty,
      fromPage,
      isMergeDeposit,
      changeCoupon,
      isPriceLoading,
      toolBoxCustomerJumpUrl,
      isSecretBox,
      config: remoteQConfig,
      agreeSubmitName = texts.agreeAndBook,
      isKlb,
      changeFormData,
      currenctTotalPrice,
      driverLicenseItems,
      curDriverLicense,
      selectCurDriverLicense,
      passenger,
      isBusinessLicenseModalVisible,
      isRefactor,
      addInstructData,
      isEasyLife2024,
      hasExtrasProducts,
      isFuelDescriptionModalVisible,
      isRebookOsd,
      osdModifyOrderNote,
      pickUpAreaCode,
      logBaseInfo,
      isEtcIntroModalVisible,
      isHasEtcIntroModal,
      cancelEncourage,
      storeGuidInfos,
      queryPriceInfo,
    } = this.props;
    const {
      isFinishRender,
      opacity,
      addInstructModalVisible,
      approveExplainModalVisible,
      approveExplain,
      isShowModifyOrderModal,
      isShowOptimizeStoreModal,
      productConfirmModalVisible,
      optimizationStrengthenModalVisible,
      productConfirmAnchor,
      priceDetailModalVisible,
      osdPriceDetailModalVisible,
      serviceClaimMoreVisible,
      carServiceDetailVisible,
      showServiceDetailCode,
      isShowApplyPenaltyInputModal,
      isShowCouponModal,
      personalInfoAuthModalVisible,
      personalInfoChecked,
      driverLicenseModalVisible,
      showConfirm,
      isShowTableArrow,
      keyboardHeight,
      localContactsModalVisible,
      localContactsInputIsFocus,
      noLimitModalVisible,
      noLimitDesc,
    } = this.state;
    const { amount, canRefund } = resCancelFeeRebook || {};
    const isHasPenalty = canRefund && amount > 0;
    const rebookPenaltyTip = `${rebookPenaltyTipText(`${texts.rmb}${amount}`)}${
      rebookPenalty ? rebookApplyPenaltyTip(`${texts.rmb}${rebookPenalty}`) : ''
    }`;

    const tipText = isHasPenalty ? rebookPenaltyTip : rebookTipText;
    const rentalGuaranteeV2 = getRentalGuaranteeV2(curInsPackageId);
    const asMobileNumber = this.getAsMobileNumber();

    const hasPriceInfo = !!cancelRuleInfo;
    if (isFail) {
      return (
        <NoMatch
          onPressLeft={this.goBack}
          operateButtonPress={this.queryProduct}
        />
      );
    }
    const DepositPayment = Utils.getComponentByChannel(
      depositPaymentComponentConfig(isCreditRent()),
    );

    const pageModalProps = {
      visible: addInstructModalVisible,
      onMaskPress: this.onCloseSelected,
      style: styles.shadow,
    };

    const handlePressHeaderRight = Utils.isCtripIsd()
      ? this.onPressTandC
      : this.clickSideToolBoxBtn;

    const hasFirstScreenParam = getBookingFirstScreenParam();
    if (isProductLoading && !hasFirstScreenParam) {
      return (
        <BookingLoading pageName={PageType.BookingIsdLoadingAll}>
          <Header
            opacity={opacity}
            onPressLeft={this.goBack}
            onPressRight={handlePressHeaderRight}
          />
        </BookingLoading>
      );
    }
    const couponTestID = this.getCouponTestId({
      hasCoupon: couponList?.usableCoupons?.length ? 1 : 0,
      vendorCode: getBaseResData()?.vendorInfo?.vendorCode,
      vendorId: getBaseResData()?.vendorInfo?.bizVendorCode,
      ifCoupon: couponList?.usableCoupons?.length ? 1 : 0,
      orderGmv: currenctTotalPrice,
      couponPrice: couponList?.selectedCoupon?.deductionAmount,
      couponPromotionId: couponList?.selectedCoupon?.promotionId,
    });

    const activityLogData = this.getActivityLog();

    const activityTestID = this.getActivityTestId({
      hasActivity:
        activityDetail?.promotion &&
        Object.keys(activityDetail?.promotion)?.length
          ? 1
          : 0,
      ifActivity: activityDetail?.status === 0 ? 0 : 1,
      ...activityLogData,
    });

    const BookForm = BookingFormContainer;
    const isShelves2 = CarServerABTesting.isISDShelves2B();
    const isRP = getIsRP();
    const VehicleAndVendorInfoWrapper = isShelves2 ? View : React.Fragment;
    const { vehicleInfo } = getBaseResData();
    const { name: vehicleName, license, esgInfo } = vehicleInfo || {};
    const isNewEnergy = esgInfo?.reducedCarbonEmissionRatio > 0;
    const logExpInfo = this.getHeaderExpInfo();
    const showIncentive =
      !GetABCache.isISDShelves3C() && GetABCache.isISDShelves3();
    return (
      <BookingWrapper>
        <HeaderNew
          vehicleName={vehicleName}
          license={license}
          isNewEnergy={isNewEnergy}
          onPressLeft={this.goBack}
          logExpInfo={logExpInfo}
        />

        <KeyboardAwareScrollView
          showsVerticalScrollIndicator={false}
          ref={this.scrollView}
          style={xMergeStyles([
            layout.flex1,
            Utils.isCtripIsd() && isHarmony && { backgroundColor: color.white },
          ])}
          onScrollBeginDrag={Keyboard.dismiss}
          onScroll={this.onScroll}
          scrollEventThrottle={50}
          enableResetScrollToCoords={false}
          keyboardDismissMode="on-drag"
          keyboardShouldPersistTaps="handled"
          automaticallyAdjustContentInsets={false}
          keyboardOpeningTime={100}
          extraHeight={100}
          bounces={false}
        >
          {Utils.isCtripIsd() && (
            <ModifyOrderInfoExplain
              modifyRuleExplain={this.props.modifyOrderDesc}
              style={styles.modifyRuleExplainWrap}
              isBook={true}
            />
          )}
          <VehicleAndVendorInfoWrapper className={c2xStyles.radiusWrap}>
            <BookingVehicleAndVendorInfoVCContainer
              onLocationPress={this.onPressLocation}
              onPressVendor={this.onPressVendor}
              onPressCancelRule={this.onPressCancelRule}
              isShowLoading={isProductLoading}
              isShelves2={isShelves2}
              cancelEncourage={cancelEncourage}
              storeGuidInfos={storeGuidInfos}
            />
          </VehicleAndVendorInfoWrapper>

          {showIncentive && (
            <IncentiveMarqueeVertical
              enableAnimate={positivePolicies?.length > 1}
              data={positivePolicies}
              isShowLoading={isPriceLoading}
            />
          )}

          <FormWrapper isShelves2={isShelves2} onLayout={this.onLayoutDriver}>
            {!isShelves2 && Utils.isCtripIsd() && isFinishRender && (
              <AdvantageInfo
                positivePolicies={positivePolicies}
                membershipPerception={membershipPerception}
              />
            )}
            <BookForm
              isShowLoading={isProductLoading}
              onPressDriver={this.onPressDriver}
              onPressQuestion={this.onPressFormQuestion}
              onPressPickUpMaterials={this.onPressPickUpMaterials}
              onPressFlightDelayRules={this.onPressFlightDelayRules}
              onPresssMore={this.handlePresssMore}
              onPressDriverLicense={this.showDriverLicenseModal}
              onPressLocalContacts={this.showLocalContactsModal}
              localContactsInputFocus={this.localContactsInputFocus}
            />

            {Utils.isCtripIsd() && (
              <BookingCouponAndDepositContainer
                couponTestID={couponTestID}
                activityTestID={activityTestID}
                couponList={couponList}
                activityDetail={activityDetail}
                currency={payParams && payParams.currency}
                onPressCoupon={this.onPressCoupon}
                onPressActivity={this.onPressActivity}
                toSelectPassenger={this.onPressDriver}
                queryPriceInfo={queryPriceInfo}
                isShowLoading={
                  isPriceLoading && !(isFinishRender && hasPriceInfo)
                }
              />
            )}

            {((isProductLoading && hasFirstScreenParam) ||
              (isShelves2 && !isFinishRender)) && (
              <BookingLoading
                pageName={PageType.BookingLoadingHalf}
                style={
                  Utils.isCtripIsd()
                    ? styles.bookingOptimizationLoadingSpace
                    : styles.loadingSpace
                }
              />
            )}

            {hasPriceInfo && !isMergeDeposit && (
              <XViewExposure
                onLayout={this.onLayoutDepositPay}
                testID={this.getDepositTestId()}
              >
                <DepositPayment onPressDriver={this.onPressDriver} />
              </XViewExposure>
            )}

            {Utils.isCtripIsd() && !isProductLoading && isFinishRender && (
              <Insurance
                onPressServiceClaimMore={this.showServiceClaimMore}
                onPressCarServiceDetail={this.showCarServiceDetail}
                ptime={ptime}
                rtime={rtime}
                isShowTableArrow={isShowTableArrow}
              />
            )}
            {isFinishRender && (
              <>
                {Utils.isCtripIsd() && (
                  <Extras
                    onPressExtras={this.gotoExtras}
                    style={styles.extrasInfo}
                  />
                )}
                {Utils.isCtripIsd() ? (
                  <BlockCard
                    title={invoiceInfo?.title}
                    desc={invoiceInfo?.description}
                    testID={CarLog.LogExposure({
                      name: '曝光_填写页_发票模块',
                    })}
                  />
                ) : (
                  <InvoiceInfo
                    invoiceInfo={invoiceInfo}
                    testID={CarLog.LogExposure({
                      info: logBaseInfo,
                      name: '曝光_填写页_发票模块',
                    })}
                  />
                )}
              </>
            )}
          </FormWrapper>
          {isFinishRender && (
            <XViewExposure
              testID={CarLog.LogExposure({
                name: '曝光_填写页_租车服务提供方',
              })}
            >
              <GpdrContainer
                onPressTandC={this.onPressTandC}
                onPressCtripDepositFee={this.onPressCtripDepositFee}
                showCtripDepositFee={isCreditRentPayType(depositPayType)}
                onPressExplain={this.showApproveExplainModal}
                isSecretBox={isSecretBox}
                showPersonalAuthCheck={remoteQConfig?.personalInfoAuthCheck}
                onPressSelfServiceInstruction={
                  this.onPressSelfServiceInstruction
                }
                isBookIsdGS3={true}
              />
            </XViewExposure>
          )}
          {/* 安心订放心行 */}
          {isFinishRender && (
            <View className={c2xStyles.bookingOptimizationRelievedContainer}>
              <MarketingFooter
                wrapStyle={styles.bookingOptimizationRelievedBookingMtNew}
                bottomImg={`${ImageUrl.DIMG04_PATH}1tg4c12000dg92m3rDFED.png`}
                isBookIsdGS3={true}
              />
            </View>
          )}
        </KeyboardAwareScrollView>
        {!!(
          !!asMobileNumber &&
          localContactsInputIsFocus &&
          keyboardHeight
        ) && (
          <AsMobileBlock
            setAsMobile={() => this.setAsMobile(asMobileNumber)}
            mobile={asMobileNumber}
            style={{ bottom: keyboardHeight }}
          />
        )}
        {!isProductLoading && (
          <BookingFooter
            onPressBtn={this.handleBookPress}
            onPressBar={this.onPressBar}
            buttonTestID={UITestID.car_testid_page_booking_footer_button}
            barTestID={UITestID.car_testid_page_booking_footer_bar}
            renderTip={
              isRebook ? (
                <RebookTip
                  isHasPenalty={isHasPenalty}
                  isHasReBookPenalty={!!rebookPenalty}
                  tip={tipText}
                  showApplyPenaltyInputModal={this.showApplyPenaltyInputModal}
                />
              ) : (
                !this.state.priceDetailModalVisible && <DepositTip />
              )
            }
            isShowFreeCancelLabel={cancelRuleInfo?.showFree}
            buttonTextStyle={styles.bookingOptimizationButtonTextStyle}
            isShowTotalAmount={true}
            showPriceDetailModal={priceDetailModalVisible}
            style={styles.footBarWrap}
          />
        )}
        {isFinishRender && (
          <BookingModalsContainer
            onGotoOrderDetail={this.goToOrder}
            onConfirmFightNo={this.onConfirmFightNo}
            onCancelFightNo={this.onCancelFightNo}
            onPressCouponModalButton={this.onPressCouponModalButton}
            fromPage={fromPage}
          />
        )}
        {this.state.isSnapShotRender && (
          <BookingSnapShot
            productConfirmRef={this.materialModalRef}
            priceDetailsRef={this.priceDetailsRef}
          />
        )}
        {Utils.isCtripIsd() && (
          <CouponModal
            onSelected={changeCoupon}
            visible={isShowCouponModal}
            onCancel={this.closeCouponModal}
            renderData={couponList}
          />
        )}
        <BookingPriceChangePop
          onClose={this.closePriceChangePop}
          onBackToList={this.handlePriceChangeLeftButton}
          onSubmit={this.handlePriceChangeRightButton}
          onExposure={this.onPriceChangeExposure}
        />

        {Utils.isCtripIsd() && needDownGrade && isFinishRender && (
          <Distance
            onPickupDriveTimeCalculated={this.onPickupDriveTimeCalculated}
          />
        )}
        {isFinishRender && (
          <>
            {isHasPenalty && (
              <ApplyPenaltyInputModal
                orderId={ctripOrderId}
                setRebookPenalty={setRebookPenalty}
                rebookPenalty={rebookPenalty}
                visible={isShowApplyPenaltyInputModal}
                onMaskPress={this.hideApplyPenaltyInputModal}
                resCancelFee={resCancelFeeRebook}
                headerDom={
                  <RebookTip
                    isHasPenalty={isHasPenalty}
                    isHasReBookPenalty={!!rebookPenalty}
                    isShowApplyPenaltyInputModal={isShowApplyPenaltyInputModal}
                    tip={tipText}
                    showApplyPenaltyInputModal={this.hideApplyPenaltyInputModal}
                  />
                }
              />
            )}
            <PersonalAuthCheckModal
              visible={personalInfoAuthModalVisible}
              onMaskPress={this.hidePersonalInfoAuthModal}
              style={styles.shadow}
              onPressBtn={this.handleBookPress}
              onCheckBarCheck={this.onCheckBarCheck}
              onCheckBarPress={this.showApproveExplainModal}
              buttonName={agreeSubmitName}
              personalInfoCheck={personalInfoChecked}
              showPersonalAuthCheck={remoteQConfig?.personalInfoAuthCheck}
            />

            <ApproveExplainModal
              visible={approveExplainModalVisible}
              onMaskPress={this.hideApproveExplainModal}
              style={styles.shadow}
              data={approveExplain}
              onPressBtn={this.getApproveExplainModalBtnFn()}
              hasFooterBtn={remoteQConfig?.personalInfoAuthCheck}
              buttonName={
                (personalInfoChecked || personalInfoAuthModalVisible) &&
                agreeSubmitName
              }
            />

            <HalfPageModal {...this.getActivityNoteModalProps()} />

            {/* 调整价格说明 */}
            <HalfPageModal
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...this.getAdjustPriceNoteModalProps()}
            />

            {Utils.isCtripIsd() && (
              <ProductConfirmModalNew
                page={this}
                visible={productConfirmModalVisible}
                anchor={productConfirmAnchor}
                showFooter={false}
                onPressEasyLife={this.showEasyLifeModal}
                onClose={this.closeProductConfirmModal}
                onPressOptimize={this.showOptimizeStoreModal}
                showOptimizationStrengthenModal={
                  this.openOptimizationStrengthenModal
                }
                isSecretBox={isSecretBox}
                pageName="Booking"
                showCarServiceDetailBooking={this.showCarServiceDetail}
                onPressShowNoLimitModal={this.showNoLimitModal}
              />
            )}
            {Utils.isCtripIsd() && (
              <ServiceClaimMoreModal
                visible={serviceClaimMoreVisible}
                onCancel={this.hideServiceClaimMore}
                data={rentalGuaranteeV2?.vendorServiceDetail}
              />
            )}
            {Utils.isCtripIsd() &&
              (isShelves2 ? (
                <CarServiceDetailModalShelves2
                  visible={carServiceDetailVisible}
                  onCancel={this.hideCarServiceDetail}
                  data={rentalGuaranteeV2?.packageDetailList}
                  purchasingNotice={rentalGuaranteeV2?.purchasingNotice}
                  fromPage={CarServiceFromPageTypes.booking}
                  showServiceDetailCode={showServiceDetailCode}
                  haveFooter={false}
                />
              ) : (
                <CarServiceDetailModal
                  visible={carServiceDetailVisible}
                  onCancel={this.hideCarServiceDetail}
                  data={rentalGuaranteeV2?.packageDetailList}
                  purchasingNotice={rentalGuaranteeV2?.purchasingNotice}
                  ptime={ptime}
                  rtime={rtime}
                  selectedInsuranceIds={addOnCodes}
                  changeSelectInsurance={changeSelectInsurance}
                  fromPage={CarServiceFromPageTypes.booking}
                  showServiceDetailCode={showServiceDetailCode}
                  haveFooter={!isEasyLife2024 || !(isShelves2 && isRP)}
                />
              ))}

            <BookingEasyLifeModal />
            <BookingPriceDetail
              visible={priceDetailModalVisible}
              onClose={this.closePriceDetailModal}
              onContinue={this.closePriceDetailModal}
              style={styles.priceDetailStyle}
              role={PageRole.BOOKING}
            />
            <SesameContainer />
            {isShowModifyOrderModal && (
              <ModifyOrderModal {...this.getModifyOrderModalData()} />
            )}
            <StoreModal
              onMaskPress={this.hideOptimizeStoreModal}
              visible={isShowOptimizeStoreModal}
            />

            <EhiFreeDepositRuleModalContainer isBooking={true} />

            <CancelZhimaModal />
            {Utils.isCtripIsd() && (
              <PriceAlert
                visible={isShowPriceConfirm}
                priceAlertHandler={this.priceAlertHandler}
                popToVendorList={this.popToVendorList}
              />
            )}
            {Utils.isCtripIsd() && <PickupDownGradePop />}
            <VendorListOptimizationStrengthenModal
              visible={optimizationStrengthenModalVisible}
              onCancel={this.closeOptimizationStrengthenModal}
              isSecretBox={isSecretBox}
              pageName="Booking"
            />

            {Utils.isCtripIsd() && !isHasEtcIntroModal && (
              <EtcIntroModalContainer
                visible={isEtcIntroModalVisible}
                onClose={this.handleEtcIntroModalClose}
              />
            )}
            <BookingCreateInsLoading />
            <BookingCreateInsFailPop
              onBack={this.handleCreateInsFailPopLeftButton}
              onPay={this.handleCreateInsFailPopRightButton}
            />

            <AddInstructModal
              pageModalProps={pageModalProps}
              addInstructData={addInstructData}
            />
            {Utils.isCtripIsd() && (
              <BookingConfirmModal
                visible={showConfirm}
                onConfirm={this.closeBookingConfirmModal}
                onCancel={this.closeBookingConfirmModalWithPop}
              />
            )}
            {Utils.isCtripIsd() && (
              <BusinessLicenseModal
                visible={isBusinessLicenseModalVisible}
                onClose={this.handleBusinessLicenseClose}
              />
            )}
            <NoLimitModal
              visible={noLimitModalVisible}
              onClose={this.hideNoLimitModal}
              content={noLimitDesc}
            />
            <LimitRulesPop goMap={this.goMap} />
          </>
        )}
        {this.props.isDebugMode && (
          <AssistiveTouch
            onPress={() => {
              this.push('Debug');
            }}
          />
        )}
      </BookingWrapper>
    );
  }
}

export default BookingIsd;
