import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import React, { ReactNode } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import { KeyboardAwareScrollView } from '@c2x/extraPackages/react-native-keyboard-aware-scroll-view';
import CText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkComponentPageModal } from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import ModalFooter from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalFooter/src/Index';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Button from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import { InputFormatType } from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import BbkInput from '@ctrip/rn_com_car/dist/src/Components/Basic/Input';
import { font, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './applyPenaltyInputModalC2xStyles.module.scss';
import CancelPenaltyModal from '../OrderDetail/Components/CancelPenaltyModal';
import { Utils } from '../../Util/Index';
import Texts, {
  applyLossyCancelSpacehold,
  lossyAmountInfoRebookFirst,
  lossyAmountInfoSecond,
} from './Texts';
import { UITestID } from '../../Constants/Index';

interface IProps {
  visible: boolean;
  onMaskPress: () => void;
  setRebookPenalty: (data) => void;
  rebookPenalty: string;
  orderId: string | number;
  headerDom?: ReactNode | ReactNode[];
  resCancelFee?: any;
}

interface IState {
  refundPenaltyAmount: string;
  refundPenaltyError: string;
  refundPenaltyInfoFirst: string;
  refundPenaltyInfoSecond: string;
  isShowCancelPenaltyModal: boolean;
}

const { getPixel, getLineHeight } = BbkUtils;
const extraHeight = getPixel(140);
const styles = StyleSheet.create({
  applyBtn: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: getPixel(8),
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    paddingTop: getPixel(23),
    paddingBottom: getPixel(23),
    backgroundColor: color.blueBase,
  },
  cnbuttonDisable: {
    backgroundColor: color.grayBorder,
    borderColor: color.grayBorder,
  },
  cnBtnTextDisable: {
    color: color.grayBase,
  },
  inputWrap: {
    marginTop: getPixel(0),
  },
  keyboardWrap: {
    flexDirection: 'row',
  },
  inputWrapperStyle: {
    paddingTop: getPixel(16),
    paddingBottom: getPixel(12),
  },
  inputStyle: {
    height: getPixel(40),
    ...font.title3MediumStyle,
    lineHeight: getLineHeight(40),
  },
});

class ApplyPenaltyInputModal extends React.Component<IProps, IState> {
  lossyInputRef: any = null;

  constructor(props) {
    super(props);
    this.state = {
      refundPenaltyAmount: '',
      refundPenaltyError: '',
      refundPenaltyInfoFirst: '',
      refundPenaltyInfoSecond: '',
      isShowCancelPenaltyModal: false,
    };
  }

  componentDidUpdate(prevProps) {
    const { rebookPenalty, visible } = this.props;
    const { visible: preVisible } = prevProps;
    if (!preVisible && !!visible) {
      this.setRefundPenaltyAmount(rebookPenalty, true);
    }
  }

  // 取消订单展示的订单总额为订单详情页展示的订单金额
  getAmountText = () => {
    const { resCancelFee } = this.props;
    const { orderAmount } = resCancelFee || {};
    return orderAmount;
  };

  setRefundPenaltyAmount = (refundAmount, isInitial = false) => {
    const amountText = this.getAmountText();
    const { resCancelFee } = this.props;
    const { amount, auditTime = Texts.auditTime } = resCancelFee || {};
    let writeAmount = `${refundAmount}`;
    let penaltyAmount = refundAmount;
    const isNeedRound =
      `${writeAmount}`.substring(writeAmount.indexOf('.') + 1).length > 2;

    if (isNeedRound) {
      penaltyAmount = Utils.toFixed(penaltyAmount, 2);
    }
    if (penaltyAmount > 0) {
      writeAmount = penaltyAmount;
    }

    let errorMessage = '';
    let infoFirst = '';
    let infoSecond = '';
    if (penaltyAmount >= 0) {
      if (penaltyAmount > amount) {
        errorMessage = Texts.lossyAmountValidateErrorRange;
      } else if (penaltyAmount > 0) {
        infoFirst = lossyAmountInfoRebookFirst(
          `${Texts.rmb}${Utils.Sub(amountText, amount)}`,
        );
        infoSecond = `${lossyAmountInfoSecond(
          `${Texts.rmb}${Number(penaltyAmount)}`,
          auditTime,
        )}`;
      } else if (isInitial) {
        infoFirst = lossyAmountInfoRebookFirst(
          `${Texts.rmb}${Utils.Sub(amountText, amount)}`,
        );
        infoSecond = `${lossyAmountInfoSecond('', auditTime)}`;
      } else {
        errorMessage = Texts.lossyAmountValidateError;
      }

      this.setState({
        refundPenaltyAmount: `${writeAmount}`,
        refundPenaltyError: isInitial ? '' : errorMessage,
        refundPenaltyInfoFirst: infoFirst,
        refundPenaltyInfoSecond: infoSecond,
      });
    }
  };

  onChangeAmount = refundAmount => {
    this.setRefundPenaltyAmount(`${refundAmount}`, false);
  };

  lossyInputRefHandle = ref => {
    this.lossyInputRef = ref;
  };

  agreeApply = () => {
    this.setState({
      isShowCancelPenaltyModal: false,
    });
    this.lossyInputRef.focus();
  };

  sureApplyPenalty = () => {
    const { setRebookPenalty = Utils.noop, onMaskPress = Utils.noop } =
      this.props;
    const { refundPenaltyAmount, refundPenaltyError } = this.state;
    if (refundPenaltyError) {
      this.lossyInputRef.focus();
    } else {
      setRebookPenalty({
        rebookPenalty: Number(refundPenaltyAmount),
      });
      onMaskPress();
    }
  };

  showCancelPenaltyModal = () => {
    this.setState({
      isShowCancelPenaltyModal: true,
    });
  };

  hideCancelPenaltyModal = () => {
    this.setState({ isShowCancelPenaltyModal: false });
  };

  closeModal = () => {
    const { onMaskPress = Utils.noop } = this.props;
    Keyboard.dismiss();
    setTimeout(() => {
      onMaskPress();
    }, 50);
  };

  render() {
    const {
      visible,
      onMaskPress = Utils.noop,
      headerDom,
      orderId,
      resCancelFee,
    } = this.props;
    const {
      refundPenaltyAmount,
      refundPenaltyError,
      refundPenaltyInfoFirst,
      refundPenaltyInfoSecond,
      isShowCancelPenaltyModal,
    } = this.state;
    const { auditTime, amount } = resCancelFee || {};
    return (
      <BbkComponentPageModal
        location="bottom"
        animateType="slideUp"
        animateDuration={300}
        visible={visible}
        onMaskPress={onMaskPress}
        closeModalBtnTestID={
          UITestID.car_testid_page_booking_applypenalty_modal_closemask
        }
      >
        <KeyboardAwareScrollView
          style={styles.keyboardWrap}
          showsVerticalScrollIndicator={false}
          onScrollBeginDrag={Keyboard.dismiss}
          scrollEventThrottle={50}
          keyboardDismissMode="on-drag"
          keyboardShouldPersistTaps="handled"
          automaticallyAdjustContentInsets={false}
          keyboardOpeningTime={100}
          extraHeight={extraHeight}
          bounces={false}
        >
          <BbkTouchable
            className={c2xStyles.slideBottomEmpty}
            testID={UITestID.car_testid_page_booking_applypenalty_modal_mask}
            onPress={this.closeModal}
            activeOpacity={1}
          />

          <View
            className={classNames(c2xStyles.wrap, c2xStyles.modalWrap)}
            style={{ minHeight: BbkUtils.vh(40), maxHeight: BbkUtils.vh(90) }}
          >
            {headerDom}
            <View className={c2xStyles.content}>
              <View className={c2xStyles.tipWrap}>
                <CText className={c2xStyles.refundPenaltyTip}>
                  {Texts.applyLossyCancelText}
                </CText>
                <CText
                  type="icon"
                  className={c2xStyles.helpIcon}
                  testID={
                    UITestID.car_testid_page_booking_applypenalty_modal_help
                  }
                  onPress={this.showCancelPenaltyModal}
                >
                  {icon.circleQuestion}
                </CText>
              </View>
              <View>
                <BbkInput
                  style={styles.inputWrap}
                  value={refundPenaltyAmount}
                  isShowTitle={false}
                  leftChildren={
                    <CText className={c2xStyles.inputLeft} fontWeight="medium">
                      {Texts.rmb}
                    </CText>
                  }
                  testID={
                    UITestID.car_testid_page_booking_applypenalty_modal_input
                  }
                  showClearWhileEditing={false}
                  title={applyLossyCancelSpacehold(`${Texts.rmb}${amount}`)}
                  inputWrapperStyle={styles.inputWrapperStyle}
                  inputStyle={styles.inputStyle}
                  formatType={InputFormatType.decimal}
                  onChangeText={this.onChangeAmount}
                  placeholder={applyLossyCancelSpacehold(
                    `${Texts.rmb}${amount}`,
                  )}
                  inputRefHandler={this.lossyInputRefHandle}
                />

                <View className={c2xStyles.validateWrap}>
                  {!!refundPenaltyError && (
                    <CText className={c2xStyles.error}>
                      {refundPenaltyError}
                    </CText>
                  )}
                  {!!refundPenaltyInfoFirst && (
                    <CText className={c2xStyles.info}>
                      {refundPenaltyInfoFirst}
                    </CText>
                  )}
                  {!!refundPenaltyInfoSecond && (
                    <CText className={c2xStyles.info}>
                      {refundPenaltyInfoSecond}
                    </CText>
                  )}
                </View>
              </View>
              {Utils.isCtripIsd() && (
                <CancelPenaltyModal
                  orderId={`${orderId}`}
                  isRebook={true}
                  auditTime={auditTime}
                  onHide={this.hideCancelPenaltyModal}
                  onAgreeApply={this.agreeApply}
                  modalVisible={isShowCancelPenaltyModal}
                />
              )}
            </View>
            <ModalFooter isShadow={true}>
              <View className={c2xStyles.IntegralModalFooterWrap}>
                <Button
                  style={xMergeStyles([
                    styles.applyBtn,
                    !refundPenaltyAmount && styles.cnbuttonDisable,
                  ])}
                  testID={
                    UITestID.car_testid_page_booking_applypenalty_modal_apply
                  }
                  textStyle={!refundPenaltyAmount && styles.cnBtnTextDisable}
                  disabled={!refundPenaltyAmount}
                  onPress={this.sureApplyPenalty}
                  text={Texts.list_apply}
                  buttonSize="L"
                />
              </View>
            </ModalFooter>
          </View>
        </KeyboardAwareScrollView>
      </BbkComponentPageModal>
    );
  }
}

export default ApplyPenaltyInputModal;
