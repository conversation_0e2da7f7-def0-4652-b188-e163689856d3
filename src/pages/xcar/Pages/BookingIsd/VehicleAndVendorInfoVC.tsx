import Image from '@c2x/components/Image';
import {
  xMergeStyles,
  XView as View,
  xClassNames,
  XLinearGradient,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useMemo, CSSProperties } from 'react';
import { BbkUtils, DateFormatter } from '@ctrip/rn_com_car/dist/src/Utils';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import * as ImageUrl from '../../Constants/ImageUrl';
import c2xStyles from './vehicleAndVendorInfoVCC2xStyles.module.scss';
import { IMergeGuidInfo } from './Types';
import RentalInfo from './Component/ISD/RentalInfo';
import RentalDate from '../../Components/Business/RentalDate';
import RentalLocationHead from './Component/ISD/RentalLocationHead';
import { UITestID } from '../../Constants/Index';
import NationalChainTagGray from '../../Components/Business/NationalChainTagGray';
import Text from '../../Common/src/Components/Basic/Text';
import { getProductRequestReference } from '../../Global/Cache/ProductSelectors';
import { CarLog } from '../../Util/Index';
import SkeletonLoading, {
  PageType,
} from '../../ComponentBusiness/SkeletonLoading';

const { getPixel, ensureFunctionCall, useMemoizedFn } = BbkUtils;
const styles = StyleSheet.create({
  noLimitRulesBlueWrapper: {
    paddingBottom: getPixel(16),
  },
  noLimitRulesWhiteWrapper: {
    marginBottom: getPixel(-32),
  },
  loadingBg: {
    backgroundColor: color.white,
    marginBottom: getPixel(20),
  },
});

interface IVehicleAndVendorInfo {
  vehicleName?: string; // 车型名称
  ptime?: string; // 取车时间，格式是****年**月**日**：**
  rtime?: string;
  storeGuidInfos?: Array<IMergeGuidInfo>;
  vendorName?: string; // 供应商名称
  isOptimize?: boolean; // 是否是优选
  nationalChainTagTitle?: string; // 全国连锁名称
  onLocationPress?: () => void;
  onPressVendor?: () => void;
  onPressCancelRule?: () => void;
  wrapStyle?: CSSProperties;
  isShowLoading?: boolean;
  vehicleCode?: string;
  isShelves2?: boolean;
  commodityDescList?: string[];
  cancelEncourage?: string;
}
interface IVendorName {
  isOptimize?: boolean;
  nationalChainTagTitle?: string;
  vendorName: string;
  isShowLoading?: boolean;
}

interface ICancelInfo {
  cancelEncourage?: string;
  onPressCancelRule?: () => void;
  isShowLoading?: boolean;
}
const VendorName: React.FC<IVendorName> = memo(
  ({
    isOptimize,
    nationalChainTagTitle,
    vendorName,
    isShowLoading,
  }: IVendorName) => {
    if (isShowLoading) {
      return (
        <SkeletonLoading
          visible={true}
          style={styles.loadingBg}
          pageName={PageType.BookCancelISD}
        />
      );
    }
    if (!vendorName) return null;
    return (
      <View className={c2xStyles.vendorNameWrap}>
        {!!isOptimize && (
          <Image
            src={`${ImageUrl.DIMG04_PATH}1tg6212000kpqo5v3997E.png`}
            className={c2xStyles.youxuanBg}
            mode="heightFix"
          />
        )}
        {!!nationalChainTagTitle && (
          <NationalChainTagGray tagTitle={nationalChainTagTitle} />
        )}
        <Text className={c2xStyles.vendorName}>{vendorName}</Text>
      </View>
    );
  },
);

const CancelInfo: React.FC<ICancelInfo> = memo(
  ({ cancelEncourage, onPressCancelRule, isShowLoading }: ICancelInfo) => {
    if (isShowLoading) {
      return (
        <SkeletonLoading
          visible={true}
          style={styles.loadingBg}
          pageName={PageType.BookCancelISD}
        />
      );
    }
    if (!cancelEncourage) return null;
    return (
      <Touchable
        className={c2xStyles.cancelInfoWrap}
        onPress={onPressCancelRule}
      >
        <Text className={c2xStyles.cancelInfoText} fontWeight="regular">
          {cancelEncourage}
        </Text>
      </Touchable>
    );
  },
);

const VehicleAndVendorInfo = memo((props: IVehicleAndVendorInfo) => {
  const {
    vehicleName,
    ptime,
    rtime,
    storeGuidInfos,
    vendorName,
    isOptimize,
    nationalChainTagTitle,
    onLocationPress,
    onPressVendor,
    onPressCancelRule,
    wrapStyle,
    isShowLoading,
    vehicleCode,
    isShelves2,
    commodityDescList,
    cancelEncourage,
  } = props;
  const ctripDate = DateFormatter.ctripDateFormat({
    ptime,
    rtime,
    mode: 'bookingHeader',
  });
  const ptimeStr = ctripDate?.pickUpDateStr;
  const rtimeStr = ctripDate?.dropOffDateStr;
  const timeDetail = BbkUtils.isd_dhm(ptime, rtime);
  const { width } = useWindowSizeChanged();
  const width100Style = useMemo(
    () => ({ width: width - getPixel(56) }),
    [width],
  );
  const traceBaseInfo = useMemo(() => {
    const {
      skuId,
      pStoreCode: pstoreCode,
      rStoreCode: rstoreCode,
    } = getProductRequestReference() || {};
    return {
      skuId,
      pstoreCode,
      rstoreCode,
      vehicleCode,
    };
  }, [vehicleCode]);
  const handlePressDetail = useMemoizedFn(() => {
    ensureFunctionCall(onPressVendor());
    CarLog.LogCode({
      name: '点击_填写页_车型详情',
      info: traceBaseInfo,
    });
  });

  const handleLocationPress = useMemoizedFn(() => {
    ensureFunctionCall(onLocationPress());
    CarLog.LogCode({
      name: '点击_填写页_取还车地图及指引',
      info: traceBaseInfo,
    });
  });
  return (
    <>
      <XLinearGradient
        start={{ x: 0.5, y: 1.0 }}
        end={{ x: 0.5, y: 0 }}
        locations={[0, 1]}
        colors={[color.R_255_255_255_0, color.C_FFF]}
        className={c2xStyles.linear}
      />
      <Touchable
        testID={UITestID.car_testid_comp_booking_vehicle_info}
        className={xClassNames(
          isShelves2 ? c2xStyles.shelves2Wrap : c2xStyles.wrapper,
        )}
        style={
          !isShelves2 &&
          xMergeStyles([
            styles.noLimitRulesBlueWrapper,
            styles.noLimitRulesWhiteWrapper,
            wrapStyle,
            width100Style,
          ])
        }
        onPress={handlePressDetail}
      >
        {/** 车型信息 */}
        <RentalInfo
          vehicleName={vehicleName}
          onPress={onPressVendor}
          isShowLoading={isShowLoading}
          vehicleCode={vehicleCode}
          commodityDescList={commodityDescList}
        />
        {/** 取还车时间回显 */}
        <RentalDate
          isShelves2={isShelves2}
          pTime={ptimeStr}
          rTime={rtimeStr}
          duration={timeDetail}
          showTimeIcon={false}
          isShowLoading={isShowLoading}
          isShelves3={true}
        />

        {/** 取还车方式 */}
        <RentalLocationHead
          storeGuidInfos={storeGuidInfos}
          onPress={handleLocationPress}
          isShowLoading={isShowLoading}
        />

        {/** 取消政策 */}
        <CancelInfo
          cancelEncourage={cancelEncourage}
          onPressCancelRule={onPressCancelRule}
          isShowLoading={isShowLoading}
        />

        {/** 供应商名称 */}
        <VendorName
          isOptimize={isOptimize}
          nationalChainTagTitle={nationalChainTagTitle}
          vendorName={vendorName}
          isShowLoading={isShowLoading}
        />
      </Touchable>
    </>
  );
});

export default VehicleAndVendorInfo;
