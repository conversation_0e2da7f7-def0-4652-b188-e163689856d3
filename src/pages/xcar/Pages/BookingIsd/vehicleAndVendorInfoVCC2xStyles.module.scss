@import '../../Common/src/Tokens/tokens/color.scss';

.linear {
  height: 200px;
  width: 100%;
  position: absolute;
  top: 0;
}

.wrapper {
  background-color: $white;
  margin-left: 24px;
  margin-right: 24px;
  border-radius: 16px;
  padding-left: 32px;
  padding-right: 32px;
}
.shelves2Wrap {
  background-color: $white;
  margin-left: 24px;
  margin-right: 24px;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  padding-left: 32px;
  padding-right: 32px;
  margin-top: 10px;
}
.vendorNameWrap {
  flex-direction: row;
  align-items: center;
  margin-bottom: 24px;
}

.youxuanBg {
  height: 30px;
  margin-right: 10px;
}
.vendorName {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}

.cancelInfoWrap {
  margin-bottom: 16px;
}

.cancelInfoText {
  font-size: 26px;
  line-height: 36px;
  color:$C_111111;
}
