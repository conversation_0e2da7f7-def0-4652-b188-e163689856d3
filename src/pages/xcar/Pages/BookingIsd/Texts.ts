import { Utils } from '../../Util/Index';
import { InsMsg } from '../../Constants/PromptMessage';

// todo-dyy: 迁移服务端，控制包大小
export const ctripCreditFModalData = {
  get title() {
    return '为什么我没通过？';
  },
  content: [
    '评估您能否通过是基于对您的程信分分数、个人信用额度、历史履约情况、个人实名信息等多维度的综合评估。',
    '信用租属于信用类产品，预订时会实时动态评估。请保持以下良好信用行为，有助于评估的通过。',
    '1、  建立并维护您在携程平台的个人信用履约记录。如：携程金融借贷产品的按期还款、酒店闪住、打车先行后付，WIFI免押等信用生活的按时履约，将对您的信用评估有很大的积极帮助哦！',
    '2、  保持个人信息的完整和稳定性。如：注意实名认证、手机号码、常旅信息等信息的完善并保持使用账户的良好状态。',
    '3、  按需使用金融信贷产品并保持良好信用情况。良好的资信状况及信用履约记录可以丰富您的信用历史，协助对您的综合评分作出更全面的评估！但请注意量入为出，适度消费哦！',
    '4、  感谢您的支持！如需帮助，欢迎致电程信分人工客服：点击拨打 95010,78',
  ],

  keywords: {
    get callPhone() {
      return '点击拨打 95010,78';
    },
  },
  phoneNum: '95010,78',
};

const createInsLoadingDataIsd = [
  '即将进入携程保险代理平台进行投保',
  '并授权驾驶员为被保险人',
];

const createInsLoadingDataOsd = ['即将进入携程保险代理平台进行投保'];

export const createInsLoadingData = {
  get title() {
    return '正在创建保险订单…';
  },
  get content() {
    return Utils.isCtripOsd()
      ? createInsLoadingDataOsd
      : createInsLoadingDataIsd;
  },
};

export const createInsFailPopData = {
  get title() {
    return '保险订单创建失败';
  },
  get content() {
    return Utils.isCtripOsd()
      ? InsMsg.insFailModalContent
      : '您可返回重试，或放弃保障并支付租车订单，支付后前往订单详情页购买保险';
  },
  get backBtnText() {
    return '返回重试';
  },
  get payBtnText() {
    return '放弃保障，去支付租车订单';
  },
};

export default {
  get ctripCar() {
    return '携程租车';
  },
  get orderView() {
    return '查看详情';
  },
  get depositPaymentTitle() {
    return '押金';
  },
  get paymentTitle() {
    return '支付方式';
  },
  get depositPaymentTip() {
    return '请选择押金支付方式';
  },
  get payFail() {
    return '支付失败，请重试';
  },
  get osdInsUnit() {
    return '/天';
  },
  get preferentialTitle() {
    return '优惠';
  },
  get pickup() {
    return '取';
  },
  get dropoff() {
    return '还';
  },
  get totalTitle() {
    return '共';
  },
  get mapDetails() {
    return '地图及指引';
  },
  get emptyDriver() {
    return '请选择驾驶员';
  },
  get drivers_addDriver() {
    return '请新增驾驶员';
  },
  get limitRules() {
    return '限制政策';
  },
  get goPay() {
    return '去支付';
  },
  get activityDetails() {
    return '活动说明';
  },
  get couponActivity() {
    return '活动';
  },
  get couponPromoCode() {
    return '优惠券';
  },
  get activityDisableMessage() {
    return '无可享活动';
  },
  get approveExplain() {
    return '个人身份信息授权声明';
  },
  get orderFailTip() {
    return '出错了，请稍后重试';
  },
  get giveupDeposit() {
    return '放弃免押';
  },
  get list_apply() {
    return '确定';
  },
  rmb: '¥',
  get auditTime() {
    return '24小时';
  },
  get lossyAmountValidateErrorRange() {
    return '抱歉，申请的退款金额不可大于订单违约金额';
  },
  get lossyAmountValidateError() {
    return '请填写和门店协商的退还金额';
  },
  get applyLossyCancelText() {
    return '申请退违约金';
  },
  get backReselectCar() {
    return '返回重新选车';
  },
  get pickUp() {
    return '取';
  },
  get dropOff() {
    return '还';
  },
  get pickUpAndDropOff() {
    return '取还';
  },
  get storePolicyTitle() {
    return '门店政策';
  },
  get disableCouponTip() {
    return '暂无可享优惠';
  },
  get couponAndDepositTitle() {
    return '本单可享';
  },
  get freeDepositTitle() {
    return '免押';
  },
  get showFree() {
    return '免收';
  },
  get giveUpFreeDeposit() {
    return '放弃';
  },
  get selectPassengerTitle() {
    return '请先选择驾驶员，查看是否免押';
  },
  get depositMethod() {
    return '押金方式';
  },
  get readAndAgree() {
    return '我已阅读并同意';
  },
  get agreeAndPay() {
    return '同意并支付';
  },
  get agreeAndBook() {
    return '同意并预订';
  },
  get returnTipText() {
    return '已提前还车';
  },
  get returnTipTextInUse() {
    return '已申请提前还车';
  },
  get cancelBook() {
    return '放弃预订';
  },
  get continueBook() {
    return '继续预订';
  },
  get rentTime() {
    return '租期';
  },
  get detailVendor() {
    return '查看详情';
  },
  get driverLicenseType() {
    return '驾照类型';
  },
  get locatedIn() {
    return '位于';
  },
  get freeDepositModalTitle() {
    return '减免规则';
  },
  get moreExTrasInfoTitle() {
    return '预约附加产品';
  },
};

export const applyLossyCancelSpacehold = value =>
  `请填写退还金额，最多${value}(必填)`;
export const lossyAmountInfoRebookFirst = value =>
  `新订单支付后，原订单直接退还${value}`;
export const lossyAmountInfoSecond = (value, range) =>
  `违约金${value}将于${range}内，门店同意您的申请后退回`;
