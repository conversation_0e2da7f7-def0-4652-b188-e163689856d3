import Image from '@c2x/components/Image';
import {
  xMergeStyles,
  xClassNames as classNames,
  XView as View,
  XViewExposure,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';
import {
  BbkUtils,
  BbkStyleUtil,
  DateFormatter,
} from '@ctrip/rn_com_car/dist/src/Utils';
import { color, layout, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { AllTagsType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailDtoType';
import { selector, vw } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import useLazyLoad from '@ctrip/rn_com_car/dist/src/Hooks';
import c2xStyles from './vehicleAndVendorInfoC2xStyles.module.scss';
import { VendorLabel } from '../../ComponentBusiness/VendorTag';
import CarRentalCenterDesc from '../../ComponentBusiness/CarRentalCenterDesc';
import OptimizationStrengthen from '../../ComponentBusiness/OptimizationStrengthen';
import texts from './Texts';
import { CommonEnums, ImageUrl, UITestID } from '../../Constants/Index';
import { ItemLabelCodeType } from '../../ComponentBusiness/PackageIncludes';
import { CarLog, GetAB, Utils } from '../../Util/Index';
import { OrderStatusCtrip } from '../../Constants/OrderDetail';
import { mergeEasyLifeLabel } from '../../State/List/VehicleListMappers';
import LicensePlate, {
  PlateBgSize,
} from '../../ComponentBusiness/CarVehicleName/src/LicensePlate';
import c2xCommonStyles from '../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, isIos } = BbkUtils;
const styles = StyleSheet.create({
  limitRulesWrapper: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: color.blueGrayBg,
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    marginTop: getPixel(12),
    marginBottom: -getPixel(24),
  },
  limitRulesTextTitle: { marginRight: getPixel(8) },
  vehicleLabelWrapper: {
    flexDirection: 'row',
    marginTop: getPixel(isIos ? 8 : 10),
    marginLeft: getPixel(8),
  },
  mr16: { marginRight: getPixel(16) },
  marginBottom16: { marginBottom: getPixel(16) },
  orangeBackground: { backgroundColor: color.orangeBase },
  mb8: { marginBottom: getPixel(8) },
  optimizationStrengthenTitleWrap: { marginBottom: getPixel(17) },
  optimizationStrengthenImgStyle: {
    width: getPixel(126),
    height: getPixel(32),
  },
  optimizationStrengthenVendorName: { ...font.caption1LightStyle },
  optimizationStrengthenArrowRightStyle: { fontSize: getPixel(20) },
  plateWrap: {
    top: isIos ? getPixel(-6) : getPixel(-7),
  },
  linearPlateWrap: {
    top: getPixel(-2),
  },
  plateTextStyle: {
    ...font.title1MediumStyle,
    color: color.fontPrimary,
  },
  bottomLinearStyle: {
    left: getPixel(2),
  },
});

interface IPropsType {
  rentalMustReadTitle?: {
    title?: string;
    desc?: string;
  };
  vehicleName?: string; // 车型名称
  licenseTag?: string; // 沪牌
  licenseStyle?: string; // 牌照样式
  isHotLabel?: boolean; // 是否是热销
  ptime?: string; // 取车时间，格式是****年**月**日**：**
  rtime?: string;
  // detailDiff: string; // 时间间隔，格式是*天*小时
  pickupWay?: string; // 取车方式
  dropOffWay?: string; // 还车方式
  pickupRentCenterName?: string; // 取车租车中心门店名称
  dropOffRentCenterName?: string; // 还车租车中心门店名称
  pickupAddress?: string; // 取车地点
  dropOffAddress?: string; // 还车地点
  vendorName?: string; // 供应商名称
  isOptimize?: boolean; // 是否是优选
  nationalChainTagTitle?: string; // 全国连锁名称
  isEasyLife?: boolean; // 是否无忧租
  allTagsInfo?: IAllTagsInfoType;
  onLocationPress?: (type: string) => void;
  onPressLimit?: () => void;
  onPressVendor?: () => void;
  wrapStyle?: CSSProperties;
  wayWrapper?: CSSProperties;
  isShowLocationBottomDashLine?: boolean;
  orderStatusCtrip?: OrderStatusCtrip | undefined;
  timeTextStyle?: CSSProperties;
  isNewEnergy?: boolean;
  isShowAdvanceTip?: boolean;
  isShowFulfillmentModifyRemind?: boolean;
  onPressReturnTip?: () => void;
  setOrderModalsVisible?: (data: {
    [key: string]: { [key: string]: boolean };
  }) => void;
  orderFulfillmentModifyInfoTip?: string;
}

export interface IAllTagsInfoType {
  vehicleTagList?: Array<AllTagsType>;
  policyTagList?: Array<AllTagsType>;
  restAssuredTag?: Array<AllTagsType>;
}

interface LocationInfoType {
  isPickup?: boolean;
  way: string;
  address: string;
  rentCenterName?: string;
  addressStyle?: CSSProperties;
  dotStyle?: CSSProperties;
  testID?: string;
  onLocationPress: (type: string) => void;
}

interface EasyLifeTagProps {
  imageStyle?: CSSProperties;
}

// 取还车方式
const LocationInfo = (props: LocationInfoType) => {
  const {
    isPickup,
    way,
    address,
    rentCenterName,
    dotStyle,
    addressStyle,
    testID,
    onLocationPress,
  } = props;
  const title = isPickup ? texts.pickup : texts.dropoff;
  const { GuideTabType } = CommonEnums;
  const guideType = isPickup ? GuideTabType.Pickup : GuideTabType.Dropoff;
  const wayLen = way?.length || 0;
  let showWay = way;
  if (!rentCenterName && wayLen > 15) {
    showWay = `${way.slice(0, 15)}...`;
  }
  return (
    <BbkTouchable testID={testID} onPress={() => onLocationPress(guideType)}>
      <View style={layout.flexRow}>
        <View className={c2xStyles.wayDot} style={dotStyle} />
        <BbkText className={classNames(c2xStyles.wayText, c2xStyles.wayTitle)}>
          {title}
        </BbkText>
        {!!showWay && (
          <BbkText className={classNames(c2xStyles.wayText, c2xStyles.wayDesc)}>
            {showWay}
          </BbkText>
        )}
        {!!rentCenterName && <CarRentalCenterDesc />}
      </View>
      {!!address && (
        <BbkText
          className={classNames(c2xStyles.wayText, c2xStyles.wayAddressDesc)}
          style={addressStyle}
        >
          {address}
        </BbkText>
      )}
    </BbkTouchable>
  );
};

LocationInfo.defaultProps = {
  isPickup: false,
  rentCenterName: '',
  addressStyle: {},
  dotStyle: {},
};

// 全国连锁标签
const NationlChainTag = ({ tagTitle }: { tagTitle: string }) => (
  <View className={c2xStyles.nationalChainWrap}>
    <BbkText className={c2xStyles.nationalChainText}>{tagTitle}</BbkText>
  </View>
);

// 无忧租标签
export const EasyLifeTag: React.FC<EasyLifeTagProps> = ({ imageStyle }) => (
  <Image
    src={
      GetAB.isISDInterestPoints()
        ? `${ImageUrl.DIMG04_PATH}1tg4212000cxv1gzu736C.png`
        : `${ImageUrl.CTRIP_EROS_URL}wuyouzu_tag.png`
    }
    style={xMergeStyles([BbkStyleUtil.getWHAndMR(85, 32, 8), imageStyle])}
  />
);

EasyLifeTag.defaultProps = { imageStyle: null }; // 分割线
const DashLine = () => (
  <Image
    src={`${ImageUrl.BBK_IMAGE_PATH}booking_dashline.png`}
    className={c2xStyles.dashLineImg}
  />
);

// 牌照标签各个字数匹配的宽度
const licenseTagWidth = { 2: 64, 3: 84 };
const getMaxWidth = (licenseTag, isHotLabel, isNewEnergy) => {
  const baseWidth = 32 * 2 + 16 * 2; // 外边距和内边距
  const licenseLen = licenseTag?.length || 0;
  const licenseWidth =
    licenseTag && licenseTagWidth[licenseLen] ? licenseTagWidth[licenseLen] : 0; // 沪牌标签所占最大宽度
  const hotWidth = isHotLabel ? 40 : 0; // 热销标签所占宽度
  const lessWidth = isNewEnergy ? 164 : 0; // 新能源车型logo所占宽度
  return vw(100) - getPixel(baseWidth + licenseWidth + hotWidth + lessWidth);
};
export const getTagListInfo = (allTags, isEasyLife) => {
  let vehicleTagList = [];
  let policyTagList = [];
  if (allTags) {
    const bookingTags = allTags.filter(item => item.tagGroups);
    vehicleTagList = bookingTags.filter(flex => flex.tagGroups === 1);
    policyTagList = bookingTags.filter(flex => flex.tagGroups === 2);
    vehicleTagList.sort((a, b) => a.tagSortNum - b.tagSortNum);
    policyTagList.sort((a, b) => a.tagSortNum - b.tagSortNum);
  } // 安心行标签单独展示在第一个
  const restAssuredTagIndex = policyTagList.findIndex(
    f => f.labelCode === ItemLabelCodeType.RESTASSURED,
  );
  let restAssuredTag = null;
  if (restAssuredTagIndex > -1) {
    restAssuredTag = policyTagList[restAssuredTagIndex];
    policyTagList.splice(restAssuredTagIndex, 1);
  } // 2022-11-2 无忧租标签合并
  vehicleTagList = mergeEasyLifeLabel(vehicleTagList, isEasyLife);
  return { vehicleTagList, policyTagList, restAssuredTag };
};
interface LimitRulesProps {
  title: string;
  text: string;
  onPress: () => void;
}
const LimitRules: React.FC<LimitRulesProps> = ({ title, text, onPress }) => {
  const isLazyLoad = useLazyLoad();
  if (!isLazyLoad) return null;
  return (
    <BbkTouchable
      debounce={true}
      testID={UITestID.car_testid_vehicleandvendorinfo_limit}
      onPress={onPress}
      style={xMergeStyles([layout.flexRow, styles.limitRulesWrapper])}
    >
      <XViewExposure
        testID={CarLog.LogExposure({ name: '曝光_限制政策' })}
        style={xMergeStyles([layout.flex1, layout.flexRow])}
      >
        <View
          style={xMergeStyles([layout.flexRow, styles.limitRulesTextTitle])}
        >
          <BbkText type="icon" className={c2xStyles.limitRulesIcon}>
            {icon.circleWithSigh}
          </BbkText>
          <BbkText style={font.caption1BoldStyle} fontWeight="medium">
            {title}
          </BbkText>
        </View>
        <BbkText
          numberOfLines={1}
          style={xMergeStyles([layout.flex1, font.caption1LightStyle])}
        >
          {text}
        </BbkText>
      </XViewExposure>
      <BbkText type="icon" className={c2xStyles.limitRulesIconRight}>
        {icon.arrowRight}
      </BbkText>
    </BbkTouchable>
  );
};
const PolicyTags: React.FC<{ policyTagList: any[]; style: CSSProperties }> = ({
  policyTagList,
  style,
}) => {
  const isLazyLoad = useLazyLoad();
  if (!policyTagList?.length || !isLazyLoad) return null;
  return (
    <>
      {policyTagList.map(tagItem => (
        <VendorLabel key={tagItem?.title} tag={tagItem} labelStyle={style} />
      ))}
    </>
  );
};
const VehicleAndVendorInfo = (props: IPropsType) => {
  const {
    vehicleName,
    licenseTag,
    isHotLabel,
    rentalMustReadTitle,
    ptime,
    rtime,
    pickupWay,
    pickupAddress,
    pickupRentCenterName,
    dropOffWay,
    dropOffAddress,
    dropOffRentCenterName,
    vendorName,
    isOptimize,
    isEasyLife,
    nationalChainTagTitle,
    allTagsInfo,
    onLocationPress,
    onPressLimit,
    onPressVendor,
    licenseStyle,
    wrapStyle,
    wayWrapper,
    isShowLocationBottomDashLine = true,
    orderStatusCtrip,
    timeTextStyle,
    isNewEnergy = false,
    isShowAdvanceTip,
    isShowFulfillmentModifyRemind,
    onPressReturnTip,
    setOrderModalsVisible,
    orderFulfillmentModifyInfoTip,
  } = props;
  const { GuideTabType } = CommonEnums;
  const ctripDate = DateFormatter.ctripDateFormat({
    ptime,
    rtime,
    mode: 'bookingHeader',
  });
  const ptimeStr = ctripDate?.pickUpDateStr;
  const rtimeStr = ctripDate?.dropOffDateStr;
  const timeDetail = BbkUtils.isd_dhm(ptime, rtime);
  const vehicleNameMaxWidth = getMaxWidth(licenseTag, isHotLabel, isNewEnergy);
  const { vehicleTagList, policyTagList, restAssuredTag } = allTagsInfo || {};
  const Wrapper = Utils.isCtripIsd() ? BbkTouchable : View;
  let mapIconToGuideTabType = GuideTabType.Pickup;
  if (
    Utils.isCtripIsd() &&
    [OrderStatusCtrip.COMPLETED, OrderStatusCtrip.IN_SERVICE].includes(
      orderStatusCtrip,
    )
  ) {
    mapIconToGuideTabType = GuideTabType.Dropoff;
  }
  const labelStyle = styles.mb8;
  const getVendorInfo = () => {
    if (isOptimize) {
      return (
        <OptimizationStrengthen
          vendorName={vendorName}
          titleWrapStyle={styles.optimizationStrengthenTitleWrap}
          imgStyle={styles.optimizationStrengthenImgStyle}
          vendorNameStyle={styles.optimizationStrengthenVendorName}
          arrowRightStyle={styles.optimizationStrengthenArrowRightStyle}
        />
      );
    }
    if (vendorName) {
      return (
        <View className={c2xStyles.vendorWrapper}>
          <BbkText>{vendorName}</BbkText>
          {!!nationalChainTagTitle && (
            <NationlChainTag tagTitle={nationalChainTagTitle} />
          )}
          {Utils.isCtripIsd() && (
            <BbkText type="icon" className={c2xStyles.vendorIconRight}>
              {icon.arrowRightStrong}
            </BbkText>
          )}
        </View>
      );
    }
    return null;
  };
  return (
    <View className={c2xStyles.wrapper} style={wrapStyle}>
      {/** 车型信息 */}
      {!!vehicleName && (
        <View className={c2xStyles.vehicleWrap}>
          <View style={{ maxWidth: vehicleNameMaxWidth }}>
            <BbkText className={c2xStyles.vehicleNameText}>
              {vehicleName}
            </BbkText>
          </View>
          <View style={styles.vehicleLabelWrapper}>
            {!!licenseTag && (
              <LicensePlate
                title={licenseTag}
                size={PlateBgSize.large}
                licenseType={licenseStyle}
                style={styles.plateWrap}
                linearPlateWrap={styles.linearPlateWrap}
                plateTextStyle={styles.plateTextStyle}
                bottomLinearStyle={styles.bottomLinearStyle}
              />
            )}
            {isHotLabel && (
              <Image
                src={`${ImageUrl.BBK_IMAGE_PATH}hot.png`}
                mode="aspectFill"
                className={c2xStyles.hotImage}
              />
            )}
            {isNewEnergy && (
              <Image
                src={`${ImageUrl.componentImagePath}Less/less_product.png`}
                className={c2xStyles.lessImg}
              />
            )}
          </View>
        </View>
      )}
      <View style={layout.flexRowWrap}>
        {/** 取还车时间回显 */}
        {!!ptimeStr && !!rtimeStr && (
          <View style={layout.flexRowWrap}>
            <View style={xMergeStyles([layout.flexRow, styles.mr16])}>
              <BbkText className={c2xStyles.timeText} style={timeTextStyle}>
                {ptimeStr}
              </BbkText>
              <BbkText className={c2xStyles.timeText} style={timeTextStyle}>
                {' '}
                -{' '}
              </BbkText>
              <BbkText className={c2xStyles.timeText} style={timeTextStyle}>
                {rtimeStr}
              </BbkText>
            </View>
            {!!timeDetail && (
              <BbkText className={c2xStyles.timeText} style={timeTextStyle}>
                {`${texts.totalTitle}${timeDetail}`}
              </BbkText>
            )}
          </View>
        )}

        {isShowFulfillmentModifyRemind && !!orderFulfillmentModifyInfoTip && (
          <BbkTouchable
            onPress={() =>
              setOrderModalsVisible({
                fulfillmentModifyModal: {
                  visible: true,
                },
              })
            }
            className={c2xStyles.fulfillmentModifyRemind}
          >
            <BbkText
              type="icon"
              className={classNames(
                c2xStyles.fulfillmentModifyIcon,
                c2xStyles.fulfillmentModifyInfoIcon,
              )}
            >
              {icon.remind}
            </BbkText>
            <BbkText className={c2xStyles.fulfillmentModifyText}>
              {orderFulfillmentModifyInfoTip}
            </BbkText>
            <BbkText
              type="icon"
              className={classNames(
                c2xStyles.fulfillmentModifyIcon,
                c2xStyles.fulfillmentModifyRightIcon,
              )}
            >
              {icon.arrowRight}
            </BbkText>
          </BbkTouchable>
        )}

        {isShowAdvanceTip && (
          <BbkTouchable
            testID={UITestID.car_testid_vehicleandvendorinfo_returntip}
            onPress={onPressReturnTip}
            className={c2xStyles.advanceReturnTip}
          >
            <View style={layout.flexRow}>
              <Image
                src={`${ImageUrl.componentImagePath}AdvanceReturn/tip.png`}
                className={c2xStyles.returnTipIcon}
              />

              <BbkText className={c2xStyles.returnTipText}>
                {orderStatusCtrip === OrderStatusCtrip.COMPLETED
                  ? texts.returnTipText
                  : texts.returnTipTextInUse}
              </BbkText>
            </View>
            <BbkText type="icon" className={c2xStyles.returnTipIconRight}>
              {icon.arrowRight}
            </BbkText>
          </BbkTouchable>
        )}
      </View>
      {/** 取还车方式 */}
      <View className={c2xStyles.wayWrapper} style={wayWrapper}>
        <Image
          src={`${ImageUrl.BBK_IMAGE_PATH}mapBackground.png`}
          className={c2xStyles.mapBackgroundImg}
        />

        <View style={layout.flexRow}>
          <View style={layout.flex1}>
            <LocationInfo
              isPickup={true}
              way={pickupWay}
              address={pickupAddress}
              rentCenterName={pickupRentCenterName}
              addressStyle={styles.marginBottom16}
              onLocationPress={onLocationPress}
              testID={UITestID.car_testid_vehicleandvendorinfo_location_pickup}
            />

            <LocationInfo
              way={dropOffWay}
              address={dropOffAddress}
              rentCenterName={dropOffRentCenterName}
              dotStyle={styles.orangeBackground}
              onLocationPress={onLocationPress}
              testID={UITestID.car_testid_vehicleandvendorinfo_location_dropoff}
            />
          </View>
          <BbkTouchable
            onPress={() => onLocationPress(mapIconToGuideTabType)}
            testID={UITestID.car_testid_page_order_detail_pick_return_map}
            className={c2xStyles.mapIconWrap}
          >
            <Image
              testID={CarLog.LogExposure({
                name: '曝光_订详详情页_行中汇合点',
              })}
              src={`${ImageUrl.BBK_IMAGE_PATH}mapBackgroundIcon.png`}
              style={BbkStyleUtil.getWH(54, 54)}
            />

            <BbkText
              className={classNames(
                c2xCommonStyles.c2xTextDefaultCss,
                GetAB.isISDInterestPoints()
                  ? c2xStyles.mapIconTextNew
                  : c2xStyles.mapIconText,
              )}
            >
              {texts.mapDetails}
            </BbkText>
          </BbkTouchable>
        </View>
        {selector(isShowLocationBottomDashLine, <DashLine />)}
      </View>
      <Wrapper
        testID={UITestID.car_testid_vehicleandvendorinfo_vendor}
        onPress={onPressVendor}
      >
        {/** 供应商信息 */}
        {getVendorInfo()}

        {/** 车辆标签 */}
        {vehicleTagList?.length > 0 && (
          <View
            style={xMergeStyles([layout.flexRowWrap, layout.alignItemsStart])}
          >
            {vehicleTagList.map(tagItem => (
              <VendorLabel tag={tagItem} labelStyle={styles.mb8} />
            ))}
          </View>
        )}
        {/** 服务+政策标签 */}
        <View className={c2xStyles.tagsWrap}>
          {restAssuredTag && (
            <VendorLabel tag={restAssuredTag} labelStyle={labelStyle} />
          )}
          {isEasyLife && <EasyLifeTag />}
          {policyTagList?.length > 0 && (
            <PolicyTags policyTagList={policyTagList} style={labelStyle} />
          )}
        </View>
      </Wrapper>
      {
        // 限制出行政策
        !!rentalMustReadTitle?.desc && Utils.isCtripIsd() && (
          <LimitRules
            title={rentalMustReadTitle?.title}
            text={rentalMustReadTitle?.desc}
            onPress={onPressLimit}
          />
        )
      }
    </View>
  );
};
VehicleAndVendorInfo.defaultProps = {
  rentalMustReadTitle: { title: '', desc: '' },
  vehicleName: '',
  licenseTag: '',
  licenseStyle: '',
  isHotLabel: false,
  ptime: '',
  rtime: '',
  pickupWay: '',
  dropOffWay: '',
  pickupRentCenterName: '',
  dropOffRentCenterName: '',
  pickupAddress: '',
  dropOffAddress: '',
  vendorName: '',
  isOptimize: false,
  nationalChainTagTitle: '',
  isEasyLife: false,
  allTagsInfo: null,
  onLocationPress: () => {},
  onPressLimit: () => {},
  onPressVendor: () => {},
  wrapStyle: null,
  wayWrapper: {},
  isShowLocationBottomDashLine: true,
  orderStatusCtrip: undefined,
  timeTextStyle: {},
  isNewEnergy: false,
  isShowAdvanceTip: false,
  isShowFulfillmentModifyRemind: false,
  onPressReturnTip: () => {},
  setOrderModalsVisible: () => {},
  orderFulfillmentModifyInfoTip: '',
};

export default VehicleAndVendorInfo;
