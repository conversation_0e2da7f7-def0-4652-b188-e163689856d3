@import '../../../Common/src/Tokens/tokens/color.scss';

.container {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
  background-color: $white;
  border-radius: 16px;
  margin-left: 24px;
  margin-right: 24px;
  margin-top: 16px;
}
.wrap {
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.title {
  font-size: 34px;
  line-height: 44px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.desc {
  font-size: 26px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
  margin-left: 24px;
  flex: 1;
}
.icon {
  font-size: 26px;
  color: $linearGradientBlackDiamondStart;
}
