@import '../../../Common/src/Tokens/tokens/color.scss';

.wrapNew {
  flex-direction: row;
  align-items: flex-start;
  z-index: 10;
}
.wrap {
  flex-direction: row;
  align-items: flex-start;
}
.labelWrap {
  padding-top: 2px;
  padding-bottom: 2px;
  padding-left: 8px;
  padding-right: 8px;
  background-color: $rentalDateDurationBg;
  margin-top: 2px;
  border-radius: 4px;
}
.label {
  font-size: 20px;
  line-height: 28px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.leftLine {
  position: absolute;
  width: 1px;
  height: 57px;
  background-color: $rentalDateDurationBg;
  top: 32px;
  left: 18px;
}
.row {
  flex-direction: row;
}
.rowNew {
  flex-direction: row;
  flex-wrap: 'wrap';
}
.storeGuid {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.locatedInStyle {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_111111;
  margin-left: 10px;
  margin-right: 4px;
}
.address {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
  margin-top: 8px;
}

.centerIcon {
  width: 32px;
  height: 32px;
  margin-right: 4px;
  margin-top: 2px;
}
