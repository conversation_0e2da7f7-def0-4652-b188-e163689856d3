@import '../../../Common/src/Tokens/tokens/color.scss';

.wrap {
  flex-direction: row;
  align-items: center;
  padding-top: 62px;
  padding-left: 32px;
  padding-right: 32px;
  padding-bottom: 30px;
  background-color: rgba($insuranceTipBg, 0.8);
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  margin-top: -32px;
  margin-left: 24px;
  margin-right: 24px;
  z-index: -1;
}
.title {
  font-size: 26px;
  line-height: 36px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.desc {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $linearGradientBlackDiamondStart;
  margin-left: 16px;
}
