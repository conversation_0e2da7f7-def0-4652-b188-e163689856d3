import { isNil as lodashIsNil } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import {
  CRNMapViewV3,
  CRNMapViewV3Biztype,
} from '@c2x/components/CRNMapViewV3';
// http://conf.ctripcorp.com/pages/viewpage.action?pageId=142731210
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { styleSheet } from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  calculateRouteETAForProxyViewPromise,
  converToDrawLatLng,
} from '../../../ComponentBusiness/PickdropMap/index';
import { TransportType } from '../../../ComponentBusiness/PickdropMap/src/Constants';
import {
  getTransportTypeByCalculateWalkDistance,
  calculateDistanceTime,
} from '../../../ComponentBusiness/PickdropMap/src/Utils';
import { PickupStoreInfoType } from '../../../ComponentBusiness/Common/src/ServiceType/src/queryProductInfo';
import { Utils, AppContext } from '../../../Util/Index';
import {
  StoreType,
  StoreWayInfoType,
} from '../../../ComponentBusiness/Common/src/Enums';

const { mapBiztype } = styleSheet;

const styles = StyleSheet.create({
  map: {
    position: 'absolute',
    opacity: 0,
    top: 0,
    width: 0,
    height: 0,
    zIndex: -3,
  },
});

interface PoiInfoType {
  locationName: string;
  longitude: number;
  latitude: number;
}
interface DistanceProps {
  pickupStoreInfo: PickupStoreInfoType;
  returnStoreInfo: PickupStoreInfoType;
  pickupPoiInfo: PoiInfoType;
  returnPoiInfo: PoiInfoType;
  onPickupDistanceChange?: (desc: string) => void;
  onReturnDistanceChange?: (desc: string) => void;
  onPickupDriveTimeCalculated?: (time: number) => void;
}

const oneKm = 1000;
const oneMinute = 60;

const getWalkStr = (poiName: string, distance: number, time: number) => {
  const curDistance = `${
    distance > oneKm ? `${Math.ceil(distance / oneKm)}km` : `${distance}米`
  }`;

  const curTime = Math.ceil(time / oneMinute);
  return `距${poiName}步行距离${curDistance}，约${Math.ceil(curTime)}分钟到达`;
};

const getDriveStr = (poiName: string, distance: number, time: number) => {
  const curDistance = `${
    distance > oneKm ? `${Math.ceil(distance / oneKm)}km` : `${distance}米`
  }`;

  const curTime = Math.ceil(time / oneMinute);
  return `距${poiName}驾车距离${curDistance}，约${Math.ceil(curTime)}分钟到达`;
};

const getCalculateStartDestPoint = (
  storeInfo: PickupStoreInfoType,
  poiInfo: PoiInfoType,
) => {
  const {
    latitude,
    longitude,
    shuttlePointLongitude,
    shuttlePointLatitude,
    storeType,
  } = storeInfo;
  const isPickupInStation =
    storeInfo.wayInfo === StoreWayInfoType.PickupInStation;
  if (isPickupInStation) return null;

  const isPickPointCurrent = storeType === StoreType.PickPoint;

  const startLatLng = converToDrawLatLng({
    latitude: parseFloat(`${poiInfo.latitude}`),
    longitude: parseFloat(`${poiInfo.longitude}`),
    type: Utils.getCoordinateType(),
  });

  const destLatLng = converToDrawLatLng({
    latitude: parseFloat(
      `${isPickPointCurrent ? shuttlePointLatitude : latitude}`,
    ),
    longitude: parseFloat(
      `${isPickPointCurrent ? shuttlePointLongitude : longitude}`,
    ),
    type: Utils.getCoordinateType(),
  });

  if (
    Number.isNaN(startLatLng?.lon) ||
    Number.isNaN(startLatLng?.lat) ||
    Number.isNaN(destLatLng?.lon) ||
    Number.isNaN(destLatLng?.lat)
  ) {
    return null;
  }
  return {
    startLatLng,
    destLatLng,
  };
};

export const calculateDistanceTimeCallBack = async (
  map,
  storeInfo: PickupStoreInfoType,
  poiInfo: PoiInfoType,
  callback,
) => {
  const calPoint = getCalculateStartDestPoint(storeInfo, poiInfo);
  if (!calPoint) {
    return;
  }
  // 计算步行还是驾车
  const nextTransportType = await getTransportTypeByCalculateWalkDistance(
    map,
    calPoint.startLatLng,
    calPoint.destLatLng,
    Utils.noop,
  );
  const { distanceRes, etaRes } = await calculateDistanceTime(
    map,
    calPoint.startLatLng,
    calPoint.destLatLng,
    nextTransportType,
  );
  if (distanceRes?.distance > 0 && etaRes?.etaTime > 0) {
    const isDriver = nextTransportType === TransportType.Driver;
    const distanceStr = isDriver
      ? getDriveStr(
          poiInfo.locationName,
          distanceRes?.distance,
          etaRes?.etaTime,
        )
      : getWalkStr(
          poiInfo.locationName,
          distanceRes?.distance,
          etaRes?.etaTime,
        );
    callback(distanceStr);
  }
};

const Distance: React.FC<DistanceProps> = ({
  onPickupDistanceChange,
  onReturnDistanceChange,
  onPickupDriveTimeCalculated,
  pickupStoreInfo,
  returnStoreInfo,
  pickupPoiInfo,
  returnPoiInfo,
}) => {
  const [pDistanceStr, setPDistanceStr] = useState<string>('');
  const [rDistanceStr, setRDistanceStr] = useState<string>('');
  const map = useRef(null);

  useEffect(() => {
    AppContext.clearLocationDistance();
    AppContext.clearDriveTime();
    return () => {
      map.current = null;
    };
  }, []);

  useEffect(() => {
    if (typeof onPickupDistanceChange === 'function') {
      onPickupDistanceChange(pDistanceStr);
    }
  }, [pDistanceStr, onPickupDistanceChange]);

  useEffect(() => {
    if (typeof onReturnDistanceChange === 'function') {
      onReturnDistanceChange(rDistanceStr);
    }
  }, [rDistanceStr, onReturnDistanceChange]);

  const isPickPoint = useMemo(() => {
    return pickupStoreInfo?.storeType === StoreType.PickPoint;
  }, [pickupStoreInfo]);

  const isNeedCalculate = useMemo(() => {
    return (
      !lodashIsNil(pickupStoreInfo?.latitude) &&
      !lodashIsNil(returnStoreInfo?.latitude) &&
      (isPickPoint ||
        !pickupStoreInfo?.pickUpOnDoor ||
        !returnStoreInfo?.returnOnDoor)
    );
  }, [pickupStoreInfo, returnStoreInfo, isPickPoint]);

  // 计算距离和时间
  const calculateETADistance = useCallback(async () => {
    if (!isNeedCalculate || !map.current) return;

    // 取车门店计算步行Or驾车回调
    if (
      (!pickupStoreInfo.pickUpOnDoor || isPickPoint) &&
      onPickupDistanceChange
    ) {
      calculateDistanceTimeCallBack(
        map,
        pickupStoreInfo,
        pickupPoiInfo,
        setPDistanceStr,
      );
    }

    // 还车门店计算步行Or驾车回调
    if (
      !returnStoreInfo.pickUpOnDoor &&
      returnPoiInfo.locationName !== pickupPoiInfo.locationName &&
      onReturnDistanceChange
    ) {
      calculateDistanceTimeCallBack(
        map,
        returnStoreInfo,
        returnPoiInfo,
        setRDistanceStr,
      );
    }

    // 填写页驾车时间距离计算回调
    if (typeof onPickupDriveTimeCalculated === 'function') {
      const calPoint = getCalculateStartDestPoint(
        pickupStoreInfo,
        pickupPoiInfo,
      );
      if (!calPoint) {
        return;
      }
      const driveTimeRes = await calculateRouteETAForProxyViewPromise({
        getMap: () => map.current,
        startPoi: calPoint.startLatLng,
        destinationPoi: calPoint.destLatLng,
        transportType: TransportType.Driver,
      });
      if (driveTimeRes?.etaTime > 0) {
        onPickupDriveTimeCalculated(
          Math.ceil(driveTimeRes?.etaTime / oneMinute),
        );
      }
    }
  }, [
    pickupStoreInfo,
    returnStoreInfo,
    pickupPoiInfo,
    returnPoiInfo,
    isNeedCalculate,
    isPickPoint,
    onPickupDriveTimeCalculated,
  ]);

  if (!isNeedCalculate) return null;
  return (
    <>
      {CRNMapViewV3Biztype.registerBiztype({ biztype: mapBiztype })}
      <CRNMapViewV3
        ref={map}
        onMapReady={calculateETADistance}
        style={styles.map}
        showMapType={0}
      />
    </>
  );
};
export default Distance;
