import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, Ref } from 'react';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Footer from '../../../../Containers/BookingFooterContainer';
import PriceDetail from '../../../../Containers/BookingPriceDetailContainer';
import ProductConfirmNew from '../../../../Containers/ProductConfirmModalContainerNew';

const { vw } = BbkUtils;

const styles = StyleSheet.create({
  wrapper: {
    position: 'absolute',
    zIndex: -1,
    left: vw(110),
  },
});

interface SnapShotProps {
  productConfirmRef: Ref<any>;
  priceDetailsRef: Ref<any>;
}

const SnapShot: React.FC<SnapShotProps> = memo(
  ({ productConfirmRef, priceDetailsRef }) => {
    return (
      <>
        <ScrollView ref={productConfirmRef} style={styles.wrapper}>
          <ProductConfirmNew
            visible={true}
            isSnapShot={true}
            useModal={false}
            showFooter={false}
            isHeightFix={false}
          />
        </ScrollView>
        <ScrollView ref={priceDetailsRef} style={styles.wrapper}>
          <PriceDetail
            visible={true}
            useModal={false}
            footerChildren={<Footer />}
          />
        </ScrollView>
      </>
    );
  },
);

export default SnapShot;
