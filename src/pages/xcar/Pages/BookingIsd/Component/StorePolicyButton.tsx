import { xMergeStyles } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useCallback, CSSProperties } from 'react';

import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { Utils, CarLog } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import Texts from '../Texts';
import c2xStyles from './storePolicyButtonC2xStyles.module.scss';

const { getPixel, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  wrap: {
    width: getPixel(84),
    height: getPixel(72),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 5,
    position: 'absolute',
    top: getPixel(isAndroid ? 20 : 6),
    right: getPixel(24),
    bottom: 0,
  },
  bookingOptimizationIsBColor: {
    color: color.recommendProposeBg,
  },
});

interface IStorePolicyButton {
  wrapStyle?: CSSProperties;
  onPress?: () => void;
  logParams?: {
    vehicleCode?: string;
    pstoreCode?: string;
    rstoreCode?: string;
  };
}

const StorePolicyButton: React.FC<IStorePolicyButton> = memo(
  ({ wrapStyle, onPress = Utils.noop, logParams }) => {
    const fixOnPress = useCallback(() => {
      onPress();
      CarLog.LogCode({
        name: '点击_填写页_门店政策_顶部',

        info: logParams,
      });
    }, [onPress, logParams]);
    return (
      <BbkTouchable
        testID={UITestID.car_testid_page_booking_header_storepolicy}
        onPress={fixOnPress}
        style={xMergeStyles([styles.wrap, wrapStyle])}
      >
        <Text
          type="icon"
          className={c2xStyles.icon}
          style={Utils.isCtripIsd() && styles.bookingOptimizationIsBColor}
        >
          {icon.calendar}
        </Text>
        <Text
          className={c2xStyles.title}
          style={Utils.isCtripIsd() && styles.bookingOptimizationIsBColor}
        >
          {Texts.storePolicyTitle}
        </Text>
      </BbkTouchable>
    );
  },
);

export default StorePolicyButton;
