import React from 'react';
import { XView as View, xClassNames } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import InsuranceTipTypes from './InsuranceTipTypes';
import { UITestID } from '../../../Constants/Index';
import c2xStyle from './insuranceTipInLine.module.scss';

const { getPixel, isAndroid } = BbkUtils;

/* 车行险三者险理赔提示 */
const InsuranceTipInLine: React.FC<InsuranceTipTypes> = ({
  insuranceTips,
  onPressMore,
  noSpace,
}) => {
  if (!insuranceTips) return null;
  return (
    <Touchable
      className={xClassNames(
        c2xStyle.insuranceProductTip,
        noSpace && c2xStyle.noSpace,
      )}
      onPress={onPressMore}
      testID={UITestID.car_testid_page_order_detail_car_service_tips}
    >
      <Text className={c2xStyle.contentText}>
        <Text className={c2xStyle.moreText}>
          门店服务保障相关要求及须知
          <View>
            <View
              className={c2xStyle.moreIconWrap}
              style={{ top: isAndroid ? getPixel(6) : getPixel(2) }}
            >
              <Text className={c2xStyle.moreIcon} type="icon">
                {icon.arrowRight}
              </Text>
            </View>
          </View>
        </Text>
      </Text>
    </Touchable>
  );
};

export default InsuranceTipInLine;
