@import '../../../Common/src/Tokens/tokens/color.scss';

.topAngle {
  width: 22px;
  height: 14px;
  position: absolute;
  left: 26px;
  top: 11px;
  z-index: 100;
}
.wrap {
  flex-direction: row;
  align-items: center;
  border-width: 1px;
  border-color: rgba($depositRadioChecked, 0.4);
  padding: 24px;
  margin-right: 24px;
  margin-top: 24px;
  margin-left: -12px;
}
.leftWrap {
  flex: 1;
}
.content {
  font-size: 26px;
  line-height: 36px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.note {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
  margin-top: 1px;
}
.linearGradient {
  height: 59px;
  padding-left: 16px;
  padding-right: 16px;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-left: 24px;
}
.buttonText {
  font-size: 24px;
  line-height: 34px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $white;
}
