import React, { memo } from 'react';
import { getPriceAlertProps } from '../../../Global/Cache/ProductSelectors';
import CarDialog from '../../../ComponentBusiness/CarDialog';
import { PriceAlertType } from '../../../ComponentBusiness/Common/src/Enums';
import { UITestID } from '../../../Constants/Index';

interface IPriceAlertType {
  visible: boolean;
  priceAlertHandler: (type?: PriceAlertType) => void;
  popToVendorList: (type?: PriceAlertType) => void;
}

const PriceAlert = memo(
  ({ visible, priceAlertHandler, popToVendorList }: IPriceAlertType) => {
    const { imageUrl, message, desc, confirmBtnText, cancelBtnText, type } =
      getPriceAlertProps();
    const confirmTypes = [PriceAlertType.Error, PriceAlertType.PriceCacheError];

    return (
      <CarDialog
        imageUrl={imageUrl}
        message={message}
        desc={desc}
        confirmBtnText={confirmBtnText}
        cancelBtnText={cancelBtnText}
        visible={visible}
        onConfirm={() => priceAlertHandler(type)}
        onCancel={() => popToVendorList(type)}
        priceAlertType={type}
        confirmTestID={UITestID.car_testid_price_alert_confirm}
        cancelTestID={UITestID.car_testid_price_alert_cancel}
        type={confirmTypes.includes(type) ? 'confirm' : 'alert'}
      />
    );
  },
);

export default PriceAlert;
