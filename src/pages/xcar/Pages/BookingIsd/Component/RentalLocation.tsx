import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useCallback } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './rentalLocationC2xStyles.module.scss';
import RentalLocationItem from './RentalLocationItem';
import { IGuidInfoType, IRentalLocation } from '../Types';
import { CommonEnums, ImageUrl, UITestID } from '../../../Constants/Index';
import { Utils } from '../../../Util/Index';

const { getPixel, isHarmony } = BbkUtils;
const styles = StyleSheet.create({
  mergeWrap: {
    marginTop: getPixel(8),
    marginRight: getPixel(32),
  },
  modalMergeWrap: {
    marginTop: getPixel(8),
  },
  vendorListTopItemWrap: {
    paddingTop: getPixel(8),
    paddingBottom: getPixel(10),
    marginRight: getPixel(30),
  },
  topItemWrap: {
    marginTop: getPixel(8),
    paddingBottom: getPixel(10),
    marginRight: getPixel(32),
  },
  bottomItemWrap: {
    marginRight: getPixel(32),
  },
  mapImageWrap: {
    position: 'absolute',
    right: getPixel(-56),
    top: getPixel(-38),
    zIndex: isHarmony ? 1 : 0,
  },
  vendorListMapImageWrap: {
    alignItems: 'center',
    position: 'absolute',
    right: 0,
    paddingBottom: getPixel(20),
  },
  bookingOptimizationIsBMapImageWrap: {
    position: 'absolute',
    right: getPixel(-56),
    top: getPixel(-38),
    zIndex: isHarmony ? 1 : -1,
  },
  mapImage: {
    width: getPixel(153),
    height: getPixel(134),
  },
  vendorListMapImage: {
    width: getPixel(176),
    height: getPixel(134),
  },
  mergeVendorListMapImage: {
    position: 'absolute',
    right: 0,
    bottom: getPixel(-14),
  },
  fixMapImageWrap: {
    top: getPixel(24),
  },
});

const RentalLocation: React.FC<IRentalLocation> = memo(
  ({
    storeGuidInfos,
    onPress,
    isVendorList = false,
    isPickupCenter,
    isReturnCenter,
    onPressPickUpRentalCenter,
    onPressReturnRentalCenter,
    pickUpStoreSelfServiceInfo,
    returnStoreSelfServiceInfo,
    isShowPickUpDropOffLabel,
  }: IRentalLocation) => {
    const { GuideTabType } = CommonEnums;
    const handlePress = useCallback(() => {
      onPress(GuideTabType.Pickup);
    }, [onPress, GuideTabType.Pickup]);

    if (!storeGuidInfos?.length) return null;

    const mergeInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.Merge,
    );
    const merLocationBg = mergeInfo
      ? `${ImageUrl.DIMG04_PATH}1mp6412000c2cbdvv084C.png`
      : `${ImageUrl.DIMG04_PATH}1mp6d12000c2mi51fA7E3.png`;
    const mapIconUrl = isVendorList
      ? `${ImageUrl.CTRIP_EROS_URL}mapIcon.png`
      : merLocationBg;
    const mapImageStyle = isVendorList
      ? styles.vendorListMapImage
      : styles.mapImage;
    const mergeMapImageWrap = Utils.isCtripIsd()
      ? styles.bookingOptimizationIsBMapImageWrap
      : styles.mapImageWrap;
    if (mergeInfo) {
      return (
        <View className={c2xStyles.wrap}>
          <RentalLocationItem
            storeGuid={mergeInfo?.storeGuid}
            isCenter={isPickupCenter}
            isVendorList={isVendorList}
            onPressRentalCenter={onPressPickUpRentalCenter}
            address={mergeInfo?.address}
            type={mergeInfo?.type}
            isMerged={true}
            wrapStyle={isVendorList ? styles.modalMergeWrap : styles.mergeWrap}
            onPress={onPress}
            guideType={GuideTabType.Pickup}
            isSelfService={pickUpStoreSelfServiceInfo?.isSelfService}
            selfServiceText={pickUpStoreSelfServiceInfo?.text}
            isShowPickUpDropOffLabel={isShowPickUpDropOffLabel}
          />

          <BbkTouchable
            onPress={handlePress}
            testID={UITestID.car_testid_comp_vendor_modal_location_map}
            style={
              isVendorList ? styles.vendorListMapImageWrap : mergeMapImageWrap
            }
          >
            <Image src={mapIconUrl} mode="aspectFill" style={mapImageStyle} />
          </BbkTouchable>
        </View>
      );
    }
    const pickUpInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.PickUp,
    );
    const dropOffInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.DropOff,
    );
    if (pickUpInfo && dropOffInfo) {
      return (
        <View className={c2xStyles.wrap}>
          <View>
            <RentalLocationItem
              storeGuid={pickUpInfo?.storeGuid}
              isCenter={isPickupCenter}
              isVendorList={isVendorList}
              onPressRentalCenter={onPressPickUpRentalCenter}
              address={pickUpInfo?.address}
              type={pickUpInfo?.type}
              isShowLeftLine={true}
              wrapStyle={
                isVendorList ? styles.vendorListTopItemWrap : styles.topItemWrap
              }
              onPress={onPress}
              guideType={GuideTabType.Pickup}
              isSelfService={pickUpStoreSelfServiceInfo?.isSelfService}
              selfServiceText={pickUpStoreSelfServiceInfo?.text}
            />

            <RentalLocationItem
              storeGuid={dropOffInfo?.storeGuid}
              isCenter={isReturnCenter}
              isVendorList={isVendorList}
              onPressRentalCenter={onPressReturnRentalCenter}
              address={dropOffInfo?.address}
              type={dropOffInfo?.type}
              wrapStyle={!isVendorList && styles.bottomItemWrap}
              onPress={onPress}
              guideType={GuideTabType.Dropoff}
              isSelfService={returnStoreSelfServiceInfo?.isSelfService}
              selfServiceText={returnStoreSelfServiceInfo?.text}
            />
          </View>
          <BbkTouchable
            onPress={handlePress}
            testID={UITestID.car_testid_page_vendorlist_product_modal_gomap}
            style={
              isVendorList
                ? styles.vendorListMapImageWrap
                : xMergeStyles([styles.mapImageWrap, styles.fixMapImageWrap])
            }
          >
            <Image src={mapIconUrl} mode="aspectFill" style={mapImageStyle} />
          </BbkTouchable>
        </View>
      );
    }
    return null;
  },
);
export default RentalLocation;
