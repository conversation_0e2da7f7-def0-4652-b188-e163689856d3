import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { Utils } from '../../../Util/Index';

const { getPixel } = BbkUtils;

export const getNewBookingBlockStyle = () => {
  if (Utils.isCtripIsd()) {
    return {
      marginLeft: getPixel(24),
      marginRight: getPixel(24),
      borderRadius: getPixel(16),
    };
  }
  if (Utils.isCtripIsd()) {
    return {
      marginLeft: getPixel(16),
      marginRight: getPixel(16),
      borderRadius: getPixel(16),
    };
  }
  return {
    marginLeft: 0,
    marginRight: 0,
    borderRadius: 0,
  };
};

export default getNewBookingBlockStyle;
