@import '../../../Common/src/Tokens/tokens/color.scss';

.desc {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
  margin-top: 8px;
}
.descChecked {
  font-size: 26px;
  line-height: 36px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.backgroundImage {
  position: absolute;
  z-index: -1;
  width: 202px;
  height: 163px;
  right: 0px;
  top: 0px;
}
.iconWrap {
  margin-right: 18px;
}
.icon {
  font-size: 39px;
  color: $fontGrayBlue;
}
.iconChecked {
  color: $depositRadioChecked;
}
.contentWrap {
  flex-direction: 'row';
  align-items: 'center';
  justify-content: 'space-between';
  margin-top: 20px;
}
.titleWrap {
  flex-direction: row;
  align-items: center;
}
.titleChecked {
  font-size: 32px;
  line-height: 42px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.depositFreeRight {
  width: 272px;
  height: 32px;
  margin-left: 13px;
}
.freeDepositRuleEntryWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 42px;
  margin-top: 6px;
}
.ruleEntryTitle {
  color: $C_006ff6;
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-right: 11px;
}
.titleWrap {
  flex-direction: row;
  align-items: center;
}
.convenient {
  width: 28px;
  height: 28px;
  margin-right: 6px;
}
.contentWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.maxWidth75Percent {
  max-width: 76%;
}
.maxWidth90Percent {
  max-width: 90%;
}
.depositAmountTextWrap {
  flex-direction: row;
  align-items: center;
  margin-right: 8px;
  flex: 1;
  flex-wrap: wrap;
}
.creditCardImgWrap {
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 3px;
}
.creditCardImg {
  width: 86px;
  height: 52px;
  margin-right: 8px;
  margin-bottom: 8px;
}
.chooseImage {
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 40px;
  height: 40px;
}

.wrap {
    margin-bottom: 24px;
    border-radius: 8px;
    padding-left: 24px;
    padding-right: 24px;
    padding-top: 32px;
    padding-bottom: 26px;
    border-width: 2px;
    border-style: solid;
    border-color: $C_F0F0F0;
    background-color: $white;
}
.title {
    font-size: 32px;
    font-weight: bold;
    color: $C_111111;
    line-height: 42px;
}
.ruleWrap {
    border-width: 1px;
    border-color: $R_70_115_190_0_25;
    padding-left: 8px;
    padding-right: 8px;
    border-radius: 6px;
    margin-left: 16px;
    margin-right: 10px;
    flex-direction: row;
    align-items: center;
}
.rule {
    color: $C_4673B2;
    font-size: 22px;
}
.ruleEntryTitleIcon {
    color: $C_4673B2;
    font-size: 22px;
}
.positiveWrap {
    border-width: 1px;
    border-color: $C_FFC899;
    padding-left: 8px;
    padding-right: 8px;
    border-radius: 6px;
    padding-top: 1px;
    padding-bottom: 1px;
    flex-direction: row;
    align-items: center;
}
.positiveText {
    color: $C_FF5500;
    font-size: 22px;
}
.subContent {
    color: $C_888888;
    font-size: 26px;
    margin-top: 6px;
    margin-bottom: 6px;
}
.toAuth {
    width: 104px;
    height: 58px;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
}
.toAuthText {
    color: $white;
    font-size: 24px;
}