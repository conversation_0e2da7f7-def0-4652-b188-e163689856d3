import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';

import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './insuranceTipC2xStyles.module.scss';

interface IInsuranceTip {
  title: string;
  desc: string;
}

const InsuranceTip: React.FC<IInsuranceTip> = memo(({ title, desc }) => {
  if (!title || !desc) return null;
  return (
    <View className={c2xStyles.wrap}>
      <Text className={c2xStyles.title}>{title}</Text>
      <Text className={c2xStyles.desc}>{desc}</Text>
    </View>
  );
});

export default InsuranceTip;
