import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './depositDescriptionSectionC2xStyles.module.scss';
import DepositDescription from '../../Product/Components/DepositDescription';
import { IDepositDescriptionSection } from '../Types';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  wrap: {
    paddingTop: getPixel(28),
    paddingBottom: getPixel(24),
    borderTopWidth: getPixel(1),
    borderTopColor: color.blueGrayBg,
    backgroundColor: color.white,
  },
  title: {
    ...font.title3MediumStyle,
    color: color.fontPrimary,
  },
  tableWrap: {
    marginTop: getPixel(16),
  },
  noticeWrap: {
    marginTop: getPixel(24),
  },
  notice: {
    ...font.caption1LightStyle,
    color: color.fontSecondary,
  },
  wrapNoBorderBottom: {
    borderBottomWidth: 0,
  },
  wrapNoMarginTop: {
    marginTop: 0,
  },
});

const DepositDescriptionSection: React.FC<IDepositDescriptionSection> = memo(
  ({
    title,
    items = [],
    notices,
    onPressQuestion,
  }: IDepositDescriptionSection) => {
    return (
      <View className={c2xStyles.wrap}>
        {!!title && (
          <Text className={c2xStyles.title} fontWeight="medium">
            {title}
          </Text>
        )}
        {items?.length > 0 && (
          <View className={c2xStyles.tableWrap}>
            {items.map((item, index) => (
              <DepositDescription
                key={item.title}
                title={item.title}
                content={item.content}
                description={item.description}
                isShowFree={item.isShowFree}
                positiveDesc={item.positiveDesc}
                isShowQuestion={item.isShowQuestion}
                onPressQuestion={onPressQuestion}
                creditCardImgList={item.creditCardImgList}
                index={index}
                wrapStyle={xMergeStyles([
                  index > 0 && styles.wrapNoMarginTop,
                  index !== items.length - 1 && styles.wrapNoBorderBottom,
                ])}
              />
            ))}
          </View>
        )}
        {notices?.length > 0 && (
          <View className={c2xStyles.noticeWrap}>
            {notices.map(item => (
              <Text key={item} className={c2xStyles.notice}>
                {item}
              </Text>
            ))}
          </View>
        )}
      </View>
    );
  },
);

export default DepositDescriptionSection;
