import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { xMergeStyles, XView as View } from '@ctrip/xtaro';

import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { layout, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './tagsC2xStyles.module.scss';
import { Utils } from '../../../Util/Index';
import { VendorLabel } from '../../../ComponentBusiness/VendorTag';
import EasyLifeTag from './EasyLifeTag';
import { IAllTagsInfoType } from '../Types';
import SkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import SelfServiceLabel from '../../../ComponentBusiness/SelfServiceLabel';
import { UITestID } from '../../../Constants/Index';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  labelStyle: { marginBottom: getPixel(14), marginRight: getPixel(14) },
  labelStyle2: { marginBottom: getPixel(14), marginRight: getPixel(6) },
  loadingBg: {
    backgroundColor: color.white,
    marginTop: getPixel(16),
    marginBottom: getPixel(16),
  },
  selfServiceLabel: {
    marginRight: getPixel(8),
  },
});

interface ITags {
  allTagsInfo?: IAllTagsInfoType;
  isEasyLife?: boolean;
  onPress?: () => void;
  isShowLoading?: boolean;
  isSelfService?: boolean;
}

const Tags: React.FC<ITags> = memo(
  ({
    allTagsInfo,
    isEasyLife,
    onPress = Utils.noop,
    isShowLoading,
    isSelfService,
  }) => {
    const { vehicleTagList, policyTagList, restAssuredTag } = allTagsInfo || {};
    if (isShowLoading) {
      return (
        <SkeletonLoading
          visible={true}
          style={styles.loadingBg}
          pageName={PageType.BookingTags}
        />
      );
    }
    return (
      <BbkTouchable
        testID={UITestID.car_testid_page_booking_vehicle_tags}
        onPress={onPress}
        className={c2xStyles.wrap}
      >
        {/** 车辆标签 */}
        {vehicleTagList?.length > 0 && (
          <View
            style={xMergeStyles(layout.flexRowWrap, layout.alignItemsStart)}
          >
            {vehicleTagList.map(tagItem => (
              <VendorLabel tag={tagItem} labelStyle={styles.labelStyle} />
            ))}
          </View>
        )}
        {/** 服务+政策标签 */}
        <View className={c2xStyles.tagsWrap}>
          {restAssuredTag && (
            <VendorLabel tag={restAssuredTag} labelStyle={styles.labelStyle2} />
          )}
          {isSelfService && (
            <SelfServiceLabel style={styles.selfServiceLabel} />
          )}
          {isEasyLife && <EasyLifeTag />}
          {policyTagList?.length > 0 &&
            policyTagList.map(tagItem => (
              <VendorLabel tag={tagItem} labelStyle={styles.labelStyle2} />
            ))}
        </View>
      </BbkTouchable>
    );
  },
);

export default Tags;
