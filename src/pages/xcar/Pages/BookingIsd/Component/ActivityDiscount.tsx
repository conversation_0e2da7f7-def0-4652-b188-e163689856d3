import Image from '@c2x/components/Image';
import React, { useState, memo, useMemo } from 'react';
import StyleSheet from '@c2x/apis/StyleSheet';
import { XView as View, xClassNames as classNames } from '@ctrip/xtaro';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { color, layout, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import { ImageUrl } from '../../../Constants/Index';
import c2xStyles from './activityDiscountC2xStyles.module.scss';
import {
  ActivityDetailType,
  PromotionType,
} from '../../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';

const { useMemoizedFn, isAndroid, isIos } = BbkUtils;

const styles = StyleSheet.create({
  priceStyle: { ...font.body3LightStyle, color: color.fontPrimary },
});

interface PromotionItemProps {
  promotion?: PromotionType;
  currency?: string;
  onPress: (data?: string) => void;
}

interface ActivityHeaderProps {
  onHeaderPress: (data?: string) => void;
  expanded: boolean;
  hasMultiplePromotions: boolean;
  displayTitle: string | string[];
  totalPopAmountTile?: string;
}

interface ActivityDiscountProps {
  activityDetail?: ActivityDetailType;
  currency?: string;
  onPress: (data?: string) => void;
}

// 单个活动项组件
const PromotionItem = memo(
  ({ promotion, currency, onPress }: PromotionItemProps) => {
    const { title, deductionAmount, code } = promotion || {};
    return (
      <BbkTouchable
        onPress={() => onPress(code)}
        className={c2xStyles.promotionItem}
      >
        <BbkText className={c2xStyles.promotionName} numberOfLines={1}>
          {title}
        </BbkText>
        {!!deductionAmount && (
          <View style={layout.flexRow}>
            <BbkText className={c2xStyles.priceStyle}>-</BbkText>
            <BbkCurrencyFormatter
              currency={currency}
              price={deductionAmount}
              currencyStyle={styles.priceStyle}
              priceStyle={styles.priceStyle}
            />
            <BbkText type="icon" className={c2xStyles.circleQuestionIcon}>
              {icon.circleQuestion}
            </BbkText>
          </View>
        )}
      </BbkTouchable>
    );
  },
);

// 活动标题行组件
const ActivityHeader = memo(
  ({
    onHeaderPress,
    expanded,
    hasMultiplePromotions,
    displayTitle,
    totalPopAmountTile,
  }: ActivityHeaderProps) => {
    const activityImg = isAndroid
      ? `${ImageUrl.DIMG04_PATH}1tg5n12000lgbbhzr87BF.png`
      : `${ImageUrl.DIMG04_PATH}1tg6v12000lgb91tv3C18.png`;
    return (
      <BbkTouchable onPress={onHeaderPress} className={c2xStyles.itemWrap}>
        <View className={c2xStyles.leftItem}>
          <Image src={activityImg} className={c2xStyles.logoImg} />
          <BbkText fontWeight="bold" className={c2xStyles.itemTitle}>
            活动立减
          </BbkText>
        </View>
        <View className={c2xStyles.activityRow}>
          {!expanded && (
            <View className={c2xStyles.activityInfo}>
              {Array.isArray(displayTitle) ? (
                <BbkText className={c2xStyles.activityName} numberOfLines={1}>
                  {displayTitle.map((title, index) => (
                    <>
                      {index > 0 && (
                        <BbkText className={c2xStyles.separatorText}>
                          {' '}
                          |{' '}
                        </BbkText>
                      )}
                      <BbkText
                        className={c2xStyles.activityName}
                        numberOfLines={1}
                      >
                        {title}
                      </BbkText>
                    </>
                  ))}
                </BbkText>
              ) : (
                <BbkText className={c2xStyles.activityName} numberOfLines={1}>
                  {displayTitle}
                </BbkText>
              )}
            </View>
          )}

          <View className={c2xStyles.discountAmount}>
            <BbkText className={c2xStyles.priceStyle}>
              {totalPopAmountTile}
            </BbkText>
            {hasMultiplePromotions ? (
              <BbkText type="icon" className={c2xStyles.activityIcon}>
                {expanded ? icon.arrowUp : icon.arrowDown}
              </BbkText>
            ) : (
              <BbkText type="icon" className={c2xStyles.activityIcon}>
                {icon.circleQuestion}
              </BbkText>
            )}
          </View>
        </View>
      </BbkTouchable>
    );
  },
);

const ActivityDiscount = memo(
  ({ activityDetail, currency, onPress }: ActivityDiscountProps) => {
    const [expanded, setExpanded] = useState(false);

    const toggleExpand = useMemoizedFn(() => {
      setExpanded(prev => !prev);
    });

    const {
      promotions = [],
      status,
      totalPopAmountTile,
    } = activityDetail || {};

    const hasMultiplePromotions = promotions?.length > 1;

    const displayTitle = useMemo(() => {
      if (!hasMultiplePromotions) {
        return promotions[0]?.title || '';
      }
      return promotions?.map(p => p.title);
    }, [promotions, hasMultiplePromotions]);

    const handlePress = useMemoizedFn((data: string) => {
      const code =
        typeof data === 'string' && data ? data : promotions?.[0]?.code;
      onPress(code);
    });

    if (!promotions.length || status === 0) {
      return null;
    }

    return (
      <View>
        <ActivityHeader
          onHeaderPress={hasMultiplePromotions ? toggleExpand : handlePress}
          expanded={expanded}
          hasMultiplePromotions={hasMultiplePromotions}
          displayTitle={displayTitle}
          totalPopAmountTile={totalPopAmountTile}
        />
        {expanded && hasMultiplePromotions && (
          <View className={c2xStyles.promotionsList}>
            {promotions?.map((promotion, index) => (
              <PromotionItem
                key={promotion.code || index}
                promotion={promotion}
                currency={currency}
                onPress={handlePress}
              />
            ))}
          </View>
        )}
        <View
          className={classNames(
            !(expanded && hasMultiplePromotions) && c2xStyles.line,
            isIos && c2xStyles.trans,
          )}
        />
      </View>
    );
  },
);

export default ActivityDiscount;
