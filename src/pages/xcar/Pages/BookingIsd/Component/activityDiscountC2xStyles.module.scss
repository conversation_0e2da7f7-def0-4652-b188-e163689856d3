@import '../../../Common/src/Tokens/tokens/color.scss';

.itemWrap {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    padding: 26px 0;
}

.leftItem {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.logoImg {
    width: 36px;
    height: 36px;
    margin-right: 14px;
}

.itemTitle {
    font-size: 30px;
    line-height: 40px;
    color: $fontPrimary;
    font-family: PingFangSC-Regular;
}
.activityRow {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: flex-end;
}

.activityInfo {
    display: flex;
    flex-direction: row;
    align-items: center;
    max-width: 280px;
    margin-top: 1px;
}

.activityName {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 26px;
    color: $fontPrimary;
    font-family: PingFangSC-Regular;
}

.separatorText {
    margin: 0 8px;
    color: $C_D8D8D8;
    font-size: 22px;
}

.activityIcon {
    font-size: 26px;
    font-family: PingFangSC-Regular;
    color: $fontPrimary;
    margin-left: 13px;
}

.discountAmount {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 8px;
}

.promotionsList {
    background-color: $C_F7F8FA;
    padding: 8px 32px 32px;
    border-radius: 8px;
}

.promotionItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: 24px;
}

.promotionName {
    font-size: 26px;
    color: $fontPrimary;
    font-family: PingFangSC-Regular;
    max-width: 75%;
}

.promotionAmount {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.promotionPrice {
    font-size: 14px;
}

.circleQuestionIcon {
    font-size: 26px;
    margin-left: 10px;
}
.line {
  height: 1px;
  background: $couponSplit;
  width: 100%; 
}
.trans {
  transform: scaleY(0.5);
}