import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';

import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Block from '../../../ComponentBusiness/DetailsBlock';

const { getPixel } = BbkUtils;

const styles = StyleSheet.create({
  subTitle: {
    color: color.fontPrimary,
    ...font.areaLabel,
  },
  out: {
    backgroundColor: color.white,
    marginTop: getPixel(24),
    marginLeft: getPixel(16),
    marginRight: getPixel(16),
    borderRadius: getPixel(16),
  },
});

type IInvoiceInfo = {
  invoiceInfo: any;
  testID: any;
};

const InvoiceInfo: React.FC<IInvoiceInfo> = memo(({ invoiceInfo, testID }) => {
  if (!invoiceInfo || !Object.keys(invoiceInfo).length) return null;
  return (
    <Block
      title={invoiceInfo.title}
      style={styles.out}
      isTitleRight={false}
      subTitleStyle={styles.subTitle}
      subTitle={invoiceInfo.description}
      numberOfLines={10}
      testID={testID}
    />
  );
});

export default InvoiceInfo;
