import {
  xMergeStyles,
  XView as View,
  XViewExposure,
  XImage as Image,
  xClassNames,
} from '@ctrip/xtaro';
import React from 'react';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './limitRulesBC2xStyles.module.scss';
import { CarLog } from '../../../Util/Index';
import MarqueeHorizontal from './MarqueeHorizontal';
import { UITestID, ImageUrl } from '../../../Constants/Index';

interface ILimitRules {
  title: string;
  text: string;
  onPress: () => void;
  onReady: () => void;
  isShelves2?: boolean;
}
interface ILimitRulesState {
  width: number;
  height: number;
}

class LimitRules extends React.Component<ILimitRules, ILimitRulesState> {
  constructor(props: ILimitRules) {
    super(props);
    this.state = {
      width: 0,
      height: 0,
    };
  }

  handleViewLayout = event => {
    const { width } = event.nativeEvent.layout;
    this.setState({
      width,
    });
  };

  handleLayout = event => {
    const { height } = event.nativeEvent.layout;
    this.setState({
      height,
    });
  };

  render() {
    const { title, text, onPress, onReady, isShelves2 } = this.props;
    const { width, height } = this.state;
    if (!title || !text) return null;
    return (
      <BbkTouchable
        testID={UITestID.car_testid_page_booking_vehicle_limitrules}
        debounce={true}
        onPress={onPress}
        className={xClassNames(c2xStyles.wrap, isShelves2 && c2xStyles.mt2)}
      >
        <XViewExposure
          testID={CarLog.LogExposure({ name: '曝光_限制政策' })}
          onLayout={this.handleLayout}
          style={xMergeStyles([layout.flex1, layout.flexRow])}
        >
          {isShelves2 ? (
            <Image
              src={`${ImageUrl.DIMG04_PATH}1tg2b12000jpows9zED5D.png`}
              className={c2xStyles.iconPng}
            />
          ) : (
            <View className={c2xStyles.titleWrap}>
              <BbkText type="icon" className={c2xStyles.icon}>
                {icon.v2limit}
              </BbkText>
            </View>
          )}
          <View
            onLayout={this.handleViewLayout}
            className={xClassNames(
              c2xStyles.limitView,
              isShelves2 && c2xStyles.mbf2,
            )}
          >
            <MarqueeHorizontal
              textList={text}
              width={width}
              height={height}
              onMarqueeChange={onReady}
              fonSize26={isShelves2}
            />
          </View>
          <View className={c2xStyles.detailWrap}>
            <BbkText
              className={xClassNames(
                c2xStyles.title,
                isShelves2 && c2xStyles.titleBlack,
              )}
            >
              {title}
            </BbkText>
            {!!isShelves2 && (
              <BbkText className={c2xStyles.titleBlack} type="icon">
                {icon.arrowRight}
              </BbkText>
            )}
          </View>
        </XViewExposure>
      </BbkTouchable>
    );
  }
}
export default LimitRules;
