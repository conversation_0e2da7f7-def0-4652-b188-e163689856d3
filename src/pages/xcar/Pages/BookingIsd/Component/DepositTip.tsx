import Image from '@c2x/components/Image';
import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import { connect } from 'react-redux';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import * as ImageUrl from '../../../Constants/ImageUrl';
import c2xStyles from './depositTipC2xStyles.module.scss';
import { getDepositTip } from '../../../State/Booking/Selectors';

interface IDepositTip {
  title: string;
}
const DepositTip: React.FC<IDepositTip> = memo(({ title }) => {
  if (!title) return null;
  return (
    <View className={c2xStyles.wrapper}>
      <Image
        className={c2xStyles.img}
        src={`${ImageUrl.DIMG04_PATH}1tg3l12000lbil58o91BD.png`}
      />

      <View className={c2xStyles.flex1}>
        <Text className={c2xStyles.title}>{title}</Text>
      </View>
    </View>
  );
});
const mapStateToProps = state => ({
  title: getDepositTip(state),
});
export default connect(mapStateToProps)(DepositTip);
