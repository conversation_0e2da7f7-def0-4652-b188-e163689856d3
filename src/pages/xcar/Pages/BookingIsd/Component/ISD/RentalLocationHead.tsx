import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { XView as View, xClassNames } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { getPixel, isIos } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './rentalLocationHead.module.scss';
import { IGuidInfoType } from '../../Types';
import SkeletonLoading, {
  PageType,
} from '../../../../ComponentBusiness/SkeletonLoading';

interface IRentalLocationHead {
  storeGuidInfos: Array<any>;
  onPress?: (guideType?: string) => void;
  isShowLoading?: boolean;
}

const styles = StyleSheet.create({
  loadingBg: {
    backgroundColor: color.white,
    marginBottom: getPixel(20),
  },
});
const RentalLocationHead: React.FC<IRentalLocationHead> = memo(
  ({ storeGuidInfos, onPress, isShowLoading }: IRentalLocationHead) => {
    if (isShowLoading) {
      return (
        <SkeletonLoading
          visible={true}
          style={styles.loadingBg}
          pageName={PageType.BookTimeISD}
        />
      );
    }
    if (!storeGuidInfos?.length) return null;
    const mergeInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.Merge,
    );
    if (mergeInfo) {
      const { storeGuid } = mergeInfo;
      return (
        <BbkTouchable onPress={onPress} className={c2xStyles.wrapper}>
          <Text
            className={c2xStyles.textDesc}
            fontWeight="regular"
            numberOfLines={1}
          >
            {storeGuid}
          </Text>
        </BbkTouchable>
      );
    }
    const pickUpInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.PickUp,
    );
    const dropOffInfo = storeGuidInfos.find(
      item => item.type === IGuidInfoType.DropOff,
    );
    if (pickUpInfo && dropOffInfo) {
      return (
        <BbkTouchable onPress={onPress} className={c2xStyles.wrapper}>
          <View
            className={xClassNames(c2xStyles.line, isIos && c2xStyles.left7)}
          />
          <View
            className={xClassNames(c2xStyles.pickUp, isIos && c2xStyles.ml3)}
          >
            <View className={c2xStyles.filledDot} />
            <Text className={c2xStyles.textDesc} fontWeight="regular">
              {pickUpInfo.storeGuid}
            </Text>
          </View>
          <View
            className={xClassNames(c2xStyles.dropOff, isIos && c2xStyles.ml3)}
          >
            <View className={c2xStyles.unfilledDot} />
            <Text className={c2xStyles.textDesc} fontWeight="regular">
              {dropOffInfo.storeGuid}
            </Text>
          </View>
        </BbkTouchable>
      );
    }
    return null;
  },
);
export default RentalLocationHead;
