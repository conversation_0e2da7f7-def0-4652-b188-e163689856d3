import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import { XView as View, XImage, XViewExposure } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { LabelsType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/PriceResponseDtoType';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './IncentiveMarqueeVertical.module.scss';
import VerticalMarquee from '../../../../ComponentBusiness/VerticalMarquee';
import { ImageUrl } from '../../../../Constants/Index';
import { CarLog } from '../../../../Util/Index';
import SkeletonLoading, {
  PageType,
} from '../../../../ComponentBusiness/SkeletonLoading';

const { getPixel, useMemoizedFn, isAndroid } = BbkUtils;
interface IPropsType {
  data: Array<LabelsType>;
  enableAnimate: boolean;
  isShowLoading?: boolean;
}
const positivePoliciesEnum = {
  policy209: 209,
  policy303: 303,
  policy207: 207,
  policy201: 201,
  policy202: 202,
};

const styles = StyleSheet.create({
  wrap: {
    height: getPixel(80),
    overflow: 'hidden',
    backgroundColor: color.rentalDateDurationBg,
    marginTop: isAndroid ? -getPixel(15) : -getPixel(16),
  },
  imageTop: {
    paddingTop: isAndroid ? getPixel(8) : getPixel(7),
  },
  textTop: {
    paddingTop: isAndroid ? getPixel(5) : getPixel(6),
  },
  loadingBg: {
    marginTop: isAndroid ? -getPixel(15) : -getPixel(16),
    marginLeft: getPixel(38),
    backgroundColor: color.rentalDateDurationBg,
    marginBottom: getPixel(32),
    height: getPixel(48),
  },
});

const IncentiveBarItem = ({
  positivePolicy,
}: {
  positivePolicy: LabelsType;
}) => {
  const getAdvantage = useMemoizedFn((data: LabelsType) => {
    if (
      data.type === positivePoliciesEnum.policy209 ||
      data.type === positivePoliciesEnum.policy303
    ) {
      return {
        title: data.title,
        textColor: color.tipsRed,
        image: `${ImageUrl.DIMG04_PATH}1tg0412000kt9ku1v7643.png`,
      };
    }
    if (data.type === positivePoliciesEnum.policy207) {
      return {
        title: data.title,
        textColor: color.C_3565B9,
        image: `${ImageUrl.DIMG04_PATH}1tg4612000kt9n2z6E791.png`,
      };
    }
    if (
      data.type === positivePoliciesEnum.policy201 ||
      data.type === positivePoliciesEnum.policy202
    ) {
      return {
        title: data.title,
        textColor: color.C_FF5500,
        image: `${ImageUrl.DIMG04_PATH}1tg0712000kt9qj2k51A9.png`,
      };
    }
    return null;
  });
  const { title, textColor, image } = getAdvantage(positivePolicy) || {};
  if (!title) return null;
  return (
    <XViewExposure
      className={c2xStyles.itemwrap}
      testID={CarLog.LogExposure({
        name: '曝光_填写页_激励模块',
        info: { incentiveText: title, incentiveCode: positivePolicy.type },
      })}
    >
      <View className={c2xStyles.imagewrap} style={styles.imageTop}>
        <XImage src={image} className={c2xStyles.image} />
      </View>
      <View className={c2xStyles.textWrap} style={styles.textTop}>
        <Text
          className={c2xStyles.itemText}
          fontWeight="regular"
          style={{ color: textColor }}
        >
          {title}
        </Text>
      </View>
    </XViewExposure>
  );
};

const IncentiveMarqueeVertical = (props: IPropsType) => {
  const { data, enableAnimate, isShowLoading } = props;
  const itemHeight = getPixel(80);
  if (isShowLoading) {
    return (
      <SkeletonLoading
        visible={true}
        style={styles.loadingBg}
        pageName={PageType.BookingIncentive}
      />
    );
  }

  if (!data) return null;
  return (
    <VerticalMarquee
      itemHeight={itemHeight}
      duration={500}
      interval={3000}
      enableAnimate={enableAnimate}
      style={styles.wrap}
    >
      {data?.map(item => (
        <IncentiveBarItem key={item?.type} positivePolicy={item} />
      ))}
    </VerticalMarquee>
  );
};

export default IncentiveMarqueeVertical;
