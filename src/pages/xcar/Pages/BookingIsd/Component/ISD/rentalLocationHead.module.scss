@import '../../../../Common/src/Tokens//tokens/color.scss';

.wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 16px;
}

.textDesc {
    font-size: 26px;
    color: $C_111111;
}

.pickUp {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin-bottom: 16px;
    margin-right: 32px;
    margin-left: 2px;
}

.dropOff {
    display: flex;
    align-items: flex-start;
    flex-direction: row;
    margin-right: 32px;
    margin-left: 2px;
}
.ml3 {
    margin-left: 3px;;
}

.dotwrapper {
    display: flex;
    align-items: stretch;
    flex-direction: column;
    justify-content: space-between;
}

.filledDot {
    width: 10px;
    height: 10px;
    border: 1.6px solid $C_111111;
    background: $C_111111;
    border-radius: 5px;
    margin-right: 18px;
    margin-top: 10px;
}

.line {
    position: absolute;
    width: 1px;
    height: 28px;
    background: $C_666;
    left: 6px;
    top: 28px;
}
.left7 {
    left: 7px;
}

.unfilledDot {
    width: 10px;
    height: 10px;
    border: 1.6px solid $C_111111;
    border-radius: 5px;
    margin-right: 18px;
    margin-top: 10px;
}