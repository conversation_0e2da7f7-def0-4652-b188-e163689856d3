import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useMemo } from 'react';
import { XView as View, xClassNames } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { ensureFunctionCall } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './rentalInfoStyles.module.scss';
import { Utils, CarLog } from '../../../../Util/Index';
import SkeletonLoading, {
  PageType,
} from '../../../../ComponentBusiness/SkeletonLoading';
import { getProductRequestReference } from '../../../../Global/Cache/ProductSelectors';

const { getPixel, useMemoizedFn } = BbkUtils;
const styles = StyleSheet.create({
  loadingBg: {
    backgroundColor: color.white,
    marginTop: getPixel(16),
    marginBottom: getPixel(20),
  },
});

interface IVehicle {
  vehicleName: string;
  onPress: () => void;
  isShowLoading?: boolean;
  vehicleCode?: string;
  commodityDescList?: Array<string>;
}

const RentalInfo: React.FC<IVehicle> = memo(
  ({
    vehicleName,
    onPress = Utils.noop,
    isShowLoading,
    vehicleCode,
    commodityDescList,
  }: IVehicle) => {
    const traceBaseInfo = useMemo(() => {
      const {
        skuId,
        pStoreCode: pstoreCode,
        rStoreCode: rstoreCode,
      } = getProductRequestReference() || {};
      return {
        skuId,
        pstoreCode,
        rstoreCode,
        vehicleCode,
      };
    }, [vehicleCode]);
    const pressHandler = useMemoizedFn(() => {
      ensureFunctionCall(onPress());
      CarLog.LogCode({
        name: '点击_填写页_车型详情',
        info: traceBaseInfo,
      });
    });
    if (!vehicleName) return null;
    if (isShowLoading) {
      return (
        <SkeletonLoading
          visible={true}
          style={styles.loadingBg}
          pageName={PageType.BookVehicleISD}
        />
      );
    }
    return (
      <BbkTouchable
        onPress={pressHandler}
        testID="booking_vehicle_info"
        className={c2xStyles.wrap}
      >
        <View className={c2xStyles.rightWrap}>
          <View className={xClassNames(c2xStyles.flexStart, c2xStyles.space)}>
            {commodityDescList.length > 0 && (
              <View className={c2xStyles.labelList}>
                {commodityDescList.map((item, index) => (
                  <View
                    key={item}
                    className={xClassNames(
                      c2xStyles.labelItem,
                      index === commodityDescList.length - 1 && c2xStyles.mr0,
                    )}
                  >
                    <Text className={c2xStyles.label}>{item}</Text>
                    {index !== commodityDescList.length - 1 && (
                      <View className={c2xStyles.labelLine} />
                    )}
                  </View>
                ))}
              </View>
            )}
            <View className={c2xStyles.detailWrap}>
              <Text
                className={xClassNames(c2xStyles.label, c2xStyles.labelDetail)}
              >
                详情
              </Text>
              <Text type="icon" className={c2xStyles.rightIcon}>
                {icon.arrowRight}
              </Text>
            </View>
          </View>
        </View>
      </BbkTouchable>
    );
  },
);
export default RentalInfo;
