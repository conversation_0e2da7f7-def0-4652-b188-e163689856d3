@import '../../../../Common/src/Tokens/tokens/color.scss';
.container {
  background-color: $C_FFF;
  border-radius: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin: 0 24px;
  margin-top: 16px;
  padding: 32px;
  padding-bottom: 0;
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title {
  font-size: 34px;
  color: $C_111111;
  line-height: 42px;
}

.labelList {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-right: 30px;
  flex: 1;
}
.labelItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 8px;
}
.labelLine {
  width: 2px;
  height: 20px;
  background-color: $C_E5E5E5;
  margin-left: 8px;
}
.label {
  font-size: 26px;
  line-height: 36px;
  color: $C_111111;
}
.flexStart {
  flex-direction: row;
  align-items: flex-start;
}
.space {
  margin-top: 14px;
}
.titleWrap {
  flex-direction: row;
  align-items: center;
}
.insuranceImg {
  width: 34px;
  height: 34px;
  margin-right: 8px;
  margin-left: -4px;
}
.insuranceText {
  font-size: 30px;
  line-height: 40px;
  color: $C_111111;
}
.orange {
  color: $C_ff6600;
}
.detailIcon {
  margin-left: 10px;
  margin-top: -1px;
  color: $C_111111;
}
.insuranceTips {
  margin-top: -4px;
}

.prepServiceName {
  width: 117px;
  height: 40px;
}