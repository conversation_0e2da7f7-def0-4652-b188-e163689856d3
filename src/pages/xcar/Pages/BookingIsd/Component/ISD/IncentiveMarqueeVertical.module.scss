@import '../../../../Common/src/Tokens/tokens/color.scss';

.title {
  font-size: 24px;
  line-height: 30px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_111111;
}

.itemwrap {
  flex-direction: row;
  align-items: center;
  background-color: $bookingOptimizationGrayBg;
  margin-left: 38px;
  margin-bottom: 32px;
}

.image {
  width: 32px;
  height: 32px;
}

.textWrap {
  padding-bottom: 6px;
  background-color: $C_FFF;
  padding-left: 8px;
  padding-right: 18px;
  border-top-right-radius: 26px;
  border-bottom-right-radius: 26px;
  height: 48px;
}

.imagewrap {
  padding-bottom: 7px;
  background-color: $C_FFF;
  padding-left: 12px;
  border-top-left-radius: 26px;
  border-bottom-left-radius: 26px;
  height: 48px;
}

.itemText {
  font-size: 24px;
  height: 34px;
}
