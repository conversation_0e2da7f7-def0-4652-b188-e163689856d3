@import '../../../../Common/src/Tokens/tokens/color.scss';
.container {
  background-color: $C_FFF;
  border-radius: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px $R_0_0_0_004;
  margin: 0 24px;
  margin-top: 16px;
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 0 32px;
}

.title {
  font-size: 34px;
  color: $C_111111;
  line-height: 42px;
}
.priceWrap {
  flex-direction: row;
  margin-bottom: -7px;
}
.priceText {
  color: $blueDeepBase;
  line-height: 40px;
  font-size: 26px;
  margin-bottom: 2px;
}
.textWrapRel {
  position: relative;
  bottom: -12px;
  flex-direction: row;
  align-items: center;
}
.textWrapRelIos {
  bottom: -8px;
}
.price {
  font-size: 36px;
  font-family: TripNumber-SemiBold;
  color: $blueDeepBase;
}
.priceYanWrap {
  margin-top: -4px;
}

.insuranceOptions {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  height: 315px;
  width: 100%;
}

.imageBgWrap {
  padding: 0 32px;
  margin: 22px 1px 6px 1px;
}
.insuranceOptionsImage {
  width: 100vw;
  height: 315px;
}
.currentOption {
  padding-top: 30px;
  width: 321px;
  justify-content: center;
  align-items: flex-start;
  padding-left: 27px;
}

.recommendedOption {
  padding-top: 30px;
  width: 321px;
  justify-content: center;
  align-items: flex-start;
  padding-left: 44px;
}
.optionText {
  font-size: 26px;
  color: $C_111111;
  line-height: 37px;
  margin-bottom: 14px;
}
.recommendedOptionText {
  color: $C_111111;
}
.recommendedOption {
  margin-right: 42px;
}
.currentOption {
  margin-left: 10px;
}
.optionDetail {
  font-size: 30px;
  line-height: 42px;
  color: $C_111111;
}
.optionDetailIcon {
  font-size: 22px;
  color: $C_111111;
  margin-left: 8px;
  margin-top: 2px;
}
.upgradeArrow {
  width: 40px;
  text-align: center;
}
.recommendedOptionDetail {
  color: $C_111111;
}
.arrowIcon {
  font-size: 24px;
  color: #007aff;
}

.optionTitle {
  font-size: 24px;
  color: $C_111111;
  line-height: 32px;
}

.recommendedOptionTitle {
  font-size: 24px;
  color: $C_E35600;
  line-height: 32px;
}
.currentTagWrap {
  border-radius: 4px;
  padding: 4px 11px;
  margin-bottom: 20px;
  flex-direction: row;
  align-items: center;
  background-color: $C_f5f6fa;
  border: 1px solid $R_151_151_151_0_0_7;
}
.recommendedTagWrap {
  border-radius: 4px;
  padding: 4px 11px;
  margin-bottom: 20px;
  flex-direction: row;
  align-items: center;
  border: 1px solid $R_137_73_24_0_0_5;
}
.orangeTagWrap {
  background-color: $C_ff6600;
}
.mb14 {
  margin-bottom: 14px;
}

.negaMl4 {
  margin-left: -4px;
}

.upgradeBtnWrap {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: relative;
  top: -4px;
  right: -1px;
}
.upgradeGou {
  width: 34px;
  height: 34px;
  margin-left: 12px;
  margin-top: 3px;
}
.thumb {
  width: 30px;
  height: 30px;
}

.packageIcon {
  width: 36px;
  height: 36px;
  margin-right: 4px;
}

.priceWrap {
  flex-direction: row;
  align-items: center;
}

.perDayWrap {
  flex-direction: row;
}

.mt7 {
  margin-bottom: 7px;
}

.perDay {
  margin-bottom: 5px;
}

.prepServiceName {
  width: 117px;
  height: 40px;
}