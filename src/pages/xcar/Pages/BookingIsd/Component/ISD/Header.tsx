import StyleSheet from '@c2x/apis/StyleSheet';
import { XView as View, XImage as Image, XViewExposure } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import React, { memo } from 'react';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xstyles from './Header.module.scss';
import { CarLog } from '../../../../Util/Index';

type HeaderProps = {
  vehicleName: string;
  license: string;
  isNewEnergy: boolean;
  onPressLeft?: () => void;
  logExpInfo?: {
    vehicleName?: string;
    vehicleCode?: string;
    license?: string;
    vehicleBasicInfo?: string;
    carAge?: string;
    insuranceInfo?: string;
    pickUpStoreInfo?: string;
    dropOffStoreInfo?: string;
  };
};
const { fixOffsetTop, getPixel, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    margin: 0,
    paddingTop: fixOffsetTop(),
    backgroundColor: color.white,
  },
  wrapperInner: {
    minHeight: isAndroid ? getPixel(56) : getPixel(44),
    flexDirection: 'row',
  },
  sideLeft: {
    position: 'absolute',
    marginTop: getPixel(15),
    marginBottom: isAndroid ? getPixel(21) : getPixel(15),
    zIndex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: getPixel(48),
    minHeight: getPixel(48),
    left: isAndroid ? getPixel(20) : getPixel(24),
    top: getPixel(6),
  },
});

const Header = memo(
  ({
    vehicleName,
    license,
    isNewEnergy,
    onPressLeft,
    logExpInfo,
  }: HeaderProps) => {
    const licenseWid = license ? license.length * getPixel(34) : 0;
    const limitedWid = getPixel(558);
    const EnergyWid = isNewEnergy ? getPixel(38) : 0;
    const titleWid = limitedWid - licenseWid - EnergyWid;

    return (
      <XViewExposure
        className={c2xstyles.wrapper}
        style={styles.wrapper}
        testID={CarLog.LogExposure({
          name: '曝光_填写页_产品信息',
          info: {
            ...logExpInfo,
          },
        })}
      >
        <View style={styles.wrapperInner}>
          <Touchable style={styles.sideLeft} onPress={onPressLeft}>
            <Text type="icon" className={c2xstyles.leftIcon}>
              &#xe943;
            </Text>
          </Touchable>
          <View className={c2xstyles.content}>
            <View className={c2xstyles.contentVehicle}>
              <Text
                fontWeight="semibold"
                className={c2xstyles.title}
                style={{ maxWidth: titleWid }}
                numberOfLines={1}
              >
                {vehicleName}
              </Text>
            </View>
            <Text fontWeight="semibold" className={c2xstyles.title}>
              {license}
            </Text>
            {isNewEnergy && (
              <Image
                src="https://dimg04.c-ctrip.com/images/1tg2u12000kt9kmldF888.png"
                className={c2xstyles.carIcon}
              />
            )}
          </View>
        </View>
      </XViewExposure>
    );
  },
);

export default Header;
