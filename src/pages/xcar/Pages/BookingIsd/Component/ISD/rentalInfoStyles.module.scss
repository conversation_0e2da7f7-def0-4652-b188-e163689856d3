@import '../../../../Common/src/Tokens/tokens/color.scss';

.secretBoxWrap {
  flex-direction: row;
  align-items: center;
  margin-top: 16px;
  margin-left: -13px;
}
.wrap {
  flex-direction: row;
  align-items: center;
  margin-bottom: 4px;
}
.secretBoxImage {
  width: 170px;
  height: 150px;
  margin-right: 16px;
}
.image {
  width: 165px;
  height: 110px;
  margin-right: 12px;
  margin-left: -12px;
}
.rightWrap {
  flex: 1;
  justify-content: flex-start;
}
.rightTopWrap {
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
}
.vehicleName {
  font-size: 34px;
  line-height: 44px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
  margin-right: 8px;
}
.lessGap {
  height: 28px;
  width: 8px;
}
.rightBottomWrap {
  flex-direction: row;
  align-items: center;
  margin-top: 14px;
}
.vendorName {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.arrowRight {
  font-size: 26px;
  margin-left: 4px;
  color: $linearGradientBlackDiamondStart;
}

.vehicleNameWrap {
  flex: 1;
}
.labelList {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-right: 4px;
  flex: 1;
  margin-top: -6px;
}
.labelItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 6px;
  margin-top: 6px;
}
.mr0 {
  margin-right: 0;
}
.labelLine {
  width: 2px;
  height: 27px;
  background-color: $C_E5E5E5;
  margin-left: 8px;
  margin-right: 2px;
}
.label {
  font-size: 30px;
  line-height: 36px;
  color: $C_111111;
  font-weight: medium;
}
.labelDetail {
  font-size: 26px;
  font-weight: normal;
  margin-right: -4px;
}
.flexStart {
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
}
.detailWrap {
  flex-direction: row;
  align-items: center;
  margin-top: -4px;
  margin-right: -8px;
}
.lessImg {
  width: 38px;
  height: 38px;
}
.optimizeTag {
  padding-left: 8px;
  padding-right: 8px;
  border-radius: 4px;
  background-color: rgba($C_FFF0DC, 0.6);
  margin-right: 8px;
  flex-direction: row;
  align-items: center;
  padding-top: 2px;
  padding-bottom: 2px;
}
.optimizeText {
  font-size: 24px;
  line-height: 32px;
  color: $C_602A12;
}
.space {
  margin-top: 12px;
}
.arrowRight {
  font-size: 33px;
}
.arrowRightBtn {
  margin-top: 2px;
}
.optimizeImg {
  width: 26px;
  height: 26px;
}
.optimizeImgAndroid {
  margin-bottom: -1px;
}
.optimizeImgIos {
  margin-top: -1px;
}
.youxuanBg {
  height: 34px;
  margin-right: 10px;
}
.rightIcon {
  color: $C_111111;
  margin-left: 4px;
}