import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useCallback, useMemo } from 'react';
import { XViewExposure } from '@ctrip/xtaro';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import c2xStyles from './serviceC2xStyles.module.scss';
import { CarLog, Utils } from '../../../../Util/Index';
import {
  getRentalGuaranteeV2,
  getcarAgentInsuranceCode,
} from '../../../../State/Product/BbkMapper';
import { getCurProductInfo } from '../../../../State/Product/Mappers';
import { Platform } from '../../../../Constants/Index';
import BbkComponentCarService, {
  CarServiceFromPageTypes,
} from '../../../../ComponentBusiness/CarService';
import InsuranceUpgrade from './InsuranceUpgrade';
import CurrentInsurance from './CurrentInsurance';
import CtripInsurances from '../CtripInsurances';

const { getPixel, isHarmony } = BbkUtils;

const styles = StyleSheet.create({
  bookingOptimizationBlockSection: {
    marginTop: getPixel(16),
    backgroundColor: color.white,
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
    borderRadius: getPixel(16),
  },
});

const gotoInsurance = () => {
  const param = {
    type: 1,
  };
  /* eslint-disable max-len */
  const url = `${
    Platform.CAR_CROSS_URL.TravelInsuranceAgreement.ISD
  }&sparam=${encodeURIComponent(JSON.stringify(param))}`;
  Utils.openUrlWithTicket(url);
};

const Service = ({
  curInsPackageId,
  changeSelectInsurance,
  addOnCodes,
  isPriceLoading,
  showPayMode,
  selectedInsuranceId,
  insuranceAvailable,
  onPressServiceClaimMore = Utils.noop,
  onPressCarServiceDetail,
  ptime,
  rtime,
  insuranceFlag,
  isEhai,
  isEasyLife2024,
  isShowTableArrow,
  isRP,
}) => {
  const { rentalGuaranteeV2, carAgentInsuranceCode } = useMemo(() => {
    const rentalGuarantee = getRentalGuaranteeV2(curInsPackageId);
    const code = getcarAgentInsuranceCode(rentalGuarantee);
    return {
      rentalGuaranteeV2: rentalGuarantee,
      carAgentInsuranceCode: code,
    };
  }, [curInsPackageId]);
  const { packageDetailList } = rentalGuaranteeV2 || {};
  const filterNewPackageList = useMemo(() => {
    if (!packageDetailList?.length) {
      return [];
    }
    return packageDetailList.filter(item => !!item.isSelect);
  }, [packageDetailList]);
  const showNewInsurance = !!isRP && !!filterNewPackageList?.length;
  const onPressButton = useCallback(
    item => {
      if (isPriceLoading) {
        return;
      }
      const insuranceId = item.uniqueCode;
      changeSelectInsurance({
        addOnCode: insuranceId,
        isFromServiceTableEvent: true,
      });
    },
    [changeSelectInsurance, isPriceLoading],
  );

  const { ctripInsurances } = getCurProductInfo(curInsPackageId);
  const { width } = useWindowSizeChanged();
  return (
    <XViewExposure testID={CarLog.LogExposure({ name: '曝光_保险模块' })}>
      <XViewExposure
        className={isHarmony && c2xStyles.wrapHarmony}
        testID={CarLog.LogExposure({
          name: '曝光_填写页_车行服务',

          info: {
            carAgentInsuranceCode,
          },
        })}
      >
        {showNewInsurance &&
          (filterNewPackageList?.length === 1 ? (
            <CurrentInsurance
              insuranceTips={rentalGuaranteeV2?.vendorServiceSubDesc}
              descriptions={filterNewPackageList[0]?.descriptionV2}
              onPressServiceClaimMore={onPressServiceClaimMore}
              uniqueCode={filterNewPackageList[0]?.uniqueCode}
              onPressCarServiceDetail={onPressCarServiceDetail}
            />
          ) : (
            <InsuranceUpgrade
              insuranceTips={rentalGuaranteeV2?.vendorServiceSubDesc}
              packageDetailList={filterNewPackageList}
              onPressButton={onPressButton}
              onPressServiceClaimMore={onPressServiceClaimMore}
              onPressCarServiceDetail={onPressCarServiceDetail}
            />
          ))}
        {!showNewInsurance && (
          <BbkComponentCarService
            insuranceProductTips={rentalGuaranteeV2?.vendorServiceSubDesc}
            rentalGuaranteeV2={rentalGuaranteeV2}
            selectedInsuranceId={addOnCodes}
            isPriceLoading={isPriceLoading}
            curInsPackageId={curInsPackageId}
            onPressButton={onPressButton}
            onPressServiceClaimMore={onPressServiceClaimMore}
            onPressCarServiceDetail={onPressCarServiceDetail || Utils.noop}
            style={styles.bookingOptimizationBlockSection}
            fromPage={CarServiceFromPageTypes.booking}
            ptime={ptime}
            rtime={rtime}
            width={width - getPixel(48)}
            isHideDetailBtn={!isEasyLife2024}
            isEasyLife2024={isEasyLife2024}
            isEhai={isEhai}
            isShowTableArrow={isShowTableArrow}
            haveInsBg={false}
            isNewIsdBooking={true}
          />
        )}
      </XViewExposure>
      <CtripInsurances
        isPriceLoading={isPriceLoading}
        updateCarGuarantee={changeSelectInsurance}
        ctripInsurance={ctripInsurances?.[0]}
        gotoInsurance={gotoInsurance}
      />
    </XViewExposure>
  );
};

export default Service;
