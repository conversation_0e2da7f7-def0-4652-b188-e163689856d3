@import '../../../Common/src/Tokens/tokens/color.scss';


.checkImg {
  width: 32px;
  height: 32px;
  margin-left: 10px;
}

.itemTitle {
  font-size: 30px;
  top: 2px;
}
.depositSubTitleWrap {
  min-height: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.mt8 {
  margin-top: 8px;
}
.helpIcon {
  font-family: ct_font_common;
}
.selectPassengerWrap {
  min-height: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.selectPassengerText {
  color: $fontPrimary;
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.rightIcon {
  color: $C_111111;
  margin-left: 12px;
  width: 26px;
  height: 26px;
  right: 3px;
}

.mainWrap{
  background-color: $white;
  border-radius: 12px;
  margin: 16px 24px 0 24px;
  // padding-bottom: 24px;
}
.couponHead {
  margin: 2px 2px 0 2px;
  padding: 0 28px 0 30px;
  height: 88px;
  background-color: $C_FFF4F5;
  border-radius: 12px 12px 0 0 ;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.couponBg {
  background-color: $C_FFF4F5;
  height: 12px;
  margin: 0px 2px 0 2px;
}
.newImg {
  width: 277px;
  height: 48px;
  top: -2px;
}
.headImg {
  flex: 1 ;
}
.itemWrap {
  display: flex;
  flex-direction: row;
  display: flex;
  align-items: center;
  background: $white;
  padding: 26px 0;
}
.leftItem {
  flex:1;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.logoImg {
  width: 36px;
  height: 36px;
  margin-right: 14px;
}
.couponContent {
  padding:0 36px;
}
.headTitle {
  display: flex;
  flex-direction: row;
  top: 2px;
}
.top5 {
  top: 5px;
}
.font32 {
  color: $orangePrice;
  font-size: 32px;
  line-height: 42px;
  top: -2px;
}
.headTitle {
  display: flex;
  align-items: center;
}
.itemTxt {
  font-size: 26px;
  color: $C_111111;
 flex-wrap: wrap;
}
.rightItem {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.itemArrow {
  width: 26px;
  height: 26px;
  margin-left: 12px;
  color: $C_111111;
  right: 3px;
}
.receiveWrap {
  display: flex;
  flex-direction: row;
  background: $orangePrice;
  color: $white;
  padding: 0 2px 0 14px;
  border-radius: 6px;
  align-items: center;
  height: 40px;
}
.receiveTxt {
  font-size: 24px;
  color: $white;
  // line-height: 40px;
}
.receiveArrow {
  // line-height: 40px;
  color: $white;
  margin-left: 6px;
  top: 1px;
}

.newContentWrap {
  margin-top: -12px;
  background-color: $white;
  border-radius: 12px;
  padding: 0 32px;
}
.bottom8 {
  padding-bottom: 8px ;
}

.top8{
  position: relative;
  top: 8px;
}
.top4 {
  position: relative;
  top: -4px;
}

.dotBg {
  width: 24px;
  height: 23px;
  position: absolute;
  left:104px;
}
.richTxt {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.c11 {
  color: $C_111111
}
.noCoupon {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.noTxt {
  color: $C_888888;
}
.noBtn {
  color: $C_111111;
  align-items: center;
}
.oldImg {
  width: 139px;
  height: 41px;
}
.left4 {
  margin-left: -4px;
}
.line {
  height: 1px;
  background: $couponSplit;
  width: 100%;
}
.trans {
  transform: scaleY(0.5);
}