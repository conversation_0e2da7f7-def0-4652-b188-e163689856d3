import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import {
  xMergeStyles,
  XImageBackground as ImageBackground,
  XViewExposure,
} from '@ctrip/xtaro';
import { color, font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import c2xStyles from './advantageC2xStyles.module.scss';
import { CarLog, Utils } from '../../../Util/Index';
import { ImageUrl, UITestID } from '../../../Constants/Index';
import { TypeEnum } from '../../../ComponentBusiness/AdvantageBox/src/AdvantageBox';
import { Constants } from '../../../ComponentBusiness/Common';
import { MembershipPerceptionType } from '../../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';

const { MemberStyle } = Constants;
const { getPixel, autoProtocol } = BbkUtils;
const styles = StyleSheet.create({
  bg: {
    paddingTop: getPixel(8),
    paddingBottom: getPixel(4),
    paddingLeft: getPixel(39),
  },
  img: {
    height: getPixel(310),
  },
  wrap: {
    borderTopLeftRadius: getPixel(16),
    borderTopRightRadius: getPixel(16),
    paddingBottom: getPixel(6),
  },
  wrapTop: {
    marginTop: getPixel(24),
  },
  cCancelRuleBg: {
    paddingTop: getPixel(16),
    paddingBottom: getPixel(8),
  },
  bCancelRuleImg: {
    height: getPixel(66),
  },
  cCancelRuleImg: {
    height: getPixel(100),
    borderTopLeftRadius: getPixel(16),
    borderTopRightRadius: getPixel(16),
  },
  bookingOptimizationCancelRuleIcon: {
    width: getPixel(50),
    height: getPixel(50),
    marginRight: getPixel(11),
    marginLeft: getPixel(8),
  },
  mlf16: {
    marginLeft: getPixel(-16),
  },
  bookingOptimizationCancelRuleText: {
    ...font.body3BoldStyle,
  },
  topIsRadiusImg: {
    borderTopLeftRadius: getPixel(12),
    borderTopRightRadius: getPixel(12),
  },
});

type IAdvantage = {
  positivePolicies: any;
  membershipPerception: MembershipPerceptionType;
  topIsRadius?: boolean;
};
const { DiscountType, UrgentType, CancelRule } = TypeEnum;
const getAdvantage = (data: any, membershipPerception) => {
  if (membershipPerception) {
    const { booking } = MemberStyle[membershipPerception.curLevelCode] || {};
    if (booking) {
      return {
        textColor: booking.positivePoliciesImgTxtColor,
        image: booking.positivePoliciesImg,
        icon: booking.positivePoliciesIcon,
      };
    }
  }
  if (DiscountType.includes(data.type)) {
    return {
      textColor: color.marketCouponPriceColor,
      image: `${ImageUrl.CTRIP_EROS_URL}advantageBgRed.png`,
      icon: `${ImageUrl.CTRIP_EROS_URL}advantageNewCoupon.png`,
    };
  }
  if (data.type === CancelRule) {
    const imguri = `${ImageUrl.BBK_IMAGE_PATH}free_cancel_banner_bg.png`;
    return {
      textColor: color.refundSuccess,
      image: Utils.isCtripIsd()
        ? imguri
        : `${ImageUrl.CTRIP_EROS_URL}advantageBgGreen.png`,

      icon: Utils.isCtripIsd()
        ? `${ImageUrl.BBK_IMAGE_PATH}booking_green_check.png`
        : `${ImageUrl.CTRIP_EROS_URL}advantageCanclePolicy.png`,

      isBCancelRule: Utils.isCtripIsd(),
    };
  }
  if (data.type === UrgentType) {
    return {
      textColor: color.refundSuccess,
      image: `${ImageUrl.CTRIP_EROS_URL}advantageBgGreen.png`,

      icon: `${ImageUrl.CTRIP_EROS_URL}advantageTime.png`,
    };
  }
  return {
    textColor: color.refundSuccess,
    image: `${ImageUrl.CTRIP_EROS_URL}advantageBgGreen.png`,
    icon: `${ImageUrl.CTRIP_EROS_URL}advantageDefault.png`,
  };
};
const Advantage: (data: IAdvantage) => React.ReactElement = memo(
  ({ positivePolicies, membershipPerception, topIsRadius }) => {
    const width100Style = useWindowSizeChanged();
    if (!positivePolicies?.length) return null;
    const positivePolicie = positivePolicies[0];
    if (!positivePolicie?.title) return null;
    const { textColor, image, icon } = getAdvantage(
      positivePolicie,
      membershipPerception,
    );
    const { curLevelCode } = membershipPerception || {};
    return (
      <XViewExposure
        testID={CarLog.LogExposure({
          name: '曝光_填写页_激励模块',
          info: { memberLevel: curLevelCode },
        })}
        className={topIsRadius && c2xStyles.topIsRadius}
      >
        <ImageBackground
          imageStyle={xMergeStyles([
            styles.img,
            width100Style,
            Utils.isCtripIsd() && styles.bCancelRuleImg,
            topIsRadius && styles.topIsRadiusImg,
          ])}
          style={xMergeStyles(
            styles.bg,
            layout.flexRow,
            Utils.isCtripIsd() && styles.wrap,
          )}
          source={{ uri: autoProtocol(image) }}
          resizeMode={Utils.isCtripIsd() ? 'cover' : 'contain'}
        >
          <Image
            className={c2xStyles.icon}
            src={autoProtocol(icon)}
            mode="aspectFit"
            style={xMergeStyles(
              Utils.isCtripIsd() && styles.bookingOptimizationCancelRuleIcon,
              topIsRadius && styles.mlf16,
            )}
          />

          <Text
            testID={UITestID.car_testid_comp_booking_advantageDom}
            fontWeight="medium"
            style={xMergeStyles(
              font.body3MediumStyle,
              { color: textColor },
              Utils.isCtripIsd() && styles.bookingOptimizationCancelRuleText,
            )}
          >
            {positivePolicie.title}
          </Text>
        </ImageBackground>
      </XViewExposure>
    );
  },
);
export default Advantage;
