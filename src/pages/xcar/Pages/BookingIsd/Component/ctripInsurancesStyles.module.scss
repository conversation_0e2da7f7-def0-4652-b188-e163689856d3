@import '../../../Common/src/Tokens/tokens/color.scss';


.cInsuItem {
  background-color: $C_FFF;
  margin: 16px 24px 0px;
  border-radius: 12px;
  padding: 32px 32px 26px 32px;
}

.cInsuItemTop {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.priceWrap {
  flex-direction: row;
  margin-bottom: -4px;
}
.priceText {
  color: $blueDeepBase;
  line-height: 40px;
  font-size: 26px;
}
.textWrapRel {
  position: relative;
  bottom: -12px;
  flex-direction: row;
  align-items: center;
}
.textWrapRelIos {
  bottom: -8px;
}
.price {
  font-size: 36px;
  font-family: TripNumber-SemiBold;
  color: $blueDeepBase;
}
.priceYanWrap {
  margin-top: -4px;
}

.upgradeGou {
  width: 34px;
  height: 34px;
  margin-left: 12px;
}

.cInsuItemTopR {
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.cInsuTipsWrap {
  flex-direction: row;
  align-items: center;
}

.cInsuItemName {
  font-size: 34px;
  line-height: 42px;
  color: $C_111111;
}

.cInsuDescWrap {
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 16px;
}

.cInsuDescTextWrap {
  flex-direction: row;
  align-items: center;
}

.cInsuDescText {
  font-size: 26px;
  color: $C_111111;
}

.cInsuTipsWrap {
  margin-top: 16px;
}

.cInsuTipsText {
  line-height: 36px;
  font-size: 26px;
  color: $C_888888;
}

.moreIcon {
  font-size: 26px;
  color: $C_888888;
}

.serviceItemSplit {
  width: 2px;
  height: 24px;
  background-color: $C_E5E5E5;
  margin: 0 8px;
}

.priceWrap {
  flex-direction: row;
  align-items: center;
}

.perDayWrap {
  flex-direction: row;
}

.mt5 {
  margin-bottom: 5px;
}

.perDay {
  margin-bottom: 5px;
}