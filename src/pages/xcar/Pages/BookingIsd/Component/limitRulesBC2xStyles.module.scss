@import '../../../Common/src/Tokens/tokens/color.scss';

.wrap {
  flex-direction: row;
  align-items: center;
  margin-top: 16px;
}
.titleWrap {
  margin-right: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.detailWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.icon {
  font-size: 26px;
  margin-right: 4px;
  color: $redBase;
}
.limitView {
  flex: 1;
  overflow: hidden;
}
.mbf2 {
  margin-bottom: -2px;
}
.title {
  color: $bookingOptimizationBlue;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-left: 12px;
}
.iconPng {
  width: 30px;
  height: 30px;
  margin-right: 12px;
}
.titleBlack {
  color: $C_111111;
  font-size: 26px;
}
.mt2 {
  margin-top: 4px;
}
