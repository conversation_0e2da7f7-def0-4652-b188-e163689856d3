import { xMergeStyles } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, CSSProperties } from 'react';

import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import SkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import { BookingWrapper } from '../Components';

const { getPixel, vw } = BbkUtils;
const styles = StyleSheet.create({
  bg: {
    backgroundColor: color.white,
    marginLeft: getPixel(16),
    marginRight: getPixel(16),
    width: vw(100) - getPixel(32),
    overflow: 'hidden',
    borderRadius: getPixel(16),
    shadowRadius: getPixel(16),
    alignItems: 'center',
  },
});

interface ILoadingType {
  pageName: PageType;
  children?: React.ReactNode;
  style?: CSSProperties;
}

const Loading = memo(({ children, pageName, style }: ILoadingType) => {
  return (
    <BookingWrapper>
      {children}
      <SkeletonLoading
        style={xMergeStyles([styles.bg, style])}
        visible={true}
        pageName={pageName}
      />
    </BookingWrapper>
  );
});

export default Loading;
