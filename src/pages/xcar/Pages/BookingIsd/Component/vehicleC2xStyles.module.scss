@import '../../../Common/src/Tokens/tokens/color.scss';

.secretBoxWrap {
  flex-direction: row;
  align-items: center;
  margin-top: 16px;
  margin-left: -13px;
}
.wrap {
  flex-direction: row;
  align-items: center;
}
.secretBoxImage {
  width: 170px;
  height: 150px;
  margin-right: 16px;
}
.image {
  width: 165px;
  height: 110px;
  margin-right: 16px;
}
.rightWrap {
  flex: 1;
  justify-content: flex-start;
}
.rightTopWrap {
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
}
.vehicleName {
  font-size: 34px;
  line-height: 44px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
  margin-right: 8px;
}
.lessGap {
  margin-left: 6px;
  height: 28px;
}
.rightBottomWrap {
  flex-direction: row;
  align-items: center;
  margin-top: 8px;
}
.vendorName {
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.arrowRight {
  font-size: 26px;
  margin-left: 4px;
  color: $linearGradientBlackDiamondStart;
}

.vehicleNameWrap {
  flex: 1;
}
