@import '../../../Common/src/Tokens/tokens/color.scss';

.contentItemWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.logoImg {
  width: 50px;
  height: 50px;
  margin-right: 8px;
  margin-top: -1px;
}
.itemTitle {
  margin-right: 36px;
  color: $recommendProposeBg;
  font-size: 30px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.couponItemTitle {
  color: $recommendBg;
  font-size: 30px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
}
.priceStyle {
  font-size: 32px;
  line-height: 42px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $recommendBg;
}
.rightIcon {
  color: $fontSecondary;
  margin-left: 8px;
  width: 26px;
  height: 26px;
}
.activityItemWrap {
  padding-top: 25px;
  padding-bottom: 25px;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.disableText {
  color: $darkGrayBorder;
  font-size: 30px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.detailText {
  color: $recommendProposeBg;
  font-size: 30px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.circleQuestionIcon {
  font-size: 26px;
  color: $grayBase;
  margin-left: 8px;
  margin-bottom: 1px;
}
.titleText {
  color: $recommendBg;
  margin-left: 32px;
  margin-top: 28px;
  margin-bottom: 4px;
  font-size: 36px;
  line-height: 48px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
}
.couponWrap {
  padding-left: 32px;
  padding-right: 32px;
}
.pdTOP {
  padding-top: 6px;
}
