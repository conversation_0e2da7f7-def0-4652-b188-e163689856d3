import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import Image from '@c2x/components/Image';
import React, { memo, useCallback, useState } from 'react';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import * as ImageUrl from '../../../Constants/ImageUrl';
import c2xStyles from './rentalLocationItemC2xStyles.module.scss';
import CarRentalCenterDesc from '../../../ComponentBusiness/CarRentalCenterDesc';
import { GetAB, Utils, GetABCache } from '../../../Util/Index';
import { IGuidInfoType, IRentalLocationItem } from '../Types';
import Texts from '../Texts';
import { UITestID } from '../../../Constants/Index';
import SelfServiceSlogan from '../../../ComponentBusiness/SelfServiceSlogan';

const { getPixel, useMemoizedFn } = BbkUtils;
const styles = StyleSheet.create({
  content: {
    marginLeft: getPixel(16),
    marginRight: getPixel(60),
    maxWidth: getPixel(480),
  },
  vendorListcontent: { marginLeft: getPixel(16), width: getPixel(422) },
  vendorListContentSingle: { marginLeft: getPixel(16), width: getPixel(512) },
  blueContent: { marginRight: getPixel(90) },
  selfServiceSlogan: {
    marginTop: getPixel(2),
    marginBottom: getPixel(14),
  },
  ml0: {
    marginLeft: 0,
  },
});

const getLabelName = (type: IGuidInfoType) => {
  let labelName = '';
  switch (type) {
    case IGuidInfoType.PickUp:
      labelName = Texts.pickUp;
      break;
    case IGuidInfoType.DropOff:
      labelName = Texts.dropOff;
      break;
    case IGuidInfoType.Merge:
      labelName = Texts.pickUpAndDropOff;
      break;
    default:
      labelName = '';
      break;
  }
  return labelName;
};

const RentalLocationItem: React.FC<IRentalLocationItem> = memo(
  ({
    storeGuid,
    address,
    type,
    isShowLeftLine = false,
    onPress = Utils.noop,
    guideType,
    wrapStyle,
    isCenter,
    isVendorList,
    onPressRentalCenter,
    isSelfService,
    selfServiceText,
    isShowPickUpDropOffLabel = true,
    isMerged,
  }) => {
    const handlePress = useCallback(() => {
      onPress(guideType);
    }, [onPress, guideType]);

    const [wrapHeight, setWrapHeight] = useState(0);
    const wrapOnLayout = useMemoizedFn(event => {
      setWrapHeight(event.nativeEvent.layout.height);
    });
    if (!storeGuid || !address || !type) return null;
    const isISDInterestPoints = GetAB.isISDInterestPoints();
    const vendorListStyle = isMerged
      ? styles.vendorListContentSingle
      : styles.vendorListcontent;
    const noCenterText = isCenter ? storeGuid?.split(' ')?.[0] : storeGuid;
    
    return (
      <BbkTouchable
        onPress={handlePress}
        testID={UITestID.car_testid_comp_vendor_modal_address}
        className={isISDInterestPoints ? c2xStyles.wrapNew : c2xStyles.wrap}
        style={wrapStyle}
      >
        {isShowPickUpDropOffLabel && (
          <View className={c2xStyles.labelWrap}>
            <Text className={c2xStyles.label}>{getLabelName(type)}</Text>
            {isShowLeftLine && (
              <View
                className={c2xStyles.leftLine}
                style={{ height: wrapHeight }}
              />
            )}
          </View>
        )}
        <View
          onLayout={wrapOnLayout}
          style={xMergeStyles([
            isVendorList ? vendorListStyle : styles.content,
            !isShowPickUpDropOffLabel && styles.ml0,
          ])}
        >
          {isSelfService && selfServiceText && (
            <SelfServiceSlogan
              text={selfServiceText}
              style={styles.selfServiceSlogan}
            />
          )}
          <View
            className={
              c2xStyles.rowNew
            }
          >
            {isCenter && (
              <Image
                className={c2xStyles.centerIcon}
                source={{
                  uri: `${ImageUrl.DIMG04_PATH}1tg1d12000hhgtn1xA635.png`,
                }}
              />
            )}
            <Text numberOfLines={1} className={c2xStyles.storeGuid}>
              {noCenterText}
            </Text>
          </View>
          {isVendorList ? (
            <Text className={c2xStyles.address}>{address}</Text>
          ) : (
            <Text numberOfLines={1} className={c2xStyles.address}>
              {address}
            </Text>
          )}
        </View>
      </BbkTouchable>
    );
  },
);

export default RentalLocationItem;
