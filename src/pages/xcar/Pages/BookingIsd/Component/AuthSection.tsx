import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Image from '@c2x/components/Image';
import React, { memo, useCallback, useMemo } from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  XViewExposure,
} from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './authSectionC2xStyles.module.scss';
import { IAuthSection, IAuthTypes, IAuthStatus } from '../Types';
import { ImageUrl } from '../../../Constants/Index';
import { AppContext, Channel, CarLog } from '../../../Util/Index';

const { autoProtocol } = BbkUtils;

const wrapLinearGradientColors = [
  setOpacity(color.depositFreeAuthGradientStart, 0.45),
  color.depositFreeAuthGradientEnd,
];

const buttonLinearGradientColors = [
  color.linearGradientBlueLight,
  color.linearGradientBlueDark,
];

const AuthSection: React.FC<IAuthSection> = memo(
  ({ authStatus, content, note, button }: IAuthSection) => {
    const { title, type } = button || {};

    const Wrapper = type === IAuthTypes.SelectPassenger ? Touchable : View;

    const handlePress = useCallback(() => {
      if (type === IAuthTypes.SelectPassenger) {
        AppContext.PageInstance.push(Channel.getPageId().DriverList.EN);
      }
    }, [type]);

    const testID = useMemo(
      () =>
        authStatus === IAuthStatus.NotEnough
          ? CarLog.LogExposure({ name: '填写页押金模块_额度不足_曝光埋点' })
          : '',
      [authStatus],
    );

    return (
      <XViewExposure testID={testID}>
        <Wrapper onPress={handlePress} debounce={true}>
          <Image
            className={c2xStyles.topAngle}
            src={autoProtocol(`${ImageUrl.CTRIP_EROS_URL}top_angle_2.png`)}
            mode="aspectFill"
          />

          <LinearGradient
            className={c2xStyles.wrap}
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 0.0, y: 1.0 }}
            locations={[0, 1.0]}
            colors={wrapLinearGradientColors}
          >
            <View className={c2xStyles.leftWrap}>
              {content?.map(item => (
                <Text
                  key={item}
                  className={c2xStyles.content}
                  fontWeight="medium"
                >
                  {item}
                </Text>
              ))}
              {!!note && <Text className={c2xStyles.note}>{`*${note}`}</Text>}
            </View>
            {!!title && (
              <LinearGradient
                className={c2xStyles.linearGradient}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 1.0 }}
                locations={[0, 1.0]}
                colors={buttonLinearGradientColors}
              >
                <Text className={c2xStyles.buttonText} fontWeight="medium">
                  {title}
                </Text>
              </LinearGradient>
            )}
          </LinearGradient>
        </Wrapper>
      </XViewExposure>
    );
  },
);
export default AuthSection;
