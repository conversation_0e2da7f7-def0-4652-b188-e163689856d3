import {
  xClassNames as classN<PERSON>s,
  XView as View,
  XAnimated,
  xCreateAnimation,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './marqueeHorizontalC2xStyles.module.scss';

const { getPixel } = BbkUtils;

export type Props = {
  textList: string;
  width: number;
  height: number;
  onMarqueeChange: () => void;
  fonSize26?: boolean;
};

type State = {
  animation: any;
  textList: string;
  textLength: number;
  textWidth: number;
  viewWidth: number;
  animating: boolean;
};
const styles = StyleSheet.create({ flexRow: { flexDirection: 'row' } });

class MarqueeHorizontal extends React.Component<Props, State> {
  animatedTransformX = 0;

  constructor(props: Props) {
    super(props);
    this.state = {
      animation: null,
      textList: props.textList,
      textLength: props.textList.length,
      textWidth: 0,
      viewWidth: 0,
      animating: false,
    };
    this.handleMarqueeChange = this.handleMarqueeChange.bind(this);
  }

  componentDidUpdate() {
    const { textWidth, viewWidth, textLength, animating } = this.state;
    const { width } = this.props;
    const fontWidth = getPixel(24);
    const oneLineNumber = Math.ceil(width / fontWidth);
    let tovalue = 0;
    let mDuration = 0;
    if (textLength < 2 * oneLineNumber) {
      tovalue = (textLength + 2) * fontWidth;
    } else {
      tovalue = 2 * oneLineNumber * fontWidth;
    }
    if (Number(JSON.stringify(this.animatedTransformX)) === 0) {
      mDuration = tovalue * 15;
    } else {
      mDuration = (tovalue + width) * 15;
    }
    if (!animating && textWidth && viewWidth) {
      this.handleMarqueeChange();
      const animation = xCreateAnimation({
        duration: mDuration,
        delay: Number(JSON.stringify(this.animatedTransformX)) ? 0 : 1500,
        timingFunction: 'linear',
      });
      animation.translateX(-tovalue).step();
      animation.translateX(width).step({ duration: 0, delay: 0 });
      this.setState(
        {
          animation: animation?.export(),
          animating: true,
        },
        () => {
          this.animatedTransformX = width;
        },
      );
    }
  }

  handleMarqueeChange() {
    this.props.onMarqueeChange();
  }

  onTransitionEnd = () => {
    this.setState({ animating: false });
  };

  textOnLayout = (e: { nativeEvent: { layout: { width: number } } }) => {
    const { width } = e.nativeEvent.layout;
    this.setState({
      textWidth: width,
    });
  };

  viewOnLayout = (e: { nativeEvent: { layout: { width: number } } }) => {
    const { width } = e.nativeEvent.layout;
    this.setState({
      viewWidth: width,
    });
  };

  textLengthView(text: string) {
    return (
      <View className={c2xStyles.textMeasuringViewStyle}>
        <Text
          onLayout={this.textOnLayout}
          numberOfLines={1}
          className={c2xStyles.textStyle}
        >
          {text}
        </Text>
      </View>
    );
  }

  render() {
    const { textList, textWidth, animation } = this.state;
    const { height, fonSize26 } = this.props;
    return (
      <BbkTouchable debounce={true}>
        <XAnimated.View
          style={{
            ...styles.flexRow,
            width: 2 * textWidth,
            height,
          }}
          animation={animation}
          onTransitionEnd={this.onTransitionEnd}
          onLayout={this.viewOnLayout}
        >
          <View className={classNames(c2xStyles.flexRow, c2xStyles.textStyle)}>
            <Text
              numberOfLines={1}
              className={classNames(
                c2xStyles.textStyle,
                fonSize26 && c2xStyles.f26,
              )}
            >
              {textList}
            </Text>
          </View>
        </XAnimated.View>
        {this.textLengthView(textList)}
      </BbkTouchable>
    );
  }
}

export default MarqueeHorizontal;
