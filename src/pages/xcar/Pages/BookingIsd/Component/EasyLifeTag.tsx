import Image from '@c2x/components/Image';
import { xMergeStyles } from '@ctrip/xtaro';
import React, { memo, CSSProperties } from 'react';

import { BbkStyleUtil } from '@ctrip/rn_com_car/dist/src/Utils';
import { ImageUrl } from '../../../Constants/Index';
import { GetAB } from '../../../Util/Index';

interface IEasyLifeTag {
  imageStyle?: CSSProperties;
}

// 无忧租标签
const EasyLifeTag: React.FC<IEasyLifeTag> = memo(({ imageStyle }) => {
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  return (
    <Image
      src={
        isISDInterestPoints
          ? `${ImageUrl.DIMG04_PATH}1tg4212000cxv1gzu736C.png`
          : `${ImageUrl.BBK_IMAGE_PATH}easy_life_label_new.png`
      }
      mode="aspectFit"
      style={xMergeStyles(
        isISDInterestPoints
          ? BbkStyleUtil.getWHAndMR(82, 34, 8)
          : BbkStyleUtil.getWHAndMR(85, 32, 8),
        imageStyle,
      )}
    />
  );
});
export default EasyLifeTag;
