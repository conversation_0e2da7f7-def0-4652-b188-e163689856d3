import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { color, icon, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './vehicleC2xStyles.module.scss';
import { Utils, CarLog } from '../../../Util/Index';
import { ImageUrl } from '../../../Constants/Index';
import OptimizationStrengthen from '../../../ComponentBusiness/OptimizationStrengthen';
import NationalChainTag from '../../../Components/Business/NationalChainTag';
import SkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import LicensePlate, {
  PlateBgSize,
} from '../../../ComponentBusiness/CarVehicleName/src/LicensePlate';
import DefaultCarImg from '../../../Constants/defaultCarImg';

const { getPixel, getProcImageUrl, ProcImageParamsType, useMemoizedFn, isIos } =
  BbkUtils;
const [followMaxByteLength, largeFontMaxByteLength] = [26, 20];
const styles = StyleSheet.create({
  vehicleNameWrap: {
    flex: 1,
  },
  vehicleName: {
    ...font.title4BoldStyle,
    color: color.recommendProposeBg,
    marginRight: getPixel(8),
  },
  vehicleNameMini: {
    ...font.title3BoldStyle,
  },
  lessImg: {
    width: getPixel(125),
    height: getPixel(28),
    marginTop: isIos ? -getPixel(2) : 0,
  },
  optimizationStrengthenImgStyle: {
    width: getPixel(136),
    height: getPixel(34),
  },
  optimizationStrengthenDotStyle: {
    width: getPixel(3),
    height: getPixel(3),
    borderRadius: getPixel(3),
    marginLeft: getPixel(8),
    marginRight: getPixel(8),
  },
  optimizationStrengthenVendorName: {
    ...font.body3LightStyle,
    color: color.recommendProposeBg,
  },
  optimizationStrengthenArrowRightStyle: {
    fontSize: getPixel(26),
    color: color.linearGradientBlackDiamondStart,
  },
  loadingBg: {
    backgroundColor: color.white,
    marginBottom: getPixel(11),
  },
});

interface IVehicle {
  imageUrl: string;
  vehicleName: string;
  vendorName: string;
  isOptimize: boolean;
  nationalChainTagTitle: string;
  licenseTag: string;
  licenseType: string;
  isNewEnergy: boolean;
  onPress: () => void;
  isShowLoading?: boolean;
  isSecretBox?: boolean;
  vehicleCode?: string;
}

const Vehicle: React.FC<IVehicle> = memo(
  ({
    imageUrl,
    vehicleName,
    vendorName,
    isOptimize,
    nationalChainTagTitle,
    licenseTag,
    licenseType,
    isNewEnergy = false,
    onPress = Utils.noop,
    isShowLoading,
    isSecretBox,
    vehicleCode,
  }) => {
    const imgUrl = getProcImageUrl(
      imageUrl,
      isSecretBox
        ? ProcImageParamsType.bookingSecretBox
        : ProcImageParamsType.bookingTop,
    );
    const imageLoadError = useMemoizedFn(error => {
      CarLog.LogImageLoadFail({
        error,
        imageUrl: imgUrl,
        expPoint: isSecretBox
          ? ProcImageParamsType.bookingSecretBox
          : ProcImageParamsType.bookingTop,
        vehicleCode,
      });
    });
    if (!vehicleName) return null;
    if (isShowLoading) {
      return (
        <SkeletonLoading
          visible={true}
          style={styles.loadingBg}
          pageName={PageType.BookingVehicle}
        />
      );
    }

    const isExceedsTitleMaxLength =
      Utils.getByteLength(vehicleName) > largeFontMaxByteLength;

    return (
      <BbkTouchable
        onPress={onPress}
        testID="booking_vehicle_info"
        className={isSecretBox ? c2xStyles.secretBoxWrap : c2xStyles.wrap}
      >
        <Image
          onError={imageLoadError}
          src={imageUrl ? imgUrl : DefaultCarImg}
          mode="aspectFill"
          className={isSecretBox ? c2xStyles.secretBoxImage : c2xStyles.image}
        />

        <View className={c2xStyles.rightWrap}>
          <View className={c2xStyles.rightTopWrap}>
            {!!vehicleName && (
              <View
                className={
                  Utils.getByteLength(vehicleName) > followMaxByteLength &&
                  c2xStyles.vehicleNameWrap
                }
              >
                <Text
                  numberOfLines={2}
                  className={c2xStyles.vehicleName}
                  style={isExceedsTitleMaxLength && styles.vehicleNameMini}
                >
                  {vehicleName}
                </Text>
              </View>
            )}
            {!!licenseTag && (
              <LicensePlate
                title={licenseTag}
                size={PlateBgSize.large}
                licenseType={licenseType}
                plateTextStyle={
                  isExceedsTitleMaxLength
                    ? styles.vehicleNameMini
                    : styles.vehicleName
                }
                isSecretBox={isSecretBox}
              />
            )}
            <View className={c2xStyles.lessGap} />
            {isNewEnergy && (
              <Image
                src={`${ImageUrl.DIMG04_PATH}1tg5f12000d4t4wzw8FFD.png`}
                mode="aspectFit"
                style={styles.lessImg}
              />
            )}
          </View>
          <View className={c2xStyles.rightBottomWrap}>
            {isOptimize ? (
              <OptimizationStrengthen
                vendorName={vendorName}
                imgStyle={styles.optimizationStrengthenImgStyle}
                vendorNameStyle={styles.optimizationStrengthenVendorName}
                arrowRightStyle={styles.optimizationStrengthenArrowRightStyle}
                isShowRightArrow={false}
                fromPage="book"
              />
            ) : (
              <Text className={c2xStyles.vendorName}>{vendorName}</Text>
            )}
            {!!nationalChainTagTitle && (
              <NationalChainTag tagTitle={nationalChainTagTitle} />
            )}
            <Text type="icon" className={c2xStyles.arrowRight}>
              {icon.arrowRight}
            </Text>
          </View>
        </View>
      </BbkTouchable>
    );
  },
);
export default Vehicle;
