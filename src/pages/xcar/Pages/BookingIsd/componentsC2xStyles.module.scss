@import '../../Common/src/Tokens/tokens/color.scss';

.wrap {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  z-index: -1;
}
.whiteWrap {
  background-color: $white;
}
.wrapper {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  z-index: -1;
}
.police {
  min-height: 32px;
  padding-left: 24px;
  padding-right: 24px;
}
.memberTipLabel {
  width: 170px;
  height: 32px;
  margin-right: 2px;
}
.tipLabel {
  width: 84px;
  height: 32px;
  margin-right: 2px;
}
.maxTipText {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $marketCouponPriceColor;
}
.formWrapperNew {
  background-color: $bookingOptimizationGrayBg;
  margin-top: 24px;
}
.formWrapperShelves2 {
  margin-top: -32px;
}
.formWrapper {
  background-color: $grayBg;
  padding-top: 20px;
}
.headerSlideToolTouchable {
  z-index: 5;
  position: absolute;
  top: 0px;
  right: 10px;
  bottom: 0px;
  width: 80px;
  height: 80px;
}
