import Image from '@c2x/components/Image';
import {
  xClassNames as classNames,
  xMergeStyles,
  XView as View,
  XLinearGradient as LinearGradient,
  XBoxShadow,
  XViewExposure,
  xClassNames,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import Platform from '@c2x/apis/Platform';
import React, { memo } from 'react';

import BbkHeader, {
  TextColorType,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';

import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { BbkUtils, BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  space,
  color,
  layout,
  font,
  setOpacity,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import CancellationBox from '../../ComponentBusiness/CancellationBox';
import { HeaderSideToolBox } from '../../ComponentBusiness/CarHeader';
import Block from '../../ComponentBusiness/DetailsBlock';
import PreferentialBox from '../../ComponentBusiness/PreferentialBox';
import AdvantageBox from '../../ComponentBusiness/AdvantageBox';
import Discount from '../../ComponentBusiness/DiscountBlock';
import InvoiceInfoNew from './Component/InvoiceInfo';
import ListNoMatch from '../../ComponentBusiness/ListNoMatch';
import { ImgType } from '../../ComponentBusiness/ListNoMatch/src/NoMatchImg';
import VehicleLocationContainer from '../../Containers/VehicleLocationContainer';
import Channel from '../../Util/Channel';
import { Utils, CarLog } from '../../Util/Index';
import { UITestID, CommonEnums } from '../../Constants/Index';
import texts from './Texts';
import Constants from '../../ComponentBusiness/Common/src/Constants';
import StorePolicyButton from '../../Containers/BookingStorePolicyButtonContainer';
import c2xStyles from './componentsC2xStyles.module.scss';

const { MemberStyle } = Constants;

const {
  getPixel,
  fixOffsetTop,
  vh,
  isAndroid,
  autoProtocol,
  isIos,
  isHarmony,
} = BbkUtils;
const styles = StyleSheet.create({
  bookingOptimizationTitle: { ...font.title4MediumStyle },
  titleStyle: { color: color.recommendProposeBg },
  header: Platform.select({
    web: { position: 'absolute', top: 0, left: 0, right: 0, zIndex: 1 },
    ios: { backgroundColor: color.white },
    android: { backgroundColor: color.white }, // @ts-ignore
    harmony: { backgroundColor: color.white },
  }),
  headIconWrap: { top: getPixel(isIos ? 8 : 20) },
  headContentWrap: { alignItems: 'center' },
  bookingWrapper: Platform.select({
    web: { height: vh(100) },
    ios: {},
    android: {}, // @ts-ignore
    harmony: {},
  }),
  vhiecleOut: {
    marginTop: getPixel(230 - fixOffsetTop()),
    backgroundColor: color.white,
  },
  vhiecleWrapper: {
    marginLeft: getPixel(32),
    marginRight: getPixel(32),
    borderRadius: getPixel(16),
    marginTop: getPixel(-230 + fixOffsetTop() + 24),
  },
  headerSlideToolBox: { marginTop: getPixel(7) },
  headerSlideTool: {
    zIndex: 5,
    position: 'absolute',
    top: -getPixel(2),
    right: getPixel(32),
    bottom: 0,
  },
  noMatch: {
    paddingTop: fixOffsetTop(BbkConstants.DEFAULT_HEADER_HEIGHT + 55),
    backgroundColor: color.white,
    flex: 1,
    justifyContent: 'flex-start',
  },
  tip: {
    marginTop: getPixel(-6),
    marginLeft: getPixel(14),
    paddingRight: getPixel(8),
    borderRadius: getPixel(4),
    height: getPixel(32),
    backgroundColor: setOpacity(color.bookingCouponLabelBg, 0.2),
  },
  bookingOptimizationPreferentialTitle: {
    ...font.title4BoldStyle,
    color: color.recommendProposeBg,
  },
});
interface VehicleLocationProps {
  onLocationPress: () => void;
  isRefactor: boolean;
  theme: any;
}
export const VehicleLocation = memo(
  withTheme(({ theme, onLocationPress, isRefactor }: VehicleLocationProps) => (
    <XBoxShadow
      coordinate={{ x: 0, y: getPixel(6) }}
      color="rgba(0, 0, 0, 0.2)"
      opacity={1}
      blurRadius={getPixel(16)}
      elevation={5}
      testID={UITestID.car_testid_page_booking_vehicleLocation}
      style={xMergeStyles([
        styles.vhiecleWrapper,
        { backgroundColor: theme.backgroundColor },
      ])}
    >
      <VehicleLocationContainer
        // @ts-ignore
        onLocationPress={onLocationPress}
        isRefactor={isRefactor}
      />
    </XBoxShadow>
  )),
);

export const NoMatch = memo(
  withTheme(({ onPressLeft, operateButtonPress }) => (
    <View
      style={xMergeStyles([
        layout.flex1,
        { position: 'relative', backgroundColor: color.white },
      ])}
    >
      {/* eslint-disable-next-line @typescript-eslint/no-use-before-define */}
      <Header opacity={1} onPressLeft={onPressLeft} />
      <ListNoMatch
        type={ImgType.No_Network}
        title="系统繁忙"
        subTitle=""
        isShowRentalDate={false}
        isShowOperateButton={true}
        operateButtonText="重试"
        buttonTestId={UITestID.car_testid_page_booking_retry}
        operateButtonPress={operateButtonPress}
        style={styles.noMatch}
      />
    </View>
  )),
);

const getBackGround = () => {
  return Utils.isCtripIsd() ? (
    <View
      className={classNames(c2xStyles.wrap, c2xStyles.whiteWrap)}
      style={{ height: BbkUtils.vh(60) }}
    />
  ) : (
    <LinearGradient
      start={{ x: 0.0, y: 0.5 }}
      end={{ x: 1.0, y: 0.5 }}
      locations={[0, 1]}
      colors={[color.linearGradientBlueLight, color.linearGradientBlueDark]}
      className={c2xStyles.wrapper}
      style={{ height: BbkUtils.vh(50) }}
    />
  );
};

export const Header = memo(
  withTheme(
    ({ opacity, toolBoxCustomerJumpUrl, onPressLeft, onPressRight }) => (
      <>
        <BbkHeader
          title="订单填写"
          titleStyle={xMergeStyles([
            Utils.isCtripIsd() && styles.bookingOptimizationTitle,
            Utils.isCtripIsd() && styles.titleStyle,
          ])}
          textColorType={
            opacity > 0.5 ? TextColorType.dark : TextColorType.light
          }
          leftIconColor={Utils.isCtripIsd() && color.recommendProposeBg}
          isBottomBorder={opacity === 1}
          style={styles.header}
          backgroundOpacity={Utils.isCtripIsd() ? 1 : opacity}
          onPressLeft={onPressLeft}
          leftIconTestID={UITestID.car_testid_page_booking_header_lefticon}
          sideLeftStyle={styles.headIconWrap}
          contentStyle={styles.headContentWrap}
          renderRight={
            onPressRight ? (
              <Touchable
                onPress={onPressRight}
                testID={UITestID.car_testid_page_booking_header_right}
                className={isAndroid && c2xStyles.headerSlideToolTouchable}
              >
                {Utils.isCtripIsd() ? (
                  <StorePolicyButton onPress={onPressRight} />
                ) : (
                  <HeaderSideToolBox
                    pageId={Channel.getPageId().Book.ID}
                    styleType={
                      opacity > 0.5
                        ? CommonEnums.ToolBoxStyleType.blackAndMore
                        : CommonEnums.ToolBoxStyleType.whiteAndMore
                    }
                    style={
                      isAndroid
                        ? styles.headerSlideToolBox
                        : styles.headerSlideTool
                    }
                    bizType={Utils.getSideToolBizType()}
                    jumpUrl={toolBoxCustomerJumpUrl}
                  />
                )}
              </Touchable>
            ) : (
              <HeaderSideToolBox
                pageId={Channel.getPageId().Book.ID}
                styleType={
                  opacity > 0.5
                    ? CommonEnums.ToolBoxStyleType.blackAndMore
                    : CommonEnums.ToolBoxStyleType.whiteAndMore
                }
                style={
                  isAndroid ? styles.headerSlideToolBox : styles.headerSlideTool
                }
                bizType={Utils.getSideToolBizType()}
                jumpUrl={toolBoxCustomerJumpUrl}
              />
            )
          }
        />
      </>
    ),
  ),
);

export const Coupon = memo(
  withTheme(({ theme, isLogin, onPressCoupon, couponList, currency }) => {
    if (!couponList) {
      return null;
    }
    let discountItem = null;
    const { selectedCoupon = {}, title } = couponList;
    const { couponName, deductionAmount } = selectedCoupon;
    if (couponName) {
      discountItem = {
        title: couponName,
        // label: , TODO 限制供应商的券
        currency,
        price: deductionAmount,
      };
    }
    return (
      <Discount
        isLogin={isLogin}
        isActivity={false}
        discountItem={discountItem}
        disableText={!discountItem && title}
        onPressCoupon={onPressCoupon}
        style={{
          backgroundColor: theme.backgroundColor,
          marginTop: space.verticalXL,
        }}
      />
    );
  }),
);

export const Activity = memo(
  withTheme(({ theme, isLogin, activityDetail, currency, onPressActivity }) => {
    if (!isLogin || !activityDetail) {
      return null;
    }
    const { title, promotion = {} } = activityDetail;
    const {
      couponName,
      longTag,
      title: titleInner,
      couponDesc,
      longDesc,
      deductionAmount,
    } = promotion;
    let discountItem = null;
    if (couponName || longTag || titleInner) {
      discountItem = {
        title: couponName || longTag || titleInner,
        desc: couponDesc || longDesc,
        currency,
        price: deductionAmount,
      };
    }
    return (
      <Discount
        isLogin={isLogin}
        isActivity={true}
        discountItem={discountItem}
        disableText={!discountItem && title}
        onPressActivity={onPressActivity}
        style={{
          backgroundColor: theme.backgroundColor,
          marginTop: space.verticalXL,
        }}
      />
    );
  }),
);

export const Cancellation = memo(
  withTheme(({ theme, cancelRuleInfo, testID }) => {
    if (
      !Utils.isCtripIsd() &&
      (!cancelRuleInfo || !cancelRuleInfo.description)
    ) {
      return null;
    }
    return (
      <Block
        title="取消政策"
        style={{
          backgroundColor: theme.backgroundColor,
          marginTop: space.verticalXL,
          paddingBottom: getPixel(8),
        }}
        titleStyle={{ paddingBottom: getPixel(32) }}
        isTitleRight={false}
        subTitle={!Utils.isCtripIsd() && cancelRuleInfo.description}
        numberOfLines={10}
        testID={testID}
      >
        <CancellationBox cancelRuleInfo={cancelRuleInfo} theme={theme} />
      </Block>
    );
  }),
);

export const InvoiceInfo = memo(
  withTheme(({ theme, invoiceInfo, testID }) => {
    if (!invoiceInfo || !Object.keys(invoiceInfo).length) return null;
    if (Utils.isCtripIsd()) {
      return <InvoiceInfoNew invoiceInfo={invoiceInfo} testID={testID} />;
    }
    return (
      <Block
        title={invoiceInfo.title}
        style={{
          backgroundColor: theme.backgroundColor,
          marginTop: space.verticalXL,
          paddingBottom: getPixel(8),
        }}
        titleStyle={{ paddingBottom: getPixel(32) }}
        isTitleRight={false}
        subTitle={invoiceInfo.description}
        numberOfLines={10}
        testID={testID}
      />
    );
  }),
);

export const Advantage = memo(
  withTheme(
    ({ theme, positivePolicies, cancelRuleInfo, confirmInfo, testID }) => {
      const bg = theme && theme.bbkDatePickerTabBackgroundColor;
      return (
        <View
          className={c2xStyles.police}
          style={{ backgroundColor: bg }}
          testID={testID}
        >
          <AdvantageBox
            data={positivePolicies}
            firstLineLeft={
              confirmInfo &&
              confirmInfo.isInstantConfirm &&
              confirmInfo.subTitle
            }
            firstLineRight={cancelRuleInfo && cancelRuleInfo.subTitle}
            firstLineRightEnable={cancelRuleInfo && cancelRuleInfo.showFree}
          />
        </View>
      );
    },
  ),
);

export const BookingWrapper = withTheme(({ children, theme }) => {
  let bgGray = theme && theme.bbkDatePickerTabBackgroundColor;
  if (!bgGray) {
    bgGray = color.grayBg;
  }
  return (
    <View
      testID={UITestID.car_testid_page_booking}
      style={xMergeStyles([
        layout.flex1,
        styles.bookingWrapper,
        { position: 'relative' },
        !isHarmony && { backgroundColor: color.C_EDF2F8 },
      ])}
    >
      {children}
    </View>
  );
});

export const VehicleWrapper = withTheme(({ children, theme }) => {
  let bgGray = theme && theme.bbkDatePickerTabBackgroundColor;
  if (!bgGray) {
    bgGray = color.grayBg;
  }
  const bgWhite = theme.backgroundColor || color.white;
  return (
    <View
      style={xMergeStyles([styles.vhiecleOut, { backgroundColor: bgWhite }])}
    >
      {children}
    </View>
  );
});

export const renderPreferentialTip = (
  preferentialTips,
  membershipPerception,
) => {
  const { description } = preferentialTips || {};
  const { curLevelCode } = membershipPerception || {};
  if (!description) return null;
  const { booking } = MemberStyle[curLevelCode] || {};
  return (
    <XViewExposure
      testID={CarLog.LogExposure({
        name: '曝光_填写页_优惠模块',

        info: {
          memberLevel: curLevelCode,
        },
      })}
      style={xMergeStyles([
        layout.flexRow,
        styles.tip,
        booking && {
          backgroundColor: booking.couponLabelbg,
          paddingRight: getPixel(6),
        },
      ])}
    >
      <Image
        src={
          booking?.couponLableImg ||
          autoProtocol(
            '//pages.c-ctrip.com/rncarapp/ctrip/app/newCouponLabel.png',
          )
        }
        className={booking ? c2xStyles.memberTipLabel : c2xStyles.tipLabel}
      />

      <BbkText
        className={c2xStyles.maxTipText}
        style={
          booking && {
            color: booking.couponLabelTxt,
          }
        }
      >
        {description}
      </BbkText>
    </XViewExposure>
  );
};

export const Preferential = memo(
  withTheme(
    ({
      theme,
      couponTestID,
      activityTestID,
      isLogin,
      couponList,
      activityDetail,
      currency,
      onPressCoupon,
      onPressActivity,
      preferentialTips,
      membershipPerception,
    }) => (
      <Block
        style={{
          backgroundColor: theme.backgroundColor,
          marginTop: space.verticalXXL,
          marginLeft: Utils.isCtripIsd() ? getPixel(24) : space.spaceL,
          marginRight: Utils.isCtripIsd() ? getPixel(24) : space.spaceL,
          paddingTop: getPixel(Utils.isCtripIsd() ? 32 : 40),
          borderRadius: getPixel(16),
        }}
        titleStyle={{ paddingBottom: 0 }}
        renderTitle={
          <View style={layout.betweenHorizontal}>
            <BbkText
              fontWeight="medium"
              style={xMergeStyles([
                font.title1MediumFlatStyle,
                Utils.isCtripIsd() &&
                  styles.bookingOptimizationPreferentialTitle,
              ])}
            >
              {texts.preferentialTitle}
            </BbkText>
            {renderPreferentialTip(preferentialTips, membershipPerception)}
          </View>
        }
      >
        <PreferentialBox
          couponTestID={couponTestID}
          activityTestID={activityTestID}
          isLogin={isLogin}
          couponList={couponList}
          activityDetail={activityDetail}
          currency={currency}
          onPressCoupon={onPressCoupon}
          onPressActivity={onPressActivity}
        />
      </Block>
    ),
  ),
);

export const FormWrapper = withTheme(({ children, theme, ...props }) => (
  <View
    className={xClassNames(
      Utils.isCtripIsd() ? c2xStyles.formWrapperNew : c2xStyles.formWrapper,
      props.isShelves2 && c2xStyles.formWrapperShelves2,
    )}
    {...props}
  >
    {children}
  </View>
));

export default VehicleLocation;
