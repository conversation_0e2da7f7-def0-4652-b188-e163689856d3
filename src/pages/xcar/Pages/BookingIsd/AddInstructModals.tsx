import React, { CSSProperties } from 'react';

import BookingHalfPageModal from './BookingHalfPageModal';
import { AddInstructDataType } from '../../Types/Dto/QConfigResponseType';

interface PageModalProps {
  visible: boolean;
  onMaskPress: () => void;
  style: CSSProperties;
}
interface IAddModal {
  addInstructData: AddInstructDataType;
  pageModalProps: PageModalProps;
}

const AddInstructModal: React.FC<IAddModal> = ({
  pageModalProps,
  addInstructData,
}) => {
  const contentProps = {
    ...pageModalProps,
    title: addInstructData?.title || '',
    content: [addInstructData?.content || ''],
  };
  return <BookingHalfPageModal {...contentProps} />;
};

export default AddInstructModal;
