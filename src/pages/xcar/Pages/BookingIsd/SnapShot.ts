import Platform from '@c2x/apis/Platform';
import ScreenShot from '@c2x/apis/ScreenShot';
import Zip from '@c2x/apis/Zip';

import SnapshotData, {
  snapshotDataKeyList,
} from '../../Global/Cache/SnapshotData';

const getSnapShotImage = (node, key) =>
  new Promise(resolve => {
    if (!node) resolve({ res: '', key });
    try {
      ScreenShot.captureComponentShot(node, result => {
        if (!!result && !!result.imageData) {
          let imgbase64 = `data:image/png;base64,${result.imageData}`;
          if (Platform.OS === 'ios') {
            imgbase64 = imgbase64.replace(/\\r\\n/g, '');
            Zip.gzip(imgbase64, res => {
              resolve({ res, key });
            });
          } else {
            imgbase64 = imgbase64.replace(/\\n/g, '');
            resolve({ res: imgbase64, key });
          }
        } else {
          resolve({ res: '', key });
        }
      });
    } catch (e) {
      resolve({ res: '', key });
    }
  });

export const getSnapImageData = async ({
  materialModalRef,
  priceDetailsRef,
  insuranceSuitsModalRef,
}: {
  materialModalRef: any;
  priceDetailsRef: any;
  insuranceSuitsModalRef: any;
}) => {
  const SnapshotPromises = [];
  const detailSnapNode = SnapshotData.getData(
    snapshotDataKeyList.detailSnapshotData,
  );
  if (detailSnapNode) {
    SnapshotPromises.push(getSnapShotImage(detailSnapNode, 'detailSnap'));
  }
  if (materialModalRef && materialModalRef.current) {
    SnapshotPromises.push(
      getSnapShotImage(materialModalRef.current, 'materialSnap'),
    );
  }
  if (priceDetailsRef && priceDetailsRef.current) {
    SnapshotPromises.push(
      getSnapShotImage(priceDetailsRef.current, 'priceDetailSnap'),
    );
  }
  if (insuranceSuitsModalRef && insuranceSuitsModalRef.current) {
    SnapshotPromises.push(
      getSnapShotImage(insuranceSuitsModalRef.current, 'insuranceSuitsSnap'),
    );
  }

  const snaps = await Promise.all(SnapshotPromises);
  return snaps;
};

export const getBookingSnapImageData = async (mainScrollViewRef: any) => {
  if (mainScrollViewRef && mainScrollViewRef.current) {
    return getSnapShotImage(mainScrollViewRef.current, 'bookingSnap');
  }
  return null;
};

export const getSnapShotData = ({
  orderId,
  snaps,
}: {
  orderId: number;
  snaps: any[];
}) =>
  snaps.map(item => ({
    orderId,
    type: 'image',
    pageName: item.key,
    message: item.res,
    version: 'v4',
  }));
