/* eslint-disable max-lines */
import {
  get as lodashGet,
  isNil as lodashIsNil,
  merge as lodashMerge,
  throttle as lodashThrottle,
  forEach as lodashForEach,
  sortBy as lodashSortBy,
} from 'lodash-es';
// cSpell:ignore mapIsuranceBox, FuntionTypes, PRICECHANGE, SALEOUT, POPTOLIST, authen, Cotent, rtime, vehicel, Dropoff, IDTYPE, MUSTREAD, vehicledata, vehiclecode, rsid, vpid, vendorid, csname, Headerprops
import StyleSheet from '@c2x/apis/StyleSheet';
import Dimensions from '@c2x/apis/Dimensions';
import ScrollView from '@c2x/components/ScrollView';
import Alert from '@c2x/apis/Alert';
import Platform from '@c2x/apis/Platform';
import Image from '@c2x/components/Image';
import ViewPort from '@c2x/components/ViewPort';
import { IBasePageProps } from '@c2x/components/Page';
import Bridge from '@c2x/apis/Bridge';
import React from 'react';
import {
  XView as View,
  xRouter,
  xMergeStyles,
  xClassNames as classNames,
  xCreateAnimation,
  xGetCurrentInstance,
} from '@ctrip/xtaro';
import { toRMB } from '@ctrip/rn_com_car/dist/src/Shark/src/Index';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import memoize from 'memoize-one';
import { BbkUtils, BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';
import { ReferenceType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens'; // bbk
import * as ImageUrl from '../../Constants/ImageUrl'; /* eslint-disable complexity */
import c2xStyles from './product.module.scss';
import CarDialog from '../../ComponentBusiness/CarDialog';
import BbkSkeletonLoading, {
  PageType,
} from '../../ComponentBusiness/SkeletonLoading';
import ProductNoMatch from '../../ComponentBusiness/ProductNoMatch';
import {
  WarningListResponseType,
  WarningDto,
  Enums,
} from '../../ComponentBusiness/Common/index';
import {
  PickupStoreInfoType,
  VendorTagType,
  PriceChangeInfoType,
} from '../../ComponentBusiness/Common/src/ServiceType/src/queryProductInfo';
import RestAssuredBanner from '../../ComponentBusiness/RestAssuredBanner';
// 组件
import ProductContent from './Components/ProductContent';
import ProductHeaderContainer from '../../Containers/ProductHeaderContainer';
import PriceChangePop from './Components/PriceChangePop';
import PriceChangeTips from './Components/PriceChangeTips';
import ProductModalsContainer from '../../Containers/ProductModalsContainer'; // State
import {
  getCurPayModeInfo,
  getCurPackageIsEasyLife,
} from '../../State/Product/Mappers';
import {
  mapIsuranceBox,
  getVehicleId,
  getBbkVehicleNameProps,
} from '../../State/Product/BbkMapper';
import { ProductReq } from '../../State/Product/FuntionTypes';
import {
  ISelectedIdType,
  ProductModalVisible,
  ProductModalVisibleType,
  SelectPackageData,
  ProductReducer,
  NoMatchButtonType,
  SoldOutKeyWithText,
  ImageListTypeLog,
  ImageListType,
} from './Types';
import { ALL_MODULE_INFO_MAP, GuideTabType } from '../../State/Product/Enums';
import { PriceAlertType } from '../../ComponentBusiness/Common/src/Enums';
import { PriceTimer } from '../../State/Product/Model';
import EasyLifeTagListModal from '../../Containers/ProductEasyLifeModalContainer';
import SesameContainer from '../../Containers/SesameContainer';
import PickupDownGradePop from '../../Containers/PickupDownGradePopContainer';
import { WarningInfo, UITestID } from '../../Constants/Index';
import CPage, { IStateType } from '../../Components/App/CPage';
import {
  Utils,
  CarLog,
  AppContext,
  User,
  GetAB,
  GetABCache,
  CarServerABTesting,
  CarFetch,
} from '../../Util/Index';
import Channel from '../../Util/Channel';
import Texts from './Texts';
import {
  getPickupStoreInfo,
  getProductResAppResponseMap,
  getProductReq,
  getWholeDayPrice,
  getPriceAlertProps,
  // getUserRealImageLength,
  getProductPageParam,
  getProductRequestReference,
  getHeaderCarImageUrl,
} from '../../Global/Cache/ProductSelectors';
import { ProductReqAndResData } from '../../Global/Cache/Index';
import SnapshotData, {
  snapshotDataKeyList,
} from '../../Global/Cache/SnapshotData';
import { setFromPage } from '../../Global/Cache/ListReqAndResData';
import LogKey from '../../Constants/LogKey';
import { ItemLabelCodeType } from '../../ComponentBusiness/PackageIncludes';
import * as homeConfig from '../Home/Logic/config';
import DepositRateDescriptionModal from '../../Containers/ProductDepositRateDescriptionModalContainer';
import BookBar from './Components/BookBar';
import { DetailRecommendInfo } from '../../Types/Dto/QueryProductInfoType';
import { initializeABProductPage } from '../../Util/CarABTesting/InitializeAB';
import InsuranceSellingModal from '../../Containers/ProductInsuranceSellingModalContainer';
import ProductRecommendTip from './Components/ProductRecommendTip';
import ProductHeaderImage from './Components/ProductHeaderImage';
import { RecommendType } from '../../Constants/ListEnum';
import { getCurrentModuleName } from '../../State/Product/Method';
import { AssistiveTouch } from '../../Components/Index';

const { ProductImageType } = Enums;
const baseTabSections = [
  Texts.carDetails,
  Texts.selectPackageTitle,
  Texts.pickupMaterialsTitle,
  Texts.storePolicyTitle,
];

const getTabSections = () => {
  const curTabSections = BbkUtils.cloneDeep(baseTabSections);
  return curTabSections;
};

const {
  getPixel,
  fixOffsetTop,
  getDayGap,
  isd_dhm: isdDhm,
  vh,
  adaptNoaNomalousBottom,
  fixIOSOffsetBottom,
} = BbkUtils;
const { DEFAULT_HEADER_HEIGHT } = BbkConstants;
const { width } = Dimensions.get('window');
const noop = () => {};
const noopEmpty = () => {};
const { PageIndexId } = WarningInfo;
const priceAlertRetryKey = {
  [PriceAlertType.PriceChange]: '点击_详情页_变价弹层_重新获取报价',
  [PriceAlertType.Error]: '点击_详情页_其他报错异常提示_重试',
  [PriceAlertType.PriceCacheError]: '点击_详情页_命中缓存失效_重试',
};

const priceAlertPopKey = {
  [PriceAlertType.SoldOut]: '点击_详情页_无库存弹层_返回列表页',
  [PriceAlertType.PriceCacheError]: '点击_详情页_命中缓存失效_返回列表页',
};

interface ProductStateType extends IStateType {
  tabSelectedId?: string;
  sectionLayout?: number[];
  authenStatusTicket?: string;
  modalVisible?: ProductModalVisible;
  insPackageLayout?: Array<any>;
  firstScreen?: boolean; // 触发二次渲染
  opacityAnimation?: any;
  fixOpacityAnimation?: any;
  packageTabOpacityAnimation?: any;
  showPricePop?: boolean;
  warningTipsModalCotent?: Array<WarningDto>;
  productHeaderLayoutHeight?: number;
  isProductHeaderFixed?: boolean;
  isShowPackageTabs?: boolean;
  showPriceChangeTips?: boolean;
  footerBarHeight?: any;
  selectPackageId?: number;
  curPackageId?: number;
  insuranceNotice?: string;
  materialAnchor?: string;
  payModeWrapPosition?: number;
  curBusinessTimePolicyType?: GuideTabType | null;
  bookBarHeight?: number;
  threeScreen?: any;
}

interface ProductPropsType extends IBasePageProps, ProductReducer {
  reference?: ReferenceType;
  ptime: string;
  rtime: string;
  pickUpLocationName: string;
  dropOffLocationName: string;
  authenStatusTicket?: string;
  pageParam?: any;
  easyLifePopVisible?: boolean;
  isCreditRent?: boolean;
  productReq?: ProductReq;
  selectedIdType?: ISelectedIdType;
  productRentalLocationInfo: any;
  priceChange?: boolean;
  priceTip?: string;
  materialAnchor?: string;
  headerCarImageUrl?: string;

  headerData?: any;
  currency?: string;
  bizType?: string;
  toolBoxCustomerJumpUrl?: string;
  isShowDropOff: boolean;
  isDifferentLocation: boolean;
  countryId?: number;
  isEasyLife?: boolean;
  curInsPackageId?: number;
  tripH5LocationDate?: any;
  expandLocationAndDate?: any;
  productWaringInfo?: WarningListResponseType;

  queryProduct: (param: any) => void;
  selectPackage: (data: SelectPackageData) => void;
  setCrossPlaces: (tempCrossSpaces: any[]) => void;
  fetchApiGuide: (params?: any) => void;
  setProductDropOffDate: (date?: string) => void;
  reset: () => void;
  initSesameAuthState: () => void;
  queryPriceInfo: () => void;
  addSaleOutList: (productKey: string | SoldOutKeyWithText) => void;
  setEasyLifePopVisible?: (data: { visible: boolean }) => void;
  showPriceConfirmAction?: (visible: boolean) => void;
  changeSelectInsurance: ({ insuranceId }) => void;
  refreshList: () => void;
  onErrorRefreshList: () => void;
  onErrorShowLocationAndDatePop: () => void;
  getListWarningInfo: (data) => void;
  curCtripInsurance: string[];
  priceUuid?: string;
  imageLogInfo?: Object;
  pickupStoreInfo?: PickupStoreInfoType;
  isPickPoint?: boolean;
  isRebook: boolean;
  restAssuredTag: VendorTagType;
  priceChangeInfo?: PriceChangeInfoType;
  validateIsDownGrade: (data) => void;
  needDownGrade: boolean;
  pageVehicleHeaderParam?: any;
  pageVendorParam?: any;
  fetchWarningInfoLoading: any;
  depositRateDescriptionModalVisible: boolean;
  closeDepositRateDescriptionModal: () => void;
  isShowTravelLimit?: boolean;
  emptyGuideInfo: DetailRecommendInfo;
  isKlb?: boolean;
  insuranceCompareModalVisible?: boolean;
  openInsuranceCompareModal?: () => void;
  closeInsuranceCompareModal?: () => void;
  insuranceSellingModalVisible?: boolean;
  openInsuranceSellingModal?: () => void;
  closeInsuranceSellingModal?: () => void;
  isOpenInsuranceSellingModal?: boolean;
  recommendType?: RecommendType;
  restPrevDataInfo?: (data: any) => void;
  restPrevLocationInfo?: (data: any) => void;
  morePackageLogBaseInfo?: any;
  isRefactor?: boolean;
  queryProductCallBack?: (data: any) => void;
  priceTimerRes?: any;
  traceInfo?: any;
  isDebugMode?: boolean;
  queryEquipmentInfo?: (params) => void;
  ipollLogData?: {
    queryVid?: string;
    vehicleCode?: string;
    pCityId?: string;
    rCityId?: string;
    pickupLocation?: string;
    returnLocation?: string;
    ptime?: string;
    rtime?: string;
  };
  fuelModalData?: {
    fuelNote?: string;
    fuelNoteTitle?: string;
  };
}
const styles = StyleSheet.create({
  page: {
    backgroundColor: color.grayBg,
    flex: 1,
    ...Platform.select({
      web: {
        height: vh(100),
      },
      ios: {},
      android: {},
      // @ts-ignore
      harmony: {},
    }),
  },
  scrollPage: {
    flex: 1,
    backgroundColor: color.transparent,
    position: 'relative',
    zIndex: 0,
  },
  loadingBg: {
    backgroundColor: color.transparent,
  },
  details: {
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
    borderBottomLeftRadius: getPixel(0),
    borderBottomRightRadius: getPixel(0),
  },
  noMatch: {
    paddingTop: fixOffsetTop(DEFAULT_HEADER_HEIGHT + 120),
    backgroundColor: color.white,
    flex: 1,
    justifyContent: 'flex-start',
  },
  newBg: {
    backgroundColor: color.C_EDF2F8,
  },
});

const logKey = {
  [ProductModalVisibleType.addServicesModalVisible]:
    '点击_详情页_航班延误_打开弹层',

  [ProductModalVisibleType.vehicelModalVisible]: '点击_详情页_车型详情',

  [ProductModalVisibleType.optimizeModalVisible]: '点击_详情页_携程优选',

  [ProductModalVisibleType.idTypeModalVisible]: '点击_详情页_更多证件',

  // crossPlaceModalVisible: ClickKey.C_PRODUCT_CROSS_PLACE_MODAL.KEY,
  [GuideTabType.Pickup]: '点击_详情页_取车指引',
  [GuideTabType.Dropoff]: '点击_详情页_还车指引',
  [Texts.details]: '点击_详情页_顶部bar_详情',
  // [tabSections[1]]: ClickKey.C_PRODUCT_TOP_BAR_COMMENT.KEY,
  [Texts.pickUpNeed]: '点击_详情页_顶部bar_取车材料',
  [Texts.insuranceTitle]: '点击_详情页_顶部bar_保险',
  [Texts.carRentalMustRead]: '点击_详情页_顶部bar_租车必读',
};

const getImageLogData = ({ index, videoUrl, imgList }) => {
  const type = videoUrl ? ProductImageType.Video : ProductImageType.Image;
  return {
    index,
    url: videoUrl || imgList?.[index]?.imageUrl,
    type,
  };
};

export default class Product extends CPage<ProductPropsType, ProductStateType> {
  scrollRef = null;

  headerRef = null;

  materialsLiveTime = 0;

  priceChangeDom = null;

  footerDom = null;

  authenStatusTicket = '';

  scrollThrottle = null;

  tabClick = false;

  // 国内向上比价
  comparePrice = true;

  // 轮询
  pricePool = false;

  // 用于填写页回来后刷新价格接口
  needRefresh = false;

  videoRef = null;

  scrollY = 0;

  // pv埋点是否已触发
  isPvSend = false;

  isLastAnchor = false;

  priceTimer = null;

  isShowMarketLabel = false;

  tabSections = [];

  setTimers: { [key: string]: any } = {};

  allModulePosition = {
    [ALL_MODULE_INFO_MAP.FIRST_SCREEN.key]: {
      ...ALL_MODULE_INFO_MAP.FIRST_SCREEN,
      pos: 0,
    },
  };

  ipollProductConfig = {};

  constructor(props) {
    super(props);
    // 页面跳转时，列表页会禁用侧滑，详情页需手动发送pv埋点
    this.needLogPage = true;
    // 由于列表页可能出现弹窗未关闭，且禁用侧滑情况
    this.enableDragBack();
    this.tabSections = getTabSections();
    this.state = {
      tabSelectedId: this.tabSections[0],
      sectionLayout: new Array(this.tabSections.length).fill(0),
      modalVisible: {
        [ProductModalVisibleType.vehicelModalVisible]: false,
        [ProductModalVisibleType.optimizeModalVisible]: false,
        [ProductModalVisibleType.addServicesModalVisible]: false,
        [ProductModalVisibleType.idTypeModalVisible]: false,
        [ProductModalVisibleType.xyzNoteVisible]: false,
        [ProductModalVisibleType.warningTipsVisible]: false,
        [ProductModalVisibleType.osdInsuranceVisible]: false,
        [ProductModalVisibleType.osdExcessIntroduceVisible]: false,
        [ProductModalVisibleType.insuranceNoticeMustReadVisible]: false,
        [ProductModalVisibleType.osdPriceDetailVisible]: false,
        [ProductModalVisibleType.materialModalVisible]: false,
        [ProductModalVisibleType.extrasInfoVisible]: false,
        [ProductModalVisibleType.morePackageIntroModalVisible]: false,
        [ProductModalVisibleType.businessTimePolicyModalVisible]: false,
      },
      insuranceNotice: '',
      insPackageLayout: [],
      firstScreen: true,
      opacityAnimation: 1,
      fixOpacityAnimation: 0,
      packageTabOpacityAnimation: null,
      showPricePop: true,
      warningTipsModalCotent: [],
      isProductHeaderFixed: false,
      isShowPackageTabs: false,
      showPriceChangeTips: false,
      selectPackageId: 0,
      curPackageId: 0,
      materialAnchor: '',
      payModeWrapPosition: 0,
      curBusinessTimePolicyType: null,
      bookBarHeight: 0,
    };
    this.authenStatusTicket = props.authenStatusTicket;
    this.setScrollThrottle();
    this.initListData();
    this.initMarketLabelStatus();
    initializeABProductPage();
    // 同步已缓存的AB实验结果
    GetABCache.syncCacheAb();
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().Product.ID;
  }

  getPageParamInfo() {
    const info: any = {};
    const propParams: any = this.props || {};
    const {
      reference = {},
      pageVehicleHeaderParam = {},
      pageVendorParam = {},
      traceInfo,
    } = propParams;
    const { isdParam } = reference || {};
    if (isdParam) {
      info.vehicledata = {
        vehiclecode: isdParam.vpid,
        vehicleName: isdParam.vehicleName,
        vendorCode: isdParam.vendorid,
        vendorName: isdParam.csname,
        storeCode: isdParam.psid,
        rStoreCode: isdParam.rsid,
        groupName: pageVehicleHeaderParam?.groupName,
        commentCnt: pageVendorParam?.commentInfo?.commentCount,
        commentScore: pageVendorParam?.commentInfo?.overallRating,
      };
    }
    if (traceInfo) {
      info.vehicledata = traceInfo;
    }
    return info;
  }

  getOsdHeaderImgTraceInfo(imageHeaderprops) {
    const propParams: any = this.props || {};
    const {
      reference = {},
      pageVehicleHeaderParam = {},
      pageVendorParam = {},
      isProductLoading,
    } = propParams;
    const { vendorCode, bizVendorCode, skuId, pStoreCode, rStoreCode } =
      reference || {};
    const { vendorName } = pageVendorParam || {};
    const { groupId, groupName, vehicleCode, vehicleName } =
      pageVehicleHeaderParam || {};
    const { imageList } = imageHeaderprops || {};
    let vehicleImageType = ImageListTypeLog.realPic;
    if ((!imageList?.length || !imageList?.[0].url) && !isProductLoading) {
      vehicleImageType = ImageListTypeLog.noCarPic;
    }
    return {
      vendorCode,
      vendorName,
      groupId,
      groupName,
      vehicleCode,
      vehicleName,
      vehicleImageType,
      skuId,
      pStoreId: pStoreCode,
      rStoreId: rStoreCode,
      vendorId: bizVendorCode,
    };
  }

  /* eslint-disable camelcase */
  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();
    const productPageParam = getProductPageParam();
    this.setTimers.lazyQueue = setTimeout(() => {
      if (!productPageParam?.isPreDispatchAtList) {
        this.queryProduct({
          reset: true,
        });
      }
      this.props.getListWarningInfo({ pageIndexId: PageIndexId.Product });
      this.getIpollConfig();
    }, 0);
  }

  componentDidMount() {
    super.componentDidMount();
    const productPageParam = getProductPageParam();
    if (productPageParam) {
      this.performanceMonitor.contentShowStart =
        this.performanceMonitor.firstRenderEnd;
      this.performanceMonitor.submitTTI?.();
    }
  }

  getIpollConfig = async () => {
    if (GetABCache.isOSDProductIpoll()) {
      const response = await CarFetch.getIpollConfig({ pageType: 5 }).catch(
        () => {},
      );
      if (response?.baseResponse?.isSuccess) {
        if (response?.allConfigs) {
          this.ipollProductConfig = response?.allConfigs?.find(
            // 出境详情页type 5
            item => item?.pageType === 5,
          );
        }
      }
    }
  };

  componentDidShow() {
    super.componentDidShow();
    const { navigation = {} } = xGetCurrentInstance().app;
    const routes = navigation.getCurrentRoutes?.() || [];
    if (routes.length > 0) {
      const currentRoute = routes[routes.length - 1] || {};
      this.logPage(currentRoute);
    }
  }

  /* eslint-disable camelcase */
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.isProductLoading && !nextProps.isProductLoading) {
      this.performanceMonitor.receiveResStart = new Date();
    }
  }

  componentDidUpdate(prevProps) {
    const {
      isProductLoading,
      isFail,
      authenStatusTicket,
      isPriceLoading,
      isPriceFail,
      isCreditRent,
      productReq,
      showPriceConfirmAction,
      showPriceConfirm,
      curCtripInsurance,
      priceChange,
      priceChangeInfo,
      queryEquipmentInfo,
    } = this.props;

    // 保险合规埋点
    if (prevProps.isProductLoading && !isProductLoading && !this.isPvSend) {
      this.isPvSend = true;
      CarLog.LogTrace({
        key: LogKey.c_car_trace_product_pv_10650041522,
        info: {
          pageId: this.getPageId(),
          insuranceId: curCtripInsurance,
        },
      });
    }

    if (prevProps.isProductLoading && !isProductLoading && !isFail) {
      this.onPageReady();
      this.setTimers.timer1 = setTimeout(() => {
        this.setState({
          firstScreen: false,
        });
      });
      this.pricePool = true;
    }

    if (!isProductLoading) {
      if (!isFail && !isPriceLoading && !isPriceFail) {
        // 接口目前已经不返回 wholeDayPrice 字段，前端会展示异常，暂时屏蔽该逻辑
        // if (this.comparePrice) {
        //   this.setTimers.timer3 = setTimeout(this.comparePriceAlert);
        //   this.comparePrice = false;
        // }

        if (this.pricePool) {
          // 每次加载成功后开始轮询
          if (this.priceTimer) {
            this.priceTimer.clearPriceTimer();
          }
          this.priceTimer = new PriceTimer();
          this.priceTimer.setPriceTimer(
            productReq,
            () => showPriceConfirmAction(true),
            this.retry,
            false,
            // 增加额外设备轮询，防止缓存失效
            params => Utils.isCtripOsd() && queryEquipmentInfo(params),
          );
          this.pricePool = false;
        }
      }
    }
    // 信用租详情页与填写页信息隔离，不需要刷新
    if (!isCreditRent && this.authenStatusTicket !== authenStatusTicket) {
      this.authenStatusTicket = authenStatusTicket;
      this.setTimers.timer4 = setTimeout(() => this.queryProduct(), 500);
    }

    if (!prevProps.showPriceConfirm && showPriceConfirm) {
      this.handlePopExposure();
    }

    if (!prevProps.priceChange && priceChange) {
      CarLog.LogTrace({
        key: LogKey.c_car_trace_product_price_exposure_10650041522,
        info: {
          pageId: this.getPageId(),
        },
      });
    }

    if (!prevProps.priceChangeInfo && priceChangeInfo) {
      const { priceDiff } = priceChangeInfo;
      if (priceDiff && priceDiff !== 0) {
        this.setTimers.timer5 = setTimeout(() => {
          this.setState({ showPriceChangeTips: true });
        });
      }
    }
  }

  componentWillUnmount() {
    const {
      reset,
      setProductDropOffDate,
      restPrevDataInfo,
      restPrevLocationInfo,
      recommendType,
    } = this.props;
    super.componentWillUnmount();
    ProductReqAndResData.removeData();
    reset();
    setProductDropOffDate();
    // 不在 pageDidDisAppear 中 clear,
    // 存在子页面的情况, 如: 费用明细等
    if (this.priceTimer) {
      this.priceTimer.clearPriceTimer();
    }
    if (CarServerABTesting.isRecommend()) {
      switch (recommendType) {
        case RecommendType.PDTime:
          restPrevDataInfo({ notDisPatchPreFetch: true });
          break;
        case RecommendType.DiffLocation:
        case RecommendType.DefaultArea:
          restPrevLocationInfo({ notDisPatchPreFetch: true });
          break;
        default:
          break;
      }
    }
    Object.values(this.setTimers).forEach(value => {
      clearTimeout(value);
    });
  }

  onPageReady = () => {
    this.performanceMonitor.responseMap = getProductResAppResponseMap();
    this.performanceMonitor.interactiveStart = new Date();
    this.performanceMonitor.submit();
    const productPageParam = getProductPageParam();
    if (!productPageParam) {
      this.performanceMonitor.contentShowStart =
        this.performanceMonitor.interactiveStart;
      this.performanceMonitor.submitTTI();
    }
  };

  pageDidAppear = () => {
    super.pageDidAppear();
    if (this.pageAppearCount > 1) {
      const { getListWarningInfo } = this.props;
      getListWarningInfo({ pageIndexId: PageIndexId.Product });
    }
  };

  pageDidDisappear() {
    super.pageDidDisappear();
  }

  onBackAndroid = () => {
    const {
      easyLifePopVisible,
      depositRateDescriptionModalVisible,
      closeDepositRateDescriptionModal,
      insuranceCompareModalVisible,
      closeInsuranceCompareModal,
      insuranceSellingModalVisible,
      closeInsuranceSellingModal,
    } = this.props;
    const { modalVisible } = this.state;
    const showModalKey = Object.keys(modalVisible).find(
      key => modalVisible[key],
    );
    if (showModalKey) {
      this.setModalVisible(showModalKey, false)();
    } else if (easyLifePopVisible) {
      this.props.setEasyLifePopVisible({ visible: false });
    } else if (this?.videoRef?.onBackAndroid) {
      // 视频回退兼容
      this.videoRef.onBackAndroid(this);
    } else if (depositRateDescriptionModalVisible) {
      closeDepositRateDescriptionModal();
    } else if (insuranceCompareModalVisible) {
      closeInsuranceCompareModal();
    } else if (insuranceSellingModalVisible) {
      closeInsuranceSellingModal();
    } else {
      this.pageGoBack();
    }
  };

  pageGoBack = () => {
    const isOSDProductBack = GetAB.isOSDProductBack();
    if (isOSDProductBack) {
      setTimeout(() => {
        const numbers = [3095, 3102];
        const randomIndex = Math.floor(Math.random() * numbers.length);
        Bridge.callNative('InstantSurvey', 'showTipView', {
          senceId: numbers[randomIndex],
        });
      }, 500);
    }
    this.pop();
  };

  videoRefCallBack = ref => {
    this.videoRef = ref;
  };

  initListData = () => {
    const { pageParam } = this.props;
    ProductReqAndResData.setData(
      ProductReqAndResData.keyList.productPageParam,
      pageParam,
    );
  };

  initMarketLabelStatus = () => {
    const { homeThemeConfig: homeTheme } = homeConfig;

    if (homeTheme) {
      const allTags = this.props.pageParam?.vendor?.allTags || [];
      const marketLabel = allTags.find(
        item => item.labelCode === ItemLabelCodeType.SUMMERSPECIAL,
      );
      if (marketLabel) {
        this.isShowMarketLabel = true;
      }
    }
  };

  queryProduct = (data?: any) => {
    const { reference, isRebookOsd, queryOsdModifyOrderNote } = this.props;
    this.scrollY = 0;
    const param = {
      reference,
      ...data,
    };
    this.props.queryProduct(param);
    // 境外修改订单提醒预请求
    if (isRebookOsd) {
      queryOsdModifyOrderNote();
    }
  };

  onLogin = async () => {
    const res = await User.toLogin();
    if (res) {
      this.queryProduct();
    }
  };

  getFormattedPriceWithCurrency = async price =>
    toRMB(AppContext.LanguageInfo.currency) + price;

  comparePriceAlert = async () => {
    const { ptime, rtime } = this.props;
    const { returnPointInfo = {} } = getProductReq();
    // actualTotalPrice 是优惠后的价格
    const { returnDate, actualTotalPrice } = getWholeDayPrice();
    const curPayModeInfo = getCurPayModeInfo();
    const oldPrice = lodashGet(curPayModeInfo, 'currenctPriceInfo.totalPrice');
    const newReturnDate = dayjs(returnDate).format('YYYY-MM-DD HH:mm:ss');
    const duration = getDayGap(newReturnDate, ptime);
    const durationHour = isdDhm(ptime, rtime);

    if (
      lodashIsNil(oldPrice) ||
      actualTotalPrice > oldPrice ||
      !dayjs(newReturnDate).isAfter(returnPointInfo.date)
    ) {
      return;
    }

    let msg = '';
    if (actualTotalPrice < oldPrice) {
      msg = `该供应商租期${
        duration
      }天的报价为${await this.getFormattedPriceWithCurrency(
        actualTotalPrice,
      )}，`;

      msg += `比${durationHour}便宜`;
      // eslint-disable-next-line max-len
      msg += `${await this.getFormattedPriceWithCurrency(
        oldPrice - actualTotalPrice,
      )}，推荐您更改还车时间至${newReturnDate}`;
    } else {
      msg = `该供应商租期${
        duration
      }天的报价为${await this.getFormattedPriceWithCurrency(
        actualTotalPrice,
      )}，`;

      msg += `和${durationHour}价格一致，推荐您更改还车时间至${newReturnDate}`;
    }

    Alert.alert('', msg, [
      {
        style: 'cancel',
        get text() {
          return '无需更改';
        },
        onPress: () => {},
      },
      {
        get text() {
          return '接受更改';
        },
        onPress: () => {
          this.props.setProductDropOffDate(newReturnDate);
          this.queryProduct(
            lodashMerge({}, returnPointInfo, {
              date: newReturnDate,
            }),
          );
        },
      },
    ]);
  };

  getProductHeaderProps = () => {
    const { tabSelectedId, isShowPackageTabs } = this.state;
    const {
      isFail,
      headerData,
      currency,
      bizType,
      toolBoxCustomerJumpUrl,
      isShowDropOff,
      countryId,
      isEasyLife,
      curInsPackageId,
      tripH5LocationDate,
      expandLocationAndDate,
      isProductLoading,
    } = this.props;
    return {
      scrollY: 0,
      tabSections: this.tabSections,
      selectedId: tabSelectedId,
      isFail,
      scrollHandler: this.scrollHandler,
      handleBackPress: this.handleBackPress,
      data: headerData,
      currency,
      bizType,
      toolBoxCustomerJumpUrl,
      isShowDropOff,
      countryId,
      isEasyLife,
      curInsPackageId,
      tripH5LocationDate,
      expandLocationAndDate,
      isProductLoading,
      isShowPackageTabs,
      leftIconTestID: UITestID.car_testid_page_product_header_lefticon,
      serviceTestID: UITestID.car_testid_page_product_header_serviceicon,
    };
  };

  priceAlertPropsMapping = (): any => {
    const { showPriceConfirm } = this.props;
    const { imageUrl, message, desc, confirmBtnText, cancelBtnText, type } =
      getPriceAlertProps();
    const confirmTypes = [PriceAlertType.Error, PriceAlertType.PriceCacheError];
    return {
      imageUrl,
      message,
      desc,
      confirmBtnText,
      cancelBtnText,
      visible: showPriceConfirm,
      onConfirm: this.priceAlertHandler,
      onCancel: () => this.popToList(type),
      priceAlertType: type,
      type: confirmTypes.includes(type) ? 'confirm' : 'alert',
      confirmTestID: UITestID.car_testid_page_product_pricealert_confirm,
      cancelTestID: UITestID.car_testid_page_product_pricealert_cancel,
    };
  };

  priceAlertHandler = () => {
    const { type } = getPriceAlertProps();
    const { priceTimerRes } = this.props;
    const { res, param } = priceTimerRes || {};
    const resStatus = {
      isError: true,
      param,
      res,
    };
    switch (type) {
      case PriceAlertType.PriceChange:
      case PriceAlertType.Error:
      case PriceAlertType.PriceCacheError:
        // 一期全部回到详情页进行刷新
        // 后面需区分填写页及其他分支页面（如：车型弹层页）
        // 已经考虑选中当前套餐及支付方式、额外设备
        this.props.showPriceConfirmAction(false);
        AppContext.PageInstance.pop(Channel.getPageId().Product.EN);
        this.retry({
          reset: true,
        });

        CarLog.LogCode({ name: priceAlertRetryKey[type] });
        break;
      case PriceAlertType.ServerError:
        this.props.showPriceConfirmAction(false);
        this.props.queryProductCallBack(resStatus);
        break;
      default:
        this.popToList(type);
    }
  };

  /**
   * common
   */
  setModalVisible =
    (key, value, logData = {}, otherState = {}) =>
    () => {
      const nextState: ProductStateType = {};

      this.setState(({ modalVisible }) => ({
        ...nextState,
        modalVisible: {
          ...modalVisible,
          [key]: value,
        },
        ...otherState,
      }));

      CarLog.LogCode({
        name: logKey[key],
        data: {
          modalVisible: value,
          ...logData,
        },
      });
    };

  getScrollRef = () =>
    this.scrollRef.getNode ? this.scrollRef.getNode() : this.scrollRef;

  // 滚动偏移量
  getScrollToLeftHeight = (y, index) => {
    const { productHeaderLayoutHeight } = this.state;
    let fixedY = y;
    fixedY = index === 0 ? 0 : y + productHeaderLayoutHeight - getPixel(44);
    return fixedY;
  };

  scrollHandler = item => {
    const { sectionLayout } = this.state;
    const index = this.tabSections.indexOf(item);
    const y = this.getScrollToLeftHeight(
      Math.floor(sectionLayout[index]),
      index,
    );
    this.getScrollRef().scrollTo({ y, animated: true });
    this.scrollY = y;
    this.setTabSelectedId(y);
    if (this.tabSections[this.tabSections.length - 1] === item) {
      // 解决最后一个高度不够的情况下，第一次点击无法选择最后一项
      this.isLastAnchor = true;
      this.setTimers.timer6 = setTimeout(() => {
        this.isLastAnchor = false;
      }, 50);
    }

    CarLog.LogCode({
      name: logKey[item],
    });
  };

  setScrollThrottle = () => {
    this.scrollThrottle = lodashThrottle(this.onScroll, 16, {
      trailing: false,
    });
  };

  skipScroll = () => {
    const { isProductLoading } = this.props;
    if (this.tabClick || isProductLoading) {
      return true;
    }
    return false;
  };

  onScroll = ({ nativeEvent }) => {
    if (this.skipScroll()) {
      return;
    }
    const { y } = nativeEvent.contentOffset;
    const { setShowHeaderBottom = noop, setShowAnimation = noop } =
      this.headerRef || {};
    setShowAnimation(y > 0);
    this.scrollY = Math.floor(y);
    const isShowHeaderBottom = this.scrollY >= 50;
    setShowHeaderBottom(isShowHeaderBottom);
    this.setTabSelectedId(Math.floor(y));
    const morePackagePos =
      this.allModulePosition[ALL_MODULE_INFO_MAP.MORE_PACKAGE.key]?.pos;
    const pickMatersPos =
      this.allModulePosition[ALL_MODULE_INFO_MAP.PICK_MATERS.key]?.pos;
    const fixScrollY = this.scrollY - getPixel(70);
    const isShowPackageTabs =
      fixScrollY > morePackagePos - (BbkUtils.isAndroid ? getPixel(10) : 0) &&
      fixScrollY < pickMatersPos;
    this.setState({
      isShowPackageTabs,
    });
    const fixOpacityAnimation = y >= 50 ? 1 : (y > 0 ? y : 0) / 50;
    const opacityAnimation = y <= 0 ? 1 : (50 - y) / 50;
    if (
      fixOpacityAnimation !== this.state.fixOpacityAnimation ||
      opacityAnimation !== this.state.opacityAnimation
    ) {
      this.setState({
        fixOpacityAnimation,
        opacityAnimation,
      });
    }

    const packageTabOpacityRef = xCreateAnimation({
      duration: BbkUtils.isAndroid ? 300 : 100,
      delay: 0,
      timingFunction: 'linear',
    });
    packageTabOpacityRef.opacity(1).step();
    this.setState({
      packageTabOpacityAnimation: packageTabOpacityRef.export(),
    });
  };

  onMomentumScrollEnd = ({ nativeEvent }) => {
    if (this.skipScroll()) {
      this.tabClick = false;
      return;
    }

    this.scrollThrottle.cancel();
    const { y } = nativeEvent.contentOffset;
    this.scrollY = Math.floor(y);
    this.setTabSelectedId(Math.floor(y));
  };

  // anchor 距离位置计算
  calculateAnchorPosition = () => {
    // 小图模式兼容
    // http://iwork.ctripcorp.com/sync/opencard/3588/6512/18470/798250
    const { productHeaderLayoutHeight, isProductHeaderFixed, sectionLayout } =
      this.state;
    if (this.scrollY >= 50 && !isProductHeaderFixed) {
      const fixedLayout = sectionLayout.map((y, index) => {
        let fixedY = y;
        fixedY = index === 0 ? 0 : y + productHeaderLayoutHeight - 44;
        return Math.floor(fixedY);
      });
      this.setState({
        isProductHeaderFixed: true,
        sectionLayout: fixedLayout,
      });
    }
  };

  setTabSelectedId = y => {
    const { sectionLayout, tabSelectedId } = this.state;
    const { setSelectedId = noop } = this.headerRef || {};

    this.calculateAnchorPosition();

    let nextTabSelectedId = tabSelectedId;

    lodashForEach(sectionLayout, (value, i) => {
      if (y >= value) {
        nextTabSelectedId = this.tabSections[i];
      }
    });
    if (!this.isLastAnchor) {
      setSelectedId(nextTabSelectedId);
    }
  };

  /**
   * layoouts
   */
  onSectionLayout =
    sectionIndex =>
    ({ nativeEvent }) => {
      const { y } = nativeEvent.layout;
      const { sectionLayout } = this.state;
      const nextSectionLayout = [...sectionLayout];
      nextSectionLayout.splice(sectionIndex, 1, Math.floor(y));
      this.setState({
        sectionLayout: nextSectionLayout,
      });

      switch (sectionIndex) {
        case 1:
          this.handleAllModuleLayout(ALL_MODULE_INFO_MAP.SELECT_PACKAGE, y);
          break;
        case 2:
          this.handleAllModuleLayout(ALL_MODULE_INFO_MAP.PICK_MATERS, y);
          break;
        case 3:
          this.handleAllModuleLayout(ALL_MODULE_INFO_MAP.STORE_POLICY, y);
          break;
        default:
          break;
      }
    };

  onHeaderLayout = ({ nativeEvent }) => {
    this.setState({
      productHeaderLayoutHeight: nativeEvent.layout.height,
    });
  };

  getInsPackageLayout = layout => {
    const { curInsPackageId } = this.props;
    const { insPackageLayout } = this.state;
    const { packageInfos = [] } = mapIsuranceBox(curInsPackageId);
    if (
      packageInfos.length === 0 ||
      insPackageLayout.length === packageInfos.length
    )
      return;
    insPackageLayout.push(layout);
    const soryByY = lodashSortBy(insPackageLayout, ['y']);
    this.setState({ insPackageLayout: soryByY });
  };

  handleSelectInsPackageInModal = index => {
    const { insPackageLayout, sectionLayout } = this.state;
    const { curInsPackageId } = this.props;
    const { packageInfos = [] } = mapIsuranceBox(curInsPackageId);
    if (packageInfos.length === 0) return;
    const insAnchor = sectionLayout[1];
    const layoutY = insPackageLayout[index]?.y || 0;
    this.getScrollRef().scrollTo({ y: insAnchor + layoutY });
  };

  /**
   * press handlers
   */

  retry = (param?: any) => {
    this.queryProduct(param);
    CarLog.LogCode({ name: '点击_详情页_重试' });
  };

  handleBackPress = () => {
    this.pageGoBack();
  };

  popToList = (type?: PriceAlertType) => {
    if (type === PriceAlertType.SoldOut) {
      const productKey = Utils.getProductKey(getProductRequestReference());
      const { addSaleOutList } = this.props;
      addSaleOutList(productKey);
    }
    const enName =
      priceAlertPopKey[type] || '点击_详情页_其他报错异常提示_返回列表页';
    CarLog.LogCode({ name: enName });
    this.props.showPriceConfirmAction(false);
    setFromPage(AppContext.PageInstance.getPageId());
    AppContext.PageInstance.pop(Channel.getPageId().List.EN);
  };

  gotoBook = async () => {
    // 校验送车上门是否需要降级
    if (this.props.needDownGrade) {
      this.props.validateIsDownGrade({
        callbackFun: this.continueGoToBook,
        isFailToContinue: true,
      });
      return;
    }
    const {
      isOpenInsuranceSellingModal,
      openInsuranceSellingModal = Utils.noop,
    } = this.props;
    if (isOpenInsuranceSellingModal) {
      openInsuranceSellingModal();
      return;
    }
    this.continueGoToBook();
  };

  continueGoToBook = async () => {
    const productRes = ProductReqAndResData.getData(
      ProductReqAndResData.keyList.productRes,
    );
    const { reference = {}, selectedInsuranceId } = this.props;
    const { vendorCode, bizVendorCode, pStoreCode, rStoreCode, klbVersion } =
      reference;
    const {
      vehicleInfo = {},
      vendorInfo = {},
      commentInfo = {},
    } = productRes || {};
    const { vehicleCode, name, groupName } = vehicleInfo;
    const { vendorName } = vendorInfo;
    const isEasyLife = getCurPackageIsEasyLife();

    CarLog.LogCode({
      name: '点击_详情页_去预订',

      insuranceId: selectedInsuranceId,
      data: {
        vehicleCode,
        vehicleName: name,
        groupName,
        bizVendorCode,
        pStoreCode,
        rStoreCode,
        vendorCode,
        vendorName,
        isEasyLife,
        commentCnt: commentInfo.commentCount,
        commentScore: commentInfo.overallRating,
        klbVersion,
      },
    });
    const { selectedIdType, refreshList } = this.props;
    const param = {
      selectedIdType,
      noNeedRefresh: true,
      retry: this.retry,
      priceTimer: this.priceTimer,
      reference,
    };
    const isLogin = await User.isLogin();
    if (!isLogin) {
      const isLoginNow = await User.toLogin();
      if (!isLoginNow) {
        return;
      }
      param.noNeedRefresh = false;
      refreshList();
    }
    if (AppContext.PageInstance.getPageId() === this.getPageId()) {
      this.push('Booking', param);
      // 如果是费用明细弹窗则，延迟500毫秒关闭弹窗
      const { modalVisible } = this.state;
      if (modalVisible.osdPriceDetailVisible) {
        this.setTimers.timer7 = setTimeout(() => {
          this.setModalVisible(
            ProductModalVisibleType.osdPriceDetailVisible,
            false,
          )();
        }, 500);
      }
    } else {
      // 费用明细page
      this.replace('Booking', param);
    }
  };

  imagePressHandler = data => {
    const { imageLogInfo, reference } = this.props;
    const { pStoreCode, rStoreCode, klbVersion } = reference || {};
    CarLog.LogCode({
      name: '点击_详情页_车型图片',

      data: {
        ...getImageLogData(data),
        ...imageLogInfo,
        pStoreCode,
        rStoreCode,
        klbVersion,
      },
    });
  };

  getImageHeaderTestId = data => {
    const { imageLogInfo } = this.props;
    return CarLog.createExposureId(
      LogKey.c_car_exposure_productdetail_picture_10650041522,
      {
        data: {
          ...getImageLogData(data),
          ...imageLogInfo,
        },
      },
    );
  };

  goImagePage = () => {
    const { storeCode } = getPickupStoreInfo();
    const { curInsPackageId } = this.props;
    const pageParam = {
      storeCode,
      vehicleId: getVehicleId(curInsPackageId),
      categoryId: +Utils.getBusinessType(),
    };
    CarLog.LogCode({
      name: '点击_详情页_跳转图片聚合页',

      data: {
        pageParam,
      },
    });
    const ImagePageName = Channel.getPageId().Image.EN;
    if (AppContext.Flutter?.routers?.includes(ImagePageName)) {
      xRouter.navigateTo({
        url: `/trip_flutter?flutterName=flutter_car_image&allowRotation=1&showtype=present&params=${JSON.stringify(
          pageParam,
        )}`,
      });
    } else {
      this.push(ImagePageName, { pageParam });
    }
  };

  gotoFeeDatail = () => {
    if (AppContext.PageInstance.getPageId() !== this.getPageId()) {
      CarLog.LogCode({
        name: '点击_详情页_查看价格明细',

        modalVisible: false,
      });
      AppContext.PageInstance.pop();
      return;
    }
    const { modalVisible } = this.state;
    CarLog.LogCode({
      name: '点击_详情页_查看价格明细',

      modalVisible: true,
    });
    this.setModalVisible(
      ProductModalVisibleType.osdPriceDetailVisible,
      !modalVisible.osdPriceDetailVisible,
    )();
  };

  onClosePriceChange = () => {
    this.setState({ showPricePop: false });
    CarLog.LogCode({ name: '点击_详情页_关闭零散小时费弹框' });
  };

  onClosePriceChangeTips = PricechangeReason => {
    this.setState({ showPriceChangeTips: false });
    CarLog.LogCode({
      name: '点击_产品详情页_变价提醒_关闭',

      PricechangeReason,
    });
  };

  showWarningTipsModal = content => {
    this.setModalVisible(ProductModalVisibleType.warningTipsVisible, true)();
    this.setState({
      warningTipsModalCotent: content,
    });
  };

  onFooterLayout = event => {
    // eslint-disable-next-line no-shadow
    const { height: curheight } = event.nativeEvent.layout;
    this.setState({
      footerBarHeight: curheight + getPixel(24),
    });
  };

  setBookBarHeight = height => {
    this.setState({
      bookBarHeight: height + fixIOSOffsetBottom() + adaptNoaNomalousBottom(),
    });
  };

  handleAllModuleLayout = (module, pos) => {
    this.allModulePosition[module.key] = {
      name: module?.name,
      pos,
      key: module.key,
    };
  };

  onPayModeLayout = event => {
    const { y } = event.nativeEvent.layout;
    this.setState({
      payModeWrapPosition: y,
    });
    this.handleAllModuleLayout(ALL_MODULE_INFO_MAP.MORE_PACKAGE, y);
  };

  onPressNextPayModes = () => {
    const { payModeWrapPosition, productHeaderLayoutHeight } = this.state;
    const { morePackageLogBaseInfo } = this.props;
    CarLog.LogCode({
      name: '点击_详情页_支付方式bar',

      info: {
        ...morePackageLogBaseInfo,
        clickModule: getCurrentModuleName(this.scrollY, this.allModulePosition),
      },
    });

    let targetPosition = 0;
    targetPosition =
      payModeWrapPosition + productHeaderLayoutHeight - getPixel(44);
    this.getScrollRef().scrollTo({
      y: targetPosition,
      animated: true,
    });
  };

  handleNotMatchOperateByType = (type: NoMatchButtonType) => {
    const {
      toolBoxCustomerJumpUrl,
      addSaleOutList,
      onErrorShowLocationAndDatePop,
    } = this.props;
    const productKey = Utils.getProductKey(getProductRequestReference());
    switch (type) {
      case NoMatchButtonType.BackAndDisable:
        this.handleBackPress();
        break;
      case NoMatchButtonType.ReloadProduct:
        this.retry();
        break;
      case NoMatchButtonType.Customer:
        Utils.openUrlWithTicket(toolBoxCustomerJumpUrl);
        break;
      case NoMatchButtonType.BackAndModify:
        this.handleBackPress();
        onErrorShowLocationAndDatePop();
        break;
      case NoMatchButtonType.BackAndReloadList:
        this.handleBackPress();
        break;
      case NoMatchButtonType.BackAndSoldOut:
        this.handleBackPress();
        break;
      default:
        this.handleBackPress();
        addSaleOutList({
          priceSoldOutText: Texts.listCombineTempCannotBook,
          saleOutProductKey: productKey,
        });
        break;
    }
  };

  renderPriceChange = () => {
    const { isPriceLoading, priceChangeInfo } = this.props;
    const { showPriceChangeTips, footerBarHeight = 0 } = this.state;
    return (
      priceChangeInfo &&
      showPriceChangeTips &&
      !isPriceLoading && (
        <PriceChangeTips
          priceDiff={priceChangeInfo.priceDiff}
          priceTitle={priceChangeInfo.title}
          priceDesc={priceChangeInfo.subTitle}
          bottomHeight={footerBarHeight}
          onClosePriceChange={() => {
            this.onClosePriceChangeTips(
              [priceChangeInfo.title, priceChangeInfo.subTitle].join('-'),
            );
          }}
          testID={CarLog.LogExposure({
            name: '曝光_产品详情页_变价提醒',

            PricechangeReason: [
              priceChangeInfo.title,
              priceChangeInfo.subTitle,
            ].join('-'),
          })}
        />
      )
    );
  };

  renderFooterCache = memoize(
    (
      noExtraInfo,
      vehicelModalVisible,
      showPricePop,
      priceChange,
      isPriceLoading,
      isProductLoading,
      isPriceFail,
      isCreditRent,
      isRebook,
      osdPriceDetailVisible,
      canShowInsuranceSellingModal,
    ) => {
      return (
        <View
          style={{ position: 'relative', zIndex: 1 }}
          onLayout={event => this.onFooterLayout(event)}
        >
          {!vehicelModalVisible &&
            !noExtraInfo &&
            showPricePop &&
            priceChange && (
              <PriceChangePop onClosePriceChange={this.onClosePriceChange} />
            )}

          <BookBar
            noExtraInfo={noExtraInfo}
            isPriceLoading={isPriceLoading}
            isProductLoading={isProductLoading}
            isPriceFail={isPriceFail}
            gotoBook={!isProductLoading ? this.gotoBook : noopEmpty}
            // 如果是出境产品详情页点击下一步按钮，如果需要弹出加购保险提醒弹窗
            // 则防抖间隔为1秒，防止快速关闭弹窗后，点击下一步无响应
            debounceTime={canShowInsuranceSellingModal ? 1000 : 2000}
            isCreditRent={isCreditRent}
            gotoFeeDatail={
              !isProductLoading && !isPriceLoading && this.gotoFeeDatail
            }
            isRebook={isRebook}
            onPressNextPayModes={this.onPressNextPayModes}
            visible={!osdPriceDetailVisible}
            setBookBarHeight={this.setBookBarHeight}
            osdPriceDetailVisible={osdPriceDetailVisible}
          />
        </View>
      );
    },
  );

  /**
   * render functions
   */
  renderFooter = (noExtraInfo?: boolean) => {
    const {
      isProductLoading,
      isPriceLoading,
      isPriceFail,
      isCreditRent,
      priceChange,
      isRebook,
      isOpenInsuranceSellingModal,
    } = this.props;
    const { showPricePop, modalVisible } = this.state;
    const vehicelModalVisible =
      modalVisible[ProductModalVisibleType.vehicelModalVisible];
    const osdPriceDetailVisible =
      modalVisible[ProductModalVisibleType.osdPriceDetailVisible];
    return this.renderFooterCache(
      noExtraInfo,
      vehicelModalVisible,
      showPricePop,
      priceChange,
      isPriceLoading,
      isProductLoading,
      isPriceFail,
      isCreditRent,
      isRebook,
      osdPriceDetailVisible,
      isOpenInsuranceSellingModal,
    );
  };

  renderNewImageHeader() {
    const isNoResultNew = CarServerABTesting.isNoResult();
    const { isProductLoading } = this.props;
    const standardImg = this.props.pageParam?.vehicleDesc?.imgUrl;
    let headerCarImageUrl = standardImg;
    if (!isProductLoading) {
      headerCarImageUrl = getHeaderCarImageUrl();
    }
    const imageHeaderprops = {
      imageList: [{ url: headerCarImageUrl, type: ImageListType.VendorPic }],
    };
    return (
      <ProductHeaderImage
        imageUrl={Utils.autoProtocol(headerCarImageUrl)}
        onPressFn={this.imagePressHandler}
        getTestId={this.getImageHeaderTestId}
        traceLog={this.getOsdHeaderImgTraceInfo(imageHeaderprops)}
        isNoResultNew={isNoResultNew}
      />
    );
  }

  onContentLayout = () => {
    this.performanceMonitor.layoutEnd = new Date();
  };

  // https://zh-hans.reactjs.org/docs/refs-and-the-dom.html#caveats-with-callback-refs
  handleScrollRef = ref => {
    this.scrollRef = ref;
    SnapshotData.setData(
      snapshotDataKeyList.detailSnapshotData,
      ref && ref.getNode ? ref.getNode() : ref,
    );
  };

  renderElevenHoliday = () => (
    <Image
      style={{
        width,
        height: getPixel(92),
        marginTop: -getPixel(24),
        marginBottom: getPixel(24),
      }}
      source={{
        uri: `${ImageUrl.rncarappBasicUrl}summerspecial/product_summer_2021.png`,
      }}
    />
  );

  getListWarningInfo = () => {
    const { getListWarningInfo } = this.props;
    getListWarningInfo({ pageIndexId: PageIndexId.Product });
  };

  renderScrollView = () => {
    const { firstScreen } = this.state;
    const isRecommend = CarServerABTesting.isRecommend();
    const isNoResultNew = CarServerABTesting.isNoResult();
    const priceTip = isNoResultNew
      ? this.props.reference?.priceTip
      : this.props.pageParam?.vendor?.priceTip;
    const {
      restAssuredTag,
      productWaringInfo,
      fetchWarningInfoLoading,
      isShowTravelLimit,
      isRefactor,
      ipollLogData,
    } = this.props;
    const content = (
      <>
        {this.renderNewImageHeader()}
        {this.isShowMarketLabel && this.renderElevenHoliday()}
        <RestAssuredBanner tag={restAssuredTag} isProduct={true} />
        {isRecommend && (
          <ProductRecommendTip
            recommendInfo={priceTip}
            isNoResultNew={isNoResultNew}
          />
        )}
        <ProductContent
          {...this.props}
          onSectionLayout={this.onSectionLayout}
          setModalVisible={this.setModalVisible}
          logKey={logKey}
          getInsPackageLayout={this.getInsPackageLayout}
          handleSelectInsPackageInModal={this.handleSelectInsPackageInModal}
          firstScreen={firstScreen}
          onLogin={this.onLogin}
          showWarningTipsModal={this.showWarningTipsModal}
          isShowMarketLabel={this.isShowMarketLabel}
          productWaringInfo={productWaringInfo}
          getListWarningInfo={this.getListWarningInfo}
          fetchWarningInfoLoading={fetchWarningInfoLoading}
          isShowTravelLimit={isShowTravelLimit}
          onPayModeLayout={this.onPayModeLayout}
          isRefactor={isRefactor}
          ipollProductConfig={this.ipollProductConfig}
          ipollLogData={ipollLogData}
        />
      </>
    );

    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={this.scrollThrottle}
        style={styles.scrollPage}
        onMomentumScrollEnd={this.onMomentumScrollEnd}
        ref={this.handleScrollRef}
        onLayout={this.onContentLayout}
      >
        {content}
      </ScrollView>
    );
  };

  goMap = limitScope => {
    this.push(Channel.getPageId().LimitMap.EN, { limitScope });
  };

  renderModals = () => {
    const {
      modalVisible,
      firstScreen,
      warningTipsModalCotent,
      selectPackageId,
      curPackageId,
      insuranceNotice,
      materialAnchor,
      curBusinessTimePolicyType,
      bookBarHeight,
    } = this.state;
    const { selectedIdType } = this.props;
    if (firstScreen) {
      return null;
    }
    return (
      <>
        <ProductModalsContainer
          modalVisible={modalVisible}
          selectPackageId={selectPackageId}
          curPackageId={curPackageId}
          selectedIdType={selectedIdType}
          setModalVisible={this.setModalVisible}
          handleSelectInsPackageInModal={this.handleSelectInsPackageInModal}
          retry={this.retry}
          bookBar={this.footerDom}
          warningTipsModalCotent={warningTipsModalCotent}
          goMap={this.goMap}
          renderFooter={this.renderFooter}
          insuranceNotice={insuranceNotice}
          materialAnchor={materialAnchor}
          curBusinessTimePolicyType={curBusinessTimePolicyType}
          footerBarHeight={bookBarHeight}
        />

        <EasyLifeTagListModal />
        <PickupDownGradePop />
        <DepositRateDescriptionModal />
        <InsuranceSellingModal goToBook={this.continueGoToBook} />
      </>
    );
  };

  handlePopExposure = () => {
    const { priceAlertType } = this.priceAlertPropsMapping();
    CarLog.LogTrace({
      key: LogKey.c_car_trace_product_pop_exposure,
      info: {
        type: priceAlertType,
      },
    });
  };

  refFn = ref => {
    this.headerRef = ref;
  };

  // 异常页面埋点数据组合
  getNotMatchBaseTraceInfo = () => {
    const { emptyGuideInfo, reference, pageVehicleHeaderParam } = this.props;
    return {
      vendorCode: reference?.vendorCode,
      pstoreCode: reference?.pStoreCode,
      rstoreCode: reference?.rStoreCode,
      groupId: pageVehicleHeaderParam?.groupId,
      groupName: pageVehicleHeaderParam?.groupName,
      errorCode: emptyGuideInfo?.errorCode || '',
      errorType: emptyGuideInfo?.errorType || '',
    };
  };

  renderContent = () => {
    const { isFail, emptyGuideInfo } = this.props;
    const {
      opacityAnimation,
      fixOpacityAnimation,
      packageTabOpacityAnimation,
    } = this.state;
    this.priceChangeDom = this.renderPriceChange();
    this.footerDom = this.renderFooter();

    return (
      <>
        <ProductHeaderContainer
          refFn={this.refFn}
          opacityAnimation={opacityAnimation}
          fixOpacityAnimation={fixOpacityAnimation}
          packageTabOpacityAnimation={packageTabOpacityAnimation}
          channelType={Utils.getType()}
          onHeaderLayout={this.onHeaderLayout}
          {...this.getProductHeaderProps()}
          {...getBbkVehicleNameProps()}
        />

        {isFail ? (
          <ProductNoMatch
            type={emptyGuideInfo?.picType}
            title={emptyGuideInfo?.title}
            subTitle={emptyGuideInfo?.subTitle}
            buttonInfo={emptyGuideInfo?.buttonInfo}
            handleOperateByType={this.handleNotMatchOperateByType}
            baseTraceInfo={this.getNotMatchBaseTraceInfo()}
            style={styles.noMatch}
          />
        ) : (
          <>
            {this.renderScrollView()}

            {this.priceChangeDom}
            <View className={c2xStyles.bookBarWrap}>{this.footerDom}</View>

            {this.renderModals()}
          </>
        )}
        <CarDialog {...this.priceAlertPropsMapping()} />
      </>
    );
  };

  renderPage() {
    const { isProductLoading } = this.props;
    const productPageParam = getProductPageParam();
    return (
      <ViewPort>
        <View
          testID={UITestID.car_testid_page_product_page_osd}
          style={xMergeStyles([styles.page, styles.newBg])}
        >
          {!productPageParam && isProductLoading ? (
            <>
              <ProductHeaderImage />
              <View
                className={classNames(
                  c2xStyles.loadingWrap,
                  c2xStyles.details,
                  c2xStyles.content,
                )}
              >
                <BbkSkeletonLoading
                  style={styles.loadingBg}
                  imageStyle={styles.details}
                  visible={isProductLoading}
                  pageName={PageType.Product}
                />
              </View>
            </>
          ) : (
            this.renderContent()
          )}
          <SesameContainer />
          {this.props.isDebugMode && (
            <AssistiveTouch
              onPress={() => {
                this.push('Debug');
              }}
            />
          )}
        </View>
      </ViewPort>
    );
  }
}
