import ScrollView from '@c2x/components/ScrollView';
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useCallback, useRef } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XBoxShadow,
} from '@ctrip/xtaro';

import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { color, font, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  getPixel,
  isAndroid,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import CurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { ImageUrl, UITestID } from '../../../Constants/Index';
import { mapIsuranceBox } from '../../../State/Product/BbkMapper';
import Texts from '../Texts';
import c2xStyles from './packageTabsC2xStyles.module.scss';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const styles = StyleSheet.create({
  tabsWrap: {
    backgroundColor: color.white,
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    paddingLeft: getPixel(32),
    flexDirection: 'row',
  },
  scrollWrap: {
    justifyContent: 'space-between',
    alignItems: 'stretch',
    paddingRight: getPixel(48),
  },
  dayPricePlusWrap: {
    textAlign: 'center',
    alignSelf: 'center',
    top: isAndroid ? getPixel(8) : 0,
  },
  pricePlus: {
    color: color.C_555555,
    ...font.F_24_10_regular_TripNumberRegular,
  },
  priceSelected: {
    color: color.C_FF5500,
    ...font.F_24_10_regular_TripNumberSemiBold,
  },
});

interface IPackageTabs {
  curInsPackageId: number;
  selectPackage: (data: any) => void;
}

const PackageTabs = (props: IPackageTabs) => {
  const { curInsPackageId, selectPackage } = props;
  const { packageInfos = [] } = mapIsuranceBox(curInsPackageId);
  const scrollViewRef = useRef(null);

  const getIsSelected = item => curInsPackageId === item?.insPackageId;

  const handleModuleScrollIntoView = useCallback(index => {
    const moduleWidth = getPixel(180);
    const targetX = index * moduleWidth;
    scrollViewRef?.current?.scrollTo?.({ x: targetX, animated: true });
  }, []);

  const onPressSelect = useCallback(
    (data, index) => {
      if (curInsPackageId === data.insPackageId) {
        return;
      }
      selectPackage({ curInsPackageId: data.insPackageId });
      handleModuleScrollIntoView(index);
    },
    [curInsPackageId, selectPackage, handleModuleScrollIntoView],
  );

  if (packageInfos?.length <= 1) return null;

  return (
    <View style={{ opacity: 1 }}>
      <XBoxShadow
        coordinate={{ x: 0, y: 1 }}
        color={setOpacity(color.C_272727, 0.07)}
        opacity={1}
        blurRadius={getPixel(12)}
      >
        <ScrollView
          style={styles.tabsWrap}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollWrap}
          ref={scrollViewRef}
        >
          <View className={c2xStyles.scrollLine}>
            {packageInfos?.map((item, index) => (
              <Touchable
                className={
                  getIsSelected(item)
                    ? c2xStyles.tabItemSelected
                    : c2xStyles.tabItem
                }
                testID={`${UITestID.car_testid_page_product_header_package_tab_item}_${item?.packageName}`}
                key={item?.packageName}
                onPress={() => onPressSelect(item, index)}
              >
                <View className={c2xStyles.titleWrap}>
                  <Text
                    className={classNames(
                      c2xCommonStyles.c2xTextDefaultCss,

                      getIsSelected(item)
                        ? c2xStyles.titleSelected
                        : c2xStyles.title,
                    )}
                  >
                    {item?.packageName}
                  </Text>
                  <Text style={styles.dayPricePlusWrap}>
                    <Text
                      className={classNames(
                        c2xStyles.pricePlus,
                        getIsSelected(item) && c2xStyles.priceSelected,
                      )}
                      fontWeight={getIsSelected(item) ? 'bold' : 'regular'}
                    >
                      +{' '}
                    </Text>
                    <CurrencyFormatter
                      currency={item?.currencyCode}
                      price={item?.gapPrice}
                      currencyStyle={xMergeStyles([
                        styles.pricePlus,
                        getIsSelected(item) && styles.priceSelected,
                      ])}
                      priceStyle={xMergeStyles([
                        styles.pricePlus,
                        getIsSelected(item) && styles.priceSelected,
                      ])}
                    />

                    <Text className={c2xStyles.priceUnit}>{Texts.dayUnit}</Text>
                  </Text>
                </View>
                {getIsSelected(item) && (
                  <Image
                    className={c2xStyles.selectedIcon}
                    src={`${ImageUrl.DIMG04_PATH}1tg0h12000de9znhy6896.png`}
                  />
                )}
              </Touchable>
            ))}
          </View>
        </ScrollView>
      </XBoxShadow>
    </View>
  );
};
export default memo(PackageTabs);
