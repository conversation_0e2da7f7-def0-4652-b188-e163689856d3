import { find as lodashFind } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { Component } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import { color, space, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkExtrasItem from '../../../ComponentBusiness/ExtrasItem';
import BbkDetailsBlock from '../../../ComponentBusiness/DetailsBlock';
import { ExtrasInfoProps } from '../Types';
import { CarLog, Utils } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import Text from '../Texts';
import BlockCard from '../../Booking/Component/BlockCard';

const { availablePurchase } = Text;

const { selector, I18nTextConnectionViaComma, getPixel } = BbkUtils;
const styles = StyleSheet.create({
  wrapper: {
    marginTop: space.spaceL,
    marginBottom: getPixel(16),
    backgroundColor: color.white,
    paddingLeft: space.spaceXXL,
    paddingRight: space.spaceXXL,
    paddingTop: space.spaceXXL,
  },
  wrapperNew: {
    borderRadius: getPixel(16),
    marginTop: getPixel(24),
    marginBottom: 0,
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
    paddingTop: getPixel(32),
  },
  out: {
    backgroundColor: color.white,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: color.grayBorder,
  },
  item: { marginLeft: 0, marginRight: 0 },
  titleTextStyle: {
    ...font.title1BoldStyle,
  },
  bookingOptimizationTitleTextStyle: {
    ...font.title4BoldStyle,
    color: color.recommendProposeBg,
  },
  bookingOptimizationTitleStyle: {
    paddingBottom: getPixel(24),
  },
});

interface ExtrasInfoState {}

enum AddProductInfoStatus {
  noShow = 1, // 不展示
  soldOut = 2,
} // 无库存

export default class ExtrasInfo extends Component<
  ExtrasInfoProps,
  ExtrasInfoState
> {
  constructor(props) {
    super(props);
    this.state = {};
  }

  handleExtrasChange = ({ equipmentCode }, value) => {
    const { changeExtrasNum, selectedExtras } = this.props;
    const selected = lodashFind(selectedExtras, { equipmentCode });
    if (selected && selected.currentNum === value) {
      return;
    }
    const param = [
      {
        code: equipmentCode,
        num: value,
      },
    ];

    changeExtrasNum(param);
    CarLog.LogCode({
      name: '点击_详情页_选择附加产品',

      data: param,
    });
  };

  handleCombinationChange = (data, isChoose) => {
    const { selectPackage, insPackageId } = this.props;
    if (!isChoose) {
      // 取消精选组合，重置 bomCode
      selectPackage({ curInsPackageId: insPackageId });
    }
    CarLog.LogCode({
      name: '点击_详情页_切换精选组合',

      data: {
        select: !isChoose,
      },
    });
  };

  render() {
    const {
      curEquipments,
      selectedExtras,
      combinations,
      bomCode,
      onPressExtras,
      addProductInfo,
      style,
    } = this.props;
    const combinationsHiked =
      combinations && combinations.length && combinations.filter(v => !v.hike);

    const emptyContent = !(
      (curEquipments && curEquipments.length) ||
      (combinationsHiked && combinationsHiked.length)
    );

    // 是否售罄
    const isSoldOut = addProductInfo?.status === AddProductInfoStatus.soldOut;
    const hideModule = addProductInfo?.status === AddProductInfoStatus.noShow;

    // 非售罄场景，如果无设备信息或者当前SKU未关联儿童座椅，则不展示此模块
    if (!isSoldOut && (emptyContent || hideModule)) return null;

    const equipmentNames = I18nTextConnectionViaComma(
      curEquipments.map(v => v.equipmentName),
    );

    const subTitle = isSoldOut
      ? Text.extrasChildSeatSoldOutDesc
      : selector(equipmentNames, availablePurchase(equipmentNames));
    const combination =
      combinationsHiked && combinationsHiked.find(v => v.bomCode === bomCode);

    const isShowExtrasItem = selectedExtras?.length || combination;

    if (Utils.isCtripIsd() && !isShowExtrasItem) {
      return (
        <BlockCard
          title={Text.extras}
          desc={subTitle}
          isShowArrow={!isSoldOut}
          onPress={!isSoldOut && onPressExtras}
          style={style}
        />
      );
    }

    return (
      <BbkDetailsBlock
        style={xMergeStyles([
          styles.wrapper,
          Utils.isCtripIsd() && styles.wrapperNew,
        ])}
        title="附加产品"
        titleStyle={Utils.isCtripIsd() && styles.bookingOptimizationTitleStyle}
        onPress={onPressExtras}
        isTitleRight={true}
        titleTextStyle={
          Utils.isCtripIsd()
            ? styles.bookingOptimizationTitleTextStyle
            : styles.titleTextStyle
        }
        subTitle={subTitle}
        subTitleStyle={{ color: color.fontPrimary }}
        testID={CarLog.LogExposure({ name: '曝光_附加产品模块' })}
        taTestID={UITestID.car_testid_page_product_extrasInfo}
      >
        {isShowExtrasItem && (
          <View style={styles.out}>
            {combination && (
              <BbkExtrasItem
                key={combination.bomCode}
                data={{ ...combination, description: '' }}
                type="choose"
                style={styles.item}
                isSelected={true}
                isBottomLine={!!(selectedExtras && selectedExtras.length)}
                isOnlyChoose={!combinations.filter(v => v.hike).length}
                isInlude={combinations.length === 1}
                onChooseItem={this.handleCombinationChange}
              />
            )}
            {selectedExtras.map((data, index) => (
              <BbkExtrasItem
                numMax={data.maxCount}
                key={data.equipmentCode}
                data={{ ...data, equipmentDesc: '' }}
                style={styles.item}
                numberValue={data.currentNum}
                isBottomLine={index !== selectedExtras.length - 1}
                onChooseItem={this.handleExtrasChange}
                isCtripIsd={Utils.isCtripIsd()}
              />
            ))}
          </View>
        )}
      </BbkDetailsBlock>
    );
  }
}
