import React, { memo, useState, useMemo } from 'react';
import {
  XView as View,
  xClassNames,
  XImageBackground,
  XImage,
} from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import {
  getPixel,
  useMemoizedFn,
  isIos,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import styles from './InsuranceUpgrade.module.scss';
import * as ImageUrl from '../../../../Constants/ImageUrl';
import {
  PackageDetailV2,
  IInsuranceCode,
} from '../../../../Types/Dto/QueryProductInfoType';
import { CarLog } from '../../../../Util/Index';
import {
  getProductRequestReference,
  getBaseResData,
} from '../../../../Global/Cache/ProductSelectors';
import InsuranceTipInLine from '../../../../ComponentBusiness/Tips/src/InsuranceTipInLine';

interface IInsuranceItem {
  isRecommended?: boolean;
  data: PackageDetailV2;
  onPressDetail?: () => void;
  isOrangeTheme?: boolean;
}
const InsuranceItem: React.FC<IInsuranceItem> = memo(
  ({ isRecommended, data, onPressDetail, isOrangeTheme }: IInsuranceItem) => {
    const { descriptionV2, cityEncourage, name } = data || {};
    return (
      <View
        className={
          isRecommended ? styles.recommendedOption : styles.currentOption
        }
      >
        <View
          className={xClassNames(
            styles.currentTagWrap,
            isRecommended && styles.recommendedTagWrap,
            isRecommended && isOrangeTheme && styles.orangeTagWrap,
          )}
        >
          <Text className={styles.optionTitle} fontWeight="medium">
            {isRecommended ? cityEncourage : '当前'}
          </Text>
        </View>
        <Touchable
          onPress={onPressDetail}
          style={layout.alignHorizontal}
          className={styles.mb14}
        >
          <Text
            className={xClassNames(
              styles.optionDetail,
              isRecommended && styles.recommendedOptionDetail,
            )}
            fontWeight="medium"
          >
            {name}
          </Text>
          <Text
            type="icon"
            className={xClassNames(
              styles.optionDetailIcon,
              isRecommended && styles.recommendedOptionDetail,
            )}
          >
            {icon.arrowRight}
          </Text>
        </Touchable>
        {descriptionV2?.length > 0 &&
          descriptionV2.map(item => (
            <Text
              key={item?.description}
              className={xClassNames(
                styles.optionText,
                isRecommended && styles.recommendedOptionText,
              )}
            >
              {item?.description}
            </Text>
          ))}
      </View>
    );
  },
);
interface IInsuranceUpgrade {
  insuranceTips?: string;
  packageDetailList?: PackageDetailV2[];
  onPressButton?: (data: PackageDetailV2) => void;
  onPressServiceClaimMore?: () => void;
  onPressCarServiceDetail?: (data) => void;
}
const InsuranceUpgrade: React.FC<IInsuranceUpgrade> = memo(
  ({
    insuranceTips,
    packageDetailList,
    onPressButton,
    onPressServiceClaimMore,
    onPressCarServiceDetail,
  }: IInsuranceUpgrade) => {
    const [isSelected, setIsSelected] = useState(false);
    const traceBaseInfo = useMemo(() => {
      const { vehicleInfo } = getBaseResData();
      const { vehicleCode } = vehicleInfo || {};
      const {
        skuId,
        pStoreCode: pstoreCode,
        rStoreCode: rstoreCode,
      } = getProductRequestReference() || {};
      return {
        skuId,
        pstoreCode,
        rstoreCode,
        vehicleCode,
      };
    }, []);
    const pressUpgradeBtn = useMemoizedFn(() => {
      if (onPressButton) {
        const data = !isSelected ? packageDetailList[1] : packageDetailList[0];
        setIsSelected(!isSelected);
        onPressButton(data);
        CarLog.LogCode({
          name: '点击_填写页_升级服务_服务加购选择',
          info: {
            ...traceBaseInfo,
            isSelected,
          },
        });
      }
    });
    const pressCarServiceDetail = useMemoizedFn(uniqueCode => {
      if (uniqueCode && onPressCarServiceDetail) {
        onPressCarServiceDetail(uniqueCode);
        CarLog.LogCode({
          name: '点击_填写页_升级服务_详情按钮',
          info: traceBaseInfo,
        });
      }
    });
    const pressServiceClaimMore = useMemoizedFn(() => {
      if (onPressServiceClaimMore) {
        onPressServiceClaimMore();
        CarLog.LogCode({
          name: '点击_填写页_升级服务_门店服务保障相关要求及须知',
          info: traceBaseInfo,
        });
      }
    });
    const isOrangeTheme = [
      IInsuranceCode.PRE,
      IInsuranceCode.PREP,
      IInsuranceCode.prep,
    ].includes(packageDetailList[1]?.uniqueCode);
    const bgImgUrlCode = isOrangeTheme
      ? '1tg1y12000jpulddy3F60'
      : '1tg3g12000jpul5va1327';
    return (
      <View className={styles.container}>
        <View className={styles.header}>
          <Text className={styles.title} fontWeight="medium">
            保障升级
          </Text>
          <Touchable
            onPress={pressUpgradeBtn}
            className={styles.upgradeBtnWrap}
          >
            <Text className={styles.priceText} fontWeight="medium">
              +
              <View>
                <View
                  className={xClassNames(
                    styles.textWrapRel,
                    isIos && styles.textWrapRelIos,
                  )}
                >
                  <Text className={styles.priceText}>￥</Text>
                  <Text className={styles.price} fontWeight="bold">
                    {packageDetailList?.[1]?.gapPrice}
                  </Text>
                </View>
              </View>
              /天
            </Text>
            <XImage
              src={`${ImageUrl.DIMG04_PATH}${isSelected ? '1tg2z12000jbuo29tDE0B' : '1tg2412000jbvqow04C7F'}.png`}
              className={styles.upgradeGou}
              mode="scaleToFill"
            />
          </Touchable>
        </View>
        <XImageBackground
          source={{ uri: `${ImageUrl.DIMG04_PATH}${bgImgUrlCode}.png` }}
          className={styles.insuranceOptions}
          imageStyle={{ width: '100%', height: getPixel(373) }}
          resizeMode="contain"
        >
          {packageDetailList?.map((item, index) => (
            <InsuranceItem
              key={item?.name}
              isRecommended={index !== 0}
              data={item}
              onPressDetail={() => {
                pressCarServiceDetail(item?.uniqueCode);
              }}
              isOrangeTheme={isOrangeTheme}
            />
          ))}
        </XImageBackground>
        <View className={styles.insuranceTipWrap}>
          <InsuranceTipInLine
            isShowBorder={false}
            insuranceTips={insuranceTips}
            onPressMore={pressServiceClaimMore}
          />
        </View>
      </View>
    );
  },
);

export default InsuranceUpgrade;
