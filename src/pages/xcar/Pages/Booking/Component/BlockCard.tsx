import React, { CSSProperties, memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './blockCardC2xStyles.module.scss';
import { UITestID } from '../../../Constants/Index';

interface IBlockCard {
  title: string;
  desc: string;
  onPress?: () => void;
  isShowArrow?: boolean;
  testID?: string;
  style?: CSSProperties;
}
const BlockCard: React.FC<IBlockCard> = memo(
  ({ title, desc, onPress, isShowArrow = false, testID, style }) => {
    if (!title || !desc) return null;
    return (
      <View testID={testID}>
        <BbkTouchable
          testID={UITestID.car_testid_page_booking_extras}
          onPress={onPress}
          disabled={!onPress}
          className={c2xStyles.container}
          style={style}
        >
          <View className={c2xStyles.wrap}>
            <Text className={c2xStyles.title}>{title}</Text>
            <Text numberOfLines={1} className={c2xStyles.desc}>
              {desc}
            </Text>
          </View>
          {isShowArrow && (
            <Text type="icon" className={c2xStyles.icon}>
              {icon.arrowRight}
            </Text>
          )}
        </BbkTouchable>
      </View>
    );
  },
);
export default BlockCard;
