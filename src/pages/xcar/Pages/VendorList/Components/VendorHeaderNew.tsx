import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import { font, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './vendorHeaderNewC2xStyles.module.scss';
import NumberSplitText from '../../../ComponentBusiness/NumberSplitText/index';
import { OptimizationStrengthenNew } from '../../../ComponentBusiness/OptimizationStrengthen';
import Texts from '../Texts';
import { IVendorHeader } from '../Types';
import UITestId from '../../../Constants/UITestID';
import TextIncludeSoldOut from './TextIncludeSoldOut';
import { GetAB, Utils } from '../../../Util/Index';

const { getPixel, isIos, vw } = BbkUtils;
const getMaxByteStr = (str: string, maxByteLength: number) => {
  if (Utils.getByteLength(str) <= maxByteLength) return str;
  return `${Utils.getByteLengthStr(str, maxByteLength - 2)}...`;
};
const styles = StyleSheet.create({
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  vendorNameNew: {
    ...font.F_30_10_medium,
    color: color.C_111111,
  },
  scoreNumNew: {
    ...font.F_28_10_regular_TripNumberMedium,
    color: color.deepBlueBase,
    top: getPixel(isIos ? 0 : 3),
  },
  scoreText: {
    ...font.F_26_10_regular,
    marginLeft: getPixel(2),
  },
  scoreTextNew: {
    ...(isIos ? font.F_26_10_medium : font.F_26_10_regular),
    color: color.deepBlueBase,
  },
  optimizationStrengthenDotStyle: {
    backgroundColor: color.C_111111,
  },
  optimizationStrengthenVendorNameStyle: {
    marginTop: getPixel(isIos ? 3 : -1),
  },
  arrowRightStyle: {
    marginLeft: getPixel(8),
    marginTop: getPixel(isIos ? 2 : -2),
  },
});

const VendorHeader: React.FC<IVendorHeader> = ({
  vendorName,
  isOptimize,
  nationalChainTagTitle,
  score,
  commentDesc,
  theme,
  onPressReview,
  showRightIcon = true,
  arrowRightStyle,
  rightBtn,
  onPressRightBtn,
  wrapStyle,
  vendorNameStyle,
  optimizationStrengthenImgStyle,
  isShowOptimizationStrengthenRightArrow,
  isHasBookBtn,
  onPressVendor,
}) => {
  let venderNameLimitWidth = vw(100);
  if (!isHasBookBtn) {
    venderNameLimitWidth -= getPixel(136) + getPixel(24 * 2) + getPixel(4); // 列表页置顶车型时，车型组宽度
  } else {
    venderNameLimitWidth -=
      getPixel(24 * 2) + getPixel(32 * 2) + getPixel(26 + 8) + getPixel(4); // 间距+供应商箭头宽度
  }

  if (isOptimize) {
    venderNameLimitWidth -= getPixel(156 + 4); // 优选图片 + 点
  } else if (nationalChainTagTitle) {
    venderNameLimitWidth -= getPixel(
      8 + 8 * 2 + Utils.getByteLength(nationalChainTagTitle) * 12,
    ); // 全国连锁标签宽度及间距
  }
  if (!rightBtn) {
    if (score) {
      venderNameLimitWidth -= getPixel(
        Utils.getByteLength(score) * 14 + 26 + 11,
      ); // 点评分数+分字宽度
    } else {
      venderNameLimitWidth -= getPixel(
        Utils.getByteLength(Texts.noScore) * 13 + 2 + 11,
      ); // 无点评文案宽度
    }
    if (commentDesc) {
      const commentDescLength = Utils.getByteLength(commentDesc);
      venderNameLimitWidth -= getPixel(
        8 + (commentDescLength - 4) * 16 + 4 * 13,
      ); // 点评描述宽度
    }
  }
  const vendorNameMaxByte = Math.floor(venderNameLimitWidth / getPixel(15));
  const { soldOutTextColor, soldOutLabelBgColor } = theme;
  const soldOutTextStyle = soldOutTextColor && { color: soldOutTextColor };
  const getVendorAscription = () => {
    if (nationalChainTagTitle) {
      return (
        <View
          className={c2xStyles.famousBrandWrap}
          style={
            soldOutLabelBgColor && { backgroundColor: soldOutLabelBgColor }
          }
        >
          <BbkText
            className={c2xStyles.famousBrandText}
            style={soldOutTextColor && { color: soldOutTextColor }}
          >
            {nationalChainTagTitle}
          </BbkText>
        </View>
      );
    }
    return null;
  };

  const getVendorInfo = () => {
    if (isOptimize) {
      return (
        <OptimizationStrengthenNew
          vendorName={vendorName}
          imgStyle={optimizationStrengthenImgStyle}
          dotStyle={styles.optimizationStrengthenDotStyle}
          vendorNameStyle={styles.optimizationStrengthenVendorNameStyle}
          isShowRightArrow={isShowOptimizationStrengthenRightArrow}
          vendorNameMaxByte={vendorNameMaxByte}
          arrowRightStyle={styles.arrowRightStyle}
          onPressVendor={onPressVendor}
        />
      );
    }
    return (
      <BbkTouchable
        testID={UITestId.car_testid_vendorheader}
        style={styles.leftSection}
        onPress={onPressVendor}
      >
        <TextIncludeSoldOut
          style={xMergeStyles([styles.vendorNameNew, vendorNameStyle])}
          text={getMaxByteStr(vendorName, vendorNameMaxByte)}
          testID={UITestId.car_testid_comp_vendor_name}
        />

        {getVendorAscription()}
        {!soldOutTextColor && showRightIcon && (
          <BbkText
            type="icon"
            className={c2xStyles.arrowRightNew}
            style={arrowRightStyle}
          >
            {icon.right_arrow}
          </BbkText>
        )}
      </BbkTouchable>
    );
  };

  return (
    <View className={c2xStyles.wrap} style={wrapStyle}>
      {getVendorInfo()}
      {rightBtn ? (
        <BbkTouchable
          testID={UITestId.car_testid_vendorheader_righticon}
          className={c2xStyles.rightSection}
          onPress={onPressRightBtn}
        >
          <BbkText className={c2xStyles.rightBtnNew}>{rightBtn}</BbkText>
        </BbkTouchable>
      ) : (
        <BbkTouchable
          onPress={onPressReview}
          disabled={!commentDesc || !!soldOutTextColor} // 暂无评论或者售罄的情况下不可点击
          testID={UITestId.car_testid_comp_vendor_comment}
        >
          <View
            className={classNames(
              c2xStyles.rightSection,
              c2xStyles.scoreContainerWrap,
            )}
          >
            <View className={c2xStyles.scoreWrap}>
              {score ? (
                <>
                  <TextIncludeSoldOut style={styles.scoreNumNew} text={score} />
                  <TextIncludeSoldOut
                    style={styles.scoreTextNew}
                    text={Texts.score}
                  />
                </>
              ) : (
                <TextIncludeSoldOut
                  style={styles.scoreText}
                  text={Texts.noScore}
                />
              )}
            </View>
            {!!commentDesc && (
              <View className={c2xStyles.commentDesc}>
                <NumberSplitText
                  textStyle={soldOutTextStyle}
                  text={commentDesc}
                  fontSize={26}
                />
              </View>
            )}
          </View>
        </BbkTouchable>
      )}
    </View>
  );
};

export default withTheme(VendorHeader);
