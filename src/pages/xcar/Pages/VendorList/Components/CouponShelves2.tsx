import React, { useMemo } from 'react';
import { XView as View, xClassNames as classNames } from '@ctrip/xtaro';
import Image from '@c2x/components/Image';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './CouponShelves2.module.scss';
import * as ImageUrl from '../../../Constants/ImageUrl';
import { CarLog, AppContext } from '../../../Util/Index';
import Channel from '../../../Util/Channel';
import { LogKey } from '../../../Constants/Index';
import {
  getServerRequestId,
  getListRequestId,
} from '../../../Global/Cache/ListReqAndResData';

const { useMemoizedFn, isAndroid } = BbkUtils;

interface CouponShelves2Props {
  couponRenderData: any[];
  onPress?: () => void;
  isReceiveAble?: boolean;
  vehicleCode?: string;
}
export default function CouponShelves2(porps: CouponShelves2Props) {
  const { couponRenderData = [], onPress, isReceiveAble, vehicleCode } = porps;

  const isBLonger = useMemo(() => {
    return couponRenderData?.[1] > couponRenderData?.[0];
  }, [couponRenderData]);

  const handlePressBanner = useMemoizedFn(() => {
    onPress();
    if (isReceiveAble) {
      CarLog.LogCode({
        name: '点击_租车券包_领取按钮',
        pageId: Channel.getPageId().VendorList.ID,
        vehicleCode,
      });
      CarLog.LogTrace({
        key: LogKey.cm_car_app_click_airanking,
        info: {
          name: '点击_租车券包_领取按钮',
          newMergeId: AppContext.currentNewMergeId,
          vehicleId: vehicleCode,
          serverRequestId: getServerRequestId(),
          requestId: getListRequestId(),
        },
      });
    } else {
      CarLog.LogCode({
        name: '点击_产品详情页_打开券包弹层',
        pageId: Channel.getPageId().VendorList.ID,
        vehicleCode,
      });
    }
  });

  return (
    <View className={c2xStyles.couponContainer}>
      <Image
        className={c2xStyles.couponImgContainer}
        source={{
          uri: `${ImageUrl.DIMG04_PATH}1tg5412000j846sme1521.png`,
        }}
        resizeMode="cover"
      />
      <View className={c2xStyles.couponMessageContainer}>
        <View className={isBLonger ? c2xStyles.couponMessageTextContainer : ''}>
          <BbkText
            className={c2xStyles.couponMessageText}
            numberOfLines={isBLonger ? 1 : undefined}
          >
            {couponRenderData?.[0]}
          </BbkText>
        </View>
        {couponRenderData?.[1] && (
          <Image
            className={c2xStyles.couponSpliceLine}
            source={{
              uri: `${ImageUrl.DIMG04_PATH}1tg5q12000j84jmc44730.png`,
            }}
            resizeMode="cover"
          />
        )}
        {couponRenderData?.[1] && (
          <View
            className={!isBLonger ? c2xStyles.couponMessageTextContainer : ''}
          >
            <BbkText
              className={c2xStyles.couponMessageText}
              numberOfLines={!isBLonger ? 1 : undefined}
            >
              {couponRenderData?.[1]}
            </BbkText>
          </View>
        )}
      </View>
      <Touchable className={c2xStyles.btnContainer} onPress={handlePressBanner}>
        {isReceiveAble ? (
          <View className={c2xStyles.receiveBtn}>
            <BbkText
              fontWeight="bold"
              className={classNames(
                c2xStyles.rBtnTxt,
                isAndroid && c2xStyles.mgt1,
              )}
            >
              领取
            </BbkText>
          </View>
        ) : (
          <>
            <BbkText className={c2xStyles.btnText}>查看</BbkText>
            <BbkText className={c2xStyles.iconStyle} type="icon">
              {icon.arrowRight}
            </BbkText>
          </>
        )}
      </Touchable>
    </View>
  );
}
