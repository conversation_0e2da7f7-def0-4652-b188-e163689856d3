import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useCallback, useMemo } from 'react';
import {
  XView as View,
  XImageBackground as ImageBackground,
  XLinearGradient as LinearGradient,
  xMergeStyles,
  XViewExposure,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import BBkThemingProvider, {
  withTheme,
} from '@ctrip/rn_com_car/dist/src/Theming';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import { CarLog, Utils } from '../../../Util/Index';
import { ImageUrl, LogKey, UITestID } from '../../../Constants/Index';
import { getServerRequestId } from '../../../Global/Cache/ListReqAndResData';
import VendorTag from '../../../ComponentBusiness/VendorTag';
import VendorHeader from './VendorHeader';
import PickUpDropOffDesc from '../../../ComponentBusiness/VendorListEnter/src/PickUpDropOffDesc';
import { IVendor, IVendorPrice, IClickVendorArea } from '../Types';
import Texts from '../Texts';
import { Enums } from '../../../ComponentBusiness/Common';

import UITestId from '../../../Constants/UITestID';
import c2xStyles from './vendorC2xStyles.module.scss';

const { getPixel, isAndroid, isIos, isHarmony, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  mtf16: { marginTop: getPixel(-16) },
  showAtTopWrap: { paddingTop: getPixel(60) },
  totalCurrencyStyle: {
    color: color.fontSecondary,
    marginRight: 0,
    ...font.labelLStyle,
  },
  totalPriceStyle: {
    color: color.fontSecondary,
    marginRight: 0,
    ...font.caption1LightStyle,
    height: getPixel(20),
  },
  mt12: { marginTop: getPixel(12) },
  couponBookBtnText: { ...font.couponBookTitle },
  middleWrapModification: { paddingBottom: getPixel(30) },
  vendorPriceWrapNoMarketTags: { marginBottom: getPixel(-9) },
  originDayPrice: {
    textDecorationLine: 'line-through',
    ...font.labelLStyle,
    color: color.fontSecondary,
  },
  originDayPriceWrap: { marginBottom: getPixel(BbkUtils.isAndroid ? 2 : 4) },
  dayPriceCurrencyStyle: {
    ...font.caption1LightStyle,
    color: color.orangePrice,
    lineHeight: getLineHeight(40),
  },
  dayPriceStyle: { color: color.orangePrice, ...font.title4BoldStyle },
  totalIcon: {
    marginTop: getPixel(BbkUtils.isAndroid ? 3 : 0),
    fontSize: getPixel(26),
    ...font.labelLStyle,
    color: color.fontSecondary,
  },
  helpIcon: {
    width: getPixel(45),
    height: getPixel(40),
    alignItems: 'flex-end',
    justifyContent: 'center',
    marginTop: -getPixel(isAndroid ? 2 : 0),
    paddingLeft: getPixel(6),
    paddingRight: getPixel(4),
  },
  helpIconUpgrade: {
    paddingLeft: getPixel(6),
    paddingRight: getPixel(4),
    marginTop: -getPixel(isAndroid ? 2 : 0),
  },
  soldOutStyle: { color: color.darkGrayBorder },
  totalPriceWrapStyle: { paddingTop: getPixel(isIos || isHarmony ? 0 : 6) },
  newCarLabelWrapper: {
    borderRadius: getPixel(4),
    overflow: 'hidden',
    ...layout.flexRow,
  },
  newCarLabelBg: { width: getPixel(322), height: getPixel(38) },
  topBorder: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: color.grayBorder,
    position: 'absolute',
    top: getPixel(12),
    left: getPixel(24),
    right: getPixel(16),
  },
});

type INewCarLabel = {
  text: string;
};
const NewCarLabel: React.FC<INewCarLabel> = ({ text }) => (
  <ImageBackground
    source={{ uri: `${ImageUrl.imageComponentPath}new-car-bg.png` }}
    style={xMergeStyles([styles.newCarLabelWrapper, styles.newCarLabelBg])}
    imageStyle={styles.newCarLabelBg}
  >
    <BbkText className={c2xStyles.newCarLabelText}>{text}</BbkText>
  </ImageBackground>
);

export const VendorPriceDesc: React.FC<IVendorPrice> = ({
  dayPrice,
  originDailyPrice,
  dayText,
  totalText,
  totalPrice,
  currentCurrencyCode,
  isSoldOut,
  onPressQuestion,
  wrapStyle,
  renderMarket = Utils.noop,
  isCouponBook = false,
  hasMarketTagsUpgrade,
  singlePriceWrapStyle,
  totalPriceWrapStyle,
}) => {
  const linearUpgradeColors = isCouponBook
    ? [
        color.white,
        color.marketLinearCouponStartColor,
        color.marketLineaCouponEndColor,
      ]
    : [color.white, color.marketLinearStartColor, color.marketLineaEndColor];
  const soldOutColors = [
    color.white,
    color.grayBgSecondary,
    color.grayBgSecondary,
  ];

  return (
    <View className={c2xStyles.vendorPriceWrap} style={wrapStyle}>
      {/** 日价 */}
      <View className={c2xStyles.dayPriceWrap}>
        {originDailyPrice > 0 && (
          <BbkCurrencyFormatter
            price={originDailyPrice}
            currency={currentCurrencyCode}
            wrapperStyle={styles.originDayPriceWrap}
            currencyStyle={xMergeStyles([
              styles.originDayPrice,
              isSoldOut && styles.soldOutStyle,
            ])}
            priceStyle={xMergeStyles([
              styles.originDayPrice,
              isSoldOut && styles.soldOutStyle,
            ])}
            isNew={true}
          />
        )}
        <View className={c2xStyles.dayPriceSubWrap}>
          {!!dayText && (
            <BbkText
              className={c2xStyles.dayText}
              style={isSoldOut && styles.soldOutStyle}
            >
              {dayText}
            </BbkText>
          )}
          <BbkCurrencyFormatter
            price={dayPrice}
            currency={currentCurrencyCode}
            currencyStyle={xMergeStyles([
              styles.dayPriceCurrencyStyle,
              isSoldOut && styles.soldOutStyle,
            ])}
            priceStyle={xMergeStyles([
              styles.dayPriceStyle,
              isSoldOut && styles.soldOutStyle,
            ])}
            wrapperStyle={singlePriceWrapStyle}
            testID={UITestId.car_testid_comp_vendor_day_price}
          />
        </View>
      </View>

      {/** 总价 */}
      <BbkTouchable
        onPress={onPressQuestion}
        testID={UITestID.car_testid_comp_vehicle_price_help}
        className={
          hasMarketTagsUpgrade
            ? c2xStyles.totalPriceWrapUpgrade
            : c2xStyles.totalPriceWrap
        }
      >
        <LinearGradient
          start={{ x: 0.0, y: 1.0 }}
          end={{ x: 1.0, y: 1.0 }}
          locations={[0, 0.15, 1]}
          colors={isSoldOut ? soldOutColors : linearUpgradeColors}
          className={c2xStyles.marketPriceLabel}
        >
          <>{renderMarket()}</>
        </LinearGradient>
        {!!onPressQuestion && !!totalPrice && (
          <BbkTouchable
            disabled={isSoldOut}
            testID={UITestId.car_testid_comp_vendor_price_help}
            onPress={onPressQuestion}
            style={
              hasMarketTagsUpgrade ? styles.helpIconUpgrade : styles.helpIcon
            }
          >
            <BbkText
              type="icon"
              style={xMergeStyles([
                styles.totalIcon,
                isSoldOut && styles.soldOutStyle,
              ])}
            >
              {icon.help}
            </BbkText>
          </BbkTouchable>
        )}
        {!!totalText && (
          <BbkText
            className={c2xStyles.totalTextStyle}
            style={isSoldOut && styles.soldOutStyle}
          >
            {totalText}
          </BbkText>
        )}
        {totalPrice >= 0 && (
          <BbkCurrencyFormatter
            price={totalPrice}
            currency={currentCurrencyCode}
            wrapperStyle={totalPriceWrapStyle}
            currencyStyle={xMergeStyles([
              styles.totalCurrencyStyle,
              isSoldOut && styles.soldOutStyle,
            ])}
            priceStyle={xMergeStyles([
              styles.totalPriceStyle,
              isSoldOut && styles.soldOutStyle,
            ])}
            testID={UITestId.car_testid_comp_vendor_total_price}
          />
        )}
      </BbkTouchable>
    </View>
  );
};

const Vendor: React.FC<IVendor> = ({
  vendorName,
  isOptimize,
  nationalChainTagTitle,
  score,
  commentDesc,
  isShowAtTop,
  wrapStyle,
  isShowBtnIcon,
  vehicleTags,
  serviceTags,
  marketTags,
  hint,
  newCar,
  priceDescProps,
  isSoldOut,
  almostSoldOutLabel,
  uniqueCode,
  onPressVendor,
  onPressBooking,
  onPressQuestion,
  onPressReview,
  clickLogData,
  exposureLogData,
  showRightIcon,
  hasFees,
  isCouponBook,
  skuId,
  pickUpDesc,
  dropOffDesc,
  isPickUpRentCenter,
  isDropOffRentCenter,
  pickUpRentCenterName,
  dropOffRentCenterName,
  isSelfService,
  showTopBorder,
}) => {
  const soldOutBtnBackgroundColor = isSoldOut && color.darkGrayBorder;
  const hasVehicleTags = vehicleTags?.length > 0;
  const hasServiceTags = serviceTags?.length > 0;
  const hasMarketTags = marketTags?.length > 0;
  const hasMarketTagsUpgrade = false;
  const getBottomLeftDom = () => {
    if (isSoldOut) {
      return (
        <BbkText className={c2xStyles.soldOutTextStyle}>
          {Texts.soldOutText}
        </BbkText>
      );
    }
    // almostSoldOutLabel 就是服务端返回的urge 低价 库存文案
    if (almostSoldOutLabel) {
      return (
        <BbkText
          className={c2xStyles.almostSoldOutLabelStyle}
          style={isSoldOut && styles.soldOutStyle}
        >
          {almostSoldOutLabel}
        </BbkText>
      );
    }
    if (newCar && hint) {
      return <NewCarLabel text={hint} />;
    }
    if (hint) {
      return (
        <View className={c2xStyles.hintWrap}>
          <BbkText
            className={c2xStyles.hintText}
            style={isSoldOut && styles.soldOutStyle}
          >
            {hint}
          </BbkText>
        </View>
      );
    }
    return null;
  };

  const clickLog = useCallback(
    (buttonValue: IClickVendorArea) => {
      CarLog.LogCode({
        name: '点击_产品详情页_供应商报价',

        button: buttonValue,
        abVersion: clickLogData.abVersion,
        data: clickLogData,
        info: {
          isSelfService: isSelfService ? 1 : 0,
        },
      });
      CarLog.LogTrace({
        key: LogKey.c_rn_car_trace_click,
        info: {
          name: '点击_产品详情页_供应商报价',
          severRequestId: getServerRequestId(),
        },
      });
    },
    [clickLogData, isSelfService],
  );

  const getVendorExposureData = () => ({
    key: LogKey.c_car_exposure_product_quote_10650068646,
    ...exposureLogData,
  });

  const handlePressVendor = useCallback(() => {
    onPressVendor(uniqueCode, skuId);
    clickLog(IClickVendorArea.Vendor);
  }, [onPressVendor, uniqueCode, skuId, clickLog]);

  const handlePressBooking = useCallback(() => {
    onPressBooking(Enums.TotalPriceModalType.Vendor, uniqueCode);
    clickLog(
      isCouponBook ? IClickVendorArea.CouponBook : IClickVendorArea.Booking,
    );
  }, [clickLog, isCouponBook, onPressBooking, uniqueCode]);

  const handlePressQuestion = useCallback(() => {
    onPressQuestion(Enums.TotalPriceModalType.Vendor, uniqueCode, hasFees);
    clickLog(IClickVendorArea.Question);
  }, [clickLog, hasFees, onPressQuestion, uniqueCode]);

  const handlePressReview = useCallback(() => {
    onPressReview(uniqueCode);
    clickLog(IClickVendorArea.Review);
  }, [clickLog, onPressReview, uniqueCode]);

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const renderMarket = (show, isCouponBook = false) => {
    if (show) {
      return (
        <BbkTouchable
          debounce={true}
          debounceTime={1500}
          onPress={handlePressBooking}
          disabled={isSoldOut}
          className={c2xStyles.marketTagsWrap}
        >
          <XViewExposure
            testID={CarLog.LogExposure({
              name: '曝光_详情页_供应商_营销标签',

              info: { labelCode: marketTags?.[0]?.labelCode },
            })}
          >
            <VendorTag
              tags={marketTags}
              isCouponBook={isCouponBook}
              isSoldOut={isSoldOut}
            />
          </XViewExposure>
        </BbkTouchable>
      );
    }
    return null;
  };

  const renderMarketUpgrade = () => {
    return renderMarket(hasMarketTagsUpgrade, isCouponBook);
  };

  const getVendorButtonColors = useMemo(() => {
    let buttonColors = [color.linearGradientOrangeOrderLight, color.orangeBase];
    if (soldOutBtnBackgroundColor) {
      buttonColors = [soldOutBtnBackgroundColor, soldOutBtnBackgroundColor];
    } else if (isCouponBook) {
      buttonColors = [color.orangeBase, color.linearGradientCouponBookEnd];
    }
    return buttonColors;
  }, [soldOutBtnBackgroundColor, isCouponBook]);

  return (
    <XViewExposure
      testID={CarLog.LogExposure(getVendorExposureData())}
      className={c2xStyles.container}
      style={showTopBorder && styles.mtf16}
    >
      <BbkTouchable
        debounce={true}
        onPress={handlePressVendor}
        disabled={isSoldOut}
        testID={UITestId.car_testid_page_vendor_card}
        className={c2xStyles.wrap}
        style={xMergeStyles([isShowAtTop && styles.showAtTopWrap, wrapStyle])}
      >
        {!!showTopBorder && <View style={styles.topBorder} />}
        {isShowAtTop && (
          <Image
            src={`${ImageUrl.BBK_IMAGE_PATH}<EMAIL>`}
            mode="aspectFill"
            className={c2xStyles.topImage}
          />
        )}
        <VendorHeader
          vendorName={vendorName}
          isOptimize={isOptimize}
          nationalChainTagTitle={nationalChainTagTitle}
          score={score}
          commentDesc={commentDesc}
          onPressReview={handlePressReview}
          showRightIcon={showRightIcon}
        />

        <PickUpDropOffDesc
          pickUpDesc={pickUpDesc}
          dropOffDesc={dropOffDesc}
          isPickUpRentCenter={isPickUpRentCenter}
          isDropOffRentCenter={isDropOffRentCenter}
          pickUpRentCenterName={pickUpRentCenterName}
          dropOffRentCenterName={dropOffRentCenterName}
        />

        {hasVehicleTags && (
          <View className={c2xStyles.vehicleTagsWrap}>
            <VendorTag tags={vehicleTags} isSoldOut={isSoldOut} />
          </View>
        )}
        <View
          className={c2xStyles.bottomWrap}
          style={!hasVehicleTags && styles.mt12}
        >
          <View className={c2xStyles.bottomContentWrap}>
            {renderMarket(hasMarketTags)}
            <View className={c2xStyles.leftWrap}>
              {hasServiceTags && (
                <VendorTag tags={serviceTags} isSoldOut={isSoldOut} />
              )}
              <View className={c2xStyles.leftBottomWrap}>
                {getBottomLeftDom()}
              </View>
            </View>
            <BbkTouchable
              debounce={true}
              debounceTime={1500}
              onPress={handlePressBooking}
              disabled={isSoldOut}
              className={c2xStyles.middleWrap}
              style={hasMarketTags && styles.middleWrapModification}
            >
              {!!priceDescProps && (
                <VendorPriceDesc
                  {...priceDescProps}
                  hasMarketTagsUpgrade={hasMarketTagsUpgrade}
                  isCouponBook={isCouponBook}
                  renderMarket={renderMarketUpgrade}
                  onPressQuestion={handlePressQuestion}
                  isSoldOut={isSoldOut}
                  wrapStyle={xMergeStyles([
                    !hasMarketTags && styles.vendorPriceWrapNoMarketTags,
                  ])}
                  singlePriceWrapStyle={null}
                  totalPriceWrapStyle={styles.totalPriceWrapStyle}
                />
              )}
            </BbkTouchable>
          </View>
          {isShowBtnIcon ? (
            <Image
              src={`${ImageUrl.imageComponentPath}moreRightIcon.png`}
              mode="aspectFit"
              className={c2xStyles.bookIconWrap}
            />
          ) : (
            <BbkTouchable
              onPress={handlePressBooking}
              debounce={true}
              debounceTime={1500}
              disabled={isSoldOut}
              testID={UITestId.car_testid_comp_vendor_list_book}
            >
              <LinearGradient
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 1.0 }}
                locations={[0, 1]}
                colors={getVendorButtonColors}
                className={c2xStyles.bookBtnWrap}
              >
                <BbkText
                  className={c2xStyles.bookBtnText}
                  style={isCouponBook && styles.couponBookBtnText}
                >
                  {isCouponBook ? Texts.couponBook : Texts.book}
                </BbkText>
              </LinearGradient>
            </BbkTouchable>
          )}
        </View>
      </BbkTouchable>
    </XViewExposure>
  );
};

const soldOutTheme = {
  soldOutTextColor: color.darkGrayBorder,
  soldOutLabelBgColor: color.soldOutLabelBgColor,
  soldOutTagBgColor: color.grayBgSecondary,
};

export default memo(
  withTheme(props => {
    const { isSoldOut } = props;
    const VendorDom = <Vendor {...props} isSoldOut={isSoldOut} />;
    if (isSoldOut) {
      return (
        <BBkThemingProvider theme={soldOutTheme}>
          {VendorDom}
        </BBkThemingProvider>
      );
    }
    return VendorDom;
  }),
);
