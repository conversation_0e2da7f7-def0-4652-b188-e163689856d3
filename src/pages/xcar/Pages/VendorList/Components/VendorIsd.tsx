import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useCallback, useMemo, useRef, useState } from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import BBkThemingProvider, {
  withTheme,
} from '@ctrip/rn_com_car/dist/src/Theming';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import { CarLog, GetAB, Utils } from '../../../Util/Index';
import { ImageUrl, LogKey, UITestID } from '../../../Constants/Index';
import VendorTag from '../../../ComponentBusiness/VendorTag';
import VendorHeader from './VendorHeader';
import PickUpDropOffDesc from '../../../ComponentBusiness/VendorListEnter/src/PickUpDropOffDesc';
import {
  IVendor,
  IVendorPrice,
  IClickVendorArea,
  INewCarLabel,
} from '../Types';
import Texts from '../Texts';
import { Enums } from '../../../ComponentBusiness/Common';

import UITestId from '../../../Constants/UITestID';
import c2xStyles from './vendorIsdC2xStyles.module.scss';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, isAndroid, useMemoizedFn, isHarmony, getLineHeight } =
  BbkUtils;
const styles = StyleSheet.create({
  showAtTopWrap: { paddingTop: getPixel(60) },
  serviceLabelStyle: { marginBottom: getPixel(10) },
  totalCurrencyStyle: {
    color: color.fontSecondary,
    marginRight: 0,
    ...font.labelLStyle,
  },
  totalPriceStyle: {
    color: color.fontSecondary,
    marginRight: 0,
    ...font.caption1LightStyle,
    height: getPixel(20),
  },
  mt12: { marginTop: getPixel(12) },
  couponBookBtnText: { ...font.couponBookTitle },
  marketLabelStyle: { marginBottom: 0, height: getPixel(34) },
  originDayPrice: {
    textDecorationLine: 'line-through',
    ...font.labelXLStyle,
    color: color.fontSecondary,
  },
  originDayPriceWrap: { marginBottom: getPixel(2), marginRight: getPixel(4) },
  dayPriceCurrencyStyle: {
    ...font.caption1LightStyle,
    color: color.deepBlueBase,
    lineHeight: getLineHeight(40),
  },
  dayPriceStyle: {
    color: color.deepBlueBase,
    ...font.F_32_10_regular_TripNumberSemiBold,
  },
  marketLabelAndPriceWrap: {
    borderTopLeftRadius: getPixel(4),
    borderBottomLeftRadius: getPixel(4),
    height: getPixel(34),
    flexDirection: 'row',
  },
  questionWrap: {
    flexDirection: 'row', // harmony
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  soldOutStyle: { color: color.darkGrayBorder },
  totalPriceWrapStyle: { paddingTop: getPixel(isAndroid ? 2 : 1) },
  fixHarmonyPT: { paddingTop: getPixel(0) },
  gapLine: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: color.grayBorder,
    width: getPixel(566),
    marginLeft: getPixel(22),
  },
});

const newCarLinearColors = [color.C_E4F9F2, color.white];
const newCarGrayLinearColors = [color.C_eee, color.white];

const NewCarLabel: React.FC<INewCarLabel> = ({
  text,
  isSoldOut,
  isOverRow,
}) => (
  <View
    className={isOverRow ? c2xStyles.newCarWrapOverRow : c2xStyles.newCarWrap}
  >
    <LinearGradient
      start={{ x: 0.0, y: 0.0 }}
      end={{ x: 1.0, y: 1.0 }}
      locations={[0, 1]}
      colors={isSoldOut ? newCarGrayLinearColors : newCarLinearColors}
      className={c2xStyles.newCarLabelWrap}
    >
      <Image
        src={`${ImageUrl.DIMG04_PATH}1tg4t12000cxv1jgy6101.png`}
        className={c2xStyles.newCarLabelImage}
      />

      <BbkText className={c2xStyles.newCarText}>{Texts.newCar}</BbkText>
      <BbkText className={c2xStyles.newCarLabelText}>{text}</BbkText>
    </LinearGradient>
  </View>
);

interface IMoreRightIcon {} // 供应商车型置顶模块右箭头
const MoreRightIcon: React.FC<IMoreRightIcon> = () => {
  return (
    <BbkText type="icon" className={c2xStyles.arrowRightList}>
      {icon.arrowRightList}
    </BbkText>
  );
};
const marketLinearColors = [color.C_FFEDE8, color.white];
const marketGrayLinearColors = [color.C_eee, color.white];
const soldOutBtnLinearColors = [color.C_eee, color.C_999];
export const VendorPriceDesc: React.FC<IVendorPrice> = ({
  dayPrice,
  originDailyPrice,
  dayText,
  totalText,
  totalPrice,
  currentCurrencyCode,
  isSoldOut,
  handlePressBooking,
  onPressQuestion,
  wrapStyle,
  hasMarketTags,
  renderMarket = Utils.noop,
  bottomRightContentLayout = Utils.noop,
  singlePriceWrapStyle,
  totalPriceWrapStyle,
  isShowBtnIcon,
  isStorageNumTip,
  almostSoldOutLabel,
  isCouponBook,
  vendorButtonColors,
}) => {
  const MarketLabelAndPriceContainer: any = hasMarketTags
    ? LinearGradient
    : View;
  const MarketLabelAndPriceProps = hasMarketTags
    ? {
        start: { x: 0.0, y: 0.0 },
        end: { x: 1.0, y: 1.0 },
        locations: [0, 1],
        colors: isSoldOut ? marketGrayLinearColors : marketLinearColors,
      }
    : {};
  return (
    <View className={c2xStyles.vendorPriceWrap} style={wrapStyle}>
      <BbkTouchable
        onLayout={bottomRightContentLayout}
        debounce={true}
        onPress={handlePressBooking}
        testID={UITestID.car_testid_page_list_vendor_item_priceinfo}
        disabled={isSoldOut}
        className={c2xStyles.bookingWrap}
      >
        <View>
          {/** 日价 */}
          <View className={c2xStyles.dayPriceWrap}>
            {originDailyPrice > 0 && (
              <BbkCurrencyFormatter
                price={originDailyPrice}
                currency={currentCurrencyCode}
                wrapperStyle={styles.originDayPriceWrap}
                currencyStyle={xMergeStyles([
                  styles.originDayPrice,
                  isSoldOut && styles.soldOutStyle,
                ])}
                priceStyle={xMergeStyles([
                  styles.originDayPrice,
                  isSoldOut && styles.soldOutStyle,
                ])}
                isNew={true}
              />
            )}
            <View className={c2xStyles.dayPriceSubWrap}>
              {!!dayText && (
                <BbkText
                  className={classNames(
                    c2xCommonStyles.c2xTextDefaultCss,
                    isAndroid ? c2xStyles.dayText : c2xStyles.dayTextIos,
                  )}
                  style={isSoldOut && styles.soldOutStyle}
                >
                  {dayText}
                </BbkText>
              )}
              <BbkCurrencyFormatter
                price={dayPrice}
                currency={currentCurrencyCode}
                currencyStyle={xMergeStyles([
                  styles.dayPriceCurrencyStyle,
                  isSoldOut && styles.soldOutStyle,
                ])}
                priceStyle={xMergeStyles([
                  styles.dayPriceStyle,
                  isSoldOut && styles.soldOutStyle,
                ])}
                wrapperStyle={singlePriceWrapStyle}
                testID={UITestId.car_testid_comp_vendor_day_price}
              />
            </View>
          </View>
          {/** 总价 */}
          <View className={c2xStyles.totalPriceWrap}>
            <MarketLabelAndPriceContainer
              style={styles.marketLabelAndPriceWrap}
              {...MarketLabelAndPriceProps}
            >
              {renderMarket()}
              <View
                style={styles.questionWrap}
                onPress={onPressQuestion}
                testID={UITestID.car_testid_comp_vehicle_price_help}
              >
                <BbkTouchable
                  style={layout.flexRow}
                  onPress={onPressQuestion}
                  testID={
                    UITestID.car_testid_page_list_vendor_item_priceinfo_price
                  }
                >
                  {!!onPressQuestion && !!totalPrice && (
                    <BbkTouchable
                      disabled={isSoldOut}
                      testID={UITestId.car_testid_comp_vendor_price_help}
                      onPress={onPressQuestion}
                      className={c2xStyles.helpIcon}
                    >
                      <BbkText
                        type="icon"
                        className={c2xStyles.totalIcon}
                        style={isSoldOut && styles.soldOutStyle}
                      >
                        {icon.help}
                      </BbkText>
                    </BbkTouchable>
                  )}
                  {!!totalText && (
                    <BbkText
                      className={c2xStyles.totalTextStyle}
                      style={isSoldOut && styles.soldOutStyle}
                    >
                      {totalText}
                    </BbkText>
                  )}
                </BbkTouchable>
                {totalPrice >= 0 && (
                  <BbkCurrencyFormatter
                    price={totalPrice}
                    currency={currentCurrencyCode}
                    wrapperStyle={totalPriceWrapStyle}
                    currencyStyle={xMergeStyles([
                      styles.totalCurrencyStyle,
                      isSoldOut && styles.soldOutStyle,
                    ])}
                    priceStyle={xMergeStyles([
                      styles.totalPriceStyle,
                      isSoldOut && styles.soldOutStyle,
                    ])}
                    testID={UITestId.car_testid_comp_vendor_total_price}
                  />
                )}
              </View>
            </MarketLabelAndPriceContainer>
          </View>
        </View>
        {isShowBtnIcon ? (
          <MoreRightIcon />
        ) : (
          <>
            {isStorageNumTip && ( // 是库存文案的话展示在预订按钮上方
              <View className={c2xStyles.storageNumWrap}>
                <BbkText
                  className={c2xStyles.storageNumStyle}
                  style={isSoldOut && styles.soldOutStyle}
                >
                  {almostSoldOutLabel}
                </BbkText>
              </View>
            )}
            <BbkTouchable
              onPress={handlePressBooking}
              debounce={true}
              disabled={isSoldOut}
              testID={UITestId.car_testid_comp_vendor_list_book}
            >
              <LinearGradient
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 1.0 }}
                locations={[0, 1]}
                colors={vendorButtonColors}
                className={
                  isCouponBook
                    ? c2xStyles.bookBtnWrap
                    : c2xStyles.bookBtnWrapNew
                }
              >
                <BbkText
                  className={c2xStyles.bookBtnText}
                  style={isCouponBook && styles.couponBookBtnText}
                >
                  {isCouponBook ? Texts.couponBook : Texts.book}
                </BbkText>
                {!isCouponBook && (
                  <Image
                    src={
                      isSoldOut
                        ? `${ImageUrl.DIMG04_PATH}1tg4d12000d0mx9c498D3.png`
                        : `${ImageUrl.DIMG04_PATH}1tg2f12000cxv24f4A980.png`
                    }
                    className={c2xStyles.bookingBlueBtn}
                  />
                )}
              </LinearGradient>
            </BbkTouchable>
          </>
        )}
      </BbkTouchable>
    </View>
  );
};
const Vendor: React.FC<IVendor> = ({
  vendorName,
  isOptimize,
  nationalChainTagTitle,
  score,
  commentDesc,
  isShowAtTop,
  vendorContainerStyle,
  wrapStyle,
  isShowBtnIcon,
  vehicleTags,
  serviceTags,
  marketTags,
  hint,
  newCar,
  priceDescProps,
  isSoldOut,
  almostSoldOutLabel,
  uniqueCode,
  onPressVendor,
  onPressBooking,
  onPressQuestion,
  onPressReview,
  clickLogData,
  exposureLogData,
  showRightIcon,
  hasFees,
  isCouponBook,
  skuId,
  pickUpDesc,
  dropOffDesc,
  isPickUpRentCenter,
  isDropOffRentCenter,
  optimizationStrengthenVendorNameStyle,
  isSelfService,
  vendorNameMaxByte,
}) => {
  const bottomContentWrap = useRef(0);
  const bottomLeftContentWrap = useRef(0);
  const bottomRightContentWrap = useRef(0);
  const [isOverRow, setIsOverRow] = useState(false);
  const soldOutBtnBackgroundColor = isSoldOut && color.darkGrayBorder;
  const hasVehicleTags = vehicleTags?.length > 0;
  const hasServiceTags = serviceTags?.length > 0;
  const hasMarketTags = marketTags?.length > 0; // 是否是库存提示文案，通过文案中是否包含 辆 来判断, 因为almostSoldOutLabel字段的定义是 低价 库存文案。
  const isStorageNumTip = almostSoldOutLabel?.indexOf(Texts.carUnit) > -1;
  const getBottomLeftDom = () => {
    if (isSoldOut) {
      return (
        <BbkText className={c2xStyles.soldOutTextStyle}>
          {Texts.soldOutText}
        </BbkText>
      );
    } // almostSoldOutLabel 就是服务端返回的urge 低价 库存文案
    // 如果是库存文案则不展示在底部，而是展示在预订按钮上方
    if (almostSoldOutLabel) {
      // 库存文案的话不展示在左下角
      return isStorageNumTip ? null : (
        <BbkText
          className={c2xStyles.almostSoldOutLabelStyle}
          style={isSoldOut && styles.soldOutStyle}
        >
          {almostSoldOutLabel}
        </BbkText>
      );
    }
    if (newCar && hint) {
      return (
        <NewCarLabel isOverRow={isOverRow} isSoldOut={isSoldOut} text={hint} />
      );
    }
    if (hint) {
      return (
        <View className={c2xStyles.hintWrap}>
          <BbkText
            className={c2xStyles.hintText}
            style={isSoldOut && styles.soldOutStyle}
          >
            {hint}
          </BbkText>
        </View>
      );
    }
    return null;
  };
  const clickLog = useCallback(
    (buttonValue: IClickVendorArea) => {
      CarLog.LogCode({
        name: '点击_产品详情页_供应商报价',
        button: buttonValue,
        abVersion: clickLogData.abVersion,
        data: clickLogData,
        info: { isSelfService: isSelfService ? 1 : 0 },
      });
    },
    [clickLogData, isSelfService],
  );
  const refreshIsOverRow = useMemoizedFn(() => {
    if (
      bottomContentWrap.current > 0 &&
      bottomLeftContentWrap.current + bottomRightContentWrap.current - 1 >
        bottomContentWrap.current
    ) {
      setIsOverRow(true);
    }
  });
  const bottomContentLayout = useMemoizedFn(event => {
    if (!bottomContentWrap.current) {
      bottomContentWrap.current = event.nativeEvent.layout.width;
    }
    refreshIsOverRow();
  });
  const bottomLeftContentLayout = useMemoizedFn(event => {
    if (!bottomLeftContentWrap.current) {
      bottomLeftContentWrap.current = event.nativeEvent.layout.width;
    }
    refreshIsOverRow();
  });
  const bottomRightContentLayout = useMemoizedFn(event => {
    if (!bottomRightContentWrap.current) {
      bottomRightContentWrap.current = event.nativeEvent.layout.width;
    }
    refreshIsOverRow();
  });
  const getVendorExposureData = () => ({
    key: LogKey.c_car_exposure_product_quote_10650068646,
    ...exposureLogData,
  });
  const handlePressVendor = useCallback(() => {
    onPressVendor(uniqueCode, skuId);
    clickLog(IClickVendorArea.Vendor);
  }, [onPressVendor, uniqueCode, skuId, clickLog]);
  const handlePressBooking = useCallback(() => {
    onPressBooking(Enums.TotalPriceModalType.Vendor, uniqueCode);
    clickLog(
      isCouponBook ? IClickVendorArea.CouponBook : IClickVendorArea.Booking,
    );
  }, [clickLog, isCouponBook, onPressBooking, uniqueCode]);
  const handlePressQuestion = useCallback(() => {
    onPressQuestion(Enums.TotalPriceModalType.Vendor, uniqueCode, hasFees);
    clickLog(IClickVendorArea.Question);
  }, [clickLog, hasFees, onPressQuestion, uniqueCode]);
  const handlePressReview = useCallback(() => {
    onPressReview(uniqueCode);
    clickLog(IClickVendorArea.Review);
  }, [clickLog, onPressReview, uniqueCode]); // eslint-disable-next-line @typescript-eslint/no-shadow
  const renderMarket = show => {
    if (show) {
      return (
        <BbkTouchable
          debounce={true}
          onPress={handlePressBooking}
          disabled={isSoldOut}
          testID={UITestID.car_testid_page_list_vendor_item_marketlabel}
          className={c2xStyles.marketTagsWrap}
        >
          <XViewExposure
            testID={CarLog.LogExposure({
              name: '曝光_详情页_供应商_营销标签',
              info: { labelCode: marketTags?.[0]?.labelCode },
            })}
          >
            <VendorTag
              labelStyle={styles.marketLabelStyle}
              tags={marketTags}
              isSoldOut={isSoldOut}
            />
          </XViewExposure>
        </BbkTouchable>
      );
    }
    return null;
  };
  const renderMarketUpgrade = () => {
    return renderMarket(true);
  };
  const vendorButtonColors = useMemo(() => {
    let buttonColors = [color.deepBlueBase, color.deepBlueBase];
    if (soldOutBtnBackgroundColor) {
      buttonColors = GetAB.isISDInterestPoints()
        ? soldOutBtnLinearColors
        : [soldOutBtnBackgroundColor, soldOutBtnBackgroundColor];
    } else if (isCouponBook) {
      buttonColors = [color.orangeBase, color.linearGradientCouponBookEnd];
    }
    return buttonColors;
  }, [soldOutBtnBackgroundColor, isCouponBook]);
  return (
    <XViewExposure
      testID={CarLog.LogExposure(getVendorExposureData())}
      className={c2xStyles.container}
      style={vendorContainerStyle}
    >
      <View style={styles.gapLine} />
      <BbkTouchable
        debounce={true}
        onPress={handlePressBooking}
        disabled={isSoldOut}
        testID={UITestId.car_testid_page_vendor_card}
        className={c2xStyles.wrap}
        style={xMergeStyles([isShowAtTop && styles.showAtTopWrap, wrapStyle])}
      >
        {isShowAtTop && (
          <Image
            src={`${ImageUrl.BBK_IMAGE_PATH}<EMAIL>`}
            mode="aspectFill"
            className={c2xStyles.topImage}
          />
        )}
        <View className={c2xStyles.contentWrap}>
          <VendorHeader
            vendorName={vendorName}
            isOptimize={isOptimize}
            nationalChainTagTitle={nationalChainTagTitle}
            score={score}
            commentDesc={commentDesc}
            onPressReview={handlePressReview}
            showRightIcon={showRightIcon}
            optimizationStrengthenVendorNameStyle={
              optimizationStrengthenVendorNameStyle
            }
            isShowOptimizationStrengthenRightArrow={showRightIcon}
            vendorNameMaxByte={vendorNameMaxByte}
            onPressVendor={handlePressVendor}
          />

          <PickUpDropOffDesc
            pickUpDesc={pickUpDesc}
            dropOffDesc={dropOffDesc}
            isPickUpRentCenter={isPickUpRentCenter}
            isDropOffRentCenter={isDropOffRentCenter}
          />

          {hasVehicleTags && (
            <View className={c2xStyles.vehicleTagsWrap}>
              <VendorTag tags={vehicleTags} isSoldOut={isSoldOut} />
            </View>
          )}
          {hasServiceTags && (
            <View
              className={classNames(
                c2xStyles.serviceTagsContainer,
                hasVehicleTags
                  ? c2xStyles.serviceTagsWrap2
                  : c2xStyles.serviceTagsWrap,
              )}
            >
              <VendorTag
                tags={serviceTags}
                labelStyle={styles.serviceLabelStyle}
                isSoldOut={isSoldOut}
              />
            </View>
          )}
        </View>
        <View
          className={c2xStyles.bottomWrap}
          style={!hasVehicleTags && styles.mt12}
        >
          <View
            onLayout={bottomContentLayout}
            className={c2xStyles.bottomContentWrap}
          >
            <View
              onLayout={bottomLeftContentLayout}
              className={
                isOverRow ? c2xStyles.leftWrapOverRow : c2xStyles.leftWrap
              }
            >
              {getBottomLeftDom()}
            </View>
            <View className={c2xStyles.rightWrap}>
              {!!priceDescProps && (
                <VendorPriceDesc
                  {...priceDescProps}
                  bottomRightContentLayout={bottomRightContentLayout}
                  hasMarketTags={hasMarketTags}
                  isCouponBook={isCouponBook}
                  renderMarket={renderMarketUpgrade}
                  onPressQuestion={handlePressQuestion}
                  handlePressBooking={handlePressBooking}
                  isSoldOut={isSoldOut}
                  singlePriceWrapStyle={null}
                  totalPriceWrapStyle={Utils.getPackageStyle([
                    styles.totalPriceWrapStyle,
                    isHarmony && styles.fixHarmonyPT,
                  ])}
                  isShowBtnIcon={isShowBtnIcon}
                  isStorageNumTip={isStorageNumTip}
                  almostSoldOutLabel={almostSoldOutLabel}
                  vendorButtonColors={vendorButtonColors}
                />
              )}
            </View>
          </View>
        </View>
      </BbkTouchable>
    </XViewExposure>
  );
};

const soldOutTheme = {
  soldOutTextColor: color.darkGrayBorder,
  soldOutLabelBgColor: color.soldOutLabelBgColor,
  soldOutTagBgColor: color.grayBgSecondary,
};

export default memo(
  withTheme(props => {
    const { isSoldOut } = props;
    const VendorDom = <Vendor {...props} isSoldOut={isSoldOut} />;
    if (isSoldOut) {
      return (
        <BBkThemingProvider theme={soldOutTheme}>
          {VendorDom}
        </BBkThemingProvider>
      );
    }
    return VendorDom;
  }),
);
