import StyleSheet from '@c2x/apis/StyleSheet';
import Image from '@c2x/components/Image';
import React, { memo, useCallback, useMemo } from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import BBkThemingProvider, {
  withTheme,
} from '@ctrip/rn_com_car/dist/src/Theming';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import * as ImageUrl from '../../../Constants/ImageUrl';
import c2xStyles from './vendorIsdNewC2xStyles.module.scss';
import { AppContext, CarLog, GetAB, Utils } from '../../../Util/Index';
import { LogKey, UITestID } from '../../../Constants/Index';
import {
  MarketLabel,
  VendorLabelISD,
} from '../../../ComponentBusiness/VendorTag';

import VendorHeaderNew from './VendorHeaderNew';
import PickUpDropOffDescNew from '../../../ComponentBusiness/VendorListEnter/src/PickUpDropOffDescNew';
import { IVendor, IVendorPrice, IClickVendorArea } from '../Types';
import Texts from '../Texts';
import { Enums } from '../../../ComponentBusiness/Common';

import UITestId from '../../../Constants/UITestID';
import {
  getListRequestId,
  getServerRequestId,
} from '../../../Global/Cache/ListReqAndResData';

const { getPixel, vw, isIos } = BbkUtils;
const btnWidth = 70;
const btnHeight = 70;
const btnBottom = 6;
const styles = StyleSheet.create({
  vehicleTagsWrap: {
    maxWidth: vw(100) - getPixel(182),
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 0,
  },
  totalCurrencyStyle: {
    color: color.C_888888,
    marginRight: 0,
    ...font.F_26_10_regular_TripNumberRegular,
  },
  totalPriceStyle: {
    color: color.C_888888,
    marginRight: 0,
    ...font.F_28_10_regular_TripNumberRegular,
  },
  bookBtnWrapNew: {
    width: getPixel(btnWidth),
    height: getPixel(btnHeight),
    marginLeft: getPixel(12),
    backgroundColor: color.deepBlueBase,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: getPixel(8),
    marginBottom: getPixel(btnBottom),
  },
  originDayPrice: {
    textDecorationLine: 'line-through',
    ...font.F_28_10_regular_TripNumberRegular,
    color: color.C_888888,
  },
  originDayPriceWrap: {
    marginRight: getPixel(4),
    top: getPixel(isIos ? 2 : 0),
  },
  dayText: {
    ...(isIos ? font.F_26_10_medium : font.F_26_10_regular),
    color: color.deepBlueBase,
    marginRight: getPixel(4),
    marginBottom: getPixel(isIos ? 1 : 4),
  },
  dayPriceCurrencyStyle: {
    ...font.F_28_10_regular,
    color: color.deepBlueBase,
  },
  dayPriceStyle: {
    color: color.deepBlueBase,
    ...font.F_38_10_regular_TripNumberSemiBold,
    lineHeight: getPixel(38),
  },
  singlePriceWrapStyle: {
    top: getPixel(isIos ? 1 : 0),
  },
  marketLabelAndPriceWrap: {
    height: getPixel(38),
    flexDirection: 'row',
  },
  questionWrap: {
    flexDirection: 'row',
    // harmony
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  soldOutStyle: {
    color: color.C_ccc,
  },

  storageNumWrap: {
    position: 'absolute',
    right: 0,
    bottom: getPixel(btnHeight + 24 + btnBottom + 4),
    justifyContent: 'center',
    width: getPixel(64 + btnWidth),
    alignItems: 'center',
  },
  gapLine: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: color.grayBorder,
    width: getPixel(566),
    marginLeft: getPixel(22),
  },
});

interface IMoreRightIcon {}

// 供应商车型置顶模块右箭头
const MoreRightIcon: React.FC<IMoreRightIcon> = () => {
  return (
    <BbkText type="icon" className={c2xStyles.arrowRightList}>
      {icon.arrowRightList}
    </BbkText>
  );
};

const marketLinearColors = [color.C_FFEDE8, color.white];
const marketGrayLinearColors = [color.C_eee, color.white];
const soldOutBtnLinearColors = [color.C_eee, color.C_eee];

export const VendorPriceDescHasBookBtn: React.FC<IVendorPrice> = ({
  dayPrice,
  originDailyPrice,
  dayText,
  totalText,
  totalPrice,
  currentCurrencyCode,
  isSoldOut,
  handlePressBooking,
  onPressQuestion,
  wrapStyle,
  priceStyle,
  renderMarket = Utils.noop,
  bottomRightContentLayout = Utils.noop,
  totalPriceWrapStyle,
  isStorageNumTip,
  almostSoldOutLabel,
  isCouponBook,
  vendorButtonColors,
}) => {
  return (
    <View
      className={c2xStyles.vendorPriceWrap}
      style={xMergeStyles([priceStyle, wrapStyle])}
    >
      <BbkTouchable
        className={c2xStyles.bookingHasBookBtnWrap}
        onLayout={bottomRightContentLayout}
        debounce={true}
        onPress={handlePressBooking}
        testID={UITestID.car_testid_page_list_vendor_item_priceinfo}
        disabled={isSoldOut}
      >
        <View>
          {/** 日价 */}
          <View className={c2xStyles.dayPriceWrap}>
            {originDailyPrice > 0 && (
              <BbkCurrencyFormatter
                price={originDailyPrice}
                currency={currentCurrencyCode}
                wrapperStyle={styles.originDayPriceWrap}
                currencyStyle={xMergeStyles([
                  styles.originDayPrice,
                  isSoldOut && styles.soldOutStyle,
                ])}
                priceStyle={xMergeStyles([
                  styles.originDayPrice,
                  isSoldOut && styles.soldOutStyle,
                ])}
                isNew={true}
              />
            )}
            <View className={c2xStyles.dayPriceSubWrap}>
              {!!dayText && (
                <BbkText
                  style={xMergeStyles([
                    styles.dayText,
                    isSoldOut && styles.soldOutStyle,
                  ])}
                >
                  {dayText}
                </BbkText>
              )}
              <BbkCurrencyFormatter
                price={dayPrice}
                currency={currentCurrencyCode}
                currencyStyle={xMergeStyles([
                  styles.dayPriceCurrencyStyle,
                  isSoldOut && styles.soldOutStyle,
                ])}
                priceStyle={xMergeStyles([
                  styles.dayPriceStyle,
                  isSoldOut && styles.soldOutStyle,
                ])}
                wrapperStyle={styles.singlePriceWrapStyle}
                testID={UITestId.car_testid_comp_vendor_day_price}
              />
            </View>
          </View>
          {/** 总价 */}
          <View className={c2xStyles.totalPriceWrap}>
            <View className={c2xStyles.marketLabelAndPriceWrap}>
              {renderMarket()}
              <View
                style={styles.questionWrap}
                onPress={onPressQuestion}
                testID={UITestID.car_testid_comp_vehicle_price_help}
              >
                <BbkTouchable
                  style={layout.flexRow}
                  onPress={onPressQuestion}
                  testID={
                    UITestID.car_testid_page_list_vendor_item_priceinfo_price
                  }
                >
                  {!!onPressQuestion && !!totalPrice && (
                    <BbkTouchable
                      className={c2xStyles.helpIcon}
                      disabled={isSoldOut}
                      testID={UITestId.car_testid_comp_vendor_price_help}
                      onPress={onPressQuestion}
                    >
                      <BbkText
                        className={classNames(
                          c2xStyles.totalIcon,
                          isSoldOut && c2xStyles.soldOutStyle,
                        )}
                        type="icon"
                      >
                        {icon.help}
                      </BbkText>
                    </BbkTouchable>
                  )}
                  {!!totalText && (
                    <BbkText
                      className={classNames(
                        c2xStyles.totalTextStyle,
                        isSoldOut && c2xStyles.soldOutStyle,
                      )}
                    >
                      {totalText}
                    </BbkText>
                  )}
                </BbkTouchable>
                {totalPrice >= 0 && (
                  <BbkCurrencyFormatter
                    price={totalPrice}
                    currency={currentCurrencyCode}
                    wrapperStyle={totalPriceWrapStyle}
                    currencyStyle={xMergeStyles([
                      styles.totalCurrencyStyle,
                      isSoldOut && styles.soldOutStyle,
                    ])}
                    priceStyle={xMergeStyles([
                      styles.totalPriceStyle,
                      isSoldOut && styles.soldOutStyle,
                    ])}
                    testID={UITestId.car_testid_comp_vendor_total_price}
                  />
                )}
              </View>
            </View>
          </View>
        </View>
        <>
          {(isStorageNumTip || isSoldOut) && ( // 是库存文案的话展示在预订按钮上方
            <View style={styles.storageNumWrap}>
              <BbkText className={c2xStyles.storageNumStyle}>
                {isSoldOut ? Texts.vendorListSoldOutText : almostSoldOutLabel}
              </BbkText>
            </View>
          )}
          <BbkTouchable
            onPress={handlePressBooking}
            debounce={true}
            disabled={isSoldOut}
            testID={UITestId.car_testid_comp_vendor_list_book}
            className={c2xStyles.bookBtn}
          >
            <LinearGradient
              style={styles.bookBtnWrapNew}
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 1.0, y: 1.0 }}
              locations={[0, 1]}
              colors={vendorButtonColors}
            >
              {isCouponBook ? (
                <Image
                  className={c2xStyles.couponBtnText}
                  source={{
                    uri: isSoldOut
                      ? `${ImageUrl.DIMG04_PATH}1tg5412000hhgtefmB91E.png`
                      : `${ImageUrl.DIMG04_PATH}1tg1j12000hhgs1ydAECA.png`,
                  }}
                />
              ) : (
                <BbkText
                  className={classNames(
                    c2xStyles.bookBtnText,
                    isSoldOut && c2xStyles.soldOutBtnStyle,
                  )}
                  fontWeight="medium"
                >
                  {Texts.book}
                </BbkText>
              )}
            </LinearGradient>
          </BbkTouchable>
        </>
      </BbkTouchable>
    </View>
  );
};

export const VendorPriceDescNoBookBtn: React.FC<IVendorPrice> = ({
  dayPrice,
  originDailyPrice,
  dayText,
  totalText,
  totalPrice,
  currentCurrencyCode,
  isSoldOut,
  handlePressBooking,
  onPressQuestion,
  wrapStyle,
  renderMarket = Utils.noop,
  bottomRightContentLayout = Utils.noop,
  singlePriceWrapStyle,
  totalPriceWrapStyle,
  hasMarketTags,
}) => {
  const MarketLabelAndPriceProps = hasMarketTags
    ? {
        start: { x: 0.0, y: 0.0 },
        end: { x: 1.0, y: 1.0 },
        locations: [0, 1],
        colors: isSoldOut ? marketGrayLinearColors : marketLinearColors,
      }
    : {};
  const MarketLabelAndPriceContainer: any = hasMarketTags
    ? LinearGradient
    : View;
  return (
    <View className={c2xStyles.vendorPriceWrap} style={wrapStyle}>
      <BbkTouchable
        className={c2xStyles.bookingNoBookBtnWrap}
        onLayout={bottomRightContentLayout}
        debounce={true}
        onPress={handlePressBooking}
        testID={UITestID.car_testid_page_list_vendor_item_priceinfo}
        disabled={isSoldOut}
      >
        <View>
          {/** 日价 */}
          <View className={c2xStyles.dayPriceWrap}>
            {originDailyPrice > 0 && (
              <BbkCurrencyFormatter
                price={originDailyPrice}
                currency={currentCurrencyCode}
                wrapperStyle={styles.originDayPriceWrap}
                currencyStyle={xMergeStyles([
                  styles.originDayPrice,
                  isSoldOut && styles.soldOutStyle,
                ])}
                priceStyle={xMergeStyles([
                  styles.originDayPrice,
                  isSoldOut && styles.soldOutStyle,
                ])}
                isNew={true}
              />
            )}
            <View className={c2xStyles.dayPriceSubWrap}>
              {!!dayText && (
                <BbkText
                  style={xMergeStyles([
                    styles.dayText,
                    isSoldOut && styles.soldOutStyle,
                  ])}
                >
                  {dayText}
                </BbkText>
              )}
              <BbkCurrencyFormatter
                price={dayPrice}
                currency={currentCurrencyCode}
                currencyStyle={xMergeStyles([
                  styles.dayPriceCurrencyStyle,
                  isSoldOut && styles.soldOutStyle,
                ])}
                priceStyle={xMergeStyles([
                  styles.dayPriceStyle,
                  isSoldOut && styles.soldOutStyle,
                ])}
                wrapperStyle={singlePriceWrapStyle}
                testID={UITestId.car_testid_comp_vendor_day_price}
              />
            </View>
          </View>
          {/** 总价 */}
          <View className={c2xStyles.totalPriceWrap}>
            <MarketLabelAndPriceContainer
              style={styles.marketLabelAndPriceWrap}
              {...MarketLabelAndPriceProps}
            >
              {renderMarket()}
              <View
                style={styles.questionWrap}
                onPress={onPressQuestion}
                testID={UITestID.car_testid_comp_vehicle_price_help}
              >
                <BbkTouchable
                  style={layout.flexRow}
                  onPress={onPressQuestion}
                  testID={
                    UITestID.car_testid_page_list_vendor_item_priceinfo_price
                  }
                >
                  {!!onPressQuestion && !!totalPrice && (
                    <BbkTouchable
                      className={c2xStyles.helpIcon}
                      disabled={isSoldOut}
                      testID={UITestId.car_testid_comp_vendor_price_help}
                      onPress={onPressQuestion}
                    >
                      <BbkText
                        className={classNames(
                          c2xStyles.totalIcon,
                          isSoldOut && c2xStyles.soldOutStyle,
                        )}
                        type="icon"
                      >
                        {icon.help}
                      </BbkText>
                    </BbkTouchable>
                  )}
                  {!!totalText && (
                    <BbkText
                      className={classNames(
                        c2xStyles.totalTextStyle,
                        isSoldOut && c2xStyles.soldOutStyle,
                      )}
                    >
                      {totalText}
                    </BbkText>
                  )}
                </BbkTouchable>
                {totalPrice >= 0 && (
                  <BbkCurrencyFormatter
                    price={totalPrice}
                    currency={currentCurrencyCode}
                    wrapperStyle={totalPriceWrapStyle}
                    currencyStyle={xMergeStyles([
                      styles.totalCurrencyStyle,
                      isSoldOut && styles.soldOutStyle,
                    ])}
                    priceStyle={xMergeStyles([
                      styles.totalPriceStyle,
                      isSoldOut && styles.soldOutStyle,
                    ])}
                    testID={UITestId.car_testid_comp_vendor_total_price}
                  />
                )}
              </View>
            </MarketLabelAndPriceContainer>
          </View>
        </View>
        <MoreRightIcon />
      </BbkTouchable>
    </View>
  );
};

const Vendor: React.FC<IVendor> = ({
  vendorName,
  isOptimize,
  nationalChainTagTitle,
  score,
  commentDesc,
  isShowAtTop,
  vendorContainerStyle,
  vendorContentStyle,
  wrapStyle,
  isShowBtnIcon,
  marketTags,
  allLabels,
  selfServiceLabel,
  priceDescProps,
  isSoldOut,
  almostSoldOutLabel,
  uniqueCode,
  onPressVendor,
  onPressBooking,
  onPressQuestion,
  onPressReview,
  clickLogData,
  exposureLogData,
  showRightIcon,
  hasFees,
  isCouponBook,
  skuId,
  pickUpDesc,
  dropOffDesc,
  isPickUpRentCenter,
  isDropOffRentCenter,
  optimizationStrengthenVendorNameStyle,
  isSelfService,
  belongTab,
  isHasBookBtn = true,
  lowestPrice,
  vendorListLength,
  index,
}) => {
  const isShowLowestPrice = lowestPrice === 1 && vendorListLength > 1;
  // 低价或有正在浏览的车展示横幅
  const showBannerTip = isShowLowestPrice || isShowAtTop;
  const soldOutBtnBackgroundColor = isSoldOut && color.darkGrayBorder;
  const hasTags = allLabels?.length > 0;
  const hasMarketTags = marketTags?.length > 0;
  // 是否是库存提示文案，通过文案中是否包含 辆 来判断, 因为almostSoldOutLabel字段的定义是 低价 库存文案。
  const isStorageNumTip = almostSoldOutLabel?.indexOf(Texts.carUnit) > -1;

  const clickLog = useCallback(
    (buttonValue: IClickVendorArea) => {
      const isSelectedVendorTop1 =
        AppContext.vehicleLogInfo?.isSelectedVendorTop1;
      CarLog.LogCode({
        name: '点击_产品详情页_供应商报价',
        button: buttonValue,
        abVersion: clickLogData.abVersion,
        data: clickLogData,
        info: {
          isSelfService: isSelfService ? 1 : 0,
          belongTab,
          totalPrice: clickLogData.totalPrice,
          dailyPrice: clickLogData.dailyPrice,
          isLowestPrice: lowestPrice,
          isSelectedVendorTop1,
          topSelectedVendorCarAge: isSelectedVendorTop1 && clickLogData?.carAge,
          isSelectedVendor: clickLogData?.isOptimize ? 1 : 0,
          quoteRank: index,
          filterInfo: AppContext?.vehicleLogInfo?.filterInfo,
        },
      });
      CarLog.LogTrace({
        key: LogKey.cm_car_app_click_airanking,
        info: {
          name: '点击_产品详情页_供应商报价',
          newMergeId: AppContext.currentNewMergeId,
          vehicleId: clickLogData?.vehicleCode,
          serverRequestId: getServerRequestId(),
          requestId: getListRequestId(),
        },
      });
    },
    [clickLogData, isSelfService, lowestPrice, belongTab, index],
  );

  const getVendorExposureData = () => ({
    key: LogKey.c_car_exposure_product_quote_10650068646,
    ...exposureLogData,
    info: {
      ...exposureLogData?.info,
      ...AppContext?.vehicleLogInfo,
      quoteRank: index, // 车型楼层
    },
  });

  const handlePressVendor = useCallback(() => {
    onPressVendor(uniqueCode, skuId);
    clickLog(IClickVendorArea.Vendor);
  }, [onPressVendor, uniqueCode, skuId, clickLog]);

  const handlePressBooking = useCallback(() => {
    onPressBooking(Enums.TotalPriceModalType.Vendor, uniqueCode);
    clickLog(
      isCouponBook ? IClickVendorArea.CouponBook : IClickVendorArea.Booking,
    );
  }, [clickLog, isCouponBook, onPressBooking, uniqueCode]);

  const handlePressQuestion = useCallback(() => {
    onPressQuestion(Enums.TotalPriceModalType.Vendor, uniqueCode, hasFees);
    clickLog(IClickVendorArea.Question);
  }, [clickLog, hasFees, onPressQuestion, uniqueCode]);

  const handlePressReview = useCallback(() => {
    onPressReview(uniqueCode);
    clickLog(IClickVendorArea.Review);
  }, [clickLog, onPressReview, uniqueCode]);

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const renderMarket = show => {
    if (show) {
      return (
        <BbkTouchable
          debounce={true}
          onPress={handlePressBooking}
          disabled={isSoldOut}
          testID={UITestID.car_testid_page_list_vendor_item_marketlabel}
          className={c2xStyles.marketTagsWrap}
        >
          <XViewExposure
            testID={CarLog.LogExposure({
              name: '曝光_详情页_供应商_营销标签',

              info: { labelCode: marketTags?.[0]?.labelCode },
            })}
          >
            <MarketLabel
              title={marketTags?.[0]?.title}
              desc={marketTags?.[0]?.amountTitle}
              isSoldOut={isSoldOut}
              isBg={isHasBookBtn}
            />
          </XViewExposure>
        </BbkTouchable>
      );
    }
    return null;
  };

  const renderMarketUpgrade = () => {
    return renderMarket(true);
  };

  const vendorButtonColors = useMemo(() => {
    let buttonColors = [color.C_00a7fa, color.deepBlueBase];
    if (soldOutBtnBackgroundColor) {
      buttonColors = GetAB.isISDInterestPoints()
        ? soldOutBtnLinearColors
        : [soldOutBtnBackgroundColor, soldOutBtnBackgroundColor];
    } else if (isCouponBook) {
      buttonColors = [color.C_ff9d0a, color.orangeBase];
    }
    return buttonColors;
  }, [soldOutBtnBackgroundColor, isCouponBook]);

  const priceStyle = React.useMemo(() => {
    const fontLength = allLabels.reduce((m, v) => {
      return `${m}${v.title || ''}`;
    }, '').length;
    const marketLabelLength =
      `${marketTags?.[0]?.title || ''}${marketTags?.[0]?.amountTitle || ''}`
        .length;

    let marginTop = 0;
    // tag字数 < 7，价格模块往上提40px
    if (fontLength < 7) {
      if (marketLabelLength < 12) {
        // 优惠标签的字数
        marginTop = -40;
      } else {
        marginTop = -4;
      }
    } else if (fontLength >= 7 && fontLength < 13) {
      // 7 <= tag字数 <13
      if (priceDescProps.originDailyPrice) {
        // 有原价
        marginTop = -4;
      } else {
        // 无原价
        marginTop = -40;
      }
    } else if (fontLength > 19 && fontLength < 27) {
      // 19< tag字数<27,价格模块往上提4px
      marginTop = -4;
    }
    return { marginTop: getPixel(marginTop) };
  }, [allLabels, priceDescProps, marketTags]);

  const VendorPriceDescComponent = isHasBookBtn
    ? VendorPriceDescHasBookBtn
    : VendorPriceDescNoBookBtn;

  let showBannerTipOut = '';
  if (isShowLowestPrice) {
    showBannerTipOut = c2xStyles.showAtTopWrap;
  } else if (isShowAtTop) {
    showBannerTipOut = c2xStyles.showAtTopOnlyWrap;
  }

  return (
    <XViewExposure
      className={c2xStyles.container}
      style={vendorContainerStyle}
      testID={CarLog.LogExposure(getVendorExposureData())}
    >
      <View style={styles.gapLine} />
      <BbkTouchable
        debounce={true}
        onPress={handlePressBooking}
        disabled={isSoldOut}
        className={classNames(c2xStyles.wrap, showBannerTipOut)}
        style={wrapStyle}
        testID={UITestId.car_testid_page_vendor_card}
      >
        {showBannerTip && (
          <View className={c2xStyles.topTip}>
            {isShowLowestPrice && (
              <Image
                className={c2xStyles.topTipImage}
                mode="heightFix"
                source={{
                  uri: isSoldOut
                    ? 'https://dimg04.c-ctrip.com/images/1tg6v12000iukticbCF1A.png'
                    : 'https://dimg04.c-ctrip.com/images/1tg2112000iukufkr57A5.png',
                }}
              />
            )}
            {isShowAtTop && (
              <BbkText className={c2xStyles.tipText}>正在浏览的车辆</BbkText>
            )}
          </View>
        )}
        <View className={c2xStyles.contentWrap} style={vendorContentStyle}>
          <VendorHeaderNew
            vendorName={vendorName}
            isOptimize={isOptimize}
            nationalChainTagTitle={nationalChainTagTitle}
            score={score}
            commentDesc={commentDesc}
            onPressReview={handlePressReview}
            showRightIcon={showRightIcon}
            optimizationStrengthenVendorNameStyle={
              optimizationStrengthenVendorNameStyle
            }
            isShowOptimizationStrengthenRightArrow={showRightIcon}
            onPressVendor={handlePressVendor}
            isHasBookBtn={isHasBookBtn}
          />

          <XViewExposure
            className={c2xStyles.PickUpDropOffWrap}
            testID={CarLog.LogExposure({
              name: '曝光-货架页-门店位置',
              info: {
                pickupway: pickUpDesc,
                returnway: dropOffDesc,
              },
            })}
          >
            <PickUpDropOffDescNew
              pickUpDesc={pickUpDesc}
              dropOffDesc={dropOffDesc}
              isPickUpRentCenter={isPickUpRentCenter}
              isDropOffRentCenter={isDropOffRentCenter}
              selfServiceTitle={selfServiceLabel?.title}
              isSoldOut={isSoldOut}
            />
          </XViewExposure>
          {hasTags && (
            <View style={styles.vehicleTagsWrap}>
              {allLabels?.map(item => {
                return (
                  <View className={c2xStyles.labelStyle}>
                    <VendorLabelISD
                      title={item.title}
                      labelCode={item.labelCode}
                      isSoldOut={isSoldOut}
                    />
                  </View>
                );
              })}
            </View>
          )}
        </View>
        <View className={c2xStyles.bottomWrap}>
          <View className={c2xStyles.bottomContentWrap}>
            <View className={c2xStyles.rightWrap}>
              {!!priceDescProps && (
                <VendorPriceDescComponent
                  {...priceDescProps}
                  hasMarketTags={hasMarketTags}
                  isCouponBook={isCouponBook}
                  renderMarket={renderMarketUpgrade}
                  onPressQuestion={handlePressQuestion}
                  handlePressBooking={handlePressBooking}
                  isSoldOut={isSoldOut}
                  singlePriceWrapStyle={null}
                  isShowBtnIcon={isShowBtnIcon}
                  isStorageNumTip={isStorageNumTip}
                  almostSoldOutLabel={almostSoldOutLabel}
                  vendorButtonColors={vendorButtonColors}
                  priceStyle={priceStyle}
                />
              )}
            </View>
          </View>
        </View>
      </BbkTouchable>
    </XViewExposure>
  );
};

const soldOutTheme = {
  soldOutTextColor: color.darkGrayBorder,
  soldOutLabelBgColor: color.soldOutLabelBgColor,
  soldOutTagBgColor: color.grayBgSecondary,
};

export default memo(
  withTheme(props => {
    const { isSoldOut } = props;
    const VendorDom = <Vendor {...props} isSoldOut={isSoldOut} />;
    if (isSoldOut) {
      return (
        <BBkThemingProvider theme={soldOutTheme}>
          {VendorDom}
        </BBkThemingProvider>
      );
    }
    return VendorDom;
  }),
);
