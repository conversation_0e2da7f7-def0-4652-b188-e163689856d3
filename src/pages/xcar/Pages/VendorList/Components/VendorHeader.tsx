import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import { font, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import OptimizationStrengthen from '../../../ComponentBusiness/OptimizationStrengthen';
import Texts from '../Texts';
import { IVendorHeader } from '../Types';
import UITestId from '../../../Constants/UITestID';
import TextIncludeSoldOut from './TextIncludeSoldOut';
import { GetAB, Utils } from '../../../Util/Index';
import c2xStyles from './vendorHeaderC2xStyles.module.scss';

import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, isIos } = BbkUtils;
const getMaxByteStr = (str: string, maxByteLength: number) => {
  if (Utils.getByteLength(str) <= maxByteLength) return str;
  return `${Utils.getByteLengthStr(str, maxByteLength - 2)}...`;
};
const styles = StyleSheet.create({
  leftSection: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  vendorName: {
    ...font.title3BoldStyle,
    color: color.fontPrimary,
  },
  vendorNameNew: {
    ...font.F_30_10_medium,
    color: color.fontPrimary,
  },
  scoreContainerWrap: {
    paddingLeft: getPixel(11),
    borderTopLeftRadius: getPixel(6),
    borderBottomLeftRadius: getPixel(6),
  },
  scoreNum: { ...font.title3MediumStyle },
  scoreNumNew: {
    ...font.F_28_10_regular_TripNumberMedium,
    color: color.deepBlueBase,
    top: getPixel(isIos ? 0 : 2),
  },
  scoreText: { ...font.caption1LightStyle, marginLeft: getPixel(2) },
  scoreTextNew: {
    ...font.caption1LightStyle,
    marginLeft: getPixel(2),
    color: color.deepBlueBase,
  },
  commentDesc: {
    marginLeft: getPixel(12),
    ...font.caption1LightStyle,
    color: color.fontPrimary,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: '0',
  },
});

const scoreLinearColors = [color.C_EEF4FF, color.white];
const scoreGrayLinerColors = [color.C_eee, color.white];

const VendorHeader: React.FC<IVendorHeader> = ({
  vendorName,
  isOptimize,
  nationalChainTagTitle,
  score,
  commentDesc,
  theme,
  onPressReview,
  showRightIcon = true,
  arrowRightStyle,
  rightBtn,
  onPressRightBtn,
  wrapStyle,
  vendorNameStyle,
  optimizationStrengthenImgStyle,
  optimizationStrengthenDotStyle,
  optimizationStrengthenVendorNameStyle,
  isShowOptimizationStrengthenRightArrow,
  vendorNameMaxByte,
  onPressVendor,
}) => {
  const { soldOutTextColor, soldOutLabelBgColor } = theme;
  const isISDInterestPoints = GetAB.isISDInterestPoints();
  const ScoreContainer: any =
    isISDInterestPoints && score ? LinearGradient : View;
  const ScoreContainerProps =
    isISDInterestPoints && score
      ? {
          start: { x: 0.0, y: 0.0 },
          end: { x: 1.0, y: 1.0 },
          locations: [0, 1],
          colors: soldOutTextColor ? scoreGrayLinerColors : scoreLinearColors,
        }
      : {};
  const getVendorAscription = () => {
    if (nationalChainTagTitle) {
      return (
        <View
          className={c2xStyles.famousBrandWrap}
          style={
            soldOutLabelBgColor && { backgroundColor: soldOutLabelBgColor }
          }
        >
          <BbkText
            className={c2xStyles.famousBrandText}
            style={soldOutTextColor && { color: soldOutTextColor }}
          >
            {nationalChainTagTitle}
          </BbkText>
        </View>
      );
    }
    return null;
  };

  const getVendorInfo = () => {
    if (isOptimize) {
      return (
        <OptimizationStrengthen
          vendorName={vendorName}
          imgStyle={optimizationStrengthenImgStyle}
          dotStyle={optimizationStrengthenDotStyle}
          vendorNameStyle={optimizationStrengthenVendorNameStyle}
          isShowRightArrow={isShowOptimizationStrengthenRightArrow}
          vendorNameMaxByte={vendorNameMaxByte}
          onPressVendor={onPressVendor}
        />
      );
    }
    return (
      <BbkTouchable
        testID={UITestId.car_testid_vendorheader}
        style={styles.leftSection}
        onPress={onPressVendor}
      >
        <TextIncludeSoldOut
          // todo-xtaro-css-xMergeStyles
          style={xMergeStyles([
            isISDInterestPoints ? styles.vendorNameNew : styles.vendorName,
            vendorNameStyle,
          ])}
          text={getMaxByteStr(vendorName, 26)}
          testID={UITestId.car_testid_comp_vendor_name}
        />

        {getVendorAscription()}
        {!soldOutTextColor && showRightIcon && (
          <BbkText
            type="icon"
            className={classNames(
              c2xCommonStyles.c2xTextDefaultColor,

              isISDInterestPoints
                ? c2xStyles.arrowRightNew
                : c2xStyles.arrowRight,
            )}
            style={arrowRightStyle}
          >
            {icon.right_arrow}
          </BbkText>
        )}
      </BbkTouchable>
    );
  };
  return (
    <View className={c2xStyles.wrap} style={wrapStyle}>
      {getVendorInfo()}
      {rightBtn ? (
        <BbkTouchable
          testID={UITestId.car_testid_vendorheader_righticon}
          onPress={onPressRightBtn}
          className={c2xStyles.rightSection}
        >
          <BbkText
            className={classNames(
              c2xCommonStyles.c2xTextDefaultCss,

              isISDInterestPoints ? c2xStyles.rightBtnNew : c2xStyles.rightBtn,
            )}
          >
            {rightBtn}
          </BbkText>
        </BbkTouchable>
      ) : (
        <BbkTouchable
          onPress={onPressReview}
          disabled={!commentDesc || !!soldOutTextColor} // 暂无评论或者售罄的情况下不可点击
          testID={UITestId.car_testid_comp_vendor_comment}
        >
          <ScoreContainer
            style={xMergeStyles([
              styles.rightSection,
              isISDInterestPoints && styles.scoreContainerWrap,
            ])}
            {...ScoreContainerProps}
          >
            <View className={c2xStyles.scoreWrap}>
              {score ? (
                <>
                  <TextIncludeSoldOut
                    style={
                      isISDInterestPoints ? styles.scoreNumNew : styles.scoreNum
                    }
                    text={score}
                  />

                  <TextIncludeSoldOut
                    style={
                      isISDInterestPoints
                        ? styles.scoreTextNew
                        : styles.scoreText
                    }
                    text={Texts.score}
                  />

                  {!soldOutTextColor && !isISDInterestPoints && (
                    <LinearGradient
                      start={{ x: 0.0, y: 0.0 }}
                      end={{ x: 1.0, y: 1.0 }}
                      locations={[0, 1]}
                      colors={[color.blueShadow, color.white]}
                      className={c2xStyles.scoreGradient}
                    />
                  )}
                </>
              ) : (
                <TextIncludeSoldOut
                  style={styles.scoreText}
                  text={Texts.noScore}
                />
              )}
            </View>
            {!!commentDesc && (
              <TextIncludeSoldOut
                style={styles.commentDesc}
                text={commentDesc}
              />
            )}
          </ScoreContainer>
        </BbkTouchable>
      )}
    </View>
  );
};

export default withTheme(VendorHeader);
