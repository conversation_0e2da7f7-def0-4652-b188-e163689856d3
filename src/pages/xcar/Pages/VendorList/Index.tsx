/* eslint-disable no-nested-ternary */
import { omit as lodashOmit, pick as lodashPick } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import Dimensions from '@c2x/apis/Dimensions';
import PanResponder from '@c2x/apis/PanResponder';
import LayoutAnimation from '@c2x/apis/LayoutAnimation';
import ViewPort from '@c2x/components/ViewPort';
import { IBasePageProps } from '@c2x/components/Page';
import Bridge from '@c2x/apis/Bridge';
import React, { createRef } from 'react';
import {
  xMergeStyles,
  XView as View,
  xUser,
  XAnimated,
  xCreateAnimation,
  xGetCurrentInstance,
  xEnv,
} from '@ctrip/xtaro';

import memoizeOne from 'memoize-one';

import { color, druation } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { ResponseMapType } from '@ctrip/rn_com_car/dist/src/CarFetch/src/FetchTypes';
import IUrs from '@c2x/components/IUrs';
import c2xStyles from './vendorList.module.scss';
import CPage, { IStateType } from '../../Components/App/CPage';
import {
  Utils,
  User,
  AppContext,
  CarLog,
  CarStorage,
  Channel,
  GetABCache,
  GetAB,
  CarServerABTesting,
} from '../../Util/Index';
import {
  PageName,
  StorageKey,
  UITestID,
  ImageUrl,
} from '../../Constants/Index';
import {
  VendorListSpecific,
  ProductConfirmModalNew,
  VirtualNumberDialog,
  VendorListOptimizationStrengthenModal,
  VirtualNumberStoreModal,
  SearchPanelModal,
  TotalPriceModal,
  CouponModal,
  LimitRulesPop,
  VehicleModal,
  TimeOutPop,
  ProductConfirmPriceDetail,
  VendorListEasyLifeModal,
  LocationAndDateMonitor,
  VehicleImageBounces,
  VehicleAndLimitB,
  VehicleAndLimit2,
  CouponEntryNew,
  VendorListFiltered,
  VendorListRecommondLists,
  BusinessLicenseModal,
  EtcIntroModalContainer,
  VendorListShelvesContainer,
  VendorListShelves2Container,
} from '../../Containers/VendorListIndex';
import {
  ListCalendarModalContainer as ListCalendarModal,
  RecommendListPriceDetailModal,
  RecommendListTotalPriceModal,
} from '../../Containers/ListIndex';
import {
  BackToTopBtn,
  LessDetailModal,
  FuelTypeModal,
  StoreModal,
  ProductHeader,
  MarketingBanner,
  TangramEntrance,
  MarketingFooter,
  VehicleModalFooter,
  BouncesScrollView,
  BouncesScrollContanst,
  NoLimitModal,
} from './Components/Index';
import ProductConfirmEffect from '../../Containers/ProductConfirmEffectContainer';
import * as homeConfig from '../Home/Logic/config';
import {
  LayoutPartEnum,
  VehicleInfoLogDataType,
} from '../../ComponentBusiness/ProductConfirmModal/Type';
import { PageParamType } from '../../Types/Dto/QueryVehicleDetailListRequestType';
import { INVOKE_FROM } from '../../Constants/ServerMapping';
import {
  IVendorListFirstScreenParamType,
  IVendorListCurrentPageParams,
} from './Types';
import { TotalPriceModalType } from '../../ComponentBusiness/Common/src/Enums';
import { PromptInfosType } from '../../Types/Dto/QueryVehicleDetailInfoResponseType';
import {
  ExtraInfosType,
  MediaGroupType,
  MediaInfo,
  AlbumType,
  Floor,
} from '../../Types/Dto/QueryVehicleDetailListResponseType';
import { IMarketThemeVendorList } from '../../Types/Dto/QueryThemeConfigResponseType';
import { QConfigType } from '../../Types/Dto/QConfigResponseType';
import { CalendarModalTypes } from '../../Components/Calendar/Types';
import { VehicleNewFromPage } from '../List/Components/VehicleNew';
import { getStore } from '../../State/StoreRef';
import { FilterPoiType } from '../../Util/AppContext';
import { initializeABVendorListPage } from '../../Util/CarABTesting/InitializeAB';
import { StickyBanner } from './Components/SectionHeader';
import NewSectionHeader from './Components/NewSectionHeader';
import { AssistiveTouch } from '../../Components/Index';
import { XAnimateExport } from '../../Types/Animate';
import { CarServiceFromPageTypes } from '../../ComponentBusiness/CarService';
import CarServiceDetailModal from '../../ComponentBusiness/CarServiceDetailModal/src/CarServiceDetailModalShelves2';
import ListReqAndResData, {
  listStatusKeyList,
  setListStatusData,
} from '../../Global/Cache/ListReqAndResData';

const {
  getPixel,
  fixIOSOffsetBottom,
  adaptNoaNomalousBottom,
  isAndroid,
  isHarmony,
  vh,
} = BbkUtils;
const { IMMERSE_Y0, FULL_THRESHOLD, IMMERSE_MAX_HEIGHT, CAROUSEL_HEIGHT } =
  BouncesScrollContanst;

const MultiVendorListKey = StorageKey.CAR_VENDORLISTPAGE_LOCATIONANDDATE;

interface IVisible {
  visible: boolean;
}
interface VendorListPropsType extends IBasePageProps {
  config: QConfigType;
  // 头部组件参数
  headerData?: any;
  bizType?: string;
  toolBoxCustomerJumpUrl?: string;
  // 头部车型信息
  bbkVehicleNameProps?: any;
  // 车型信息弹层
  openVehicleModal: () => void;
  // 车型信息弹窗关闭
  closeVehicleModal: () => void;
  // 请求报价列表接口
  queryVehicleDetailList: (params: {
    reqParam: PageParamType;
    callbackFun?: () => void;
  }) => void;
  vehicleIndex: number;
  setVehicleIndex: (vehicleIndex: number) => void;
  isShowRestAssured?: boolean;
  marketingAtmosphere?: number;
  pageParam: PageParamType;
  uniqueCode: string;
  // 请求租车劵包接口
  fetchReceivePromotion: () => void;
  // 处理领取弹层关闭事件
  handleCloseCouponModal: (param: any) => void;
  priceDetailModalVisible: boolean;
  // 信息确认弹层是否展示
  productConfirmModalVisible: boolean;
  totalPriceModalVisible: boolean;
  setPriceDetailModal: (data: {
    type: string;
    uniqueCode?: string;
    floorId?: string;
    packageCode?: string;
    hasFees?: boolean;
    curPriceDetailModalVisible: boolean;
    curTotalPriceModalVisible: boolean;
  }) => void;
  closeProductConfirmModal: () => void;
  closePriceDetailModal: () => void;
  onPressEasyLife: () => void;
  goToBooking: (data: {
    type: TotalPriceModalType;
    pTime: string;
    uniqueCode?: string;
    floorId?: string;
    packageCode?: string;
    logData?: any;
  }) => void;
  isLoginAtBookingPage: boolean;
  setIsLoginAtBookingPage: (data: boolean) => void;
  isCouponBookAtBookingPage: boolean;
  setIsCouponBookAtBookingPage: (data: boolean) => void; // 更新是否进行了领券订的记录
  isLoading: boolean;
  isError: boolean;
  appResponseMap: ResponseMapType;
  reset: () => void;
  productConfirmAnchor: LayoutPartEnum;
  isListReceiveSuccess: boolean;
  pTime: string;
  showModalKey: string;
  closeVendorListModal: (closeModalKey: string) => void;
  setFirstScreenParam: (data: IVendorListFirstScreenParamType) => void;
  vendorListFirstScreenParam?: IVendorListFirstScreenParamType;
  priceListLen?: number;
  tangramEntranceInfos: PromptInfosType[];
  vehicleCode: string;
  hasLaborDayLabel?: boolean;
  vendorListMarketTheme?: IMarketThemeVendorList;
  isShowMarketTheme?: boolean;
  yunnanBannerInfo?: ExtraInfosType;
  getMarketTheme?: () => void;
  optimizationStrengthenModalVisible?: boolean;
  showOptimizationStrengthenModal?: () => void;
  hideOptimizationStrengthenModal?: () => void;
  vehicleInfoLogData?: VehicleInfoLogDataType;
  price?: number;
  descText?: string;
  minTotalPrice?: number;
  minTotalPriceDesc?: string;
  minTotalPriceOtherDesc?: string;
  locationDatePopVisible?: boolean;
  vrUrl?: string;
  isNomatch?: boolean; // 无结果
  setLocationDateInfo: (data: any) => void;
  vendorListCurrentPageParams?: IVendorListCurrentPageParams;
  setCurrentPageParams?: (data: IVendorListCurrentPageParams) => void;
  fetchLimitContent?: (data: boolean) => void;
  queryMultimediaAlbum?: (data) => void;
  isBusinessLicenseModalVisible?: boolean;
  setBusinessLicenseModal: (data: any) => void;
  setLocationAndDatePopIsShow: ({ visible }: IVisible) => void;
  isEtcIntroModalVisible?: boolean;
  isSetFirstScreenBefore?: boolean;
  setEtcIntroModal?: (data: any) => void;
  fromPage?: string;
  vehicleDetailListReq: any;
  vehicleDetailListRes: any;
  shareVehicleInfo: object;
  isDebugMode?: boolean;
  reducedCarbonEmissionRatio: number;
  shelvesFloor?: Array<Floor>;
  floorId?: string;
  packageCode?: string;
  isCouponEntry?: boolean;
  vehicleDetailModalLogInfo?: any;
  insuranceDetails?: {
    packageDetailList: Array<any>;
    purchasingNotice: object;
  };
  queryInsuranceDetails?: (data) => void;
  showCarServiceDetailModal?: () => void;
  carServiceDetailVisible?: boolean;
  hideCarServiceDetailModal?: () => void;
  setInsuranceDetail?: (data: any) => void;
  ipollPositionType: number;
  queryVendorIpollConfig?: (data?: any) => void;
  sceneid?: string;
  bigGroupId?: string;
  cardPriceIndex?: number; // 卡片价格索引
  isSelectedVendorTop1?: boolean;
  filterInfo?: string;
  ipollPositionNum?: number;
  ipollLogData?: {
    queryVid?: string;
    vehicleCode?: string;
    pCityId?: string;
    rCityId?: string;
    pickupLocation?: string;
    returnLocation?: string;
    ptime?: string;
    rtime?: string;
  };
  setHasReceivedCouponAtBooking?: (data: boolean) => any;
  hasReceivedCouponAtBooking?: boolean;
}
interface VendorListStateType extends IStateType {
  isShowInfoLayer?: boolean;
  // 头部组件透明度参数
  opacityAnimation?: number;
  fixOpacityAnimation?: number;
  headerHeight: number;
  vendorListTopHeight: number; // 报价列表上面组件的总高度
  locationAndDateMonitorVisible: boolean;
  isShowOptimizeStoreModal: boolean;
  isFuelTypeModalVisible: boolean;
  fuelType: string;
  lessModalVisible: boolean;
  noLimitModalVisible: boolean;
  noLimitDesc?: string;
  backToTopBtnAnim?: XAnimateExport;
  backToTopBtnAnimRight: number;
  showBackToTopBtnAtRight?: boolean;
  isShowVRIcon?: boolean;
  isLazyLoad: boolean;
  showFullImmerse: boolean; // 是否展示沉浸式大图，即动画2结束后（用于显示达人笔记等组件）
  scrollEnabledAndroid: boolean; // android是否可以滑动
  scrollEnabledIOS: boolean; // ios是否可以滑动
  hideFullImmerse: boolean; // 判断是否可以显示沉浸式大图（避免上拉、收起过快时产生bug）
  readyImmerse: boolean;
  update: boolean; // 只用于强制更新
  isFitVisibale: boolean;
  isFitVendotListHeight: number;
  noFitVendorLIstHeight: number;
  recommendVendorListHeight: number;
  noFitVisibale: boolean;
  recommendHeaderVisibale: boolean;
  hasFit: boolean;
  hasNoFit: boolean;
  hasRecommend: boolean;
  loadNextPageVendorList: boolean;
  immerseTransYAni: XAnimateExport;
  immerseTransYAniImage: XAnimateExport;
  isAnimated: boolean;
  curFoorName?: string[];
  curClickLogData?: any;
  activeInsuranceCode?: string;
  allPackageLevel?: string[];
}
const styles = StyleSheet.create({
  mainScroll: {
    flex: 1,
  },
  marketingFooterWrapStyle: {
    backgroundColor: color.newBookingGrayBg,
  },
  priceDetailStyle: {
    bottom: getPixel(120) + fixIOSOffsetBottom() + adaptNoaNomalousBottom(),
  },
  recommendTitleWrap: {
    height: getPixel(86),
    position: 'absolute',
    width: '100%',
  },
  iursContainer1: {
    marginTop: getPixel(-16),
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
    marginBottom: getPixel(24),
  },
  iursContainer2: {
    marginLeft: getPixel(14),
    marginRight: getPixel(14),
    marginBottom: getPixel(24),
  },
});

const noop = () => {};

const TotalPriceEnName = {
  [TotalPriceModalType.Vendor]: '点击_供应商总价提示',
  [TotalPriceModalType.VendorListModal]: '点击_费用明细',
};
interface VehicleInfoType {
  vehiclecode: string;
  vehicleName: string;
  vehicleIndex: number;
}
interface InfoLogsType {
  vehicledata?: VehicleInfoType;
}

const ShowFullImmerseDuration = 600;

export default class VendorList extends CPage<
  VendorListPropsType,
  VendorListStateType
> {
  headerRef = null;

  vendorListSpecificRef = null;

  scrollViewRef: any = null;

  scrollY = 0;

  isLogin = !!xUser.isLogin();

  isLoginChanged = false;

  videoRef: any = null;

  hasPauseVideo = false;

  videoPlayerState = null;

  datePickerRef: any;

  searchPanelModalRef: any;

  backToTopBtnAtRight: boolean;

  isScrolling: boolean;

  backToTopBtnAnimation = xCreateAnimation({
    duration: 0,
    delay: 0,
    timingFunction: 'ease',
  });

  floorHeight = {}; // 楼层的高度 需要根据每次的请求设置一次高度，分别记录其的展开情况

  // #region 半屏滑动相关

  isScrollingByBounces: boolean; // 用于沉浸屏判断是否滚动页面

  enablePanResponder; //* 是否开启手势系统，准备展示沉浸式大图

  immerseTransYInter; // 屏幕Y轴动画值

  immerseTransYAni = xCreateAnimation({
    duration: 0,
    delay: 0,
  }); // 屏幕Y轴动画

  immerseTransYAniImage = xCreateAnimation({
    duration: 0,
    delay: 0,
  }); // 图片轮播组件Y轴动画

  scrollViewPanResponder;

  animating = false; // 是否执行动画

  isFullAnimating = false; // 是否正在执行全屏动画，用于解决慢速的手势下滑时设置的插值动画与全屏动画之间的冲突。

  isInterpolate; // 是否正在执行动画

  useAniTimingIn = false;

  animatedType = '';

  fullScreenHeight = isAndroid ? CAROUSEL_HEIGHT : null; // 沉浸式动画时的轮播图高度

  bannerSubRef; // 分类子项

  headerCarousel; // 轮播头部第一帧

  videoDom; // 视屏播放组件

  fullScreenView;

  // #endregion

  constructor(props) {
    super(props);
    // 页面跳转时，列表页会禁用侧滑，详情页需手动发送pv埋点
    this.needLogPage = true;
    this.initialPageDisableDragBack();
    this.fullScreenHeight = isAndroid ? CAROUSEL_HEIGHT : null; // 沉浸式动画时的轮播图高度
    //* 针对页面自动滚动（如location=ticket）时大图下拉产生的页面无法滑动问题，使用以下两个变量
    this.isScrollingByBounces = false; // 页面是否正在滚动，自动滚动早于下拉沉浸式时，自动滚动生效，下拉失效（直到自动滚动动画结束）
    this.bannerSubRef = createRef();
    // 手势系统
    this.scrollViewPanResponder = PanResponder.create({
      onMoveShouldSetPanResponder: (e, gestureState) => {
        /**
         ** 此处执行move函数而非onPanResponderMove是因为android上会存在夺权问题，很难触发onPanResponderMove
         ** 根据手势移动距离来判断是否成为响应者是因为android上子元素的点击事件在父元素成为响应者后会失效
         */
        if (isAndroid && !this.isScrollingByBounces && this.scrollY <= 0) {
          this.onPanResponderMoveOnAndroid(e, gestureState);
        }
        return Math.abs(gestureState.dy) > 5;
      },
      onMoveShouldSetPanResponderCapture: () => false,
      onPanResponderGrant: this.onPanResponderGrant.bind(this),
      onPanResponderMove: (e, gestureState) =>
        !isAndroid &&
        this.scrollY <= 0 &&
        this.onPanResponderMoveOnIos(e, gestureState),
      onPanResponderRelease: this.onPanResponderRelease.bind(this),
    });
    this.backToTopBtnAnimation.right(0).opacity(0.8).step();
    this.state = {
      isShowInfoLayer: false,
      opacityAnimation: 1,
      fixOpacityAnimation: 0,
      headerHeight: 0,
      vendorListTopHeight: 10000,
      locationAndDateMonitorVisible: false,
      isShowVRIcon: true,
      isShowOptimizeStoreModal: false,
      isFuelTypeModalVisible: false,
      fuelType: '',
      lessModalVisible: false,
      noLimitModalVisible: false,
      noLimitDesc: '',
      backToTopBtnAnim: this.backToTopBtnAnimation.export(),
      backToTopBtnAnimRight: 0,
      isLazyLoad: false,
      showFullImmerse: false, // 是否展示沉浸式大图，即动画2结束后（用于显示达人笔记等组件）
      scrollEnabledAndroid: true,
      scrollEnabledIOS: true,
      hideFullImmerse: true, // 判断是否可以显示沉浸式大图（避免上拉、收起过快时产生bug）
      readyImmerse: false,
      update: false, // 只用于强制更新
      isFitVisibale: false,
      isFitVendotListHeight: 0,
      noFitVendorLIstHeight: 0,
      recommendVendorListHeight: 0,
      noFitVisibale: false,
      recommendHeaderVisibale: false,
      hasFit: false,
      hasNoFit: false,
      hasRecommend: false,
      loadNextPageVendorList: false,
      isAnimated: false,
      activeInsuranceCode: '',
      immerseTransYAni: this.immerseTransYAni
        .translateY(IMMERSE_Y0)
        .step()
        .export(),
      immerseTransYAniImage: this.immerseTransYAniImage
        .translateY(-IMMERSE_Y0 / 2)
        .step()
        .export(),
      curFoorName: [],
      curClickLogData: {},
      allPackageLevel: [],
    };
    initializeABVendorListPage();
    this.initListData();
    this.setFilterPoi();
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().VendorList.ID;
  }

  getPageParamInfo() {
    const info: InfoLogsType = {};
    const { vehicleIndex, vendorListFirstScreenParam } = this.props;
    if (vendorListFirstScreenParam?.vehicleInfo) {
      const { vehicleInfo } = vendorListFirstScreenParam;
      info.vehicledata = {
        vehiclecode: vehicleInfo?.vehicleCode,
        vehicleName: vehicleInfo?.zhName,
        vehicleIndex,
      };
    }
    return info;
  }

  /* eslint-disable camelcase */
  UNSAFE_componentWillMount() {
    super.UNSAFE_componentWillMount();
  }

  initListData() {
    const {
      vendorListCurrentPageParams,
      vendorListFirstScreenParam,
      isSetFirstScreenBefore,
      setCurrentPageParams,
    } = this.props;
    if (!isSetFirstScreenBefore) {
      this.props.setFirstScreenParam(vendorListFirstScreenParam);
    }
    if (Utils.openUrlVendorListPageUniqueId()) {
      setCurrentPageParams(vendorListCurrentPageParams);
    }
  }

  setFilterPoi() {
    AppContext.setFilterPoi(FilterPoiType.OSD);
  }

  showSearchSelectorWrapWithLog = () => {
    const { vehicleCode } = this.props;
    CarLog.LogCode({
      name: '点击_产品详情页_修改取还车信息',

      info: { vehicleCode },
    });
  };

  lazyDidMount() {
    // 先进行其他文件的追加;
    super.lazyLoadOtherModules();
    this.setState({ isLazyLoad: true });
    this.props.fetchReceivePromotion();
    this.props.getMarketTheme();
    this.props.setVehicleIndex(this.props.vehicleIndex);
  }

  fetchMultimediaAlbum = () => {
    const { vehicleCode, queryMultimediaAlbum = noop } = this.props;
    queryMultimediaAlbum({
      vehicleId: vehicleCode,
      albumType: AlbumType.Official,
      isPreFetch: true,
    });
  };

  onPageReadyAfter() {
    this.fetchMultimediaAlbum();
  }

  componentDidShow() {
    super.componentDidShow();
    const { navigation = {} } = xGetCurrentInstance().app;
    const routes = navigation.getCurrentRoutes?.() || [];
    if (routes.length > 0) {
      const currentRoute = routes[routes.length - 1] || {};
      this.logPage(currentRoute);
    }
  }

  componentDidMount() {
    super.componentDidMount();
    this.fetchVehicleDetailList();
    this.addTimer(
      setTimeout(() => {
        this.lazyDidMount();
      }, 100),
    );
    if (GetABCache.isISDProductIpoll()) {
      this.props.queryVendorIpollConfig({ pageType: 2 });
    }
    const {
      bigGroupId,
      vehicleIndex,
      vehicleCode,
      cardPriceIndex,
      isSelectedVendorTop1,
      filterInfo,
      setHasReceivedCouponAtBooking,
    } = this.props;
    // 记录曝光串联埋点数据
    AppContext.setVehicleLogInfo({
      bigGroupId,
      bigGroupRank: vehicleIndex,
      smallGroupId: vehicleCode,
      smallGroupRank: cardPriceIndex,
      isSelectedVendorTop1,
      filterInfo,
    });
    setHasReceivedCouponAtBooking(false);
  }

  fetchVehicleDetailList = (params?: {
    deleteUniqSign?: boolean;
    callbackFun?: () => void;
  }) => {
    const { deleteUniqSign, callbackFun } = params || {};
    const { pageParam, fetchLimitContent } = this.props;
    this.props.queryVehicleDetailList({
      reqParam: deleteUniqSign ? lodashOmit(pageParam, 'uniqSign') : pageParam,
      callbackFun,
    });
    if (Utils.openUrlVendorListPageUniqueId() || deleteUniqSign) {
      fetchLimitContent(true);
    }
    // 存储更新后的取还车条件信息
    if (deleteUniqSign && Utils.openUrlVendorListPageUniqueId()) {
      const data = lodashPick(getStore().getState(), ['LocationAndDate']);
      this.multiContainerSaveLocationAndDate(data?.LocationAndDate);
    }
  };

  // 页面滑动时处理视频暂停与播放
  handleVideoPauseAndPlay = () => {
    const vehImgHeight = getPixel(640 - 108); // 640是车图模块的高度, 108是搜索条件浮动头部的高度
    if (
      this.videoPlayerState === '1' &&
      this.scrollY > vehImgHeight &&
      !this.hasPauseVideo
    ) {
      this.videoRef?.pause();
      this.hasPauseVideo = true;
    }
    if (this.hasPauseVideo && this.scrollY < vehImgHeight) {
      this.videoRef?.play();
      this.hasPauseVideo = false;
    }
  };

  backToTopBtnAnim = ({ right, opacity }) => {
    this.backToTopBtnAnimation.right(right).opacity(opacity).step({
      duration: druation.animationDurationMm,
    });
    this.setState({
      backToTopBtnAnim: this.backToTopBtnAnimation.export(),
      backToTopBtnAnimRight: right,
    });
  };

  backToTopBtnAnimEnd = () => {
    const { backToTopBtnAnimRight } = this.state;
    this.backToTopBtnAtRight = backToTopBtnAnimRight > 0;
  };

  onScrollToFloor = index => {
    const { isCouponEntry, vehicleDetailListReq, vehicleDetailListRes } =
      this.props;
    const isShowFitTitle = vehicleDetailListReq?.filters?.length > 0;
    this.isScrolling = true;
    const { headerHeight, vendorListTopHeight } = this.state;
    const isISDShelves2B = CarServerABTesting.isISDShelves2B();
    const vendorListMarginTop = getPixel(isISDShelves2B ? 16 : 18);
    const couponEntryHeight =
      isISDShelves2B && isCouponEntry ? getPixel(70) : 0;
    const filterHeight = isISDShelves2B && isShowFitTitle ? 88 : 0;
    let locationAndDateMonitorShowOffsetY =
      vendorListTopHeight +
      vendorListMarginTop -
      headerHeight +
      couponEntryHeight;
    locationAndDateMonitorShowOffsetY += getPixel(filterHeight) + getPixel(1);
    const requestId = vehicleDetailListRes?.baseResponse?.requestId;
    this.floorHeight?.[requestId]?.forEach((fHeightInfo, fIndex) => {
      const { fHeight = 0 } = fHeightInfo || {};
      if (fIndex < index) {
        locationAndDateMonitorShowOffsetY +=
          fHeight + (fIndex === 0 ? getPixel(16) : 0);
      }
    });
    this.scrollViewRef.scrollTo({
      y: locationAndDateMonitorShowOffsetY,
    });
  };

  onScroll = ({ nativeEvent }) => {
    const {
      isCouponEntry,
      shelvesFloor,
      vehicleDetailListReq,
      vehicleDetailListRes,
    } = this.props;
    const isShowFitTitle = vehicleDetailListReq?.filters?.length > 0;
    let foorName: string[] = [];
    let curClickLogData = {};
    this.isScrolling = true;
    const { y } = nativeEvent.contentOffset;
    this.isScrollingByBounces = y > 0;
    const {
      headerHeight,
      vendorListTopHeight,
      isFitVendotListHeight,
      noFitVendorLIstHeight,
      curFoorName,
    } = this.state;
    const { setShowAnimation = noop } = this.headerRef || {};
    setShowAnimation(y > 0);
    if (noFitVendorLIstHeight > 0) {
      this.handleshowNoFit();
    }
    // 搜索条件回显置顶
    let locationAndDateMonitorVisible = false;
    let isFitVisibale = false;
    let noFitVisibale = false;
    let recommendHeaderVisibale = false;
    let loadNextPageVendorList = false;
    const isISDShelves2B = CarServerABTesting.isISDShelves2B();
    const vendorListMarginTop = getPixel(isISDShelves2B ? 16 : 18);
    const locationAndDateMonitorHeight = getPixel(108);
    const stickyTitleHeight = getPixel(92);
    const couponEntryHeight =
      isISDShelves2B && isCouponEntry ? getPixel(70) : 0;
    const filterHeight = isISDShelves2B && isShowFitTitle ? 88 : 0;
    const locationAndDateMonitorShowOffsetY =
      vendorListTopHeight +
      vendorListMarginTop -
      headerHeight +
      couponEntryHeight;
    // 筛选&&看了又看Title置顶
    const noFitMonitorShowOffsetY =
      isFitVendotListHeight +
      locationAndDateMonitorShowOffsetY -
      locationAndDateMonitorHeight -
      getPixel(28);
    const recommendMonitorShowOffsetY =
      noFitMonitorShowOffsetY + noFitVendorLIstHeight + getPixel(8);
    const scrollOffset = isAndroid ? vh(100) : vh(100) + vh(40);
    if (y > recommendMonitorShowOffsetY - scrollOffset) {
      loadNextPageVendorList = true;
    }
    if (y > locationAndDateMonitorShowOffsetY) {
      locationAndDateMonitorVisible = true;
    } else {
      locationAndDateMonitorVisible = false;
    }
    // 设置置顶楼层标题
    if (isISDShelves2B) {
      let floorStartHeight =
        locationAndDateMonitorShowOffsetY + getPixel(filterHeight);
      let floorEndHeight =
        locationAndDateMonitorShowOffsetY +
        getPixel(filterHeight) -
        getPixel(76);
      const requestId = vehicleDetailListRes?.baseResponse?.requestId;
      this.floorHeight?.[requestId]?.forEach((fHeightInfo, fIndex) => {
        const { fHeight = 0, isExpend } = fHeightInfo || {};
        floorEndHeight += fHeight;
        // 仅展开的货架支持置顶
        if (
          isExpend &&
          y >
            floorStartHeight +
              getPixel(shelvesFloor?.[fIndex]?.lowestPrice === 1 ? 88 : 32) +
              getPixel(fIndex === 0 ? 0 : 16) &&
          y < floorEndHeight
        ) {
          foorName = shelvesFloor?.[fIndex]?.floorName;
          curClickLogData = shelvesFloor?.[fIndex]?.clickLogData;
        }
        floorStartHeight += fHeight;
      });
    }
    if (
      y > locationAndDateMonitorShowOffsetY &&
      y < noFitMonitorShowOffsetY - stickyTitleHeight
    ) {
      isFitVisibale = true;
    } else {
      isFitVisibale = false;
    }
    if (
      y >= noFitMonitorShowOffsetY &&
      y < recommendMonitorShowOffsetY - stickyTitleHeight
    ) {
      noFitVisibale = true;
    } else {
      noFitVisibale = false;
    }
    if (y >= recommendMonitorShowOffsetY) {
      recommendHeaderVisibale = true;
    } else {
      recommendHeaderVisibale = false;
    }
    const isShowVRIcon = y < headerHeight / 2;
    const fixOpacityAnimation = y >= 50 ? 1 : (y > 0 ? y : 0) / 50;
    let opacityAnimation = y <= 0 ? 1 : (50 - y) / 50;
    opacityAnimation = opacityAnimation < -1 ? -1 : opacityAnimation;
    if (
      locationAndDateMonitorVisible !==
        this.state.locationAndDateMonitorVisible ||
      isShowVRIcon !== this.state.isShowVRIcon ||
      fixOpacityAnimation !== this.state.fixOpacityAnimation ||
      opacityAnimation !== this.state.opacityAnimation ||
      curFoorName !== foorName
    ) {
      this.setState({
        locationAndDateMonitorVisible,
        isShowVRIcon, // VR 置顶入口不展示
        fixOpacityAnimation,
        opacityAnimation,
        isFitVisibale,
        noFitVisibale,
        recommendHeaderVisibale,
        loadNextPageVendorList,
        curFoorName: foorName,
        curClickLogData,
      });
    }

    this.scrollY = Math.floor(y);

    this.handleVideoPauseAndPlay();
    this.backToTopBtnShow(y);
  };

  backToTopBtnShow = y => {
    const { showBackToTopBtnAtRight } = this.state;
    const scrollHeight = Dimensions.get('window').height * 0.7;
    if (y > scrollHeight) {
      if (!showBackToTopBtnAtRight) {
        this.setState({
          showBackToTopBtnAtRight: true,
        });
      }
      if (this.backToTopBtnAtRight) {
        this.backToTopBtnAnim({ right: getPixel(-40), opacity: 0.8 });
      }
    } else if (showBackToTopBtnAtRight) {
      this.setState({
        showBackToTopBtnAtRight: false,
      });
    }
  };

  onMomentumScrollEnd = ({ nativeEvent }) => {
    const { y } = nativeEvent.contentOffset;
    this.scrollY = Math.floor(y);
    this.isScrolling = false;
    this.isScrollingByBounces = false;
    this.onScrollEndDrag();
  };

  onScrollEndDrag = () => {
    if (this.isScrolling) return;
    const { showBackToTopBtnAtRight } = this.state;
    if (showBackToTopBtnAtRight) {
      this.backToTopBtnAnim({ right: getPixel(32), opacity: 1 });
    }
    this.isScrolling = false;
  };

  scrollContainerToTop = () => {
    this.scrollViewRef?.scrollTo({
      x: 0,
      y: 0,
      animated: true,
    });
    CarLog.LogCode({ name: '点击_产品详情页_快速回到顶部' });
  };

  onHeaderLayout = ({ nativeEvent }) => {
    this.setState({
      headerHeight: nativeEvent.layout.height,
    });
  };

  onVendorListTopLayout = ({ nativeEvent }) => {
    this.setState({
      vendorListTopHeight: nativeEvent.layout.height,
    });
  };

  onVendorListSpecificLayout = ({ nativeEvent }) => {
    this.setState({
      isFitVendotListHeight: nativeEvent.layout.height,
    });
  };

  onFloorLayout = (fHeight: number, index: number) => {
    const { vehicleDetailListRes } = this.props;
    const requestId = vehicleDetailListRes?.baseResponse?.requestId;
    if (!this.floorHeight?.[requestId]) {
      this.floorHeight[requestId] = [
        {
          fHeight: 0,
          isExpend: true,
        },
      ];
    }
    this.floorHeight[requestId][index] = {
      fHeight,
      isExpend: fHeight > this.floorHeight[requestId]?.[index]?.fHeight,
    };
  };
  // 货架2楼层收起时，需要把展开收起部分（3条以外的部分）的isExpend关掉
  onHandleExpand = () => {
    const { vehicleDetailListRes } = this.props;
    const requestId = vehicleDetailListRes?.baseResponse?.requestId;
    if (this.floorHeight?.[requestId]?.length) {
      this.floorHeight[requestId].forEach((item, idx) => {
        if (idx > 2) {
          item.isExpend = false;
        }
      });
    }
  };

  onVendorListFilteredLayout = ({ nativeEvent }) => {
    this.setState({
      noFitVendorLIstHeight: nativeEvent.layout.height,
    });
  };

  scrollHandler = () => {};

  handleBackPress = () => {
    this.pageGoBack();
  };

  getProductHeaderProps = memoizeOne(
    (
      data,
      bizType,
      toolBoxCustomerJumpUrl,
      isShowVRIcon,
      vrUrl,
      vehicleCode,
      vehicleDetailListReq,
      shareVehicleInfo,
    ) => {
      return {
        scrollY: 0,
        data,
        bizType,
        toolBoxCustomerJumpUrl,
        scrollHandler: this.scrollHandler,
        handleBackPress: this.handleBackPress,
        onHeaderLayout: this.onHeaderLayout,
        headerContentShowAtCenter: true, // 头部内容居中展示
        vrUrl: isShowVRIcon && vrUrl,
        vehicleCode,
        vehicleDetailListReq,
        shareVehicleInfo,
      };
    },
  );

  queryVehicleDetailListNotUseCache = () => {
    // 不再传uniqSign,以避免服务端命中缓存
    this.props.queryVehicleDetailList({
      reqParam: lodashOmit(this.props.pageParam, 'uniqSign'),
    });
  };

  /* eslint-disable camelcase */
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.isLoading && !nextProps.isLoading) {
      this.performanceMonitor.receiveResStart = new Date();
    }
  }

  componentDidUpdate(prevProps, preState) {
    const { isLoading, isError, appResponseMap } = this.props;
    // 加载完成，设置禁用侧滑有效
    if (!isLoading) {
      this.initialPageEnableDragBack();
    }
    if (!isLoading || isError) {
      this.onPageReady(appResponseMap);
    }
    if (prevProps.isLoading && !isLoading) {
      this.scrollContainerToTop();
    }
    if (this.state.hasFit !== preState.hasFit) {
      this.handleshowFit();
    }
    if (this.state.hasRecommend !== preState.hasRecommend) {
      this.handleshowRecommend();
    }
  }

  handleshowFit = () => {
    this.setState({ hasFit: true });
  };

  handleshowNoFit = () => {
    this.setState({ hasNoFit: true });
  };

  handleshowRecommend = () => {
    this.setState({ hasRecommend: true });
  };

  componentWillUnmount() {
    super.componentWillUnmount();
    this.props.reset();
    const { fromPage } = this.props;
    if (fromPage !== Channel.getPageId().RecommendVehicle.EN) {
      AppContext.setFilterPoi(FilterPoiType.Default);
    }
  }

  pageDidAppear = () => {
    super.pageDidAppear();
    if (this.isImmediatelyResetRouteStack && this.pageAppearCount !== 1) return;
    this.handleLoginChange();
    const {
      isLoginAtBookingPage,
      setIsLoginAtBookingPage,
      setLocationDateInfo,
      hasReceivedCouponAtBooking,
      setHasReceivedCouponAtBooking,
    } = this.props;
    if (isLoginAtBookingPage) {
      setIsLoginAtBookingPage(false);
      this.queryVehicleDetailListNotUseCache();
    } else if (
      this.props.isCouponBookAtBookingPage ||
      hasReceivedCouponAtBooking
    ) {
      if (this.props.isCouponBookAtBookingPage) {
        // 点击领券订产品进入填写页回退时，需要更新当前报价+劵包列表
        this.props.setIsCouponBookAtBookingPage(false);
      }
      // 清空列表页缓存,不命中前端缓存
      AppContext.setUserFetchCacheId({
        actionType: 'couponBook',
      });
      this.queryVehicleDetailListNotUseCache();
      this.props.fetchReceivePromotion();
      if (hasReceivedCouponAtBooking) {
        setHasReceivedCouponAtBooking(false);
        // 重刷列表页
        setListStatusData(listStatusKeyList.refreshFromVendorListPage, true);
      }
    }
    if (this.pageAppearCount !== 1) {
      this.multiContainerWhenPageDidAppear(setLocationDateInfo);
    }
  };

  multiContainerSaveLocationAndDate = data => {
    const uniqueId = Utils.openUrlVendorListPageUniqueId();
    if (uniqueId && data) {
      CarStorage.save(MultiVendorListKey, {
        data,
        currentUniqueId: uniqueId,
      });
    }
  };

  multiContainerWhenPageDidAppear = setLocationDateInfo => {
    // 根据localStorage更新后的取还车条件信息
    const cache = CarStorage.privateLoadSync(MultiVendorListKey);
    if (cache) {
      try {
        const uniqueId = Utils.openUrlVendorListPageUniqueId();
        const { data, currentUniqueId } = JSON.parse(cache) || {};
        // currentUniqueId uniqueId 都为空时是：首个VendorList页面且未设置Storage中时间
        if ((currentUniqueId !== uniqueId || !currentUniqueId) && data) {
          setLocationDateInfo(data);
          this.queryVehicleDetailListNotUseCache();
          this.props.fetchLimitContent(true);
        }
        if (uniqueId) {
          // 非首个VendorList页面，需保存currentUniqueId标识已使用Storage数据
          this.multiContainerSaveLocationAndDate(data);
        } else {
          // 首个VendorList页面，使用过数据后直接删除
          CarStorage.remove(MultiVendorListKey);
        }
      } catch {
        CarStorage.remove(MultiVendorListKey);
      }
    }
  };

  pageDidDisappear() {
    super.pageDidDisappear();
  }

  // 记录登录态更新
  handleLoginChange = () => {
    const isLogin = User.isLoginSync();
    if (this.isLogin !== isLogin) {
      this.isLogin = true;
      this.setIsLoginChanged(true);
      // 更新劵接口
      this.props.fetchReceivePromotion();
    }
  };

  setIsLoginChanged = (flag: boolean) => {
    this.isLoginChanged = flag;
  };

  onBackAndroid = () => {
    const { showModalKey, locationDatePopVisible } = this.props;
    const { isShowOptimizeStoreModal, lessModalVisible, noLimitModalVisible } =
      this.state;
    if (showModalKey) {
      // 如果优选好店二级窗口展示， 则先关闭优选好店二级弹窗
      if (isShowOptimizeStoreModal) {
        this.hideOptimizeStoreModal();
      } else if (lessModalVisible) {
        this.hideLessModal();
      } else if (noLimitModalVisible) {
        this.hideNoLimitModal();
      } else {
        this.props.closeVendorListModal(showModalKey);
      }
    } else if (locationDatePopVisible) {
      this.props.setLocationAndDatePopIsShow({ visible: false });
    } else {
      this.pageGoBack();
    }
  };

  pageGoBack = () => {
    // 国内详情页返回时调用调研
    const isISDProductBack = GetAB.isISDProductBack();
    if (isISDProductBack && !AppContext.isReachedBookingPage) {
      setTimeout(() => {
        const numbers = [3074, 3081];
        const randomIndex = Math.floor(Math.random() * numbers.length);
        Bridge.callNative('InstantSurvey', 'showTipView', {
          senceId: numbers[randomIndex],
        });
      }, 500);
    }
    this.pop();
  };

  getConfig = () => {
    const config = homeConfig;
    const { marktingFooterData: footDataAlias } = config;
    return {
      marktingFooterData: {
        ...footDataAlias.isd,
        bottomImg: `${ImageUrl.DIMG04_PATH}1tg4c12000dg92m3rDFED.png`,
        wrapStyle: styles.marketingFooterWrapStyle,
      },
    };
  };

  handleCloseCouponModal = () => {
    const param = {
      pageParam: this.props.pageParam,
      isListReceiveSuccess: this.props.isListReceiveSuccess,
      isLoginChanged: this.isLoginChanged,
      setIsLoginChanged: this.setIsLoginChanged,
    };
    this.props.handleCloseCouponModal(param);
  };

  goMap = limitScope => {
    this.push(Channel.getPageId().LimitMap.EN, { limitScope });
  };

  // 跳转填写页
  handleGoToBooking = (type: TotalPriceModalType, uniqueCode?: string) => {
    this.props.goToBooking({
      type,
      pTime: this.props.pTime,
      uniqueCode,
    });
  };

  // 跳转填写页
  handleGoToBooking2 = (
    type: TotalPriceModalType,
    floorId?: string,
    packageCode?: string,
    index?: number,
  ) => {
    this.props.goToBooking({
      type,
      pTime: this.props.pTime,
      floorId,
      packageCode,
    });
    // 设置当前点击报价位置数据
    AppContext.setVehicleLogInfo({
      quoteRank: index,
    });
  };

  // 打开保险弹层
  showCarServiceDetail = (
    reference: any,
    code: string,
    allPackageLevel: string[],
  ) => {
    const { setInsuranceDetail } = this.props;
    let tempCode = code;
    // 如果code是prep，则说明选中的是无忧租，需要和保险详情接口的code对应，前端需要手动替换成20000001
    if (tempCode === 'prep') {
      tempCode = '20000001';
    }
    this.setState({
      activeInsuranceCode: tempCode,
    });
    setInsuranceDetail({});
    const { pageParam } = this.props;
    this.setState({
      allPackageLevel,
    });
    this.props.queryInsuranceDetails({
      pageParam,
      reference,
      allPackageLevel,
    });
    this.props.showCarServiceDetailModal();
  };

  handleModalContinue = () => {
    const { floorId, packageCode } = this.props;
    CarLog.LogCode({
      name: '点击_门店弹层_立即预订',
      info: this.props.vehicleDetailModalLogInfo,
    });
    if (CarServerABTesting.isISDShelves2B()) {
      this.handleGoToBooking2(
        TotalPriceModalType.VendorListModal,
        floorId,
        packageCode,
      );
    } else {
      this.handleGoToBooking(
        TotalPriceModalType.VendorListModal,
        this.props.uniqueCode,
      );
    }
  };

  showFuelTypeModal = fuelType => {
    this.setState({
      isFuelTypeModalVisible: true,
      fuelType,
    });
  };

  hideFuelTypeModal = () => {
    this.setState({
      isFuelTypeModalVisible: false,
    });
  };

  showLessModal = () => {
    this.setState({
      lessModalVisible: true,
    });
  };

  hideLessModal = () => {
    this.setState({
      lessModalVisible: false,
    });
  };

  showNoLimitModal = noLimitDesc => {
    this.setState({
      noLimitModalVisible: true,
      noLimitDesc,
    });
  };

  hideNoLimitModal = () => {
    this.setState({
      noLimitModalVisible: false,
    });
  };

  scrollToPrice = () => {
    const { vendorListTopHeight, headerHeight } = this.state;
    const scrollY = vendorListTopHeight - headerHeight + getPixel(25);
    if (scrollY > 0) {
      this.scrollViewRef?.scrollTo({
        y: scrollY,
      });
    }
  };

  lookPrice = () => {
    const { showFullImmerse } = this.state;
    this.props.closeVehicleModal();
    this.onLeftScroll();
    setTimeout(
      () => {
        this.scrollToPrice();
      },
      // Android且全屏展示时，需要等待全屏->半屏动画结束后再执行，否则定位不准
      showFullImmerse && isAndroid ? 300 : 0,
    );
  };

  showOptimizeStoreModal = () => {
    this.setState({
      isShowOptimizeStoreModal: true,
    });
  };

  hideOptimizeStoreModal = () => {
    this.setState({
      isShowOptimizeStoreModal: false,
    });
  };

  // 设置总价说明弹层展示与关闭,包含报价的总价问号+信息确认弹层的明细按钮
  handleSetPriceDetailModal = (
    type: TotalPriceModalType,
    uniqueCode?,
    hasFees?: boolean,
  ) => {
    const {
      priceDetailModalVisible,
      totalPriceModalVisible,
      vehicleDetailModalLogInfo,
    } = this.props;
    this.props.setPriceDetailModal({
      type,
      uniqueCode,
      hasFees,
      curPriceDetailModalVisible: priceDetailModalVisible,
      curTotalPriceModalVisible: totalPriceModalVisible,
    });
    CarLog.LogCode({
      name: TotalPriceEnName[type],
      info: vehicleDetailModalLogInfo,
    });
  };

  // 设置总价说明弹层展示与关闭,包含报价的总价问号+信息确认弹层的明细按钮
  handleSetPriceDetailModal2 = (
    type: TotalPriceModalType,
    floorId?: string,
    packageCode?: string,
  ) => {
    const {
      priceDetailModalVisible,
      totalPriceModalVisible,
      vehicleDetailModalLogInfo,
    } = this.props;
    this.props.setPriceDetailModal({
      type,
      curPriceDetailModalVisible: priceDetailModalVisible,
      curTotalPriceModalVisible: totalPriceModalVisible,
      floorId,
      packageCode,
    });
    CarLog.LogCode({
      name: TotalPriceEnName[type],
      info: vehicleDetailModalLogInfo,
    });
  };

  openOptimizationStrengthenModal = () => {
    this.props.showOptimizationStrengthenModal();
    CarLog.LogCode({
      name: '点击_门店弹层_优选简介浮层',

      info: this.props.vehicleInfoLogData,
    });
  };

  videoRefCallBack = ref => {
    this.videoRef = ref;
  };

  videoStateChangedCallBack = videoPlayerState => {
    this.videoPlayerState = videoPlayerState;
  };

  handleDatePickerRef = ref => {
    this.datePickerRef = ref;
  };

  handleSearchPanelModalRef = ref => {
    this.searchPanelModalRef = ref;
  };

  renderFooter = memoizeOne(
    (
      price,
      descText,
      minTotalPrice,
      minTotalPriceDesc,
      minTotalPriceOtherDesc,
    ) => {
      return (
        <VehicleModalFooter
          price={price}
          lookPrice={this.lookPrice}
          descText={descText}
          minTotalPrice={minTotalPrice}
          minTotalPriceDesc={minTotalPriceDesc}
          minTotalPriceOtherDesc={minTotalPriceOtherDesc}
        />
      );
    },
  );

  refCallBack = ref => {
    this.headerRef = ref;
  };

  getMonitorStyle = memoizeOne(headerHeight => {
    const monitorNewStyle = { paddingTop: 0, paddingBottom: getPixel(12) };
    const monitorStyle = Utils.getPackageStyle([
      { top: headerHeight },
      monitorNewStyle,
    ]);
    return monitorStyle;
  });

  // #region 半屏滑动相关代码
  getInterpolate = scrollY => {
    const y1 =
      (FULL_THRESHOLD / IMMERSE_MAX_HEIGHT + 0.03) * IMMERSE_MAX_HEIGHT;
    return (scrollY * y1) / FULL_THRESHOLD;
  };

  onPanResponderMoveOnAndroid(e, gestureState) {
    //* Android使用LayoutAnimation
    const { dx, dy } = gestureState;
    if (Math.abs(dx / dy) >= 1) {
      return;
    }

    if (!this.animating) {
      if (!this.state.showFullImmerse && dy > 5) {
        this.toggleFullScreenAnimatedOnAndroid(true);
      } else if (this.state.showFullImmerse && dy < -6) {
        this.toggleFullScreenAnimatedOnAndroid(false);
      }
    }
  }

  //! 沉浸式手势(在动画过程中，不做改变手势的响应)
  //* fullScreenHeight: 空值时，使用插值动画；达到阈值后重新设置动画值
  onPanResponderGrant() {
    if (!this.isScrollingByBounces) {
      //* 是否开启手势系统，准备展示沉浸式大图
      this.enablePanResponder = true;
    } else {
      this.enablePanResponder = false;
    }
  }

  onPanResponderMoveOnIos(e, gestureState) {
    const { dx, dy } = gestureState;
    //* 水平滑动 (dy && dx / dy > 1) || (dy === 0 && dx  > 0.5)
    //* 针对左滑返回上一页，(可能)同时下滑
    if ((!isAndroid && dy && Math.abs(dx / dy) > 1) || (dy === 0 && !dx)) {
      return;
    }
    if (this.enablePanResponder) {
      if (!this.state.showFullImmerse && dy > 0) {
        if (!this.isFullAnimating) {
          this.setState({
            immerseTransYAni: this.immerseTransYAni
              .translateY(dy)
              .step()
              .export(),
            immerseTransYAniImage: this.immerseTransYAni
              .translateY(-dy / 2)
              .step()
              .export(),
          });
        }

        if (dy < FULL_THRESHOLD) {
          //* 动画1： 插值
          if (!this.animating) {
            this.isInterpolate = true;
            this.setState({
              scrollEnabledIOS: false,
            });
            this.animating = true;
          }
        } else if (dy >= FULL_THRESHOLD) {
          //* 动画2：Animate.timing
          this.isInterpolate = false;
          this.animating = false;
          if (!this.useAniTimingIn) {
            // 防止多次执行toggleFullScreenAnimatedOnIos
            this.toggleFullScreenAnimatedOnIos(true);
          }
        }
      } else if (this.state.showFullImmerse && dy < -20 && Math.abs(dx) < 10) {
        //* 展示完整大图后 && 向上滚动 => 隐藏大图
        if (!this.animating) {
          this.toggleFullScreenAnimatedOnIos(false);
        }
      }
    }
  }

  onPanResponderRelease(e, gestureState) {
    const { dy } = gestureState;
    //* 手势系统下，滚动距离在0-阈值之间 && 插值动画 => 恢复到不显示大图的初始状态
    if (this.enablePanResponder && this.isInterpolate && dy < FULL_THRESHOLD) {
      this.toggleFullScreenAnimatedOnIos(false);
    }
  }

  animatedMenuEnd = () => {
    const { hideFullImmerse } = this.state;
    if (!this.isScrollingByBounces) return;
    this.isScrollingByBounces = false;
    if (this.isFullAnimating) {
      this.isFullAnimating = false;
    }
    if (hideFullImmerse) {
      // 操作过快会导致展开/收起动画的start回调时机异常（正常顺序: 展开->展开结束->收起->收起结束），如收起动画时执行顺序变成: 收起动画->展开动画->收起动画结束回调->展开动画结束回调，从而导致获取动画值是null，error(stopTracking' of null)
      if (this.animatedType === 'show') {
        this.animating = true;
        // 延迟全屏动画时间设置可继续滑动，防止滑动过快
        setTimeout(() => {
          this.animating = false;
        }, ShowFullImmerseDuration);
        this.setState({
          showFullImmerse: true,
          hideFullImmerse: false,
          isAnimated: false,
        });
      } else {
        this.setState({
          scrollEnabledIOS: true,
          readyImmerse: false,
          showFullImmerse: false,
          hideFullImmerse: true,
        });
        this.animating = false;
        this.useAniTimingIn = false;
      }
    } else {
      this.animatedType = 'hide';
      this.animating = false;
      this.setState({
        scrollEnabledIOS: true,
        readyImmerse: false,
        showFullImmerse: false,
        hideFullImmerse: true,
        isAnimated: false,
        immerseTransYAni: this.immerseTransYAni
          .translateY(0)
          .step({
            duration: ShowFullImmerseDuration,
          })
          .export(),
        immerseTransYAniImage: this.immerseTransYAniImage
          .translateY(0)
          .step({
            duration: ShowFullImmerseDuration,
          })
          .export(),
      });
    }
  };

  // 完全展示/隐藏沉浸式的动画（Ios端）
  toggleFullScreenAnimatedOnIos(isShow) {
    let duration = ShowFullImmerseDuration;
    if (isShow && this.state.hideFullImmerse) {
      this.isFullAnimating = true;
      this.animating = true;
      this.useAniTimingIn = true;
      this.animatedType = 'show'; // 展开动画结束执行回调的前一个状态（是展开动画还是收起动画结束回调）
      this.isScrollingByBounces = true;
      this.immerseTransYAni
        .translateY(IMMERSE_MAX_HEIGHT - CAROUSEL_HEIGHT)
        .step({
          duration,
        });
      this.immerseTransYAniImage
        .translateY(-(IMMERSE_MAX_HEIGHT - CAROUSEL_HEIGHT) / 2)
        .step({
          duration,
        });
      this.setState({
        scrollEnabledIOS: false,
        readyImmerse: true,
        immerseTransYAni: this.immerseTransYAni.export(),
        immerseTransYAniImage: this.immerseTransYAniImage.export(),
        isAnimated: false,
      });
    } else if (!isShow) {
      this.isScrollingByBounces = true;
      //* 隐藏沉浸式大图 -> 显示常规轮播图
      duration = 400;
      if (!this.state.showFullImmerse && !this.useAniTimingIn) {
        duration = 250;
        this.setState({
          update: !this.state.update,
        });
      }
      this.animating = false;
      this.useAniTimingIn = false;
      this.immerseTransYAni.translateY(IMMERSE_Y0).step({ duration });
      this.immerseTransYAniImage.translateY(-IMMERSE_Y0 / 2).step({ duration });
      this.immerseTransYInter = null;
      this.setState({
        showFullImmerse: false,
        immerseTransYAni: this.immerseTransYAni.export(),
        immerseTransYAniImage: this.immerseTransYAniImage.export(),
        isAnimated: false,
      });
    }
  }

  // 完全展示/隐藏沉浸式（Android端）
  toggleFullScreenAnimatedOnAndroid(isShow, cb?) {
    if (isShow) {
      //* 显示沉浸式大图
      this.setState({
        scrollEnabledAndroid: false,
      });
      this.animating = true;
      LayoutAnimation.configureNext(
        {
          duration: 300,
          delete: { type: 'linear', property: 'opacity' },
          update: { type: 'linear', property: 'opacity' },
        },
        () => {
          this.animating = false;
          this.setState({
            showFullImmerse: true,
          });
        },
      );
    } else {
      //* 隐藏沉浸式大图 -> 显示常规轮播图
      this.animating = true;
      LayoutAnimation.configureNext(
        {
          duration: 300,
          create: { type: 'linear', property: 'opacity' },
          update: { type: 'linear', property: 'opacity' },
        },
        () => {
          this.animating = false;
          this.setState(
            {
              scrollEnabledAndroid: true,
            },
            () => {
              cb?.();
            },
          );
        },
      );
      this.setState({
        showFullImmerse: false,
      });
    }
    this.fullScreenHeight = isShow ? IMMERSE_MAX_HEIGHT : CAROUSEL_HEIGHT;
    this.setState({
      readyImmerse: isShow,
    });
  }

  carouselImagePress = (media: MediaInfo, index?: number) => {
    const {
      vehicleCode,
      price,
      minTotalPrice,
      minTotalPriceDesc,
      minTotalPriceOtherDesc,
    } = this.props;

    CarLog.LogCode({
      name: '点击_产品详情页_车型头图',

      info: {
        vehicleCode,
        mediaGroupType: media?.groupType,
      },
    });
    // 跳转官方相册落地页
    this.push(Channel.getPageId().Album.EN, {
      vehicleCode,
      price,
      minTotalPrice,
      minTotalPriceDesc,
      minTotalPriceOtherDesc,
      lookPrice: this.lookPrice,
      groupType:
        !media?.groupType ||
        media.groupType === MediaGroupType.Cover ||
        index === 0
          ? MediaGroupType.All
          : media.groupType,
    });
  };

  onLeftScroll = () => {
    if (isAndroid) {
      this.toggleFullScreenAnimatedOnAndroid(false);
    } else {
      this.toggleFullScreenAnimatedOnIos(false);
    }
  };

  changeBanner = groupStartIndex => {
    this.headerCarousel.swiperSwipe(groupStartIndex);
  };

  onBouncesBlurBeginDrag = () => {
    this.enablePanResponder = false;
  };

  onBouncesBlurScrollEndDrag = () => {
    this.enablePanResponder = true;
  };

  handleBusinessLicenseClose = () => {
    const { setBusinessLicenseModal } = this.props;
    setBusinessLicenseModal(false);
  };

  handleEtcIntroModalClose = () => {
    const { setEtcIntroModal } = this.props;
    setEtcIntroModal(false);
  };

  renderPage() {
    const {
      bbkVehicleNameProps,
      isShowRestAssured,
      priceListLen,
      tangramEntranceInfos,
      vehicleCode,
      hasLaborDayLabel,
      vendorListMarketTheme,
      isShowMarketTheme,
      config: remoteQConfig,
      price,
      descText,
      minTotalPrice,
      minTotalPriceDesc,
      minTotalPriceOtherDesc,
      isNomatch,
      isLoading,
      headerData,
      bizType,
      toolBoxCustomerJumpUrl,
      isBusinessLicenseModalVisible,
      isEtcIntroModalVisible,
      pageParam,
      vehicleDetailListReq,
      shareVehicleInfo,
      reducedCarbonEmissionRatio,
      isCouponEntry,
      insuranceDetails,
      ipollPositionType,
      ipollPositionNum,
      sceneid,
      ipollLogData,
    } = this.props;

    const {
      opacityAnimation,
      fixOpacityAnimation,
      headerHeight,
      locationAndDateMonitorVisible,
      isShowOptimizeStoreModal,
      isFuelTypeModalVisible,
      fuelType,
      lessModalVisible,
      noLimitModalVisible,
      noLimitDesc,
      showBackToTopBtnAtRight,
      backToTopBtnAnim,
      isShowVRIcon,
      isLazyLoad,
      isFitVisibale,
      noFitVisibale,
      recommendHeaderVisibale,
      hasFit,
      hasNoFit,
      hasRecommend,
      showFullImmerse,
      loadNextPageVendorList,
      curFoorName,
      curClickLogData,
      activeInsuranceCode,
    } = this.state;

    const { marktingFooterData } = this.getConfig();
    const monitorNewStyle = {
      paddingTop: 0,
      paddingBottom: getPixel(0),
    };
    const monitorStyle = Utils.getPackageStyle([
      { top: headerHeight },
      monitorNewStyle,
    ]);
    const titleStyle = Utils.getPackageStyle([
      { top: headerHeight + getPixel(108) },
      { zIndex: isHarmony ? 1 : 100 },
    ]);
    const showBackToTopBtn = !!showBackToTopBtnAtRight;

    const BouncesScrollProps = !isHarmony && {
      scrollEnabled: isAndroid
        ? this.state.scrollEnabledAndroid
        : this.state.scrollEnabledIOS,
      bouncesDelay: false,
      bouncesViewHeight: CAROUSEL_HEIGHT,
      scrollEventThrottle: 1,
      isHeaderHaveBanner: false,
      automaticallyAdjustContentInsets: false,
      scrollViewPanResponder: this.scrollViewPanResponder,
    };

    const isShelvesServer = CarServerABTesting.isISDShelves2B();
    const isISDProductIpoll = GetABCache.isISDProductIpoll();
    const env = xEnv.getDevEnv()?.toLowerCase();
    return (
      <ViewPort>
        <View className={c2xStyles.page}>
          <ProductHeader
            refFn={this.refCallBack}
            opacityAnimation={opacityAnimation}
            fixOpacityAnimation={fixOpacityAnimation}
            channelType={Utils.getType()}
            {...bbkVehicleNameProps}
            {...this.getProductHeaderProps(
              headerData,
              bizType,
              toolBoxCustomerJumpUrl,
              isShowVRIcon,
              '',
              vehicleCode,
              vehicleDetailListReq,
              shareVehicleInfo,
            )}
            isHideLinear={true}
            isHideShare={!remoteQConfig?.isShowIsdShareBtn}
            isShowProductName={true}
            leftIconTestID={UITestID.car_testid_page_vendorlist_header_goback}
          />

          {locationAndDateMonitorVisible && !isLoading && (
            <LocationAndDateMonitor
              style={monitorStyle}
              isShelvesServer={isShelvesServer}
              floorName={curFoorName}
              clickLogData={curClickLogData}
              clickWithLog={this.showSearchSelectorWrapWithLog}
              datePickerRef={this.datePickerRef}
              testID={CarLog.LogExposure({
                name: '曝光_产品详情页_修改取还车信息',
                info: {
                  vehicleCode,
                },
              })}
            />
          )}

          <BouncesScrollView
            ref={scrollView => {
              this.scrollViewRef = scrollView;
            }}
            showsVerticalScrollIndicator={false}
            style={styles.mainScroll}
            scrollEventThrottle={16}
            onScroll={this.onScroll}
            onMomentumScrollEnd={this.onMomentumScrollEnd}
            onScrollEndDrag={this.onScrollEndDrag}
            testID={UITestID.car_testid_page_vendorList_ScrollView}
            {...BouncesScrollProps}
          >
            <XAnimated.View
              animation={isAndroid ? 0 : this.state.immerseTransYAni}
              onTransitionEnd={this.animatedMenuEnd}
            >
              <View onLayout={this.onVendorListTopLayout}>
                <VehicleImageBounces
                  // @ts-ignore
                  immerseMoveY={this.state.immerseTransYAniImage}
                  showFullImmerse={showFullImmerse}
                  height={this.fullScreenHeight}
                  onBeginDrag={this.onBouncesBlurBeginDrag}
                  onScrollEndDrag={this.onBouncesBlurScrollEndDrag}
                  onLeftScrollDrag={this.onLeftScroll}
                  onImagePress={this.carouselImagePress}
                />

                <View className={c2xStyles.contentMargin_B2} />
                <MarketingBanner
                  isShowRestAssured={isShowRestAssured}
                  hasLaborDayLabel={hasLaborDayLabel}
                  marketingAtmosphere={this.props.marketingAtmosphere}
                  marketTheme={vendorListMarketTheme}
                  isShowMarketTheme={isShowMarketTheme}
                  yunnanBannerInfo={this.props.yunnanBannerInfo}
                />
                {isShelvesServer ? (
                  <VehicleAndLimit2
                    onPressVehicleName={this.props.openVehicleModal}
                    hasLaborDayLabel={hasLaborDayLabel}
                    showFullImmerse={showFullImmerse}
                    onPressShowLessModal={this.showLessModal}
                    onPressShowNoLimitModal={this.showNoLimitModal}
                  />
                ) : (
                  <VehicleAndLimitB
                    onPressVehicleName={this.props.openVehicleModal}
                    hasLaborDayLabel={hasLaborDayLabel}
                    showFullImmerse={showFullImmerse}
                    onPressShowLessModal={this.showLessModal}
                    onPressShowNoLimitModal={this.showNoLimitModal}
                  />
                )}
                {!isShelvesServer && (
                  <CouponEntryNew vehicleCode={vehicleCode} />
                )}
                {!!remoteQConfig?.vendorListTangramEntrance &&
                  tangramEntranceInfos?.length > 0 &&
                  tangramEntranceInfos.map(item => (
                    <TangramEntrance
                      key={item.title}
                      data={item}
                      fromPage={this.getPageId()}
                    />
                  ))}
              </View>
              {isShelvesServer ? (
                <VendorListShelves2Container
                  isCouponEntry={isCouponEntry}
                  filterLabelsStr={pageParam?.filterLabelsStr}
                  onPressQuestion={this.handleSetPriceDetailModal2}
                  onPressBooking={this.handleGoToBooking2}
                  showCarServiceDetail={this.showCarServiceDetail}
                  onSpecificLayout={this.onVendorListSpecificLayout}
                  onFloorLayout={this.onFloorLayout}
                  showSearchSelectorWrapWithLog={
                    this.showSearchSelectorWrapWithLog
                  }
                  vehicleCode={vehicleCode}
                  onScrollToFloor={this.onScrollToFloor}
                  sectionHeaderTestID={CarLog.LogExposure({
                    name: '曝光_产品详情页_修改取还车信息',
                    info: {
                      vehicleCode,
                    },
                  })}
                  handleExpand={this.onHandleExpand}
                  ipollSceneid={sceneid}
                  ipollPositionNum={ipollPositionNum}
                  ipollLogData={ipollLogData}
                />
              ) : (
                <VendorListShelvesContainer
                  priceListLen={priceListLen}
                  onPressQuestion={this.handleSetPriceDetailModal}
                  onPressBooking={this.handleGoToBooking}
                  filterLabelsStr={pageParam?.filterLabelsStr}
                  onSpecificLayout={this.onVendorListSpecificLayout}
                  onFilteredLayout={this.onVendorListFilteredLayout}
                  showAmount={7}
                  showFilterAmount={2}
                  vehicleCode={vehicleCode}
                  showSearchSelectorWrapWithLog={
                    this.showSearchSelectorWrapWithLog
                  }
                  sectionHeaderTestID={CarLog.LogExposure({
                    name: '曝光_产品详情页_修改取还车信息',

                    info: {
                      vehicleCode,
                    },
                  })}
                  handleshowFit={this.handleshowFit}
                  loadNextPageVendorList={loadNextPageVendorList}
                />
              )}
              {!isLoading &&
                isLazyLoad &&
                isISDProductIpoll &&
                ipollPositionType === 1 &&
                !!sceneid && (
                  <IUrs
                    sceneId={sceneid}
                    env={env}
                    bizId="CAR"
                    locale="zh-CN"
                    containerStyle={styles.iursContainer1}
                    passData={JSON.stringify(ipollLogData)}
                  />
                )}
              {!isLoading && isLazyLoad && (
                <VendorListRecommondLists
                  fromPage={VehicleNewFromPage.fromVendorList}
                  handleshowRecommond={this.handleshowRecommend}
                />
              )}
              {!isLoading &&
                isLazyLoad &&
                isISDProductIpoll &&
                ipollPositionType === 2 &&
                !!sceneid && (
                  <IUrs
                    sceneId={sceneid}
                    env={env}
                    bizId="CAR"
                    locale="zh-CN"
                    containerStyle={styles.iursContainer1}
                    passData={JSON.stringify(ipollLogData)}
                  />
                )}
              {(!isNomatch || !isLoading) && (
                <View className={c2xStyles.marketingFooterWrap}>
                  <MarketingFooter {...marktingFooterData} />
                </View>
              )}
            </XAnimated.View>
          </BouncesScrollView>
        </View>
        <ProductConfirmEffect
          isModalVisible={this.props.productConfirmModalVisible}
        />

        {isLazyLoad && (
          <ProductConfirmModalNew
            page={this}
            pageParam={this.props.pageParam}
            onContinue={this.handleModalContinue}
            onPressOptimize={this.showOptimizeStoreModal}
            onPressEasyLife={this.props.onPressEasyLife}
            visible={this.props.productConfirmModalVisible}
            showFooter={true}
            showPriceDetailModal={this.props.priceDetailModalVisible}
            anchor={this.props.productConfirmAnchor}
            onClose={this.props.closeProductConfirmModal}
            onPressFooter={this.handleSetPriceDetailModal}
            showOptimizationStrengthenModal={
              this.openOptimizationStrengthenModal
            }
            optimizationStrengthenHeaderExposureLogData={
              this.props.vehicleInfoLogData
            }
            showCarServiceDetail={this.showCarServiceDetail}
            onPressShowNoLimitModal={this.showNoLimitModal}
          />
        )}
        {isLazyLoad && (
          <StoreModal
            onMaskPress={this.hideOptimizeStoreModal}
            visible={isShowOptimizeStoreModal}
          />
        )}
        {isLazyLoad && <VendorListEasyLifeModal />}
        {isLazyLoad && (
          <ProductConfirmPriceDetail
            visible={this.props.priceDetailModalVisible}
            onClose={this.props.closePriceDetailModal}
            onContinue={this.handleModalContinue}
            invokeFrom={INVOKE_FROM.VENDORLIST}
            style={
              this.props.productConfirmModalVisible && styles.priceDetailStyle
            }
          />
        )}
        {isLazyLoad && <TimeOutPop />}
        {isLazyLoad && (
          <VehicleModal
            onPressHelp={this.showFuelTypeModal}
            onPressShowLessModal={this.showLessModal}
            footChildren={this.renderFooter(
              price,
              descText,
              minTotalPrice,
              minTotalPriceDesc,
              minTotalPriceOtherDesc,
            )}
          />
        )}
        {isLazyLoad && (
          <FuelTypeModal
            visible={isFuelTypeModalVisible}
            fuelType={fuelType}
            onCancel={this.hideFuelTypeModal}
            info={remoteQConfig?.fuelTypeModalInfo}
          />
        )}
        {isLazyLoad && (
          <LessDetailModal
            visible={lessModalVisible}
            onClose={this.hideLessModal}
            data={reducedCarbonEmissionRatio}
          />
        )}
        {isLazyLoad && (
          <NoLimitModal
            visible={noLimitModalVisible}
            onClose={this.hideNoLimitModal}
            content={noLimitDesc}
          />
        )}
        {isLazyLoad && <LimitRulesPop goMap={this.goMap} />}
        {isLazyLoad && (
          <CouponModal
            vehicleCode={vehicleCode}
            isList={true}
            onCancel={this.handleCloseCouponModal}
            isVendorList={true}
          />
        )}
        {isLazyLoad && (
          <TotalPriceModal pageRef={this} onContinue={this.handleGoToBooking} />
        )}
        {isLazyLoad && <VirtualNumberDialog />}
        {isLazyLoad && (
          <VendorListOptimizationStrengthenModal
            visible={this.props.optimizationStrengthenModalVisible}
            onCancel={this.props.hideOptimizationStrengthenModal}
          />
        )}
        {isLazyLoad && <VirtualNumberStoreModal />}
        {isLazyLoad && (
          <SearchPanelModal
            searchPanelModalRefFn={this.handleSearchPanelModalRef}
            currentPageId={this.getPageId()}
            isHideAutonomy={true}
            fetchVehicleDetailList={this.fetchVehicleDetailList}
            backPageName={PageName.VendorList}
            vehicleCode={vehicleCode}
          />
        )}
        {isLazyLoad && (
          <ListCalendarModal
            handleDatePickerRef={this.handleDatePickerRef}
            calendarModalTypes={CalendarModalTypes.fromVendorListPage}
            fetchVehicleDetailList={this.fetchVehicleDetailList}
            vehicleCode={vehicleCode}
          />
        )}
        {isLazyLoad && (
          <CarServiceDetailModal
            visible={this.props.carServiceDetailVisible}
            onCancel={this.props.hideCarServiceDetailModal}
            data={insuranceDetails?.packageDetailList}
            purchasingNotice={insuranceDetails?.purchasingNotice}
            fromPage={CarServiceFromPageTypes.booking}
            showServiceDetailCode={activeInsuranceCode}
            haveFooter={false}
          />
        )}
        {isLazyLoad && showBackToTopBtn && (
          <BackToTopBtn
            backToTopBtnAnim={backToTopBtnAnim}
            onPress={this.scrollContainerToTop}
            backToTopBtnAnimEnd={this.backToTopBtnAnimEnd}
            testIDKey="曝光_产品详情页_快速回到顶部"
          />
        )}
        {isLazyLoad && <RecommendListTotalPriceModal pageRef={this} />}
        {isLazyLoad && <RecommendListPriceDetailModal pageRef={this} />}
        {isLazyLoad && (
          <BusinessLicenseModal
            visible={isBusinessLicenseModalVisible}
            onClose={this.handleBusinessLicenseClose}
          />
        )}
        {isLazyLoad && (
          <EtcIntroModalContainer
            visible={isEtcIntroModalVisible}
            onClose={this.handleEtcIntroModalClose}
          />
        )}
        {this.props.isDebugMode && (
          <AssistiveTouch
            onPress={() => {
              this.push('Debug');
            }}
          />
        )}
      </ViewPort>
    );
  }
}
