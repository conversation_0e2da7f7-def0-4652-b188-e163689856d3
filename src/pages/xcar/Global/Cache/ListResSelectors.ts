import {
  get as lodashGet,
  groupBy as lodashGroupBy,
  uniqWith as lodashUniqWith,
  map as lodashMap,
} from 'lodash-es';
import {
  getProductGroupsAndCount,
  FilterCalculater,
  combineProductGroups,
  SortType,
} from '@ctrip/rn_com_car/dist/src/Logic';

import produce from 'immer';
import memoize from 'memoize-one';
import uuid from 'uuid';
import { toRMB } from '@ctrip/rn_com_car/dist/src/Shark/src/Index';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { ApiResCode, ListEnum, LogKey, LogKeyDev } from '../../Constants/Index';
import {
  RecommendEventResult,
  ChannelType,
  FilterBarType,
} from '../../Constants/ListEnum';
import Texts from '../../Pages/List/Texts';
// 对列表页响应数据的一系列选择操作方法
import * as CarServerABTesting from '../../Util/ServerABTesting';
import * as CarABTesting from '../../Util/ABTesting';
import AppContext from '../../Util/AppContext';
import Utils from '../../Util/Utils';
import CarLog from '../../Util/CarLog';
import * as GetABCache from '../../Util/CarABTesting/GetABCache';

import ListReqAndResData, { listStatusKeyList } from './ListReqAndResData';
import { packageVehicleRenderUniqId } from '../../State/List/Method';
import { getStore } from '../../State/StoreRef';

const { getLogStrValue } = Utils;

const EMPTY_OBJECT = {};

const { NavGroupCode } = ListEnum;
const getAllCarsConfig = memoize(() => ({
  groupCode: 'all',
  get groupName() {
    return '全部车型';
  },
}));

interface ProductGroupType {
  isNoMatchRecommond?: boolean;
  selectedFilters?: any;
}

enum GetProductGroupsAndCount {
  Normal = 'normal',
  NewNoResult = 'NewNoResult',
}

export const getBaseResData = () =>
  ListReqAndResData.getData(ListReqAndResData.keyList.listProductRes) ||
  Utils.EmptyObj;

export const getBaseResponseMap = () => ({
  ...getBaseResData().appResponseMap,
  callApiCost: getBaseResData().baseResponse?.extMap?.allCost,
  resBodySize: getBaseResData()?.resBodySize,
});

export const getNetworkCost = () =>
  lodashGet(getBaseResponseMap(), 'networkCost');

export const getHasRetry = () =>
  lodashGet(getBaseResponseMap(), 'hasRetry') || false;

export const getListSearchCondition = () =>
  ListReqAndResData.getData(
    ListReqAndResData.keyList.listProductSearchCondition,
  );

// 获取服务端返回的基础的车型组报价数据
export const getBaseProductGroups = () => getBaseResData().productGroups || [];

export const getMergeVehicleExtendInfos = () =>
  getBaseResData().mergeVehicleExtendInfos;

export const getIsFilteredRecommend = () =>
  getBaseResData().filteredRecommendProducts?.length > 0;

export const getHashCode = () => getBaseResData().productGroupsHashCode;

export const getRealRequestId = () => getBaseResData().baseResponse?.requestId;

export const getRecommendProductsList = () =>
  getBaseResData().recommendProductsList || [];

export const getRequestId = () =>
  lodashGet(getBaseResData(), 'baseResponse.requestId');

export const validateIsFromMemoryCache = () => {
  let valid = '0';
  const appResponseMap = getBaseResponseMap();
  if (
    appResponseMap &&
    appResponseMap.isFromCache &&
    appResponseMap.cacheFrom === 'memorey'
  ) {
    valid = '1';
  }

  return valid;
};

export const getIncludeFees = () =>
  getBaseResData().includeFees || EMPTY_OBJECT;

// 获取车型组报价最低价
const getMinDailyPrice = memoize(
  // 需要hashCode控制函数不读缓存，从而返回不同的值
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  hashCode => {
    let minDailyPrice = 0;

    const baseProductGroups = getBaseProductGroups();
    if (baseProductGroups.length > 0) {
      baseProductGroups.forEach((group, index) => {
        if (index === 0 || minDailyPrice > group.dailyPrice) {
          minDailyPrice = group.dailyPrice;
        }
      });
    }
    return minDailyPrice;
  },
  (newArgs, oldArgs) => newArgs[0] === oldArgs[0],
);

// 获取所有筛选项列表
export const getAllFilterMenuItems = () =>
  getBaseResData().filterMenuItems || [];

// 获取快捷筛选呢项列表
export const getQuickFilter = () => getBaseResData().quickFilter || [];

export const getGS = () => getBaseResData().gs || {};

const getFilterItemsByItemCode = (
  itemCode: string,
  allFilterMenuItems = [],
) => {
  const groupCode = '';
  const binaryDigit = -1;
  const name = '';
  let result = {
    groupCode,
    binaryDigit,
    name,
  };
  allFilterMenuItems.forEach(menu => {
    const { filterGroups = [] } = menu;
    filterGroups.forEach(group => {
      const { filterItems = [] } = group;
      filterItems.forEach(item => {
        if (item.itemCode === itemCode) {
          result = item;
        }
      });
    });
  });
  return result;
};

export const getFilterNameByCode = (itemCode: string) => {
  const allFilterMenuItems = getAllFilterMenuItems();
  const filterItem = getFilterItemsByItemCode(itemCode, allFilterMenuItems);
  return filterItem?.name || '';
};

// 对于车型组数据做url跳转置顶
export const getAllProductGroupsForTopAndSort = (
  filterResult,
  allFilterMenuItems,
) => {
  const { productGroups = [] } = filterResult;
  const filterBrand = ListReqAndResData.getData(listStatusKeyList.filterBrand);
  const filterVendor = ListReqAndResData.getData(
    listStatusKeyList.filterVendor,
  );
  const filterGroupId =
    ListReqAndResData.getData(listStatusKeyList.filterGroupId) ||
    NavGroupCode.all;
  if (!filterBrand && !filterVendor) {
    return filterResult;
  }
  const vendorItem = getFilterItemsByItemCode(filterVendor, allFilterMenuItems);
  const { groupCode, binaryDigit } = vendorItem;
  const resultGroup = productGroups;
  // 只有url参数有品牌且没有供应商时，做置顶操作
  if (filterBrand && !filterVendor) {
    resultGroup.map(group => {
      const { productList = [], groupCode: igroupCode } = group;
      if (igroupCode === filterGroupId) {
        // eslint-disable-next-line no-param-reassign
        group.unUrlProduct = true;
        let topIndex = -1;
        productList.map((product, index) => {
          const { vendorPriceList = [] } = product;
          vendorPriceList.map(vendorPrice => {
            const { filterAggregations = [] } = vendorPrice;
            const hasBrand = filterAggregations.find(
              item => item.groupCode === filterBrand,
            );
            if (hasBrand && topIndex < 0) {
              // eslint-disable-next-line no-param-reassign
              group.unUrlProduct = false;
              topIndex = index;
            }
            return true;
          });
          return true;
        });
        if (topIndex >= 0 && !productList[0].isBrowseProduct) {
          const temp = BbkUtils.cloneDeep(productList[topIndex]);
          temp.isBrowseProduct = true; // 设置正在浏览车辆
          productList.splice(topIndex, 1);
          productList.splice(0, 0, temp);
        }
      }
      return true;
    });
  } else if (filterBrand && filterVendor) {
    resultGroup.map(group => {
      const { productList = [], groupCode: igroupCode } = group;
      if (igroupCode === filterGroupId) {
        // eslint-disable-next-line no-param-reassign
        group.unUrlProduct = true;
        let topIndex = -1;
        let vendorIndex = -1;
        let vehicleIndex = -1; // 没有满足车型、品牌的时候置顶的车型下标
        productList.map((product, index) => {
          const { vendorPriceList = [] } = product;
          vendorPriceList.map((vendorPrice, vIndex) => {
            const { filterAggregations = [] } = vendorPrice;
            const hasBrand = filterAggregations.find(
              item => item.groupCode === filterBrand,
            );
            const hasVendor = filterAggregations.find(
              item =>
                item.groupCode === groupCode &&
                item.binaryDigit === binaryDigit,
            );
            if (hasBrand && hasVendor && topIndex < 0) {
              // eslint-disable-next-line no-param-reassign
              group.unUrlProduct = false;
              topIndex = index;
              vendorIndex = vIndex;
            } else if (hasBrand && vehicleIndex < 0) {
              vehicleIndex = index;
            }
            return true;
          });
          return true;
        });
        // 满足条件置顶或者 车型置顶
        if (
          (topIndex >= 0 || vehicleIndex >= 0) &&
          !productList[0].isBrowseProduct
        ) {
          if (vendorIndex >= 0) {
            const temp = BbkUtils.cloneDeep(productList[topIndex]);
            temp.vendorPriceList = [temp.vendorPriceList[vendorIndex]];
            temp.isUrlTop = true; // 设置车型为置顶车型
            temp.isBrowseProduct = true; // 设置正在浏览车辆
            productList.splice(0, 0, temp);
          } else {
            const temp = BbkUtils.cloneDeep(productList[vehicleIndex]);
            productList.splice(vehicleIndex, 1);
            productList.splice(0, 0, temp);
          }
        }
      }
      return true;
    });
  }
  return filterResult;
};

// 全部车型根据批次标识进行过滤和排序
export const sortByBatchNo = productList => {
  const firstBatch = productList
    ?.filter(item => item.batchNo === 1)
    ?.sort((a, b) => a.sortNum - b.sortNum);
  const secondBatch = productList
    ?.filter(item => item.batchNo === 2)
    ?.sort((a, b) => a.sortNum - b.sortNum);
  return [...firstBatch, ...secondBatch];
};

// 获取筛选后的所有车型组下的报价数据(包含全部车型)
export const getAllProductGroupsAndCount = ({
  selectedFilters = null,
  isNoMatchRecommond = false,
}: ProductGroupType) => {
  const hashCode = getHashCode();
  // @ts-ignore
  const state = getStore()?.getState();
  const { groupCode, groupName } = getAllCarsConfig();
  const allCarsConfig = {
    groupCode,
    groupName,
  };
  const allFilterMenuItems = getAllFilterMenuItems();
  let selectFilter = selectedFilters || state?.List?.selectedFilters;

  // 列表页提升项目增加项目，增加推荐排序移除标识
  if (!GetABCache.isOSDListAllRefresh()) {
    selectFilter = produce(selectFilter, draftSelectFilter => {
      // eslint-disable-next-line no-param-reassign
      draftSelectFilter.isBatchConcat = true;
    });
  }
  let filterResult = getProductGroupsAndCount(
    getBaseProductGroups(), // zxy-todo 需要根据 `isNoMatchRecommond` 获取无结果推荐数据
    hashCode,
    isNoMatchRecommond ? null : allCarsConfig,
    allFilterMenuItems,
    selectFilter,
    GetProductGroupsAndCount.Normal,
  );

  const filterBrand = ListReqAndResData.getData(listStatusKeyList.filterBrand);
  const filterVendor = ListReqAndResData.getData(
    listStatusKeyList.filterVendor,
  );
  if (filterBrand || filterVendor) {
    // 返回车型组信息针对url上传递的车辆品牌，供应商做特殊处理
    filterResult = getAllProductGroupsForTopAndSort(
      filterResult,
      allFilterMenuItems,
    );
  }

  // 对全部车型根据批次进行二次排序，保证为一二批拼接顺序
  const allProducts = filterResult?.productGroups?.[0]?.productList;
  if (
    !GetABCache.isOSDListAllRefresh() &&
    !CarServerABTesting.isRecommend() && // 无结果推荐结果不做批次处理
    allProducts?.length > 0 &&
    selectFilter.sortFilter === SortType.recommended
  ) {
    filterResult.productGroups[0].productList = sortByBatchNo(allProducts);
  }
  return filterResult;
};

export const getNoResultAllProductGroupsAndCount = (
  productGroups,
  productGroupsHashCode,
  selectedFilters,
) => {
  const { groupCode, groupName } = getAllCarsConfig();
  const allCarsConfig = {
    groupCode,
    groupName,
  };
  const allFilterMenuItems = getAllFilterMenuItems();
  const filterResult = getProductGroupsAndCount(
    productGroups,
    productGroupsHashCode,
    allCarsConfig,
    allFilterMenuItems,
    selectedFilters,
    GetProductGroupsAndCount.NewNoResult,
  );

  return filterResult;
};

export const getNewRecommendProductsList = (
  selectedFilters: any,
  recommendProductsList: any,
) => {
  const groupListDataList = [];
  if (recommendProductsList.length) {
    recommendProductsList.forEach((item: any) => {
      const {
        productGroups,
        recommendProductInfo,
        productGroupsHashCode,
        vehicleList,
      } = item;
      const allProductGroups = getNoResultAllProductGroupsAndCount(
        productGroups,
        productGroupsHashCode,
        selectedFilters,
      );
      groupListDataList.push({
        recommendProductInfo,
        productGroups: allProductGroups?.productGroups,
        vehicleList,
      });
    });
  }
  return groupListDataList;
};

export const getAllNoResultProductGroups = (
  selectedFilters: any,
  recommendProductsList: any,
) => {
  const recommendList = getNewRecommendProductsList(
    selectedFilters,
    recommendProductsList,
  );
  const vehicleList = [];
  recommendList.forEach((productGroupItem: any) => {
    const { productGroups } = productGroupItem;
    if (productGroups) {
      productGroups.forEach((productItem: any) => {
        const vehicleIndex = vehicleList.findIndex(
          vehicleItem => vehicleItem.groupCode === productItem.groupCode,
        );
        if (vehicleIndex === -1) {
          vehicleList.push(productItem);
        } else if (
          productItem.filterDailyPrice <
          vehicleList[vehicleIndex].filterDailyPrice
        ) {
          vehicleList.splice(vehicleIndex, 1, productItem);
        }
      });
    }
  });
  return vehicleList;
};

// 获取分页时的所有车型组下的报价数据
// 需要hashCode控制函数不读缓存，从而返回不同的值
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const getAllProductGroupsOfPage = memoize(hashCode => {
  const baseProductGroups = getBaseProductGroups();
  // 多年款的处理
  return baseProductGroups.map(productGroup => {
    if (!productGroup.productList) {
      return productGroup;
    }
    const isEasyLife2024Group =
      productGroup.groupCode === ApiResCode.EasyLife2024GroupCode;
    let isPriceGroup = false;
    productGroup.productList.map((m, index) => {
      if (!m) return m;
      /* eslint-disable no-param-reassign */
      m.logicIndex = index;
      // 置顶车辆不参与合并车型组
      m.isGroup = m.group > 0 && m.productTopInfo !== 2; // 确保只有两个值 true false
      if (!isPriceGroup && m.isGroup) isPriceGroup = true;
      m.renderUniqId = packageVehicleRenderUniqId(m);
      return m;
    });
    if (!isPriceGroup || isEasyLife2024Group) return productGroup;

    const newProductGroup = { ...productGroup };

    const group = lodashGroupBy(newProductGroup.productList, 'isGroup');
    const vehiclePriceList = group.false || [];
    const vehicles = lodashGroupBy(group.true, 'group');
    Object.keys(vehicles).forEach(key => {
      // 多年款内部需要按groupSort排序，先进行排序，再取第一个节点
      vehicles[key].sort((a, b) => a?.groupSort - b?.groupSort);
      const item = BbkUtils.cloneDeep(vehicles[key][0]);
      /**
       * 2021-11-26 添加去重操作
       * 原因: 多年款车型置顶时，会在第一页返回，但如果本身该车型按排序不在第一页，在下拉到其它页时会再次返回，导致重复展示
       * 2023-04-12 牌照合并增加去重逻辑
       * 当是新版本合并牌照B版本（服务AB），除 vehicleCode 外，增加车牌作为去重条件之一
       *  */
      const uniqVehs = lodashUniqWith(vehicles[key], (arrVal, othVal) => {
        return (
          arrVal.vehicleCode === othVal.vehicleCode &&
          arrVal?.productRef?.license === othVal?.productRef?.license
        );
      });

      item.priceGroup = uniqVehs;
      vehiclePriceList.push(item);
    });
    vehiclePriceList.sort((a, b) => a.logicIndex - b.logicIndex);
    newProductGroup.productList = vehiclePriceList;
    return newProductGroup;
  });
});

export const getAllProductGroups = (selectedFilters?: any) => {
  if (CarABTesting.isListInPage()) {
    const hashCode = getHashCode() || uuid();
    return getAllProductGroupsOfPage(hashCode);
  }
  return getAllProductGroupsAndCount({ selectedFilters }).productGroups;
};

export const getRecommondProductGroups = () =>
  getAllProductGroupsAndCount({
    isNoMatchRecommond: true,
  }).productGroups;

// 获取原始报价个数
export const getBaseAllVendorPriceCount = () =>
  getBaseResData().allVendorPriceCount || 0;

// 获取是否是无结果还是少结果
export const getRecommendEventResult = () => {
  const priceCount = getBaseAllVendorPriceCount();
  const recommendEventResult =
    // eslint-disable-next-line no-nested-ternary
    priceCount > 9
      ? RecommendEventResult.Normal
      : priceCount > 0
        ? RecommendEventResult.LessResult
        : RecommendEventResult.NoResult;

  return recommendEventResult;
};

export const getRequestInfo = () => getBaseResData().requestInfo || {};

// 获取所有车型详情数据列表
export const getVehicleList = () => getBaseResData().vehicleList || [];

// 获取所有车型详情列表和报价列表
export const getVehAndProductList = () => ({
  vehicleList: getVehicleList(),
  productGroups: getAllProductGroups(),
});

const getNoFilterAllProductGroups = () => {
  const hashCode = getHashCode();
  const { groupCode, groupName } = getAllCarsConfig();
  const allCarsConfig = {
    groupCode,
    groupName,
    dailyPrice: getMinDailyPrice(hashCode),
  };
  return combineProductGroups(getBaseProductGroups(), allCarsConfig);
};

export const getNoFilterVehAndProductList = () => ({
  vehicleList: getVehicleList(),
  productGroups: getNoFilterAllProductGroups(),
});

export const getRecommendInfo = () => getBaseResData().recommendInfo || {};

export const getRecommendProducts = () => getRecommendInfo().recommendProducts;

export const getIsNewSearchNoResult = () => getRecommendInfo().isNew;

// 获取车型组列表数据
export const getVehGroupList = memoize(
  productGroups => {
    const vehGroupList = [];
    productGroups.forEach(item => {
      const count = lodashGet(item, 'productList.length');
      const price = CarABTesting.isListInPage()
        ? item.dailyPrice
        : item.filterDailyPrice;
      const hasResult = CarABTesting.isListInPage()
        ? item.hasResult
        : count > 0;
      vehGroupList.push({
        gId: item.groupCode,
        title: item.groupName,
        subTitle: hasResult
          ? `${toRMB('CNY')}${price || 0}${Texts.recommendPriceTitle}`
          : `${Texts.noProduct}`,
        isEasyLife: item.groupCode === ApiResCode.EasyLifeCode,
        isEasyLife2024: item.groupCode === ApiResCode.EasyLife2024GroupCode,
        count,
        hasResult,
      });
    });

    return vehGroupList;
  },
  (newArgs, oldArgs) => newArgs[0] === oldArgs[0],
);

// 获取所有车型个数
export const getAllVehicleCount = () => getBaseResData().allVehicleCount || 0;

// 获取所有报价个数
export const getAllVendorPriceCount = () =>
  getBaseResData().allVendorPriceCount || 0;

export const getFilterCalculateCache = memoize(
  (tempSelectedFiltersStr, productGroupsHashCode) => {
    const tempSelectedFilters = JSON.parse(tempSelectedFiltersStr);
    const filterCalculater =
      !CarABTesting.isListInPage() &&
      FilterCalculater(
        getBaseProductGroups(),
        getAllFilterMenuItems(),
        tempSelectedFilters,
        productGroupsHashCode,
      );

    if (Utils.isCtripOsd()) {
      const filterSource = '';
      const info: any = {
        vehicleCount: filterCalculater?.vehiclesCount,
        priceCount: filterCalculater?.pricesCount,
        filterSource,
        ifFlightChannel: AppContext.channelId === ChannelType.Flight ? 1 : 0, // 是否是机票渠道
      };
      info.selectedFilters = lodashMap(
        tempSelectedFilters?.filterLabels,
        'name',
      ).join(',');
      CarLog.LogTrace({
        key: LogKey.vac_car_trace_list_usersearch_result,
        info,
      });
    }

    return filterCalculater;
  },
);

export const getFilterCalculate = tempSelectedFilters => {
  return getFilterCalculateCache(
    JSON.stringify(tempSelectedFilters),
    getHashCode(),
  );
};

const getFilterItemsByHierarchy = (hierarchy: number) => {
  const targetList = [];
  getAllFilterMenuItems().forEach(item => {
    if (item.hierarchy === hierarchy) {
      const { filterGroups = [] } = item;
      // 20211202 解决sort排序不稳定的问题：排序字段值相等时，再通过groupCode从小到大排序
      const tempFilterGroups = filterGroups.sort((item1, item2) => {
        if (item1.sortNum === item2.sortNum) {
          return item1.groupCode > item2.groupCode ? 1 : -1;
        }
        return item1.sortNum - item2.sortNum;
      });
      targetList.push({
        ...item,
        filterGroups: tempFilterGroups,
      });
    }
  });
  return targetList;
};

// 获取热门筛选项列表
export const getPopularFilterItems = () => getFilterItemsByHierarchy(1);

// 获取点击'Filter'后对应的所有筛选项列表
export const getFilterItems = () => getFilterItemsByHierarchy(2);

export const getPromotionFilterItems = () => getFilterItemsByHierarchy(3);

// 获取排序列表
export const getSortList = () => {
  const { basicData } = getBaseResData();
  return (basicData && basicData.sortItems) || [];
};

// 是否为异地取还
export const isDiffLocation = () =>
  getRequestInfo().pickupLocationName !== getRequestInfo().returnLocationName;

// 遍历筛选code
const mapCode = (filterMenu, result) => {
  if (filterMenu.filterGroups && filterMenu.filterGroups.length > 0) {
    filterMenu.filterGroups.forEach(group => {
      if (group.filterItems && group.filterItems.length > 0) {
        group.filterItems.forEach(filter => {
          result.push(filter.itemCode);
        });
      }
    });
  }

  return result;
};

// 根据code筛选name
export const getNameByCode = code => {
  const filterMenu = getAllFilterMenuItems();
  let result = '';

  if (filterMenu && filterMenu.length > 0) {
    filterMenu.forEach(menu => {
      if (menu.filterGroups && menu.filterGroups.length > 0) {
        menu.filterGroups.forEach(group => {
          if (group.filterItems && group.filterItems.length > 0) {
            group.filterItems.forEach(filter => {
              if (filter.itemCode === code) result = filter.name;
            });
          }
        });
      }
    });
  }
  if (code === ApiResCode.ITEM_CODE.RentalStoreEnter && !result) {
    return '携程租车中心';
  }
  return result;
};

// 根据code筛选Icon
export const getIconByCode = code => {
  const filterMenu = getAllFilterMenuItems();
  let result = '';

  if (filterMenu && filterMenu.length > 0) {
    filterMenu.forEach(menu => {
      if (menu.filterGroups && menu.filterGroups.length > 0) {
        menu.filterGroups.forEach(group => {
          if (group.filterItems && group.filterItems.length > 0) {
            group.filterItems.forEach(filter => {
              if (filter.itemCode === code) result = filter.icon;
            });
          }
        });
      }
    });
  }

  return result;
};

// 获取filterbar上每个选项所含有的筛选项的全部code
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const getFilterBarItemsCodeByCache = memoize(filterMenuItems => {
  const popularFilterList = [];
  let VendorCodeList = [];
  let vendorFilter = null;

  getPopularFilterItems().forEach(item => {
    const type = item.code;
    const codeList = mapCode(item, []);
    const isVendor = item.code.indexOf('Vendor_') > -1;
    if (isVendor) {
      VendorCodeList =
        VendorCodeList.length > 0 ? VendorCodeList.concat(codeList) : codeList;
      vendorFilter = {
        type: 'Vendor',
        codeList: VendorCodeList,
      };
    } else {
      const filter = {
        type,
        codeList,
      };
      popularFilterList.push(filter);
    }
  });

  if (vendorFilter) {
    popularFilterList.push(vendorFilter);
  }

  let filterList = [];

  getFilterItems().forEach(item => {
    filterList = filterList.concat(mapCode(item, []));
  });

  getPromotionFilterItems().forEach(item => {
    filterList = filterList.concat(mapCode(item, []));
  });

  popularFilterList.push({
    type: 'Filters',
    codeList: filterList,
  });
  const eventResult = !popularFilterList?.find(
    item => item?.type !== 'Filters' && !item?.codeList?.length,
  );
  const data = eventResult
    ? {}
    : {
        popularFilterItems: getPopularFilterItems(),
        filterItems: getFilterItems(),
        promotionFilterItems: getPromotionFilterItems(),
      };
  if (!eventResult) {
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_trace_list_filterandsortmodal_data,
      info: {
        requestId: getRequestId(),
        eventResult,
        data,
      },
    });
  }
  return popularFilterList;
});

export const getFilterBarItemsCode = () => {
  const filterMenuItems = getBaseResData().filterMenuItems || [];
  return getFilterBarItemsCodeByCache(filterMenuItems);
};

export const getLicenseInfo = () => getBaseResData().licenseInfo;

export const getLicenseTip = () =>
  lodashGet(getBaseResData().licenseInfo, 'pickupLicenseDesc');

export const getLicenseTipNotice = () =>
  lodashGet(getBaseResData().licenseInfo, 'noticeMsg');

export const getTipGroups = () => getBaseResData().tipsGroups;

export const getPromptInfos = () => getBaseResData().promptInfos || [];

export const getPromotionTip = () => {
  let tip = '';
  const promptInfos = getPromptInfos();
  const promotionInfo = promptInfos.find(
    f => f.type === ApiResCode.ListPromptType.Promotion,
  );
  tip = lodashGet(promotionInfo, 'title');
  return tip;
};

export const getEasyLife2024PromptInfo = () =>
  getPromptInfos().find(f => f.type === ApiResCode.ListPromptType.EasyLife2024);

export const getSearchCarPromptInfo = () =>
  getPromptInfos().find(f => f.type === ApiResCode.ListPromptType.SearchCar);

export const getYXEnterInfo = () =>
  getPromptInfos().find(f => f.type === ApiResCode.ListPromptType.YXEnter);

export const getRentalStoreEnterInfo = () =>
  getPromptInfos().find(
    f => f.type === ApiResCode.ListPromptType.RentalStoreEnter,
  );

export const getSpecialDateEnterInfo = () =>
  getPromptInfos().find(
    f => f.type === ApiResCode.ListPromptType.SpecialDateEnter,
  );

export const getEncourageEnterInfo = () => {
  const encourageList = [];
  getPromptInfos().forEach(f => {
    if (f.type === ApiResCode.ListPromptType.EncourageEnter) {
      encourageList.push(f);
    }
  });
  return encourageList;
};

export const getTicketPrivilegesEnterInfo = () =>
  getPromptInfos().find(
    f => f.type === ApiResCode.ListPromptType.TicketPrivileges,
  );

export const getSuperMenberEnterInfo = () =>
  getPromptInfos().find(f => f.type === ApiResCode.ListPromptType.SuperMenber);

export const getQiangDanEnterInfo = () =>
  getPromptInfos().find(
    f => f.type === ApiResCode.ListPromptType.QiangDan_hasResult,
  );

export const getQiangDanNoEnterInfo = () =>
  getPromptInfos().find(
    f => f.type === ApiResCode.ListPromptType.QiangDan_NoResult,
  );

export const getEasyLifeEnter = () =>
  getPromptInfos().find(
    f => f.type === ApiResCode.ListPromptType.EasyLifeEnter,
  );

export const getRentCenterEnter = () =>
  getPromptInfos().find(
    f => f.type === ApiResCode.ListPromptType.RentCenterEnter,
  );

export const getMarketEnter = () =>
  getPromptInfos().find(
    f =>
      f.type === ApiResCode.ListPromptType.Market ||
      f.type === ApiResCode.ListPromptType.MarketBanner,
  );

export const getFirstPageSelfServiceBannerInfo = () =>
  getPromptInfos().find(
    f => f?.type === ApiResCode.ListPromptType.SelfServiceBanner,
  );

export const getYunnanEnter = () =>
  getPromptInfos().find(f => f.type === ApiResCode.ListPromptType.YunnanEnter);

export const getFeedBackEnter = () =>
  getPromptInfos().find(
    f => f.type === ApiResCode.ListPromptType.FeedBackEnter,
  );

export const getEasyLifeTagList = () =>
  lodashGet(getBaseResData(), 'easyLifeInfo.tagList') || Utils.EmptyArray;

export const getVendorEasyLifeLabelList = () =>
  getEasyLifeTagList().filter(f => f.type === 1);

export const getLimitRuleData = () =>
  ListReqAndResData.getData(ListReqAndResData.keyList.listLimitRuleRes);

// 获取同组车型或指定车型解释信息
export const getSimilarVehicleIntroduce = () =>
  getBaseResData().similarVehicleIntroduce || {};

export const getDesignatedVehicleIntroduce = () =>
  getBaseResData().designatedVehicleIntroduce;

// 获取租车中心数据
export const getRentCenter = () => getBaseResData().rentCenter;

export const getDropOffRentCenter = () => getBaseResData()?.rRentCenter;

export const getPHub = () => getBaseResData()?.pHub;

export const getRHub = () => getBaseResData()?.rHub;

export const getIsPickupStation = () => getLogStrValue(getPHub());

export const getIsDropOffStation = () => getLogStrValue(getRHub());

export const getIsHasRentCenter = () => getRentCenter() && getRentCenter().id;

// 获取国内心仪车型
export const getFavoriteInfo = () => getBaseResData().favoriteInfo;

export const getCommNotices = () => getBaseResData().commNotices || [];

export const getCoronavirusNoticeInfo = () =>
  getCommNotices().find(f => f.type === 3);

export const getTrunkInfo = () =>
  lodashGet(getBaseResData(), 'basicData.trunkInfo');

// 获取storeList门店数据
export const getStoreList = () => getBaseResData().storeList;

export const hasEasyLife = () =>
  getBaseProductGroups().findIndex(
    f => f.groupCode === ApiResCode.EasyLifeCode,
  ) > -1;

export const hasGroupCode = groupCode =>
  getBaseProductGroups().find(f => f.groupCode === groupCode);

export const getProductGroupInfo = (groupId, res) => {
  let productGroupInfo = null;
  const productGroups = lodashGet(res, 'productGroups');

  if (productGroups && productGroups.length > 0) {
    productGroupInfo = productGroups.find(f => f.groupCode === groupId);
  }

  return productGroupInfo;
};

export const getProductGroupIndex = groupId =>
  getBaseProductGroups().findIndex(f => f.groupCode === groupId);

export const getProductListByGroupId = groupId => {
  const curProductGroup = getAllProductGroups().find(
    f => f?.groupCode === groupId,
  );
  if (curProductGroup && curProductGroup.productList) {
    return curProductGroup.productList;
  }
  return [];
};

export const getIsLastPage = () => getBaseResData().isLastPage;

export const getIsNoTopProduct = () => getBaseResData().isNoTopProduct;

export const getIsFromCache = () => getBaseResponseMap()?.isFromCache;

export const getIsFromSearch = () => getBaseResData()?.isFromSearch;

export const getBaseResponse = () => getBaseResData()?.baseResponse || {};

export const getErrorInfo = () => {
  const errorObj = ListReqAndResData.getData(
    ListReqAndResData.keyList.listProductError,
  );
  if (errorObj && typeof errorObj === 'object') {
    return JSON.stringify(errorObj);
  }
  return '';
};

export const getUniqSign = () => getBaseResData()?.uniqSign;

// 获取新详情页所需的基础参数
export const getVendorListBasePageParam = (sign = '') => {
  const reqParam = ListReqAndResData.getData(
    ListReqAndResData.keyList.listProductReq,
  );
  return {
    tops: reqParam?.tops,
    filters: reqParam?.filters,
    sortType: reqParam?.sortType,
    uniqSign: sign || getUniqSign(),
  };
};

export const getBaseReqData = () =>
  ListReqAndResData.getData(ListReqAndResData.keyList.listProductReq);

export const getListReqUniqSign = () => getBaseReqData()?.uniqSign;

export const getListReqRequestId = () => getBaseReqData()?.requestId;

export const getCurProductGroupId = () =>
  getBaseResData()?.productGroupCodeUesd;

export const getFeeMap = () => getBaseResData()?.feeMap;

export const getSecretBoxInfo = () => getBaseResData()?.secretBoxBanners;

export const getSelfServiceSwitch = () =>
  getBaseResData()?.extras?.selfServiceSwitch;

// 是否存在自助取还资源
export const getHasSelfService = () =>
  !!getFilterNameByCode(ApiResCode.ITEM_CODE.SelfService);

export const getIsVehicle2 = () => getBaseResData()?.extras?.isVehicle2 === '1';

export const getIsEasyLife2024NoResult = (stateActiveGroupId?: string) => {
  const res = getBaseResData();
  const isEasyLife2024Group =
    stateActiveGroupId === ApiResCode.EasyLife2024GroupCode;
  return !!(
    isEasyLife2024Group &&
    !res?.productGroups?.find(item => item.groupCode === stateActiveGroupId)
      ?.hasResult
  );
};

export const getSearchSuggestBarData = () => {
  const allFilterMenuItems = getAllFilterMenuItems();
  return allFilterMenuItems?.find(
    item => item.code === FilterBarType.SearchSuggest,
  );
};

export const getSearchSuggestTitle = () => {
  return getSearchSuggestBarData()?.name;
};
