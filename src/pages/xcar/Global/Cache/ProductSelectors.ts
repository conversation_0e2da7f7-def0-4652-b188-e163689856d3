import { get as lodashGet } from 'lodash-es';
import ProductReqAndResData from './ProductReqAndResData';
import { ApiResCode } from '../../Constants/Index';
import Utils from '../../Util/Utils';
import {
  LocalContactInfoType,
  localContactsMap,
} from '../../Constants/LocalContactsData';
import { DepositPayType } from '../../ComponentBusiness/Common/src/Enums';

export const getProductReq = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.productReq) ||
  Utils.EmptyObj;

export const getProductRes = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.productRes) ||
  Utils.EmptyObj;

export const getProductRequestId = () =>
  lodashGet(getProductReq(), 'requestId');

export const getProductRequestReference = () =>
  lodashGet(getProductReq(), 'reference', Utils.EmptyObj);

export const getBaseResData = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.productRes) ||
  Utils.EmptyObj;

export const getProductResSuccess = () =>
  lodashGet(getBaseResData(), 'baseResponse.isSuccess');

export const getProductResAppResponseMap = () =>
  lodashGet(getBaseResData(), 'appResponseMap');

export const getProductMapWithHandleCache = () => {
  const curAppResponseMap = getProductResAppResponseMap();
  const curResponse = getBaseResData();
  if (curAppResponseMap) {
    // "vcCacheKey":"true"这个表示服务端命中了缓存
    curAppResponseMap.isFromCache = curResponse?.vcCacheKey === 'true';
    // callApiCost: 服务内部自己的耗时
    curAppResponseMap.callApiCost =
      +curResponse?.baseResponse?.extMap?.allCost || 0;
  }
  return curAppResponseMap;
};

export const getProductResError = () =>
  lodashGet(getBaseResData(), 'baseResponse.returnMsg');

export const getPriceResData = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.priceRes) || {};

export const getPriceReqData = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.priceReq) || {};

export const getPriceUuid = () => lodashGet(getPriceReqData(), 'uuid');

export const getPriceResNetwork = () =>
  lodashGet(getPriceResData(), 'appResponseMap.networkCost');

export const getVehicleInfo = () =>
  lodashGet(getBaseResData(), 'vehicleInfo', {});

export const getVendorInfo = () =>
  lodashGet(getBaseResData(), 'vendorInfo', {});

export const getIsSelected = () =>
  lodashGet(getBaseResData(), 'isSelected', {});

export const getQueryProductInfoStoreGuidInfos = () =>
  lodashGet(getBaseResData(), 'storeGuidInfos', []);

export const getPickupStoreInfo = () =>
  lodashGet(getBaseResData(), 'pickupStoreInfo', Utils.EmptyObj);

export const getReturnStoreInfo = () =>
  lodashGet(getBaseResData(), 'returnStoreInfo', {});

export const getTrunkInfo = () => lodashGet(getBaseResData(), 'trunkInfo', {});

export const getSesameInfo = () =>
  lodashGet(getPriceResData(), 'zhimaInfo', {});

export const getDepositInfo = () =>
  lodashGet(getPriceResData(), 'depositInfo', {});

export const getDepositPayInfos = () =>
  lodashGet(getPriceResData(), 'depositPayInfos', {});

export const getCreditRentDepositInfo = () =>
  lodashGet(getBaseResData(), 'depositInfo', {});

export const getHeaderCarImageUrl = () => {
  const { vehicleInfo = {} } = getBaseResData();
  const { imageUrl } = vehicleInfo || {};
  return imageUrl;
};

export const getPriceTimerRes = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.priceTimerRes) ||
  {};

export const getPriceAlertProps = () =>
  lodashGet(getPriceTimerRes(), 'alertProps', {});

export const getIsSoldOut = () => lodashGet(getBaseResData(), 'isSoldOut');

export const getPriceChange = () => lodashGet(getBaseResData(), 'priceChange');

export const getCreditVersion = () =>
  lodashGet(getPriceResData(), 'creditVersion');

export const getWholeDayPrice = () =>
  lodashGet(getBaseResData(), 'wholeDayPrice', {});

export const getUserRealImageLength = () => {
  const { vehicleInfo = {} } = getBaseResData();
  return lodashGet(vehicleInfo, 'userRealImageList.length') || 0;
};

export const getProductPageParam = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.productPageParam);

export const getProductPageParamVendor = () =>
  lodashGet(getProductPageParam(), 'vendor');

export const getBookResData = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.bookRes) || {};

export const getBookResNetwork = () =>
  lodashGet(getBookResData(), 'appResponseMap.networkCost');

export const getBookPriceTrackInfo = () =>
  lodashGet(getPriceResData(), 'trackInfo') || {};

export const getPriceResSuccess = () =>
  lodashGet(getPriceResData(), 'baseResponse.isSuccess');

export const getProductPriceChangeInfo = () =>
  lodashGet(getBaseResData(), 'priceChangeInfo');

export const getCreateOrderClickId = () =>
  ProductReqAndResData.getData(
    ProductReqAndResData.keyList.createOrderClickId,
  ) || '';
export const getPriceChangeInfoRes = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.priceChangeInfoRes);

export const getPriceChangeCode = () =>
  lodashGet(getPriceChangeInfoRes(), 'code') || '';

export const getPriceVersion = () =>
  lodashGet(getPriceResData(), 'orderPriceInfo.priceVersion');

export const getPricePromptInfos = () => getPriceResData()?.promptInfos;

export const getFreezeDepositContent = () =>
  getPricePromptInfos()?.find(f => f.type === ApiResCode.ListPromptType.Market);

export const getDepositRateDescriptionContent = () =>
  getPricePromptInfos()?.find(
    f => f.type === ApiResCode.ListPromptType.MarketBanner,
  );

export const getBookingConfirmData = () =>
  getPricePromptInfos()?.find(
    f => f.type === ApiResCode.ListPromptType.BookingConfirm,
  );

export const getPriceInsuranceTips = () => getPriceResData()?.insuranceTips;

export const getProductInsuranceTips = () =>
  getProductRes()?.vendorInsuranceDesc?.vendorInsuranceTips;

export const getInsuranceExclusionDesc = () =>
  getBaseResData()?.vendorInsuranceDesc?.exclusionDesc;

export const getDownGradeResData = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.downGradeRes) || {};

export const getDownGradeInfo = () => getDownGradeResData()?.downGradeInfo;

export const getNeedDownGrade = () => getProductRes()?.needDownGrade;

export const getReqVendorId = () => getProductReq()?.reference?.vendorCode;

export const getNewRentCenterName = () =>
  getProductRes()?.rentCenter?.isNew && getProductRes()?.rentCenter?.name;

export const getNewDropOffRentCenterName = () =>
  getProductRes()?.rRentCenter?.isNew && getProductRes()?.rRentCenter?.name;

export const getNewRentCenterId = () =>
  getProductRes()?.rentCenter?.isNew && getProductRes()?.rentCenter?.id;

export const getNewDropOffRentCenterId = () =>
  getProductRes()?.rRentCenter?.isNew && getProductRes()?.rRentCenter?.id;

// 是否展示还车方式，以服务端返回的字段为准, 0 正常展示 1: 合并展示
export const validateIsShowDropOff = () =>
  !getProductRes()?.pickupStoreInfo?.showType;

export const getPickUpWayInfo = () => getProductRes()?.pickupStoreInfo?.wayInfo;

export const getDropOffWayInfo = () =>
  getProductRes()?.returnStoreInfo?.wayInfo;

export const getReferenceTemp = () => getProductRes()?.referenceTemp;

export const getBookingFirstScreenParam = () => {
  return ProductReqAndResData.getData(
    ProductReqAndResData.keyList.bookingFirstScreenParam,
  );
};

export const getVehicleInfoV2 = () => {
  const firstScreenParam = getBookingFirstScreenParam();
  if (firstScreenParam?.vehicleInfo) {
    return firstScreenParam.vehicleInfo;
  }
  return getVehicleInfo();
};

export const getModifyOrderDesc = () => getPriceResData()?.modifyOrderDesc;

export const getImStatus = () => getProductRes()?.imStatus;

export const getSecretBoxRuleUrl = () =>
  getProductRes()?.extra?.secretBoxRuleUrl;

export const getEmptyGuideInfo = () => getProductRes()?.detailRecommendInfo;

export const getSelfServiceTipInfo = () => {
  const selfServiceTipInfo = getProductRes()?.promptInfos?.find(
    item => item?.type === ApiResCode.ListPromptType.SelfServiceTip,
  );
  return {
    title: selfServiceTipInfo?.title,
    subTitle: selfServiceTipInfo?.subTitle,
  };
};

export const getTplPrompt = () => getBaseResData()?.tplPrompt || '';

export const getOptionalContactMethods: () => LocalContactInfoType[] = () => {
  const { contactMethods } = getBaseResData()?.optionalContactMethods || {};
  return localContactsMap(contactMethods);
};
export const getCommodityDescs = () => {
  const commodityDesc = getBaseResData()?.commodityDescDTO || {};
  const { newModels, carAgeLabel, cancelRulesDesc, packageDesc } =
    commodityDesc || {};
  return [newModels, carAgeLabel, cancelRulesDesc, packageDesc].filter(Boolean);
};

export const getCommodityDescsGS3 = () => {
  const commodityDesc = getBaseResData()?.commodityDescDTO || {};
  const { vehicleBasicInfo, newModels, carAgeLabel, packageDesc } =
    commodityDesc || {};
  return [vehicleBasicInfo, newModels, carAgeLabel, packageDesc].filter(
    Boolean,
  );
};

export const getIsRP = () => !!getProductRequestReference()?.productId;

// 是否是境外信用租：判断是否有信用租的支付方式和是否选中
export const isOsdCreditRent = () => {
  const result = getPriceResData()?.depositPayInfos?.find(
    payInfo =>
      payInfo?.depositPayType === DepositPayType?.OSDCreditAuth &&
      payInfo?.isCheck,
  );
  return !!result;
};

export const getPriceNoFreeDepositTip = () =>
  getPriceResData()?.noFreeDepositTip;
