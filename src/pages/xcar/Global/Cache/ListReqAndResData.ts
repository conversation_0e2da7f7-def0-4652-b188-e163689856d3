import AppContext from '../../Util/AppContext';
import CarStorage from '../../Util/CarStorage';
import Utils from '../../Util/Utils';
import StorageKey from '../../Constants/StorageKey';

/** 列表页接口的请求+响应数据 */
let reqAndResData = {
  listProductReq: null,
  listProductRes: null,
  listProductRes200: null, // 二批数据
  listProductRes201: null, // 一批数据
  filterBrand: '', // url跳转链接的品牌筛选数据
  filterVendor: '', // url跳转链接的供应商筛选数据
  filterGroupId: '', // url跳转链接的车型组筛选数据 用于车型置顶
  routeTopParam: null, // url跳转链接的置顶参数
  listProductError: null, // 接口请求失败信息
  listNeedRecommend: null, // 是否需要请求推荐接口
};

/**
 * 列表页状态数据
 */
export const listStatusKeyList = {
  drivelicencePopList: 'drivelicencePopList',
  listForwardNeedInfo: 'listForwardNeedInfo',
  coronavirusTipIsClose: 'coronavirusTipIsClose',
  warningTipIsClose: 'warningTipIsClose',
  licenseTipIsClose: 'licenseTipIsClose',
  filterBarIsShow: 'filterBarIsShow',
  quickFilterBarIsShow: 'quickFilterBarIsShow',
  hasInitListData: 'hasInitListData',
  ticketPrivilegeTipIsClose: 'ticketPrivilegeTipIsClose',
  superMemberTipIsClose: 'superMemberTipIsClose',
  limitTipIsClose: 'limitTipIsClose',
  marketBannerIsClose: 'marketBannerIsClose',
  tipHeight: 'tipHeight',
  loginFromProduct: 'loginFromProduct',
  priceChangeFromBook: 'priceChangeFromBook',
  filterBrand: 'filterBrand',
  filterVendor: 'filterVendor',
  filterGroupId: 'filterGroupId',
  tipPopVisible: 'tipPopVisible',
  routeTopParam: 'routeTopParam',
  downGradeFromProduct: 'downGradeFromProduct',
  timeOutFromVendorListPage: 'timeOutFromVendorListPage',
  refreshFromVendorListPage: 'refreshFromVendorListPage',
  refreshFromOSDProductPage: 'refreshFromOSDProductPage',
  cacheExpireFromVendorListPage: 'cacheExpireFromVendorListPage',
  couponBookFromVendorListPage: 'couponBookFromVendorListPage',
  updateLocationAndDateByRecommend: 'updateLocationAndDateByRecommend', // 推荐车型点击更新时间地点信息
};
let listStatusData = {};

const registerPageData = {};

export default class ListReqAndResData {
  static keyList = {
    listProductReq: 'listProductReq',
    listProductRes: 'listProductRes',
    listProductRes200: 'listProductRes200',
    listProductRes201: 'listProductRes201',
    listProductSearchCondition: 'listProductSearchCondition',
    listLimitRuleRes: 'listLimitRuleRes',
    listIMUrl: 'listIMUrl',
    listProductError: 'listProductError',
    listNeedRecommend: 'listNeedRecommend',
  };

  static getData = (key: string) => reqAndResData[key];

  static setData = (key, data) => {
    reqAndResData[key] = data;
    if (key === ListReqAndResData.keyList.listProductRes) {
      updateServerRequestId();
    }
  };

  static removeData = () => {
    reqAndResData = {
      listProductReq: null,
      listProductRes: null,
      listProductRes200: null,
      listProductRes201: null,
      filterBrand: '',
      filterVendor: '',
      filterGroupId: '',
      routeTopParam: null,
      listProductError: null,
      listNeedRecommend: null,
    };
    updateServerRequestId();
  };
}

export const setListStatusData = (key, data) => {
  listStatusData[key] = data;
  CarStorage.save(
    StorageKey.CAR_MULTI_PAGE_REFRESH_ID,
    JSON.stringify(listStatusData),
  );
};

export const getListStatusData = (key: string) => listStatusData[key];

// fix: 兼容OpenURL打开VendorList时，后续页面触发列表页刷新的场景
// 回退到列表页时，同步获取CarStorage
// http://conf.ctripcorp.com/pages/viewpage.action?pageId=1369256764
export const getListStatusDataByStorage = () => {
  const data = CarStorage.loadSync(StorageKey.CAR_MULTI_PAGE_REFRESH_ID);
  try {
    const cache = JSON.parse(data);
    listStatusData = { ...listStatusData, ...cache };
  } catch {
    //
  }
  CarStorage.remove(StorageKey.CAR_MULTI_PAGE_REFRESH_ID);
};

export const removeListStatusData = () => {
  listStatusData = {};
  CarStorage.remove(StorageKey.CAR_MULTI_PAGE_REFRESH_ID);
};

export const setRegisterPageData = (key, data) => {
  registerPageData[key] = data;
};

export const getRegisterPageData = key => registerPageData[key];

// 用于列表页无结果曝光埋点
const listNoResultExposure = {
  fromPage: '',
};
export const setFromPage = data => {
  listNoResultExposure.fromPage = data;
};

export const getFromPage = () => listNoResultExposure.fromPage;

export const getBaseResData = () =>
  ListReqAndResData.getData(ListReqAndResData.keyList.listProductRes) || {};

// Map<String, String> ,  key : abVersion , 存放服务端AB分流结果
export const getListResExtras = () => getBaseResData()?.extras;

export const getListResExtraIndexTags = () =>
  getBaseResData()?.baseResponse?.extraIndexTags;

export const getServerRequestId = () => {
  if (Utils.isCtripOsd()) {
    return getBaseResData()?.baseResponse?.requestId;
  }
  return getListResExtras()?.serverRequestId;
};

export const getNewMergeId = () => {
  return getListResExtras()?.serverRequestId;
};

export const getListRequestId = () => {
  return getBaseResData()?.baseResponse?.requestId;
};

export const getSecretBoxRuleUrl = () => {
  return getBaseResData()?.baseResponse?.extMap?.secretBoxRuleUrl;
};

const updateServerRequestId = () => {
  const serverRequestId = getServerRequestId();
  AppContext.setServerRequestId(serverRequestId);
};
