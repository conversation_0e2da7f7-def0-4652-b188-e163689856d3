const cssObjMap = {
  layout: {
    alignHorizontal: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    betweenHorizontal: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    startHorizontal: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
    },
    betweenStart: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'space-between',
    },
    betweenEnd: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'flex-end',
      justifyContent: 'space-between',
    },
    rowStart: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    rowEnd: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    verticalItem: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    },
    flexRow: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
    },
    flexRowWrap: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    flexRowWrapCenterEnd: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      flexWrap: 'wrap',
    },
    flexCenter: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    flex1: {
      flex: 1,
    },
    justifyCenter: {
      justifyContent: 'center',
    },
    justifyStart: {
      justifyContent: 'flex-start',
    },
    textCenter: {
      textAlign: 'center',
    },
    textRight: {
      textAlign: 'right',
    },
    alignItemsStart: {
      alignItems: 'flex-start',
    },
  },
  font: {
    fontTripNumberMedium: 'TripNumber-Medium',
    fontTripNumberTFSemiBold: 'TripNumberTF-SemiBold',
    fontTripNumberSemiBold: 'TripNumber-SemiBold',
    fontCnIosBold: 'PingFangSC-Semibold',
  },
  dimensions: {
    width: '100%',
    height: '100%'
  },
  StyleSheet: {
    hairlineWidth: '1px',
    absoluteFillObject: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    absoluteFill: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    }
  },
  radius: {
    radiusXS: '4px',
    radiusS: '8px',
    radiusM: '16px'
  },
  space: {
    spaceXS: '4px',
    spaceS: '8px',
    spaceM: '12px',
    spaceL: '16px',
    spaceXL: '24px',
    spaceXXL: '32px',
    spaceXXL2: '64px',
    verticalXS: '4px',
    verticalS: '8px',
    verticalM: '12px',
    verticalL: '16px',
    verticalXL: '20px',
    verticalXXL: '24px'
  },
  zIndex: {
    one: 1,
    two: 2,
    three: 3,
    four: 4,
    five: 5,
    six: 6,
    seven: 7,
    eight: 8,
    nine: 9,
    modalDefault: 1000,
  },
  label: {
    baseLabelLHeight: '32px',
    baseLabelSHeight: '28px'
  },
  button: {
    buttonLStyle: {
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: '12px',
      paddingLeft: '40px',
      paddingRight: '40px',
      paddingTop: '24px',
      paddingBottom: '24px',
      minHeight: '88px',
      minWidth: '180px',
    },
    buttonSprayStyle: {
      borderTopLeftRadius: '12px',
      borderTopRightRadius: '12px',
      borderBottomLeftRadius: '12px',
      borderBottomRightRadius: '36px',
    }
  },
  border: {
    borderSizeXsm: '1px',
    borderSizeSm: '2px',
    borderSizeLg: '4px',
    borderSizeXl: '6px',
  }
};
const styleKeyMap = {
  paddingVertical: (value) => {
    return {
      paddingTop: value,
      paddingBottom: value,
    }
  },
  paddingHorizontal: (value) => {
    return {
      paddingLeft: value,
      paddingRight: value,
    }
  },
  marginVertical: (value) => {
    return {
      marginTop: value,
      marginBottom: value,
    }
  },
  marginHorizontal: (value) => {
    return {
      marginLeft: value,
      marginRight: value,
    }
  },
}

module.exports = {
  cssObjMap,
  styleKeyMap,
};
