const fs = require('fs');
const path = require('path');
const babel = require('@babel/core');
const readline = require('readline');
const { recursion } = require('./utils');

// yarn removeAB GetAB.isISDInterestPoints true
const dirRootUrl = 'src/';
// const dirRootUrl = 'src/pages/xcar/Pages/VendorList/Components';

const dirsRoot = path.resolve(dirRootUrl);
// 本地console.log调试开关
const isDebug = true;
// 获取除了 Node 路径和脚本路径之外的参数
const args = process.argv.slice(2);
let abExpression = args[0];
let abValue = args[1] === 'true';
let logFile = '';
const logFilePath = './scripts/removeABLog.txt';

function debugLog(message) {
  logFile += `${message} \n \n`;
  if (isDebug) {
    console.log(message);
  }
}

// 还原bool值常量赋值表达式变量替换后的ABFunction的成员表达式
function getABReplaceExpression(abExpression, abValue, boolExpression) {
  if ((!!abValue && !!boolExpression) || (!abValue && !boolExpression)) {
    return `${abExpression}()`;
  }
  return `!${abExpression}()`;
}

function getNodeStartEnd(node) {
  return {
    start: node?.start || node?.loc?.start?.index,
    end: node?.end || node?.loc?.end?.index,
  };
}

// 移除AB实验代码For对象表达式，返回替换操作对象
function removeABForObjectPropertyExpression(
  content,
  nodePath,
  isUnary,
  abExpression,
  abValue,
) {
  const expressBool = isUnary ? !abValue : !!abValue;
  const node = nodePath.node;
  const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
  const before = content.substring(nodeStart, nodeEnd);
  let start = nodeStart;
  let after = before.replace(abExpression, expressBool);
  return [
    {
      before,
      after,
      start,
      type: node.type,
    },
  ];
}

// 移除AB实验代码Forif表达式，返回替换操作对象
function removeABForIfStatementExpression(
  content,
  nodePath,
  isUnary,
  abExpression,
  abValue,
) {
  const expressBool = isUnary ? !abValue : !!abValue;
  const node = nodePath.node;
  const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
  const before = content.substring(nodeStart, nodeEnd);
  let start = nodeStart;
  let after = '';
  let stayNode = null;
  if (expressBool) {
    stayNode = node.consequent;
  } else {
    stayNode = node.alternate;
  }
  if (!!stayNode) {
    // if表达式要读取body的第一个节点的起点和最后一个节点的结束
    const { start: stayNodeStart} = getNodeStartEnd(
      stayNode?.body?.[0] || stayNode,
    );
    const { end: stayNodeEnd } = getNodeStartEnd(
      stayNode?.body?.[stayNode?.body?.length - 1] || stayNode,
    );
    after = content.substring(stayNodeStart, stayNodeEnd);
  } else {
    after = '';
  }
  return [
    {
      before,
      after,
      start,
      type: node.type,
    },
  ];
}

// 移除AB实验代码For赋值表达式，返回替换操作对象，同时返回需要替换的变量
function removeABForVariableDeclaratorExpression(
  content,
  nodePath,
  isUnary,
  abExpression,
  abValue,
) {
  const result = [];
  const node = nodePath.node;
  const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
  const parentNode = nodePath.parentPath.node;
  const moduleNode = nodePath.parentPath.parentPath.node;
  const { start: parentNodeStart, end: parentNodeEnd } =
    getNodeStartEnd(parentNode);
  const { start: moduleNodeStart, end: moduleNodeEnd } =
    getNodeStartEnd(moduleNode);
  const moduleText = content.substring(moduleNodeStart, moduleNodeEnd);
  const declarator = content.substring(parentNodeStart, parentNodeEnd);
  const start = parentNodeStart;

  result.push({
    before: declarator,
    after: '',
    start,
    type: node.type,
  });
  result.push({
    before: node.id.name,
    after: isUnary ? !abValue : abValue,
    start: nodeStart,
    isReplace: true,
    type: node.type,
    moduleText,
    moduleNodeStart,
    moduleNodeEnd,
  });

  return result;
}

// 移除AB实验代码For条件表达式，返回替换操作对象
function removeABForConditionalExpression(
  content,
  nodePath,
  isUnary,
  abExpression,
  abValue,
) {
  const expressBool = isUnary ? !abValue : !!abValue;
  const node = nodePath.node;
  const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
  const { start: nodeConsequentStart, end: nodeConsequentEnd } =
    getNodeStartEnd(node.consequent);
  const { start: nodeAlternateStart, end: nodeAlternateEnd } = getNodeStartEnd(
    node.alternate,
  );
  const before = content.substring(nodeStart, nodeEnd);
  if (isConditionalNeedRemove(content, node, abExpression)) {
    let start = 0;
    const consequentExpression = content.substring(
      nodeConsequentStart,
      nodeConsequentEnd,
    );
    const alternateExpression = content.substring(
      nodeAlternateStart,
      nodeAlternateEnd,
    );
    let after = '';
    if (expressBool) {
      after = consequentExpression;
      start = nodeConsequentStart;
    } else {
      after = alternateExpression;
      start = nodeAlternateStart;
    }
    return [
      {
        before,
        after,
        start,
        type: node.type,
      },
    ];
  }
  return [];
}

// 移除AB实验代码For调用表达式，返回替换操作对象
function removeABForCallExpression(
  content,
  nodePath,
  abExpression,
) {
  const result = [];
  const node = nodePath.node;
  const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
  let before = content.substring(nodeStart, nodeEnd);
  let after = before.replace(`!!${abExpression}`, abValue);
  after = after.replace(`!${abExpression}`, !abValue);
  result.push({
    before,
    after,
    start: nodeStart,
    type: node.type,
  });
  return result;
}

// 移除AB实验代码For逻辑表达式，返回替换操作对象
function removeABForLogicalExpression(
  content,
  nodePath,
  isUnary,
  abExpression,
  abValue,
) {
  const expressBool = isUnary ? !abValue : !!abValue;
  const result = [];
  const node = nodePath.node;
  const parentNode = nodePath.parentPath.node;
  const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
  const { start: parentNodeStart, end: parentNodeEnd } =
    getNodeStartEnd(parentNode);
  let before = content.substring(nodeStart, nodeEnd);
  const parentText = content.substring(parentNodeStart, parentNodeEnd);
  let start = 0;
  let after = '';
  let { start: nodeLeftStart, end: nodeLeftEnd } = getNodeStartEnd(node.left);
  const { start: nodeRightStart, end: nodeRightEnd } = getNodeStartEnd(
    node.right,
  );
  if (!nodeLeftStart) {
    nodeLeftStart = nodeStart;
    nodeLeftEnd = nodeRightStart - 3;
  }
  const leftExpression = content.substring(nodeLeftStart, nodeLeftEnd);

  const isABLeft = leftExpression.indexOf(abExpression) > -1;
  const stayNode = isABLeft ? node.right : node.left;
  let { start: nodeStayStart, end: nodeStayEnd } = getNodeStartEnd(stayNode);
  if (!nodeStayStart) {
    if (isABLeft) {
      nodeStayStart = nodeLeftEnd + 4;
      nodeStayEnd = nodeEnd;
    } else {
      nodeStayStart = nodeStart;
      nodeStayEnd = nodeRightStart - 3;
    }
  }
  start = nodeStayStart;
  const stayExpression = content.substring(nodeStayStart, nodeStayEnd);
  if (node.operator === '&&' && !expressBool) {
    after = false;
  } else if (node.operator === '||' && expressBool) {
    after = true;
  } else {
    after = stayExpression;
  }
  result.push({
    before,
    after,
    start,
    type: node.type,
  });
  return result;
}

// 移除AB实验代码For取非表达式，返回替换操作对象
function removeABForUnaryExpression(
  content,
  nodePath,
  isUnary,
  abExpression,
  abValue,
) {
  const node = nodePath.node;
  let result = [];
  switch (node.type) {
    case 'IfStatement':
      result = removeABForIfStatementExpression(
        content,
        nodePath,
        isUnary,
        abExpression,
        abValue,
      );
      break;
    case 'ConditionalExpression':
      result = removeABForConditionalExpression(
        content,
        nodePath,
        isUnary,
        abExpression,
        abValue,
      );
      break;
    case 'LogicalExpression':
      result = removeABForLogicalExpression(
        content,
        nodePath,
        isUnary,
        abExpression,
        abValue,
      );
      break;
    case 'VariableDeclarator':
      result = removeABForVariableDeclaratorExpression(
        content,
        nodePath,
        isUnary,
        abExpression,
        abValue,
      );
      break;
    case 'ObjectProperty':
      result = removeABForObjectPropertyExpression(
        content,
        nodePath,
        isUnary,
        `!${abExpression}`,
        abValue,
      );
      break;
    case 'UnaryExpression':
      result = removeABForUnaryExpression(
        content,
        nodePath.parentPath,
        isUnary,
        abExpression,
        !abValue,
      );
      break;
    case 'CallExpression':
      result = removeABForCallExpression(
        content,
        nodePath,
        abExpression,
      );
      break;
    default:
      break;
  }
  return result;
}

// 移除成员表达式 .getABFunction()
function removeABMemberExpression(path, abExpression, abValue) {
  // 读取文件
  let content = fs.readFileSync(path).toString();
  let replaceExpression = [];
  let replaceBoolVarList = [];
  babel.transform(content, {
    plugins: [
      {
        visitor: {
          MemberExpression(path) {
            const node = path.node;
            const parentPath = path.parentPath.parentPath;
            const parentNode = parentPath.node;
            const unaryParentPath = path.parentPath.parentPath.parentPath;
            const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
            const { start: parentNodeStart, end: parentNodeEnd } =
              getNodeStartEnd(parentNode);

            const textContext = content.substring(nodeStart, nodeEnd);
            const parentTextContext = content.substring(
              parentNodeStart,
              parentNodeEnd,
            );
            if (textContext === abExpression) {
              switch (parentNode.type) {
                case 'CallExpression':
                case 'ObjectProperty':
                  replaceExpression.push(
                    ...removeABForObjectPropertyExpression(
                      content,
                      parentPath,
                      false,
                      `${abExpression}()`,
                      abValue,
                    ),
                  );
                  break;
                case 'IfStatement':
                  replaceExpression.push(
                    ...removeABForIfStatementExpression(
                      content,
                      parentPath,
                      false,
                      `${abExpression}()`,
                      abValue,
                    ),
                  );
                  break;
                case 'VariableDeclarator':
                  replaceExpression.push(
                    ...removeABForVariableDeclaratorExpression(
                      content,
                      parentPath,
                      false,
                      `${abExpression}()`,
                      abValue,
                    ),
                  );
                  break;
                case 'ConditionalExpression':
                  replaceExpression.push(
                    ...removeABForConditionalExpression(
                      content,
                      parentPath,
                      false,
                      `${abExpression}()`,
                      abValue,
                    ),
                  );
                  break;
                case 'LogicalExpression':
                  replaceExpression.push(
                    ...removeABForLogicalExpression(
                      content,
                      parentPath,
                      false,
                      `${abExpression}()`,
                      abValue,
                    ),
                  );
                  break;
                // 取非表达式
                case 'UnaryExpression':
                  replaceExpression.push(
                    ...removeABForUnaryExpression(
                      content,
                      unaryParentPath,
                      true,
                      `${abExpression}()`,
                      abValue,
                    ),
                  );
                  break;
                case 'ExpressionStatement':
                  replaceExpression.push({
                    before: parentTextContext,
                    after: '',
                    start: parentNodeStart,
                    type: parentNode.type,
                  });
                  break;
                default:
                  break;
              }
            }
          },
        },
      },
    ],
    filename: path,
  });

  if (replaceExpression.length > 0) {
    // 排序替换
    replaceExpression = replaceExpression.sort((a, b) => a.start - b.start);
    let replaceIndex = 0;
    let isSuccess = true;
    debugLog(`步骤1: 开始替换AB实验获取的函数调用表达式${abExpression}()`);
    // 替换实验下线代码
    replaceExpression.map((item, index) => {
      const { before, after, start, isReplace, type } = item || {};
      if (start > replaceIndex) {
        if (isReplace) {
          replaceBoolVarList.push(item);
        } else {
          replaceIndex = start;
          beforeContent = content;
          beforereplace = before;
          if (content.indexOf(before) > -1) {
            content = content.replace(before, after);
            debugLog(
              `${
                index + 1
              }、成功 ${type}表达式，start： ${start}  before： ${before}  after： ${after}\n`,
            );
          } else {
            debugLog(
              `${
                index + 1
              }、失败 ${type}表达式！！！，start： ${start}  before： ${before}  after： ${after}\n`,
            );
          }
        }
      }
    });

    if (isSuccess) {
      // 保存下线AB实验后的文件
      fs.writeFileSync(path, content);
      return replaceBoolVarList;
    } else {
      return [];
    }
  }
  return [];
}

// 替换布尔常量表达式const a = true、 const b = false, 替换为GetAB.isISDInterestPoints()函数
function removeBooleanConstantExpression(path, abExpression, abValue) {
  // 读取文件
  let content = fs.readFileSync(path).toString();
  let replaceExpression = [];
  babel.transform(content, {
    plugins: [
      {
        visitor: {
          BooleanLiteral(path) {
            const node = path.node;
            const parentPath = path.parentPath;
            const parentNode = parentPath.node;
            const parentParentNode = parentPath.parentPath.node;
            const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
            const { start: parentNodeStart, end: parentNodeEnd } =
              getNodeStartEnd(parentNode);
            const { start: parentParentNodeStart, end: parentParentNodeEnd } =
              getNodeStartEnd(parentParentNode);
            const parentText = content.substring(
              parentNodeStart,
              parentNodeEnd,
            );
            const parentParentText = content.substring(
              parentParentNodeStart,
              parentParentNodeEnd,
            );
            const textContext = content.substring(nodeStart, nodeEnd);
            if (textContext === 'false' || textContext === 'true') {
              switch (parentNode.type) {
                case 'VariableDeclarator':
                  if (!isBooleanConstantVarExpression(parentParentText)) {
                    break;
                  }
                case 'ConditionalExpression':
                  if (parentNode.type === 'ConditionalExpression') {
                    const isNeedRemove =
                      isConditionalNeedRemove(content, parentNode, 'false') ||
                      isConditionalNeedRemove(content, parentNode, 'true');

                    if (!isNeedRemove) {
                      break;
                    }
                  }
                case 'LogicalExpression':
                case 'IfStatement':
                  if (parentNode.start > 0) {
                    const logicReplace = getABReplaceExpression(
                      abExpression,
                      abValue,
                      textContext === 'true',
                    );
                    replaceExpression.push({
                      start: parentNode.start,
                      before: parentText,
                      after: parentText.replace(textContext, logicReplace),
                      type: parentNode.type,
                    });
                  }
                  break;
                case 'JSXExpressionContainer':
                  if (
                    ['JSXFragment', 'JSXElement'].includes(
                      parentParentNode.type,
                    ) &&
                    isBooleanConstantJsxExpression(parentText)
                  ) {
                    const boolExpressionLeft = content.substring(
                      parentParentNodeStart,
                      parentNodeStart,
                    );
                    const boolExpressionRight = content.substring(
                      parentNodeEnd,
                      parentParentNodeEnd,
                    );
                    replaceExpression.push({
                      start: parentParentNodeStart,
                      before: parentParentText,
                      after: boolExpressionLeft + boolExpressionRight,
                      type: parentNode.type,
                    });
                  }
                  break;
                default:
                  break;
              }
            }
          },
        },
      },
    ],
    filename: path,
  });

  if (replaceExpression.length > 0) {
    debugLog(
      `步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式${abExpression}(), 替换数量=${replaceExpression.length}`,
    );
    let replaceIndex = 0;
    // 排序
    replaceExpression = replaceExpression.sort((a, b) => a.start - b.start);
    // 替换实验下线代码
    replaceExpression.map((item, index) => {
      const { before, after, start, type } = item || {};
      if (start > replaceIndex) {
        replaceIndex = start;
        if (content.indexOf(before) > -1) {
          content = content.replace(before, after);
          debugLog(
            `${
              index + 1
            }、成功 ${type}表达式，start： ${start}  before： ${before}  after： ${after}\n`,
          );
        } else {
          debugLog(
            `${
              index + 1
            }、失败 ${type}表达式！！！，start： ${start}  before： ${before}  after： ${after}\n`,
          );
        }
      }
    });

    fs.writeFileSync(path, content);
    return true;
  } else {
    return false;
  }
}

function getABBoolVarExpression(expressionList, name, parentText) {
  return expressionList?.find(
    item => item.before === name && item.moduleText.indexOf(parentText) > -1,
  );
}

function removeABBoolVarExpression(
  path,
  abExpression,
  abValue,
  expressionList,
) {
  if (expressionList?.length > 0) {
    debugLog('步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失');
    // 读取文件
    let content = fs.readFileSync(path).toString();
    let replaceExpression = [];
    babel.transform(content, {
      plugins: [
        {
          visitor: {
            Identifier(path) {
              const node = path.node;
              const parentPath = path.parentPath;
              const parentNode = parentPath.node;
              const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
              const { start: parentNodeStart, end: parentNodeEnd } =
                getNodeStartEnd(parentNode);
              const nodeText = content.substring(nodeStart, nodeEnd);
              const parentText = content.substring(
                parentNodeStart,
                parentNodeEnd,
              );
              const idenName = node.name;
              const varExpression = getABBoolVarExpression(
                expressionList,
                idenName,
                parentText,
              );
              if (!!varExpression) {
                switch (parentNode.type) {
                  case 'ObjectProperty':
                    const { start: parentKeyNodeStart, end: parentKeyNodeEnd } =
                      getNodeStartEnd(parentNode);
                    // 如果是Object的key，则不进行替换
                    if (parentKeyNodeStart === nodeStart) {
                      break;
                    }
                  case 'IfStatement':
                  case 'ConditionalExpression':
                  case 'CallExpression':
                  case 'LogicalExpression':
                    const logicReplace = getABReplaceExpression(
                      abExpression,
                      abValue,
                      varExpression.after,
                    );
                    const replaceBefore = parentText.substring(
                      0,
                      nodeStart - parentNodeStart,
                    );
                    const replaceAfter = parentText.substring(
                      nodeEnd - parentNodeStart,
                      parentNodeEnd,
                    );
                    replaceExpression.push({
                      before: parentText,
                      after: `${replaceBefore}${logicReplace}${replaceAfter}`,
                      start: parentNodeStart,
                    });
                    break;
                  case 'UnaryExpression':
                    const unaryReplace = getABReplaceExpression(
                      abExpression,
                      !abValue,
                      varExpression.after,
                    );
                    replaceExpression.push({
                      before: parentText,
                      after: parentText.replace(`!${idenName}`, unaryReplace),
                      start: parentNode.start,
                    });
                    break;
                  default:
                    break;
                }
              }
            },
          },
        },
      ],
      filename: path,
    });

    // 排序
    replaceExpression = replaceExpression.sort((a, b) => a.start - b.start);
    let replaceIndex = 0;
    replaceExpression.map((item, index) => {
      const { before, after, start } = item || {};
      if (start > replaceIndex) {
        replaceIndex = start;

        if (content.indexOf(before) > -1) {
          content = content.replace(before, after);
          debugLog(
            `${
              index + 1
            }、成功，start： ${start}  before： ${before}  after： ${after}\n`,
          );
        } else {
          debugLog(
            `${
              index + 1
            }、失败！！！，start： ${start}  before： ${before}  after： ${after}\n`,
          );
        }
      }
    });
    // 保存下线AB实验后的文件
    fs.writeFileSync(path, content);
  }
}

function isBooleanConstantVarExpression(content) {
  const declareRegex = /const .* = (false|true);/;
  return declareRegex.test(content);
}

function isBooleanConstantJsxExpression(content) {
  const jsxRegex = /{(false|true)}/;
  return jsxRegex.test(content);
}

function isConditionalNeedRemove(content, node, abExpression) {
  const exporessText = content.substring(node.test.start, node.test.end);
  const index = exporessText.indexOf(abExpression);
  return index > -1;
}

// 第一次调用，判断是否包含AB实验的成员函数，递归调用需要判断是否包含布尔常量赋值表达式或者逻辑表达式
function isNeedRemove(path, abExpression) {
  // 读取文件
  let content = fs.readFileSync(path).toString();
  return content.indexOf(`${abExpression}()`) > -1;
}

function removeAB(path, times = 1) {
  if (!/\.ts(x)?$/.test(path) || /\/dist/.test(path)) {
    return;
  }

  // 0、判断文件是否存在AB实验代码
  if (isNeedRemove(path, abExpression, times)) {
    debugLog(`下线AB实验，文件路径Path=${path}, 第${times}次调用`);

    // 1、下线AB实验相关的成员表达式, 即代码中去除所有GetAB.isISDInterestPoints()函数调用
    const replaceBoolVarList = removeABMemberExpression(
      path,
      abExpression,
      abValue,
    );

    // 2、下线步骤2引起的变量缺失, 进行逻辑表达式的替换。不满足以下条件，替换失败
    removeABBoolVarExpression(path, abExpression, abValue, replaceBoolVarList);

    // 3、反向替换booleal常量赋值表达式为GetAB.isISDInterestPoints()函数
    const isHasBooleanExpress = removeBooleanConstantExpression(
      path,
      abExpression,
      abValue,
    );

    // 5、递归替换AB实验表达式
    if (isHasBooleanExpress || isNeedRemove(path, abExpression)) {
      removeAB(path, times + 1);
    }
  }
}

function startRemove() {
  debugLog(
    `\n开始下线AB实验相关代码，${abExpression}函数相关，取值为${abValue}`,
  );

  recursion(dirsRoot, removeAB);

  debugLog(
    `\n下线AB实验相关代码，${abExpression}函数相关，取值为${abValue}，全部执行完成`,
  );

  fs.writeFileSync(logFilePath, logFile, err => {
    debugLog('\n创建日志文件失败' + err);
  });

  debugLog(`\n日志文件路径：${path.resolve(logFilePath)}`);
}

function main() {
  if (!abExpression) {
    debugLog(`请输入AB实验相关的函数表达式，例如 GetAB.isISDInterestPoints \n`);
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    rl.question('请输入->', answer => {
      abExpression = answer;
      debugLog(`你输入的函数表达式是：${abExpression} \n`);
      debugLog(`请输入AB实验相关的函数表达式的值，B 代表true 其它为false \n`);
      rl.question('请输入->', answerValue => {
        abValue = answerValue === 'B' || answerValue === 'b';
        debugLog(`你输入的函数表达式的值是：${abValue} \n`);
        rl.close();
        startRemove();
      });
    });
    return;
  }
  startRemove();
}

main();
