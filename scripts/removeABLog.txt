请输入AB实验相关的函数表达式，例如 GetAB.isISDInterestPoints 
 
 
你输入的函数表达式是：GetABCache.isISDShelves 
 
 
请输入AB实验相关的函数表达式的值，B 代表true 其它为false 
 
 
你输入的函数表达式的值是：true 
 
 

开始下线AB实验相关代码，GetABCache.isISDShelves函数相关，取值为true 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/CarPriceDescribe/src/PriceDescItem.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 6006  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 7405  before： isISDShelves ? style.totalHelpIcon : priceDescIconStyle  after： GetABCache.isISDShelves() ? style.totalHelpIcon : priceDescIconStyle
 
 
9、成功，start： 7770  before： isISDShelves ? style.totalPriceNew : style.totalPrice  after： GetABCache.isISDShelves() ? style.totalPriceNew : style.totalPrice
 
 
16、成功，start： 8138  before： isISDShelves && style.totalPriceWrap  after： GetABCache.isISDShelves() && style.totalPriceWrap
 
 
22、成功，start： 8423  before： isISDShelves ? style.totalPriceWrapStyle : totalPriceWrapStyle  after： GetABCache.isISDShelves() ? style.totalPriceWrapStyle : totalPriceWrapStyle
 
 
29、成功，start： 8554  before： isISDShelves ? style.totalPriceNew : style.totalPrice  after： GetABCache.isISDShelves() ? style.totalPriceNew : style.totalPrice
 
 
36、成功，start： 8687  before： isISDShelves
                    ? style.totalPriceStyleNew
                    : style.totalPriceStyle  after： GetABCache.isISDShelves()
                    ? style.totalPriceStyleNew
                    : style.totalPriceStyle
 
 
43、成功，start： 9057  before： isISDShelves ? style.otherDescNew : otherDescStyle  after： GetABCache.isISDShelves() ? style.otherDescNew : otherDescStyle
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/CarPriceDescribe/src/PriceDescItem.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 7433  before： GetABCache.isISDShelves() ? style.totalHelpIcon : priceDescIconStyle  after： style.totalHelpIcon
 
 
9、成功 ConditionalExpression表达式，start： 7811  before： GetABCache.isISDShelves() ? style.totalPriceNew : style.totalPrice  after： style.totalPriceNew
 
 
16、成功 LogicalExpression表达式，start： 8193  before： GetABCache.isISDShelves() && style.totalPriceWrap  after： style.totalPriceWrap
 
 
22、成功 ConditionalExpression表达式，start： 8490  before： GetABCache.isISDShelves() ? style.totalPriceWrapStyle : totalPriceWrapStyle  after： style.totalPriceWrapStyle
 
 
29、成功 ConditionalExpression表达式，start： 8634  before： GetABCache.isISDShelves() ? style.totalPriceNew : style.totalPrice  after： style.totalPriceNew
 
 
36、成功 ConditionalExpression表达式，start： 8800  before： GetABCache.isISDShelves()
                    ? style.totalPriceStyleNew
                    : style.totalPriceStyle  after： style.totalPriceStyleNew
 
 
43、成功 ConditionalExpression表达式，start： 9163  before： GetABCache.isISDShelves() ? style.otherDescNew : otherDescStyle  after： style.otherDescNew
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/CarRentalCenterDesc/src/CarRentalCenterDesc.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 2202  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 2297  before： isISDShelves ? (
    <Image
      className={c2xStyles.centerIcon}
      source={{
        uri: soldOutTextColor
          ? `${ImageUrl.DIMG04_PATH}1tg1r12000hhgthzx98EC.png`
          : `${ImageUrl.DIMG04_PATH}1tg1d12000hhgtn1xA635.png`,
      }}
    />
  ) : isISDInterestPoints ? (
    <Image
      style={xMergeStyles([
        styles.carRentalCenterImgNew,
        isShowStoreLocated && styles.carRentalCenterImgNewFix,
      ])}
      src={
        soldOutTextColor
          ? `${ImageUrl.DIMG04_PATH}1tg1b12000d0vwast83C4.png`
          : `${ImageUrl.DIMG04_PATH}1tg6v12000d0vw3hd4A39.png`
      }
      mode="aspectFill"
    />
  ) : isPressAble ? (
    <Image
      className={c2xStyles.carRentalCenterImg}
      src={autoProtocol(
        '//pic.c-ctrip.com/car/osd/mobile/bbk/resource/car_rental_center_icon.png',
      )}
      mode="aspectFill"
    />
  ) : (
    <Image
      style={xMergeStyles([styles.carRentalCenterIcon, iconStyle])}
      src={autoProtocol(
        `//pic.c-ctrip.com/car/osd/mobile/bbk/resource/car_rental_center_icon_small${soldOutTextColor ? '_gray' : ''}.png`,
      )}
      mode="aspectFill"
    />
  )  after： GetABCache.isISDShelves() ? (
    <Image
      className={c2xStyles.centerIcon}
      source={{
        uri: soldOutTextColor
          ? `${ImageUrl.DIMG04_PATH}1tg1r12000hhgthzx98EC.png`
          : `${ImageUrl.DIMG04_PATH}1tg1d12000hhgtn1xA635.png`,
      }}
    />
  ) : isISDInterestPoints ? (
    <Image
      style={xMergeStyles([
        styles.carRentalCenterImgNew,
        isShowStoreLocated && styles.carRentalCenterImgNewFix,
      ])}
      src={
        soldOutTextColor
          ? `${ImageUrl.DIMG04_PATH}1tg1b12000d0vwast83C4.png`
          : `${ImageUrl.DIMG04_PATH}1tg6v12000d0vw3hd4A39.png`
      }
      mode="aspectFill"
    />
  ) : isPressAble ? (
    <Image
      className={c2xStyles.carRentalCenterImg}
      src={autoProtocol(
        '//pic.c-ctrip.com/car/osd/mobile/bbk/resource/car_rental_center_icon.png',
      )}
      mode="aspectFill"
    />
  ) : (
    <Image
      style={xMergeStyles([styles.carRentalCenterIcon, iconStyle])}
      src={autoProtocol(
        `//pic.c-ctrip.com/car/osd/mobile/bbk/resource/car_rental_center_icon_small${soldOutTextColor ? '_gray' : ''}.png`,
      )}
      mode="aspectFill"
    />
  )
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/CarRentalCenterDesc/src/CarRentalCenterDesc.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 2331  before： GetABCache.isISDShelves() ? (
    <Image
      className={c2xStyles.centerIcon}
      source={{
        uri: soldOutTextColor
          ? `${ImageUrl.DIMG04_PATH}1tg1r12000hhgthzx98EC.png`
          : `${ImageUrl.DIMG04_PATH}1tg1d12000hhgtn1xA635.png`,
      }}
    />
  ) : isISDInterestPoints ? (
    <Image
      style={xMergeStyles([
        styles.carRentalCenterImgNew,
        isShowStoreLocated && styles.carRentalCenterImgNewFix,
      ])}
      src={
        soldOutTextColor
          ? `${ImageUrl.DIMG04_PATH}1tg1b12000d0vwast83C4.png`
          : `${ImageUrl.DIMG04_PATH}1tg6v12000d0vw3hd4A39.png`
      }
      mode="aspectFill"
    />
  ) : isPressAble ? (
    <Image
      className={c2xStyles.carRentalCenterImg}
      src={autoProtocol(
        '//pic.c-ctrip.com/car/osd/mobile/bbk/resource/car_rental_center_icon.png',
      )}
      mode="aspectFill"
    />
  ) : (
    <Image
      style={xMergeStyles([styles.carRentalCenterIcon, iconStyle])}
      src={autoProtocol(
        `//pic.c-ctrip.com/car/osd/mobile/bbk/resource/car_rental_center_icon_small${soldOutTextColor ? '_gray' : ''}.png`,
      )}
      mode="aspectFill"
    />
  )  after： <Image
      className={c2xStyles.centerIcon}
      source={{
        uri: soldOutTextColor
          ? `${ImageUrl.DIMG04_PATH}1tg1r12000hhgthzx98EC.png`
          : `${ImageUrl.DIMG04_PATH}1tg1d12000hhgtn1xA635.png`,
      }}
    />
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/FilterList/src/FilterListWithNav.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 17134  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 17287  before： isISDShelves && groupCode === GroupCode.Promotion  after： GetABCache.isISDShelves() && groupCode === GroupCode.Promotion
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=1 
 
1、成功 LogicalExpression表达式，start： 20850  before： filterItems.find((f, index) => f.isSelected && index > minIndex) && true  after： filterItems.find((f, index) => f.isSelected && index > minIndex) && GetABCache.isISDShelves()
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/FilterList/src/FilterListWithNav.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 17316  before： GetABCache.isISDShelves() && groupCode === GroupCode.Promotion  after： groupCode === GroupCode.Promotion
 
 
2、成功 LogicalExpression表达式，start： 20850  before： filterItems.find((f, index) => f.isSelected && index > minIndex) && GetABCache.isISDShelves()  after： filterItems.find((f, index) => f.isSelected && index > minIndex)
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/ProductConfirmModal/components/VehicleDetailNew.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 14354  before： !GetABCache.isISDShelves() &&
          (isEasyLife2024 ? (
            <EasyLife2024 onPress={onPressEasyLife} />
          ) : (
            <EasyLifeNew
              easyLifeInfo={easyLifeInfo}
              onPress={onPressEasyLife}
              testID={CarLog.LogExposure({ name: '曝光_门店弹层_无忧租' })}
            />
          ))  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=1 
 
1、成功 JSXExpressionContainer表达式，start： 12260  before： <View className={c2xStyles.wrapper}>
        {!!(secretBoxStage || bookingSecretBoxType) && (
          <Image
            style={styles.secretBoxVehicleDetailInfoBg}
            src={`${ImageUrl.componentImagePath}SecretBox/secretBoxProductInfoBg.png`}
            mode="aspectFill"
          />
        )}
        <NewVehicleName
          vehicleName={decorateVehicleName || name}
          licenseTag={licenseTag}
          licenseType={licenseType}
          isDetail={false}
          isNewEnergy={isNewEnergy}
          lessStyle={isNewEnergy && styles.lessModalStyle}
          secretBoxStage={secretBoxStage || bookingSecretBoxType}
          isShowDetailBtn={false}
        />

        {/* 车辆配置 */}
        {hasVehicleConfig && (
          <View
            onLayout={e =>
              onLayoutVehicleConfig(e, LayoutPartEnum.VehicleConfig)
            }
            className={c2xStyles.venderTagWrap}
            testID={UITestID.car_testid_comp_vendor_modal_tags}
          >
            <XViewExposure
              testID={CarLog.LogExposure({ name: '曝光_门店弹层_事实标签' })}
            >
              <VendorTag
                tags={vehicleTags}
                labelStyle={styles.labelStyle}
                questionIconStyle={styles.questionIconStyle}
                onPressTagQuestion={onPressVehicleTagsQuestion}
                isFromProductConfirmModal={true}
              />
            </XViewExposure>
          </View>
        )}
        {/* 门店实拍 */}
        {hasVideoAndPic && (
          <ImagesNew
            imagesTotalNumber={images.imagesTotalNumber}
            albumName={images.albumName}
            imageList={images.imageList}
            video={images.video}
            videoCover={images.videoCover}
            vehicleInfoLog={vehicleInfoLog}
            onPressMore={onPressMore}
            queryMultimediaAlbum={queryMultimediaAlbum}
            storeAlbumPageParams={storeAlbumPageParams}
            testID={CarLog.LogExposure({ name: '曝光_门店弹层_实拍图' })}
          />
        )}
        {/* 无忧租 */}
        {false}

        <View style={styles.border} />
        {/* 取还方式 */}
        <View className={c2xStyles.locationWrapper}>
          <Text className={c2xStyles.guideWayTitle} fontWeight="medium">
            {texts.guideWay}
          </Text>
          {!isOrderDetail && (
            <RentalLocation
              isVendorList={true}
              isPickupCenter={!!location.pickup.rentCenterName}
              isReturnCenter={!!location.return.rentCenterName}
              onPressPickUpRentalCenter={onPressPickUpRentalCenter}
              onPressReturnRentalCenter={onPressReturnRentalCenter}
              storeGuidInfos={storeGuidInfos}
              onPress={onPressLocation}
              pickUpStoreSelfServiceInfo={pickUpStoreSelfServiceInfo}
              returnStoreSelfServiceInfo={returnStoreSelfServiceInfo}
              isShowPickUpDropOffLabel={false}
            />
          )}
          {isOrderDetail && (
            <View className={c2xStyles.pickReturnInfoWrap}>
              <View>
                {location.pickup && (
                  <LocationInfo
                    title={texts.pickup}
                    dotColor={color.blueBase}
                    way={location.pickup.way}
                    address={location.pickup.address}
                    distance={pickupDistance}
                    rentCenterName={location.pickup.rentCenterName}
                    style={location.return && styles.marginBottom16}
                    onPressRentalCenter={onPressPickUpRentalCenter}
                    onPressLocation={onPressPickUpLocation}
                    isSelfService={isSelfService}
                    selfServiceSlogan={texts.selfServicePickUpSlogan}
                    testID={
                      UITestID.car_testid_page_vendorlist_product_modal_pickup_address
                    }
                    centerTestID={
                      UITestID.car_testid_page_vendorlist_product_modal_pickup_center
                    }
                  />
                )}
                {location.return && (
                  <LocationInfo
                    title={texts.return}
                    way={location.return.way}
                    dotColor={color.orangeBase}
                    address={location.return.address}
                    distance={returnDistance}
                    rentCenterName={location.return.rentCenterName}
                    onPressRentalCenter={onPressReturnRentalCenter}
                    onPressLocation={onPressDropOffLocation}
                    isSelfService={isSelfService}
                    selfServiceSlogan={texts.selfServiceReturnSlogan}
                    testID={
                      UITestID.car_testid_page_vendorlist_product_modal_dropoff_address
                    }
                    centerTestID={
                      UITestID.car_testid_page_vendorlist_product_modal_dropoff_center
                    }
                  />
                )}
              </View>
              <Touchable
                testID={UITestID.car_testid_page_vendorlist_product_modal_gomap}
                onPress={onPressPickUpLocation}
              >
                <Image
                  className={
                    location.return
                      ? c2xStyles.pickReturnMapIcon
                      : c2xStyles.mapIcon
                  }
                  src={autoProtocol(`${ImageUrl.CTRIP_EROS_URL}mapIcon.png`)}
                  mode="aspectFill"
                />
              </Touchable>
            </View>
          )}
        </View>
      </View>  after： <View className={c2xStyles.wrapper}>
        {!!(secretBoxStage || bookingSecretBoxType) && (
          <Image
            style={styles.secretBoxVehicleDetailInfoBg}
            src={`${ImageUrl.componentImagePath}SecretBox/secretBoxProductInfoBg.png`}
            mode="aspectFill"
          />
        )}
        <NewVehicleName
          vehicleName={decorateVehicleName || name}
          licenseTag={licenseTag}
          licenseType={licenseType}
          isDetail={false}
          isNewEnergy={isNewEnergy}
          lessStyle={isNewEnergy && styles.lessModalStyle}
          secretBoxStage={secretBoxStage || bookingSecretBoxType}
          isShowDetailBtn={false}
        />

        {/* 车辆配置 */}
        {hasVehicleConfig && (
          <View
            onLayout={e =>
              onLayoutVehicleConfig(e, LayoutPartEnum.VehicleConfig)
            }
            className={c2xStyles.venderTagWrap}
            testID={UITestID.car_testid_comp_vendor_modal_tags}
          >
            <XViewExposure
              testID={CarLog.LogExposure({ name: '曝光_门店弹层_事实标签' })}
            >
              <VendorTag
                tags={vehicleTags}
                labelStyle={styles.labelStyle}
                questionIconStyle={styles.questionIconStyle}
                onPressTagQuestion={onPressVehicleTagsQuestion}
                isFromProductConfirmModal={true}
              />
            </XViewExposure>
          </View>
        )}
        {/* 门店实拍 */}
        {hasVideoAndPic && (
          <ImagesNew
            imagesTotalNumber={images.imagesTotalNumber}
            albumName={images.albumName}
            imageList={images.imageList}
            video={images.video}
            videoCover={images.videoCover}
            vehicleInfoLog={vehicleInfoLog}
            onPressMore={onPressMore}
            queryMultimediaAlbum={queryMultimediaAlbum}
            storeAlbumPageParams={storeAlbumPageParams}
            testID={CarLog.LogExposure({ name: '曝光_门店弹层_实拍图' })}
          />
        )}
        {/* 无忧租 */}
        

        <View style={styles.border} />
        {/* 取还方式 */}
        <View className={c2xStyles.locationWrapper}>
          <Text className={c2xStyles.guideWayTitle} fontWeight="medium">
            {texts.guideWay}
          </Text>
          {!isOrderDetail && (
            <RentalLocation
              isVendorList={true}
              isPickupCenter={!!location.pickup.rentCenterName}
              isReturnCenter={!!location.return.rentCenterName}
              onPressPickUpRentalCenter={onPressPickUpRentalCenter}
              onPressReturnRentalCenter={onPressReturnRentalCenter}
              storeGuidInfos={storeGuidInfos}
              onPress={onPressLocation}
              pickUpStoreSelfServiceInfo={pickUpStoreSelfServiceInfo}
              returnStoreSelfServiceInfo={returnStoreSelfServiceInfo}
              isShowPickUpDropOffLabel={false}
            />
          )}
          {isOrderDetail && (
            <View className={c2xStyles.pickReturnInfoWrap}>
              <View>
                {location.pickup && (
                  <LocationInfo
                    title={texts.pickup}
                    dotColor={color.blueBase}
                    way={location.pickup.way}
                    address={location.pickup.address}
                    distance={pickupDistance}
                    rentCenterName={location.pickup.rentCenterName}
                    style={location.return && styles.marginBottom16}
                    onPressRentalCenter={onPressPickUpRentalCenter}
                    onPressLocation={onPressPickUpLocation}
                    isSelfService={isSelfService}
                    selfServiceSlogan={texts.selfServicePickUpSlogan}
                    testID={
                      UITestID.car_testid_page_vendorlist_product_modal_pickup_address
                    }
                    centerTestID={
                      UITestID.car_testid_page_vendorlist_product_modal_pickup_center
                    }
                  />
                )}
                {location.return && (
                  <LocationInfo
                    title={texts.return}
                    way={location.return.way}
                    dotColor={color.orangeBase}
                    address={location.return.address}
                    distance={returnDistance}
                    rentCenterName={location.return.rentCenterName}
                    onPressRentalCenter={onPressReturnRentalCenter}
                    onPressLocation={onPressDropOffLocation}
                    isSelfService={isSelfService}
                    selfServiceSlogan={texts.selfServiceReturnSlogan}
                    testID={
                      UITestID.car_testid_page_vendorlist_product_modal_dropoff_address
                    }
                    centerTestID={
                      UITestID.car_testid_page_vendorlist_product_modal_dropoff_center
                    }
                  />
                )}
              </View>
              <Touchable
                testID={UITestID.car_testid_page_vendorlist_product_modal_gomap}
                onPress={onPressPickUpLocation}
              >
                <Image
                  className={
                    location.return
                      ? c2xStyles.pickReturnMapIcon
                      : c2xStyles.mapIcon
                  }
                  src={autoProtocol(`${ImageUrl.CTRIP_EROS_URL}mapIcon.png`)}
                  mode="aspectFill"
                />
              </Touchable>
            </View>
          )}
        </View>
      </View>
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/SkeletonLoading/src/index.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 3879  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 7552  before： isISDShelves
                ? `${ImageUrl.DIMG04_PATH}1tg6c12000hk6f9cq7DBD.png`
                : `${ImageUrl.BBK_IMAGE_PATH}vendor_list_item_loading.png`  after： GetABCache.isISDShelves()
                ? `${ImageUrl.DIMG04_PATH}1tg6c12000hk6f9cq7DBD.png`
                : `${ImageUrl.BBK_IMAGE_PATH}vendor_list_item_loading.png`
 
 
4、成功，start： 7824  before： isISDShelves ? 48 : 0  after： GetABCache.isISDShelves() ? 48 : 0
 
 
7、成功，start： 7881  before： isISDShelves ? 274 : 293  after： GetABCache.isISDShelves() ? 274 : 293
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/SkeletonLoading/src/index.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 7596  before： GetABCache.isISDShelves()
                ? `${ImageUrl.DIMG04_PATH}1tg6c12000hk6f9cq7DBD.png`
                : `${ImageUrl.BBK_IMAGE_PATH}vendor_list_item_loading.png`  after： `${ImageUrl.DIMG04_PATH}1tg6c12000hk6f9cq7DBD.png`
 
 
2、成功 ConditionalExpression表达式，start： 7865  before： GetABCache.isISDShelves() ? 48 : 0  after： 48
 
 
3、成功 ConditionalExpression表达式，start： 7935  before： GetABCache.isISDShelves() ? 274 : 293  after： 274
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/VehicleModal/src/VehicleBaseItem.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 1452  before： GetABCache.isISDShelves() ? c2xStyles.iconNew : c2xStyles.icon  after： c2xStyles.iconNew
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/VendorListEnter/src/VendorListEnterRow.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 5231  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
3、成功 LogicalExpression表达式，start： 8900  before： GetABCache.isISDShelves() && isAndroid  after： isAndroid
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 7382  before： isISDShelves
                      ? styles.originPriceStyleNew
                      : styles.originPriceStyle  after： GetABCache.isISDShelves()
                      ? styles.originPriceStyleNew
                      : styles.originPriceStyle
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/ComponentBusiness/VendorListEnter/src/VendorListEnterRow.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 7432  before： GetABCache.isISDShelves()
                      ? styles.originPriceStyleNew
                      : styles.originPriceStyle  after： styles.originPriceStyleNew
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Containers/ListQuickFilterBarContainer.ts, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 1056  before： GetABCache.isISDShelves() &&
          item?.groupCode === GroupCode.Promotion  after： item?.groupCode === GroupCode.Promotion
 
 
2、成功 LogicalExpression表达式，start： 1210  before： GetABCache.isISDShelves() &&
          item?.groupCode === GroupCode.Promotion  after： item?.groupCode === GroupCode.Promotion
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Containers/VendorListPageContainer.ts, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 7461  before： GetABCache.isISDShelves()
      ? getNoMatchDataNew(state)?.isNomatch
      : getNoMatchData(state)?.isNomatch  after： getNoMatchDataNew(state)?.isNomatch
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/Booking/Component/RentalLocationItem.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 2757  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
3、成功 ConditionalExpression表达式，start： 3904  before： GetABCache.isISDShelves() ? c2xStyles.rowNew : c2xStyles.row  after： c2xStyles.rowNew
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 3885  before： isCenter && isISDShelves  after： isCenter && GetABCache.isISDShelves()
 
 
5、成功，start： 4305  before： !isISDShelves  after： !GetABCache.isISDShelves()
 
 
9、成功，start： 4480  before： !isISDShelves  after： !GetABCache.isISDShelves()
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/Booking/Component/RentalLocationItem.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 3885  before： isCenter && GetABCache.isISDShelves()  after： isCenter
 
 
5、成功 LogicalExpression表达式，start： 4281  before： isISDInterestPoints && !!isCenter && !GetABCache.isISDShelves()  after： false
 
 
9、成功 LogicalExpression表达式，start： 4492  before： !!isCenter && !GetABCache.isISDShelves()  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=8 
 
1、成功 LogicalExpression表达式，start： 4252  before： false && (
              <Text className={c2xStyles.locatedInStyle}>
                {Texts.locatedIn}
              </Text>
            )  after： !GetABCache.isISDShelves() && (
              <Text className={c2xStyles.locatedInStyle}>
                {Texts.locatedIn}
              </Text>
            )
 
 
5、成功 LogicalExpression表达式，start： 4405  before： false && (
              <CarRentalCenterDesc
                isShowStoreLocated={false}
                isLinearGradient={false}
                onPress={onPressRentalCenter}
              />
            )  after： !GetABCache.isISDShelves() && (
              <CarRentalCenterDesc
                isShowStoreLocated={false}
                isLinearGradient={false}
                onPress={onPressRentalCenter}
              />
            )
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/Booking/Component/RentalLocationItem.tsx, 第3次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 4298  before： !GetABCache.isISDShelves() && (
              <Text className={c2xStyles.locatedInStyle}>
                {Texts.locatedIn}
              </Text>
            )  after： false
 
 
5、成功 LogicalExpression表达式，start： 4472  before： !GetABCache.isISDShelves() && (
              <CarRentalCenterDesc
                isShowStoreLocated={false}
                isLinearGradient={false}
                onPress={onPressRentalCenter}
              />
            )  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=2 
 
1、成功 JSXExpressionContainer表达式，start： 3785  before： <View
            className={
              c2xStyles.rowNew
            }
          >
            {isCenter && (
              <Image
                className={c2xStyles.centerIcon}
                source={{
                  uri: `${ImageUrl.DIMG04_PATH}1tg1d12000hhgtn1xA635.png`,
                }}
              />
            )}
            <Text numberOfLines={1} className={c2xStyles.storeGuid}>
              {noCenterText}
            </Text>
            {false}
            {false}
          </View>  after： <View
            className={
              c2xStyles.rowNew
            }
          >
            {isCenter && (
              <Image
                className={c2xStyles.centerIcon}
                source={{
                  uri: `${ImageUrl.DIMG04_PATH}1tg1d12000hhgtn1xA635.png`,
                }}
              />
            )}
            <Text numberOfLines={1} className={c2xStyles.storeGuid}>
              {noCenterText}
            </Text>
            
            {false}
          </View>
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/Booking/VehicleAndVendorInfoVC.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 8086  before： isEasyLife &&
                  !CarServerABTesting.isBlockOldEasyLife() &&
                  !GetABCache.isISDShelves()  after： false
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/List/Components/VehicleNew.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 34854  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
5、成功 ConditionalExpression表达式，start： 45147  before： GetABCache.isISDShelves() ? (
                <DetailVendorNew
                  key={`${vehicleCode}_${vehicleIndex}_${-1}`}
                  index={0}
                  isUrlTop={isUrlTop}
                  {...topVendor}
                  isHideCommentLabel
                  section={section}
                  vehicleItemData={vehicleItemData}
                  vehicleName={vehicleName}
                  isHotLabel={isHotLabel}
                  isSoldOut={isSoldOut}
                  totalPricePress={totalPricePress}
                  onPressQuestion={totalPricePress}
                  isShowBtnIcon={true}
                  onPressVendor={handleVehiclePress}
                  onPressBooking={handleVehiclePress}
                  onPressReview={handleVehiclePress}
                  vendorContainerStyle={VehicleStyle.vendorContainerStyleNew}
                  vendorContentStyle={VehicleStyle.vendorContentStyleNew}
                  wrapStyle={
                    isUserBrowsedFlag
                      ? VehicleStyle.userBrowsingVendorWrapStyleNew
                      : VehicleStyle.vendorWrapStyleNew
                  }
                  showRightIcon={false}
                  licenseTag={licenseLabel}
                  licenseType={licenseType}
                  licenseSize={PlateBgSize.small}
                  isSelfService={isSelfService}
                  showTopBorder={true}
                  optimizationStrengthenVendorNameStyle={
                    VehicleStyle.topVendorNameStyle
                  }
                  vendorNameMaxByte={10}
                  isHasBookBtn={false}
                />
              ) : (
                <DetailVendor
                  key={`${vehicleCode}_${vehicleIndex}_${-1}`}
                  index={0}
                  isUrlTop={isUrlTop}
                  {...topVendor}
                  isHideCommentLabel
                  section={section}
                  vehicleItemData={vehicleItemData}
                  vehicleName={vehicleName}
                  isHotLabel={isHotLabel}
                  isSoldOut={isSoldOut}
                  totalPricePress={totalPricePress}
                  onPressQuestion={totalPricePress}
                  isShowBtnIcon={true}
                  onPressVendor={handleVehiclePress}
                  onPressBooking={handleVehiclePress}
                  onPressReview={handleVehiclePress}
                  vendorContainerStyle={VehicleStyle.vendorContainerStyle}
                  wrapStyle={
                    isUserBrowsedFlag
                      ? VehicleStyle.userBrowsingVendorWrapStyle
                      : VehicleStyle.vendorWrapStyle
                  }
                  showRightIcon={false}
                  licenseTag={licenseLabel}
                  licenseType={licenseType}
                  licenseSize={PlateBgSize.small}
                  isSelfService={isSelfService}
                  showTopBorder={true}
                  optimizationStrengthenVendorNameStyle={
                    VehicleStyle.topVendorNameStyle
                  }
                  vendorNameMaxByte={10}
                />
              )  after： <DetailVendorNew
                  key={`${vehicleCode}_${vehicleIndex}_${-1}`}
                  index={0}
                  isUrlTop={isUrlTop}
                  {...topVendor}
                  isHideCommentLabel
                  section={section}
                  vehicleItemData={vehicleItemData}
                  vehicleName={vehicleName}
                  isHotLabel={isHotLabel}
                  isSoldOut={isSoldOut}
                  totalPricePress={totalPricePress}
                  onPressQuestion={totalPricePress}
                  isShowBtnIcon={true}
                  onPressVendor={handleVehiclePress}
                  onPressBooking={handleVehiclePress}
                  onPressReview={handleVehiclePress}
                  vendorContainerStyle={VehicleStyle.vendorContainerStyleNew}
                  vendorContentStyle={VehicleStyle.vendorContentStyleNew}
                  wrapStyle={
                    isUserBrowsedFlag
                      ? VehicleStyle.userBrowsingVendorWrapStyleNew
                      : VehicleStyle.vendorWrapStyleNew
                  }
                  showRightIcon={false}
                  licenseTag={licenseLabel}
                  licenseType={licenseType}
                  licenseSize={PlateBgSize.small}
                  isSelfService={isSelfService}
                  showTopBorder={true}
                  optimizationStrengthenVendorNameStyle={
                    VehicleStyle.topVendorNameStyle
                  }
                  vendorNameMaxByte={10}
                  isHasBookBtn={false}
                />
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 34881  before： isISDShelves ? color.C_555555 : color.fontSecondary  after： GetABCache.isISDShelves() ? color.C_555555 : color.fontSecondary
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/List/Components/VehicleNew.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 34909  before： GetABCache.isISDShelves() ? color.C_555555 : color.fontSecondary  after： color.C_555555
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/OrderDetail/Components/Vendor.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 4705  before： isEasyLife && !GetABCache.isISDShelves()  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=4 
 
1、成功 LogicalExpression表达式，start： 4705  before： false && (
            <EasyLifeTag imageStyle={styles.easyLiftTagStyle} />
          )  after： !GetABCache.isISDShelves() && (
            <EasyLifeTag imageStyle={styles.easyLiftTagStyle} />
          )
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/OrderDetail/Components/Vendor.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 4749  before： !GetABCache.isISDShelves() && (
            <EasyLifeTag imageStyle={styles.easyLiftTagStyle} />
          )  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=1 
 
1、成功 JSXExpressionContainer表达式，start： 4542  before： <View className={c2xStyles.serviceTagWrap}>
          {isSelfService && (
            <SelfServiceLabel style={styles.selfServiceLabel} />
          )}
          {false}
          <VendorTag tags={serviceTags} />
        </View>  after： <View className={c2xStyles.serviceTagWrap}>
          {isSelfService && (
            <SelfServiceLabel style={styles.selfServiceLabel} />
          )}
          
          <VendorTag tags={serviceTags} />
        </View>
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Components/LocationAndDateMonitor.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 1884  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 2037  before： isISDShelves
      ? ShelvesLocationSearchCenter
      : LocationSearchCenterView  after： GetABCache.isISDShelves()
      ? ShelvesLocationSearchCenter
      : LocationSearchCenterView
 
 
2、成功，start： 2185  before： isISDShelves ? styles.wrapNew : styles.wrap  after： GetABCache.isISDShelves() ? styles.wrapNew : styles.wrap
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Components/LocationAndDateMonitor.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 2071  before： GetABCache.isISDShelves()
      ? ShelvesLocationSearchCenter
      : LocationSearchCenterView  after： ShelvesLocationSearchCenter
 
 
2、成功 ConditionalExpression表达式，start： 2226  before： GetABCache.isISDShelves() ? styles.wrapNew : styles.wrap  after： styles.wrapNew
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Components/SectionFooter.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 1397  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 1899  before： isISDShelves ? c2xStyles.moreBtnNew : c2xStyles.moreBtn  after： GetABCache.isISDShelves() ? c2xStyles.moreBtnNew : c2xStyles.moreBtn
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Components/SectionFooter.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 1927  before： GetABCache.isISDShelves() ? c2xStyles.moreBtnNew : c2xStyles.moreBtn  after： c2xStyles.moreBtnNew
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Components/ShelvesItem.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 VariableDeclarator表达式，start： 2090  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 6787  before： isISDShelves && i > 0  after： GetABCache.isISDShelves() && i > 0
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Components/ShelvesItem.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 6816  before： GetABCache.isISDShelves() && i > 0  after： i > 0
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Components/VehicleModal.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 1321  before： GetABCache.isISDShelves()
          ? false
          : data.vehicleBaseInfoProps.isNewEnergyVeh  after： false
 
 
2、成功 LogicalExpression表达式，start： 2038  before： GetABCache.isISDShelves() && styles.headerGapLine  after： styles.headerGapLine
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Components/VendorListRecommondLists.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 1942  before： GetABCache.isISDShelves() ? styles.itemNew : styles.item  after： styles.itemNew
 
 
3、成功 VariableDeclarator表达式，start： 2229  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 2217  before： isISDShelves ? (
        <ShelvesItemHeader shelvesType={ShelvesType.Recommend} />
      ) : (
        <NewSectionHeader isShowGradient={true} />
      )  after： GetABCache.isISDShelves() ? (
        <ShelvesItemHeader shelvesType={ShelvesType.Recommend} />
      ) : (
        <NewSectionHeader isShowGradient={true} />
      )
 
 
3、成功，start： 2434  before： !isISDShelves  after： !GetABCache.isISDShelves()
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Components/VendorListRecommondLists.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 2255  before： GetABCache.isISDShelves() ? (
        <ShelvesItemHeader shelvesType={ShelvesType.Recommend} />
      ) : (
        <NewSectionHeader isShowGradient={true} />
      )  after： <ShelvesItemHeader shelvesType={ShelvesType.Recommend} />
 
 
3、成功 LogicalExpression表达式，start： 2477  before： !GetABCache.isISDShelves() && styles.listWrap  after： false
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Index.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 48503  before： GetABCache.isISDShelves() ? 0 : 12  after： 0
 
 
3、成功 VariableDeclarator表达式，start： 49232  before： const isISDShelves = GetABCache.isISDShelves();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 50680  before： !isISDShelves  after： !GetABCache.isISDShelves()
 
 
5、成功，start： 50811  before： !isISDShelves  after： !GetABCache.isISDShelves()
 
 
9、成功，start： 50945  before： !isISDShelves  after： !GetABCache.isISDShelves()
 
 
13、成功，start： 54225  before： isISDShelves ? (
                isShelvesServer ? (
                  <VendorListShelves2Container
                    isCouponEntry={isCouponEntry}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onPressQuestion={this.handleSetPriceDetailModal2}
                    onPressBooking={this.handleGoToBooking2}
                    showCarServiceDetail={this.showCarServiceDetail}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFloorLayout={this.onFloorLayout}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    vehicleCode={vehicleCode}
                    onScrollToFloor={this.onScrollToFloor}
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',
                      info: {
                        vehicleCode,
                      },
                    })}
                  />
                ) : (
                  <VendorListShelvesContainer
                    priceListLen={priceListLen}
                    onPressQuestion={this.handleSetPriceDetailModal}
                    onPressBooking={this.handleGoToBooking}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFilteredLayout={this.onVendorListFilteredLayout}
                    showAmount={7}
                    showFilterAmount={2}
                    vehicleCode={vehicleCode}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',

                      info: {
                        vehicleCode,
                      },
                    })}
                    handleshowFit={this.handleshowFit}
                    loadNextPageVendorList={loadNextPageVendorList}
                  />
                )
              ) : (
                <>
                  <View onLayout={this.onVendorListSpecificLayout}>
                    <VendorListSpecific
                      fetchVehicleDetailList={this.fetchVehicleDetailList}
                      priceListLen={priceListLen}
                      onPressQuestion={this.handleSetPriceDetailModal}
                      onPressBooking={this.handleGoToBooking}
                      datePickerRef={this.datePickerRef}
                      isSpecificList={true}
                      showAmount={7}
                      vehicleCode={vehicleCode}
                      showSearchSelectorWrapWithLog={
                        this.showSearchSelectorWrapWithLog
                      }
                      sectionHeaderTestID={CarLog.LogExposure({
                        name: '曝光_产品详情页_修改取还车信息',

                        info: {
                          vehicleCode,
                        },
                      })}
                      handleshowFit={this.handleshowFit}
                      loadNextPageVendorList={loadNextPageVendorList}
                    />
                  </View>
                  <View onLayout={this.onVendorListFilteredLayout}>
                    <VendorListFiltered
                      onPressQuestion={this.handleSetPriceDetailModal}
                      onPressBooking={this.handleGoToBooking}
                      showAmount={2}
                      vehicleCode={vehicleCode}
                      loadNextPageVendorList={loadNextPageVendorList}
                    />
                  </View>
                </>
              )  after： GetABCache.isISDShelves() ? (
                isShelvesServer ? (
                  <VendorListShelves2Container
                    isCouponEntry={isCouponEntry}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onPressQuestion={this.handleSetPriceDetailModal2}
                    onPressBooking={this.handleGoToBooking2}
                    showCarServiceDetail={this.showCarServiceDetail}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFloorLayout={this.onFloorLayout}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    vehicleCode={vehicleCode}
                    onScrollToFloor={this.onScrollToFloor}
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',
                      info: {
                        vehicleCode,
                      },
                    })}
                  />
                ) : (
                  <VendorListShelvesContainer
                    priceListLen={priceListLen}
                    onPressQuestion={this.handleSetPriceDetailModal}
                    onPressBooking={this.handleGoToBooking}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFilteredLayout={this.onVendorListFilteredLayout}
                    showAmount={7}
                    showFilterAmount={2}
                    vehicleCode={vehicleCode}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',

                      info: {
                        vehicleCode,
                      },
                    })}
                    handleshowFit={this.handleshowFit}
                    loadNextPageVendorList={loadNextPageVendorList}
                  />
                )
              ) : (
                <>
                  <View onLayout={this.onVendorListSpecificLayout}>
                    <VendorListSpecific
                      fetchVehicleDetailList={this.fetchVehicleDetailList}
                      priceListLen={priceListLen}
                      onPressQuestion={this.handleSetPriceDetailModal}
                      onPressBooking={this.handleGoToBooking}
                      datePickerRef={this.datePickerRef}
                      isSpecificList={true}
                      showAmount={7}
                      vehicleCode={vehicleCode}
                      showSearchSelectorWrapWithLog={
                        this.showSearchSelectorWrapWithLog
                      }
                      sectionHeaderTestID={CarLog.LogExposure({
                        name: '曝光_产品详情页_修改取还车信息',

                        info: {
                          vehicleCode,
                        },
                      })}
                      handleshowFit={this.handleshowFit}
                      loadNextPageVendorList={loadNextPageVendorList}
                    />
                  </View>
                  <View onLayout={this.onVendorListFilteredLayout}>
                    <VendorListFiltered
                      onPressQuestion={this.handleSetPriceDetailModal}
                      onPressBooking={this.handleGoToBooking}
                      showAmount={2}
                      vehicleCode={vehicleCode}
                      loadNextPageVendorList={loadNextPageVendorList}
                    />
                  </View>
                </>
              )
 
 
19、成功，start： 58250  before： isISDShelves && !isLoading  after： GetABCache.isISDShelves() && !isLoading
 
 
25、成功，start： 61023  before： isISDShelves && isLazyLoad  after： GetABCache.isISDShelves() && isLazyLoad
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Index.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 50710  before： !GetABCache.isISDShelves() && hasFit  after： false
 
 
5、成功 LogicalExpression表达式，start： 50854  before： !GetABCache.isISDShelves() && hasNoFit  after： false
 
 
9、成功 LogicalExpression表达式，start： 51001  before： !GetABCache.isISDShelves() && hasRecommend  after： false
 
 
13、成功 ConditionalExpression表达式，start： 54310  before： GetABCache.isISDShelves() ? (
                isShelvesServer ? (
                  <VendorListShelves2Container
                    isCouponEntry={isCouponEntry}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onPressQuestion={this.handleSetPriceDetailModal2}
                    onPressBooking={this.handleGoToBooking2}
                    showCarServiceDetail={this.showCarServiceDetail}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFloorLayout={this.onFloorLayout}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    vehicleCode={vehicleCode}
                    onScrollToFloor={this.onScrollToFloor}
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',
                      info: {
                        vehicleCode,
                      },
                    })}
                  />
                ) : (
                  <VendorListShelvesContainer
                    priceListLen={priceListLen}
                    onPressQuestion={this.handleSetPriceDetailModal}
                    onPressBooking={this.handleGoToBooking}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFilteredLayout={this.onVendorListFilteredLayout}
                    showAmount={7}
                    showFilterAmount={2}
                    vehicleCode={vehicleCode}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',

                      info: {
                        vehicleCode,
                      },
                    })}
                    handleshowFit={this.handleshowFit}
                    loadNextPageVendorList={loadNextPageVendorList}
                  />
                )
              ) : (
                <>
                  <View onLayout={this.onVendorListSpecificLayout}>
                    <VendorListSpecific
                      fetchVehicleDetailList={this.fetchVehicleDetailList}
                      priceListLen={priceListLen}
                      onPressQuestion={this.handleSetPriceDetailModal}
                      onPressBooking={this.handleGoToBooking}
                      datePickerRef={this.datePickerRef}
                      isSpecificList={true}
                      showAmount={7}
                      vehicleCode={vehicleCode}
                      showSearchSelectorWrapWithLog={
                        this.showSearchSelectorWrapWithLog
                      }
                      sectionHeaderTestID={CarLog.LogExposure({
                        name: '曝光_产品详情页_修改取还车信息',

                        info: {
                          vehicleCode,
                        },
                      })}
                      handleshowFit={this.handleshowFit}
                      loadNextPageVendorList={loadNextPageVendorList}
                    />
                  </View>
                  <View onLayout={this.onVendorListFilteredLayout}>
                    <VendorListFiltered
                      onPressQuestion={this.handleSetPriceDetailModal}
                      onPressBooking={this.handleGoToBooking}
                      showAmount={2}
                      vehicleCode={vehicleCode}
                      loadNextPageVendorList={loadNextPageVendorList}
                    />
                  </View>
                </>
              )  after： isShelvesServer ? (
                  <VendorListShelves2Container
                    isCouponEntry={isCouponEntry}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onPressQuestion={this.handleSetPriceDetailModal2}
                    onPressBooking={this.handleGoToBooking2}
                    showCarServiceDetail={this.showCarServiceDetail}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFloorLayout={this.onFloorLayout}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    vehicleCode={vehicleCode}
                    onScrollToFloor={this.onScrollToFloor}
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',
                      info: {
                        vehicleCode,
                      },
                    })}
                  />
                ) : (
                  <VendorListShelvesContainer
                    priceListLen={priceListLen}
                    onPressQuestion={this.handleSetPriceDetailModal}
                    onPressBooking={this.handleGoToBooking}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFilteredLayout={this.onVendorListFilteredLayout}
                    showAmount={7}
                    showFilterAmount={2}
                    vehicleCode={vehicleCode}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',

                      info: {
                        vehicleCode,
                      },
                    })}
                    handleshowFit={this.handleshowFit}
                    loadNextPageVendorList={loadNextPageVendorList}
                  />
                )
 
 
19、成功 LogicalExpression表达式，start： 58331  before： GetABCache.isISDShelves() && !isLoading  after： !isLoading
 
 
25、成功 LogicalExpression表达式，start： 61117  before： GetABCache.isISDShelves() && isLazyLoad  after： isLazyLoad
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=12 
 
1、成功 LogicalExpression表达式，start： 50680  before： false && isFitVisibale  after： !GetABCache.isISDShelves() && isFitVisibale
 
 
5、成功 LogicalExpression表达式，start： 50793  before： false && noFitVisibale  after： !GetABCache.isISDShelves() && noFitVisibale
 
 
9、成功 LogicalExpression表达式，start： 50907  before： false && recommendHeaderVisibale  after： !GetABCache.isISDShelves() && recommendHeaderVisibale
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Index.tsx, 第3次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 50710  before： !GetABCache.isISDShelves() && isFitVisibale  after： false
 
 
5、成功 LogicalExpression表达式，start： 50844  before： !GetABCache.isISDShelves() && noFitVisibale  after： false
 
 
9、成功 LogicalExpression表达式，start： 50979  before： !GetABCache.isISDShelves() && recommendHeaderVisibale  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=12 
 
1、成功 LogicalExpression表达式，start： 50680  before： false && (
            <StickyBanner style={titleStyle} isFit={true} />
          )  after： !GetABCache.isISDShelves() && (
            <StickyBanner style={titleStyle} isFit={true} />
          )
 
 
5、成功 LogicalExpression表达式，start： 50776  before： false && (
            <StickyBanner style={titleStyle} isFit={false} />
          )  after： !GetABCache.isISDShelves() && (
            <StickyBanner style={titleStyle} isFit={false} />
          )
 
 
9、成功 LogicalExpression表达式，start： 50873  before： false && (
            <View style={xMergeStyles([titleStyle, styles.recommendTitleWrap])}>
              <NewSectionHeader isShowGradient={false} />
            </View>
          )  after： !GetABCache.isISDShelves() && (
            <View style={xMergeStyles([titleStyle, styles.recommendTitleWrap])}>
              <NewSectionHeader isShowGradient={false} />
            </View>
          )
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/VendorList/Index.tsx, 第4次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 50724  before： !GetABCache.isISDShelves() && (
            <StickyBanner style={titleStyle} isFit={true} />
          )  after： false
 
 
5、成功 LogicalExpression表达式，start： 50841  before： !GetABCache.isISDShelves() && (
            <StickyBanner style={titleStyle} isFit={false} />
          )  after： false
 
 
9、成功 LogicalExpression表达式，start： 50959  before： !GetABCache.isISDShelves() && (
            <View style={xMergeStyles([titleStyle, styles.recommendTitleWrap])}>
              <NewSectionHeader isShowGradient={false} />
            </View>
          )  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=3 
 
1、成功 JSXExpressionContainer表达式，start： 49303  before： <View className={c2xStyles.page}>
          <ProductHeader
            refFn={this.refCallBack}
            opacityAnimation={opacityAnimation}
            fixOpacityAnimation={fixOpacityAnimation}
            channelType={Utils.getType()}
            {...bbkVehicleNameProps}
            {...this.getProductHeaderProps(
              headerData,
              bizType,
              toolBoxCustomerJumpUrl,
              isShowVRIcon,
              '',
              vehicleCode,
              vehicleDetailListReq,
              shareVehicleInfo,
            )}
            isHideLinear={true}
            isHideShare={!remoteQConfig?.isShowIsdShareBtn}
            isShowProductName={true}
            leftIconTestID={UITestID.car_testid_page_vendorlist_header_goback}
          />

          {locationAndDateMonitorVisible && !isLoading && (
            <LocationAndDateMonitor
              style={monitorStyle}
              isShelvesServer={isShelvesServer}
              floorName={curFoorName}
              clickLogData={curClickLogData}
              clickWithLog={this.showSearchSelectorWrapWithLog}
              datePickerRef={this.datePickerRef}
              testID={CarLog.LogExposure({
                name: '曝光_产品详情页_修改取还车信息',
                info: {
                  vehicleCode,
                },
              })}
            />
          )}
          {false}
          {false}
          {false}
          <BouncesScrollView
            ref={scrollView => {
              this.scrollViewRef = scrollView;
            }}
            showsVerticalScrollIndicator={false}
            style={styles.mainScroll}
            scrollEventThrottle={16}
            onScroll={this.onScroll}
            onMomentumScrollEnd={this.onMomentumScrollEnd}
            onScrollEndDrag={this.onScrollEndDrag}
            testID={UITestID.car_testid_page_vendorList_ScrollView}
            {...BouncesScrollProps}
          >
            <XAnimated.View
              animation={isAndroid ? 0 : this.state.immerseTransYAni}
              onTransitionEnd={this.animatedMenuEnd}
            >
              <View onLayout={this.onVendorListTopLayout}>
                <VehicleImageBounces
                  // @ts-ignore
                  immerseMoveY={this.state.immerseTransYAniImage}
                  showFullImmerse={showFullImmerse}
                  height={this.fullScreenHeight}
                  onBeginDrag={this.onBouncesBlurBeginDrag}
                  onScrollEndDrag={this.onBouncesBlurScrollEndDrag}
                  onLeftScrollDrag={this.onLeftScroll}
                  onImagePress={this.carouselImagePress}
                />

                <View className={c2xStyles.contentMargin_B2} />
                <MarketingBanner
                  isShowRestAssured={isShowRestAssured}
                  hasLaborDayLabel={hasLaborDayLabel}
                  marketingAtmosphere={this.props.marketingAtmosphere}
                  marketTheme={vendorListMarketTheme}
                  isShowMarketTheme={isShowMarketTheme}
                  yunnanBannerInfo={this.props.yunnanBannerInfo}
                />
                {isShelvesServer ? (
                  <VehicleAndLimit2
                    onPressVehicleName={this.props.openVehicleModal}
                    hasLaborDayLabel={hasLaborDayLabel}
                    showFullImmerse={showFullImmerse}
                    onPressShowLessModal={this.showLessModal}
                    onPressShowNoLimitModal={this.showNoLimitModal}
                  />
                ) : (
                  <VehicleAndLimitB
                    onPressVehicleName={this.props.openVehicleModal}
                    hasLaborDayLabel={hasLaborDayLabel}
                    showFullImmerse={showFullImmerse}
                    onPressShowLessModal={this.showLessModal}
                    onPressShowNoLimitModal={this.showNoLimitModal}
                  />
                )}
                {!isShelvesServer && (
                  <CouponEntryNew vehicleCode={vehicleCode} />
                )}
                {!!remoteQConfig?.vendorListTangramEntrance &&
                  tangramEntranceInfos?.length > 0 &&
                  tangramEntranceInfos.map(item => (
                    <TangramEntrance
                      key={item.title}
                      data={item}
                      fromPage={this.getPageId()}
                    />
                  ))}
              </View>
              {isShelvesServer ? (
                  <VendorListShelves2Container
                    isCouponEntry={isCouponEntry}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onPressQuestion={this.handleSetPriceDetailModal2}
                    onPressBooking={this.handleGoToBooking2}
                    showCarServiceDetail={this.showCarServiceDetail}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFloorLayout={this.onFloorLayout}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    vehicleCode={vehicleCode}
                    onScrollToFloor={this.onScrollToFloor}
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',
                      info: {
                        vehicleCode,
                      },
                    })}
                  />
                ) : (
                  <VendorListShelvesContainer
                    priceListLen={priceListLen}
                    onPressQuestion={this.handleSetPriceDetailModal}
                    onPressBooking={this.handleGoToBooking}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFilteredLayout={this.onVendorListFilteredLayout}
                    showAmount={7}
                    showFilterAmount={2}
                    vehicleCode={vehicleCode}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',

                      info: {
                        vehicleCode,
                      },
                    })}
                    handleshowFit={this.handleshowFit}
                    loadNextPageVendorList={loadNextPageVendorList}
                  />
                )}
              {!isLoading && isLazyLoad && (
                <VendorListRecommondLists
                  fromPage={VehicleNewFromPage.fromVendorList}
                  handleshowRecommond={this.handleshowRecommend}
                />
              )}
              {(!isNomatch || (!isLoading)) && (
                <View className={c2xStyles.marketingFooterWrap}>
                  <MarketingFooter {...marktingFooterData} />
                </View>
              )}
            </XAnimated.View>
          </BouncesScrollView>
        </View>  after： <View className={c2xStyles.page}>
          <ProductHeader
            refFn={this.refCallBack}
            opacityAnimation={opacityAnimation}
            fixOpacityAnimation={fixOpacityAnimation}
            channelType={Utils.getType()}
            {...bbkVehicleNameProps}
            {...this.getProductHeaderProps(
              headerData,
              bizType,
              toolBoxCustomerJumpUrl,
              isShowVRIcon,
              '',
              vehicleCode,
              vehicleDetailListReq,
              shareVehicleInfo,
            )}
            isHideLinear={true}
            isHideShare={!remoteQConfig?.isShowIsdShareBtn}
            isShowProductName={true}
            leftIconTestID={UITestID.car_testid_page_vendorlist_header_goback}
          />

          {locationAndDateMonitorVisible && !isLoading && (
            <LocationAndDateMonitor
              style={monitorStyle}
              isShelvesServer={isShelvesServer}
              floorName={curFoorName}
              clickLogData={curClickLogData}
              clickWithLog={this.showSearchSelectorWrapWithLog}
              datePickerRef={this.datePickerRef}
              testID={CarLog.LogExposure({
                name: '曝光_产品详情页_修改取还车信息',
                info: {
                  vehicleCode,
                },
              })}
            />
          )}
          
          {false}
          {false}
          <BouncesScrollView
            ref={scrollView => {
              this.scrollViewRef = scrollView;
            }}
            showsVerticalScrollIndicator={false}
            style={styles.mainScroll}
            scrollEventThrottle={16}
            onScroll={this.onScroll}
            onMomentumScrollEnd={this.onMomentumScrollEnd}
            onScrollEndDrag={this.onScrollEndDrag}
            testID={UITestID.car_testid_page_vendorList_ScrollView}
            {...BouncesScrollProps}
          >
            <XAnimated.View
              animation={isAndroid ? 0 : this.state.immerseTransYAni}
              onTransitionEnd={this.animatedMenuEnd}
            >
              <View onLayout={this.onVendorListTopLayout}>
                <VehicleImageBounces
                  // @ts-ignore
                  immerseMoveY={this.state.immerseTransYAniImage}
                  showFullImmerse={showFullImmerse}
                  height={this.fullScreenHeight}
                  onBeginDrag={this.onBouncesBlurBeginDrag}
                  onScrollEndDrag={this.onBouncesBlurScrollEndDrag}
                  onLeftScrollDrag={this.onLeftScroll}
                  onImagePress={this.carouselImagePress}
                />

                <View className={c2xStyles.contentMargin_B2} />
                <MarketingBanner
                  isShowRestAssured={isShowRestAssured}
                  hasLaborDayLabel={hasLaborDayLabel}
                  marketingAtmosphere={this.props.marketingAtmosphere}
                  marketTheme={vendorListMarketTheme}
                  isShowMarketTheme={isShowMarketTheme}
                  yunnanBannerInfo={this.props.yunnanBannerInfo}
                />
                {isShelvesServer ? (
                  <VehicleAndLimit2
                    onPressVehicleName={this.props.openVehicleModal}
                    hasLaborDayLabel={hasLaborDayLabel}
                    showFullImmerse={showFullImmerse}
                    onPressShowLessModal={this.showLessModal}
                    onPressShowNoLimitModal={this.showNoLimitModal}
                  />
                ) : (
                  <VehicleAndLimitB
                    onPressVehicleName={this.props.openVehicleModal}
                    hasLaborDayLabel={hasLaborDayLabel}
                    showFullImmerse={showFullImmerse}
                    onPressShowLessModal={this.showLessModal}
                    onPressShowNoLimitModal={this.showNoLimitModal}
                  />
                )}
                {!isShelvesServer && (
                  <CouponEntryNew vehicleCode={vehicleCode} />
                )}
                {!!remoteQConfig?.vendorListTangramEntrance &&
                  tangramEntranceInfos?.length > 0 &&
                  tangramEntranceInfos.map(item => (
                    <TangramEntrance
                      key={item.title}
                      data={item}
                      fromPage={this.getPageId()}
                    />
                  ))}
              </View>
              {isShelvesServer ? (
                  <VendorListShelves2Container
                    isCouponEntry={isCouponEntry}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onPressQuestion={this.handleSetPriceDetailModal2}
                    onPressBooking={this.handleGoToBooking2}
                    showCarServiceDetail={this.showCarServiceDetail}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFloorLayout={this.onFloorLayout}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    vehicleCode={vehicleCode}
                    onScrollToFloor={this.onScrollToFloor}
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',
                      info: {
                        vehicleCode,
                      },
                    })}
                  />
                ) : (
                  <VendorListShelvesContainer
                    priceListLen={priceListLen}
                    onPressQuestion={this.handleSetPriceDetailModal}
                    onPressBooking={this.handleGoToBooking}
                    filterLabelsStr={pageParam?.filterLabelsStr}
                    onSpecificLayout={this.onVendorListSpecificLayout}
                    onFilteredLayout={this.onVendorListFilteredLayout}
                    showAmount={7}
                    showFilterAmount={2}
                    vehicleCode={vehicleCode}
                    showSearchSelectorWrapWithLog={
                      this.showSearchSelectorWrapWithLog
                    }
                    sectionHeaderTestID={CarLog.LogExposure({
                      name: '曝光_产品详情页_修改取还车信息',

                      info: {
                        vehicleCode,
                      },
                    })}
                    handleshowFit={this.handleshowFit}
                    loadNextPageVendorList={loadNextPageVendorList}
                  />
                )}
              {!isLoading && isLazyLoad && (
                <VendorListRecommondLists
                  fromPage={VehicleNewFromPage.fromVendorList}
                  handleshowRecommond={this.handleshowRecommend}
                />
              )}
              {(!isNomatch || (!isLoading)) && (
                <View className={c2xStyles.marketingFooterWrap}>
                  <MarketingFooter {...marktingFooterData} />
                </View>
              )}
            </XAnimated.View>
          </BouncesScrollView>
        </View>
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/List/Method.ts, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 ConditionalExpression表达式，start： 3563  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg5q12000iq70twf4E54.png`
        : `${ImageUrl.CTRIP_EROS_URL}groupName.png`  after： `${ImageUrl.DIMG04_PATH}1tg5q12000iq70twf4E54.png`
 
 
2、成功 ConditionalExpression表达式，start： 3776  before： GetABCache.isISDShelves() ? passengerNoAndDoor : doorAndPassengerNo  after： passengerNoAndDoor
 
 
3、成功 ConditionalExpression表达式，start： 3868  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg0i12000in25kg3061E.png`
        : `${ImageUrl.CTRIP_EROS_URL}doorAndPassengerNo.png`  after： `${ImageUrl.DIMG04_PATH}1tg0i12000in25kg3061E.png`
 
 
4、成功 ConditionalExpression表达式，start： 4127  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg5g12000in2ttuaC1F9.png`
        : `${ImageUrl.CTRIP_EROS_URL}transmissionName.png`  after： `${ImageUrl.DIMG04_PATH}1tg5g12000in2ttuaC1F9.png`
 
 
5、成功 ConditionalExpression表达式，start： 4639  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg3q12000iq70slkE9AC.png`
        : `${ImageUrl.CTRIP_EROS_URL}displacement.png`  after： `${ImageUrl.DIMG04_PATH}1tg3q12000iq70slkE9AC.png`
 
 
6、成功 ConditionalExpression表达式，start： 4868  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg2w12000iq70ncx9163.png`
        : `${ImageUrl.CTRIP_EROS_URL}fuelType.png`  after： `${ImageUrl.DIMG04_PATH}1tg2w12000iq70ncx9163.png`
 
 
7、成功 ConditionalExpression表达式，start： 5096  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg5412000in2u904F282.png`
        : `${ImageUrl.CTRIP_EROS_URL}endurance.png`  after： `${ImageUrl.DIMG04_PATH}1tg5412000in2u904F282.png`
 
 
8、成功 ConditionalExpression表达式，start： 5316  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg6s12000in2u4yuEA7B.png`
        : `${ImageUrl.CTRIP_EROS_URL}charge.png`  after： `${ImageUrl.DIMG04_PATH}1tg6s12000in2u4yuEA7B.png`
 
 
9、成功 ConditionalExpression表达式，start： 5527  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg3r12000in2t3m4017A.png`
        : `${ImageUrl.CTRIP_EROS_URL}fuel.png`  after： `${ImageUrl.DIMG04_PATH}1tg3r12000in2t3m4017A.png`
 
 
10、成功 ConditionalExpression表达式，start： 5751  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg1l12000in2r4y2B8F0.png`
        : `${ImageUrl.CTRIP_EROS_URL}driveMode.png`  after： `${ImageUrl.DIMG04_PATH}1tg1l12000in2r4y2B8F0.png`
 
 
11、成功 ConditionalExpression表达式，start： 5983  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg2p12000in2ttmh5C63.png`
        : `${ImageUrl.CTRIP_EROS_URL}luggageNum.png`  after： `${ImageUrl.DIMG04_PATH}1tg2p12000in2ttmh5C63.png`
 
 
12、成功 ConditionalExpression表达式，start： 6204  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg6y12000in2t8wd5CBE.png`
        : `${ImageUrl.CTRIP_EROS_URL}struct.png`  after： `${ImageUrl.DIMG04_PATH}1tg6y12000in2t8wd5CBE.png`
 
 
13、成功 LogicalExpression表达式，start： 6920  before： GetABCache.isISDShelves() &&
      reverseImage  after： reverseImage
 
 
14、成功 LogicalExpression表达式，start： 7229  before： GetABCache.isISDShelves() &&
      reverseSensor  after： reverseSensor
 
 
15、成功 LogicalExpression表达式，start： 7543  before： GetABCache.isISDShelves() &&
      tachograph  after： tachograph
 
 
16、成功 ConditionalExpression表达式，start： 8026  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg6j12000jprzx7l24B9.png`
        : `${ImageUrl.CTRIP_EROS_URL}guidSys.png`  after： `${ImageUrl.DIMG04_PATH}1tg6j12000jprzx7l24B9.png`
 
 
17、成功 ConditionalExpression表达式，start： 8357  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg0m12000jpsi63kFF38.png`
        : `${ImageUrl.CTRIP_EROS_URL}carPlay.png`  after： `${ImageUrl.DIMG04_PATH}1tg0m12000jpsi63kFF38.png`
 
 
18、成功 ConditionalExpression表达式，start： 8736  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg4312000jps320k0AE6.png`
        : `${ImageUrl.CTRIP_EROS_URL}chargeInterface.png`  after： `${ImageUrl.DIMG04_PATH}1tg4312000jps320k0AE6.png`
 
 
19、成功 ConditionalExpression表达式，start： 9081  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg3b12000jps0rvh6C9E.png`
        : `${ImageUrl.CTRIP_EROS_URL}skylight.png`  after： `${ImageUrl.DIMG04_PATH}1tg3b12000jps0rvh6C9E.png`
 
 
20、成功 ConditionalExpression表达式，start： 9432  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg4512000jps9roe8ADE.png`
        : `${ImageUrl.CTRIP_EROS_URL}carPhone.png`  after： `${ImageUrl.DIMG04_PATH}1tg4512000jps9roe8ADE.png`
 
 
21、成功 ConditionalExpression表达式，start： 9783  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg5t12000jpsde13A272.png`
        : `${ImageUrl.CTRIP_EROS_URL}autoPark.png`  after： `${ImageUrl.DIMG04_PATH}1tg5t12000jpsde13A272.png`
 
 
22、成功 ConditionalExpression表达式，start： 10146  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg3w12000jproyub48EF.png`
        : `${ImageUrl.CTRIP_EROS_URL}autoBackUp.png`  after： `${ImageUrl.DIMG04_PATH}1tg3w12000jproyub48EF.png`
 
 
23、成功 ConditionalExpression表达式，start： 10505  before： GetABCache.isISDShelves()
        ? `${ImageUrl.DIMG04_PATH}1tg4k12000jprr3521422.png`
        : `${ImageUrl.CTRIP_EROS_URL}autoStart.png`  after： `${ImageUrl.DIMG04_PATH}1tg4k12000jprr3521422.png`
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/List/VehicleListMappers.ts, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 IfStatement表达式，start： 47179  before： if (GetABCache.isISDShelves()) {
    allLabels = allTags
      .filter(
        f =>
          (f.groupId === Enums.LabelGroupType.Vehicle ||
            f.groupId === Enums.LabelGroupType.Service ||
            f?.code === ILableCode.Limit) &&
          f?.code !== LabelCodeType.NATIONALCHNAIN &&
          f?.labelCode !== ILableCode.SelfService,
      )
      ?.sort((prev, next) => prev.sortBy - next.sortBy);
  }  after： allLabels = allTags
      .filter(
        f =>
          (f.groupId === Enums.LabelGroupType.Vehicle ||
            f.groupId === Enums.LabelGroupType.Service ||
            f?.code === ILableCode.Limit) &&
          f?.code !== LabelCodeType.NATIONALCHNAIN &&
          f?.labelCode !== ILableCode.SelfService,
      )
      ?.sort((prev, next) => prev.sortBy - next.sortBy);
 
 
3、成功 ConditionalExpression表达式，start： 50669  before： GetABCache.isISDShelves()
    ? `${commentCount}评价`
    : Texts.review(commentCount)  after： `${commentCount}评价`
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isISDShelves(), 替换数量=8 
 
1、成功 LogicalExpression表达式，start： 13505  before： reference.fType || false  after： reference.fType || !GetABCache.isISDShelves()
 
 
2、成功 LogicalExpression表达式，start： 13966  before： (store && store.isrentcent) || false  after： (store && store.isrentcent) || !GetABCache.isISDShelves()
 
 
3、成功 LogicalExpression表达式，start： 56893  before： productItem.isEasy || false  after： productItem.isEasy || !GetABCache.isISDShelves()
 
 
5、成功 LogicalExpression表达式，start： 56949  before： productItem.isOptim || false  after： productItem.isOptim || !GetABCache.isISDShelves()
 
 
7、成功 LogicalExpression表达式，start： 57006  before： productItem.isCredit || false  after： productItem.isCredit || !GetABCache.isISDShelves()
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/List/VehicleListMappers.ts, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isISDShelves() 
 
1、成功 LogicalExpression表达式，start： 13505  before： reference.fType || !GetABCache.isISDShelves()  after： reference.fType
 
 
2、成功 LogicalExpression表达式，start： 13988  before： (store && store.isrentcent) || !GetABCache.isISDShelves()  after： store && store.isrentcent
 
 
3、成功 LogicalExpression表达式，start： 56935  before： productItem.isEasy || !GetABCache.isISDShelves()  after： productItem.isEasy
 
 
5、成功 LogicalExpression表达式，start： 57012  before： productItem.isOptim || !GetABCache.isISDShelves()  after： productItem.isOptim
 
 
7、成功 LogicalExpression表达式，start： 57090  before： productItem.isCredit || !GetABCache.isISDShelves()  after： productItem.isCredit
 
 

下线AB实验相关代码，GetABCache.isISDShelves函数相关，取值为true，全部执行完成 
 
