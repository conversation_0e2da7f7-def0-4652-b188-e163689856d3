module.exports = {
  env: {
    jest: true,
    es6: true,
  },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: ['./tsconfig.json'],
  },
  extends: [
    'plugin:@ctrip/eslint-plugin-car-linter/standardRN',
    // require.resolve('@ctrip/trip-common-eslint-rules/eslint/default'),
    // 'plugin:react/recommended',
    // 'plugin:@typescript-eslint/recommended',
  ],
  rules: {
    // // ------ node-vampire-lint 配置 ------
    // 'new-cap': 0,
    // 'import/prefer-default-export': 0,
    // 'react/no-unused-prop-types': 0,
    // 'react-native/no-color-literals': 0,
    // 'react/no-unused-class-component-methods': 0,
    // '@typescript-eslint/no-unused-expressions': 0,
    // 'no-console': 0, // temporary
    // 'no-unsafe-optional-chaining': 0, // temporary
    // '@typescript-eslint/default-param-last': 0, // temporary
    // '@typescript-eslint/naming-convention': 0,
    // 'react/prop-types': 0,
    // 'react/destructuring-assignment': 0,
    // 'class-methods-use-this': 0,
    // '@typescript-eslint/no-unused-vars': 0,
    // 'react/jsx-props-no-spreading': 0,
    // '@ctrip/ctrip/ascii-only': 'off', // TODO: 规则未生效？？
    // // ------ end ------
    '@typescript-eslint/no-var-requires': 'off', // 关闭 require 语法检查
    'react/jsx-boolean-value': [2, 'always'],
    'no-restricted-imports': [
      'error',
      {
        patterns: ['@ctrip/rn_com_car/src/*'],
      },
    ],
    'react/function-component-definition': 0,
    'react/require-default-props': 0,
    'react/display-name': 0,
    'import/no-extraneous-dependencies': 0,
    'import/no-restricted-paths': [
      'error',
      {
        zones: [
          {
            target: './src/Common/**',
            from: './src/!(Common)**/**',
            message: 'Common文件夹只能引用本包内部文件及外部package',
          },
        ],
      },
    ],
    // 'unused-imports/no-unused-imports': 'error',
    // 'unused-imports/no-unused-vars': [
    //   'warn',
    //   {
    //     vars: 'all',
    //     varsIgnorePattern: '^_',
    //     args: 'after-used',
    //     argsIgnorePattern: '^_',
    //   },
    // ],
    // 'no-unused-vars': 2,
    // 'unused-vars/no-unused-imports': 'error',
    // 'unused-vars/no-unused-vars': [
    //   'warn',
    //   {
    //     autoFix: true,
    //     vars: 'all',
    //     varsIgnorePattern: '^_',
    //     args: 'after-used',
    //     argsIgnorePattern: '^_',
    //   },
    // ],
  },
};
