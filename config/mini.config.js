module.exports = {
  routes: [],
  extraPackageJson: {
    dependencies: {
      '@ctrip/xtaro-style': '2.0.12',
      '@ctrip/xtaro-component-calendar-mini': '0.2.9',
      'lodash-es': '4.17.21',
    },
  },
  alias: {},
  pipeline: {
    // weapp: "weapp-release"
  },
  commonModules: [], // 暂时为数组类型，待 cli 发布后，再改成 对象（从而支持更复杂的配置需求）
  5227: {
    pipeline: {
      weapp: 'xtaro-weapp-release',
    },
  },
  alias: {},
  5240: {
    pipeline: {
      weapp: 'xtaro-weapp-release-standalone',
    },
    gitConfig: {
      baseConfig: {
        git: '', //默认是tarobaseproject,
        branch: '', //默认是master分支，支持设置3.4.7分支
      },
      miniConfig: {
        appId: 'wxe4bc565cbbf5e289',
        mcdAppId: '5240',
        weapp: '****************************:tinyapp/taro-car-auto-weapp.git',
      },
    },
  },
  5288: {
    pipeline: {
      alipay: 'xtaro-alipay-release-standalone',
    },
    gitConfig: {
      baseConfig: {
        git: '', //默认是tarobaseproject,
        branch: '', //默认是master分支，支持设置3.4.7分支
      },
      miniConfig: {
        appId: '2018081561055519',
        mcdAppId: '5288',
        alipay: '****************************:tinyapp/taro-car-auto-alipay.git',
      },
    },
  },
};
