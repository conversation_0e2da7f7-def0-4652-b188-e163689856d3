const path = require('path');

module.exports = {
  appid: '99999999',
  rnVersion: '0.72.5',
  alias: {
    'lodash-es': path.resolve(__dirname, '..', 'node_modules/lodash'),
  },
  customTemplate: {
    entry: path.join(__dirname, 'template/Entry.njk'), //入口模版文件
  },
  global: {
    maxDeviceWidthScale: 430, // 设置最大适配屏幕宽度为 430
  },
  CRNModuleName: 'rn_xtaro_car_main',
  extraPackageJson: {
    scripts: {
      ios: 'crn run-ios --app-version 8.68.6 --simulator "iPhone 14 Pro"',
      preinstall: 'node ./scripts/ignoreModuleTs.js',
      postinstall:
        'npm run lazyRequire && node ./scripts/postinstall.js && npm run commonScript && tsc-alias',
      lazyRequire: 'node --harmony ./scripts/lazyRequireReplace',
      commonScript:
        'node ./src/taro/pages/xcar/Common/scripts/lazyRequireReplace',
    },
    iconFontOnline: {
      crn_font_xtaro_car_osd:
        'http://developer.fx.ctripcorp.com/api/iconfont.node.js?zipUrl=http%3A%2F%2Ficonfont.nfes.ctripcorp.com%2Fdownload%2Fproject-crn_font_xtaro_car_osd-0.0.8.zip',
      crn_font_car_ctrip_v1:
        'http://developer.fx.ctripcorp.com/api/iconfont.node.js?zipUrl=http%3A%2F%2Ficonfont.nfes.ctripcorp.com%2Fdownload%2Fproject-crn_font_car_ctrip_v1-0.0.19.zip',
    },
    dependencies: {
      '@ctrip/crn-inps': '^1.0.16',
      lodash: '4.17.21',
      '@ctrip/bbk-marketInfo': '2.6.3',
      '@ctrip/crn':
        'git+http://git.dev.sh.ctripcorp.com/crn/crn.git#rel/8.68.6',
      '@ctrip/crn_ext':
        'git+http://git.dev.sh.ctripcorp.com/crn/crn_ext.git#rel/8.52.6',
      '@ctrip/crn-ext-adsdk': '3.4.13',
      '@ctrip/crn-ext-react-native-svg': '12.1.0',
      '@ctrip/crn-inps': '^1.0.16',
      '@ctrip/react-native-render-html': '4.1.6',
      '@ctrip/rr-react-native': '5.1.4',
      '@ctrip/shark-app-sdk': '^2.5.4',
      '@ctrip/ttd-marketing-rn': '3.0.4',
      '@ctrip/xtaro-car-library-crn': '0.0.45',
      '@testing-library/jest-native': '^5.0.0',
      '@testing-library/react-native': '^11.2.0',
      'react-native': '0.72.5',
      'react-native-animatable': '1.3.3',
      'react-native-keyboard-aware-scroll-view': '0.9.5',
      'react-native-typescript-transformer': '^1.2.13',
      '@ctrip/bbk-trace': '1.1.3',
      '@ctrip/ipoll-urs-crn': '1.2.16',
    },
  },
};
