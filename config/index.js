const path = require('path');
const config = {
  alias: {
    '@ctrip/rn_com_car/dist': path.resolve(
      __dirname,
      '..',
      'src/pages/xcar/Common',
    ),
    '@c2x': path.resolve(__dirname, '..', 'src/pages/xcar/c2x'),
  },
  transformSassName: true,
  // 设计稿尺寸
  designWidth: 750,
  // 设计稿尺寸换算规则
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
  },
  routes: [
    {
      path: 'pages/xcar/entry/home/<USER>',
      subRoot: 'pages/xcar/entry/home',
      crnRouterName: 'Home',
      h5RouterName: 'Home',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/list/index',
      subRoot: 'pages/xcar/entry/list',
      crnRouterName: 'List',
      h5RouterName: 'List',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/location/index',
      subRoot: 'pages/xcar/entry/location',
      crnRouterName: 'Location',
      h5RouterName: 'Location',
      platform: ['crn', 'h5'],
      jumpAnimation: 'bottom',
    },
    {
      path: 'pages/xcar/entry/vendorlist/index',
      subRoot: 'pages/xcar/entry/vendorlist',
      crnRouterName: 'VendorList',
      h5RouterName: 'VendorList',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/guide/index',
      subRoot: 'pages/xcar/entry/guide',
      crnRouterName: 'Guide',
      h5RouterName: 'Guide',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/coupon/index',
      subRoot: 'pages/xcar/entry/coupon',
      crnRouterName: 'Coupon',
      h5RouterName: 'Coupon',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/booking/index',
      subRoot: 'pages/xcar/entry/booking',
      crnRouterName: 'Booking',
      h5RouterName: 'Booking',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/bookingIsd/index',
      subRoot: 'pages/xcar/entry/bookingIsd',
      crnRouterName: 'BookingIsd',
      h5RouterName: 'BookingIsd',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/product/index',
      subRoot: 'pages/xcar/entry/product',
      crnRouterName: 'Product',
      h5RouterName: 'Product',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/market/index',
      subRoot: 'pages/xcar/entry/market',
      crnRouterName: 'Market',
      h5RouterName: 'Market',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/license/index',
      subRoot: 'pages/xcar/entry/license',
      crnRouterName: 'License',
      h5RouterName: 'License',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/debug/index',
      subRoot: 'pages/xcar/entry/debug',
      crnRouterName: 'Debug',
      h5RouterName: 'Debug',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/driverlist/index',
      subRoot: 'pages/xcar/entry/driverlist',
      crnRouterName: 'DriverList',
      h5RouterName: 'DriverList',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/driveredit/index',
      subRoot: 'pages/xcar/entry/driveredit',
      crnRouterName: 'DriverEdit',
      h5RouterName: 'DriverEdit',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/vehmodal/index',
      subRoot: 'pages/xcar/entry/vehmodal',
      crnRouterName: 'VehModal',
      h5RouterName: 'VehModal',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/orderdetail/index',
      subRoot: 'pages/xcar/entry/orderdetail',
      crnRouterName: 'OrderDetail',
      h5RouterName: 'OrderDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/onlineauth/index',
      subRoot: 'pages/xcar/entry/onlineauth',
      crnRouterName: 'OnlineAuth',
      h5RouterName: 'OnlineAuth',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/materials/index',
      subRoot: 'pages/xcar/entry/materials',
      crnRouterName: 'Materials',
      h5RouterName: 'Materials',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/policy/index',
      subRoot: 'pages/xcar/entry/policy',
      crnRouterName: 'Policy',
      h5RouterName: 'Policy',
      platform: ['crn', 'h5'],
      jumpAnimation: 'bottom',
    },
    {
      path: 'pages/xcar/entry/extras/index',
      subRoot: 'pages/xcar/entry/extras',
      crnRouterName: 'Extras',
      h5RouterName: 'Extras',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/packageincludes/index',
      subRoot: 'pages/xcar/entry/packageincludes',
      crnRouterName: 'PackageIncludes',
      h5RouterName: 'PackageIncludes',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/credentials/index',
      subRoot: 'pages/xcar/entry/credentials',
      crnRouterName: 'Credentials',
      h5RouterName: 'Credentials',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/ordercancel/index',
      subRoot: 'pages/xcar/entry/ordercancel',
      crnRouterName: 'OrderCancel',
      h5RouterName: 'OrderCancel',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/orderrefunddetail/index',
      subRoot: 'pages/xcar/entry/orderrefunddetail',
      crnRouterName: 'OrderRefundDetail',
      h5RouterName: 'OrderRefundDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/orderchange/index',
      subRoot: 'pages/xcar/entry/orderchange',
      crnRouterName: 'OrderChange',
      h5RouterName: 'OrderChange',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/supplementlist/index',
      subRoot: 'pages/xcar/entry/supplementlist',
      crnRouterName: 'SupplementList',
      h5RouterName: 'SupplementList',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/renewlist/index',
      subRoot: 'pages/xcar/entry/renewlist',
      crnRouterName: 'RenewList',
      h5RouterName: 'RenewList',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/violationdetail/index',
      subRoot: 'pages/xcar/entry/violationdetail',
      crnRouterName: 'ViolationDetail',
      h5RouterName: 'ViolationDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/damagedetail/index',
      subRoot: 'pages/xcar/entry/damagedetail',
      crnRouterName: 'DamageDetail',
      h5RouterName: 'DamageDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/supplement/index',
      subRoot: 'pages/xcar/entry/supplement',
      crnRouterName: 'Supplement',
      h5RouterName: 'Supplement',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/rerent/index',
      subRoot: 'pages/xcar/entry/rerent',
      crnRouterName: 'Rerent',
      h5RouterName: 'Rerent',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/advancereturn/index',
      subRoot: 'pages/xcar/entry/advancereturn',
      crnRouterName: 'AdvanceReturn',
      h5RouterName: 'AdvanceReturn',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/orderlimitrulespage/index',
      subRoot: 'pages/xcar/entry/orderlimitrulespage',
      crnRouterName: 'OrderLimitRulesPage',
      h5RouterName: 'OrderLimitRulesPage',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/insuranceorderdetail/index',
      subRoot: 'pages/xcar/entry/insuranceorderdetail',
      crnRouterName: 'InsuranceOrderDetail',
      h5RouterName: 'InsuranceOrderDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/ordervialationrule/index',
      subRoot: 'pages/xcar/entry/ordervialationrule',
      crnRouterName: 'OrderVialationRule',
      h5RouterName: 'OrderVialationRule',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/modifyorder/index',
      subRoot: 'pages/xcar/entry/modifyorder',
      crnRouterName: 'ModifyOrder',
      h5RouterName: 'ModifyOrder',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/modifyorderconfirm/index',
      subRoot: 'pages/xcar/entry/modifyorderconfirm',
      crnRouterName: 'ModifyOrderConfirm',
      h5RouterName: 'ModifyOrderConfirm',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/modifycoupon/index',
      subRoot: 'pages/xcar/entry/modifycoupon',
      crnRouterName: 'ModifyCoupon',
      h5RouterName: 'ModifyCoupon',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/rebookhome/index',
      subRoot: 'pages/xcar/entry/rebookhome',
      crnRouterName: 'RebookHome',
      h5RouterName: 'RebookHome',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/carrentalcenter/index',
      subRoot: 'pages/xcar/entry/carrentalcenter',
      crnRouterName: 'CarRentalCenter',
      h5RouterName: 'CarRentalCenter',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/isdagreement/index',
      subRoot: 'pages/xcar/entry/isdagreement',
      crnRouterName: 'IsdAgreement',
      h5RouterName: 'IsdAgreement',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/messageassistant/index',
      subRoot: 'pages/xcar/entry/messageassistant',
      crnRouterName: 'MessageAssistant',
      h5RouterName: 'MessageAssistant',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/instructions/index',
      subRoot: 'pages/xcar/entry/instructions',
      crnRouterName: 'Instructions',
      h5RouterName: 'Instructions',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/member/index',
      subRoot: 'pages/xcar/entry/member',
      crnRouterName: 'Member',
      h5RouterName: 'Member',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/memberdetail/index',
      subRoot: 'pages/xcar/entry/memberdetail',
      crnRouterName: 'MemberDetail',
      h5RouterName: 'MemberDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/memberbirth/index',
      subRoot: 'pages/xcar/entry/memberbirth',
      crnRouterName: 'MemberBirth',
      h5RouterName: 'MemberBirth',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/memberpoints/index',
      subRoot: 'pages/xcar/entry/memberpoints',
      crnRouterName: 'MemberPoints',
      h5RouterName: 'MemberPoints',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/vehicledamageprove/index',
      subRoot: 'pages/xcar/entry/vehicledamageprove',
      crnRouterName: 'VehicleDamageProve',
      h5RouterName: 'VehicleDamageProve',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/recommendvehicle/index',
      subRoot: 'pages/xcar/entry/recommendvehicle',
      crnRouterName: 'RecommendVehicle',
      h5RouterName: 'RecommendVehicle',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/limitmap/index',
      subRoot: 'pages/xcar/entry/limitmap',
      crnRouterName: 'LimitMap',
      h5RouterName: 'LimitMap',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/depositfree/index',
      subRoot: 'pages/xcar/entry/depositfree',
      crnRouterName: 'DepositFree',
      h5RouterName: 'DepositFree',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/album/index',
      subRoot: 'pages/xcar/entry/album',
      crnRouterName: 'Album',
      h5RouterName: 'Album',
      platform: ['crn', 'h5'],
    },
  ],
  copyFolders: [
    {
      sourcePath: './scripts',
    },
  ],
};

//XTARO_ENV在各端build时会自动设置
module.exports = function (merge) {
  if (process.env.XTARO_ENV === 'crn') {
    return merge({}, config, require('./crn.config.js'));
  }
  if (process.env.XTARO_ENV === 'h5') {
    return merge({}, config, require('./h5.config.js'));
  }
  if (process.env.XTARO_ENV === 'mini') {
    return merge({}, config, require('./mini.config.js'));
  }
  return config;
};
