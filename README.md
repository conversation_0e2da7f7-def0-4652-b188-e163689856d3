## 项目介绍

> 本项目由CRN项目迁移而来，迁移教程请参考[CRN项目迁移教程](https://pages.release.ctripcorp.com/car/crn2xtaro/)

## 本地开发

1. [xTaro环境搭建](https://pages.release.ctripcorp.com/xtaro/xtaro/docs/quick_start/environment_setup)
2. 安装依赖

```bash
npm run clean

```

### APP开发

1. 按照[xTaro - CRN](https://pages.release.ctripcorp.com/xtaro/xtaro/docs/quick_start/using_xtaro_for_crn_development)配置环境
2. 启动APP项目

```bash
npm run dev:crn
```

### 小程序开发

1. 点击[这里](http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********)申请权限微信主版权限申请
2. 按照[xTaro - mini](https://pages.release.ctripcorp.com/xtaro/xtaro/docs/quick_start/developing_master_program)配置环境，下载微信开发者工具（Stable 1.06.2405020）

3. 启动小程序项目

```bash
npm run dev:mini
```

3. 小程序打登录态

1) 首先

```bash
npm run build:mini -- --accounts
```

2. 然后登陆

3. 最后

```bash
npm run dev:mini
```

4. 注意：在微信开发者工具中打开的目录是`/xxxx/xtaro-car-main/dist/mini/weapp`
